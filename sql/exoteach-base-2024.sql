SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT = @@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS = @@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION = @@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- --------------------------------------------------------

--
-- Structure de la table `annees`
--

CREATE TABLE `annees`
(
    `id`    int NOT NULL,
    `annee` int NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `annees`
--

INSERT INTO `annees` (`id`, `annee`)
VALUES (1, 2010),
       (2, 2011),
       (3, 2012),
       (4, 2013),
       (5, 2014),
       (6, 2015),
       (7, 2016),
       (8, 2017),
       (9, 2018),
       (11, 2019),
       (12, 2020),
       (13, 2021),
       (14, 2022),
       (15, 2023);

-- --------------------------------------------------------

--
-- Structure de la table `base_revision`
--

CREATE TABLE `base_revision`
(
    `id`              int         NOT NULL,
    `dateDebut`       datetime DEFAULT NULL,
    `userId`          int      DEFAULT NULL,
    `courId`          int      DEFAULT NULL,
    `createdAt`       datetime(6) NOT NULL,
    `updatedAt`       datetime(6) NOT NULL,
    `dateDiffusionId` int      DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `bills`
--

CREATE TABLE `bills`
(
    `id`        int          NOT NULL,
    `name`      varchar(255) DEFAULT NULL,
    `file`      varchar(255) NOT NULL,
    `createdAt` datetime     NOT NULL,
    `updatedAt` datetime     NOT NULL,
    `userId`    int          DEFAULT NULL,
    `paymentId` int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `buildings`
--

CREATE TABLE `buildings`
(
    `id`        int      NOT NULL,
    `name`      varchar(255) DEFAULT NULL,
    `image`     varchar(255) DEFAULT NULL,
    `address`   text,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL,
    `postCode`  varchar(255) DEFAULT NULL,
    `city`      varchar(255) DEFAULT NULL,
    `country`   varchar(255) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `challenges`
--

CREATE TABLE `challenges`
(
    `id`             int NOT NULL,
    `name`           varchar(255) DEFAULT NULL,
    `description`    text,
    `image`          varchar(255) DEFAULT NULL,
    `folder`         varchar(255) DEFAULT NULL,
    `isPublished`    tinyint(1)   DEFAULT '0',
    `rewards`        json         DEFAULT NULL,
    `createdAt`      datetime     DEFAULT NULL,
    `updatedAt`      datetime     DEFAULT NULL,
    `name_en`        varchar(255) DEFAULT NULL,
    `description_en` varchar(255) DEFAULT NULL,
    `name_it`        varchar(255) DEFAULT NULL,
    `description_it` varchar(255) DEFAULT NULL,
    `name_de`        varchar(255) DEFAULT NULL,
    `description_de` varchar(255) DEFAULT NULL,
    `name_es`        varchar(255) DEFAULT NULL,
    `description_es` varchar(255) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `challenges_type_qcm`
--

CREATE TABLE `challenges_type_qcm`
(
    `typeQcmId`   int DEFAULT NULL,
    `challengeId` int DEFAULT NULL,
    `createdAt`   datetime NOT NULL,
    `updatedAt`   datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `challenge_badges`
--

CREATE TABLE `challenge_badges`
(
    `id`          int NOT NULL,
    `name`        varchar(255) DEFAULT NULL,
    `image`       varchar(255) DEFAULT NULL,
    `type`        varchar(255) DEFAULT NULL,
    `description` text,
    `file`        text,
    `challengeId` int          DEFAULT NULL,
    `createdAt`   datetime     DEFAULT NULL,
    `updatedAt`   datetime     DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `challenge_conditions`
--

CREATE TABLE `challenge_conditions`
(
    `id`                int NOT NULL,
    `name`              varchar(255) DEFAULT NULL,
    `type`              varchar(255) DEFAULT NULL,
    `description`       text,
    `challengeId`       int          DEFAULT NULL,
    `coursId`           int          DEFAULT NULL,
    `qcmId`             int          DEFAULT NULL,
    `createdAt`         datetime     DEFAULT NULL,
    `updatedAt`         datetime     DEFAULT NULL,
    `contentType`       varchar(255) DEFAULT NULL,
    `settings`          json         DEFAULT NULL,
    `successConditions` json         DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `challenge_groups_unlock`
--

CREATE TABLE `challenge_groups_unlock`
(
    `challengeId` int DEFAULT NULL,
    `groupeId`    int DEFAULT NULL,
    `createdAt`   datetime NOT NULL,
    `updatedAt`   datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `challenge_user_progress`
--

CREATE TABLE `challenge_user_progress`
(
    `id`                   int NOT NULL,
    `isFinished`           tinyint(1) DEFAULT '0',
    `challengeId`          int        DEFAULT NULL,
    `challengeConditionId` int        DEFAULT NULL,
    `userId`               int        DEFAULT NULL,
    `qcmSessionId`         int        DEFAULT NULL,
    `ueId`                 int        DEFAULT NULL,
    `ueCategoryId`         int        DEFAULT NULL,
    `coursId`              int        DEFAULT NULL,
    `createdAt`            datetime   DEFAULT NULL,
    `updatedAt`            datetime   DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `configs`
--

CREATE TABLE `configs`
(
    `id`        int      NOT NULL,
    `key`       varchar(255) DEFAULT NULL,
    `value`     longtext,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL,
    `domain`    varchar(255) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `configs`
--

INSERT INTO `configs` (`id`, `key`, `value`, `createdAt`, `updatedAt`, `domain`)
VALUES (1, 'WEBSITE_NAME', 'Exoteach', '2020-06-24 22:40:58', '2021-12-30 15:01:37', NULL);

INSERT INTO `configs` (`id`, `key`, `value`, `createdAt`, `updatedAt`, `domain`)
VALUES (2, 'ANNONCE_GENERALE', '', '2020-06-24 22:40:58', '2023-04-21 12:10:33', NULL);


INSERT INTO `configs` (`id`, `key`, `value`, `createdAt`, `updatedAt`, `domain`)
VALUES (3, 'MAINTENANCE_MODE', '0', '2020-06-24 22:40:58', '2020-06-24 22:40:58', NULL),
       (4, 'WEBSITE_BASE_URL', 'http://localhost:8000', '2020-06-24 22:40:58', '2020-06-24 22:40:58', NULL),
       (5, 'TWITTER_ACCOUNT', 'https://twitter.com/', '2020-06-24 22:40:58', '2020-06-24 22:40:58', NULL),
       (6, 'FACEBOOK_ACCOUNT', 'https://facebook.com', '2020-06-24 22:40:58', '2020-06-24 22:40:58', NULL),
       (7, 'DEFAULT_PAYMENT_SYSTEM', 'stripe', '2021-05-31 10:21:53', '2021-05-31 10:21:53', NULL),
       (8, 'LOGO_LOGINPAGE', NULL, '2021-12-30 14:55:55', '2021-12-30 15:01:23', NULL),
       (9, 'LOGO_MENUBAR', NULL, '2021-12-30 14:55:55', '2021-12-30 14:59:26', NULL),
       (10, 'COLORS_LOGINPAGE', NULL, '2021-12-30 14:55:55', '2021-12-30 14:55:55', NULL),
       (11, 'COLORS_BREADCRUMB', NULL, '2021-12-30 14:55:55', '2021-12-30 14:55:55', NULL),
       (17, 'REGISTERING_OPEN_ON_MOBILE_APP', '0', '2022-09-12 16:01:42', '2022-09-12 16:12:17', NULL),
       (18, 'LINK_TO_SALES_SITE', NULL, '2022-09-12 16:01:42', '2022-09-12 16:12:25', NULL),
       (19, 'COMPANY_INFORMATION', NULL, '2022-09-12 16:01:42', '2023-08-14 18:19:04', NULL),
       (20, 'CGV', NULL, '2022-09-12 16:01:42', '2022-09-12 16:15:32', NULL),

       (25, 'LOGO_PRINT', NULL, '2022-09-14 17:44:28', '2022-09-14 17:50:16', NULL),
       (26, 'PRIMARY_COLOR', NULL, '2022-09-14 17:44:28', '2022-09-14 17:44:28', NULL),
       (27, 'SECONDARY_COLOR', NULL, '2022-09-14 17:44:28', '2022-09-14 17:44:28', NULL),
       (29, 'ENABLED_LANGUAGES_FOR_CONTENT', '[\"fr\"]', '2022-11-14 14:13:52', '2022-11-14 14:13:52', NULL),
       (30, 'APPEARANCE',
        '{\"showLogoLoginPage\":true,\"showPlatformNameLoginPage\":true,\"loginPageBackgroundAnimation\":\"particles\",\"bannerAnimation\":\"particles\",\"backgroundImagePlatform\":null,\"backgroundImageDoMcq\":null,\"backgroundImageForLoginPage\":null,\"showLogoMenuBar\":true,\"showPlatformNameMenuBar\":true,\"faviconUrl\":null,\"borderRadius\":\"4px\",\"backgroundColor\":\"#f0f2f5\",\"primaryColor\":\"#2a8bbb\",\"cardBackgroundColor\":\"#1890ff\",\"darkMode\":false}',
        '2022-11-22 16:04:19', '2023-05-23 15:28:49', NULL),
       (32, 'CHAT_GPT_API_KEY', NULL, '2023-04-17 18:47:13', '2023-04-17 19:24:34', NULL),
       (34, 'CHAT_GPT_AUTO_ANSWER_QUESTIONS_FEATURE_ENABLED', '0', '2023-04-17 18:47:13', '2023-04-17 19:24:42', NULL),
       (36, 'CHAT_GPT_SETTINGS',
        '{\"model\":\"gpt-4\",\"templates\":{\"exerciseQuestion\":\"\\nVoici une question d’un étudiant sur un exercice de type {{questionTypeString}}, la question porte sur {{array_linkedCoursesString}}.\\nPour te donner le contexte : \\n\\nÉnoncé du {{questionTypeString}}: \\n{{enonceQuestion}}\\nPropositions du {{questionTypeString}}: \\n{{answersToString}}\\n\\nLa proposition concernée par la question de l\'étudiant est : {{currentAnswerLetter}}\\n\\nVoici la question de l\'élève à répondre :\\n\\n{{questionTitle}} : {{questionContent}} \\n \\n{{precisionPromptForChatGPT}}\\n{{botPersonnalityPromptFragment}}\",\"courseQuestion\":\"\\nVoici une question d’un étudiant sur le thème de {{coursName}} : {{coursDescription}}\\n{{questionTitle}} : {{questionContent}}\\n{{precisionPromptForChatGPT}}\\n{{botPersonnalityPromptFragment}}\\n\",\"questionImportation\":\"\\nVoici un QCM qui comprend un énoncé et plusieurs questions. \\nJe veux que tu transformes ce QCM dans le format JSON suivant, en prenant soin de mettre isTrue à true pour les réponses justes et à false pour les réponses fausses, d\'après les lettres de la correction.      \\n      {\\n        \\\"question\\\": \\\"Titre de la question\\\",\\n        \\\"question_answers\\\": [\\n          {\\n            \\\"text\\\": \\\"Réponse 1\\\",\\n            \\\"isTrue\\\": true,\\n            \\\"explanation\\\": \\\"Explication de la réponse 1, si il y en a une.\\\"\\n          },\\n          {\\n            \\\"text\\\": \\\"Réponse 2\\\",\\n            \\\"isTrue\\\": false,\\n            \\\"explanation\\\": \\\"Explication de la réponse 2, si il y en a une.\\\"\\n          },\\n        ]\\n      }\\n      \\n      Il est très important de respecter ce format JSON, tu ne dois pas le modifier. Ne renvoie que le JSON dans la réponse et rien d’autre. Voici un exemple de ce qui est attendu: \\n      \\n        \\\"Parmi les propositions suivantes, laquelle ou lesquelles sont exactes :\\n         A. Les mitochondries sont bleu \\n         B. Les oiseaux sont verts\\n         C. Le chat miaule\\n         D. Le ciel est gris en permanence\\n         E. Le bras et plus grand que l’avant bras.\\n         Réponses : ACE\\\"\\n       Donnera en JSON : \\n       {\\n        \\\"question\\\": \\\"Parmi les propositions suivantes, laquelle ou lesquelles sont exactes :\\\",\\n        \\\"question_answers\\\": [\\n          {\\n            \\\"text\\\": \\\"Les mitochondries sont bleu\\\",\\n            \\\"isTrue\\\": true\\n          },\\n          {\\n            \\\"text\\\": \\\"Les oiseaux sont verts\\\",\\n            \\\"isTrue\\\": false\\n          },\\n          {\\n            \\\"text\\\": \\\"Le chat miaule\\\",\\n            \\\"isTrue\\\": true\\n          },\\n          {\\n            \\\"text\\\": \\\"Le ciel est gris en permanence\\\",\\n            \\\"isTrue\\\": false\\n          },\\n          {\\n            \\\"text\\\": \\\"Le bras et plus grand que l’avant bras.\\\",\\n            \\\"isTrue\\\": true\\n          },\\n        ]\\n      }\\n      \\n      Voici l’énoncé du QCM à transformer dans le format JSON évoqué :\\n      {{subjectWithCorrection}}\\n      \"},\"user\":\"Exoteach\"}',
        '2023-04-18 20:18:36', '2023-07-06 20:46:53', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `cours`
--

CREATE TABLE `cours`
(
    `id`                   int          NOT NULL,
    `name`                 varchar(255)          DEFAULT NULL,
    `text`                 text,
    `type`                 varchar(255)          DEFAULT NULL,
    `pdf`                  varchar(255)          DEFAULT NULL,
    `epub`                 varchar(255)          DEFAULT NULL,
    `preferredDisplayType` varchar(255)          DEFAULT 'pdf',
    `isAnnale`             tinyint(1)            DEFAULT '0',
    `difficulty`           float                 DEFAULT NULL,
    `workTime`             varchar(255)          DEFAULT NULL,
    `updateInfos`          text,
    `tips`                 longtext,
    `usefulLinks`          text,
    `views`                int                   DEFAULT '0',
    `version`              varchar(255)          DEFAULT '1',
    `isEnAvant`            tinyint(1)            DEFAULT '0',
    `date`                 datetime              DEFAULT NULL,
    `deleted`              tinyint(1)            DEFAULT '0',
    `createdAt`            datetime     NOT NULL,
    `updatedAt`            datetime     NOT NULL,
    `uecategoryId`         int                   DEFAULT NULL,
    `authorId`             int                   DEFAULT NULL,
    `duration`             int                   DEFAULT NULL,
    `pdfPreviews`          json                  DEFAULT NULL,
    `layout`               varchar(255)          DEFAULT 'pdf',
    `video`                text,
    `videoPlayerSettings`  json                  DEFAULT NULL,
    `videoType`            varchar(255)          DEFAULT NULL,
    `numberOfPagesPdf`     int                   DEFAULT NULL,
    `order`                int                   DEFAULT NULL,
    `isVisible`            tinyint(1)            DEFAULT '1',
    `fileUpdateDate`       datetime              DEFAULT NULL,
    `formationId`          int                   DEFAULT NULL,
    `settings`             json                  DEFAULT NULL,
    `targetCoursId`        int                   DEFAULT NULL,
    `name_en`              varchar(255)          DEFAULT NULL,
    `text_en`              text,
    `name_it`              varchar(255)          DEFAULT NULL,
    `text_it`              text,
    `name_de`              varchar(255)          DEFAULT NULL,
    `text_de`              text,
    `name_es`              varchar(255)          DEFAULT NULL,
    `text_es`              text,
    `customImage`          text,
    `ueId`                 int                   DEFAULT NULL,
    `watermarkPicturePath` varchar(255) NOT NULL DEFAULT ''
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `cours_groups`
--

CREATE TABLE `cours_groups`
(
    `id`        int NOT NULL,
    `groupeId`  int      DEFAULT NULL,
    `coursId`   int      DEFAULT NULL,
    `createdAt` datetime DEFAULT NULL,
    `updatedAt` datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `cours_qcms`
--

CREATE TABLE `cours_qcms`
(
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL,
    `courId`    int      NOT NULL,
    `qcmIdQcm`  int      NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `cours_supports`
--

CREATE TABLE `cours_supports`
(
    `id`        int NOT NULL,
    `name`      longtext,
    `order`     int      DEFAULT NULL,
    `coursId`   int      DEFAULT NULL,
    `authorId`  int      DEFAULT NULL,
    `createdAt` datetime DEFAULT NULL,
    `updatedAt` datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `cours_types_qcm_settings`
--

CREATE TABLE `cours_types_qcm_settings`
(
    `id`              int      NOT NULL,
    `coursId`         int          DEFAULT NULL,
    `typeQcmId`       int          DEFAULT NULL,
    `coursModuleType` varchar(255) DEFAULT NULL,
    `createdAt`       datetime NOT NULL,
    `updatedAt`       datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `custom_forfaits_links`
--

CREATE TABLE `custom_forfaits_links`
(
    `id`          int NOT NULL,
    `link`        text,
    `isPublished` tinyint(1) DEFAULT '0',
    `updatedAt`   datetime   DEFAULT NULL,
    `createdAt`   datetime   DEFAULT NULL,
    `name`        text
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `custom_forfaits_links_forfaits`
--

CREATE TABLE `custom_forfaits_links_forfaits`
(
    `forfaitId`           int NOT NULL,
    `customForfaitLinkId` int NOT NULL,
    `createdAt`           datetime DEFAULT NULL,
    `updatedAt`           datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `date_diffusion`
--

CREATE TABLE `date_diffusion`
(
    `id`               int NOT NULL,
    `date`             datetime     DEFAULT NULL,
    `show`             tinyint(1)   DEFAULT '1',
    `courId`           int          DEFAULT NULL,
    `createdAt`        datetime     DEFAULT NULL,
    `updatedAt`        datetime     DEFAULT NULL,
    `updateInfos`      text,
    `qcmId`            int          DEFAULT NULL,
    `examSessionId`    int          DEFAULT NULL,
    `eventId`          int          DEFAULT NULL,
    `dateEnd`          datetime     DEFAULT NULL,
    `availability`     varchar(255) DEFAULT NULL,
    `allDay`           tinyint(1)   DEFAULT '0',
    `timezone`         varchar(255) DEFAULT NULL,
    `updateInfos_en`   text,
    `updateInfos_it`   text,
    `updateInfos_de`   text,
    `updateInfos_es`   text,
    `recurringPeriod`  varchar(255) DEFAULT NULL,
    `recurringEndDate` datetime     DEFAULT NULL,
    `link`             varchar(255) DEFAULT NULL,
    `buildingId`       int          DEFAULT NULL,
    `roomId`           int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `date_diffusion_groups`
--

CREATE TABLE `date_diffusion_groups`
(
    `groupId`           int NOT NULL,
    `date_diffusion_id` int NOT NULL,
    `createdAt`         datetime DEFAULT NULL,
    `updatedAt`         datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `date_diffusion_organizers`
--

CREATE TABLE `date_diffusion_organizers`
(
    `userId`            int      DEFAULT NULL,
    `date_diffusion_id` int      DEFAULT NULL,
    `createdAt`         datetime DEFAULT NULL,
    `updatedAt`         datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `date_diffusion_participants`
--

CREATE TABLE `date_diffusion_participants`
(
    `userId`            int        DEFAULT NULL,
    `date_diffusion_id` int        DEFAULT NULL,
    `createdAt`         datetime   DEFAULT NULL,
    `updatedAt`         datetime   DEFAULT NULL,
    `isPresent`         tinyint(1) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `discussions`
--

CREATE TABLE `discussions`
(
    `id`             int      NOT NULL,
    `name`           varchar(255) DEFAULT NULL,
    `createdAt`      datetime NOT NULL,
    `updatedAt`      datetime NOT NULL,
    `authorId`       int          DEFAULT NULL,
    `destinataireId` int          DEFAULT NULL,
    `isGroup`        tinyint(1)   DEFAULT '0',
    `image`          varchar(255) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `discussions_users`
--

CREATE TABLE `discussions_users`
(
    `createdAt`    datetime NOT NULL,
    `updatedAt`    datetime NOT NULL,
    `userId`       int      NOT NULL,
    `discussionId` int      NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `events`
--

CREATE TABLE `events`
(
    `id`              int NOT NULL,
    `name`            longtext,
    `description`     longtext,
    `customFields`    json         DEFAULT NULL,
    `organizers`      json         DEFAULT NULL,
    `createdAt`       datetime     DEFAULT NULL,
    `updatedAt`       datetime     DEFAULT NULL,
    `folderId`        int          DEFAULT NULL,
    `name_en`         text,
    `description_en`  longtext,
    `customFields_en` json         DEFAULT NULL,
    `name_it`         text,
    `description_it`  longtext,
    `customFields_it` json         DEFAULT NULL,
    `name_de`         text,
    `description_de`  longtext,
    `customFields_de` json         DEFAULT NULL,
    `name_es`         text,
    `description_es`  longtext,
    `customFields_es` json         DEFAULT NULL,
    `link`            varchar(255) DEFAULT NULL,
    `showDiscussions` tinyint(1)   DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `event_cours`
--

CREATE TABLE `event_cours`
(
    `eventId`   int      DEFAULT NULL,
    `coursId`   int      DEFAULT NULL,
    `createdAt` datetime DEFAULT NULL,
    `updatedAt` datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `event_type_qcm`
--

CREATE TABLE `event_type_qcm`
(
    `typeQcmId` int DEFAULT NULL,
    `eventId`   int DEFAULT NULL,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `exams`
--

CREATE TABLE `exams`
(
    `id`               int NOT NULL,
    `name`             longtext,
    `description`      longtext,
    `image`            varchar(255) DEFAULT NULL,
    `color1`           varchar(255) DEFAULT NULL,
    `color2`           varchar(255) DEFAULT NULL,
    `order`            int          DEFAULT NULL,
    `authorId`         int          DEFAULT NULL,
    `createdAt`        datetime     DEFAULT NULL,
    `updatedAt`        datetime     DEFAULT NULL,
    `isPublished`      tinyint(1)   DEFAULT '0',
    `folderId`         int          DEFAULT NULL,
    `name_en`          text,
    `description_en`   text,
    `name_it`          text,
    `description_it`   text,
    `name_de`          text,
    `description_de`   text,
    `name_es`          text,
    `description_es`   text,
    `showFinalResults` tinyint(1)   DEFAULT '1'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `exam_question_series`
--

CREATE TABLE `exam_question_series`
(
    `id`            int NOT NULL,
    `name`          longtext,
    `description`   longtext,
    `coefficient`   int          DEFAULT '1',
    `order`         int          DEFAULT NULL,
    `type`          varchar(255) DEFAULT NULL,
    `settings`      json         DEFAULT NULL,
    `mcqId`         int          DEFAULT NULL,
    `examId`        int          DEFAULT NULL,
    `image`         varchar(255) DEFAULT NULL,
    `color1`        varchar(255) DEFAULT NULL,
    `color2`        varchar(255) DEFAULT NULL,
    `authorId`      int          DEFAULT NULL,
    `createdAt`     datetime     DEFAULT NULL,
    `updatedAt`     datetime     DEFAULT NULL,
    `date_begins`   datetime     DEFAULT NULL,
    `date_end`      datetime     DEFAULT NULL,
    `examSessionId` int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `exam_results`
--

CREATE TABLE `exam_results`
(
    `id`            int NOT NULL,
    `examSessionId` int      DEFAULT NULL,
    `grade`         float    DEFAULT NULL,
    `userId`        int      DEFAULT NULL,
    `createdAt`     datetime DEFAULT NULL,
    `updatedAt`     datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `exam_scales`
--

CREATE TABLE `exam_scales`
(
    `id`          int NOT NULL,
    `name`        longtext,
    `description` longtext,
    `examId`      int        DEFAULT NULL,
    `authorId`    int        DEFAULT NULL,
    `createdAt`   datetime   DEFAULT NULL,
    `updatedAt`   datetime   DEFAULT NULL,
    `isDefault`   tinyint(1) DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `exam_scale_question_series`
--

CREATE TABLE `exam_scale_question_series`
(
    `id`                  int NOT NULL,
    `examScaleId`         int      DEFAULT NULL,
    `examQuestionSerieId` int      DEFAULT NULL,
    `coefficient`         float    DEFAULT NULL,
    `createdAt`           datetime DEFAULT NULL,
    `updatedAt`           datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `exam_sessions`
--

CREATE TABLE `exam_sessions`
(
    `id`                int NOT NULL,
    `name`              longtext,
    `description`       longtext,
    `date`              datetime     DEFAULT NULL,
    `isOpen`            tinyint(1)   DEFAULT NULL,
    `successConditions` json         DEFAULT NULL,
    `examId`            int          DEFAULT NULL,
    `order`             int          DEFAULT NULL,
    `authorId`          int          DEFAULT NULL,
    `createdAt`         datetime     DEFAULT NULL,
    `updatedAt`         datetime     DEFAULT NULL,
    `duration`          varchar(255) DEFAULT NULL,
    `dateEnd`           datetime     DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `exam_sessions_groups`
--

CREATE TABLE `exam_sessions_groups`
(
    `id`            int NOT NULL,
    `groupeId`      int      DEFAULT NULL,
    `examSessionId` int      DEFAULT NULL,
    `createdAt`     datetime DEFAULT NULL,
    `updatedAt`     datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `exam_type_qcm`
--

CREATE TABLE `exam_type_qcm`
(
    `typeQcmId` int DEFAULT NULL,
    `examId`    int DEFAULT NULL,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `fiches`
--

CREATE TABLE `fiches`
(
    `id`           int      NOT NULL,
    `name`         varchar(255) DEFAULT NULL,
    `file`         varchar(255) DEFAULT NULL,
    `image`        varchar(255) DEFAULT NULL,
    `deleted`      tinyint(1)   DEFAULT '0',
    `createdAt`    datetime NOT NULL,
    `updatedAt`    datetime NOT NULL,
    `courId`       int          DEFAULT NULL,
    `isAccessible` tinyint(1)   DEFAULT '1'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `fiches_groups`
--

CREATE TABLE `fiches_groups`
(
    `id`        int NOT NULL,
    `groupeId`  int      DEFAULT NULL,
    `ficheId`   int      DEFAULT NULL,
    `createdAt` datetime DEFAULT NULL,
    `updatedAt` datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `files`
--

CREATE TABLE `files`
(
    `id`                 int      NOT NULL,
    `name`               varchar(255) DEFAULT NULL,
    `file`               varchar(255) DEFAULT NULL,
    `image`              varchar(255) DEFAULT NULL,
    `type`               varchar(255) DEFAULT NULL,
    `mimetype`           varchar(255) DEFAULT NULL,
    `createdAt`          datetime NOT NULL,
    `updatedAt`          datetime NOT NULL,
    `courId`             int          DEFAULT NULL,
    `messageId`          int          DEFAULT NULL,
    `fileId`             int          DEFAULT NULL,
    `fileImageId`        int          DEFAULT NULL,
    `messageFileImageId` int          DEFAULT NULL,
    `externalLink`       text,
    `notionId`           int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `folders`
--

CREATE TABLE `folders`
(
    `id`        int      NOT NULL,
    `name`      varchar(255) DEFAULT NULL,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `forfaits`
--

CREATE TABLE `forfaits`
(
    `id`                   int      NOT NULL,
    `name`                 varchar(255) DEFAULT NULL,
    `price`                varchar(255) DEFAULT NULL,
    `time`                 datetime     DEFAULT NULL,
    `createdAt`            datetime NOT NULL,
    `updatedAt`            datetime NOT NULL,
    `description`          text,
    `image`                varchar(255) DEFAULT NULL,
    `defaultChecked`       tinyint(1)   DEFAULT '0',
    `order`                int          DEFAULT '1',
    `isPublished`          tinyint(1)   DEFAULT '0',
    `type`                 varchar(255) DEFAULT 'UNIQUE',
    `requiredForfaitId`    int          DEFAULT NULL,
    `parentId`             int          DEFAULT NULL,
    `creditCost`           int          DEFAULT '0',
    `creditGiven`          int          DEFAULT '0',
    `canExpire`            tinyint(1)   DEFAULT '0',
    `daysBeforeExpiration` int          DEFAULT NULL,
    `requiredGroups`       json         DEFAULT NULL,
    `isLocked`             tinyint(1)   DEFAULT '0',
    `name_en`              text,
    `description_en`       text,
    `name_it`              text,
    `description_it`       text,
    `name_de`              text,
    `description_de`       text,
    `name_es`              text,
    `description_es`       text,
    `products`             json         DEFAULT NULL,
    `paymentSettings`      json         DEFAULT NULL,
    `emailSettings`        json         DEFAULT NULL,
    `promoCodes`           json         DEFAULT NULL,
    `hasPromoCode`         tinyint(1)   DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `forfait_groups`
--

CREATE TABLE `forfait_groups`
(
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL,
    `forfaitId` int      NOT NULL,
    `groupeId`  int      NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formation`
--

CREATE TABLE `formation`
(
    `id`             int NOT NULL,
    `name`           longtext,
    `description`    longtext,
    `image`          varchar(255) DEFAULT NULL,
    `color1`         varchar(255) DEFAULT NULL,
    `color2`         varchar(255) DEFAULT NULL,
    `order`          int          DEFAULT NULL,
    `isPublished`    tinyint(1)   DEFAULT '0',
    `authorId`       int          DEFAULT NULL,
    `createdAt`      datetime     DEFAULT NULL,
    `updatedAt`      datetime     DEFAULT NULL,
    `name_en`        text,
    `description_en` longtext,
    `name_it`        text,
    `description_it` longtext,
    `name_de`        text,
    `description_de` longtext,
    `name_es`        text,
    `description_es` longtext
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formations_groups`
--

CREATE TABLE `formations_groups`
(
    `id`          int NOT NULL,
    `groupeId`    int      DEFAULT NULL,
    `formationId` int      DEFAULT NULL,
    `createdAt`   datetime DEFAULT NULL,
    `updatedAt`   datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formation_block`
--

CREATE TABLE `formation_block`
(
    `id`              int NOT NULL,
    `name`            longtext,
    `type`            varchar(255) DEFAULT 'single',
    `order`           int          DEFAULT NULL,
    `formationStepId` int          DEFAULT NULL,
    `authorId`        int          DEFAULT NULL,
    `createdAt`       datetime     DEFAULT NULL,
    `updatedAt`       datetime     DEFAULT NULL,
    `settings`        json         DEFAULT NULL,
    `coursId`         int          DEFAULT NULL,
    `titleId`         int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formation_element`
--

CREATE TABLE `formation_element`
(
    `id`                   int NOT NULL,
    `name`                 longtext,
    `description`          longtext,
    `image`                varchar(255) DEFAULT NULL,
    `type`                 varchar(255) DEFAULT NULL,
    `objectId`             int          DEFAULT NULL,
    `text`                 longtext,
    `order`                int          DEFAULT NULL,
    `formationStepId`      int          DEFAULT NULL,
    `coursId`              int          DEFAULT NULL,
    `mcqId`                int          DEFAULT NULL,
    `authorId`             int          DEFAULT NULL,
    `createdAt`            datetime     DEFAULT NULL,
    `updatedAt`            datetime     DEFAULT NULL,
    `settings`             json         DEFAULT NULL,
    `blockId`              int          DEFAULT NULL,
    `coursSupportId`       int          DEFAULT NULL,
    `eventId`              int          DEFAULT NULL,
    `titleId`              int          DEFAULT NULL,
    `isAccessible`         tinyint(1)   DEFAULT '1',
    `questionId`           int          DEFAULT NULL,
    `headerMcqId`          int          DEFAULT NULL,
    `footerQuestionId`     int          DEFAULT NULL,
    `name_en`              text,
    `description_en`       text,
    `text_en`              text,
    `name_it`              text,
    `description_it`       text,
    `text_it`              text,
    `name_de`              text,
    `description_de`       text,
    `text_de`              text,
    `name_es`              text,
    `description_es`       text,
    `text_es`              text,
    `challengeId`          int          DEFAULT NULL,
    `forfaitId`            int          DEFAULT NULL,
    `userPropertyFolderId` int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formation_elements_forfaits`
--

CREATE TABLE `formation_elements_forfaits`
(
    `value`     varchar(255) DEFAULT NULL,
    `elementId` int          DEFAULT NULL,
    `forfaitId` int          DEFAULT NULL,
    `createdAt` datetime     DEFAULT NULL,
    `updatedAt` datetime     DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formation_elements_groups`
--

CREATE TABLE `formation_elements_groups`
(
    `groupeId`           int DEFAULT NULL,
    `formationElementId` int DEFAULT NULL,
    `createdAt`          datetime NOT NULL,
    `updatedAt`          datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formation_elements_groups_unlock`
--

CREATE TABLE `formation_elements_groups_unlock`
(
    `formationElementId` int DEFAULT NULL,
    `groupeId`           int DEFAULT NULL,
    `createdAt`          datetime NOT NULL,
    `updatedAt`          datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formation_module_element_progress`
--

CREATE TABLE `formation_module_element_progress`
(
    `id`              int NOT NULL,
    `isActive`        tinyint(1) DEFAULT '0',
    `isFinished`      tinyint(1) DEFAULT '0',
    `formationStepId` int        DEFAULT NULL,
    `userId`          int        DEFAULT NULL,
    `createdAt`       datetime   DEFAULT NULL,
    `updatedAt`       datetime   DEFAULT NULL,
    `formationId`     int        DEFAULT '1'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formation_section`
--

CREATE TABLE `formation_section`
(
    `id`              int NOT NULL,
    `name`            longtext,
    `description`     longtext,
    `image`           varchar(255) DEFAULT NULL,
    `order`           int          DEFAULT NULL,
    `isPublished`     tinyint(1)   DEFAULT '0',
    `color1`          varchar(255) DEFAULT NULL,
    `color2`          varchar(255) DEFAULT NULL,
    `parentSectionId` int          DEFAULT NULL,
    `formationId`     int          DEFAULT NULL,
    `authorId`        int          DEFAULT NULL,
    `createdAt`       datetime     DEFAULT NULL,
    `updatedAt`       datetime     DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formation_session`
--

CREATE TABLE `formation_session`
(
    `id`            int NOT NULL,
    `isActive`      tinyint(1) DEFAULT '0',
    `isFinished`    tinyint(1) DEFAULT '0',
    `currentStepId` int        DEFAULT NULL,
    `formationId`   int        DEFAULT NULL,
    `userId`        int        DEFAULT NULL,
    `createdAt`     datetime   DEFAULT NULL,
    `updatedAt`     datetime   DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `formation_step`
--

CREATE TABLE `formation_step`
(
    `id`          int NOT NULL,
    `name`        longtext,
    `description` longtext,
    `image`       varchar(255) DEFAULT NULL,
    `order`       int          DEFAULT NULL,
    `isPublished` tinyint(1)   DEFAULT '0',
    `sectionId`   int          DEFAULT NULL,
    `authorId`    int          DEFAULT NULL,
    `createdAt`   datetime     DEFAULT NULL,
    `updatedAt`   datetime     DEFAULT NULL,
    `icon`        varchar(255) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `forums`
--

CREATE TABLE `forums`
(
    `id`             int NOT NULL,
    `name`           longtext,
    `description`    longtext,
    `tag`            varchar(255) DEFAULT NULL,
    `views`          int          DEFAULT '0',
    `isPinned`       tinyint(1)   DEFAULT '0',
    `parentId`       int          DEFAULT NULL,
    `image`          varchar(255) DEFAULT NULL,
    `createdAt`      datetime     DEFAULT NULL,
    `updatedAt`      datetime     DEFAULT NULL,
    `name_en`        text,
    `description_en` text,
    `name_it`        text,
    `description_it` text,
    `name_de`        text,
    `description_de` text,
    `name_es`        text,
    `description_es` text
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `forum_categories`
--

CREATE TABLE `forum_categories`
(
    `id`             int NOT NULL,
    `name`           longtext,
    `description`    longtext,
    `tag`            varchar(255) DEFAULT NULL,
    `views`          int          DEFAULT '0',
    `isPinned`       tinyint(1)   DEFAULT '0',
    `color`          varchar(255) DEFAULT NULL,
    `color2`         varchar(255) DEFAULT NULL,
    `forumId`        int          DEFAULT NULL,
    `createdAt`      datetime     DEFAULT NULL,
    `updatedAt`      datetime     DEFAULT NULL,
    `name_en`        text,
    `description_en` text,
    `name_it`        text,
    `description_it` text,
    `name_de`        text,
    `description_de` text,
    `name_es`        text,
    `description_es` text
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `forum_category_groups`
--

CREATE TABLE `forum_category_groups`
(
    `groupId`         int NOT NULL,
    `forumCategoryId` int NOT NULL,
    `createdAt`       datetime DEFAULT NULL,
    `updatedAt`       datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `global_announce`
--

CREATE TABLE `global_announce`
(
    `id`        int NOT NULL,
    `text`      longtext,
    `createdAt` datetime DEFAULT NULL,
    `updatedAt` datetime DEFAULT NULL,
    `text_en`   longtext,
    `text_it`   longtext,
    `text_de`   longtext,
    `text_es`   longtext
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `global_announce_groups`
--

CREATE TABLE `global_announce_groups`
(
    `id`               int NOT NULL,
    `groupeId`         int      DEFAULT NULL,
    `globalAnnounceId` int      DEFAULT NULL,
    `createdAt`        datetime DEFAULT NULL,
    `updatedAt`        datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `good_answers_stats_user_synthesis`
--

CREATE TABLE `good_answers_stats_user_synthesis`
(
    `id`             int NOT NULL,
    `userId`         int        DEFAULT NULL,
    `ueId`           int        DEFAULT NULL,
    `ueCategoryId`   int        DEFAULT NULL,
    `coursId`        int        DEFAULT NULL,
    `notionId`       int        DEFAULT NULL,
    `goodAnswers`    int        DEFAULT NULL,
    `badAnswers`     int        DEFAULT NULL,
    `pointsObtained` float      DEFAULT NULL,
    `pointsMax`      float      DEFAULT NULL,
    `createdAt`      datetime   DEFAULT NULL,
    `updatedAt`      datetime   DEFAULT NULL,
    `isUpdating`     tinyint(1) DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `groupes`
--

CREATE TABLE `groupes`
(
    `id`           int      NOT NULL,
    `name`         varchar(255) DEFAULT NULL,
    `createdAt`    datetime NOT NULL,
    `updatedAt`    datetime NOT NULL,
    `role`         varchar(255) DEFAULT NULL,
    `image`        text,
    `isIndividual` tinyint(1)   DEFAULT '0',
    `folderId`     int          DEFAULT NULL,
    `name_en`      text,
    `name_it`      text,
    `name_de`      text,
    `name_es`      text
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `groups_responsibility`
--

CREATE TABLE `groups_responsibility`
(
    `id`                    int NOT NULL,
    `groupeId`              int      DEFAULT NULL,
    `responsibleOfGroupeId` int      DEFAULT NULL,
    `createdAt`             datetime DEFAULT NULL,
    `updatedAt`             datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `like_histories`
--

CREATE TABLE `like_histories`
(
    `id`        int      NOT NULL,
    `type`      varchar(255) DEFAULT NULL,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL,
    `postId`    int          DEFAULT NULL,
    `userId`    int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `logs`
--

CREATE TABLE `logs`
(
    `id`             int      NOT NULL,
    `logOperation`   varchar(255) DEFAULT NULL,
    `logData`        json         DEFAULT NULL,
    `userId`         int          DEFAULT NULL,
    `reporteeUserId` int          DEFAULT NULL,
    `qcmId`          int          DEFAULT NULL,
    `questionId`     int          DEFAULT NULL,
    `answerId`       int          DEFAULT NULL,
    `forfaitId`      int          DEFAULT NULL,
    `postId`         int          DEFAULT NULL,
    `createdAt`      datetime NOT NULL,
    `updatedAt`      datetime NOT NULL,
    `coursId`        int          DEFAULT NULL,
    `ip`             varchar(255) DEFAULT NULL,
    `userAgent`      varchar(255) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `mcq_scale`
--

CREATE TABLE `mcq_scale`
(
    `id`                               int      NOT NULL,
    `name`                             text     NOT NULL,
    `rules`                            longtext,
    `createdAt`                        datetime NOT NULL,
    `updatedAt`                        datetime NOT NULL,
    `isDefault`                        tinyint(1)   DEFAULT '0',
    `type`                             varchar(255) DEFAULT 'manual',
    `questionType`                     varchar(255) DEFAULT NULL,
    `pointsObtainedWhenNothingChecked` float        DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `mcq_scale_ues`
--

CREATE TABLE `mcq_scale_ues`
(
    `id`         int NOT NULL,
    `ueId`       int      DEFAULT NULL,
    `mcqScaleId` int      DEFAULT NULL,
    `createdAt`  datetime DEFAULT NULL,
    `updatedAt`  datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `messages`
--

CREATE TABLE `messages`
(
    `id`           int         NOT NULL,
    `text`         longtext,
    `tag`          varchar(255) DEFAULT NULL,
    `read`         tinyint(1)   DEFAULT NULL,
    `likes`        int          DEFAULT NULL,
    `createdAt`    datetime(6) NOT NULL,
    `updatedAt`    datetime(6) NOT NULL,
    `userId`       int          DEFAULT NULL,
    `discussionId` int          DEFAULT NULL,
    `ip`           varchar(255) DEFAULT NULL,
    `isDeleted`    tinyint(1)   DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `modules_quick_access`
--

CREATE TABLE `modules_quick_access`
(
    `id`           int NOT NULL,
    `challengeId`  int          DEFAULT NULL,
    `ueId`         int          DEFAULT NULL,
    `ueCategoryId` int          DEFAULT NULL,
    `coursId`      int          DEFAULT NULL,
    `name`         varchar(255) DEFAULT NULL,
    `description`  text,
    `type`         varchar(255) DEFAULT NULL,
    `settings`     json         DEFAULT NULL,
    `createdAt`    datetime     DEFAULT NULL,
    `updatedAt`    datetime     DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `modules_quick_access_type_qcm`
--

CREATE TABLE `modules_quick_access_type_qcm`
(
    `typeQcmId`           int DEFAULT NULL,
    `moduleQuickAccessId` int DEFAULT NULL,
    `createdAt`           datetime NOT NULL,
    `updatedAt`           datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notifications`
--

CREATE TABLE `notifications`
(
    `id`         int      NOT NULL,
    `text`       varchar(255) DEFAULT NULL,
    `type`       varchar(255) DEFAULT NULL,
    `method`     varchar(255) DEFAULT NULL,
    `objectId`   int          DEFAULT NULL,
    `parentId`   int          DEFAULT NULL,
    `count`      int          DEFAULT '0',
    `value`      varchar(255) DEFAULT NULL,
    `seen`       tinyint(1)   DEFAULT '0',
    `createdAt`  datetime NOT NULL,
    `updatedAt`  datetime NOT NULL,
    `userId`     int          DEFAULT NULL,
    `fromUserId` int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notions`
--

CREATE TABLE `notions`
(
    `id`             int      NOT NULL,
    `name`           text     NOT NULL,
    `createdAt`      datetime NOT NULL,
    `updatedAt`      datetime NOT NULL,
    `image`          text,
    `description`    text,
    `formula`        text,
    `name_en`        text,
    `description_en` text,
    `name_it`        text,
    `description_it` text,
    `name_de`        text,
    `description_de` text,
    `name_es`        text,
    `description_es` text
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notions_cours`
--

CREATE TABLE `notions_cours`
(
    `notionId`  int         NOT NULL,
    `coursId`   int         NOT NULL,
    `createdAt` datetime(6) NOT NULL,
    `updatedAt` datetime(6) NOT NULL,
    `autoAdded` tinyint(1) DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notions_keywords`
--

CREATE TABLE `notions_keywords`
(
    `id`        int          NOT NULL,
    `name`      varchar(255) NOT NULL,
    `notionId`  int DEFAULT NULL,
    `createdAt` datetime     NOT NULL,
    `updatedAt` datetime     NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notions_parents`
--

CREATE TABLE `notions_parents`
(
    `notionId`       int DEFAULT NULL,
    `parentNotionId` int DEFAULT NULL,
    `createdAt`      datetime NOT NULL,
    `updatedAt`      datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notions_posts`
--

CREATE TABLE `notions_posts`
(
    `notionId`  int DEFAULT NULL,
    `postId`    int DEFAULT NULL,
    `createdAt` datetime(6) NOT NULL,
    `updatedAt` datetime(6) NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notions_questions`
--

CREATE TABLE `notions_questions`
(
    `notionId`   int         NOT NULL,
    `questionId` int         NOT NULL,
    `createdAt`  datetime(6) NOT NULL,
    `updatedAt`  datetime(6) NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notions_question_answers`
--

CREATE TABLE `notions_question_answers`
(
    `notionId`  int         NOT NULL,
    `answerId`  int         NOT NULL,
    `createdAt` datetime(6) NOT NULL,
    `updatedAt` datetime(6) NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `payments`
--

CREATE TABLE `payments`
(
    `id`                   int          NOT NULL,
    `userId`               int          DEFAULT NULL,
    `state`                varchar(255) NOT NULL,
    `paymentType`          varchar(255) NOT NULL,
    `isNewUser`            tinyint(1)   DEFAULT NULL,
    `stripeSubscriptionId` varchar(255) DEFAULT NULL,
    `stripePriceId`        varchar(255) DEFAULT NULL,
    `numberOfMensuality`   int          DEFAULT '1',
    `totalMensuality`      int          DEFAULT NULL,
    `logId`                int          DEFAULT NULL,
    `createdAt`            datetime     NOT NULL,
    `updatedAt`            datetime     NOT NULL,
    `sum`                  float        DEFAULT NULL,
    `paymentMethodId`      int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `payments_forfaits`
--

CREATE TABLE `payments_forfaits`
(
    `id`        int      NOT NULL,
    `paymentId` int      NOT NULL,
    `forfaitId` int      NOT NULL,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `posts`
--

CREATE TABLE `posts`
(
    `id`                  int      NOT NULL,
    `title`               longtext,
    `text`                longtext,
    `tag`                 varchar(255) DEFAULT NULL,
    `views`               int          DEFAULT '0',
    `likes`               int          DEFAULT '0',
    `createdAt`           datetime NOT NULL,
    `updatedAt`           datetime NOT NULL,
    `userId`              int          DEFAULT NULL,
    `courId`              int          DEFAULT NULL,
    `qcmIdQcm`            int          DEFAULT NULL,
    `parentId`            int          DEFAULT NULL,
    `forumId`             int          DEFAULT NULL,
    `answerId`            int          DEFAULT NULL,
    `isResolved`          tinyint(1)   DEFAULT '0',
    `postTypeId`          int          DEFAULT NULL,
    `ip`                  varchar(255) DEFAULT NULL,
    `state`               varchar(255) DEFAULT NULL,
    `userIdForAiFeedback` int          DEFAULT NULL,
    `eventId`             int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `post_type`
--

CREATE TABLE `post_type`
(
    `id`                        int NOT NULL,
    `name`                      varchar(255) DEFAULT NULL,
    `image`                     varchar(255) DEFAULT NULL,
    `createdAt`                 datetime     DEFAULT NULL,
    `updatedAt`                 datetime     DEFAULT NULL,
    `type`                      varchar(255) DEFAULT NULL,
    `firstAnswerByChatGPT`      tinyint(1)   DEFAULT '0',
    `otherAnswersByChatGPT`     tinyint(1)   DEFAULT '0',
    `precisionPromptForChatGPT` text
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `post_type`
--

INSERT INTO `post_type` (`id`, `name`, `image`, `createdAt`, `updatedAt`, `type`, `firstAnswerByChatGPT`,
                         `otherAnswersByChatGPT`, `precisionPromptForChatGPT`)
VALUES (1, 'Erreur dans la correction', 'GKCrBdIDS2oq-cancel 2.png', '2021-08-26 14:18:47', '2023-04-25 09:20:09',
        'QCM', 1, 0,
        'L\'élève pense qu\'il y a une erreur dans la correction.\n\nEst-ce que la proposition est vraie ou fausse selon toi ? Qui a raison ?'),
       (2, 'La correction est ambigüe', 'UamiJ9eOfBZT-question.svg', '2021-08-26 14:19:17', '2023-04-18 20:21:59',
        'QCM', 1, 0,
        'L\'élève trouve que le QCM et sa correction est ambigüe. Qu\'en penses tu ? Est-ce vrai ou faux selon toi ? Justifie.'),
       (3, 'Je ne comprends pas une notion', 'Ri6L_NZvQxQh-question (1) 2.svg', '2021-08-26 14:19:36',
        '2023-04-18 20:21:20', 'QCM', 1, 0,
        'Un élève ne comprend pas la proposition de ce QCM ainsi que sa correction. Aide le à comprendre.'),
       (4, 'La question est hors concours', 'H2CqN43kms92-tombstone.svg', '2021-08-26 14:21:59', '2021-08-30 17:03:50',
        'QCM', 0, 0, NULL),
       (5, 'Problème / bug', 'VwZrqNOxmj-magnifying-glass.svg', '2021-08-28 09:58:59', '2023-04-18 20:25:43', 'QCM', 0,
        0, NULL),
       (7, 'Il manque quelque chose dans le cours', 'xoxG4q7M9wm3-puzzle.svg', '2021-08-30 17:07:52',
        '2021-08-30 17:07:52', 'COURS', 0, 0, NULL),
       (8, 'J\'ai besoin de comprendre une notion du cours', 'KQMAQwT9GICT-Ri6L_NZvQxQh-question (1) 2.svg',
        '2021-08-30 17:08:08', '2023-04-18 09:56:06', 'COURS', 1, 0,
        'Un élève ne comprend pas quelque chose dans ce cours. Aide le à mieux comprendre grâce aux informations fournies.'),
       (9, 'Il y a une erreur dans le cours', '7LyhvggG9yt8-GKCrBdIDS2oq-cancel 2.png', '2021-08-30 17:08:36',
        '2021-08-30 17:08:36', 'COURS', 0, 0, NULL),
       (10, 'J\'ai une requête (ne concerne pas la compréhension)', 'oK5PSY8xvTw3-request.svg', '2021-08-30 17:09:19',
        '2023-04-19 09:13:49', 'COURS', 0, 0, NULL),
       (11, 'Bug / Problèmes', 'OMMPVUXwPL-magnifying-glass.svg', '2021-08-30 17:10:40', '2023-04-18 20:25:56', 'COURS',
        0, 0, NULL),
       (12, 'Question sur ce que le prof a dit en cours', '79nURkP6ae-teacher.svg', '2023-04-18 20:24:32',
        '2023-04-18 20:24:32', 'QCM', 0, 0, NULL),
       (13, 'Je ne trouve pas cette notion dans le cours', 'y_OM4naNgK-detective.svg', '2023-04-18 20:31:23',
        '2023-04-18 20:31:23', 'QCM', 0, 0, NULL),
       (14, 'Question sur l\'évènement', NULL, '2023-08-23 14:23:10', '2023-08-23 14:23:10', 'EVENT', 0, 0, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `qcm`
--

CREATE TABLE `qcm`
(
    `id_qcm`                       int          NOT NULL,
    `id_lien`                      varchar(50)  NOT NULL,
    `titre`                        varchar(900) NOT NULL,
    `description`                  varchar(900)          DEFAULT NULL,
    `url_image`                    varchar(200)          DEFAULT NULL,
    `date_creation`                date         NOT NULL,
    `id_createur`                  int                   DEFAULT '0',
    `date_modif`                   date         NOT NULL,
    `ue`                           int          NOT NULL,
    `deleted`                      int          NOT NULL DEFAULT '0',
    `chronometre`                  int          NOT NULL DEFAULT '0',
    `annee`                        int                   DEFAULT NULL,
    `pseudo_createur`              text,
    `public`                       int          NOT NULL DEFAULT '0',
    `annale`                       int                   DEFAULT '0',
    `difficulty`                   float                 DEFAULT NULL,
    `views`                        int                   DEFAULT NULL,
    `authorId`                     int                   DEFAULT NULL,
    `sousCategorieIdSousCategorie` int                   DEFAULT NULL,
    `UEId`                         int                   DEFAULT NULL,
    `linkCoursId`                  int                   DEFAULT NULL,
    `isLinkedToCours`              tinyint(1)            DEFAULT '0',
    `timer_delay`                  int                   DEFAULT '90',
    `isPublished`                  tinyint(1)            DEFAULT '0',
    `isFullscreen`                 tinyint(1)            DEFAULT '0',
    `hasCheckboxes`                tinyint(1)            DEFAULT '1',
    `timesItCanBeDone`             int                   DEFAULT NULL,
    `randomizeQuestions`           tinyint(1)            DEFAULT '0',
    `showCorrectionAtEachStep`     tinyint(1)            DEFAULT '0',
    `groupQuestionsByTheme`        tinyint(1)            DEFAULT '0',
    `hasExternalQuestions`         tinyint(1)            DEFAULT '0',
    `questionPickingStrategy`      varchar(255)          DEFAULT 'normal',
    `correctionConfig`             json                  DEFAULT NULL,
    `randomizeQuestionsAnswers`    tinyint(1)            DEFAULT '0',
    `titre_en`                     text,
    `description_en`               text,
    `titre_it`                     text,
    `description_it`               text,
    `titre_de`                     text,
    `description_de`               text,
    `titre_es`                     text,
    `description_es`               text,
    `goToNextQuestionWhenTimesUp`  tinyint(1)            DEFAULT '0',
    `shouldResumeTime`             tinyint(1)            DEFAULT '0',
    `chronoByQuestionOrGlobal`     varchar(255)          DEFAULT 'timeByQuestion',
    `isAuthorChanged`              tinyint(1)            DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `qcm_default_type_qcm_for_questions`
--

CREATE TABLE `qcm_default_type_qcm_for_questions`
(
    `qcmId`     int      DEFAULT NULL,
    `typeQcmId` int      DEFAULT NULL,
    `createdAt` datetime DEFAULT NULL,
    `updatedAt` datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `qcm_saved_state`
--

CREATE TABLE `qcm_saved_state`
(
    `id`         int      NOT NULL,
    `qcmId`      int  DEFAULT NULL,
    `sessionId`  int  DEFAULT NULL,
    `userId`     int  DEFAULT NULL,
    `formValues` json DEFAULT NULL,
    `time`       int  DEFAULT '0',
    `createdAt`  datetime NOT NULL,
    `updatedAt`  datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `qcm_sessions`
--

CREATE TABLE `qcm_sessions`
(
    `id`                         int      NOT NULL,
    `questionsIdsDone`           json         DEFAULT NULL,
    `doneQuestionsCount`         int          DEFAULT '0',
    `goodQuestionsCount`         int          DEFAULT '0',
    `isActive`                   tinyint(1)   DEFAULT '1',
    `userId`                     int          DEFAULT NULL,
    `qcmId`                      int          DEFAULT NULL,
    `createdAt`                  datetime NOT NULL,
    `updatedAt`                  datetime NOT NULL,
    `isFinished`                 tinyint(1)   DEFAULT '0',
    `currentCoursId`             int          DEFAULT NULL,
    `currentState`               varchar(255) DEFAULT NULL,
    `examSessionId`              int          DEFAULT NULL,
    `examQuestionSerieId`        int          DEFAULT NULL,
    `assumedCorrectQuestionsIds` json         DEFAULT NULL,
    `correctQuestionsIds`        json         DEFAULT NULL,
    `luckyCorrectQuestionsIds`   json         DEFAULT NULL,
    `failedQuestionsIds`         json         DEFAULT NULL,
    `assumedFailedQuestionsIds`  json         DEFAULT NULL,
    `remainingQuestionsIds`      json         DEFAULT NULL,
    `formationElementId`         int          DEFAULT NULL,
    `settings`                   json         DEFAULT NULL,
    `challengeId`                int          DEFAULT NULL,
    `countGoodInARow`            int          DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `qcm_type_qcm`
--

CREATE TABLE `qcm_type_qcm`
(
    `typeQcmId` int         NOT NULL,
    `qcmId`     int         NOT NULL,
    `createdAt` datetime(6) NOT NULL,
    `updatedAt` datetime(6) NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `questions`
--

CREATE TABLE `questions`
(
    `id_question`                  int  NOT NULL,
    `id_qcm`                       int          DEFAULT NULL,
    `question`                     text,
    `reponse_A`                    varchar(800) DEFAULT NULL,
    `reponse_B`                    varchar(800) DEFAULT NULL,
    `reponse_C`                    varchar(800) DEFAULT NULL,
    `reponse_D`                    varchar(800) DEFAULT NULL,
    `reponse_E`                    varchar(800) DEFAULT NULL,
    `A`                            int          DEFAULT NULL,
    `B`                            int          DEFAULT NULL,
    `C`                            int          DEFAULT NULL,
    `D`                            int          DEFAULT NULL,
    `E`                            int          DEFAULT NULL,
    `url_image_q`                  varchar(800) DEFAULT NULL,
    `date_creation`                date NOT NULL,
    `date_modif`                   date NOT NULL,
    `explications`                 text,
    `id_sous_categorie`            int          DEFAULT NULL,
    `explication_A`                varchar(600) DEFAULT NULL,
    `explication_B`                varchar(600) DEFAULT NULL,
    `explication_C`                varchar(600) DEFAULT NULL,
    `explication_D`                varchar(600) DEFAULT NULL,
    `explication_E`                varchar(600) DEFAULT NULL,
    `url_image_explication`        varchar(800) DEFAULT NULL,
    `linkCoursId`                  int          DEFAULT NULL,
    `reponse_F`                    varchar(800) DEFAULT NULL,
    `reponse_G`                    varchar(800) DEFAULT NULL,
    `F`                            int          DEFAULT NULL,
    `G`                            int          DEFAULT NULL,
    `explication_F`                varchar(600) DEFAULT NULL,
    `explication_G`                varchar(600) DEFAULT NULL,
    `answersData`                  json         DEFAULT NULL,
    `order`                        int          DEFAULT NULL,
    `mcqScaleId`                   int          DEFAULT NULL,
    `autoAddNotions`               tinyint(1)   DEFAULT '1',
    `isCheckbox`                   tinyint(1)   DEFAULT '1',
    `isAnswerFreeText`             tinyint(1)   DEFAULT '0',
    `isAnswerUniqueChoiceInList`   tinyint(1)   DEFAULT '0',
    `isAnswerMultipleChoiceInList` tinyint(1)   DEFAULT '0',
    `calculatedDifficulty`         float        DEFAULT NULL,
    `definedDifficulty`            float        DEFAULT NULL,
    `evaluateCertainty`            tinyint(1)   DEFAULT '0',
    `authorId`                     int          DEFAULT NULL,
    `maxPoints`                    float        DEFAULT NULL,
    `question_en`                  text,
    `question_it`                  text,
    `question_de`                  text,
    `question_es`                  text,
    `isPublished`                  tinyint(1)   DEFAULT '1',
    `allowComments`                tinyint(1)   DEFAULT '1',
    `type`                         varchar(255) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `questions_double_parents`
--

CREATE TABLE `questions_double_parents`
(
    `questionId`        int        DEFAULT NULL,
    `parentQuestionId`  int        DEFAULT NULL,
    `isError`           tinyint(1) DEFAULT '0',
    `createdAt`         datetime NOT NULL,
    `updatedAt`         datetime NOT NULL,
    `parentQuestionId2` int        DEFAULT NULL,
    `id`                int      NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `questions_parents`
--

CREATE TABLE `questions_parents`
(
    `questionId`       int        DEFAULT NULL,
    `parentQuestionId` int        DEFAULT NULL,
    `createdAt`        datetime NOT NULL,
    `updatedAt`        datetime NOT NULL,
    `isError`          tinyint(1) DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `questions_qcm`
--

CREATE TABLE `questions_qcm`
(
    `qcmId`      int DEFAULT NULL,
    `questionId` int DEFAULT NULL,
    `createdAt`  datetime NOT NULL,
    `updatedAt`  datetime NOT NULL,
    `order`      int DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `questions_type_qcm`
--

CREATE TABLE `questions_type_qcm`
(
    `typeQcmId`  int DEFAULT NULL,
    `questionId` int DEFAULT NULL,
    `createdAt`  datetime NOT NULL,
    `updatedAt`  datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `question_answers`
--

CREATE TABLE `question_answers`
(
    `id`                       int         NOT NULL,
    `questionId`               int        DEFAULT NULL,
    `isTrue`                   tinyint(1) DEFAULT NULL,
    `text`                     text,
    `explanation`              text,
    `url_image_explanation`    text,
    `createdAt`                datetime(6) NOT NULL,
    `updatedAt`                datetime(6) NOT NULL,
    `autoAddNotions`           tinyint(1) DEFAULT '1',
    `isHorsConcours`           tinyint(1) DEFAULT '0',
    `url_image`                text,
    `isAllPointsOrNothing`     tinyint(1) DEFAULT '0',
    `text_en`                  text,
    `explanation_en`           text,
    `url_image_explanation_en` text,
    `url_image_en`             text,
    `text_it`                  text,
    `explanation_it`           text,
    `url_image_explanation_it` text,
    `url_image_it`             text,
    `text_de`                  text,
    `explanation_de`           text,
    `url_image_explanation_de` text,
    `url_image_de`             text,
    `text_es`                  text,
    `explanation_es`           text,
    `url_image_explanation_es` text,
    `url_image_es`             text
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `question_cours`
--

CREATE TABLE `question_cours`
(
    `questionId` int DEFAULT NULL,
    `coursId`    int DEFAULT NULL,
    `createdAt`  datetime NOT NULL,
    `updatedAt`  datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `question_type_question`
--

CREATE TABLE `question_type_question`
(
    `typeQuestionId` int         NOT NULL,
    `questionId`     int         NOT NULL,
    `createdAt`      datetime(6) NOT NULL,
    `updatedAt`      datetime(6) NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `ranks`
--

CREATE TABLE `ranks`
(
    `id`        int      NOT NULL,
    `name`      varchar(255) DEFAULT NULL,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `rooms`
--

CREATE TABLE `rooms`
(
    `id`          int      NOT NULL,
    `image`       varchar(255) DEFAULT NULL,
    `name`        varchar(255) DEFAULT NULL,
    `description` varchar(255) DEFAULT NULL,
    `seats`       int          DEFAULT NULL,
    `buildingId`  int          DEFAULT NULL,
    `createdAt`   datetime NOT NULL,
    `updatedAt`   datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `SequelizeMeta`
--

CREATE TABLE `SequelizeMeta`
(
    `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb3
  COLLATE = utf8_unicode_ci;

--
-- Déchargement des données de la table `SequelizeMeta`
--

INSERT INTO `SequelizeMeta` (`name`)
VALUES ('20200626004656-uecategoryvisibility.js'),
       ('20200626200714-coursDatediffusion.js'),
       ('20200628232345-qcmUpdateLinkToCours.js'),
       ('20200628235746-qcmUpdateCreateur.js'),
       ('20200724181841-qcmIntegrationMoreQuestions.js'),
       ('20200728180638-forum.js'),
       ('20200728182850-forum_associations.js'),
       ('20200728185317-forum_associations_2.js'),
       ('20200728192918-forum_posts_association.js'),
       ('20200728203118-forum_dates.js'),
       ('20200729173810-groupsforumcategorydates.js'),
       ('20200805181345-date_diffusion.js'),
       ('20200807000246-add_duree_cours.js'),
       ('20200811154751-add_post_view_history.js'),
       ('20200819192616-qcm-statistique-more-precise-date.js'),
       ('20200821002919-ue-moyenne.js'),
       ('20200821011710-uecategorie-moyenne.js'),
       ('20200901151722-forfaits.js'),
       ('20200908212914-order_ue.js'),
       ('20200910200047-published_MCQ.js'),
       ('20200911190411-published_MCQ_default_false.js'),
       ('20200912161449-device-notification-added.js'),
       ('20201013100243-forfaits2.js'),
       ('20201017144116-create-token.js'),
       ('20201020165406-add-device-info-to-token-table.js'),
       ('20201021132251-device-token-device-name-added.js'),
       ('20201025124922-forfaits_custom_links.js'),
       ('20201025132636-forfaits_custom_links_dates.js'),
       ('20201027155027-users_ues_notifications.js'),
       ('20201103123405-privatemessagefiles.js'),
       ('20201114172319-add_name_custom_links.js'),
       ('20201118121702-add_base_revision.js'),
       ('20201119185718-mod_base_revision.js'),
       ('20201201115821-add_credit_cost_forfaits.js'),
       ('20201202144231-add_credit_given_forfaits.js'),
       ('20201214154503-mcq_modular_questions.js'),
       ('20201214180543-mcq_answers_questions.js'),
       ('20201215162344-notions.js'),
       ('20201216103233-type_qcm.js'),
       ('20201217203656-optionnal_legacy_question.js'),
       ('20210118143115-order_mcq_questions.js'),
       ('20210125144056-notions-question-answers.js'),
       ('20210203173317-set-default_order_mcq_questions.js'),
       ('20210204171319-migrate-all-questions-to-new-system.js'),
       ('20210211141627-notion_image.js'),
       ('20210217193550-mcq_scale.js'),
       ('20210218144329-default_mcq_scale.js'),
       ('20210218151152-mcq_scale_default.js'),
       ('20210316120349-file_external_link.js'),
       ('20210329174355-add_notions_keywords.js'),
       ('20210401124155-notions_keywords_unique_name.js'),
       ('20210405100116-answer_autoaddnotion_toggle.js'),
       ('20210428150947-cours_add_pdf_preview.js'),
       ('20210503120423-forfait_expiration_time.js'),
       ('20210503141741-log_system.js'),
       ('20210505144656-defaultPaymentSystem.js'),
       ('20210507134542-qcm_add_more_types.js'),
       ('20210507141857-qcm_add_more_types_2.js'),
       ('20210512122502-freeText.js'),
       ('20210512153125-upgradeMCQ-system.js'),
       ('20210517125207-updateSessionQcms.js'),
       ('20210521093054-cours_layout_video.js'),
       ('20210521113113-cours_video_update.js'),
       ('20210521162605-numberOfPagesPdfCours.js'),
       ('20210531104047-numberOfPagesAutoGeneration.js'),
       ('20210604111043-mcqChoicesInList.js'),
       ('20210607121729-updateQuestionsQcmFullscreen.js'),
       ('20210607132713-requiredGroupsForForfaits.js'),
       ('20210615112506-externalIdUser.js'),
       ('20210615191112-lockedForfait.js'),
       ('20210616154313-orderCategorieCours.js'),
       ('20210616154918-defaultOrderCoursCategories.js'),
       ('20210621104906-evaluateCertainty.js'),
       ('20210621121525-type_question.js'),
       ('20210622132410-user_background.js'),
       ('20210623105559-questions_bigger.js'),
       ('20210629142533-addDescriptionCategory.js'),
       ('20210705145014-addNotePerQuestion.js'),
       ('20210706120438-updateAllGradesPoints.js'),
       ('20210707121933-ponderatedGrade.js'),
       ('20210709151804-statsNotionsUser.js'),
       ('20210723123704-itemHorsConcours.js'),
       ('20210723143919-coursIsVisible.js'),
       ('20210724160004-statsQuestionsAnswers.js'),
       ('20210805144139-migrateAllUpdateInfosCours.js'),
       ('20210805153343-migrateDateDiffusionUpdateInfos.js'),
       ('20210805165053-migrateDateDiffUpdateInfos.js'),
       ('20210809083934-migrateAllStatsQuestionsAnswers.js'),
       ('20210823132123-report_answers_mcq.js'),
       ('20210823145202-post_type.js'),
       ('20210826092124-ue_isVisible.js'),
       ('20210826120032-setResolvedIfBeforeJuly.js'),
       ('20210830152122-typeOfcommentType.js'),
       ('20210830170803-changeModif.js'),
       ('20210901094013-logsCours.js'),
       ('20210901095442-CoursfileupdateDate.js'),
       ('20210901134024-notions_parents.js'),
       ('20210912175117-notions_description.js'),
       ('20210923123743-notionCoursAutoAdded.js'),
       ('20210924091628-dateDiffusionQcm.js'),
       ('20211018161932-bareme_type.js'),
       ('20211021090808-groupQuestionsMcq.js'),
       ('20211021094931-sessionCurrentCours.js'),
       ('20211021132241-sessionCurrentState.js'),
       ('20211026122501-migrateTypeQCM.js'),
       ('20211028121006-groupRoles.js'),
       ('20211029142446-uecategories_groups_cours_groups.js'),
       ('20211115103505-groups_responsibility.js'),
       ('20211115144959-migrateGroupsToNewPermissions.js'),
       ('20211122120513-qcm_dynamic_questions.js'),
       ('20211206130243-qcm_correction_config.js'),
       ('20211206141506-qcm_correction_defaultValue.js'),
       ('20211221111002-fiches_groupes.js'),
       ('20211221143926-fiches_update.js'),
       ('20211229172843-configupdate.js'),
       ('20211230145315-MIGRATEDATA.js'),
       ('20211230150825-allInGroupTmp.js'),
       ('20220103143941-configCustomByDomain.js'),
       ('20220104112649-linkCoursIdIndex.js'),
       ('20220106144028-addIndexes.js'),
       ('20220110192324-updateStatsQcm.js'),
       ('20220110195803-questionAnswersIndex.js'),
       ('20220111110614-deleteOldColumns.js'),
       ('20220111142438-formation.js'),
       ('20220111142911-formationSection.js'),
       ('20220111142912-formationStep.js'),
       ('20220111143709-formationElement.js'),
       ('20220111150512-formationProgression.js'),
       ('20220117152242-formationProgress.js'),
       ('20220118153047-formationGroups.js'),
       ('20220120130932-formationSession.js'),
       ('20220126184248-formationUpdate.js'),
       ('20220202163846-blocFormation.js'),
       ('20220202164345-modifyElement.js'),
       ('20220204102337-coursEnrichi.js'),
       ('20220204110805-coursSettings.js'),
       ('20220204152428-blockSettings.js'),
       ('20220206195740-exam.js'),
       ('20220206195837-examQuizzSerie.js'),
       ('20220207144644-cours_supports.js'),
       ('20220207144830-formation-element-coursSupport.js'),
       ('20220208154941-examSession.js'),
       ('20220208155401-exam_session_groups.js'),
       ('20220208155542-exam_scales.js'),
       ('20220208181752-exam_scale_question_series.js'),
       ('20220214132547-dateDiffusionExamen.js'),
       ('20220214152504-examSessionDuration.js'),
       ('20220214163042-updateqcmSessionAddExamSessionId.js'),
       ('20220214164817-examQuestionSerieDates.js'),
       ('20220215143813-examQuestiionSerieModif.js'),
       ('20220216144926-examResult.js'),
       ('20220221175141-updateBareme.js'),
       ('20220228112240-examPublished.js'),
       ('20220228112815-event.js'),
       ('20220228134524-eventDateDiff.js'),
       ('20220228135313-add_elements_eventId.js'),
       ('20220305173205-order_formation_migration.js'),
       ('20220308171128-titlesTable.js'),
       ('20220308172946-elementTitleForeignKey.js'),
       ('20220317115548-defaultSettingsCours.js'),
       ('20220321140041-blockInCours.js'),
       ('20220322120655-createHigherResolutionPdfPreview.js'),
       ('20220324141747-titleIdBlock.js'),
       ('20220324143709-titleFontWeight.js'),
       ('20220328150446-UETypeFormation.js'),
       ('20220329122158-importedCours.js'),
       ('20220330152050-userCodeForAntemed.js'),
       ('20220407123420-questionHierarchyDouble.js'),
       ('20220407123718-questionParentsEror.js'),
       ('20220407124206-questionParentsDoubleFix.js'),
       ('20220407125559-updateQcmSessionForSmartQuizz.js'),
       ('20220407132443-updateQcmSessionForSmartQuizz2.js'),
       ('20220407143013-addIdTest.js'),
       ('20220413140745-randomizeQuestionsAnswers.js'),
       ('20220414140454-mcqGroupsUnlock.js'),
       ('20220414151947-elementIdInSessionMcq.js'),
       ('20220418135206-questionAnswerImage.js'),
       ('20220418152032-questionAuthorId.js'),
       ('20220418152600-setDefaultAuthorIdOnQuestions.js'),
       ('20220421122026-questions_type_qcm.js'),
       ('20220421135552-migrateQuestionsTypeQcm.js'),
       ('20220426150946-elementsGroupsAccess.js'),
       ('20220426161811-formationElementAccess.js'),
       ('20220427183904-migrateElementSecondaires.js'),
       ('20220502161456-typeQcmsCoursSettings.js'),
       ('20220503142929-typeQcmsCoursSettingsMigration.js'),
       ('20220505130815-updateQuestionsQcmOrder.js'),
       ('20220505134107-updateDefaultQuestionQcmOrder.js'),
       ('20220525095049-userUpdate.js'),
       ('20220525141117-dateDiffusionEnd.js'),
       ('20220529063852-groupeImage.js'),
       ('20220529064438-blockedUsers.js'),
       ('20220609114327-questionWithElementHeader.js'),
       ('20220612154652-ipForCommentsAndPM.js'),
       ('20220613122935-qcmWithElementHeader.js'),
       ('20220615135351-migrateQcmImages.js'),
       ('20220615161315-mcqScalesByUE.js'),
       ('20220616124718-answerItemAllOrNothing.js'),
       ('20220620094804-groupChat.js'),
       ('20220620121507-examEventTypes.js'),
       ('20220623173833-deletedMessage.js'),
       ('20220628130630-dateFinExam.js'),
       ('20220705112505-dateDiffAvailability.js'),
       ('20220705130108-migrateExamGroupsToDatesDiff.js'),
       ('20220705141021-groupesIndividuels.js'),
       ('20220705141607-groupesIndividuelsCreationBatch.js'),
       ('20220707155712-dateDiffEntireDay.js'),
       ('20220714125200-folders.js'),
       ('20220714131458-foldersForGroupsExamsEvents.js'),
       ('20220722155024-notionsPosts.js'),
       ('20220803142601-deletedAtUser.js'),
       ('20220809144729-update_user_asksForDeletion.js'),
       ('20220817180527-hasAcceptedCGU.js'),
       ('20220822154809-user-add-appearsInTeam.js'),
       ('20220822174705-isUserreachableByPM.js'),
       ('20220822185715-isUserAppearingInSubjectsForAdmins.js'),
       ('20220823151438-qcmCourseLink.js'),
       ('20220823155618-migrateLinkedQuestionCours.js'),
       ('20220830120147-ipInLogs.js'),
       ('20220830155026-userAgentInLogs.js'),
       ('20220907142025-updateExamScaleToFloat.js'),
       ('20220907143556-updateMcqScaleDefault.js'),
       ('20220907152427-dates_diff_add_timezone.js'),
       ('20220912132521-configs_new_values.js'),
       ('20220914150923-configs_logo_print.js'),
       ('20220914170844-addMissingIndividualGroups.js'),
       ('20220916163406-addQcmStatId.js'),
       ('20220917131403-linkStatsQuestionsToStats.js'),
       ('20220923132320-mcqScaleUpdate.js'),
       ('20220928133515-questionWithElementFooter.js'),
       ('20220928135052-migrateQuestionElementFooter.js'),
       ('20221007140009-MULTI_LANGUAGES.js'),
       ('20221011152608-ConfigsAddEnabledLanguages.js'),
       ('20221018131302-UserLang.js'),
       ('20221025142827-updateTypesWithParentsTYpes.js'),
       ('20221027095916-defaultSerieExerciceType.js'),
       ('20221028144624-maxPointsQuestionFreeText.js'),
       ('20221028173105-onDeleteCascadeOnQcmSession.js'),
       ('20221101130821-publishedQuestion.js'),
       ('20221103130333-migratePublishedUnpiblished.js'),
       ('20221103164139-questionIdWithoutQuizz.js'),
       ('20221104203207-challenges.js'),
       ('20221104230711-challengesConditions.js'),
       ('20221104230835-badges.js'),
       ('20221107163846-challengeUserProgress.js'),
       ('20221108120311-challengesConditionsUpdate.js'),
       ('20221108131055-qcmSessionSettings.js'),
       ('20221109135554-qcmStatsGradeMcqIdCanBeNull.js'),
       ('20221109181819-QuickAccessModules.js'),
       ('20221110192137-challengeTypeQcm.js'),
       ('20221110233414-challengeGroupUnlock.js'),
       ('20221111185520-modulesQuickAccessTypeQcms.js'),
       ('20221111222503-updateSessionAddChallengeProgress.js'),
       ('20221111235045-updateSessionAddGoodInARow.js'),
       ('20221119215536-updateExamsSessionFix.js'),
       ('20221120000611-appearanceconfig.js'),
       ('20221124134959-timeSpentByUser.js'),
       ('20221128181014-challengeTranslated.js'),
       ('20221205165616-mcqChronometerUpdate.js'),
       ('20221206181950-mcqSavedState.js'),
       ('20221212044745-commentsEnableDisableSwitch.js'),
       ('20221228160109-questionsImportedMigration.js'),
       ('20230103101946-examHideResults.js'),
       ('20230110153947-alphanumericalQuestionType.js'),
       ('20230115222944-resetCache.js'),
       ('20230213170839-infiniteHierarchy-ue-categ.js'),
       ('20230214080824-infiniteHierarchy-ue-favourite.js'),
       ('20230222155714-titles-add-background.js'),
       ('20230301032904-infiniteHierarchy-cours-ue.js'),
       ('20230318221859-categUeCoursAlwaysVisible.js'),
       ('20230404170925-good_answers_stats_user_synthesis.js'),
       ('20230413142607-addIsUpdatingForSynthesis.js'),
       ('20230414181253-chatGPT_addColumns.js'),
       ('20230414183342-chatGPT_bot_user.js'),
       ('20230414192757-post_types_add.js'),
       ('20230417200050-moreCaractersUserPersonality.js'),
       ('20230418155812-IA_settings_in_config.js'),
       ('20230420122541-cours_image_add.js'),
       ('20230424122921-addPostAIAnswer.js'),
       ('20230427150349-visibleSubjects.js'),
       ('20230427180452-addMissingIndividualGroup_TMP_DIPLOMA.js'),
       ('20230502161957-addIndexOnLogOperations.js'),
       ('20230525124139-add_isAuthorChanged.js'),
       ('20230608042647-forfaitUpgrade.js'),
       ('20230626192449-elementFormulaire.js'),
       ('20230628185313-forfaitHasPromoCodeBoolean.js'),
       ('20230711161555-globalUserPropertiesAndCustomFields.js'),
       ('20230712162418-addWatermarkPictureStringPath.js'),
       ('20230723133246-add_CGU_Columns.js'),
       ('20230801223150-globalUserPropertiesDataAddColumns.js'),
       ('20230810162546-payments.js'),
       ('20230811140903-paymentsTableUpdate.js'),
       ('20230812164706-createBuildingsAndRoomsForEvents.js'),
       ('20230813124551-editBuildingsAddMoreAddress.js'),
       ('20230815133624-editEvents.js'),
       ('20230815135354-updateDateDiffusion.js'),
       ('20230815142354-dateDiffOrganizersAndParticipant.js'),
       ('20230816154017-batchEventChampsPersonnaliseToDescription.js'),
       ('20230817115830-batchEventEventOrganisers.js'),
       ('20230818132738-studentPresencePourFaireLappel.js'),
       ('20230822153714-commentsOnEvents.js'),
       ('20241119215536-updateExamsSessionFix.js'),
       ('20270822130042-createDefaultDateDiffusionGroupLinkForParticipantsEvent.js'),
       ('20280614154046-forfaitMigrateDefaultProduct.js'),
       ('20280812123653-createDefaultPaymentMethod.js'),
       ('20280823131741-eventDiscussionType.js');

-- --------------------------------------------------------

--
-- Structure de la table `sous_categories`
--

CREATE TABLE `sous_categories`
(
    `id_sous_categorie` int          NOT NULL,
    `sous_categorie`    varchar(300) NOT NULL,
    `ue`                int DEFAULT NULL,
    `ueIdId`            int DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `statistiques`
--

CREATE TABLE `statistiques`
(
    `id`              int   NOT NULL,
    `id_qcm`          int        DEFAULT NULL,
    `id_utilisateur`  int   NOT NULL,
    `note`            float NOT NULL,
    `date`            datetime   DEFAULT NULL,
    `qcmSessionId`    int        DEFAULT NULL,
    `ponderatedGrade` float      DEFAULT NULL,
    `isFirstTime`     tinyint(1) DEFAULT '1'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `statistiques_questions`
--

CREATE TABLE `statistiques_questions`
(
    `id_statistique_question`  int NOT NULL,
    `id_question`              int NOT NULL,
    `id_utilisateur`           int NOT NULL,
    `reponse`                  int         DEFAULT NULL,
    `answersData`              json        DEFAULT NULL,
    `time`                     float       DEFAULT NULL,
    `qcmSessionId`             int         DEFAULT NULL,
    `certainty`                int         DEFAULT NULL,
    `pointsObtained`           float       DEFAULT NULL,
    `ponderatedPointsObtained` float       DEFAULT NULL,
    `createdAt`                datetime(6) DEFAULT NULL,
    `updatedAt`                datetime(6) DEFAULT NULL,
    `statId`                   int         DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `stats_notions_users`
--

CREATE TABLE `stats_notions_users`
(
    `id`          int NOT NULL,
    `userId`      int      DEFAULT NULL,
    `notionId`    int      DEFAULT NULL,
    `goodAnswers` int      DEFAULT '0',
    `badAnswers`  int      DEFAULT '0',
    `createdAt`   datetime DEFAULT NULL,
    `updatedAt`   datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `stats_questions_answers`
--

CREATE TABLE `stats_questions_answers`
(
    `id`              int NOT NULL,
    `statsQuestionId` int          DEFAULT NULL,
    `answerId`        int          DEFAULT NULL,
    `value`           varchar(255) DEFAULT NULL,
    `isGood`          tinyint(1)   DEFAULT NULL,
    `createdAt`       datetime     DEFAULT NULL,
    `updatedAt`       datetime     DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `titles`
--

CREATE TABLE `titles`
(
    `id`              int NOT NULL,
    `name`            longtext,
    `size`            int          DEFAULT NULL,
    `offset`          int          DEFAULT NULL,
    `level`           int          DEFAULT NULL,
    `color1`          varchar(255) DEFAULT NULL,
    `color2`          varchar(255) DEFAULT NULL,
    `createdAt`       datetime     DEFAULT NULL,
    `updatedAt`       datetime     DEFAULT NULL,
    `fontWeight`      int          DEFAULT '600',
    `backgroundColor` varchar(255) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `titles`
--

INSERT INTO `titles` (`id`, `name`, `size`, `offset`, `level`, `color1`, `color2`, `createdAt`, `updatedAt`,
                      `fontWeight`, `backgroundColor`)
VALUES (1, 'Titre 1', 18, 0, 1, '#ffffff', '#ffffff', '2022-03-10 19:08:48', '2023-02-22 17:53:07', 600, '#0091ff'),
       (2, 'Titre 2', 16, 5, 2, '#000000', '#74a7ff', '2022-03-10 19:09:25', '2023-02-22 18:02:58', 600, '#28c875');

-- --------------------------------------------------------


--
-- Structure de la table `Tokens`
--

CREATE TABLE `Tokens`
(
    `id`        int      NOT NULL,
    `token`     varchar(255) DEFAULT NULL,
    `tag`       varchar(255) DEFAULT NULL,
    `userId`    int          DEFAULT NULL,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `type_qcm`
--

CREATE TABLE `type_qcm`
(
    `id`          int      NOT NULL,
    `name`        text     NOT NULL,
    `createdAt`   datetime NOT NULL,
    `updatedAt`   datetime NOT NULL,
    `description` text,
    `contentType` varchar(255) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `type_qcm_groups`
--

CREATE TABLE `type_qcm_groups`
(
    `id`        int NOT NULL,
    `groupeId`  int      DEFAULT NULL,
    `typeQcmId` int      DEFAULT NULL,
    `createdAt` datetime DEFAULT NULL,
    `updatedAt` datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `type_question`
--

CREATE TABLE `type_question`
(
    `id`        int      NOT NULL,
    `name`      text     NOT NULL,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `uecategories`
--

CREATE TABLE `uecategories`
(
    `id`              int      NOT NULL,
    `name`            varchar(255)      DEFAULT NULL,
    `image`           varchar(255)      DEFAULT NULL,
    `color`           varchar(255)      DEFAULT NULL,
    `createdAt`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `ueId`            int               DEFAULT NULL,
    `isVisible`       tinyint(1)        DEFAULT '1',
    `moyenneGenerale` float             DEFAULT NULL,
    `order`           int               DEFAULT NULL,
    `description`     varchar(255)      DEFAULT NULL,
    `name_en`         varchar(255)      DEFAULT NULL,
    `description_en`  varchar(255)      DEFAULT NULL,
    `name_it`         varchar(255)      DEFAULT NULL,
    `description_it`  varchar(255)      DEFAULT NULL,
    `name_de`         varchar(255)      DEFAULT NULL,
    `description_de`  varchar(255)      DEFAULT NULL,
    `name_es`         varchar(255)      DEFAULT NULL,
    `description_es`  varchar(255)      DEFAULT NULL,
    `parentId`        int               DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `uecategories_groups`
--

CREATE TABLE `uecategories_groups`
(
    `id`            int NOT NULL,
    `groupeId`      int        DEFAULT NULL,
    `uecategory_id` int        DEFAULT NULL,
    `createdAt`     datetime   DEFAULT NULL,
    `updatedAt`     datetime   DEFAULT NULL,
    `isFavorite`    tinyint(1) DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `ues`
--

CREATE TABLE `ues`
(
    `id`              int      NOT NULL,
    `name`            varchar(255) DEFAULT NULL,
    `description`     varchar(255) DEFAULT NULL,
    `image`           varchar(255) DEFAULT NULL,
    `color`           varchar(255) DEFAULT NULL,
    `color2`          varchar(255) DEFAULT NULL,
    `hasQcm`          tinyint(1)   DEFAULT '1',
    `hasAnnales`      tinyint(1)   DEFAULT '1',
    `createdAt`       datetime NOT NULL,
    `updatedAt`       datetime NOT NULL,
    `moyenneGenerale` float        DEFAULT NULL,
    `order`           int          DEFAULT NULL,
    `isVisible`       tinyint(1)   DEFAULT '1',
    `type`            varchar(255) DEFAULT 'SUBJECT',
    `name_en`         varchar(255) DEFAULT NULL,
    `description_en`  varchar(255) DEFAULT NULL,
    `name_it`         varchar(255) DEFAULT NULL,
    `description_it`  varchar(255) DEFAULT NULL,
    `name_de`         varchar(255) DEFAULT NULL,
    `description_de`  varchar(255) DEFAULT NULL,
    `name_es`         varchar(255) DEFAULT NULL,
    `description_es`  varchar(255) DEFAULT NULL,
    `isFolder`        tinyint(1)   DEFAULT '0',
    `parentId`        int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `ue_groups`
--

CREATE TABLE `ue_groups`
(
    `createdAt`  datetime NOT NULL,
    `updatedAt`  datetime NOT NULL,
    `groupeId`   int      NOT NULL,
    `ueId`       int      NOT NULL,
    `isFavorite` tinyint(1) DEFAULT '0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `ue_tuteurs`
--

CREATE TABLE `ue_tuteurs`
(
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL,
    `ueId`      int      NOT NULL,
    `userId`    int      NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `userPreferences`
--

CREATE TABLE `userPreferences`
(
    `id`                              int      NOT NULL,
    `emailPreferences`                text,
    `darkMode`                        tinyint(1) DEFAULT '0',
    `privateMessageEmailNotification` tinyint(1) DEFAULT '1',
    `coursUpdateEmailNotification`    tinyint(1) DEFAULT '0',
    `forumUpdateEmailNotification`    tinyint(1) DEFAULT '1',
    `createdAt`                       datetime NOT NULL,
    `updatedAt`                       datetime NOT NULL,
    `userId`                          int        DEFAULT NULL,
    `coursUpdateDeviceNotification`   tinyint(1) DEFAULT '1',
    `forumUpdateDeviceNotification`   tinyint(1) DEFAULT '1',
    `qcmUpdateDeviceNotification`     tinyint(1) DEFAULT '1'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `users`
--

CREATE TABLE `users`
(
    `id`                          int          NOT NULL,
    `username`                    varchar(255) NOT NULL,
    `email`                       varchar(255) NOT NULL,
    `password`                    varchar(255) NOT NULL,
    `role`                        varchar(255)          DEFAULT 'USER',
    `avatar`                      varchar(255)          DEFAULT '',
    `firstName`                   varchar(255)          DEFAULT NULL,
    `name`                        varchar(255)          DEFAULT NULL,
    `addressline1`                varchar(255)          DEFAULT NULL,
    `addressline2`                varchar(255)          DEFAULT NULL,
    `postcode`                    varchar(255)          DEFAULT NULL,
    `phone`                       varchar(255)          DEFAULT NULL,
    `city`                        varchar(255)          DEFAULT NULL,
    `ip`                          varchar(255)          DEFAULT NULL,
    `bio`                         longtext,
    `credits`                     int          NOT NULL DEFAULT '0',
    `warningReceived`             int          NOT NULL DEFAULT '0',
    `banned`                      tinyint(1)            DEFAULT '0',
    `resetPasswordToken`          varchar(255)          DEFAULT NULL,
    `lastActivityAt`              datetime              DEFAULT NULL,
    `isActive`                    tinyint(1)            DEFAULT '0',
    `createdAt`                   datetime     NOT NULL,
    `updatedAt`                   datetime     NOT NULL,
    `externalId`                  varchar(255)          DEFAULT NULL,
    `background`                  json                  DEFAULT NULL,
    `userCodeName`                varchar(255)          DEFAULT NULL,
    `parentsEmail`                varchar(255)          DEFAULT NULL,
    `parentsPhone`                varchar(255)          DEFAULT NULL,
    `parentsProfession`           varchar(255)          DEFAULT NULL,
    `deletedAt`                   datetime              DEFAULT NULL,
    `asksForDeletion`             tinyint(1)            DEFAULT '0',
    `hasAcceptedCGU`              tinyint(1)            DEFAULT '1',
    `appearsInTeam`               tinyint(1)            DEFAULT '1',
    `isReachableByPrivateMessage` tinyint(1)            DEFAULT '1',
    `appearsInSubjects`           tinyint(1)            DEFAULT '1',
    `lang`                        varchar(255)          DEFAULT 'fr',
    `bot_personality`             longtext,
    `bot`                         tinyint(1)            DEFAULT '0',
    `versionAcceptedCGU`          int                   DEFAULT NULL,
    `whenAcceptedCGU`             datetime              DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `userStats`
--

CREATE TABLE `userStats`
(
    `id`                      int      NOT NULL,
    `seenClasses`             int DEFAULT '0',
    `seenAnnales`             int DEFAULT '0',
    `seenMcq`                 int DEFAULT '0',
    `mcqDone`                 int DEFAULT '0',
    `postsSent`               int DEFAULT '0',
    `postsReceived`           int DEFAULT '0',
    `privateMessagesReceived` int DEFAULT '0',
    `privateMessagesSent`     int DEFAULT '0',
    `likesReceived`           int DEFAULT '0',
    `likesGiven`              int DEFAULT '0',
    `accountConnections`      int DEFAULT '0',
    `downloadedFiles`         int DEFAULT '0',
    `createdAt`               datetime NOT NULL,
    `updatedAt`               datetime NOT NULL,
    `userId`                  int DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `users_blockeds`
--

CREATE TABLE `users_blockeds`
(
    `userId`        int      DEFAULT NULL,
    `userBlockedId` int      DEFAULT NULL,
    `createdAt`     datetime DEFAULT NULL,
    `updatedAt`     datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `users_ues_notifications`
--

CREATE TABLE `users_ues_notifications`
(
    `userId`    int NOT NULL,
    `ueId`      int NOT NULL,
    `updatedAt` datetime DEFAULT NULL,
    `createdAt` datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `user_devices`
--

CREATE TABLE `user_devices`
(
    `id`           int         NOT NULL,
    `deviceToken`  varchar(255) DEFAULT NULL,
    `userId`       int          DEFAULT NULL,
    `createdAt`    datetime(6) NOT NULL,
    `updatedAt`    datetime(6) NOT NULL,
    `uuid`         varchar(255) DEFAULT NULL,
    `platform`     varchar(255) DEFAULT NULL,
    `osVersion`    varchar(255) DEFAULT NULL,
    `model`        varchar(255) DEFAULT NULL,
    `manufacturer` varchar(255) DEFAULT NULL,
    `isVirtual`    tinyint(1)   DEFAULT NULL,
    `deviceName`   varchar(255) DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `user_groups`
--

CREATE TABLE `user_groups`
(
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL,
    `userId`    int      NOT NULL,
    `groupeId`  int      NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `user_properties_data`
--

CREATE TABLE `user_properties_data`
(
    `id`          int NOT NULL,
    `value`       text,
    `elementId`   int      DEFAULT NULL,
    `userId`      int      DEFAULT NULL,
    `createdAt`   datetime DEFAULT NULL,
    `updatedAt`   datetime DEFAULT NULL,
    `values`      json     DEFAULT NULL,
    `logObjectId` int      DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `user_properties_folders`
--

CREATE TABLE `user_properties_folders`
(
    `id`        int NOT NULL,
    `name`      varchar(255) DEFAULT NULL,
    `name_en`   varchar(255) DEFAULT NULL,
    `name_es`   varchar(255) DEFAULT NULL,
    `name_de`   varchar(255) DEFAULT NULL,
    `name_it`   varchar(255) DEFAULT NULL,
    `createdAt` datetime     DEFAULT NULL,
    `updatedAt` datetime     DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `user_sessions`
--

CREATE TABLE `user_sessions`
(
    `id`             int      NOT NULL,
    `lastIp`         varchar(255) DEFAULT NULL,
    `userAgent`      text,
    `device`         text,
    `visible`        tinyint(1)   DEFAULT '1',
    `isAdminSession` tinyint(1)   DEFAULT '0',
    `createdAt`      datetime NOT NULL,
    `updatedAt`      datetime NOT NULL,
    `userId`         int          DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `user_stats_by_day`
--

CREATE TABLE `user_stats_by_day`
(
    `id`            int NOT NULL,
    `userId`        int      DEFAULT NULL,
    `time`          int      DEFAULT '0',
    `questionsDone` int      DEFAULT '0',
    `date`          datetime DEFAULT NULL,
    `createdAt`     datetime DEFAULT NULL,
    `updatedAt`     datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `user_temp_json`
--

CREATE TABLE `user_temp_json`
(
    `id`        int          NOT NULL,
    `user`      varchar(512) NOT NULL,
    `createdAt` datetime     NOT NULL,
    `updatedAt` datetime     NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `user_time_spent_by_ue`
--

CREATE TABLE `user_time_spent_by_ue`
(
    `id`           int NOT NULL,
    `userId`       int      DEFAULT NULL,
    `ueId`         int      DEFAULT NULL,
    `ueCategoryId` int      DEFAULT NULL,
    `coursId`      int      DEFAULT NULL,
    `time`         int      DEFAULT '0',
    `createdAt`    datetime DEFAULT NULL,
    `updatedAt`    datetime DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `views_histories`
--

CREATE TABLE `views_histories`
(
    `id`        int      NOT NULL,
    `createdAt` datetime NOT NULL,
    `updatedAt` datetime NOT NULL,
    `courId`    int DEFAULT NULL,
    `qcmIdQcm`  int DEFAULT NULL,
    `userId`    int DEFAULT NULL,
    `postId`    int DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

--
-- Index pour les tables déchargées
--


--
-- Déchargement des données de la table `users`
--
INSERT INTO `users` (`id`, `username`, `email`, `password`, `role`, `avatar`, `firstName`, `name`, `addressline1`,
                     `addressline2`, `postcode`, `phone`, `city`, `ip`, `bio`, `credits`, `warningReceived`, `banned`,
                     `resetPasswordToken`, `lastActivityAt`, `isActive`, `createdAt`, `updatedAt`, `externalId`,
                     `background`, `userCodeName`, `parentsEmail`, `parentsPhone`, `parentsProfession`,
                     `appearsInSubjects`, `appearsInTeam`)
VALUES (2, 'Alexandre', '<EMAIL>', '$2b$10$2o3vMnjt1VaO8Z5sZiouqe58PZYBG3EucE5sjMcSYoBqm.OKjrej6',
        'ADMIN', '0u6k7zwU7-Sans titre.png', 'Alexandre', 'Lunati', 'Rue du palais d\'été', NULL, '34070', NULL,
        'Montpellier', '::ffff:127.0.0.1', NULL, 4, 0, 0, NULL, '2022-06-08 11:35:22', 0, '2020-06-24 22:40:58',
        '2022-06-08 11:42:00', NULL, NULL, NULL, NULL, NULL, NULL, 0, 0),
       (38, 'sylv', '<EMAIL>', '$2b$10$5SwKcuCNQ8pCtJvxn.bE3eNp8jS4MGtiCodv3Fwt.fx7AJkKnSGSm', 'ADMIN',
        'T8qykU1jYl-88a1e8e06714965b7da1c4ea1073e6d658d22bc0r1-720-720v2_uhq.jpg', 'Sylv', 'Pt', 'adresse', NULL,
        '13000', NULL, 'Marseille', '::ffff:127.0.0.1', 'Test bio', 1, 0, 0, NULL, '2022-06-08 12:26:16', 1,
        '2020-06-24 22:40:58', '2022-06-08 12:26:16', NULL, NULL, NULL, NULL, NULL, NULL, 0, 0);

INSERT INTO mcq_scale (id, name, rules, createdAt, updatedAt, isDefault, type, questionType,
                       pointsObtainedWhenNothingChecked)
VALUES (1, 'Barême par défaut',
        '{"minimumGrade":null,"numberOfErrors":null,"pointsLostPerError":[-3,-2,-1,0,0],"pointsPerQuestion":1}',
        '2022-10-23 15:21:23', '2022-10-23 15:21:23', 1, 'dynamic', 'mcq', null);
INSERT INTO mcq_scale (id, name, rules, createdAt, updatedAt, isDefault, type, questionType,
                       pointsObtainedWhenNothingChecked)
VALUES (2, 'Barême par défaut',
        '{"minimumGrade":null,"numberOfErrors":null,"pointsLostPerError":[-3,-2,-1,0,0],"pointsPerQuestion":1}',
        '2022-10-23 15:21:31', '2022-10-23 15:21:31', 0, 'dynamic', 'ucq', null);

-- Individual groups

INSERT INTO groupes (id, name, createdAt, updatedAt, role, image, isIndividual, folderId, name_en, name_it, name_de, name_es) VALUES (3, '2', '2023-08-27 15:50:29', '2023-08-27 15:50:29', null, null, 1, null, null, null, null, null);
INSERT INTO groupes (id, name, createdAt, updatedAt, role, image, isIndividual, folderId, name_en, name_it, name_de, name_es) VALUES (4, '38', '2023-08-27 15:50:29', '2023-08-27 15:50:29', null, null, 1, null, null, null, null, null);

INSERT INTO user_groups (createdAt, updatedAt, userId, groupeId) VALUES ('2023-08-27 15:50:29', '2023-08-27 15:50:29', 2, 3);
INSERT INTO user_groups (createdAt, updatedAt, userId, groupeId) VALUES ('2023-08-27 15:50:29', '2023-08-27 15:50:29', 38, 4);



--
-- Index pour la table `annees`
--
ALTER TABLE `annees`
    ADD PRIMARY KEY (`id`),
    ADD KEY `annee` (`annee`);

--
-- Index pour la table `base_revision`
--
ALTER TABLE `base_revision`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `courId` (`courId`),
    ADD KEY `base_revision_dateDiffusionId_foreign_idx` (`dateDiffusionId`),
    ADD KEY `dateDebut` (`dateDebut`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `bills`
--
ALTER TABLE `bills`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `bills_paymentId_foreign_idx` (`paymentId`);

--
-- Index pour la table `buildings`
--
ALTER TABLE `buildings`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `challenges`
--
ALTER TABLE `challenges`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `challenges_type_qcm`
--
ALTER TABLE `challenges_type_qcm`
    ADD KEY `typeQcmId` (`typeQcmId`),
    ADD KEY `challengeId` (`challengeId`);

--
-- Index pour la table `challenge_badges`
--
ALTER TABLE `challenge_badges`
    ADD PRIMARY KEY (`id`),
    ADD KEY `challengeId` (`challengeId`);

--
-- Index pour la table `challenge_conditions`
--
ALTER TABLE `challenge_conditions`
    ADD PRIMARY KEY (`id`),
    ADD KEY `challengeId` (`challengeId`),
    ADD KEY `coursId` (`coursId`),
    ADD KEY `qcmId` (`qcmId`);

--
-- Index pour la table `challenge_groups_unlock`
--
ALTER TABLE `challenge_groups_unlock`
    ADD KEY `challengeId` (`challengeId`),
    ADD KEY `groupeId` (`groupeId`);

--
-- Index pour la table `challenge_user_progress`
--
ALTER TABLE `challenge_user_progress`
    ADD PRIMARY KEY (`id`),
    ADD KEY `challengeId` (`challengeId`),
    ADD KEY `challengeConditionId` (`challengeConditionId`),
    ADD KEY `userId` (`userId`),
    ADD KEY `qcmSessionId` (`qcmSessionId`),
    ADD KEY `ueId` (`ueId`),
    ADD KEY `ueCategoryId` (`ueCategoryId`),
    ADD KEY `coursId` (`coursId`);

--
-- Index pour la table `configs`
--
ALTER TABLE `configs`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `cours`
--
ALTER TABLE `cours`
    ADD PRIMARY KEY (`id`),
    ADD KEY `uecategoryId` (`uecategoryId`),
    ADD KEY `authorId` (`authorId`),
    ADD KEY `cours_is_visible_order` (`isVisible`, `order`),
    ADD KEY `id` (`id`),
    ADD KEY `cours_formationId_foreign_idx` (`formationId`),
    ADD KEY `cours_targetCoursId_foreign_idx` (`targetCoursId`),
    ADD KEY `cours_ueId_foreign_idx` (`ueId`);

--
-- Index pour la table `cours_groups`
--
ALTER TABLE `cours_groups`
    ADD PRIMARY KEY (`id`),
    ADD KEY `groupeId` (`groupeId`),
    ADD KEY `coursId` (`coursId`);

--
-- Index pour la table `cours_qcms`
--
ALTER TABLE `cours_qcms`
    ADD PRIMARY KEY (`courId`, `qcmIdQcm`),
    ADD KEY `qcmIdQcm` (`qcmIdQcm`),
    ADD KEY `courId` (`courId`);

--
-- Index pour la table `cours_supports`
--
ALTER TABLE `cours_supports`
    ADD PRIMARY KEY (`id`),
    ADD KEY `coursId` (`coursId`),
    ADD KEY `authorId` (`authorId`);

--
-- Index pour la table `cours_types_qcm_settings`
--
ALTER TABLE `cours_types_qcm_settings`
    ADD PRIMARY KEY (`id`),
    ADD KEY `coursId` (`coursId`),
    ADD KEY `typeQcmId` (`typeQcmId`);

--
-- Index pour la table `custom_forfaits_links`
--
ALTER TABLE `custom_forfaits_links`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `custom_forfaits_links_forfaits`
--
ALTER TABLE `custom_forfaits_links_forfaits`
    ADD PRIMARY KEY (`forfaitId`, `customForfaitLinkId`);

--
-- Index pour la table `date_diffusion`
--
ALTER TABLE `date_diffusion`
    ADD PRIMARY KEY (`id`),
    ADD KEY `courId` (`courId`),
    ADD KEY `date_diffusion_qcmId_foreign_idx` (`qcmId`),
    ADD KEY `date` (`date`),
    ADD KEY `id` (`id`),
    ADD KEY `date_diffusion_examSessionId_foreign_idx` (`examSessionId`),
    ADD KEY `date_diffusion_eventId_foreign_idx` (`eventId`),
    ADD KEY `date_diffusion_buildingId_foreign_idx` (`buildingId`),
    ADD KEY `date_diffusion_roomId_foreign_idx` (`roomId`);

--
-- Index pour la table `date_diffusion_groups`
--
ALTER TABLE `date_diffusion_groups`
    ADD PRIMARY KEY (`groupId`, `date_diffusion_id`),
    ADD KEY `groupId` (`groupId`),
    ADD KEY `date_diffusion_id` (`date_diffusion_id`);

--
-- Index pour la table `date_diffusion_organizers`
--
ALTER TABLE `date_diffusion_organizers`
    ADD KEY `userId` (`userId`),
    ADD KEY `date_diffusion_id` (`date_diffusion_id`);

--
-- Index pour la table `date_diffusion_participants`
--
ALTER TABLE `date_diffusion_participants`
    ADD KEY `userId` (`userId`),
    ADD KEY `date_diffusion_id` (`date_diffusion_id`);

--
-- Index pour la table `discussions`
--
ALTER TABLE `discussions`
    ADD PRIMARY KEY (`id`),
    ADD KEY `authorId` (`authorId`),
    ADD KEY `destinataireId` (`destinataireId`);

--
-- Index pour la table `discussions_users`
--
ALTER TABLE `discussions_users`
    ADD PRIMARY KEY (`userId`, `discussionId`),
    ADD KEY `discussionId` (`discussionId`),
    ADD KEY `userId` (`userId`);

--
-- Index pour la table `events`
--
ALTER TABLE `events`
    ADD PRIMARY KEY (`id`),
    ADD KEY `events_folderId_foreign_idx` (`folderId`);

--
-- Index pour la table `event_cours`
--
ALTER TABLE `event_cours`
    ADD KEY `eventId` (`eventId`),
    ADD KEY `coursId` (`coursId`);

--
-- Index pour la table `event_type_qcm`
--
ALTER TABLE `event_type_qcm`
    ADD KEY `typeQcmId` (`typeQcmId`),
    ADD KEY `eventId` (`eventId`);

--
-- Index pour la table `exams`
--
ALTER TABLE `exams`
    ADD PRIMARY KEY (`id`),
    ADD KEY `authorId` (`authorId`),
    ADD KEY `exams_folderId_foreign_idx` (`folderId`);

--
-- Index pour la table `exam_question_series`
--
ALTER TABLE `exam_question_series`
    ADD PRIMARY KEY (`id`),
    ADD KEY `mcqId` (`mcqId`),
    ADD KEY `examId` (`examId`),
    ADD KEY `authorId` (`authorId`),
    ADD KEY `exam_question_series_examSessionId_foreign_idx` (`examSessionId`);

--
-- Index pour la table `exam_results`
--
ALTER TABLE `exam_results`
    ADD PRIMARY KEY (`id`),
    ADD KEY `examSessionId` (`examSessionId`),
    ADD KEY `userId` (`userId`);

--
-- Index pour la table `exam_scales`
--
ALTER TABLE `exam_scales`
    ADD PRIMARY KEY (`id`),
    ADD KEY `examId` (`examId`),
    ADD KEY `authorId` (`authorId`);

--
-- Index pour la table `exam_scale_question_series`
--
ALTER TABLE `exam_scale_question_series`
    ADD PRIMARY KEY (`id`),
    ADD KEY `examScaleId` (`examScaleId`),
    ADD KEY `examQuestionSerieId` (`examQuestionSerieId`);

--
-- Index pour la table `exam_sessions`
--
ALTER TABLE `exam_sessions`
    ADD PRIMARY KEY (`id`),
    ADD KEY `examId` (`examId`),
    ADD KEY `authorId` (`authorId`);

--
-- Index pour la table `exam_sessions_groups`
--
ALTER TABLE `exam_sessions_groups`
    ADD PRIMARY KEY (`id`),
    ADD KEY `groupeId` (`groupeId`),
    ADD KEY `examSessionId` (`examSessionId`);

--
-- Index pour la table `exam_type_qcm`
--
ALTER TABLE `exam_type_qcm`
    ADD KEY `typeQcmId` (`typeQcmId`),
    ADD KEY `examId` (`examId`);

--
-- Index pour la table `fiches`
--
ALTER TABLE `fiches`
    ADD PRIMARY KEY (`id`),
    ADD KEY `courId` (`courId`),
    ADD KEY `fiches_is_accessible` (`isAccessible`);

--
-- Index pour la table `fiches_groups`
--
ALTER TABLE `fiches_groups`
    ADD PRIMARY KEY (`id`),
    ADD KEY `groupeId` (`groupeId`),
    ADD KEY `ficheId` (`ficheId`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `files`
--
ALTER TABLE `files`
    ADD PRIMARY KEY (`id`),
    ADD KEY `courId` (`courId`),
    ADD KEY `messageId` (`messageId`),
    ADD KEY `fileId` (`fileId`),
    ADD KEY `fileImageId` (`fileImageId`),
    ADD KEY `files_messageFileImageId_foreign_idx` (`messageFileImageId`),
    ADD KEY `files_notionId_foreign_idx` (`notionId`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `folders`
--
ALTER TABLE `folders`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `forfaits`
--
ALTER TABLE `forfaits`
    ADD PRIMARY KEY (`id`),
    ADD KEY `forfaits_requiredForfaitId_foreign_idx` (`requiredForfaitId`),
    ADD KEY `forfaits_parentId_foreign_idx` (`parentId`),
    ADD KEY `isPublished` (`isPublished`),
    ADD KEY `order` (`order`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `forfait_groups`
--
ALTER TABLE `forfait_groups`
    ADD PRIMARY KEY (`forfaitId`, `groupeId`),
    ADD KEY `groupeId` (`groupeId`);

--
-- Index pour la table `formation`
--
ALTER TABLE `formation`
    ADD PRIMARY KEY (`id`),
    ADD KEY `authorId` (`authorId`);

--
-- Index pour la table `formations_groups`
--
ALTER TABLE `formations_groups`
    ADD PRIMARY KEY (`id`),
    ADD KEY `groupeId` (`groupeId`),
    ADD KEY `formationId` (`formationId`);

--
-- Index pour la table `formation_block`
--
ALTER TABLE `formation_block`
    ADD PRIMARY KEY (`id`),
    ADD KEY `formationStepId` (`formationStepId`),
    ADD KEY `authorId` (`authorId`),
    ADD KEY `formation_block_coursId_foreign_idx` (`coursId`),
    ADD KEY `formation_block_titleId_foreign_idx` (`titleId`);

--
-- Index pour la table `formation_element`
--
ALTER TABLE `formation_element`
    ADD PRIMARY KEY (`id`),
    ADD KEY `formationStepId` (`formationStepId`),
    ADD KEY `coursId` (`coursId`),
    ADD KEY `mcqId` (`mcqId`),
    ADD KEY `authorId` (`authorId`),
    ADD KEY `formation_element_blockId_foreign_idx` (`blockId`),
    ADD KEY `formation_element_coursSupportId_foreign_idx` (`coursSupportId`),
    ADD KEY `formation_element_eventId_foreign_idx` (`eventId`),
    ADD KEY `formation_element_titleId_foreign_idx` (`titleId`),
    ADD KEY `formation_element_questionId_foreign_idx` (`questionId`),
    ADD KEY `formation_element_headerMcqId_foreign_idx` (`headerMcqId`),
    ADD KEY `formation_element_footerQuestionId_foreign_idx` (`footerQuestionId`),
    ADD KEY `formation_element_challengeId_foreign_idx` (`challengeId`),
    ADD KEY `formation_element_forfaitId_foreign_idx` (`forfaitId`),
    ADD KEY `formation_element_userPropertyFolderId_foreign_idx` (`userPropertyFolderId`);

--
-- Index pour la table `formation_elements_forfaits`
--
ALTER TABLE `formation_elements_forfaits`
    ADD KEY `elementId` (`elementId`),
    ADD KEY `forfaitId` (`forfaitId`);

--
-- Index pour la table `formation_elements_groups`
--
ALTER TABLE `formation_elements_groups`
    ADD KEY `groupeId` (`groupeId`),
    ADD KEY `formationElementId` (`formationElementId`);

--
-- Index pour la table `formation_elements_groups_unlock`
--
ALTER TABLE `formation_elements_groups_unlock`
    ADD KEY `formationElementId` (`formationElementId`),
    ADD KEY `groupeId` (`groupeId`);

--
-- Index pour la table `formation_module_element_progress`
--
ALTER TABLE `formation_module_element_progress`
    ADD PRIMARY KEY (`id`),
    ADD KEY `formationStepId` (`formationStepId`),
    ADD KEY `userId` (`userId`),
    ADD KEY `formation_module_element_progress_formationId_foreign_idx` (`formationId`);

--
-- Index pour la table `formation_section`
--
ALTER TABLE `formation_section`
    ADD PRIMARY KEY (`id`),
    ADD KEY `parentSectionId` (`parentSectionId`),
    ADD KEY `formationId` (`formationId`),
    ADD KEY `authorId` (`authorId`);

--
-- Index pour la table `formation_session`
--
ALTER TABLE `formation_session`
    ADD PRIMARY KEY (`id`),
    ADD KEY `currentStepId` (`currentStepId`),
    ADD KEY `formationId` (`formationId`),
    ADD KEY `userId` (`userId`);

--
-- Index pour la table `formation_step`
--
ALTER TABLE `formation_step`
    ADD PRIMARY KEY (`id`),
    ADD KEY `sectionId` (`sectionId`),
    ADD KEY `authorId` (`authorId`);

--
-- Index pour la table `forums`
--
ALTER TABLE `forums`
    ADD PRIMARY KEY (`id`),
    ADD KEY `forums_parentId_foreign_idx` (`parentId`);

--
-- Index pour la table `forum_categories`
--
ALTER TABLE `forum_categories`
    ADD PRIMARY KEY (`id`),
    ADD KEY `forum_categories_forumId_foreign_idx` (`forumId`);

--
-- Index pour la table `forum_category_groups`
--
ALTER TABLE `forum_category_groups`
    ADD PRIMARY KEY (`groupId`, `forumCategoryId`);

--
-- Index pour la table `global_announce`
--
ALTER TABLE `global_announce`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `global_announce_groups`
--
ALTER TABLE `global_announce_groups`
    ADD PRIMARY KEY (`id`),
    ADD KEY `groupeId` (`groupeId`),
    ADD KEY `globalAnnounceId` (`globalAnnounceId`);

--
-- Index pour la table `good_answers_stats_user_synthesis`
--
ALTER TABLE `good_answers_stats_user_synthesis`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `ueId` (`ueId`),
    ADD KEY `ueCategoryId` (`ueCategoryId`),
    ADD KEY `coursId` (`coursId`),
    ADD KEY `notionId` (`notionId`);

--
-- Index pour la table `groupes`
--
ALTER TABLE `groupes`
    ADD PRIMARY KEY (`id`),
    ADD KEY `groupes_role` (`role`),
    ADD KEY `name` (`name`),
    ADD KEY `id` (`id`),
    ADD KEY `groupes_folderId_foreign_idx` (`folderId`);

--
-- Index pour la table `groups_responsibility`
--
ALTER TABLE `groups_responsibility`
    ADD PRIMARY KEY (`id`),
    ADD KEY `groupeId` (`groupeId`),
    ADD KEY `responsibleOfGroupeId` (`responsibleOfGroupeId`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `like_histories`
--
ALTER TABLE `like_histories`
    ADD PRIMARY KEY (`id`),
    ADD KEY `postId` (`postId`),
    ADD KEY `userId` (`userId`),
    ADD KEY `type` (`type`);

--
-- Index pour la table `logs`
--
ALTER TABLE `logs`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `reporteeUserId` (`reporteeUserId`),
    ADD KEY `qcmId` (`qcmId`),
    ADD KEY `questionId` (`questionId`),
    ADD KEY `answerId` (`answerId`),
    ADD KEY `forfaitId` (`forfaitId`),
    ADD KEY `postId` (`postId`),
    ADD KEY `logs_coursId_foreign_idx` (`coursId`),
    ADD KEY `createdAt` (`createdAt`),
    ADD KEY `logs_created_at` (`createdAt`),
    ADD KEY `logs_log_operation` (`logOperation`);

--
-- Index pour la table `mcq_scale`
--
ALTER TABLE `mcq_scale`
    ADD PRIMARY KEY (`id`),
    ADD KEY `isDefault` (`isDefault`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `mcq_scale_ues`
--
ALTER TABLE `mcq_scale_ues`
    ADD PRIMARY KEY (`id`),
    ADD KEY `ueId` (`ueId`),
    ADD KEY `mcqScaleId` (`mcqScaleId`);

--
-- Index pour la table `messages`
--
ALTER TABLE `messages`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `discussionId` (`discussionId`);

--
-- Index pour la table `modules_quick_access`
--
ALTER TABLE `modules_quick_access`
    ADD PRIMARY KEY (`id`),
    ADD KEY `challengeId` (`challengeId`),
    ADD KEY `ueId` (`ueId`),
    ADD KEY `ueCategoryId` (`ueCategoryId`),
    ADD KEY `coursId` (`coursId`);

--
-- Index pour la table `modules_quick_access_type_qcm`
--
ALTER TABLE `modules_quick_access_type_qcm`
    ADD KEY `typeQcmId` (`typeQcmId`),
    ADD KEY `moduleQuickAccessId` (`moduleQuickAccessId`);

--
-- Index pour la table `notifications`
--
ALTER TABLE `notifications`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `fromUserId` (`fromUserId`),
    ADD KEY `notifications_object_id_parent_id_seen` (`objectId`, `parentId`, `seen`),
    ADD KEY `updatedAt` (`updatedAt`);

--
-- Index pour la table `notions`
--
ALTER TABLE `notions`
    ADD PRIMARY KEY (`id`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `notions_cours`
--
ALTER TABLE `notions_cours`
    ADD PRIMARY KEY (`notionId`, `coursId`),
    ADD KEY `autoAdded` (`autoAdded`),
    ADD KEY `notionId` (`notionId`),
    ADD KEY `coursId` (`coursId`);

--
-- Index pour la table `notions_keywords`
--
ALTER TABLE `notions_keywords`
    ADD PRIMARY KEY (`id`),
    ADD UNIQUE KEY `name` (`name`),
    ADD KEY `notionId` (`notionId`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `notions_parents`
--
ALTER TABLE `notions_parents`
    ADD KEY `notionId` (`notionId`),
    ADD KEY `parentNotionId` (`parentNotionId`);

--
-- Index pour la table `notions_posts`
--
ALTER TABLE `notions_posts`
    ADD KEY `notionId` (`notionId`),
    ADD KEY `postId` (`postId`);

--
-- Index pour la table `notions_questions`
--
ALTER TABLE `notions_questions`
    ADD PRIMARY KEY (`notionId`, `questionId`),
    ADD KEY `notionId` (`notionId`),
    ADD KEY `questionId` (`questionId`);

--
-- Index pour la table `notions_question_answers`
--
ALTER TABLE `notions_question_answers`
    ADD PRIMARY KEY (`notionId`, `answerId`),
    ADD KEY `notionId` (`notionId`),
    ADD KEY `answerId` (`answerId`);

--
-- Index pour la table `payments`
--
ALTER TABLE `payments`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `logId` (`logId`),
    ADD KEY `payments_paymentMethodId_foreign_idx` (`paymentMethodId`);

--
-- Index pour la table `payments_forfaits`
--
ALTER TABLE `payments_forfaits`
    ADD PRIMARY KEY (`id`),
    ADD KEY `paymentId` (`paymentId`),
    ADD KEY `forfaitId` (`forfaitId`);

--
-- Index pour la table `posts`
--
ALTER TABLE `posts`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `courId` (`courId`),
    ADD KEY `qcmIdQcm` (`qcmIdQcm`),
    ADD KEY `parentId` (`parentId`),
    ADD KEY `posts_forumId_foreign_idx` (`forumId`),
    ADD KEY `posts_answerId_foreign_idx` (`answerId`),
    ADD KEY `posts_postTypeId_foreign_idx` (`postTypeId`),
    ADD KEY `isResolved` (`isResolved`),
    ADD KEY `createdAt` (`createdAt`),
    ADD KEY `posts_userIdForAiFeedback_foreign_idx` (`userIdForAiFeedback`),
    ADD KEY `posts_eventId_foreign_idx` (`eventId`);

--
-- Index pour la table `post_type`
--
ALTER TABLE `post_type`
    ADD PRIMARY KEY (`id`),
    ADD KEY `type` (`type`);

--
-- Index pour la table `qcm`
--
ALTER TABLE `qcm`
    ADD PRIMARY KEY (`id_qcm`),
    ADD UNIQUE KEY `id_lien` (`id_lien`),
    ADD UNIQUE KEY `id_lien_2` (`id_lien`),
    ADD KEY `authorId` (`authorId`),
    ADD KEY `UEId` (`UEId`),
    ADD KEY `qcm_ibfk_5` (`sousCategorieIdSousCategorie`),
    ADD KEY `qcm_annale_is_published_annee` (`annale`, `isPublished`, `annee`),
    ADD KEY `deleted` (`deleted`),
    ADD KEY `id_qcm` (`id_qcm`),
    ADD KEY `id_createur` (`id_createur`);

--
-- Index pour la table `qcm_default_type_qcm_for_questions`
--
ALTER TABLE `qcm_default_type_qcm_for_questions`
    ADD KEY `qcmId` (`qcmId`),
    ADD KEY `typeQcmId` (`typeQcmId`);

--
-- Index pour la table `qcm_saved_state`
--
ALTER TABLE `qcm_saved_state`
    ADD PRIMARY KEY (`id`),
    ADD KEY `qcmId` (`qcmId`),
    ADD KEY `sessionId` (`sessionId`),
    ADD KEY `userId` (`userId`);

--
-- Index pour la table `qcm_sessions`
--
ALTER TABLE `qcm_sessions`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `qcmId` (`qcmId`),
    ADD KEY `qcm_sessions_currentCoursId_foreign_idx` (`currentCoursId`),
    ADD KEY `qcm_sessions_examSessionId_foreign_idx` (`examSessionId`),
    ADD KEY `qcm_sessions_examQuestionSerieId_foreign_idx` (`examQuestionSerieId`),
    ADD KEY `qcm_sessions_formationElementId_foreign_idx` (`formationElementId`),
    ADD KEY `qcm_sessions_challengeId_foreign_idx` (`challengeId`);

--
-- Index pour la table `qcm_type_qcm`
--
ALTER TABLE `qcm_type_qcm`
    ADD PRIMARY KEY (`typeQcmId`, `qcmId`);

--
-- Index pour la table `questions`
--
ALTER TABLE `questions`
    ADD PRIMARY KEY (`id_question`),
    ADD KEY `id_qcm` (`id_qcm`),
    ADD KEY `questions_ibfk_4` (`id_sous_categorie`),
    ADD KEY `questions_link_cours_id` (`linkCoursId`),
    ADD KEY `order` (`order`),
    ADD KEY `mcqScaleId` (`mcqScaleId`),
    ADD KEY `id_question` (`id_question`),
    ADD KEY `questions_authorId_foreign_idx` (`authorId`);

--
-- Index pour la table `questions_double_parents`
--
ALTER TABLE `questions_double_parents`
    ADD PRIMARY KEY (`id`),
    ADD KEY `questionId` (`questionId`),
    ADD KEY `parentQuestionId` (`parentQuestionId`),
    ADD KEY `questions_double_parents_parentQuestionId2_foreign_idx` (`parentQuestionId2`);

--
-- Index pour la table `questions_parents`
--
ALTER TABLE `questions_parents`
    ADD KEY `questionId` (`questionId`),
    ADD KEY `parentQuestionId` (`parentQuestionId`);

--
-- Index pour la table `questions_qcm`
--
ALTER TABLE `questions_qcm`
    ADD KEY `qcmId` (`qcmId`),
    ADD KEY `questionId` (`questionId`);

--
-- Index pour la table `questions_type_qcm`
--
ALTER TABLE `questions_type_qcm`
    ADD KEY `typeQcmId` (`typeQcmId`),
    ADD KEY `questionId` (`questionId`);

--
-- Index pour la table `question_answers`
--
ALTER TABLE `question_answers`
    ADD PRIMARY KEY (`id`),
    ADD KEY `questionId` (`questionId`),
    ADD KEY `question_answers_created_at` (`createdAt`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `question_cours`
--
ALTER TABLE `question_cours`
    ADD KEY `questionId` (`questionId`),
    ADD KEY `coursId` (`coursId`);

--
-- Index pour la table `question_type_question`
--
ALTER TABLE `question_type_question`
    ADD PRIMARY KEY (`typeQuestionId`, `questionId`);

--
-- Index pour la table `ranks`
--
ALTER TABLE `ranks`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `rooms`
--
ALTER TABLE `rooms`
    ADD PRIMARY KEY (`id`),
    ADD KEY `buildingId` (`buildingId`);

--
-- Index pour la table `SequelizeMeta`
--
ALTER TABLE `SequelizeMeta`
    ADD PRIMARY KEY (`name`),
    ADD UNIQUE KEY `name` (`name`);

--
-- Index pour la table `sous_categories`
--
ALTER TABLE `sous_categories`
    ADD PRIMARY KEY (`id_sous_categorie`),
    ADD KEY `ueIdId` (`ueIdId`);

--
-- Index pour la table `statistiques`
--
ALTER TABLE `statistiques`
    ADD PRIMARY KEY (`id`),
    ADD KEY `id_qcm` (`id_qcm`, `id_utilisateur`),
    ADD KEY `statistiques_is_first_time` (`isFirstTime`),
    ADD KEY `date` (`date`),
    ADD KEY `id` (`id`),
    ADD KEY `statistiques_qcmSessionId_foreign_idx` (`qcmSessionId`);

--
-- Index pour la table `statistiques_questions`
--
ALTER TABLE `statistiques_questions`
    ADD PRIMARY KEY (`id_statistique_question`),
    ADD KEY `id_question` (`id_question`, `id_utilisateur`),
    ADD KEY `statistiques_questions_qcmSessionId_foreign_idx` (`qcmSessionId`),
    ADD KEY `id_statistique_question` (`id_statistique_question`),
    ADD KEY `createdAt` (`createdAt`),
    ADD KEY `statistiques_questions_statId_foreign_idx` (`statId`);

--
-- Index pour la table `stats_notions_users`
--
ALTER TABLE `stats_notions_users`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `notionId` (`notionId`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `stats_questions_answers`
--
ALTER TABLE `stats_questions_answers`
    ADD PRIMARY KEY (`id`),
    ADD KEY `statsQuestionId` (`statsQuestionId`),
    ADD KEY `answerId` (`answerId`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `titles`
--
ALTER TABLE `titles`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `Tokens`
--
ALTER TABLE `Tokens`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `type_qcm`
--
ALTER TABLE `type_qcm`
    ADD PRIMARY KEY (`id`),
    ADD KEY `id` (`id`);

--
-- Index pour la table `type_qcm_groups`
--
ALTER TABLE `type_qcm_groups`
    ADD PRIMARY KEY (`id`),
    ADD KEY `groupeId` (`groupeId`),
    ADD KEY `typeQcmId` (`typeQcmId`);

--
-- Index pour la table `type_question`
--
ALTER TABLE `type_question`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `uecategories`
--
ALTER TABLE `uecategories`
    ADD PRIMARY KEY (`id`),
    ADD KEY `ueId` (`ueId`),
    ADD KEY `uecategories_order_is_visible` (`order`, `isVisible`),
    ADD KEY `id` (`id`),
    ADD KEY `uecategories_parentId_foreign_idx` (`parentId`);

--
-- Index pour la table `uecategories_groups`
--
ALTER TABLE `uecategories_groups`
    ADD PRIMARY KEY (`id`),
    ADD KEY `groupeId` (`groupeId`),
    ADD KEY `uecategory_id` (`uecategory_id`);

--
-- Index pour la table `ues`
--
ALTER TABLE `ues`
    ADD PRIMARY KEY (`id`),
    ADD KEY `ues_is_visible` (`isVisible`),
    ADD KEY `order` (`order`),
    ADD KEY `id` (`id`),
    ADD KEY `ues_parentId_foreign_idx` (`parentId`);

--
-- Index pour la table `ue_groups`
--
ALTER TABLE `ue_groups`
    ADD PRIMARY KEY (`groupeId`, `ueId`),
    ADD KEY `ueId` (`ueId`),
    ADD KEY `groupeId` (`groupeId`);

--
-- Index pour la table `ue_tuteurs`
--
ALTER TABLE `ue_tuteurs`
    ADD PRIMARY KEY (`ueId`, `userId`),
    ADD KEY `userId` (`userId`),
    ADD KEY `ueId` (`ueId`);

--
-- Index pour la table `userPreferences`
--
ALTER TABLE `userPreferences`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `coursUpdateDeviceNotification` (`coursUpdateDeviceNotification`),
    ADD KEY `forumUpdateDeviceNotification` (`forumUpdateDeviceNotification`),
    ADD KEY `qcmUpdateDeviceNotification` (`qcmUpdateDeviceNotification`);

--
-- Index pour la table `users`
--
ALTER TABLE `users`
    ADD PRIMARY KEY (`id`),
    ADD UNIQUE KEY `username` (`username`),
    ADD UNIQUE KEY `email` (`email`),
    ADD UNIQUE KEY `username_2` (`username`),
    ADD UNIQUE KEY `email_2` (`email`),
    ADD KEY `isActive` (`isActive`),
    ADD KEY `role` (`role`),
    ADD KEY `id` (`id`),
    ADD KEY `users_bot` (`bot`);

--
-- Index pour la table `userStats`
--
ALTER TABLE `userStats`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`);

--
-- Index pour la table `users_blockeds`
--
ALTER TABLE `users_blockeds`
    ADD KEY `userId` (`userId`),
    ADD KEY `userBlockedId` (`userBlockedId`);

--
-- Index pour la table `users_ues_notifications`
--
ALTER TABLE `users_ues_notifications`
    ADD PRIMARY KEY (`userId`, `ueId`);

--
-- Index pour la table `user_devices`
--
ALTER TABLE `user_devices`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `uuid` (`uuid`);

--
-- Index pour la table `user_groups`
--
ALTER TABLE `user_groups`
    ADD PRIMARY KEY (`userId`, `groupeId`),
    ADD KEY `groupeId` (`groupeId`),
    ADD KEY `userId` (`userId`);

--
-- Index pour la table `user_properties_data`
--
ALTER TABLE `user_properties_data`
    ADD PRIMARY KEY (`id`),
    ADD KEY `elementId` (`elementId`),
    ADD KEY `userId` (`userId`),
    ADD KEY `user_properties_data_logObjectId_foreign_idx` (`logObjectId`);

--
-- Index pour la table `user_properties_folders`
--
ALTER TABLE `user_properties_folders`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `user_sessions`
--
ALTER TABLE `user_sessions`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `updatedAt` (`updatedAt`),
    ADD KEY `visible` (`visible`);

--
-- Index pour la table `user_stats_by_day`
--
ALTER TABLE `user_stats_by_day`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`);

--
-- Index pour la table `user_temp_json`
--
ALTER TABLE `user_temp_json`
    ADD PRIMARY KEY (`id`);

--
-- Index pour la table `user_time_spent_by_ue`
--
ALTER TABLE `user_time_spent_by_ue`
    ADD PRIMARY KEY (`id`),
    ADD KEY `userId` (`userId`),
    ADD KEY `ueId` (`ueId`),
    ADD KEY `ueCategoryId` (`ueCategoryId`),
    ADD KEY `coursId` (`coursId`);

--
-- Index pour la table `views_histories`
--
ALTER TABLE `views_histories`
    ADD PRIMARY KEY (`id`),
    ADD KEY `courId` (`courId`),
    ADD KEY `qcmIdQcm` (`qcmIdQcm`),
    ADD KEY `userId` (`userId`),
    ADD KEY `views_histories_postId_foreign_idx` (`postId`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `annees`
--
ALTER TABLE `annees`
    MODIFY `id` int NOT NULL AUTO_INCREMENT,
    AUTO_INCREMENT = 16;

--
-- AUTO_INCREMENT pour la table `base_revision`
--
ALTER TABLE `base_revision`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `bills`
--
ALTER TABLE `bills`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `buildings`
--
ALTER TABLE `buildings`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `challenges`
--
ALTER TABLE `challenges`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `challenge_badges`
--
ALTER TABLE `challenge_badges`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `challenge_conditions`
--
ALTER TABLE `challenge_conditions`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `challenge_user_progress`
--
ALTER TABLE `challenge_user_progress`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `configs`
--
ALTER TABLE `configs`
    MODIFY `id` int NOT NULL AUTO_INCREMENT,
    AUTO_INCREMENT = 39;

--
-- AUTO_INCREMENT pour la table `cours`
--
ALTER TABLE `cours`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `cours_groups`
--
ALTER TABLE `cours_groups`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `cours_supports`
--
ALTER TABLE `cours_supports`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `cours_types_qcm_settings`
--
ALTER TABLE `cours_types_qcm_settings`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `custom_forfaits_links`
--
ALTER TABLE `custom_forfaits_links`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `date_diffusion`
--
ALTER TABLE `date_diffusion`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `discussions`
--
ALTER TABLE `discussions`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `events`
--
ALTER TABLE `events`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `exams`
--
ALTER TABLE `exams`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `exam_question_series`
--
ALTER TABLE `exam_question_series`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `exam_results`
--
ALTER TABLE `exam_results`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `exam_scales`
--
ALTER TABLE `exam_scales`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `exam_scale_question_series`
--
ALTER TABLE `exam_scale_question_series`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `exam_sessions`
--
ALTER TABLE `exam_sessions`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `exam_sessions_groups`
--
ALTER TABLE `exam_sessions_groups`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `fiches`
--
ALTER TABLE `fiches`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `fiches_groups`
--
ALTER TABLE `fiches_groups`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `files`
--
ALTER TABLE `files`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `folders`
--
ALTER TABLE `folders`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `forfaits`
--
ALTER TABLE `forfaits`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `formation`
--
ALTER TABLE `formation`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `formations_groups`
--
ALTER TABLE `formations_groups`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `formation_block`
--
ALTER TABLE `formation_block`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `formation_element`
--
ALTER TABLE `formation_element`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `formation_module_element_progress`
--
ALTER TABLE `formation_module_element_progress`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `formation_section`
--
ALTER TABLE `formation_section`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `formation_session`
--
ALTER TABLE `formation_session`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `formation_step`
--
ALTER TABLE `formation_step`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `forums`
--
ALTER TABLE `forums`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `forum_categories`
--
ALTER TABLE `forum_categories`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `global_announce`
--
ALTER TABLE `global_announce`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `global_announce_groups`
--
ALTER TABLE `global_announce_groups`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `good_answers_stats_user_synthesis`
--
ALTER TABLE `good_answers_stats_user_synthesis`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `groupes`
--
ALTER TABLE `groupes`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `groups_responsibility`
--
ALTER TABLE `groups_responsibility`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `like_histories`
--
ALTER TABLE `like_histories`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `logs`
--
ALTER TABLE `logs`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `mcq_scale`
--
ALTER TABLE `mcq_scale`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `mcq_scale_ues`
--
ALTER TABLE `mcq_scale_ues`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `messages`
--
ALTER TABLE `messages`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `modules_quick_access`
--
ALTER TABLE `modules_quick_access`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `notifications`
--
ALTER TABLE `notifications`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `notions`
--
ALTER TABLE `notions`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `notions_keywords`
--
ALTER TABLE `notions_keywords`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `payments`
--
ALTER TABLE `payments`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `payments_forfaits`
--
ALTER TABLE `payments_forfaits`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `posts`
--
ALTER TABLE `posts`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `post_type`
--
ALTER TABLE `post_type`
    MODIFY `id` int NOT NULL AUTO_INCREMENT,
    AUTO_INCREMENT = 15;

--
-- AUTO_INCREMENT pour la table `qcm`
--
ALTER TABLE `qcm`
    MODIFY `id_qcm` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `qcm_saved_state`
--
ALTER TABLE `qcm_saved_state`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `qcm_sessions`
--
ALTER TABLE `qcm_sessions`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `questions`
--
ALTER TABLE `questions`
    MODIFY `id_question` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `questions_double_parents`
--
ALTER TABLE `questions_double_parents`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `question_answers`
--
ALTER TABLE `question_answers`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `ranks`
--
ALTER TABLE `ranks`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `rooms`
--
ALTER TABLE `rooms`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `sous_categories`
--
ALTER TABLE `sous_categories`
    MODIFY `id_sous_categorie` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `statistiques`
--
ALTER TABLE `statistiques`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `statistiques_questions`
--
ALTER TABLE `statistiques_questions`
    MODIFY `id_statistique_question` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `stats_notions_users`
--
ALTER TABLE `stats_notions_users`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `stats_questions_answers`
--
ALTER TABLE `stats_questions_answers`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `titles`
--
ALTER TABLE `titles`
    MODIFY `id` int NOT NULL AUTO_INCREMENT,
    AUTO_INCREMENT = 3;

--
-- AUTO_INCREMENT pour la table `Tokens`
--
ALTER TABLE `Tokens`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `type_qcm`
--
ALTER TABLE `type_qcm`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `type_qcm_groups`
--
ALTER TABLE `type_qcm_groups`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `type_question`
--
ALTER TABLE `type_question`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `uecategories`
--
ALTER TABLE `uecategories`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `uecategories_groups`
--
ALTER TABLE `uecategories_groups`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `ues`
--
ALTER TABLE `ues`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `userPreferences`
--
ALTER TABLE `userPreferences`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `users`
--
ALTER TABLE `users`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `userStats`
--
ALTER TABLE `userStats`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `user_devices`
--
ALTER TABLE `user_devices`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `user_properties_data`
--
ALTER TABLE `user_properties_data`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `user_properties_folders`
--
ALTER TABLE `user_properties_folders`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `user_sessions`
--
ALTER TABLE `user_sessions`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `user_stats_by_day`
--
ALTER TABLE `user_stats_by_day`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `user_temp_json`
--
ALTER TABLE `user_temp_json`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `user_time_spent_by_ue`
--
ALTER TABLE `user_time_spent_by_ue`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `views_histories`
--
ALTER TABLE `views_histories`
    MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `base_revision`
--
ALTER TABLE `base_revision`
    ADD CONSTRAINT `base_revision_dateDiffusionId_foreign_idx` FOREIGN KEY (`dateDiffusionId`) REFERENCES `date_diffusion` (`id`),
    ADD CONSTRAINT `base_revision_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`),
    ADD CONSTRAINT `base_revision_ibfk_2` FOREIGN KEY (`courId`) REFERENCES `cours` (`id`);

--
-- Contraintes pour la table `bills`
--
ALTER TABLE `bills`
    ADD CONSTRAINT `bills_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `bills_paymentId_foreign_idx` FOREIGN KEY (`paymentId`) REFERENCES `payments` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `challenges_type_qcm`
--
ALTER TABLE `challenges_type_qcm`
    ADD CONSTRAINT `challenges_type_qcm_ibfk_1` FOREIGN KEY (`typeQcmId`) REFERENCES `type_qcm` (`id`),
    ADD CONSTRAINT `challenges_type_qcm_ibfk_2` FOREIGN KEY (`challengeId`) REFERENCES `challenges` (`id`);

--
-- Contraintes pour la table `challenge_badges`
--
ALTER TABLE `challenge_badges`
    ADD CONSTRAINT `challenge_badges_ibfk_1` FOREIGN KEY (`challengeId`) REFERENCES `challenges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `challenge_conditions`
--
ALTER TABLE `challenge_conditions`
    ADD CONSTRAINT `challenge_conditions_ibfk_1` FOREIGN KEY (`challengeId`) REFERENCES `challenges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `challenge_conditions_ibfk_2` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `challenge_conditions_ibfk_3` FOREIGN KEY (`qcmId`) REFERENCES `qcm` (`id_qcm`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `challenge_groups_unlock`
--
ALTER TABLE `challenge_groups_unlock`
    ADD CONSTRAINT `challenge_groups_unlock_ibfk_1` FOREIGN KEY (`challengeId`) REFERENCES `challenges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `challenge_groups_unlock_ibfk_2` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `challenge_user_progress`
--
ALTER TABLE `challenge_user_progress`
    ADD CONSTRAINT `challenge_user_progress_ibfk_1` FOREIGN KEY (`challengeId`) REFERENCES `challenges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `challenge_user_progress_ibfk_2` FOREIGN KEY (`challengeConditionId`) REFERENCES `challenge_conditions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `challenge_user_progress_ibfk_3` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `challenge_user_progress_ibfk_4` FOREIGN KEY (`qcmSessionId`) REFERENCES `qcm_sessions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `challenge_user_progress_ibfk_5` FOREIGN KEY (`ueId`) REFERENCES `ues` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `challenge_user_progress_ibfk_6` FOREIGN KEY (`ueCategoryId`) REFERENCES `uecategories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `challenge_user_progress_ibfk_7` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `cours`
--
ALTER TABLE `cours`
    ADD CONSTRAINT `cours_formationId_foreign_idx` FOREIGN KEY (`formationId`) REFERENCES `formation` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `cours_ibfk_3` FOREIGN KEY (`uecategoryId`) REFERENCES `uecategories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `cours_ibfk_4` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `cours_targetCoursId_foreign_idx` FOREIGN KEY (`targetCoursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `cours_ueId_foreign_idx` FOREIGN KEY (`ueId`) REFERENCES `ues` (`id`);

--
-- Contraintes pour la table `cours_groups`
--
ALTER TABLE `cours_groups`
    ADD CONSTRAINT `cours_groups_ibfk_1` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `cours_groups_ibfk_2` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `cours_qcms`
--
ALTER TABLE `cours_qcms`
    ADD CONSTRAINT `cours_qcms_ibfk_1` FOREIGN KEY (`courId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `cours_qcms_ibfk_2` FOREIGN KEY (`qcmIdQcm`) REFERENCES `qcm` (`id_qcm`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `cours_supports`
--
ALTER TABLE `cours_supports`
    ADD CONSTRAINT `cours_supports_ibfk_1` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `cours_supports_ibfk_2` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `cours_types_qcm_settings`
--
ALTER TABLE `cours_types_qcm_settings`
    ADD CONSTRAINT `cours_types_qcm_settings_ibfk_1` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `cours_types_qcm_settings_ibfk_2` FOREIGN KEY (`typeQcmId`) REFERENCES `type_qcm` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `date_diffusion`
--
ALTER TABLE `date_diffusion`
    ADD CONSTRAINT `date_diffusion_buildingId_foreign_idx` FOREIGN KEY (`buildingId`) REFERENCES `buildings` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `date_diffusion_eventId_foreign_idx` FOREIGN KEY (`eventId`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `date_diffusion_examSessionId_foreign_idx` FOREIGN KEY (`examSessionId`) REFERENCES `exam_sessions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `date_diffusion_ibfk_1` FOREIGN KEY (`courId`) REFERENCES `cours` (`id`),
    ADD CONSTRAINT `date_diffusion_qcmId_foreign_idx` FOREIGN KEY (`qcmId`) REFERENCES `qcm` (`id_qcm`),
    ADD CONSTRAINT `date_diffusion_roomId_foreign_idx` FOREIGN KEY (`roomId`) REFERENCES `rooms` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `date_diffusion_organizers`
--
ALTER TABLE `date_diffusion_organizers`
    ADD CONSTRAINT `date_diffusion_organizers_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `date_diffusion_organizers_ibfk_2` FOREIGN KEY (`date_diffusion_id`) REFERENCES `date_diffusion` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `date_diffusion_participants`
--
ALTER TABLE `date_diffusion_participants`
    ADD CONSTRAINT `date_diffusion_participants_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `date_diffusion_participants_ibfk_2` FOREIGN KEY (`date_diffusion_id`) REFERENCES `date_diffusion` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `discussions`
--
ALTER TABLE `discussions`
    ADD CONSTRAINT `discussions_ibfk_1` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `discussions_ibfk_2` FOREIGN KEY (`destinataireId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `discussions_users`
--
ALTER TABLE `discussions_users`
    ADD CONSTRAINT `discussions_users_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `discussions_users_ibfk_2` FOREIGN KEY (`discussionId`) REFERENCES `discussions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `events`
--
ALTER TABLE `events`
    ADD CONSTRAINT `events_folderId_foreign_idx` FOREIGN KEY (`folderId`) REFERENCES `folders` (`id`);

--
-- Contraintes pour la table `event_cours`
--
ALTER TABLE `event_cours`
    ADD CONSTRAINT `event_cours_ibfk_1` FOREIGN KEY (`eventId`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `event_cours_ibfk_2` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `event_type_qcm`
--
ALTER TABLE `event_type_qcm`
    ADD CONSTRAINT `event_type_qcm_ibfk_1` FOREIGN KEY (`typeQcmId`) REFERENCES `type_qcm` (`id`),
    ADD CONSTRAINT `event_type_qcm_ibfk_2` FOREIGN KEY (`eventId`) REFERENCES `events` (`id`);

--
-- Contraintes pour la table `exams`
--
ALTER TABLE `exams`
    ADD CONSTRAINT `exams_folderId_foreign_idx` FOREIGN KEY (`folderId`) REFERENCES `folders` (`id`),
    ADD CONSTRAINT `exams_ibfk_1` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `exam_question_series`
--
ALTER TABLE `exam_question_series`
    ADD CONSTRAINT `exam_question_series_examSessionId_foreign_idx` FOREIGN KEY (`examSessionId`) REFERENCES `exam_sessions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `exam_question_series_ibfk_1` FOREIGN KEY (`mcqId`) REFERENCES `qcm` (`id_qcm`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `exam_question_series_ibfk_2` FOREIGN KEY (`examId`) REFERENCES `exams` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `exam_question_series_ibfk_3` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `exam_results`
--
ALTER TABLE `exam_results`
    ADD CONSTRAINT `exam_results_ibfk_1` FOREIGN KEY (`examSessionId`) REFERENCES `exam_sessions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `exam_results_ibfk_2` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `exam_scales`
--
ALTER TABLE `exam_scales`
    ADD CONSTRAINT `exam_scales_ibfk_1` FOREIGN KEY (`examId`) REFERENCES `exams` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `exam_scales_ibfk_2` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `exam_scale_question_series`
--
ALTER TABLE `exam_scale_question_series`
    ADD CONSTRAINT `exam_scale_question_series_ibfk_1` FOREIGN KEY (`examScaleId`) REFERENCES `exam_scales` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `exam_scale_question_series_ibfk_2` FOREIGN KEY (`examQuestionSerieId`) REFERENCES `exam_question_series` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `exam_sessions`
--
ALTER TABLE `exam_sessions`
    ADD CONSTRAINT `exam_sessions_ibfk_1` FOREIGN KEY (`examId`) REFERENCES `exams` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `exam_sessions_ibfk_2` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `exam_sessions_groups`
--
ALTER TABLE `exam_sessions_groups`
    ADD CONSTRAINT `exam_sessions_groups_ibfk_1` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `exam_sessions_groups_ibfk_2` FOREIGN KEY (`examSessionId`) REFERENCES `exam_sessions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `exam_type_qcm`
--
ALTER TABLE `exam_type_qcm`
    ADD CONSTRAINT `exam_type_qcm_ibfk_1` FOREIGN KEY (`typeQcmId`) REFERENCES `type_qcm` (`id`),
    ADD CONSTRAINT `exam_type_qcm_ibfk_2` FOREIGN KEY (`examId`) REFERENCES `exams` (`id`);

--
-- Contraintes pour la table `fiches`
--
ALTER TABLE `fiches`
    ADD CONSTRAINT `fiches_ibfk_1` FOREIGN KEY (`courId`) REFERENCES `cours` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `fiches_groups`
--
ALTER TABLE `fiches_groups`
    ADD CONSTRAINT `fiches_groups_ibfk_1` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `fiches_groups_ibfk_2` FOREIGN KEY (`ficheId`) REFERENCES `fiches` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `files`
--
ALTER TABLE `files`
    ADD CONSTRAINT `files_ibfk_5` FOREIGN KEY (`courId`) REFERENCES `cours` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `files_ibfk_6` FOREIGN KEY (`messageId`) REFERENCES `messages` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `files_ibfk_7` FOREIGN KEY (`fileId`) REFERENCES `posts` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `files_ibfk_8` FOREIGN KEY (`fileImageId`) REFERENCES `posts` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `files_messageFileImageId_foreign_idx` FOREIGN KEY (`messageFileImageId`) REFERENCES `messages` (`id`),
    ADD CONSTRAINT `files_notionId_foreign_idx` FOREIGN KEY (`notionId`) REFERENCES `notions` (`id`);

--
-- Contraintes pour la table `forfaits`
--
ALTER TABLE `forfaits`
    ADD CONSTRAINT `forfaits_parentId_foreign_idx` FOREIGN KEY (`parentId`) REFERENCES `forfaits` (`id`),
    ADD CONSTRAINT `forfaits_requiredForfaitId_foreign_idx` FOREIGN KEY (`requiredForfaitId`) REFERENCES `forfaits` (`id`);

--
-- Contraintes pour la table `forfait_groups`
--
ALTER TABLE `forfait_groups`
    ADD CONSTRAINT `forfait_groups_ibfk_1` FOREIGN KEY (`forfaitId`) REFERENCES `forfaits` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `forfait_groups_ibfk_2` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `formation`
--
ALTER TABLE `formation`
    ADD CONSTRAINT `formation_ibfk_1` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `formations_groups`
--
ALTER TABLE `formations_groups`
    ADD CONSTRAINT `formations_groups_ibfk_1` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formations_groups_ibfk_2` FOREIGN KEY (`formationId`) REFERENCES `formation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `formation_block`
--
ALTER TABLE `formation_block`
    ADD CONSTRAINT `formation_block_coursId_foreign_idx` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_block_ibfk_1` FOREIGN KEY (`formationStepId`) REFERENCES `formation_step` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_block_ibfk_2` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_block_titleId_foreign_idx` FOREIGN KEY (`titleId`) REFERENCES `titles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `formation_element`
--
ALTER TABLE `formation_element`
    ADD CONSTRAINT `formation_element_blockId_foreign_idx` FOREIGN KEY (`blockId`) REFERENCES `formation_block` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_element_challengeId_foreign_idx` FOREIGN KEY (`challengeId`) REFERENCES `challenges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_element_coursSupportId_foreign_idx` FOREIGN KEY (`coursSupportId`) REFERENCES `cours_supports` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_element_eventId_foreign_idx` FOREIGN KEY (`eventId`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_element_footerQuestionId_foreign_idx` FOREIGN KEY (`footerQuestionId`) REFERENCES `questions` (`id_question`),
    ADD CONSTRAINT `formation_element_forfaitId_foreign_idx` FOREIGN KEY (`forfaitId`) REFERENCES `forfaits` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_element_headerMcqId_foreign_idx` FOREIGN KEY (`headerMcqId`) REFERENCES `qcm` (`id_qcm`),
    ADD CONSTRAINT `formation_element_ibfk_1` FOREIGN KEY (`formationStepId`) REFERENCES `formation_step` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_element_ibfk_2` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_element_ibfk_3` FOREIGN KEY (`mcqId`) REFERENCES `qcm` (`id_qcm`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_element_ibfk_4` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_element_questionId_foreign_idx` FOREIGN KEY (`questionId`) REFERENCES `questions` (`id_question`),
    ADD CONSTRAINT `formation_element_titleId_foreign_idx` FOREIGN KEY (`titleId`) REFERENCES `titles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_element_userPropertyFolderId_foreign_idx` FOREIGN KEY (`userPropertyFolderId`) REFERENCES `user_properties_folders` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `formation_elements_forfaits`
--
ALTER TABLE `formation_elements_forfaits`
    ADD CONSTRAINT `formation_elements_forfaits_ibfk_1` FOREIGN KEY (`elementId`) REFERENCES `formation_element` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_elements_forfaits_ibfk_2` FOREIGN KEY (`forfaitId`) REFERENCES `forfaits` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `formation_elements_groups`
--
ALTER TABLE `formation_elements_groups`
    ADD CONSTRAINT `formation_elements_groups_ibfk_1` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`),
    ADD CONSTRAINT `formation_elements_groups_ibfk_2` FOREIGN KEY (`formationElementId`) REFERENCES `formation_element` (`id`);

--
-- Contraintes pour la table `formation_elements_groups_unlock`
--
ALTER TABLE `formation_elements_groups_unlock`
    ADD CONSTRAINT `formation_elements_groups_unlock_ibfk_1` FOREIGN KEY (`formationElementId`) REFERENCES `formation_element` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_elements_groups_unlock_ibfk_2` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `formation_module_element_progress`
--
ALTER TABLE `formation_module_element_progress`
    ADD CONSTRAINT `formation_module_element_progress_formationId_foreign_idx` FOREIGN KEY (`formationId`) REFERENCES `formation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_module_element_progress_ibfk_1` FOREIGN KEY (`formationStepId`) REFERENCES `formation_step` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_module_element_progress_ibfk_2` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `formation_section`
--
ALTER TABLE `formation_section`
    ADD CONSTRAINT `formation_section_ibfk_1` FOREIGN KEY (`parentSectionId`) REFERENCES `formation_section` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_section_ibfk_2` FOREIGN KEY (`formationId`) REFERENCES `formation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_section_ibfk_3` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `formation_session`
--
ALTER TABLE `formation_session`
    ADD CONSTRAINT `formation_session_ibfk_1` FOREIGN KEY (`currentStepId`) REFERENCES `formation_step` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_session_ibfk_2` FOREIGN KEY (`formationId`) REFERENCES `formation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_session_ibfk_3` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `formation_step`
--
ALTER TABLE `formation_step`
    ADD CONSTRAINT `formation_step_ibfk_1` FOREIGN KEY (`sectionId`) REFERENCES `formation_section` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `formation_step_ibfk_2` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `forums`
--
ALTER TABLE `forums`
    ADD CONSTRAINT `forums_parentId_foreign_idx` FOREIGN KEY (`parentId`) REFERENCES `forum_categories` (`id`);

--
-- Contraintes pour la table `forum_categories`
--
ALTER TABLE `forum_categories`
    ADD CONSTRAINT `forum_categories_forumId_foreign_idx` FOREIGN KEY (`forumId`) REFERENCES `forums` (`id`);

--
-- Contraintes pour la table `global_announce_groups`
--
ALTER TABLE `global_announce_groups`
    ADD CONSTRAINT `global_announce_groups_ibfk_1` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `global_announce_groups_ibfk_2` FOREIGN KEY (`globalAnnounceId`) REFERENCES `global_announce` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `good_answers_stats_user_synthesis`
--
ALTER TABLE `good_answers_stats_user_synthesis`
    ADD CONSTRAINT `good_answers_stats_user_synthesis_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `good_answers_stats_user_synthesis_ibfk_2` FOREIGN KEY (`ueId`) REFERENCES `ues` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `good_answers_stats_user_synthesis_ibfk_3` FOREIGN KEY (`ueCategoryId`) REFERENCES `uecategories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `good_answers_stats_user_synthesis_ibfk_4` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `good_answers_stats_user_synthesis_ibfk_5` FOREIGN KEY (`notionId`) REFERENCES `notions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `groupes`
--
ALTER TABLE `groupes`
    ADD CONSTRAINT `groupes_folderId_foreign_idx` FOREIGN KEY (`folderId`) REFERENCES `folders` (`id`);

--
-- Contraintes pour la table `groups_responsibility`
--
ALTER TABLE `groups_responsibility`
    ADD CONSTRAINT `groups_responsibility_ibfk_1` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `groups_responsibility_ibfk_2` FOREIGN KEY (`responsibleOfGroupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `like_histories`
--
ALTER TABLE `like_histories`
    ADD CONSTRAINT `like_histories_ibfk_3` FOREIGN KEY (`postId`) REFERENCES `posts` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `like_histories_ibfk_4` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `logs`
--
ALTER TABLE `logs`
    ADD CONSTRAINT `logs_coursId_foreign_idx` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`),
    ADD CONSTRAINT `logs_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`),
    ADD CONSTRAINT `logs_ibfk_2` FOREIGN KEY (`reporteeUserId`) REFERENCES `users` (`id`),
    ADD CONSTRAINT `logs_ibfk_3` FOREIGN KEY (`qcmId`) REFERENCES `qcm` (`id_qcm`),
    ADD CONSTRAINT `logs_ibfk_4` FOREIGN KEY (`questionId`) REFERENCES `questions` (`id_question`),
    ADD CONSTRAINT `logs_ibfk_5` FOREIGN KEY (`answerId`) REFERENCES `question_answers` (`id`),
    ADD CONSTRAINT `logs_ibfk_6` FOREIGN KEY (`forfaitId`) REFERENCES `forfaits` (`id`),
    ADD CONSTRAINT `logs_ibfk_7` FOREIGN KEY (`postId`) REFERENCES `posts` (`id`);

--
-- Contraintes pour la table `mcq_scale_ues`
--
ALTER TABLE `mcq_scale_ues`
    ADD CONSTRAINT `mcq_scale_ues_ibfk_1` FOREIGN KEY (`ueId`) REFERENCES `ues` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `mcq_scale_ues_ibfk_2` FOREIGN KEY (`mcqScaleId`) REFERENCES `mcq_scale` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `messages`
--
ALTER TABLE `messages`
    ADD CONSTRAINT `messages_ibfk_3` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `messages_ibfk_4` FOREIGN KEY (`discussionId`) REFERENCES `discussions` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `modules_quick_access`
--
ALTER TABLE `modules_quick_access`
    ADD CONSTRAINT `modules_quick_access_ibfk_1` FOREIGN KEY (`challengeId`) REFERENCES `challenges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `modules_quick_access_ibfk_2` FOREIGN KEY (`ueId`) REFERENCES `ues` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `modules_quick_access_ibfk_3` FOREIGN KEY (`ueCategoryId`) REFERENCES `uecategories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `modules_quick_access_ibfk_4` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `modules_quick_access_type_qcm`
--
ALTER TABLE `modules_quick_access_type_qcm`
    ADD CONSTRAINT `modules_quick_access_type_qcm_ibfk_1` FOREIGN KEY (`typeQcmId`) REFERENCES `type_qcm` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `modules_quick_access_type_qcm_ibfk_2` FOREIGN KEY (`moduleQuickAccessId`) REFERENCES `modules_quick_access` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `notifications`
--
ALTER TABLE `notifications`
    ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`fromUserId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `notions_keywords`
--
ALTER TABLE `notions_keywords`
    ADD CONSTRAINT `notions_keywords_ibfk_1` FOREIGN KEY (`notionId`) REFERENCES `notions` (`id`);

--
-- Contraintes pour la table `notions_parents`
--
ALTER TABLE `notions_parents`
    ADD CONSTRAINT `notions_parents_ibfk_1` FOREIGN KEY (`notionId`) REFERENCES `notions` (`id`),
    ADD CONSTRAINT `notions_parents_ibfk_2` FOREIGN KEY (`parentNotionId`) REFERENCES `notions` (`id`);

--
-- Contraintes pour la table `notions_posts`
--
ALTER TABLE `notions_posts`
    ADD CONSTRAINT `notions_posts_ibfk_1` FOREIGN KEY (`notionId`) REFERENCES `notions` (`id`),
    ADD CONSTRAINT `notions_posts_ibfk_2` FOREIGN KEY (`postId`) REFERENCES `posts` (`id`);

--
-- Contraintes pour la table `payments`
--
ALTER TABLE `payments`
    ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`logId`) REFERENCES `logs` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `payments_paymentMethodId_foreign_idx` FOREIGN KEY (`paymentMethodId`) REFERENCES `configs` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `payments_forfaits`
--
ALTER TABLE `payments_forfaits`
    ADD CONSTRAINT `payments_forfaits_ibfk_1` FOREIGN KEY (`paymentId`) REFERENCES `payments` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `payments_forfaits_ibfk_2` FOREIGN KEY (`forfaitId`) REFERENCES `forfaits` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `posts`
--
ALTER TABLE `posts`
    ADD CONSTRAINT `posts_answerId_foreign_idx` FOREIGN KEY (`answerId`) REFERENCES `question_answers` (`id`),
    ADD CONSTRAINT `posts_eventId_foreign_idx` FOREIGN KEY (`eventId`) REFERENCES `events` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `posts_forumId_foreign_idx` FOREIGN KEY (`forumId`) REFERENCES `forums` (`id`),
    ADD CONSTRAINT `posts_ibfk_5` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `posts_ibfk_6` FOREIGN KEY (`courId`) REFERENCES `cours` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `posts_ibfk_7` FOREIGN KEY (`qcmIdQcm`) REFERENCES `qcm` (`id_qcm`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `posts_ibfk_8` FOREIGN KEY (`parentId`) REFERENCES `posts` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `posts_postTypeId_foreign_idx` FOREIGN KEY (`postTypeId`) REFERENCES `post_type` (`id`),
    ADD CONSTRAINT `posts_userIdForAiFeedback_foreign_idx` FOREIGN KEY (`userIdForAiFeedback`) REFERENCES `users` (`id`);

--
-- Contraintes pour la table `qcm`
--
ALTER TABLE `qcm`
    ADD CONSTRAINT `qcm_ibfk_4` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `qcm_ibfk_5` FOREIGN KEY (`sousCategorieIdSousCategorie`) REFERENCES `uecategories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `qcm_ibfk_6` FOREIGN KEY (`UEId`) REFERENCES `ues` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `qcm_default_type_qcm_for_questions`
--
ALTER TABLE `qcm_default_type_qcm_for_questions`
    ADD CONSTRAINT `qcm_default_type_qcm_for_questions_ibfk_1` FOREIGN KEY (`qcmId`) REFERENCES `qcm` (`id_qcm`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `qcm_default_type_qcm_for_questions_ibfk_2` FOREIGN KEY (`typeQcmId`) REFERENCES `type_qcm` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `qcm_saved_state`
--
ALTER TABLE `qcm_saved_state`
    ADD CONSTRAINT `qcm_saved_state_ibfk_1` FOREIGN KEY (`qcmId`) REFERENCES `qcm` (`id_qcm`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `qcm_saved_state_ibfk_2` FOREIGN KEY (`sessionId`) REFERENCES `qcm_sessions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `qcm_saved_state_ibfk_3` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `qcm_sessions`
--
ALTER TABLE `qcm_sessions`
    ADD CONSTRAINT `qcm_sessions_challengeId_foreign_idx` FOREIGN KEY (`challengeId`) REFERENCES `challenges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `qcm_sessions_currentCoursId_foreign_idx` FOREIGN KEY (`currentCoursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `qcm_sessions_examQuestionSerieId_foreign_idx` FOREIGN KEY (`examQuestionSerieId`) REFERENCES `exam_question_series` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `qcm_sessions_examSessionId_foreign_idx` FOREIGN KEY (`examSessionId`) REFERENCES `exam_sessions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `qcm_sessions_formationElementId_foreign_idx` FOREIGN KEY (`formationElementId`) REFERENCES `formation_element` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `qcm_sessions_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`),
    ADD CONSTRAINT `qcm_sessions_ibfk_2` FOREIGN KEY (`qcmId`) REFERENCES `qcm` (`id_qcm`);

--
-- Contraintes pour la table `questions`
--
ALTER TABLE `questions`
    ADD CONSTRAINT `questions_authorId_foreign_idx` FOREIGN KEY (`authorId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `questions_ibfk_3` FOREIGN KEY (`id_qcm`) REFERENCES `qcm` (`id_qcm`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `questions_ibfk_4` FOREIGN KEY (`id_sous_categorie`) REFERENCES `uecategories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `questions_double_parents`
--
ALTER TABLE `questions_double_parents`
    ADD CONSTRAINT `questions_double_parents_ibfk_1` FOREIGN KEY (`questionId`) REFERENCES `questions` (`id_question`),
    ADD CONSTRAINT `questions_double_parents_ibfk_2` FOREIGN KEY (`parentQuestionId`) REFERENCES `questions` (`id_question`),
    ADD CONSTRAINT `questions_double_parents_parentQuestionId2_foreign_idx` FOREIGN KEY (`parentQuestionId2`) REFERENCES `questions` (`id_question`);

--
-- Contraintes pour la table `questions_parents`
--
ALTER TABLE `questions_parents`
    ADD CONSTRAINT `questions_parents_ibfk_1` FOREIGN KEY (`questionId`) REFERENCES `questions` (`id_question`),
    ADD CONSTRAINT `questions_parents_ibfk_2` FOREIGN KEY (`parentQuestionId`) REFERENCES `questions` (`id_question`);

--
-- Contraintes pour la table `questions_qcm`
--
ALTER TABLE `questions_qcm`
    ADD CONSTRAINT `questions_qcm_ibfk_1` FOREIGN KEY (`qcmId`) REFERENCES `qcm` (`id_qcm`),
    ADD CONSTRAINT `questions_qcm_ibfk_2` FOREIGN KEY (`questionId`) REFERENCES `questions` (`id_question`);

--
-- Contraintes pour la table `questions_type_qcm`
--
ALTER TABLE `questions_type_qcm`
    ADD CONSTRAINT `questions_type_qcm_ibfk_1` FOREIGN KEY (`typeQcmId`) REFERENCES `type_qcm` (`id`),
    ADD CONSTRAINT `questions_type_qcm_ibfk_2` FOREIGN KEY (`questionId`) REFERENCES `questions` (`id_question`);

--
-- Contraintes pour la table `question_answers`
--
ALTER TABLE `question_answers`
    ADD CONSTRAINT `question_answers_ibfk_1` FOREIGN KEY (`questionId`) REFERENCES `questions` (`id_question`);

--
-- Contraintes pour la table `question_cours`
--
ALTER TABLE `question_cours`
    ADD CONSTRAINT `question_cours_ibfk_1` FOREIGN KEY (`questionId`) REFERENCES `questions` (`id_question`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `question_cours_ibfk_2` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `rooms`
--
ALTER TABLE `rooms`
    ADD CONSTRAINT `rooms_ibfk_1` FOREIGN KEY (`buildingId`) REFERENCES `buildings` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `sous_categories`
--
ALTER TABLE `sous_categories`
    ADD CONSTRAINT `sous_categories_ibfk_1` FOREIGN KEY (`ueIdId`) REFERENCES `ues` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `statistiques`
--
ALTER TABLE `statistiques`
    ADD CONSTRAINT `statistiques_qcmSessionId_foreign_idx` FOREIGN KEY (`qcmSessionId`) REFERENCES `qcm_sessions` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `statistiques_questions`
--
ALTER TABLE `statistiques_questions`
    ADD CONSTRAINT `statistiques_questions_qcmSessionId_foreign_idx` FOREIGN KEY (`qcmSessionId`) REFERENCES `qcm_sessions` (`id`),
    ADD CONSTRAINT `statistiques_questions_statId_foreign_idx` FOREIGN KEY (`statId`) REFERENCES `statistiques` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `stats_notions_users`
--
ALTER TABLE `stats_notions_users`
    ADD CONSTRAINT `stats_notions_users_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`),
    ADD CONSTRAINT `stats_notions_users_ibfk_2` FOREIGN KEY (`notionId`) REFERENCES `notions` (`id`);

--
-- Contraintes pour la table `stats_questions_answers`
--
ALTER TABLE `stats_questions_answers`
    ADD CONSTRAINT `stats_questions_answers_ibfk_1` FOREIGN KEY (`statsQuestionId`) REFERENCES `statistiques_questions` (`id_statistique_question`),
    ADD CONSTRAINT `stats_questions_answers_ibfk_2` FOREIGN KEY (`answerId`) REFERENCES `question_answers` (`id`);

--
-- Contraintes pour la table `type_qcm_groups`
--
ALTER TABLE `type_qcm_groups`
    ADD CONSTRAINT `type_qcm_groups_ibfk_1` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `type_qcm_groups_ibfk_2` FOREIGN KEY (`typeQcmId`) REFERENCES `type_qcm` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `uecategories`
--
ALTER TABLE `uecategories`
    ADD CONSTRAINT `uecategories_ibfk_1` FOREIGN KEY (`ueId`) REFERENCES `ues` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `uecategories_parentId_foreign_idx` FOREIGN KEY (`parentId`) REFERENCES `uecategories` (`id`);

--
-- Contraintes pour la table `uecategories_groups`
--
ALTER TABLE `uecategories_groups`
    ADD CONSTRAINT `uecategories_groups_ibfk_1` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `uecategories_groups_ibfk_2` FOREIGN KEY (`uecategory_id`) REFERENCES `uecategories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `ues`
--
ALTER TABLE `ues`
    ADD CONSTRAINT `ues_parentId_foreign_idx` FOREIGN KEY (`parentId`) REFERENCES `ues` (`id`);

--
-- Contraintes pour la table `ue_groups`
--
ALTER TABLE `ue_groups`
    ADD CONSTRAINT `ue_groups_ibfk_1` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `ue_groups_ibfk_2` FOREIGN KEY (`ueId`) REFERENCES `ues` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `ue_tuteurs`
--
ALTER TABLE `ue_tuteurs`
    ADD CONSTRAINT `ue_tuteurs_ibfk_1` FOREIGN KEY (`ueId`) REFERENCES `ues` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `ue_tuteurs_ibfk_2` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `userPreferences`
--
ALTER TABLE `userPreferences`
    ADD CONSTRAINT `userpreferences_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `userStats`
--
ALTER TABLE `userStats`
    ADD CONSTRAINT `userstats_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `users_blockeds`
--
ALTER TABLE `users_blockeds`
    ADD CONSTRAINT `users_blockeds_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `users_blockeds_ibfk_2` FOREIGN KEY (`userBlockedId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `user_devices`
--
ALTER TABLE `user_devices`
    ADD CONSTRAINT `user_devices_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `user_groups`
--
ALTER TABLE `user_groups`
    ADD CONSTRAINT `user_groups_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user_groups_ibfk_2` FOREIGN KEY (`groupeId`) REFERENCES `groupes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `user_properties_data`
--
ALTER TABLE `user_properties_data`
    ADD CONSTRAINT `user_properties_data_ibfk_1` FOREIGN KEY (`elementId`) REFERENCES `formation_element` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user_properties_data_ibfk_2` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user_properties_data_logObjectId_foreign_idx` FOREIGN KEY (`logObjectId`) REFERENCES `logs` (`id`);

--
-- Contraintes pour la table `user_sessions`
--
ALTER TABLE `user_sessions`
    ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `user_stats_by_day`
--
ALTER TABLE `user_stats_by_day`
    ADD CONSTRAINT `user_stats_by_day_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `user_time_spent_by_ue`
--
ALTER TABLE `user_time_spent_by_ue`
    ADD CONSTRAINT `user_time_spent_by_ue_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user_time_spent_by_ue_ibfk_2` FOREIGN KEY (`ueId`) REFERENCES `ues` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user_time_spent_by_ue_ibfk_3` FOREIGN KEY (`ueCategoryId`) REFERENCES `uecategories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user_time_spent_by_ue_ibfk_4` FOREIGN KEY (`coursId`) REFERENCES `cours` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `views_histories`
--
ALTER TABLE `views_histories`
    ADD CONSTRAINT `views_histories_ibfk_4` FOREIGN KEY (`courId`) REFERENCES `cours` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `views_histories_ibfk_5` FOREIGN KEY (`qcmIdQcm`) REFERENCES `qcm` (`id_qcm`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `views_histories_ibfk_6` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT `views_histories_postId_foreign_idx` FOREIGN KEY (`postId`) REFERENCES `posts` (`id`);
COMMIT;



/*!40101 SET CHARACTER_SET_CLIENT = @OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS = @OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION = @OLD_COLLATION_CONNECTION */;
