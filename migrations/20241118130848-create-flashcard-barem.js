'use strict';

const { McqScaleService } = require('../src/graph-api/qcm/scales/mcq-scale-service.js')
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {

    await McqScaleService.createBaseFlashcardScale()

  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
