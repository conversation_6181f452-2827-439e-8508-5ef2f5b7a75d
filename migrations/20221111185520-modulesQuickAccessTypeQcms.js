'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('modules_quick_access_type_qcm', {
      typeQcmId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'type_qcm',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      moduleQuickAccessId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'modules_quick_access',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
    })
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('modules_quick_access_type_qcm');
  }
};
