'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Set exostaff to true for user having id 2 and 38
    await queryInterface.sequelize.query(`
      UPDATE users
      SET exostaff = true
      WHERE id IN (2, 38);
    `);

  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
