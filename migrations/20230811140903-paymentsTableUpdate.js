'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {

    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
          'bills',
          'paymentId',
          {
            type: Sequelize.INTEGER,
            defaultValue: null,
            references: {
              model: 'payments',
              key: 'id',
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          },
          { transaction },
      );
      await queryInterface.addColumn(
          'payments',
          'sum',
          {
            type: Sequelize.FLOAT,
            defaultValue: null,
          },
          { transaction },
      );
      await queryInterface.addColumn(
          'payments',
          'paymentMethodId',
          {
            type: Sequelize.INTEGER,
            defaultValue: null,
            references: {
                model: 'configs',
                key: 'id',
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          },
          { transaction },
      );
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }

  },

  async down (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('bills', 'paymentId', { transaction });
      await queryInterface.removeColumn('payments', 'sum', { transaction });
      await queryInterface.removeColumn('payments', 'paymentMethodId', { transaction });
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  }
};
