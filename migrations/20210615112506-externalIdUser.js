'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'users',
        'externalId',
        {
          type: Sequelize.STRING,
          defaultValue: null,
        },
      ),
    ])
  },


  down: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.removeColumn(
        'users',
        'externalId',
      ),
    ])
  },
}
