const Sequelize = require('sequelize')
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('statistiques_questions', 'A_eleve', { transaction });
      await queryInterface.removeColumn('statistiques_questions', 'B_eleve', { transaction });
      await queryInterface.removeColumn('statistiques_questions', 'C_eleve', { transaction });
      await queryInterface.removeColumn('statistiques_questions', 'D_eleve', { transaction });
      await queryInterface.removeColumn('statistiques_questions', 'E_eleve', { transaction });
      await queryInterface.removeColumn('statistiques_questions', 'F_eleve', { transaction });
      await queryInterface.removeColumn('statistiques_questions', 'G_eleve', { transaction });

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  }
};