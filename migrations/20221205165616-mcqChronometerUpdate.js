'use strict';
const TABLENAME = 'qcm';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        TABLENAME,
        'goToNextQuestionWhenTimesUp', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        TABLENAME,
        'shouldResumeTime', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        TABLENAME,
        'chronoByQuestionOrGlobal', {
          type: Sequelize.STRING,
          defaultValue: 'timeByQuestion',
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(TABLENAME, 'goToNextQuestionWhenTimesUp', { transaction });
      await queryInterface.removeColumn(TABLENAME, 'chronoByQuestionOrGlobal', { transaction });
      await queryInterface.removeColumn(TABLENAME, 'shouldResumeTime', { transaction });
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
