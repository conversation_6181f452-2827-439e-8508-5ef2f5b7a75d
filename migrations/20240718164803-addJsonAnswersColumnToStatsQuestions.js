'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn(
      'statistiques_questions',
      'jsonAnswers', {
        type: Sequelize.JSON,
        defaultValue: null
      });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('statistiques_questions', 'jsonAnswers');
  }
};
