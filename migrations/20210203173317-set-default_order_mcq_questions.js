'use strict'

import models from '../src/models/index.js'

const updateAllQuestionOrder = async () => {
  try {
    let allQuestions = await models.Question.findAll({ attributes: ['id_question', 'order'] })
    for(let q of allQuestions) {
      q.order = q.id_question
      await q.save()
    }
    return true
  } catch (e) {
    console.error(e)
    return false
  }
}

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return await updateAllQuestionOrder()
  },

  down: (queryInterface, Sequelize) => {
    console.error('tried to migrate down updateAllQuestionOrder')
  },
}
