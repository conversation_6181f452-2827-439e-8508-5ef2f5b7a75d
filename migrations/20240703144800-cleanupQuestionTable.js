'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Remove columns 'reponse_A', 'reponse_B', 'reponse_C', 'reponse_D' and 'reponse_E' from the 'questions' table
    await queryInterface.removeColumn('questions', 'reponse_A');
    await queryInterface.removeColumn('questions', 'reponse_B');
    await queryInterface.removeColumn('questions', 'reponse_C');
    await queryInterface.removeColumn('questions', 'reponse_D');
    await queryInterface.removeColumn('questions', 'reponse_E');
    await queryInterface.removeColumn('questions', 'reponse_F');
    await queryInterface.removeColumn('questions', 'reponse_G');

    await queryInterface.removeColumn('questions', 'A');
    await queryInterface.removeColumn('questions', 'B');
    await queryInterface.removeColumn('questions', 'C');
    await queryInterface.removeColumn('questions', 'D');
    await queryInterface.removeColumn('questions', 'E');
    await queryInterface.removeColumn('questions', 'F');
    await queryInterface.removeColumn('questions', 'G');

    await queryInterface.removeColumn('questions', 'explication_A');
    await queryInterface.removeColumn('questions', 'explication_B');
    await queryInterface.removeColumn('questions', 'explication_C');
    await queryInterface.removeColumn('questions', 'explication_D');
    await queryInterface.removeColumn('questions', 'explication_E');
    await queryInterface.removeColumn('questions', 'explication_F');
    await queryInterface.removeColumn('questions', 'explication_G');

  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
