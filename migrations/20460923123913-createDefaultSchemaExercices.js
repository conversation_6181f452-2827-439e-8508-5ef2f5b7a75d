'use strict';
const { ConfigUpgrades } = require('../src/graph-api/config/config-upgrades');
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // C<PERSON><PERSON> exercices schéma point and click par défaut liés aux schémas
    await ConfigUpgrades.createDefaultSchemaExercicesPointAndClick();
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
