'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    function convertCamelToSnake(str) {
      return str.replace(/([A-Z])/g, '_$1').toLowerCase();
    }
    const addIndex = async (table, column) => {
      const t1 = table;
      const c1 = column;
      const indexes = await queryInterface.showIndex(t1);
      const indexesNames = indexes.map(index => index.name);
      const snakeString = convertCamelToSnake(`${t1}_${c1}`);
      if(!indexesNames.find(i => i === snakeString)) {
        console.log(`adding index ${snakeString}`);
        await queryInterface.addIndex(t1, [c1] );
      } else {
        console.log('index already exists')
      }
    };
    await addIndex('date_diffusion', 'date'); // Ajout index sur date pour les performances
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
