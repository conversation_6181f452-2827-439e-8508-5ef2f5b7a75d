'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Créé table de liaison schema_library et type_qcm
    await queryInterface.createTable('schema_library_type_qcm', {
      schemaLibraryId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'schema_library',
          key: 'id'
        },
      },
      typeQcmId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'type_qcm',
          key: 'id'
        },
      },
      createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
      updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('schema_library_type_qcm');
  }
};
