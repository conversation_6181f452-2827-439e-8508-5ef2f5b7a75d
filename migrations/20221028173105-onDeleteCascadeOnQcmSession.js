'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.removeConstraint(
        'statistiques',
        'statistiques_qcmSessionId_foreign_idx',
        { transaction }
      );

      await queryInterface.addConstraint('statistiques', {
        fields: ['qcmSessionId'],
        type: 'foreign key',
        name: 'statistiques_qcmSessionId_foreign_idx',
        references: {
          table: 'qcm_sessions',
          field: 'id',
        },
        onDelete: 'CASCADE',
        transaction
      });
      return transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
