'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('mathpixes', {
      id:{
        allowNull: false,
        autoIncrement: true,
        primaryKey:true,
        type: Sequelize.INTEGER,
      },
      hash: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      algorithm: {
        type: Sequelize.STRING,
        allowNull: false
      },
      mathpixFileId: {
        type: Sequelize.STRING,
        allowNull: false
      },
      file: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      image: {
        type: Sequelize.STRING,
        allowNull: true
      },
      mathpixConfigId:{
        type:Sequelize.INTEGER,
        allowNull: true,
        references:{
          model:'configs',
          key:'id'
        },
        onUpdate:'CASCADE',
        onDelete:'SET NULL',// TODO : REFLECHIR A CE QUE L'ON VEUT FAIRE SI ON DELETE LA MATHKEY API ET UPDATE DANS MIGRATION
      },
      authorId:{
        type:Sequelize.INTEGER,
        allowNull:false,
        references:{
          model:'users',
          key:'id'
        },
        onUpdate:'CASCADE',
        onDelete:'CASCADE'
      },
      filename:{
        type:Sequelize.STRING,
        allowNull:false
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });
    await queryInterface.addIndex('mathpixes', ['hash']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('mathpixes', ['hash']);
    await queryInterface.dropTable('mathpixes');
  }
};
