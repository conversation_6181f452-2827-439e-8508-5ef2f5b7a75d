'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.createTable('mcq_scale', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        name: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        rules: {
          type: Sequelize.TEXT('long'),
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
      }),
      queryInterface.addColumn(
        'questions',
        'mcqScaleId',
        {
          type: Sequelize.INTEGER,
          defaultValue: null,
        },
      ),
    ])
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
        queryInterface.dropTable('mcq_scale'),
        queryInterface.removeColumn(
          'questions',
          'mcqScaleId',
        ),
      ],
    )
  },
}
