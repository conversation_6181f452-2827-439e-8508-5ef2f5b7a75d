'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction()
    try {
      await queryInterface.addColumn(
        'notions_cours',
        'autoAdded',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false, // default, manual
        },
        { transaction },
      )
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.removeColumn(
        'notions_cours',
        'autoAdded',
      ),
    ])
  },
}
