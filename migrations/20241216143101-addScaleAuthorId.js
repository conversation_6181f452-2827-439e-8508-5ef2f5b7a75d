'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('mcq_scale', 'authorId', {
      type: Sequelize.INTEGER,
      allowNull: true, // Si l'auteur peut être facultatif, sinon mettre false.
      references: {
        model: 'users', // Nom de la table User
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL', // Ou 'CASCADE' si vous voulez supprimer les scales avec l'utilisateur.
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('mcq_scale', 'authorId');
  }
};
