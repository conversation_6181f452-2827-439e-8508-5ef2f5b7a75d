'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('schema_library', {
      id: {
        type: Sequelize.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: Sequelize.DataTypes.STRING,
      name_en: Sequelize.DataTypes.STRING,
      name_es: Sequelize.DataTypes.STRING,
      name_it: Sequelize.DataTypes.STRING,
      name_de: Sequelize.DataTypes.STRING,
      description: Sequelize.DataTypes.TEXT,
      description_en: Sequelize.DataTypes.TEXT,
      description_es: Sequelize.DataTypes.TEXT,
      description_it: Sequelize.DataTypes.TEXT,
      description_de: Sequelize.DataTypes.TEXT,
      image: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: null,
      },
      imageCorrection: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: null,
      },
      legends: {
        type: Sequelize.DataTypes.JSON,
        defaultValue: {},
      },
      authorId: {
        type: Sequelize.DataTypes.INTEGER,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
        defaultValue: Sequelize.NOW
      }
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('schema_library');
  }
};
