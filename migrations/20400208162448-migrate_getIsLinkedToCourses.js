'use strict';

const { QuestionsService } = require('../src/graph-api/qcm/questions/questions-service.js')
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    return await QuestionsService.migrateIsQuestionLinkedToCourses();
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
