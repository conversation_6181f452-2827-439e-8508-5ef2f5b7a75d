'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {

    await queryInterface.addColumn('buildings', 'postCode', {
      type: Sequelize.STRING,
      defaultValue: null,
    });
    // city, country
    await queryInterface.addColumn('buildings', 'city', {
      type: Sequelize.STRING,
      defaultValue: null,
    });
    await queryInterface.addColumn('buildings', 'country', {
      type: Sequelize.STRING,
      defaultValue: null,
    });


  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
