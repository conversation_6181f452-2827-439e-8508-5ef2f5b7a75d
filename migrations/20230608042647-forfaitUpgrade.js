'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'forfaits',
        'products',
        {
          type: Sequelize.JSON, defaultValue: [
            {
              name: 'Offre',
              priceHt: 0,
              tvaPercent: 20,
              image: null,
            },
          ],
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'forfaits',
        'paymentSettings',
        { type: Sequelize.JSON, defaultValue: {
            paymentMethod: 'stripe',
            paymentType: 'once', // multiple
          }},
        { transaction },
      );
      await queryInterface.addColumn(
        'forfaits',
        'emailSettings',
        {
          type: Sequelize.JSON,
          defaultValue: {
              shouldSend: true,
              settings: 'default',
              subject: 'Votre commande',
              body: '',
              attachments: [],
          }},
        { transaction },
      );
      await queryInterface.addColumn(
        'forfaits',
        'promoCodes',
        { type: Sequelize.JSON, defaultValue: []},
        /*
        {
            code: '',
            type: '',
            value: 0,
          }
         */
        { transaction },
      );
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
