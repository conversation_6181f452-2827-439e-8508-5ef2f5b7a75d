'use strict';
const TABLENAME = 'ues';
const TABLE_CATEGORY = 'uecategories';

// Masquer résultat final
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        TABLENAME,
        'isFolder', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        TABLENAME,
        'parentId', {
          type: Sequelize.INTEGER,
          defaultValue: null,
          references: {
            model: TABLENAME,
            key: 'id',
          },
        },
        { transaction },
      );

      await queryInterface.addColumn(
        TABLE_CATEGORY,
        'parentId', {
          type: Sequelize.INTEGER,
          defaultValue: null,
          references: {
            model: TABLE_CATEGORY,
            key: 'id',
          },
        },
        { transaction },
      );
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(TABLENAME, 'isFolder', { transaction });
      await queryInterface.removeColumn(TABLENAME, 'parentId', { transaction });
      await queryInterface.removeColumn(TABLE_CATEGORY, 'parentId', { transaction });
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
