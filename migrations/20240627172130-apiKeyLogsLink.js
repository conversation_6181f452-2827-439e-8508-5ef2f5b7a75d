'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn(
      'logs',
      'apiKeyId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'api_keys',
        key: 'id'
      },
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('logs', 'apiKeyId');
  }
};
