'use strict'

// NOT USED
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(t => {

      return Promise.all([

        // Posts can be linked to answers
        queryInterface.addColumn(
          'posts',
          'answerId',
          {
            type: Sequelize.INTEGER,
            references: {
              model: 'question_answers', // name of Target model
              key: 'id', // key in Target model that we're referencing
            },
          },
          { transaction: t },
        ),

        // Posts can be solved
        queryInterface.addColumn(
          'posts',
          'isResolved',
          {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
          },
          { transaction: t },
        ),

        // Post type
        queryInterface.createTable(
          'post_type',
          {
            id: { allowNull: false, autoIncrement: true, primaryKey: true, type: Sequelize.INTEGER },
            name: {
              type: Sequelize.STRING,
              defaultValue: null,
            },
            image: { type: Sequelize.STRING, defaultValue: null, allowNull: true },
            createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
            updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
          },
          { transaction: t },
        ),


      ])
    })
  },

  down: (queryInterface, Sequelize) => {

    return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.removeColumn(
          'posts',
          'answerId',
          { transaction: t }),
        queryInterface.removeColumn(
          'posts',
          'isResolved',
          { transaction: t }),
        queryInterface.dropTable(
          'post_type',
          { transaction: t },
        ),
      ])
    })
  },
}
