'use strict'
const TABLENAME = 'events'


module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable(TABLENAME, {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.TEXT('long')
      },
      description: {
        type: Sequelize.TEXT('long')
      },
      // CustomFields have a label, type and value
      customFields: {
        type: Sequelize.JSON,
        defaultValue: null
      },
      // User ids array
      organizers: {
        type: Sequelize.JSON,
        defaultValue: null
      },

      createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
      updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable(TABLENAME);
  }
};