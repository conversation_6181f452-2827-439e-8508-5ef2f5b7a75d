'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'forfaits',
        'creditGiven',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },
      ),
    ])
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.removeColumn(
        'forfaits',
        'creditGiven',
      ),
    ])
  },
}
