'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {

    function convertCamelToSnake(str) {
      return str.replace(/([A-Z])/g, '_$1').toLowerCase();
    }
    const addIndex = async (table, column) => {
      const t1 = table;
      const c1 = column;
      const indexes = await queryInterface.showIndex(t1);
      const indexesNames = indexes.map(index => index.name);
      const snakeString = convertCamelToSnake(`${t1}_${c1}`);
      if(!indexesNames.find(i => i === snakeString)) {
        console.log(`adding index ${snakeString}`);
        await queryInterface.addIndex(t1, [c1]);
      }
    };

    await addIndex('ue_tuteurs', 'ueId');
    await addIndex('notifications', 'createdAt');
    await addIndex('user_groups', 'userId');
    await addIndex('date_diffusion_groups', 'groupId');
    await addIndex('date_diffusion_groups', 'date_diffusion_id');
    await addIndex('qcm', 'ue');
    await addIndex('qcm', 'id_createur');
    await addIndex('statistiques_questions', 'id_utilisateur');
    await addIndex('ue_groups', 'groupeId');
    await addIndex('annees', 'annee');
    await addIndex('configs', 'key');
    await addIndex('question_type_question', 'typeQuestionId');
    await addIndex('question_type_question', 'questionId');
    await addIndex('statistiques', 'date');
    await addIndex('statistiques', 'id_utilisateur');
    await addIndex('statistiques', 'id_qcm');


  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
