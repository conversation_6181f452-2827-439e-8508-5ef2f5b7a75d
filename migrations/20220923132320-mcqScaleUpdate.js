'use strict';
const TABLENAME1 = 'mcq_scale';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        TABLENAME1,
        'questionType', {
          type: Sequelize.STRING,
          defaultValue: null,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        TABLENAME1,
        'pointsObtainedWhenNothingChecked', {
          type: Sequelize.FLOAT,
          defaultValue: null,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(TABLENAME1, 'questionType', { transaction });
      await queryInterface.removeColumn(TABLENAME1, 'pointsObtainedWhenNothingChecked', { transaction });
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};