'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'logs',
        'coursId', {
          type: Sequelize.INTEGER,
          defaultValue: null,
          references: {
            model: 'cours',
            key: 'id',
          },
        },
      ),
    ])
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  },
}
