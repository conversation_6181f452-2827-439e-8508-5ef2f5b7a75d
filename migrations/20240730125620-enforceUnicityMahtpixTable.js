'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addIndex('mathpixes',['hash','mathpixConfigId'], {
      unique:true,
      name:'unicityConditionHashAndMathpixId'
    })
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeIndex('mathpixies', 'unicityConditionHashAndMathpixId');
  }
};
