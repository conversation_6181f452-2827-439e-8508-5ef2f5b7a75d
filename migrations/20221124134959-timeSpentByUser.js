const TABLENAME1 = 'user_time_spent_by_ue';
const TABLENAME2 = 'user_stats_by_day';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(TABLENAME1, {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },

          userId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'users', // name of Target model
              key: 'id', // key in Target model that we're referencing
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          },
          ueId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'ues', // name of Target model
              key: 'id', // key in Target model that we're referencing
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          },
          ueCategoryId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'uecategories', // name of Target model
              key: 'id', // key in Target model that we're referencing
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          },
          coursId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'cours', // name of Target model
              key: 'id', // key in Target model that we're referencing
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          },

          time: { type: Sequelize.INTEGER, defaultValue: 0 }, // in seconds, time spent
          createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
          updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
        },
        { transaction },
      );

      await queryInterface.createTable(TABLENAME2, {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          userId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'users', // name of Target model
              key: 'id', // key in Target model that we're referencing
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          },
          time: { type: Sequelize.INTEGER, defaultValue: 0 }, // in seconds, time spent
          questionsDone: { type: Sequelize.INTEGER, defaultValue: 0 }, // questions done
          date: { type: Sequelize.DATE, defaultValue: Sequelize.NOW }, // when
          createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
          updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
        },
        { transaction },
      );
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
