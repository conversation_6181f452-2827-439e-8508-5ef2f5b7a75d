'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Create new table ue_module_progress_logs
    await queryInterface.createTable('ue_module_progress_logs', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      ueId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'ues',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      ueModuleId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'ue_modules',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        }
      },

      // values: "launched" indicates if the module was launched
      // "success" indicates if the module was completed successfully
      // "failed" indicates if the module was completed with errors
      logOperation: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'unknown',
      },

      seconds: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },

      /* Utiliser createdAt pour le timestamp de lancement*/
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      }
    });
    // add index on logOperation
    //await queryInterface.addIndex('ue_module_progress_logs', ['logOperation']);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('ue_module_progress_logs');
  }
};
