'use strict'
const TABLENAME = 'mcq_scale_ues'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable(TABLENAME, {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      ueId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'ues',
          key: 'id',
        },
        defaultValue: null,
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      mcqScaleId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'mcq_scale',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
      updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable(TABLENAME);
  }
};