'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction()
    try {
      await queryInterface.addColumn(
        'notions',
        'description',
        {
          type: Sequelize.TEXT,
          defaultValue: null,
        },
        { transaction },
      )
      await queryInterface.addColumn(
        'notions',
        'formula',
        {
          type: Sequelize.TEXT,
          defaultValue: null,
        },
        { transaction },
      )
      await queryInterface.addColumn(
        'files',
        'notionId',
        {
          type: Sequelize.INTEGER,
          references: {
            model: 'notions',
            key: 'id',
          },
          allowNull: true,
        },
        { transaction },
      )

    } catch (err) {
      await transaction.rollback()
      throw err
    }
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.removeColumn(
        'notions',
        'description',
      ),
      queryInterface.removeColumn(
        'notions',
        'formula',
      ),
      queryInterface.removeColumn(
        'files',
        'notionId',
      ),

    ])
  },
}
