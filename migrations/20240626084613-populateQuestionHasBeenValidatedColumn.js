'use strict';
import models from '../src/models/index.js';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Mise à jour des questions générées par AI
      await models.Question.update(
        { aiGenerationHasBeenValidated: true },
        { where: { isAigenerated: true }}
      );
    } catch (error) {
      console.error('Error during migration: ', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Revert: Remettre à false les valeurs modifiées (ou ajuster selon vos besoins)
      await models.Question.update(
        { aiGenerationHasBeenValidated: null },
        { where: { isAigenerated: true } }
      );
    } catch (error) {
      console.error('Error during rollback: ', error);
      throw error;
    }
  }
};
