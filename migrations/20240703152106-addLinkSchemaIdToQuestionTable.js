'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('questions', 'schemaLibraryId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'schema_library',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });



  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('questions', 'schemaLibraryId');
  }
};
