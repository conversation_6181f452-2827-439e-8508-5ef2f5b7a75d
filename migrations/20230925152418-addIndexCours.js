'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {

    const indexes = await queryInterface.showIndex('cours');
    const indexesNames = indexes?.map(index => index.name);
    if(!indexesNames.find(i => i === 'cours_is_en_avant')) {
      console.log('adding index news mis en avant');
      await queryInterface.addIndex('cours', ['isEnAvant'] );
    }

  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
