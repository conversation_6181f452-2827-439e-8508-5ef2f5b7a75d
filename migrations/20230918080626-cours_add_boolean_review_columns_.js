'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('cours', 'isReviewEnabled', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false
    });

    await queryInterface.addColumn('cours', 'isMarkVisible', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false
    });

    await queryInterface.addColumn('cours', 'isFeedbackVisible', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('cours', 'isReviewEnabled');
    await queryInterface.removeColumn('cours', 'isMarkVisible');
    await queryInterface.removeColumn('cours', 'isFeedbackVisible');
  }
};
