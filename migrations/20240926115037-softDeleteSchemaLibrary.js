'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Add soft delete column to schema_library table, and make it indexed
    await queryInterface.addColumn('schema_library', 'deletedAt', {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null
    });
    await queryInterface.addIndex('schema_library', ['deletedAt']);
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('schema_library', 'deletedAt');
  }
};
