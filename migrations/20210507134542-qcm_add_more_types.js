'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'qcm',
        'isFullscreen',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
      ),
      queryInterface.addColumn(
        'questions',
        'isCheckbox',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: true,
        },
      ),
    ])
  },


  down: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.removeColumn(
        'qcm',
        'isFullscreen',
      ),
      queryInterface.removeColumn(
        'questions',
        'isCheckbox',
      ),
    ])
  },
}
