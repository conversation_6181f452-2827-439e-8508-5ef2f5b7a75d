'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.createTable('type_qcm', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        name: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
      }),

      // qcm many to many
      queryInterface.createTable('qcm_type_qcm', {
        typeQcmId: {
          type: Sequelize.DataTypes.INTEGER(11),
          primaryKey: true,
        },
        qcmId: {
          type: Sequelize.DataTypes.INTEGER(11),
          primaryKey: true,
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DataTypes.DATE(6),
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DataTypes.DATE(6),
          defaultValue: Sequelize.NOW,
        },
      }),
    ])
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
        queryInterface.dropTable('type_qcm'),
        queryInterface.dropTable('qcm_type_qcm'),
      ],
    )
  },
}
