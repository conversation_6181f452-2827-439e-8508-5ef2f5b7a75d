'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.addColumn('questions', 'evaluateCertainty', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        }, { transaction: t }),
        queryInterface.addColumn('statistiques_questions', 'certainty', {
          type: Sequelize.INTEGER,
          defaultValue: null,
        }, { transaction: t }),

      ]);
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.removeColumn('questions', 'evaluateCertainty', { transaction: t }),
        queryInterface.removeColumn('statistiques_questions', 'certainty', { transaction: t }),
      ]);
    });
  }
};