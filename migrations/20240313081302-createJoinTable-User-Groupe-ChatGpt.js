'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction()
    try {
      await queryInterface.createTable('user_groupe_chatgpts', {
        id: {
          allowNull: false,
          type: Sequelize.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        chatGptName:{
          type:Sequelize.STRING(800),
          allowNull: true
        },
        userId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'users',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        groupeId:{
          type:Sequelize.INTEGER,
          references:{
            model:'groupes',
            key:'id'
          },
          onUpdate:'CASCADE',
          onDelete:'CASCADE',
        },
        chatGptId: {
          type: Sequelize.INTEGER,
          allowNull:false,
          references: {
            model: 'configs',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
        updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
      },{transaction})

      await queryInterface.addIndex(
        'user_groupe_chatgpts',
        ['userId', 'chatGptId'],
        {
          unique: true,
          transaction,
          name: 'unique_user_chatgpt'
        }
      );

      await queryInterface.addIndex(
        'user_groupe_chatgpts',
        ['groupeId', 'chatGptId'],
        {
          unique: true,
          transaction,
          name: 'unique_groupe_chatgpt'
        }
      );


      await transaction.commit()
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  },

  async down (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeIndex('user_groupe_chatgpts', 'unique_user_chatgpt', { transaction });
      await queryInterface.removeIndex('user_groupe_chatgpts', 'unique_groupe_chatgpt', { transaction });
      await queryInterface.dropTable('user_groupe_chatgpts',{transaction});
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  }
};
