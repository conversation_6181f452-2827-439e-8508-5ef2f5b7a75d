const TABLENAME = 'challenge_conditions';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        TABLENAME,
        'contentType', {
          type: Sequelize.STRING,
          defaultValue: null,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        TABLENAME,
        'settings', {
          type: Sequelize.JSON,
          defaultValue: null,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        TABLENAME,
        'successConditions', {
          type: Sequelize.JSON,
          defaultValue: null,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'formation_element',
        'challengeId', {
          type: Sequelize.INTEGER,
          defaultValue: null,
          references: {
            model: 'challenges', // name of Target model
            key: 'id', // key in Target model that we're referencing
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(TABLENAME, 'contentType', { transaction });
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
