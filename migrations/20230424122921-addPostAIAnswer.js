'use strict';
const TABLENAME = 'posts';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        TABLENAME,
        'state', {
          type: Sequelize.STRING,
          defaultValue: null,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        TABLENAME,
        'userIdForAiFeedback', {
          type: Sequelize.INTEGER,
          defaultValue: null,
          references: {
            model: 'users',
            key: 'id',
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          }
        },
        { transaction },
      );
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(TABLENAME, 'state', { transaction });
      await queryInterface.removeColumn(TABLENAME, 'userForAiFeedback', { transaction });
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
