'use strict';

const {ConfigUpgrades} = require("../src/graph-api/config/config-upgrades");
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await ConfigUpgrades.createDefaultNewChatGPTIntegrationIfAny();
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
