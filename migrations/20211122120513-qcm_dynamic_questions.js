'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([

      queryInterface.createTable('questions_qcm', {
        qcmId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'qcm',
            key: 'id_qcm',
          },
        },
        questionId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'questions',
            key: 'id_question',
          },
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
      }),

      queryInterface.createTable('questions_parents', {
        questionId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'questions',
            key: 'id_question',
          },
        },
        parentQuestionId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'questions',
            key: 'id_question',
          },
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
      }),

      // NEW TYPES QCM
      queryInterface.addColumn(
        'qcm',
        'hasExternalQuestions', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
      ),
      queryInterface.addColumn(
        'qcm',
        'questionPickingStrategy', {
          type: Sequelize.STRING,
          defaultValue: 'normal',
        },
      ),


    ])
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
        queryInterface.dropTable('questions_qcm'),
        queryInterface.dropTable('questions_parents'),
      ],
    )
  },
}
