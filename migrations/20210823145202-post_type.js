'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'posts',
        'postTypeId',
        {
          type: Sequelize.INTEGER,
          references: {
            model: 'post_type', // name of Target model
            key: 'id', // key in Target model that we're referencing
          },
        },
      ),
    ])
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
