'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'uecategories', // table name
        'isVisible', // new field name
        {
          type: Sequelize.BOOLEAN,
          defaultValue: true
        },
      )])
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.removeColumn('uecategories', 'isVisible')
      ])
  }
};
