'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const indexes = await queryInterface.showIndex('formation_element');
    const indexesNames = indexes.map(index => index.name);
    if(!indexesNames.find(i => i === 'formation_element_order')) {
      console.log('adding index');
      await queryInterface.addIndex('formation_element', ['order']);
    }


    const indexes2 = await queryInterface.showIndex('qcm');
    const indexesNames2 = indexes2.map(index => index.name);
    if(!indexesNames2.find(i => i === 'qcm_is_published')) {
      //await queryInterface.addIndex('qcm', ['isPublished']);
      await queryInterface.sequelize.query("create index qcm_is_published on qcm (isPublished);");
      console.log('adding index isPublished');
    }
    if(!indexesNames2.find(i => i === 'qcm_annee')) {
      console.log('adding index annee');
      //await queryInterface.addIndex('qcm', ['annee']);
      await queryInterface.sequelize.query("create index qcm_annee on qcm (annee);");
    }
    /*
    //  BLOB/TEXT column 'pseudo_createur' used in key specification without a key length
    */
    if(!indexesNames2.find(i => i === 'qcm_pseudo_createur')) {
      console.log('adding index pseudo createur');
      // TODO changer en STRING ou varchar la colonne
      //await queryInterface.sequelize.query("create index qcm_pseudo_createur on qcm (pseudo_createur);");
    }

    if(!indexesNames2.find(i => i === 'qcm_date_creation')) {
      console.log('adding index datecreation qcm');
      //await queryInterface.addIndex('qcm', ['date_creation']);
      await queryInterface.sequelize.query("create index qcm_date_creation on qcm (date_creation);");
    }


    const indexes3 = await queryInterface.showIndex('ues');
    const indexesNames3 = indexes3.map(index => index.name);
    if(!indexesNames3.find(i => i === 'ues_order')) {
      console.log('adding index order ue');
      await queryInterface.addIndex('ues', ['order']);
    }
    if(!indexesNames3.find(i => i === 'ues_type')) {
      console.log('adding index');
      await queryInterface.addIndex('ues', ['type']);
    }
    if(!indexesNames3.find(i => i === 'ues_is_visible')) {
      console.log('adding index');
      await queryInterface.addIndex('ues', ['isVisible']);
    }
    if(!indexesNames3.find(i => i === 'ues_is_folder')) {
      console.log('adding index');
      await queryInterface.addIndex('ues', ['isFolder']);
    }


    const indexes4 = await queryInterface.showIndex('users');
    const indexesNames4 = indexes4.map(index => index.name);
    if(!indexesNames4.find(i => i === 'users_is_active')) {
      console.log('adding index');
      await queryInterface.addIndex('users', ['isActive']);
    }
    if(!indexesNames4.find(i => i === 'users_role')) {
      console.log('adding index');
      await queryInterface.addIndex('users', ['role']);
    }
    if(!indexesNames4.find(i => i === 'users_last_activity_at')) {
      console.log('adding index');
      await queryInterface.addIndex('users', ['lastActivityAt']);
    }
    if(!indexesNames4.find(i => i === 'users_has_accepted_cgu')) {
      console.log('adding index');
      await queryInterface.addIndex('users', ['hasAcceptedCGU']);
    }
    if(!indexesNames4.find(i => i === 'users_version_accepted_cgu')) {
      console.log('adding index');
      await queryInterface.addIndex('users', ['versionAcceptedCGU']);
    }


    const indexes5 = await queryInterface.showIndex('uecategories');
    const indexesNames5 = indexes5.map(index => index.name);
    if(!indexesNames5.find(i => i === 'uecategories_order')) {
      console.log('adding index');
      await queryInterface.addIndex('uecategories', ['order']);
    }
    if(!indexesNames5.find(i => i === 'uecategories_is_visible')) {
      console.log('adding index');
      await queryInterface.addIndex('uecategories', ['isVisible']);
    }


    const indexes6 = await queryInterface.showIndex('groupes');
    const indexesNames6 = indexes6.map(index => index.name);
    if(!indexesNames6.find(i => i === 'groupes_is_individual')) {
      console.log('adding index');
      await queryInterface.addIndex('groupes', ['isIndividual']);
    }

    const indexes7 = await queryInterface.showIndex('cours');
    const indexesNames7 = indexes7.map(index => index.name);
    if(!indexesNames7.find(i => i === 'cours_deleted')) {
      console.log('adding index');
      await queryInterface.addIndex('cours', ['deleted']);
    }

    const indexes8 = await queryInterface.showIndex('questions');
    const indexesNames8 = indexes8.map(index => index.name);
    if(!indexesNames8.find(i => i === 'questions_is_published')) {
      console.log('adding index');
      await queryInterface.addIndex('questions', ['isPublished']);
    }


    const indexes9 = await queryInterface.showIndex('questions_qcm');
    const indexesNames9 = indexes9.map(index => index.name);
    if(!indexesNames9.find(i => i === 'questions_qcm_order')) {
      console.log('adding index');
      await queryInterface.addIndex('questions_qcm', ['order']);
    }

  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
