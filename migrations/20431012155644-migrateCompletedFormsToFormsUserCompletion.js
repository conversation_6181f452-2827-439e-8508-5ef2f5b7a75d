'use strict';

const { ConfigUpgrades } = require('../src/graph-api/config/config-upgrades.js');
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await ConfigUpgrades.addMissingFormUserCompletions();
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
