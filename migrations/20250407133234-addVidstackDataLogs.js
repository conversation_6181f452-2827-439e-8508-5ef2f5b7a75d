'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('vidstackLogs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },

      componantId: {
        type: Sequelize.STRING,
        allowNull:false,
      },

      inputType:{
        type:Sequelize.STRING,
        allowNull:false,
      },

      s3FileName:{
        type:Sequelize.STRING,
      },

      url:{
        type:Sequelize.STRING
      },

      volume: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },

      playedSegmentIndex: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },

      playedSegmentRaw: {
        type: Sequelize.JSON,
        allowNull: false,
      },

      videoCurrentTimeAtLog: {
        type: Sequelize.FLOAT,
      },

      ended: {
        type: Sequelize.BOOLEAN,
        allowNull:false,
      },

      paused: {
        type: Sequelize.BOOLEAN,
        allowNull:false,
      },

      pictureInPicture: {
        type: Sequelize.BOOLEAN,
        allowNull:false,
      },

      playbackRate: {
        type: Sequelize.FLOAT,
        allowNull:false,
      },

      playing: {
        type: Sequelize.BOOLEAN,
        allowNull:false,
      },

      userId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },

      formationElementId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'formation_element',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },

      actionName:{
        type:Sequelize.STRING,
        allowNull:false,
      },

      createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
      updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('vidstackLogs');
  },
};
