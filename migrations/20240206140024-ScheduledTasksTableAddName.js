'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // add name (string) to scheduled_tasks table
    await queryInterface.addColumn('scheduled_tasks', 'name', {
      type: Sequelize.STRING,
      allowNull: true
    })

  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
