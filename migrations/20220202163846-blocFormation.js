'use strict';

const TABLENAME = 'formation_block'
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable(TABLENAME, {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.TEXT('long')
      },
      // Block TYPE (Double, Triple, etc)
      type: {
        type: Sequelize.STRING,
        defaultValue: 'single'
      },

      order: {
        type: Sequelize.INTEGER,
        defaultValue: null,
      },

      // parent STEP ID
      formationStepId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'formation_step',
          key: 'id',
        },
        defaultValue: null,
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },

      // CreatorId
      authorId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
      updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable(TABLENAME);
  }
};