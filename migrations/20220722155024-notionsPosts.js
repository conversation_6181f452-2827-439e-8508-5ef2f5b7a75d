'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Notions Questions ANSWERS many to many
    return Promise.all([
      queryInterface.createTable('notions_posts', {
        notionId: {
          type: Sequelize.DataTypes.INTEGER(11),
          references: {
            model: 'notions',
            key: 'id',
          },
        },
        postId: {
          type: Sequelize.DataTypes.INTEGER(11),
          references: {
            model: 'posts',
            key: 'id',
          },
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DataTypes.DATE(6),
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DataTypes.DATE(6),
          defaultValue: Sequelize.NOW,
        },
      })])
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
        queryInterface.dropTable('notions_posts'),
      ],
    )
  },
}
