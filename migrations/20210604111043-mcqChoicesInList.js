'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'questions',
        'isAnswerUniqueChoiceInList',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
      ),
      queryInterface.addColumn(
        'questions',
        'isAnswerMultipleChoiceInList',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
      ),
    ])
  },


  down: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.removeColumn(
        'questions',
        'isAnswerUniqueChoiceInList',
      ),
      queryInterface.removeColumn(
        'questions',
        'isAnswerMultipleChoiceInList',
      ),
    ])
  },
}
