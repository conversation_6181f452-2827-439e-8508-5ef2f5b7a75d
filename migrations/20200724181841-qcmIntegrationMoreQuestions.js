'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'qcm',
        'timer_delay',
        {
          type: Sequelize.INTEGER(11),
          allowNull: true,
          defaultValue: '90'
        },
      ),
      // Questions
      queryInterface.addColumn(
        'questions',
        'reponse_F',
        {
          type: Sequelize.STRING(800),
          allowNull: true,
        },
      ),
      queryInterface.addColumn(
        'questions',
        'reponse_G',
        {
          type: Sequelize.STRING(800),
          allowNull: true,
        },
      ),
      queryInterface.addColumn(
        'questions',
        'F',
        {
          type: Sequelize.INTEGER(1),
          allowNull: true,
        },
      ),
      queryInterface.addColumn(
        'questions',
        'G',
        {
          type: Sequelize.INTEGER(1),
          allowNull: true,
        },
      ),
      queryInterface.addColumn(
        'questions',
        'explication_F',
        {
          type: Sequelize.STRING(600),
          allowNull: true,
        },
      ),
      queryInterface.addColumn(
        'questions',
        'explication_G',
        {
          type: Sequelize.STRING(600),
          allowNull: true,
        },
      ),
      queryInterface.addColumn(
        'statistiques_questions',
        'F_eleve',
        {
          type: Sequelize.INTEGER(4),
          allowNull: true,
        },
      ),
      queryInterface.addColumn(
        'statistiques_questions',
        'G_eleve',
        {
          type: Sequelize.INTEGER(4),
          allowNull: true,
        },
      ),
    ])
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.removeColumn(
        'qcm',
        'timer_delay',
      ),
      // Questions
      queryInterface.removeColumn(
        'questions',
        'reponse_F',
      ),
      queryInterface.removeColumn(
        'questions',
        'reponse_G',
      ),
      queryInterface.removeColumn(
        'questions',
        'F',
      ),
      queryInterface.removeColumn(
        'questions',
        'G',
      ),
      queryInterface.removeColumn(
        'questions',
        'explication_F',
      ),
      queryInterface.removeColumn(
        'questions',
        'explication_G',
      ),
      queryInterface.removeColumn(
        'statistiques_questions',
        'F_eleve',
      ),
      queryInterface.removeColumn(
        'statistiques_questions',
        'G_eleve',
      ),
    ])
  },
}
