const { FolderMigrateService } = require('../src/graph-api/folder/folder-migrate-service.js');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
      await FolderMigrateService.migrateAllToTypedRecursiveFoldersSystem();
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
