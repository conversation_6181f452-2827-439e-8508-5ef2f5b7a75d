'use strict';

const { CoursUpdateService } = require('../src/graph-api/cours/cours-update-service.js');
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await CoursUpdateService.transformAllCoursLegacyResourcesToElements();
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
