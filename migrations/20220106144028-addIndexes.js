'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      true,
      /*
      queryInterface.addIndex('ues', ['isVisible']),
      queryInterface.addIndex('uecategories', ['order', 'isVisible']),

      queryInterface.addIndex('cours', ['isVisible', 'order']),
      queryInterface.addIndex('groupes', ['role']),

      queryInterface.addIndex('qcm', ['annale', 'isPublished', 'annee']),

      queryInterface.addIndex('fiches', ['isAccessible']),

      queryInterface.addIndex('notifications', ['objectId', 'parentId', 'seen']),

      //queryInterface.addIndex('notions', ['name']),

      queryInterface.addIndex('notions_cours', ['autoAdded']),

      queryInterface.addIndex('questions', ['order']),

      queryInterface.addIndex('users', ['isActive', 'role']),
      */
    ])

  },
  down: (queryInterface, Sequelize) => {
    return true
  },
}
