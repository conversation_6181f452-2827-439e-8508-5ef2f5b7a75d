'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.addColumn('question_answers', 'isHorsConcours', {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        }, { transaction: t }),
      ]);
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.removeColumn('question_answers', 'isHorsConcours', { transaction: t }),
      ]);
    });
  }
};