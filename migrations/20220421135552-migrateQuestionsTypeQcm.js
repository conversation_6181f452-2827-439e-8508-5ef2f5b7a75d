'use strict';

const { QcmUpdateService } = require('../src/graph-api/qcm/qcm-update-service.js');
module.exports = {
  up: async (queryInterface, Sequelize) => {
    return await QcmUpdateService.setDefaultQuestionTypeQcmFromParentQcm();
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
