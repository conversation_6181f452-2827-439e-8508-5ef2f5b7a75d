'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.createTable('notions', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        name: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
      }),

      // Notions cours many to many
      queryInterface.createTable('notions_cours', {
        notionId: {
          type: Sequelize.DataTypes.INTEGER(11),
          primaryKey: true,
        },
        coursId: {
          type: Sequelize.DataTypes.INTEGER(11),
          primaryKey: true,
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DataTypes.DATE(6),
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DataTypes.DATE(6),
          defaultValue: Sequelize.NOW,
        },
      }),

      // Notions Questions QCM many to many
      queryInterface.createTable('notions_questions', {
        notionId: {
          type: Sequelize.DataTypes.INTEGER(11),
          primaryKey: true,
        },
        questionId: {
          type: Sequelize.DataTypes.INTEGER(11),
          primaryKey: true,
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DataTypes.DATE(6),
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DataTypes.DATE(6),
          defaultValue: Sequelize.NOW,
        },
      }),
    ])
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
        queryInterface.dropTable('notions'),
        queryInterface.dropTable('notions_cours'),
        queryInterface.dropTable('notions_questions'),
      ],
    )
  },
}
