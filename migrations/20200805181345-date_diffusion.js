'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction()
    try {

      // Table relation date diffusion et groupes
      await queryInterface.createTable('date_diffusion_groups', {
          groupId: { type: Sequelize.INTEGER, primaryKey: true },
          date_diffusion_id: { type: Sequelize.INTEGER, primaryKey: true },
          createdAt: { type: Sequelize.DATE },
          updatedAt: { type: Sequelize.DATE },
        },
        { transaction },
      )

      // DateDiffusion
      await queryInterface.createTable(
        'date_diffusion',
        {
          id: { allowNull: false, autoIncrement: true, primaryKey: true, type: Sequelize.INTEGER },
          date: { type: Sequelize.DATE, defaultValue: null },
          show: { type: Sequelize.BOOLEAN, defaultValue: true },
          courId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'cours', // name of Target model
              key: 'id', // key in Target model that we're referencing
            },
          },
          createdAt: { type: Sequelize.DATE },
          updatedAt: { type: Sequelize.DATE },
        },
        { transaction },
      )
      await transaction.commit()
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction()
    try {
      await queryInterface.dropTable('date_diffusion_groups', { transaction })
      await queryInterface.dropTable('date_diffusion', { transaction })
      await transaction.commit()
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  },
}
