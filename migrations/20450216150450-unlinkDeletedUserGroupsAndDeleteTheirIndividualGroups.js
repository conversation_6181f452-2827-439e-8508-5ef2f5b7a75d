'use strict';

const { UserMigrateService } = require('../src/graph-api/user/user-migrate-service.js');
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await UserMigrateService.unlinkDeletedUserGroupsAndDeleteTheirIndividualGroups();
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
