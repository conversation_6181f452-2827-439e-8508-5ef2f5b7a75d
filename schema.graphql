# This file was generated. Do not edit manually.

schema {
    query: Query
    mutation: Mutation
    subscription: Subscription
}

"Indicates exactly one field must be supplied and this field must not be `null`."
directive @oneOf on INPUT_OBJECT

union BreadcrumbItem = Cours | UE | UECategory

"TimeStamp for a data type for graph point"
type ActiveUserGraphNodeData {
    "la valeur de la metrique"
    count: Int
    "label : description de la métrique"
    label: String
    "La date reconstituée correspondant à l'agrégat de donnée"
    reconstructedDate: Date
}

type ActiveUsersGlobalStats {
    askedQuestions: Int
    exerciseSeriesCompleted: Int
    exercisesDone: Int
    likes: Int
    numberOfSessionsAverage: Float
    privateMessagesSend: Int
    progressAverage: Float
    "total seen classes"
    seenClasses: Int
    timeSpentAverage: Float
    "total seeable classes"
    totalSeeableClasses: Int
    "unique seen classes"
    uniqueSeenClasses: Int
}

type ActiveUsersResult {
    stats: StatsReport
    users: [User]
}

type AdditionalInfos {
    coursId: ID
    coursName: String
    forumName: String
    postCategoryType: String
    postType: String
}

type AdminPaymentResult {
    count: Int
    payments: [Payment]
}

type Annee {
    annee: String
    id: ID
}

"MCQ question answer"
type Answer {
    "Notion auto add"
    autoAddNotions: Boolean
    createdAt: Date
    "Answer explanation"
    explanation: String
    explanation_de: String
    explanation_en: String
    explanation_es: String
    explanation_it: String
    "Answer ID"
    id: ID
    "All points or nothing"
    isAllPointsOrNothing: Boolean
    "Hors concours"
    isHorsConcours: Boolean
    "Is answer true or false"
    isTrue: Boolean
    "Notions of this answer"
    notions: [Notion]
    "Posts"
    posts: [Post]
    "Question parent"
    question: Question
    "Answer text"
    text: String
    text_de: String
    text_en: String
    text_es: String
    text_it: String
    updatedAt: Date
    "Item image"
    url_image: String
    "Explanation Image"
    url_image_explanation: String
}

type ApiKey {
    createdAt: Date
    description: String
    id: ID!
    "The key of the API key"
    key: String
    name: String
    "The permissions of the API key"
    permissions: JSON
    "The status of the API key (active, inactive)"
    status: String
    updatedAt: Date
    "The user id of the API key owner/creator"
    userId: ID
}

type ApprouvedResponse {
    lastResponseFromNonUserOrBot: Post
    lastResponseRole: String
}

type AuthorizedGroupsAndIndividualsGroups {
    individualGroupIds: [ID]!
    multiIndividualGroupIds: [ID]!
}

type BaseRevision {
    cour: Cours
    courId: ID
    createdAt: Date
    dateDebut: Date
    dateDiffusionId: ID
    date_diffusion: DateDiffusion
    id: ID
    updatedAt: Date
    userId: ID
}

type Bill {
    createdAt: Date
    file: String!
    id: ID!
    name: String
    paymentId: ID
    updatedAt: Date
    userId: ID
}

type Building {
    address: String
    city: String
    country: String
    createdAt: Date
    id: ID!
    image: String
    name: String
    postCode: String
    rooms: [Room]
    updatedAt: Date
}

type Calendar {
    "Color for display"
    color: String
    "Count imported dates"
    countDates: Int
    createdAt: Date
    "If no URL defined, file will be"
    file: String
    "Authorized groups"
    groupes: [Groupe]
    id: ID
    name: String
    nonAdmin: Boolean
    showOnTheSide: Boolean
    type: String
    updatedAt: Date
    "iCal URL"
    url: String
    userId: ID
}

type Challenge {
    challengeConditions: [ChallengeCondition]
    createdAt: Date
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    folder: String
    groupsObtained: [Groupe]
    id: ID
    image: String
    isPublished: Boolean
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    rewards: JSON
    types: [TypeQcm]
    updatedAt: Date
}

type ChallengeBadge {
    challengeId: ID
    createdAt: Date
    description: String
    file: String
    id: ID
    image: String
    name: String
    type: String
    updatedAt: Date
}

type ChallengeCondition {
    challengeId: ID
    contentType: String
    coursId: ID
    createdAt: Date
    description: String
    id: ID
    name: String
    qcmId: ID
    settings: JSON
    successConditions: JSON
    type: String
    updatedAt: Date
}

type ChallengeUserProgress {
    challengeConditionId: ID
    challengeId: ID
    coursId: ID
    createdAt: Date
    id: ID
    isFinished: Boolean
    qcmSessionId: ID
    session: QcmSession
    ueCategoryId: ID
    ueId: ID
    updatedAt: Date
    userId: ID
}

"L'objet de sortie de chatGPT"
type ChatGptQueryOutput {
    "Retour d'information supplémentaire, à la demande de la query"
    additionalJson: JSON
    "string qui informe sur ce que chatGPT a modifié si il a modifié qqc"
    explicationString: String
    "mirror de l'id depuis ChatGPTTextObjectInput"
    id: String!
    "Si chatGPT considère qu'il a changé ou pas l'input (ça peut être le text ou le boolean de additionalJson)"
    isChanged: Boolean!
    "le text modifié ou pas de la réponse de chatGPT, peut être null"
    newText: String
    "mirror de l'uniqueId depuis ChatGPTTextObjectInput"
    uniqueId: String!
}

type ClassementQCM {
    Q1: QcmNote
    Q2: QcmNote
    Q3: QcmNote
    monClassement: Int
    notes: [QcmNote]
    notesParEffectif: [NoteParEffectif]
    total: Int
}

type CompanyDescription {
    companyConfigId: ID
    companyName: String
}

type Config {
    domain: String
    id: ID
    key: String
    value: String
}

type Cours {
    "Author user"
    author: User
    "Associated revision date (J0 planning)"
    baseRevision: [BaseRevision]
    countAccessiblesExercises: Int
    createdAt: Date
    customImage: String
    date: Date
    "Class diffusion dates"
    datesDiffusion: [DateDiffusion]
    "softly deleted"
    deleted: Boolean
    "Difficulty between 0 and 5"
    difficulty: Float
    "Class duration"
    duration: Int
    "epub file id"
    epub: String
    "Results summary for this course"
    exercisesResultsSummary(userId: ID): GoodAnswersStatsUserSynthesis
    "Revision sheets (fiches)"
    fiches: [Fiche]
    "Linked files (autres fichiers)"
    files: [File]
    "Cours enrichi"
    formationId: ID
    gptPrecisionPrompt: String
    "Authorized groups"
    groupes: [Groupe]
    id: ID!
    isAnnale: Boolean
    isEnAvant: Boolean
    "Si les review sont visible par tout le monde"
    isFeedbackVisible: Boolean
    "true if client has downloaded last version of pdf"
    isNew: Boolean
    "Si les review ont étées activées"
    isReviewEnabled: Boolean
    "Visibility"
    isVisible: Boolean
    "Last forum post"
    lastPost: Post
    "Layout"
    layout: String
    "Name of course"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    "linked notions (all)"
    notions: [Notion]
    "linked notions (Auto Added)"
    notionsAutoAdded: [Notion]
    "linked notions (Manually Added)"
    notionsManuallyAdded: [Notion]
    "Notions for which user has answered with good/bad answers stats"
    notionsResultsSummary(userId: ID): [GoodAnswersStatsUserSynthesis]
    "Number of comments"
    numberOfComments: Int
    "Number of pages PDF"
    numberOfPagesPdf: Int
    "Number of questions linked to this class"
    numberOfQuestionsLinked: Int
    "Show order"
    order: Int
    "Main document: PDF file id"
    pdf: String
    "pdf previews file id"
    pdfPreviews: [String]
    "Linked questions series"
    qcms: [Qcm]
    settings: JSON
    targetCours: Cours
    "In formations, the targeted course ID for imported courses"
    targetCoursId: ID
    "Description text of course"
    text: String
    text_de: String
    text_en: String
    text_es: String
    text_it: String
    "Tips, free text"
    tips: String
    "Class type"
    type: String
    typeQcmSettings: [CoursTypesQcmSettings]
    "Parent ue (if any)"
    ue: UE
    "Parent category (if any)"
    ueCategory: UECategory
    ueId: ID
    uecategoryId: ID
    "Last update info (to be not used, replaced by updateInformations)"
    updateInfos: String
    updatedAt: Date
    "Useful links"
    usefulLinks: String
    "Version"
    version: String
    "Main document: video file link"
    video: String
    "Videos"
    videos: [File]
    "Number of views"
    views: Int
    "Work time info"
    workTime: String
}

"Course annale training module response for preview"
type CoursAnnalesResponse {
    countUndoneExercises: Int
    cours: Cours
    questionIds: [ID]
    "legacy to remove"
    questions: [Question]
    "Number of exercises found"
    questionsCount: Int
}

type CoursReviewStats {
    mark: Float
    nbReview: Int
}

type CoursSearchResult {
    count: Int
    cours: [Cours]
}

type CoursSupport {
    authorId: ID
    coursId: ID
    createdAt: Date
    id: ID
    name: String
    order: Int
    updatedAt: Date
}

"Cours type QCM settings"
type CoursTypesQcmSettings {
    coursId: ID
    coursModuleType: String
    createdAt: Date
    typeQcm: TypeQcm
    typeQcmId: ID
    updatedAt: Date
}

type CustomField {
    label: String
    order: String
    value: String
}

type CustomLinkForfait {
    createdAt: Date
    forfaits: [Forfait]
    id: ID
    isPublished: Boolean
    link: String
    name: String
    updatedAt: Date
}

type CustomMobileConfig {
    backgroundImage: String
    elements: [FormationElement]
    id: ID
    type: String
}

"Custom planning generator settings"
type CustomPlanning {
    createdAt: Date
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    explanation: String
    explanation_de: String
    explanation_en: String
    explanation_es: String
    explanation_it: String
    "Groups allowed to see this planning generator"
    groupes: [Groupe]
    id: ID
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    "raw steps"
    steps: JSON
    stepsWithEvents: [StepsWithEvents]
    updatedAt: Date
}

type CustomPlanningUser {
    createdAt: Date
    customPlanningId: ID
    groupeId: ID
    updatedAt: Date
}

type DataPath {
    path: String
}

type DateDiffusion {
    allDay: Boolean
    availability: String
    building: Building
    buildingId: ID
    "Calendar object if linked to a calendar"
    calendar: Calendar
    "linked calendar id (if any)"
    calendarId: ID
    cour: Cours
    customPlanningId: ID
    "Custom title (if any, outside ical)"
    customTitle: String
    date: Date
    dateEnd: Date
    event: Event
    eventId: ID
    examSessionId: ID
    exam_session: ExamSession
    groupes: [Groupe]
    hasRecurrence: Boolean
    "all ical data"
    ical_data: JSON
    "title event ical"
    ical_summary: String
    "id ical"
    ical_uid: String
    id: ID
    link: String
    locationString: String
    organizersIds: [ID]
    participantsIds: [ID]
    participantsPresence: [ParticipantPresence]
    presentParticipantsIds: [ID]
    qcm: Qcm
    recurrenceRule: JSON
    recurringEndDate: Date
    recurringPeriod: String
    room: Room
    roomId: ID
    show: Boolean
    timezone: String
    updateInfos: String
}

type DetailedExerciceReport {
    noteAlphaNumeriqueSum: Float
    noteAlphaNumeriqueTheorique: Float
    noteFreeTextSum: Float
    noteFreeTextTheorique: Float
    noteQcmSum: Float
    noteQcmTheorique: Float
    noteQcuSum: Float
    noteQcuTheorique: Float
    totalNote: Float
    totalTheorique: Float
    uniqueAlphaNumerique: Int
    uniqueAlphaNumeriqueDone: Int
    uniqueFreeText: Int
    uniqueFreeTextDone: Int
    uniqueQcm: Int
    uniqueQcmDone: Int
    uniqueQcu: Int
    uniqueQcuDone: Int
    "Nombre de questions uniques"
    uniqueQuestions: Int
    uniqueQuestionsDone: Int
}

type Device {
    createdAt: Date
    deviceName: String
    isVirtual: Boolean
    manufacturer: String
    model: String
    osVersion: String
    platform: String
    updatedAt: Date
    "Used for push notifications"
    uuid: String
}

type DeviceFromUserAgent {
    client: JSON
    device: JSON
    os: JSON
}

type Discussion {
    author: User
    createdAt: Date
    destinataire: User
    id: ID!
    image: String
    isGroup: Boolean
    messages: [Message]
    name: String
    participants: [User]
    unreadCount: Int
    updatedAt: Date
}

"Legacy type, will be removed"
type Edt {
    date: Date
    deuxieme_cours: String
    id: ID!
    premier_cours: String
}

type Event {
    coursIds: [ID]
    createdAt: Date
    "Legacy fields, will be removed. Now in Datediffusion"
    customFields: [CustomField]
    customFields_de: [CustomField]
    customFields_en: [CustomField]
    customFields_es: [CustomField]
    customFields_it: [CustomField]
    datesDiffusion: [DateDiffusion]
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    "If user can see/edit this or not"
    disabled: Boolean
    elements: [FormationElement]
    folderId: ID
    id: ID!
    image: String
    link: String
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    "Legacy field, will be removed. Now in Datediffusion"
    organizers: [ID]
    showDiscussions: Boolean
    types: [Event]
    updatedAt: Date
}

type EventPaginated {
    count: Int
    countEvents: Int
    countFolders: Int
    events: [Event]
    folders: [Folder]
}

type Exam {
    authorId: ID
    color1: String
    color2: String
    createdAt: Date
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    disabled: Boolean
    examScales: [ExamScale]
    examSessions: [ExamSession]
    folderId: ID
    id: ID!
    image: String
    isPublished: Boolean
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    order: Int
    showFinalResults: Boolean
    types: [TypeQcm]
    updatedAt: Date
}

type ExamQuestionSeries {
    authorId: ID
    coefficient: Int
    color1: String
    color2: String
    createdAt: Date
    date_begins: Date
    date_end: Date
    description: String
    examId: ID
    examSessionId: ID
    id: ID!
    image: String
    isAvailable: Boolean
    mcqId: ID
    myStats(sessionId: ID, userId: ID): ExamQuestionSeriesStats
    name: String
    order: Int
    qcm: Qcm
    qcmSession: QcmSession
    settings: JSON
    type: String
    updatedAt: Date
    userResults: [QcmResult]
}

type ExamQuestionSeriesStats {
    classement: Int
    maxGrade: Float
    minGrade: Float
    myGrade: Float
    notes: [NoteResult]
    notesParEffectif: [NoteParEffectif]
    total: Int
}

type ExamScale {
    authorId: ID
    createdAt: Date
    description: String
    examId: ID
    examQuestionSeries: [QuestionSerieWithCoefficient]
    id: ID!
    isDefault: Boolean
    name: String
    updatedAt: Date
}

type ExamSession {
    authorId: ID
    createdAt: Date
    date: Date
    dateEnd: Date
    datesDiffusion: [DateDiffusion]
    description: String
    duration: String
    exam: Exam
    examId: ID
    examQuestionSeries: [ExamQuestionSeries]
    finishedByMe(userId: ID): Boolean
    id: ID!
    isOpen: Boolean
    name: String
    order: Int
    successConditions: JSON
    updatedAt: Date
    "Single session result for user"
    userResult(userId: ID): ExamSessionResult
    "All user results for a specified scale"
    userResults(examScaleId: ID): [ExamSessionResult]
}

type ExamSessionResult {
    createdAt: Date
    examSessionId: ID
    grade: Float
    id: ID
    monClassement: Int
    moyenne: Float
    notes: [NoteResult]
    notesParEffectif: [NoteParEffectif]
    total: Int
    updatedAt: Date
    user: User
    userId: ID
}

type ExerciseModulePreviewResponse {
    allQuestionsCount: Int
    countUndoneExercises: Int
    questionsAnswered: Int
    schemaImagesPreview: [String]
}

type ExoBackConfig {
    config: JSON
    id: ID
    name: String
}

type Fiche {
    createdAt: Date
    file: String
    groups: [Groupe]
    id: ID!
    image: String
    isAccessible: Boolean
    name: String
    updatedAt: Date
}

type File {
    createdAt: Date
    "File external link"
    externalLink: String
    "File URI"
    file: String
    id: ID!
    "Image (unused)"
    image: String
    mimetype: String
    "File name"
    name: String
    "File type - null values are files, video for videos"
    type: String
    updatedAt: Date
}

type Filepath {
    path: String!
}

"Folder (admin)"
type Folder {
    countAccessibleCourses: Int
    countEvents: Int
    countExams: Int
    countForfaits: Int
    countGroups: Int
    countUsers: Int
    createdAt: Date
    id: ID
    name: String
    "Parent folder"
    parentId: ID
    "Type of folder: group, exam, etc."
    type: String
    updatedAt: Date
}

type Forfait {
    canExpire: Boolean
    childs: [Forfait]
    createdAt: Date
    creditCost: Int
    creditGiven: Int
    customElementsIds: [ID]
    daysBeforeExpiration: Int
    defaultChecked: Boolean
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    disabled: Boolean
    emailSettings: JSON
    folderId: ID
    "Donne accès à ces groupes"
    groupes: [Groupe]
    groupesNotified: [Groupe]
    "Has at least one promo code, for public users"
    hasPromoCode: Boolean
    id: ID
    image: String
    isLocked: Boolean
    isPublished: Boolean
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    order: Int
    "parentId Will become legacy"
    parentId: ID
    paymentSettings: JSON
    price: String
    products: JSON
    promoCodes: JSON
    requiredForfaitId: ID
    "Groups required for user in order to be able to see it"
    requiredGroups: [Groupe]
    time: Date
    type: String
    updatedAt: Date
}

type Form {
    backgroundImage: String
    color: String
    completedBy: [FormUserResults]
    completedByMe: Boolean
    createdAt: Date
    elements: [FormationElement]
    id: ID!
    isMandatory: Boolean
    mandatoryGroups: [Groupe]
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    oneAnswerByUser: Boolean
    updatedAt: Date
    uuid: String
}

type FormUserResults {
    createdAt: Date
    state: String
    user: User
}

"Formation type, not used anymore"
type Formation {
    authorId: ID
    color1: String
    color2: String
    cours: Cours
    createdAt: Date
    description: String
    formationSections: [FormationSection]
    groupes: [Groupe]
    id: ID!
    image: String
    isPublished: Boolean
    name: String
    numberOfSections: Int
    numberOfSteps: Int
    numberOfStepsDone: Int
    order: Int
    progress: FormationProgress
    session: FormationSession
    updatedAt: Date
}

type FormationBlock {
    authorId: ID
    coursId: ID
    createdAt: Date
    elements: [FormationElement]
    formationStepId: ID
    id: ID!
    name: String
    order: Int
    settings: JSON
    "The block title type"
    title: Title
    titleId: ID
    "Optional, for summary only"
    titles: [FormationElement]
    type: String
    updatedAt: Date
}

type FormationElement {
    "Element's creator id"
    authorId: ID
    "Element's parent block ID"
    blockId: ID
    "Challenge id"
    challengeId: ID
    "Config ID (for mobile app)"
    configId: ID
    "Correction schema library id elements link (if any)"
    correctionSchemaLibraryId: ID
    "Target Course id in element"
    coursId: ID
    "Course support elements"
    coursSupportId: ID
    createdAt: Date
    "Element description"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    diapoSyntheseElementAfterId: ID
    diapoSyntheseElementBeforeId: ID
    "Do single exercise ID"
    doQuestionId: ID
    "Event elements"
    eventId: ID
    "Question footer elements"
    footerQuestionId: ID
    "Forfait ID"
    forfaitId: ID
    forfaitIds: [ID]
    "Target form id (if type is form)"
    formId: ID
    "Formation step parent"
    formationStepId: ID
    "If element is a global announce, this is the type of global announce"
    globalAnnounceType: String
    gptPrecisionPrompt: String
    "MCQ with element header"
    headerMcqId: ID
    id: ID!
    "Element image"
    image: String
    "If element is accessible by all groups or not"
    isAccessible: Boolean
    "Target MCQ id in element"
    mcqId: ID
    "Element name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    objectId: ID
    "Order among other elements"
    order: Int
    qcmId: ID
    "Question with element header"
    questionId: ID
    "Element's settings"
    settings: JSON
    "Target event id when element represent an event"
    targetEventId: ID
    "Target exam id when element represent an exam"
    targetExamId: ID
    "Element text content, or link"
    text: String
    text_de: String
    text_en: String
    text_es: String
    text_it: String
    "Element's title settings (if type is title)"
    title: Title
    "Element's title ID (if any)"
    titleId: ID
    "Element type"
    type: String
    updatedAt: Date
    "If element is an input for a user property, this is the parent folder user property id"
    userPropertyFolderId: ID
    userPropertyValue(userId: ID): UserPropertyData
    validationSettings: JSON
}

type FormationElementMappingToResolve {
    coursId: ID
    description: String!
    titleId: ID
}

type FormationProgress {
    createdAt: Date
    formationId: ID
    formationStepId: ID
    id: ID
    isActive: Boolean
    isFinished: Boolean
    updatedAt: Date
}

"Formation Section, not used anymore"
type FormationSection {
    authorId: ID
    createdAt: Date
    description: String
    formationElements: [FormationElement]
    formationId: ID
    id: ID!
    image: String
    isPublished: Boolean
    name: String
    order: Int
    "Parent formation data"
    parentFormation: Formation
    "Infinite sub section"
    parentSection: FormationSection
    parentSectionId: ID
    steps: [FormationStep]
    updatedAt: Date
}

type FormationSession {
    createdAt: Date
    currentStepId: ID
    formationId: ID
    id: ID
    isActive: Boolean
    isFinished: Boolean
    updatedAt: Date
}

"Formation Step"
type FormationStep {
    authorId: ID
    createdAt: Date
    description: String
    icon: String
    id: ID!
    image: String
    isPublished: Boolean
    name: String
    order: Int
    progress: FormationProgress
    sectionId: ID
    updatedAt: Date
}

type Forum {
    createdAt: Date
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    id: ID
    image: String
    isPinned: Boolean
    lastPost: Post
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    parentId: ID
    posts: [Post]
    postsNumber: Int
    tag: String
    updatedAt: Date
    views: Int
}

type ForumCategory {
    color: String
    color2: String
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    forums: [Forum]
    groupes: [Groupe]
    id: ID
    image: String
    isPinned: Boolean
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    parent: Forum
    tag: String
    views: Int
}

type ForumResponse {
    categories: [ForumCategory]
    parents: [Forum]
}

type FullAnalyseReport {
    "Description de la structure"
    description: String
    "Id de la structure"
    id: ID
    "image"
    image: String
    "is a folder or not"
    isFolder: Boolean
    "Nom de la structure"
    name: String
    "Resultats de la structure au cours du temps"
    structureProgress: [ProgressNode]
    "Résultat moyen de la structure"
    structureResume: ProgressNode
    "Type de la structure (ue,cours,category)"
    type: String
    "Résultats de l'user pour la structure au cours du temps"
    userProgress: [ProgressNode]
    "Résultat moyen de l'user au cours du temps"
    userResume: ProgressNode
}

type GPTConfig {
    "exoteach user Id"
    aiUserId: ID
    "les Ids des comapnies associées au bot"
    companyId: [ID]
    frequency_penalty: Int
    "integration config id"
    integrationId: ID
    logit_bias: Int
    max_tokens: Int
    model: String
    presence_penalty: Int
    temperature: Float
    templates: JSON
    top_p: Float
    "chat gpt complemental user name"
    user: String
}

type GPTConfigResponse {
    createdAt: Date
    domain: String
    id: ID
    key: String
    updatedAt: Date
    value: GPTConfig
}

type GPTQcm {
    enabled: Boolean
    frequency_penalty: Int
    id: ID
    logit_bias: Int
    max_tokens: Int
    model: String
    "nom du model"
    name: String
    presence_penalty: Int
    "config de l'API"
    temperature: Float
    top_p: Float
}

type GPTQcmConfig {
    authorizedGroupes: [Groupe]!
    authorizedUsers: [User]!
    id: ID!
    integrationId: ID!
    integrationName: String
    models: [GPTQcm]
    name: String
    type: String
}

type GlobalAnnounce {
    createdAt: Date
    groups: [Groupe]
    id: ID
    text: String
    text_de: String
    text_en: String
    text_es: String
    text_it: String
    updatedAt: Date
}

"Used for user's progress"
type GoodAnswersStatsUserSynthesis {
    badAnswers: Int
    coursId: ID
    createdAt: Date
    goodAnswers: Int
    "If true, the stats are being updated, frontend should show a loader"
    isUpdating: Boolean
    notion: Notion
    notionId: ID
    pointsMax: Float
    pointsObtained: Float
    ueCategoryId: ID
    ueId: ID
    updatedAt: Date
    userId: ID
}

"Return data of graph filtration => les données sont très formatées pour être universelles"
type GraphTimeDistributedData {
    activeUsers: [ActiveUserGraphNodeData]
    allSeenClasses: [ActiveUserGraphNodeData]
    downloadedFiles: [ActiveUserGraphNodeData]
    exercisesDone: [ActiveUserGraphNodeData]
    postsSent: [ActiveUserGraphNodeData]
    totalSeeableClasses: Int
    "for each dataType returned, we have timeStamp"
    uniqueSeenClasses: [ActiveUserGraphNodeData]
}

type GroupVisualizer {
    groups: JSON
    groupsWithoutHierarchy: JSON
    superAdmins: [User]
}

type Groupe {
    coursIds: [ID]
    createdAt: Date
    "If user can see/edit this group or not"
    disabled: Boolean
    "ueCategoryIds set as favorite by admins for this group"
    favoriteUECategoryIds: [ID]
    "ueIds set as favorite by admins for this group"
    favoriteUeIds: [ID]
    "Parent folder"
    folder: Folder
    folderId: ID
    id: ID
    image: String
    "Si le groupe est un groupe individuel (un seul groupe individuel par utilisateur) ou non"
    isIndividual: Boolean
    "Nom du groupe"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    "Nombre de cours dont le groupe donne accès"
    numberOfCoursesAccessible: Int
    "Nombre d'utilisateurs dans le groupe"
    numberOfUsers: Int
    "Le groupe peut être responsable de plusieurs groupes"
    responsibleFor: [Groupe]
    role: String
    "Le groupe peut être supervisé par plusieurs groupes"
    supervisedBy: [Groupe]
    typesQcms: [TypeQcm]
    "Allowed ueCategoryIds for this group"
    ueCategoryIds: [ID]
    "Allowed ueIds for this group"
    ueIds: [ID]
    updatedAt: Date
}

type Imagepixie {
    latex: String
    text: String
}

"La structure de donnée que retournera la fonction d'import de QCM"
type ImportMappingToResolve {
    formationElementMappingToResolve: [FormationElementMappingToResolve!]
    newQcmId: Int
    numberExercises: Int
    qcmDescription: String
    qcmMappingToResolve: [QcmMappingToResolve!]
    qcmTitre: String
    questionMappingToResolve: [QuestionMappingToResolve!]
}

type ImportQuestionWithAIResponse {
    aiAnswer: String
    error: String
    inputPrompt: String
    questions: [Question]
    tokenUsage: String
}

type ImportTest {
    formationElement: FormationElement
    titleId: [ID]
}

type LikeHistory {
    id: ID
    postId: ID
    type: String
    updatedAt: Date
    userId: ID
}

"Fields of LimitRule"
type LimitRule {
    createdAt: Date
    id: ID
    isActive: Boolean
    limitNumber: Int
    timeWindow: Int
    updatedAt: Date
    userId: ID
}

type Log {
    "MCQ answer"
    answerId: ID
    apiKeyId: ID
    cours: Cours
    "Course"
    coursId: ID
    createdAt: Date
    deviceFromUserAgent: DeviceFromUserAgent
    forfaitId: ID
    id: ID
    ip: String
    "Log data (if any)"
    logData: JSON
    "Log type"
    logOperation: String
    post: Post
    "Post / answer id"
    postId: ID
    qcm: Qcm
    "MCQ serie"
    qcmId: ID
    question: Question
    "MCQ question"
    questionId: ID
    "User who did the log"
    reporteeUserId: ID
    updatedAt: Date
    user: User
    "User concerned by the log"
    userId: ID
}

type MathpixConfig {
    id: ID
    key: String
    name: String
}

"le datatype d'une entrée en BDD"
type Mathpixies {
    "l'algorythme de hash"
    algorithm: String
    "L'intégration mathpix associée au mathpixie"
    authorId: ID
    "dateCreation"
    createdAt: Date
    "une description"
    description: String
    "le path vers le pdf"
    file: String
    fileName: String
    "Le hash qui sert d'ID à la table mathpix"
    hash: String
    id: ID
    "lien vers une image de description"
    image: String
    mathpixConfigId: ID
    "l'ID du pdf dans l'api mathpix"
    mathpixFileId: String
    mathpixieAuthor: User
}

type McqScale {
    author: User
    createdAt: Date
    id: ID!
    "Is it default scale"
    isDefault: Boolean
    logs(logOperationFilter: [String], numberOfLastLogs: Int! = 5): [Log]
    "Name of the rule"
    name: String
    "Optional : number of linked exercises"
    numberOfLinkedExercises: Int
    "Points obtained when nothing is checked, if null, ignored"
    pointsObtainedWhenNothingChecked: Float
    "MCQ or SCQ"
    questionType: String
    "The JSON rules"
    rules: JSON
    "Type dynamic or manual"
    type: String
    "UEs associated to this scale"
    ues: [UE]
    updatedAt: Date
}

type McqScaleDescription {
    count: Int
    defaultName: String
    questionType: String
}

type Message {
    createdAt: Date!
    discussionId: ID
    file: File
    fileImage: File
    "Multi fichiers"
    fileList: [File]
    id: ID!
    isDeleted: Boolean
    likes: Int
    read: Boolean
    tag: String
    text: String!
    updatedAt: Date
    user: User!
    userId: ID
}

type MessageConnection {
    edges: [Message]
    pageInfo: PageInfo!
}

type ModuleQuickAccess {
    challengeId: ID
    challenges: [Challenge]
    coursId: ID
    createdAt: Date
    description: String
    id: ID!
    name: String
    settings: JSON
    type: String
    types: [TypeQcm]
    ueCategoryId: ID
    ueId: ID
    updatedAt: Date
}

type Mutation {
    _: Boolean
    "Mutation to accept current CGU, return true  if transaction was good, else false"
    acceptCgu: Boolean
    "Activate with words"
    activateAssessmentWithBook(answer: String, page: String): Boolean
    addChildToForfait(childId: ID!, forfaitId: ID!): Boolean
    addCoursToEvent(coursId: ID!, eventId: ID!): Boolean
    "Add coursId to exercise"
    addCoursToQuestion(coursId: ID!, questionId: ID!): Boolean
    "Add a cours to a schema"
    addCoursToSchema(coursId: ID!, schemaLibraryId: ID!): Boolean
    addDefaultTypeQuestionQcmToQcm(qcmId: ID!, typeQcmId: ID!): TypeQcm!
    addElementToForfait(elementId: ID!, forfaitId: ID!): Boolean
    addElementToForm(elementId: ID!, formId: ID!): Boolean
    addExamScaleToExamQuestionSerie(input: ExamScaleQuestionSerie): Boolean
    addForfaitToCustomLink(customLinkId: ID!, forfaitId: ID!): CustomLinkForfait!
    addForfaitToGroup(forfaitId: ID!, groupId: ID!): Forfait!
    "add group access on a formation element"
    addGroupAccessToFormationElement(formationElementId: ID!, groupId: ID!): Boolean
    addGroupToCustomPlanning(customPlanningId: ID!, groupId: ID!): Boolean
    addGroupToDateDiffusion(dateDiffusionId: ID!, groupId: ID!): DateDiffusion!
    addGroupToExamSession(examSessionId: ID!, groupId: ID!): Boolean
    addGroupToFiche(groupId: ID, id: ID): Boolean
    addGroupToForfaitNotification(forfaitId: ID!, groupId: ID!, trigger: String): Boolean
    addGroupToFormation(formationId: ID!, groupId: ID!): Boolean
    "add group to unlock on a formation element"
    addGroupToFormationElement(formationElementId: ID!, groupId: ID!): Boolean
    "Global announces groups"
    addGroupToGlobalAnnounce(groupId: ID, id: ID): Boolean
    "mutation qui permettent de modifier l'accès d'un groupe aux créditentials mathpix"
    addGroupToMathpixIntegrationId(groupId: ID, mathpixIntegrationId: ID): Boolean
    addGroupToQcmConfig(groupId: ID, qcmConfigId: ID): Boolean
    addGroupToScheduledTask(groupId: ID!, scheduledTaskId: ID!): Boolean
    addGroupUnlockToChallenge(challengeId: ID!, groupId: ID!): Boolean
    addGroupe(folderId: ID, image: Upload, name: String!, role: String): Groupe!
    addGroupeResponsibleForGroup(groupId: ID!, responsibleOfGroupId: ID!): Groupe!
    "Add groupe to calendar"
    addGroupeToCalendar(calendarId: ID!, groupeId: ID!): Boolean
    addGroupeToCours(coursId: ID!, groupId: ID!): Groupe!
    addGroupeToForumCategory(forumId: ID!, groupId: ID!): Groupe!
    addGroupeToTypeQcm(groupId: ID!, typeQcmId: ID!): Groupe!
    addGroupeToUe(groupId: ID!, ueId: ID!): Groupe!
    addGroupeToUeCategory(groupId: ID!, ueCategoryId: ID!): Groupe!
    addLinkQuestionIdWithQcmId(qcmId: ID!, questionId: ID!): Boolean
    "Add a mandatory group to a form"
    addMandatoryGroupToForm(formId: ID!, groupId: ID!): Boolean
    "Add child notion to notionId"
    addNotionChildren(childrenNotionId: ID!, notionId: ID!): Boolean
    "Add parent notion to notionId"
    addNotionParent(notionId: ID!, parentNotionId: ID!): Boolean
    addNotionToCours(autoAdded: Boolean!, coursId: ID!, notionId: ID!): Notion!
    addNotionToQuestion(notionId: ID!, questionId: ID!): Notion!
    addNotionToQuestionAnswer(answerId: ID!, notionId: ID!): Notion!
    "Add one credit after In-App-Purchase"
    addOneCreditIAP(key: String): Boolean
    "Replace the associations for given users / groupsIds "
    addOrDeleteOrReplaceUsersAndGroupsToCompaniesAssociations(groupsIds: [ID]!, targetCompaniesIds: [ID]!, type: String!, usersIds: [ID]!): Boolean
    addOrRemoveGroupsToUsersInGroup(groupId: ID!, targetGroupsIds: [ID], type: String!): Boolean
    addOrRemoveOrReplaceGroupsToGroupsOrUsers(groupsIds: [ID]!, targetGroupsIdsString: [ID], type: String!, usersIds: [ID]!): Boolean
    addOrganizerToDateDiffusion(dateDiffusionId: ID!, userId: ID!): DateDiffusion!
    addParentChild(childId: ID!, parentId: ID!): Boolean
    addParticipantToDateDiffusion(dateDiffusionId: ID!, userId: ID!): DateDiffusion!
    addQcmToCours(coursId: ID!, qcmId: ID!): Cours
    "Add child exercice"
    addQuestionChildren(childrenQuestionId: ID!, questionId: ID!): Boolean
    "Add parent exercise"
    addQuestionParent(parentQuestionId: ID!, questionId: ID!): Boolean
    "Add exercise to exercise serie"
    addQuestionToQcm(qcmId: ID!, questionId: ID!): Boolean
    "Add or remove notions to all Questions and question answers from an array of keywords"
    addRemoveNotionQuestionsFromKeyword(action: String, keywords: [String], notionId: ID): NotionAttributionResult
    addRequiredGroupToForfait(forfaitId: ID!, groupId: ID!): Boolean
    "Add tutor as responsible for subject"
    addTuteurToUe(ueId: ID!, userId: ID!): UE
    addTypeQcmToCoursModule(input: CoursTypesQcmSettingsInput!): Boolean
    addTypeQcmToEvent(eventId: ID!, typeQcmId: ID!): Boolean
    addTypeQcmToExam(examId: ID!, typeQcmId: ID!): Boolean
    addTypeQcmToQcm(qcmId: ID!, typeQcmId: ID!): TypeQcm!
    addTypeQcmToQuestion(questionId: ID!, typeQcmId: ID!): Boolean
    addTypeQcmToSchema(schemaLibraryId: ID!, typeQcmId: ID!): Boolean
    addTypeQuestionToQuestion(questionId: ID!, typeQuestionId: ID!): TypeQuestion!
    addTypeToChallenge(challengeId: ID!, typeQcmId: ID!): Boolean
    addTypeToModuleQuickAccess(moduleQuickAccessId: ID!, typeQcmId: ID!): Boolean
    addUe(ue: UEInput!): UE
    "Add default UE to mcq scale"
    addUeToMcqScale(mcqScaleId: ID!, ueId: ID!): Boolean
    addUserToDiscussion(discussionId: ID!, userId: ID!): Discussion
    addUserToGroup(groupId: ID!, userId: ID!): User!
    addUserToUeNotifications(ueId: ID!, userId: ID): Boolean
    "user asks for deletion"
    askForAccountDeletion: Boolean
    "Block or unblock a user"
    blockOrUnBlockUser(action: String!, userId: ID!): Boolean
    "Mass change classes for questions"
    changeCoursAllQuestion(coursId: ID!, id_qcm: ID!): Boolean
    "Change evaluate certainty all questions"
    changeEvaluateCertaintyAllQuestion(id_qcm: ID!, toggle: Boolean!): Boolean
    "Mass change notions for questions using keyword"
    changeNotionsQuestionByKeyword(keyword: String, notionsIds: [ID], operation: String, shouldAddToLinkedClass: Boolean): Int
    "Change imported qcm question order"
    changeOrderImportedQcm(qcmId: ID, questionId: ID, targetQuestionId: ID): Boolean
    changeQuestionOrder(id: ID!, order: Int!, qcmId: ID!): Boolean
    "Change J0 (begin date) for base revision"
    changerDateDebutBasePlanning(baseRevisionId: ID!, dateDebut: Date!): Boolean
    "Send user answers and analytics for a question in a session"
    correctQuestionInSession(analytics: AnalyticsInput, reponses: QcmReponsesInput, sessionId: ID!): QuestionResult
    "Exercise serie generator: get MCQ result from generator answers"
    corrigerQcmGenerated(questions: JSON, reponses: [QcmReponsesInput]): QcmResult
    "Get exercise serie result"
    corrigerQcmUtilisateur(examSessionId: ID, qcmId: ID!, reponses: [QcmReponsesInput], seconds: Int, sessionId: ID): QcmResult
    "Create year"
    createAnnee(annee: Int): Boolean
    "Create one question answer"
    createAnswer(answer: AnswerInput!): Answer!
    createApiKey(input: ApiKeyInput): ApiKey
    "Create base revision (usually not linked to date diffusion, but cours)"
    createBaseRevision(input: BaseRevisionInput): BaseRevision
    createBuilding(input: BuildingInput!): Building
    "Create calendar all features (admin)"
    createCalendar(calendar: CalendarInput!): Calendar!
    "Challenge CRUD"
    createChallenge(input: ChallengeInput): Challenge
    "Challenge CRUD"
    createChallengeCondition(input: ChallengeConditionInput): ChallengeCondition
    "Create config"
    createConfig(domain: String, file: Upload, key: String!, value: String!): Boolean
    createCours(cours: CoursInput!, disableNotionAnalysis: Boolean): Cours!
    createCoursSupport(input: CoursSupportInput!): CoursSupport!
    createCustomLinkForfait(customForfaitLink: CustomLinkForfaitInput!): CustomLinkForfait!
    createCustomPlanning(customPlanning: CustomPlanningInput!): CustomPlanning!
    "Admin/prof can create date diffusion for different entities"
    createDateDiffusion(dateDiffusion: DateDiffusionInput!): DateDiffusion!
    createDiscussion(input: DiscussionInput): Discussion
    "Create a private discussion"
    createDiscussionWith(userId: ID!): Discussion
    createDuplicatedMcqFromGenerator(questionsIds: [ID], ueId: ID): ID
    createEvent(input: EventInput!): Event
    createExam(input: ExamInput!): Exam
    createExamQuestionSeries(input: ExamQuestionSeriesInput!): ExamQuestionSeries
    createExamScale(input: ExamScaleInput!): ExamScale
    createExamSession(input: ExamSessionInput!): ExamSession
    createExerciseWithAiV2(frontendToken: String, input: CreateOrImport): Boolean
    createFiche(fiche: FicheInput!): Fiche!
    createFile(file: FileInput!): File!
    "User create first password from link with token"
    createFirstPasswordToken(password: String!, token: String!, username: String): Boolean!
    createFolder(folder: FolderInput!): Folder!
    createForfait(forfait: ForfaitInput!): Forfait!
    createForm(form: FormInput!): Boolean
    createFormation(input: FormationInput!): Formation
    createFormationBlock(input: FormationBlockInput!): FormationBlock
    createFormationElement(input: FormationElementInput!): Formation
    createFormationSection(input: FormationSectionInput!): Formation
    createFormationStep(input: FormationStepInput!): Formation
    createForum(forum: ForumInput!): Forum!
    createForumCategory(forumCategory: ForumCategoryInput!): ForumCategory!
    "Create a new free user"
    createFreeUser(id: ID!, token: String): User
    "create AI config"
    createGPTConfig(input: GPTConfigInput!, username: String): Boolean
    "create Ai Qcm Config"
    createGPTQcmConfig(input: GPTQcmConfigInput!): Boolean
    "Global announce for specific groups"
    createGlobalAnnounce(input: GlobalAnnounceInput): Boolean
    createImportedMcqFromGenerator(questionsIds: [ID], ueId: ID): ID
    createMcqScale(mcqScale: McqScaleInput!): McqScale!
    createModuleQuickAccess(input: ModuleQuickAccessInput!): ModuleQuickAccess
    "Create several question answers"
    createMultipleAnswer(answers: [AnswerInput]!): Boolean
    createNotion(notion: NotionInput!): Notion!
    createNotionKeyword(notionKeyword: NotionKeywordInput!): NotionKeyword!
    "Multiples Associations creation or updates"
    createOrAddUsersToCompaniesAssociation(companiesIds: [ID] = [], usersIds: [ID] = []): Boolean
    createOrDeleteWatermarkPictureCours(action: String!, file: Upload, idArray: [ID!]!): String
    "Watermark CRUD Files => Gère la création / deletion des watermarkImage dans la table file, supporte également l'opération en masse"
    createOrDeleteWatermarkPictureElementPdf(action: String!, file: Upload, idArray: [ID!]!): String
    createOrDeleteWatermarkPictureTemplate(action: String!, file: Upload, idArray: [ID!]!): String
    "query qui permet de créer et update un config de modèle générale + créer/update/delete tous les sous-models"
    createOrUpdateQcmConfig(id: ID, input: GPTQcmConfigInput!): Boolean
    "Create if not exist, update if exist. Deleted is like null values because all users have the same properties. For global user properties only"
    createOrUpdateUserPropertyData(input: UserPropertyDataInput): Boolean
    "Create a post"
    createPost(post: PostInput!): Post!
    createPostLimitationRule(limitRuleInput: LimitRuleInput!): LimitRule
    createPostType(postType: PostTypeInput!): PostType!
    createQcm(qcm: QcmInput!): Qcm!
    createQuestion(question: QuestionInput!): Question!
    createReview(review: ReviewInput!): Review!
    createRoom(input: RoomInput!): Room
    createScheduledTask(input: ScheduledTaskInput!): ScheduledTask!
    "Create a new schema"
    createSchema(input: SchemaInput!): Schema!
    "Inscription / Ré-inscription avant paiement"
    createTempUserForfaitSelection(customFields: [CustomFieldsInput], forfaitsIds: [ID], isMobile: Boolean, paymentMethod: String, promoCodes: [PromoCodeInput], userInput: UserInput): TempUserResult
    createTitle(input: TitleInput!): Title
    createTypeQcm(typeQcm: TypeQcmInput!): TypeQcm!
    createTypeQuestion(typeQuestion: TypeQuestionInput!): TypeQuestion!
    createUECategory(category: UECategoryInput!): UE
    createUEModule(input: UEModuleInput!): UEModule
    "Add temporary payment to user to save data"
    createUpdateTemporaryPayment(customFields: [CustomFieldsInput], forfaitsIds: [ID], paymentId: ID, promoCodes: [PromoCodeInput], userInput: UserInput): ID
    "Create a new user - for admins"
    createUser(user: UserInput!): User!
    createUserPropertyFolder(input: UserPropertyFolderInput): Boolean
    createWatermark(watermark: WatermarkInput!): Watermark!
    createWebhook(input: WebhookInput!): Webhook!
    "Delete all users in groups ids"
    deleteAllUsersInGroups(groupIds: [ID]): Boolean
    "Delete year"
    deleteAnnee(id: ID!): Boolean
    deleteAnswer(id: ID!): Boolean!
    deleteApiKey(id: ID!): Boolean
    deleteBaseRevision(id: ID!): Boolean
    deleteBuilding(id: ID!): Boolean
    deleteChallenge(id: ID!): Boolean
    deleteChallengeCondition(id: ID!): Boolean
    "Delete config by id"
    deleteConfigById(id: ID!): Boolean
    deleteConversation(id: ID!): Boolean
    deleteCours(id: ID!): Boolean!
    deleteCoursSupport(id: ID!): Boolean!
    deleteCustomLinkForfait(id: ID!): Boolean!
    deleteCustomPlanning(customPlanningId: ID!): Boolean
    deleteEvent(id: ID!): Boolean
    deleteExam(id: ID!): Boolean
    deleteExamQuestionSeries(id: ID!): Boolean
    deleteExamScale(id: ID!): Boolean
    deleteExamSession(id: ID!): Boolean
    deleteFiche(id: ID!): Boolean!
    deleteFile(id: ID!): Boolean!
    deleteFolder(id: ID!): Boolean!
    deleteForfait(id: ID!): Boolean!
    deleteForm(id: ID!): Boolean
    "Delete all form results for a form and a user"
    deleteFormUserResults(formId: ID!, userId: ID!): Boolean
    deleteFormation(id: ID!): Boolean
    deleteFormationBlock(id: ID!): Boolean
    deleteFormationElement(id: ID!): Boolean
    deleteFormationSection(id: ID!): Boolean
    deleteFormationStep(id: ID!): Boolean
    deleteForum(id: ID!): Boolean!
    deleteForumCategory(id: ID!): Boolean!
    "Delete AI config"
    deleteGPTConfig(id: ID!): Boolean
    deleteGlobalAnnounce(input: GlobalAnnounceInput): Boolean
    deleteGroupe(id: ID!): Boolean!
    deleteMathpixie(mathpixConfigId: ID!, mathpixieId: ID!): Boolean
    deleteMcqScale(id: ID!): Boolean!
    deleteMessage(id: ID!): Boolean!
    deleteModuleQuickAccess(id: ID!): Boolean
    deleteMyNotifications: Boolean
    deleteNotifications: Boolean
    deleteNotion(id: ID!): Boolean!
    deletePost(id: ID!): Boolean!
    deletePostLimitationRule(id: ID!): Boolean
    deletePostType(id: ID!): Boolean!
    deleteQcm(id: ID!): Boolean!
    deleteQcmConfig(id: ID): Boolean
    "Delete exercise serie result"
    deleteQcmResult(id: ID!): Boolean
    deleteQuestion(id: ID!): Boolean!
    "Delete user reported content"
    deleteReportedContent(id: ID): Boolean
    deleteReview(id: ID!): Boolean
    deleteRoom(id: ID!): Boolean
    deleteScheduleTask(id: ID!): Boolean!
    "Delete a schema by id"
    deleteSchema(id: ID!): Boolean
    deleteTitle(id: ID!): Boolean
    deleteTypeQcm(id: ID!, replacementId: ID): Boolean!
    deleteTypeQuestion(id: ID!): Boolean!
    deleteUE(id: ID!): Boolean
    deleteUECategory(id: ID!): Boolean
    deleteUEModule(id: ID!): Boolean
    deleteUser(id: ID!): Boolean!
    deleteUserPropertyFolder(id: ID!): Boolean
    "Delete an association from associationId"
    deleteUsersToCompaniesAssociationFromAssociationIds(associationsIds: [ID]): Boolean
    "Delete an association from userIds and CompaniesIds depending of constraint ('both','company','user'). The constraint inform on how to uses Id to delete"
    deleteUsersToCompaniesAssociationFromUsersAndCompaniesId(companiesIds: [ID] = [], constraint: String = "both", usersIds: [ID] = []): Boolean
    deleteWatermark(id: ID!): Boolean
    deleteWebhook(id: ID!): Boolean!
    disconnectUserFromAllDevices(userId: ID!): Boolean
    "Dislike a post"
    dislikePost(id: ID!): Post!
    "Dislike a post"
    dislikeReview(id: ID!): Review!
    "Duplicate forfait (offer)"
    duplicateForfait(id: ID!, parentId: ID): Forfait!
    duplicateForm(id: ID!): Boolean
    "Duplicate question serie, exercises and exercise items"
    duplicateQcm(duplicate: Boolean, id: ID!): Qcm
    "Duplicate exercise and answers"
    duplicateQuestion(id: ID!, parentQcmId: ID): Question
    "Admin edit calendar"
    editCalendar(calendar: CalendarInput!, calendarId: ID!): Boolean
    editCustomPlanning(customPlanning: CustomPlanningInput!, customPlanningId: ID!): CustomPlanning!
    editDateDiffusion(dateDiffusion: DateDiffusionInput!, dateDiffusionId: ID!): DateDiffusion!
    emitAdminNotification(body: String, groupIdArray: [ID], title: String): Boolean
    emitCourseUpdateNotification(body: String, courseId: ID!, groupIdArray: [ID], route: String, title: String): Boolean
    emitEventNotification(body: String, eventId: ID!, groupIdArray: [ID], route: String, title: String): Boolean
    emitExamNotification(body: String, examSessionId: ID!, groupIdArray: [ID], route: String, title: String): Boolean
    emitPostNotification(body: String, groupIdArray: [ID], postId: ID!, route: String, title: String): Boolean
    emitQCMUpdateNotification(body: String, groupIdArray: [ID], qcmId: ID!, route: String, title: String): Boolean
    eventMassActions(action: String, ids: [ID!]!, input: EventInput): Boolean
    examsMassActions(action: String, ids: [ID!]!, input: ExamInput): Boolean
    exportJsonToXLSDownload(addUUID: Boolean = false, input: JSON, name: String): String
    extractNotionsFromPdf(coursId: ID!): Int
    finishFormationStep(formationId: ID!, formationStepId: ID!): Boolean
    "Force-finish session (useful when time's up')"
    finishSession(sessionId: ID!): Boolean
    "Finish training for session"
    finishTrainingInSession(reponses: [QcmReponsesInput], seconds: Int, sessionId: ID!): Boolean
    forfaitMassActions(action: String, ids: [ID!]!, input: ForfaitInput): Boolean
    "Send forgot password email"
    forgotPassword(email: String!): String!
    "Create a new question serie from selected question serie"
    fusionQcm(ids: [ID]): Qcm
    "Generate AI answer for a post (manual)"
    generateAiAnswerForPost(additionnalPrompt: String, aiUserId: ID, originalThreadPostId: ID, postId: ID!): Boolean
    "Génère exercice auto fill in legends pour entrainement"
    generateAutoFillInLegendsExercise(schemaLibraryId: ID): Question
    generateCustomPlanningForUser(input: UserCustomPlanningInput): [DateDiffusion]
    "Generate session questions annale for cours"
    generateExerciseSessionForCourseModule(coursId: ID, includeAlreadyDone: Boolean, mode: String, module: String, notionId: ID, numberOfExercises: Int, schemaAllOrCustom: String, schemaVariants: [String]): QcmGeneratorResponse
    "exercise serie generator: creates an MCQ from Generator parameters"
    generateQcm(params: GeneratorParams!): QcmGeneratorResponse
    "exercise serie generator advanced: creates an MCQ from Generator parameters with question packs"
    generateQcmFromPacks(params: [GeneratorParams]): QcmGeneratorResponse
    "Generate session from element Diapo synthese"
    generateSessionFromDiapoSyntheseElement(formationElementId: ID!): QcmGeneratorResponse
    "Generate session from element single exercise"
    generateSessionFromExerciseElement(formationElementId: ID!): QcmGeneratorResponse
    "Auto generate all planning revision J0"
    genererMonPlanningRevision: Boolean
    "User gives feedback to AI answer"
    giveFeedbackToAiAnswer(input: FeedbackToAiAnswerInput): Boolean
    groupsMassActions(action: String, ids: [ID!]!, input: GroupeInput): Boolean
    "When user clicks on next module in a formation, this will update the progress"
    handleNextUEModule(currentBlockId: ID, ueId: ID!, ueModuleId: ID!): Boolean
    "Handle user opens, start or resume ue module, retourne id de ue_module_progress_log"
    handleUserDoUEModule(currentBlockId: ID, ueId: ID!, ueModuleId: ID!): UserModuleProgressLogResponse
    "Ignore exerciseInSession"
    ignoreExerciseInSession(questionId: ID!, sessionId: ID!): Boolean
    "Impersonate user"
    impersonateUser(login: String!): TokenWithUser!
    importCSV(file: Upload, qcmId: ID, type: String): Boolean
    importExerciseWithAiV2(frontendToken: String, input: CreateOrImport): Boolean
    "Import question serie from file"
    importFullMcqFromJson(annee: String, file: Upload, typesIdsForImport: [ID], ueId: ID): Qcm
    "Import question serie from XLS (beta)"
    importFullMcqFromXls(file: Upload, id_qcm: ID!): Boolean
    "Import question serie user results from XLS (for Antemed)"
    importMcqUserResultsFromXls(examQuestionSerieId: ID, file: Upload, qcmId: ID!, selectedFileType: String): JSON
    importQuestionWithAI(input: ImportQuestionWithAIInput): ImportQuestionWithAIResponse
    "Import Users from XLS file"
    importUsersFromXls(doImport: Boolean, domain: String, email: EmailUserImportSettings, file: Upload, filename: String, users: JSON): JSON
    "Increment user stat, called when a user visit a course"
    incrementUserStat(objectId: ID, operation: String!): Boolean
    "Like a post"
    likePost(id: ID!): Post!
    "Like a Review"
    likeReview(id: ID!): Review!
    markAllMyNotificationsSeen: Boolean
    massChangesContentTypes(contentType: String!, operation: String, selectedTypesIds: [ID], targetTypesIds: [ID]): Boolean
    massChangesCoursesModules(coursIds: [ID], coursTypesQcmSettings: JSON, settings: JSON): Boolean
    "Change exercise categories"
    massChangesQuestionsCategories(categoryId: ID!, coursId: ID!): String
    "Mass change exercise-course link"
    massChangesQuestionsCours(selectedCoursId: ID!, targetCoursId: ID!): String
    massChangesQuestionsDisplaySettings(changeDisplay: Boolean, displaySetting: DisplaySetting, selectedCoursIds: [ID]): Int
    "Delete exercices by types (superadmin exostaff only)"
    massDeleteExercisesByType(typeQcmIds: [ID]): Int
    "mass delete exercise series by type (superadmin exostaff only)"
    massDeleteSeriesByType(typeQcmIds: [ID]): Int
    "Mass update selected users with selected groups (add or remove). if hasSelectedAll is true, all users within filter will be updated"
    massUpdateUserGroups(filter: UserSearchFilter, groupIds: [ID], hasSelectedAll: Boolean, operation: String, userIds: [ID]): Boolean
    "Mass update user preferences"
    massUpdateUserPreferences(filter: UserSearchFilter, hasSelectedAll: Boolean, preferences: PreferenceInput, userIds: [ID]): Boolean
    mathpixUploadPdf(file: Upload!, mathpixConfigId: ID): Mathpixies
    modifyDefaultMcqScaleForQuestionType(mcqScaleId: ID!, questionType: String!): Boolean
    "Edit single occurrence date"
    modifySingleOccurrenceDate(date: Date, dateDiffusionId: ID!, dateEnd: Date): DateDiffusion
    "Add exception to date diffusion with exceptionDate, and duplicate the date. returns duplicated date (non recurring)"
    modifySingleOccurrenceDateWithDuplication(dateDiffusionId: ID!, exceptionDate: Date): DateDiffusion
    moveFormationElement(direction: String, id: ID!, id2: ID): Boolean
    "MassUpdate des questions (soit depuis les ID Directement, soit depuis les cours), selon un mappingScale qui associe un questionType à un nouveau scale à associer"
    mutationMassChangesScaleForQuestions(mutationMassChangesScaleForQuestionsInput: MassChangesScaleForQuestionsInput!): Int
    payLater(id: ID!): Boolean
    "Pin or unpin a notification"
    pinOrUnpinNotification(id: ID!): Notification
    "User post reaction"
    postReaction(emoji: String!, postId: ID!): Boolean
    "Recalculate question serie stats"
    recalculateQcmStats(qcmId: ID!): Boolean
    regenerateBill(id: ID!): Boolean
    "Fonction qui pour une question générée par AI (isAiGenerated : true) switch le bool 'aiGenerationHasBeenValidated' à false"
    rejectAiGeneration(id: ID!): Boolean
    "Mutation to reject current CGU, return false if transaction was good, else false"
    rejectCgu: Boolean
    "Admin remove calendar"
    removeCalendar(calendarId: ID!): Boolean
    removeChildFromForfait(childId: ID!, forfaitId: ID!): Boolean
    removeCoursFromEvent(coursId: ID!, eventId: ID!): Boolean
    "Remove coursId from exercise"
    removeCoursFromQuestion(coursId: ID!, questionId: ID!): Boolean
    "Remove a cours from a schema"
    removeCoursFromSchema(coursId: ID!, schemaLibraryId: ID!): Boolean
    removeDateDiffusion(dateDiffusionId: ID!): Boolean
    removeDefaultTypeQuestionTypeQcmFromQcm(qcmId: ID!, typeQcmId: ID!): TypeQcm!
    removeElementFromForfait(elementId: ID!, forfaitId: ID!): Boolean
    removeElementFromForm(elementId: ID!, formId: ID!): Boolean
    removeExamScaleFromExamQuestionSerie(input: ExamScaleQuestionSerie): Boolean
    removeForfaitFromCustomLink(customLinkId: ID!, forfaitId: ID!): CustomLinkForfait!
    removeForfaitFromGroup(forfaitId: ID!, groupId: ID!): Forfait!
    "Permet de décommander un job de la génération par AI ( creation from pdf et importation from pdf)"
    removeGptJob(frontendToken: String!): Boolean
    "remove group access on a formation element"
    removeGroupAccessFromFormationElement(formationElementId: ID!, groupId: ID!): Boolean
    removeGroupFromCustomPlanning(customPlanningId: ID!, groupId: ID!): Boolean
    removeGroupFromDateDiffusion(dateDiffusionId: ID!, groupId: ID!): DateDiffusion!
    removeGroupFromExamSession(examSessionId: ID!, groupId: ID!): Boolean
    removeGroupFromFiche(groupId: ID, id: ID): Boolean
    removeGroupFromForfaitNotification(forfaitId: ID!, groupId: ID!, trigger: String): Boolean
    removeGroupFromFormation(formationId: ID!, groupId: ID!): Boolean
    "remove group to unlock on a formation element"
    removeGroupFromFormationElement(formationElementId: ID!, groupId: ID!): Boolean
    removeGroupFromGlobalAnnounce(groupId: ID, id: ID): Boolean
    removeGroupFromScheduledTask(groupId: ID!, scheduledTaskId: ID!): Boolean
    removeGroupToQcmConfig(groupId: ID, qcmConfigId: ID): Boolean
    removeGroupUnlockFromChallenge(challengeId: ID!, groupId: ID!): Boolean
    "Remove groupe from calendar"
    removeGroupeFromCalendar(calendarId: ID!, groupeId: ID!): Boolean
    removeGroupeFromCours(coursId: ID!, groupId: ID!): Groupe!
    removeGroupeFromForumCategory(forumId: ID!, groupId: ID!): Groupe!
    removeGroupeFromTypeQcm(groupId: ID!, typeQcmId: ID!): Groupe!
    removeGroupeFromUe(groupId: ID!, ueId: ID!): Groupe!
    removeGroupeFromUeCategory(groupId: ID!, ueCategoryId: ID!): Groupe!
    removeGroupeResponsibleForGroup(groupId: ID!, responsibleOfGroupId: ID!): Groupe!
    removeGroupeToMathpixIntegrationId(groupId: ID, mathpixIntegrationId: ID): Boolean
    removeLinkQuestionIdWithQcmId(qcmId: ID!, questionId: ID!): Boolean
    removeMandatoryGroupFromForm(formId: ID!, groupId: ID!): Boolean
    removeNotionChildren(childrenNotionId: ID!, notionId: ID!): Boolean
    removeNotionFromCours(autoAdded: Boolean!, coursId: ID!, notionId: ID!): Notion!
    removeNotionFromQuestion(notionId: ID!, questionId: ID!): Notion!
    removeNotionFromQuestionAnswer(answerId: ID!, notionId: ID!): Notion!
    removeNotionKeyword(notionKeywordId: ID!): Boolean!
    removeNotionParent(notionId: ID!, parentNotionId: ID!): Boolean
    removeOrganizerFromDateDiffusion(dateDiffusionId: ID!, userId: ID!): DateDiffusion!
    removeParentChild(childId: ID!, parentId: ID!): Boolean
    removeParticipantFromDateDiffusion(dateDiffusionId: ID!, userId: ID!): DateDiffusion!
    removeQcmFromCours(coursId: ID!, qcmId: ID!): Cours
    removeQuestionChildren(childrenQuestionId: ID!, questionId: ID!): Boolean
    "Remove exercise from exercise serie"
    removeQuestionFromQcm(qcmId: ID!, questionId: ID!): Boolean
    removeQuestionParent(parentQuestionId: ID!, questionId: ID!): Boolean
    removeRequiredGroupFromForfait(forfaitId: ID!, groupId: ID!): Boolean
    "Remove tutor as responsible for subject"
    removeTuteurFromUe(ueId: ID!, userId: ID!): UE
    removeTypeFromChallenge(challengeId: ID!, typeQcmId: ID!): Boolean
    removeTypeFromModuleQuickAccess(moduleQuickAccessId: ID!, typeQcmId: ID!): Boolean
    removeTypeQcmFromCoursModule(input: CoursTypesQcmSettingsInput!): Boolean
    removeTypeQcmFromEvent(eventId: ID!, typeQcmId: ID!): Boolean
    removeTypeQcmFromExam(examId: ID!, typeQcmId: ID!): Boolean
    removeTypeQcmFromQcm(qcmId: ID!, typeQcmId: ID!): TypeQcm!
    removeTypeQcmFromQuestion(questionId: ID!, typeQcmId: ID!): Boolean
    removeTypeQcmFromSchema(schemaLibraryId: ID!, typeQcmId: ID!): Boolean
    removeTypeQuestionFromQuestion(questionId: ID!, typeQuestionId: ID!): TypeQuestion!
    "Remove default UE from mcq scale"
    removeUeFromMcqScale(mcqScaleId: ID!, ueId: ID!): Boolean
    removeUserFromDiscussion(discussionId: ID!, userId: ID!): Discussion
    removeUserFromGroup(groupId: ID!, userId: ID!): User!
    removeUserFromUeNotifications(ueId: ID!, userId: ID): Boolean
    "Replace the associations for given users (remove associations for the users, then relink them"
    replaceUsersToCompaniesAssociationFromUsersAndCompaniesId(companiesIds: [ID] = [], usersIds: [ID] = []): Boolean
    "Report a post or a user"
    reportPost(input: ReportPostInput): Boolean
    reportedContentAction(action: String!, logId: ID!): Log
    "First time setup password"
    resetPasswordWithToken(password: String!, token: String!): Boolean!
    restoreDeletedUser(id: ID!): Boolean!
    saveQcmState(input: SaveQcmInput!): Boolean
    "Save time spent on module, global and for specific progress log"
    saveTimeSpentOnModule(currentBlockId: ID, seconds: Int, ueModuleId: ID!, ueModuleProgressLogId: ID): Boolean
    sendInputElementsForm(input: [UserPropertyDataInput]): Boolean
    sendMessage(message: MessageInput!): Message
    "Change section (only if mcq section option is activated)"
    sessionSectionChange(sessionId: ID!): Boolean
    "Admin/Professor set AI answer as verified/unverified by him"
    setAiAnswerVerified(postId: ID!): Boolean
    "Set form as fully completed or not, for a user. If no userId provided, use current user"
    setFormCompleted(finished: Boolean!, formId: ID!, parentElementId: ID, userId: ID): Boolean
    setMyNotificationSeenForDiscussion(discussionId: ID!): Boolean
    setNotificationSeen(id: ID!): Notification
    setNotificationTokenForUser(deviceName: String, isVirtual: Boolean, manufacturer: String, model: String, osVersion: String, platform: String, token: String!, uuid: String): Boolean
    "Set answer notions from keywords in text"
    setNotionsFromTextWithKeywords(answerId: ID!, input: String): Boolean
    setNotionsQuestionAnswer(answerId: ID!, notionsIds: [ID]!): Boolean
    "Set question notions from keywords in text"
    setNotionsQuestionFromTextWithKeywords(input: String, questionId: ID!): Boolean
    setParticipantPresence(dateDiffusionId: ID!, isPresent: Boolean, userId: ID!): Boolean
    "User's Background infos"
    setUserBackground(input: JSON!): Boolean
    "Sign in with login and password and get Token"
    signIn(login: String!, password: String!): Token!
    "Sign in with login, password, and recaptcha discret v2 and get User infos and token"
    signInWithUser(login: String!, password: String!, recaptchaToken: String): TokenWithUser!
    startOrResumeFormationSession(formationId: ID!): FormationSession
    "Launch or resume session for specific exercise, challenge"
    startOrResumeMcqSession(challengeId: ID, formationElementId: ID, id_qcm: ID, includeAlreadyDone: Boolean, quickAccessModuleId: ID): QcmSession
    "Launch or resume session for specific exam question serie"
    startOrResumeMcqSessionExam(examQuestionSerieId: ID!, formationElementId: ID): QcmSession
    "Sync now"
    syncCalendar(calendarId: ID!): Boolean
    syncPaymentIdToHubspot(paymentId: ID!): Boolean
    testImportMapping(doImport: Boolean, file: Upload, resolvedMapping: ImportQcmResolvedMapping): ImportMappingToResolve!
    "Update question serie grades"
    updateAllGradesForMcq(id: ID): Boolean
    "Global announce for all groups"
    updateAnnonceGenerale(value: String): Config
    updateAnswer(answer: AnswerInput!, id: ID!): Boolean
    updateApiKey(id: ID!, input: ApiKeyInput): ApiKey
    "Update image of UE, action can be 'upload' or 'delete'"
    updateBannerImageUE(action: String, file: Upload, ueId: ID): String
    updateBuilding(id: ID!, input: BuildingInput!): Boolean
    updateChallenge(id: ID!, input: ChallengeInput): Boolean
    updateChallengeCondition(id: ID!, input: ChallengeConditionInput): Boolean
    "Auto-update config, creates if not exists. Value can be null, to disable some logo for example"
    updateConfig(domain: String, file: Upload, key: String!, value: String): Config
    "Update config by id, does not create if not exists"
    updateConfigById(domain: String, file: Upload, id: ID!, value: String): Config
    updateCours(cours: CoursInput!, disableNotionAnalysis: Boolean, id: ID!): Boolean
    "Replace coursIdArray for exercise"
    updateCoursFromQuestion(coursIdArray: [ID]!, questionId: ID!): Boolean
    updateCoursSupport(id: ID!, input: CoursSupportInput!): Boolean
    updateCustomLinkForfait(customForfaitLink: CustomLinkForfaitInput!, id: ID!): Boolean
    updateDiscussion(id: ID!, input: DiscussionInput): Discussion
    updateEvent(id: ID!, input: EventInput!): Boolean
    updateExam(id: ID!, input: ExamInput!): Boolean
    updateExamQuestionSeries(id: ID!, input: ExamQuestionSeriesInput!): Boolean
    updateExamScale(id: ID!, input: ExamScaleInput!): Boolean
    updateExamScaleExamQuestionSerie(input: ExamScaleQuestionSerie): Boolean
    updateExamSession(id: ID!, input: ExamSessionInput!): Boolean
    updateFiche(fiche: FicheInput!, id: ID!): Boolean
    updateFile(file: FileInput!, id: ID!): Boolean
    updateFolder(folder: FolderInput!, id: ID!): Boolean
    updateForfait(forfait: ForfaitInput!, id: ID!): Boolean
    updateForm(form: FormInput!, id: ID!): Boolean
    updateFormation(id: ID!, input: FormationInput!): Boolean
    updateFormationBlock(id: ID!, input: FormationBlockInput!): Boolean
    updateFormationElement(id: ID!, input: FormationElementInput!): Boolean
    updateFormationProgression(formationId: ID!, formationStepId: ID!): Boolean
    updateFormationSection(id: ID!, input: FormationSectionInput!): Boolean
    updateFormationStep(id: ID!, input: FormationStepInput!): Boolean
    updateForum(forum: ForumInput!, id: ID!): Boolean
    updateForumCategory(forumCategory: ForumCategoryInput!, id: ID!): Boolean
    "Update AI config"
    updateGPTConfig(id: ID!, input: GPTConfigInput!): Boolean
    updateGlobalAnnounce(input: GlobalAnnounceInput): Boolean
    "Update group permissions for a group, allowing to set allowed ueIds, ueCategoryIds and coursIds"
    updateGroupPermissions(coursIds: [ID], groupId: ID!, ueCategoryIds: [ID], ueIds: [ID]): Boolean
    updateGroupe(folderId: ID, id: ID!, image: Upload, name: String!, role: String): Groupe!
    "Update or delete image of a question"
    updateImageQcm(action: String, file: Upload, id: ID, type: String): String
    "Update cours from Schema"
    updateLinkedCoursesSchema(coursIdArray: [ID]!, schemaId: ID!): Boolean
    updateMcqScale(id: ID!, mcqScale: McqScaleInput!): Boolean
    updateModuleQuickAccess(id: ID!, input: ModuleQuickAccessInput!): Boolean
    "Update user avatar"
    updateMyAvatar(file: Upload!): DataPath!
    "Update user avatar from list of avatars (setup in admin appearance)"
    updateMyAvatarFromList(filename: String!): Boolean
    "Update question serie good answers synthesis manually"
    updateMyGoodAnswersSynthesis: Boolean
    updateMyProfile(addressline1: String, addressline2: String, appearsInSubjects: Boolean, appearsInTeam: Boolean, bio: String, city: String, email: String, isReachableByPrivateMessage: Boolean, lang: String, name: String, phone: String, postcode: String, username: String): User!
    updateNotion(id: ID!, notion: NotionInput!): Boolean
    updateNotionKeyword(id: ID!, notionKeyword: NotionKeywordInput!): Boolean!
    updatePassword(newPassword: String, oldPassword: String!): User!
    "Update payment state"
    updatePayment(payment: PaymentInput!, paymentId: ID!): Boolean
    updatePost(id: ID!, post: PostInput!): Boolean
    updatePostLimitationRule(id: ID!, limitRuleInput: LimitRuleInput!): Boolean!
    updatePostType(id: ID!, postType: PostTypeInput!): Boolean
    updateQcm(id: ID!, qcm: QcmInput!): Boolean
    updateQuestion(id: ID!, question: QuestionInput!): Boolean
    updateReview(id: ID!, review: ReviewInput!): Boolean
    updateRoom(id: ID!, input: RoomInput!): Boolean
    updateScheduleTask(id: ID!, input: ScheduledTaskInput!): Boolean
    "Update a schema by id"
    updateSchema(id: ID!, input: SchemaInput!): Boolean
    updateTitle(id: ID!, input: TitleInput!): Boolean
    "Update (remove all previous associations, and add thoses in argument) tutor as responsible for subjet"
    updateTuteurFromUe(ueIdArray: [ID]!, userId: ID!): Boolean
    updateTypeQcm(id: ID!, typeQcm: TypeQcmInput!): Boolean
    updateTypeQuestion(id: ID!, typeQuestion: TypeQuestionInput!): Boolean
    updateUE(id: ID!, ue: UEInput!): Boolean
    updateUECategory(category: UECategoryInput!, id: ID!): Boolean
    updateUEModule(id: ID!, input: UEModuleInput!): Boolean
    "Update user"
    updateUser(id: ID!, user: UserInput): Boolean
    updateUserAvatar(file: Upload!, userId: ID!): DataPath!
    updateUserGroup(groupId: ID!, userId: ID!): User!
    "Update user notification preferences"
    updateUserNotification(coursUpdateDeviceNotification: Boolean, coursUpdateEmailNotification: Boolean, privateMessageEmailNotification: Boolean, qcmUpdateDeviceNotification: Boolean): NotificationSettings
    updateUserPassword(newPassword: String!, userId: ID!): User!
    "Update UserPropertyData by id"
    updateUserPropertyDataById(id: ID!, input: UserPropertyDataInput): Boolean
    updateUserPropertyFolder(id: ID!, input: UserPropertyFolderInput): Boolean
    updateWatermark(id: ID!, watermark: WatermarkInput!): Boolean!
    updateWebhook(id: ID!, input: WebhookInput!): Webhook!
    "Upload file and return file name"
    uploadFile(file: Upload!, type: String!): String
    "User create own calendar (with limitations)"
    userCreateCalendar(calendar: CalendarInput!): Calendar!
    "User can create date diffusion without any entity"
    userCreateDateDiffusion(dateDiffusion: DateDiffusionInput!): DateDiffusion!
    "Fonction qui pour une question générée par AI (isAiGenerated : true) switch le bool 'aiGenerationHasBeenValidated' à true"
    validateAiGeneration(id: ID!): Boolean
}

type NoteParEffectif {
    effectif: Int
    note: Float
}

type NoteResult {
    note: Float
}

type Notification {
    createdAt: Date
    fromUser: User
    id: ID!
    method: String
    objectId: ID
    parentId: ID
    pinned: Boolean
    seen: Boolean
    text: String
    type: String
    updatedAt: Date
    user: User
    value: String
}

type NotificationSettings {
    coursUpdateDeviceNotification: Boolean
    coursUpdateEmailNotification: Boolean
    forumUpdateDeviceNotification: Boolean
    forumUpdateEmailNotification: Boolean
    privateMessageEmailNotification: Boolean
    qcmUpdateDeviceNotification: Boolean
}

"Notion"
type Notion {
    childrens: [Notion]
    cours: [Cours]
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    exercisesResultsSummary(coursId: ID, userId: ID): GoodAnswersStatsUserSynthesis
    files: [File]
    formula: String
    id: ID
    image: String
    images: [File]
    keywords: [NotionKeyword]
    myNotionStats: StatsNotionUser
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    parents: [Notion]
    qcms: [Qcm]
    questions: [Question]
    usefulLinks: [File]
}

type NotionAttributionResult {
    totalAnswers: Int
    totalQuestions: Int
}

"Notion Keyword"
type NotionKeyword {
    id: ID
    name: String
    notion: Notion
}

type NotionsResult {
    count: Int
    notions: [Notion]
}

type PageInfo {
    endCursor: String
    hasNextPage: Boolean
}

type PaginatedExerciseSeriesResult {
    "Total"
    count: Int
    "Results"
    exerciseSeries: [Qcm]
}

type PaginatedNotifications {
    "total notifs"
    count: Int
    "total unseen notifs"
    countUnseen: Int
    "notifs paginées non épinglées"
    notifications: [Notification]
    "toutes les notifs épinglées"
    pinnedNotifications: [Notification]
}

type PaginatedPostResults {
    count: Int
    postResults: [Post]
}

type PaginatedPostsResponse {
    count: Int
    posts: [Post]
}

type ParticipantPresence {
    date_diffusion_id: ID
    isPresent: Boolean
    userId: ID
}

type Payment {
    bills: [Bill]
    createdAt: Date
    forfaits: [Forfait]
    id: ID
    isNewUser: Boolean
    "User raw input data if user is not registered yet"
    logData: JSON
    logId: ID
    numberOfMensuality: Int
    paymentMethodId: ID
    paymentType: String
    state: String
    stripePriceId: String
    stripeSubscriptionId: String
    sum: Float
    totalMensuality: Int
    updatedAt: Date
    "User who paid, if it exists"
    user: User
    userId: ID
    "Custom form items"
    userPropertyData: [UserPropertyData]
}

type PdfDescription {
    cryptedInReading: Boolean
    fileName: String
    numberOfPages: Int
    pdfId: String
}

"Structure descriptive d'un pdf. Soit est un pdf et retourne cette structure, soit null"
type PdfStats {
    "Si la fonction a eu une erreur"
    error: Boolean!
    "String qui renseigne sur l'erreur qu'a possiblement rencontré la fonction"
    errorString: String!
    "Si le nom de fichier envoyé correspond à un fichier en BDD"
    isFile: Boolean!
    "Si le fichier existe, et si oui, si c'est un pdf"
    isFileAndPdf: Boolean!
    "Si le fichier existe et que c'est un pdf et si il est crypté ou pas"
    isPdfAndCrypted: Boolean!
    "Si le pdf est éditabe => crypté fort, crypté secret ou pas crypté"
    isPdfEditable: Boolean
    "Le nombre de page du pdf"
    numberOfPages: Int
}

type Post {
    answer: Answer
    answerId: ID
    answeredByAi: Boolean
    "infère et récupère la réponse 'validée' (du bot / tuteur/super admin) "
    approuvedResponse: ApprouvedResponse
    courId: ID
    cours: Cours
    createdAt: Date
    eventId: ID
    "legacy file"
    file: File
    "legacy file"
    fileImage: File
    "Multi fichiers"
    fileList: [File]
    forum: Forum
    forumId: ID
    id: ID
    ip: String
    isAskingForHumanHelp: Boolean
    isResolved: Boolean
    isResolvedByAi: Boolean
    lastAnswer: Int
    like_histories: [LikeHistory]
    likes: Int
    myLastLikeAction: String
    parent: Post
    parentId: ID
    qcm: Qcm
    qcmIdQcm: ID
    questionId: ID
    "only for AI answers, state of the answer: 'pending', 'accepted', 'rejected'"
    state: String
    tag: String
    text: String
    threadId: ID
    threadPost: Post
    title: String
    type: PostType
    updatedAt: Date
    user: User
    userId: ID
    "User to whom AI replied to"
    userIdForAiFeedback: ID
    "Ai answer verified by"
    verifiedBy: ID
    views: Int
}

type PostReaction {
    count: Int
    emoji: String
    isUserReaction: Boolean
}

type PostType {
    firstAnswerByChatGPT: Boolean
    id: ID
    image: String
    name: String
    otherAnswersByChatGPT: Boolean
    precisionPromptForChatGPT: String
    "comment type: class, mcq, answer, etc"
    type: String
}

type ProgressAnalyseReport {
    "Description de la structure"
    description: String
    "Id de la structure"
    id: ID
    "image"
    image: String
    "is a folder or not"
    isFolder: Boolean
    "Nom de la structure"
    name: String
    "NodeS de progression qui correspondend à tous les résultats dans le temps pour la structure choisie"
    structureProgress: [ProgressNode]
    "Type de la structure (ue,cours,category)"
    type: String
    "NodeS de progression qui correspondent à tous les résultats dans le temps pour l'user choisi"
    userProgress: [ProgressNode]
}

type ProgressNode {
    "Anne de la node"
    annee: Int
    "jour de la node"
    jour: Int
    "Date 'bin-arisé' de la node"
    modifiedDate: Date
    "Mois de la node"
    mois: Int
    "Note moyenne de la node"
    moyenne: Float
    "Nombre total de résultats dans la node"
    nbResults: Int
    "semaine de la node"
    semaine: Int
}

"MCQ type"
type Qcm {
    "Parent category (UE)"
    UE: UE
    "Parent category ID"
    UEId: Int
    "Deprecated Is annale do not use"
    annale: Boolean
    "Year"
    annee: Int
    chronoByQuestionOrGlobal: String
    "If timer is enabled"
    chronometre: Boolean
    "Correction configuration (show/hide modules mostly)"
    correctionConfig: JSON
    countResultatsEleves: Int
    "Cours liés à cette entité QCM"
    cours: [Cours]
    "Cours impliqués dans les questions de ce QCM"
    coursImpliques: [Cours]
    "Creation date"
    date_creation: String
    date_modif: String
    defaultQuestionsType: [TypeQcm]
    "Is MCQ deleted or not"
    deleted: Boolean
    "MCQ Description"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    "Difficulty level"
    difficulty: Float
    "True if correction AI analysis is activated"
    enableAiAnalysis: Boolean
    external: ID
    goToNextQuestionWhenTimesUp: Boolean
    groupQuestionsByTheme: Boolean
    "Does this MCQ has checkboxes or radio buttons"
    hasCheckboxes: Boolean
    hasExternalQuestions: Boolean
    id_createur: ID
    "Legacy link ID"
    id_lien: ID!
    "MCQ ID"
    id_qcm: ID!
    "If the author has been manualy changed"
    isAuthorChanged: Boolean
    "Is MCQ question by question fullscreen or not"
    isFullscreen: Boolean
    "Is it published (visible) or not"
    isPublished: Boolean
    "Last forum post"
    lastPost: Post
    "Legacy unique link ID"
    link: String
    "Maximum points attainable"
    maximumPoints: Float
    mySession: QcmSession
    "Number of questions"
    nombreQuestions: Int
    "Mcq notions"
    notions: [Notion]
    "Number of posts"
    postsNumber: Int
    "Creator username"
    pseudo_createur: String
    "Whether it's public or not (legacy, could be used in future)"
    public: Boolean
    questionPickingStrategy: String
    "Questions that belong to the MCQ"
    questions: [Question]
    "Randomize questions order"
    randomizeQuestions: Boolean
    "Randomize answer items order"
    randomizeQuestionsAnswers: Boolean
    "Result for a session and / or user, or for specific groups"
    resultat(groupIds: [ID], sessionId: ID, statId: ID, userId: ID): QcmResult
    "All user results"
    resultatsEleves(firstTimeOnly: Boolean, groupIds: [ID]): [QcmResult]
    "All user results for this MCQ"
    resultsForUser(userId: ID): [QcmResult]
    shouldResumeTime: Boolean
    "If fullscreen, will show correction at each step"
    showCorrectionAtEachStep: Boolean
    "Stats"
    statistiques: QcmResult
    "Timer delay in seconds"
    timer_delay: Int
    "Times MCQ can be done"
    timesItCanBeDone: Int
    "MCQ Title"
    titre: String
    titre_de: String
    titre_en: String
    titre_es: String
    titre_it: String
    "Mcq types"
    type: [TypeQcm]
    "Legacy UE (unused will be removed)"
    ue: Int
    ueCategoriesImpliquees: [UECategory]
    "MCQ image URL"
    url_image: String
    "Number of views"
    views: Int
}

"New type that compile, each Questions and Update"
type QcmEachQuestionDescription {
    "Dernier log en date (premier élément de qcmEditHistory)"
    lastestEditLog: Log
    "the linked Qcm"
    qcm: Qcm
    "qcm and question edit history : dans le getLog(filter) ça sélectionne aussi les "
    qcmEditHistory: [Log]
    "the questions and updated linked to this Qcm"
    questions: [QuestionEditHistory]
}

type QcmGeneratorPreview {
    doneQuestionsIds: [ID]
    exercisesCount: Int
    questionIds: [ID]
}

type QcmGeneratorResponse {
    hasTimer: Boolean
    isAnswerOrderRandom: Boolean
    qcmSessionId: ID
    questions: [Question]
    secondsPerExercise: Int
}

type QcmItemResponse {
    A: Boolean
    B: Boolean
    C: Boolean
    D: Boolean
    E: Boolean
    F: Boolean
    G: Boolean
    erreurs: Int
    id_question: ID
}

"Les link à résolve pour l'importation de QCM"
type QcmMappingToResolve {
    defaultQuestionTypeId: ID
    "Description applicable pour chaque link à résolve"
    description: String!
    "Link entre QCM <-> Qcm Type"
    serieTypeId: ID
    "Link entre QCM <-> UE"
    ueId: ID
}

"MCQ grade"
type QcmNote {
    id: ID
    id_qcm: ID
    id_utilisateur: ID
    note: Float
    ponderatedGrade: Float
}

"Exercise serie result"
type QcmResult {
    aiAnalysis: JSON
    "average time spent for group"
    averageSeconds: Float
    "Ranking"
    classement: Int
    count: Int
    date: Date
    id: ID
    "Question serie ID"
    id_qcm: ID
    id_utilisateur: ID
    isFirstTime: Boolean
    maxGrade: Float
    maxPoints: Float
    minGrade: Float
    "Average grade for group"
    moyenne: Float
    "Final grade"
    note: Float
    ponderatedGrade: Float
    qcmSessionId: ID
    reponses: [QcmItemResponse]
    "Temps passé en secondes (série classique)"
    seconds: Int
    session: QcmSession
    user: User
}

type QcmSavedState {
    createdAt: Date
    formValues: JSON
    id: ID!
    qcmId: ID
    sessionId: ID
    time: Int
    updatedAt: Date
}

"Question series session"
type QcmSession {
    "Smart MCQ question IDS"
    assumedCorrectQuestionsIds: [ID]
    "Smart MCQ question IDS"
    assumedFailedQuestionsIds: [ID]
    challengeId: ID
    "Smart MCQ question IDS"
    correctQuestionsIds: [ID]
    countGoodInARow: Int
    createdAt: Date
    currentCoursId: ID
    currentState: String
    doneQuestionsCount: Int
    "Exam if it's an exam session"
    exam: Exam
    examQuestionSerieId: ID
    examSessionId: ID
    "Smart MCQ question IDS"
    failedQuestionsIds: [ID]
    formationElementId: ID
    goodQuestionsCount: Int
    id: ID
    isActive: Boolean
    "If MCQ is done or not"
    isFinished: Boolean
    "Smart MCQ question IDS"
    luckyCorrectQuestionsIds: [ID]
    "Maximum points based on questionsIdsDone"
    maxPoints: Float
    "Exercise serie linked (if any)"
    qcm: Qcm
    "Exercices done (for correction)"
    questionsDone: [Question]
    "Exercices ids done by user"
    questionsIdsDone: [ID]
    "Exercices to do (based on settings, for training)"
    questionsToDo: [Question]
    "Smart MCQ question IDS"
    remainingQuestionsIds: [ID]
    "Result of the session if it's finished"
    result: QcmResult
    settings: JSON
    "Smart MCQ scores and performance"
    smartMcqScore: SmartMcqScore
    updatedAt: Date
    "User who launched the session"
    user: User
}

"Question answers history"
type QcmStatsQuestion {
    "User choices, will not be used in the future"
    answersData: [ID]
    "certainty input"
    certainty: Int
    "MCQ question ID"
    id_question: ID
    id_statistique_question: ID
    "User ID"
    id_utilisateur: ID
    "Schema answer for question"
    jsonAnswers: JSON
    "Score obtained for question"
    pointsObtained: Float
    "Weighed score for question"
    ponderatedPointsObtained: Float
    "MCQ session id"
    qcmSessionId: ID
    qcmStatsQuestion: [StatsQuestionAnswer]
    "User choices when special exercise like free text or alphanumerical, usually an array"
    valueData: JSON
}

type Query {
    "Accessibles subjects for user"
    UEsAccessibleFor(userId: ID!): [UE]
    _: Boolean
    "Fonction de récupération de stats au cours du temps de façon à créer un graph "
    activeUsersGraph(activeUsersFilter: ActiveUsersFilter, graphFilter: ActiveUsersGraphFilter): GraphTimeDistributedData
    "Fonction qui permet de récupérer toutes les gpt qcm config (table config)"
    adminAiQcmConfigs: [GPTQcmConfig]
    "Admin query to search if a user can post a discussion. Retourne le nombre de secondes à attendre avant que l'user puisse de nouveau poser une question. -1 si disabled"
    adminCanUserIdPostDiscussion(userId: ID): Int!
    adminGetGroupsAuthorizedForMathpixIntegration(mathpixIntegrationId: ID): [Groupe]
    "Query admin qui retourne les config mathpix pour les admins"
    adminGetMathpixConfigs: [MathpixConfig]
    adminQueryMathpixies(mathpixConfigId: ID!): [Mathpixies]!
    "Admin search exercise series (paginated)"
    adminSearchExerciseSeries(filter: AdminQcmSearchFilterPaginated): PaginatedExerciseSeriesResult
    adminSearchPosts(filter: AdminPostSearchFilter): PaginatedPostResults
    "Admin search exercise series with search filter"
    adminSearchQcms(filter: AdminQcmSearchFilter): [Qcm]
    "default AI config"
    aiConfig: GPTConfig
    "AI config"
    aiConfigs: [GPTConfigResponse]
    "All Buildings"
    allBuildings: [Building]
    allChallenges: [Challenge]
    allCoursRecursiveInUeWithBaseRevision(ueId: ID!): [Cours]
    allCustomPlannings: [CustomPlanning]
    "Admin all events unpaginated"
    allEvents(filter: EventFilter): [Event]
    allExams(filter: ExamFilterInput): [Exam]
    "Returns all forms"
    allForms: [Form]
    "All global announces for admin"
    allGlobalAnnounces: [GlobalAnnounce]
    allGroupes: [Groupe]
    "All Rooms"
    allRooms: [Room]
    allTitles: [Title]
    allTypeQcm(forUser: ID): [TypeQcm]
    allTypeQuestion: [TypeQuestion]
    "All UEs, admin only"
    allUEs: [UE]
    allUEsUnresolvedPosts: [Post]
    annalesInUE(ueId: ID!): [Cours]
    annees: [Annee]
    "Get answer by id"
    answer(id: ID!): Answer
    "Get all API keys (admin)"
    apiKeys: [ApiKey]
    "Available for user"
    availableCustomPlannings: [CustomPlanning]
    blocksInStep(id: ID!): [FormationBlock]
    "Building by id"
    building(id: ID!): Building
    calendar(calendarId: ID!): Calendar
    "Get all calendars (admin)"
    calendars: [Calendar]
    "Check if current user can do this exercise serie"
    canIDoQcm(id: ID!): Boolean
    "query to search if the current user can post a discussion.   Retourne le nombre de secondes à attendre avant que l'user puisse de nouveau poser une question. -1 si disabled"
    canPostDiscussion: Int!
    challenge(id: ID!): Challenge
    challengeUserProgress(filter: ChallengeUserProgressFilter): ChallengeUserProgress
    "Check si le pdf d'un cours est éditable"
    checkIfCoursPdfIsEditable(id: ID!): Boolean
    checkIfUserShouldSetupUsername(inputToken: String): Boolean
    checkUsernameAvailability(username: String): Boolean
    completedChallenges(filter: CompletedChallengeFilter): [Challenge]
    "Public config (hide sensitive data)"
    config(defaultOnly: Boolean): [Config]
    "Config by key"
    configByKey(key: String!): Config
    countAccessiblesCoursesInUE(ueId: ID!): Int
    countAccessiblesExercisesInUE(ueId: ID!): Int
    "Count UE modules in a formation"
    countUEModules(ueId: ID!): Int
    countUsersInGroups(groupIds: [ID]): Int
    cour(id: ID!): Cours
    cours(filter: CoursFilter): CoursSearchResult
    coursIds(ids: [ID]): [Cours]
    "Courses in one UE"
    coursInUE(ueId: ID!): [Cours]
    "Courses in several categories"
    coursInUECategories(ueCategoryIds: [ID!]): [Cours]
    "Courses in one category"
    coursInUECategory(ueCategoryId: ID!): [Cours]
    coursModulePreview(coursId: ID!): [CoursSupport]
    "News pour Homepage : cours/pages mis en avant"
    coursNewsList: [Cours]
    coursSupportsInCours(id: ID!): [CoursSupport]
    customLinksForfaits: [CustomLinkForfait]
    "Custom mobile login page"
    customMobileConfig(domain: String): CustomMobileConfig
    dateDiffsByIds(dateDiffIds: [ID]): [DateDiffusion]
    "Get user planning for specific object (cours, qcm, event)"
    dateDiffusions(courId: ID, eventId: ID, examSessionId: ID, qcmId: ID): [DateDiffusion]
    "Get course dates"
    datesDiffusionInCour(id: ID!): [DateDiffusion]
    debugStatsForUserWithinDates(endDate: Date!, startDate: Date!, userId: ID!): JSON
    defaultForfaits: CustomLinkForfait
    defaultUEsForMcqScale(id: ID!): [UE]
    "Query réservée aux dev local pour aider à l'analyse des logs"
    devGptLogAnalyser(id: ID!): JSON
    "Get a discussion by ID"
    discussion(id: ID!): Discussion
    "Elements after completion diapo synthese"
    elementsAfterCompletionDiapoSynthese(id: ID!): [FormationElement]
    "Elements before completion diapo synthese"
    elementsBeforeCompletionDiapoSynthese(id: ID!): [FormationElement]
    "Elements for global announce"
    elementsForGlobalAnnounce(globalAnnounceType: String!): [FormationElement]
    "Elements for user properties (custom fields)"
    elementsForUserProperties: [FormationElement]
    elementsInBlock(id: ID!): [FormationElement]
    elementsInChallenge(id: ID!): [FormationElement]
    elementsInCoursSupport(id: ID!): [FormationElement]
    elementsInEvent(id: ID!): [FormationElement]
    elementsInForfait(id: ID!): [FormationElement]
    elementsInForfaits(ids: [ID]): [FormationElement]
    "Returns all elements in a form"
    elementsInForm(formId: ID!): [FormationElement]
    "Elements in header of the question serie"
    elementsInQcmHeader(id: ID!): [FormationElement]
    "Elements in header of the Exercise"
    elementsInQuestion(id: ID!): [FormationElement]
    "Elements in footer of the Exercise"
    elementsInQuestionFooter(id: ID!): [FormationElement]
    "Elements for schema correction"
    elementsInSchemaCorrection(schemaId: ID!): [FormationElement]
    elementsInStep(id: ID!): [FormationElement]
    "Query qui demande à chatGPT de demander un input"
    enhanceQcmText(input: ChatGptQueryInput): [ChatGptQueryOutput]!
    "Get event by id"
    event(id: ID!): Event
    "Get events by ids"
    events(ids: [ID]!): [Event]
    "Module évènements liés au cours"
    eventsCourseModule(coursId: ID!): [Event]
    "Get Exam by id"
    exam(id: ID!): Exam
    examQuestionSerie(id: ID!): ExamQuestionSeries
    examSession(id: ID!): ExamSession
    "Exam session result of several question series"
    examSessionResult(examScaleId: ID, id: ID!, userId: ID): ExamSessionResult
    "Exercise module preview (course, notions). Returns done/undone exercises count."
    exerciseModulePreview(filter: ExerciseModulePreviewFilter): ExerciseModulePreviewResponse
    "Linked questions series: Exercices comportant au moins une question de ce cours"
    exercisesSeriesForCours(id: ID!): [Qcm]
    "Public mobile app configs"
    exoBackConfigs: [ExoBackConfig]
    "Export users by filter and get link"
    exportUserIdsMatchingFilter(filter: UserSearchFilter): String
    "Extract notions from keywords in text"
    extractNotionsFromTextWithKeywords(input: String): [Notion]
    "Get course revision sheets (will be deleted)"
    fichesInCour(id: ID!): [Fiche]
    "Get course files (will be deleted)"
    filesInCour(id: ID!): [File]
    "Find folders by filter"
    findFolders(filter: FolderFilter): [Folder]
    findGroupes(role: String): Groupe
    "find all user active by date and belonging to one or multiples groups and with a filter on the username"
    findUsersActiveForDates(activeUsersFilter: ActiveUsersFilter, endDate: Date!, startDate: Date!): ActiveUsersResult
    "Flush la table averageHistory et demande un passWord"
    flushAverageHistory(passWord: String): Boolean
    "Folder by id"
    folder(id: ID!): Folder
    "All folders (legacy, will be removed)"
    folders: [Folder]
    "Tree data selection for folders and types (returns folders with/without associated entity types)"
    foldersTreeData(folderCheckable: Boolean, folderType: String, separateForfaitsByType: Boolean, showNumberOfUsers: Boolean, type: String): JSON
    forfait(id: ID!): Forfait
    forfaitBeforePayment(id: ID!): JSON
    forfaits: [Forfait]
    forfaitsFromCustomLink(link: String): [Forfait]
    "Form by ID (int) or UUID (string)"
    form(id: ID, uuid: ID): Form
    "Returns all filtered form results"
    formResults(filter: FormResultFilter): [UserPropertyData]
    "Get Formation by id"
    formation(id: ID!): Formation
    "Pour cours enrichi"
    formationBlocksInCours(id: ID!): [FormationBlock]
    "Query element by id"
    formationElement(id: ID!): FormationElement
    formationProgress(formationId: ID!): [FormationProgress]
    "Sommaire cours enrichi"
    formationSummaryInCours(id: ID!): [FormationBlock]
    forumCategories(parentId: ID): ForumResponse
    forumCategory(id: ID!): ForumCategory
    "Permet de récupérer les données nécessaires à l'affichage des graphes de progression"
    fullAnalyse(bin: String = "jour", categoryId: [ID], coursId: [ID], dateDebut: Date = "2020-01-01", dateFin: Date = "2023-06-01", ueId: [ID], userId: ID): [FullAnalyseReport]
    "Generate exercise serie preview for generator"
    generateQcmPreview(params: GeneratorParams!): QcmGeneratorPreview
    "Get AI analysis (serie correction)"
    getAiAnalysis(force: Boolean, qcmId: ID, sessionId: ID, statId: ID, userId: ID): JSON
    getAllNotDeletedReviewForCourse(coursId: ID!): [Review]
    "From questionId, get all scale matching the questionType of the submited question"
    getAllScalesAvailableForQuestion(questionId: ID!): [McqScale]
    getAllWatermarks: [Watermark]!
    getAllWebhooks: [Webhook]
    getBreadcrumbData(categoryId: ID, coursId: ID, ueId: ID): [BreadcrumbItem]
    "Get calendars to show on the side of agenda (user)"
    getCalendarsToShowOnTheSide: [Calendar]
    getConfigFromAiUserId(aiUserId: ID): Config
    getCoursReviewStats(coursId: ID!): CoursReviewStats
    "Question in session query"
    getCurrentQuestionInSession(sessionId: ID!): Question
    "Returns default bot id to auto answer for a post according to admin configuration"
    getDefaultBotIdToAutoAnswerForPost(postId: ID!): ID
    "Permet de récupérer la configuration de watermark par default d'exoteach => dans le cas où plate forme a pas de config et on init"
    getDefaultExoteachWatermark: Watermark!
    getDefaultScaleForExerciseTypeAndCourseId(coursIds: [ID]!, mcqScaleQuestionType: String!): McqScale
    "Permet d'avoir le nombre de chaque type d'exercice selon une sélection"
    getDetailledExerciceCountFromSelection(categoryId: [ID], coursId: [ID], dateDebut: Date = "2020-01-01", dateFin: Date = "2023-06-01", ueId: [ID], userId: ID): DetailedExerciceReport
    "Graph reponses correction"
    getGraphCorrection(groupIds: [ID], id_qcm: ID!, sessionId: ID, userId: ID): JSON
    "Graph progression"
    getGraphProgression(ueId: ID!, userId: ID): JSON
    getIndividualGroupIdsForUsersInGroup(groupId: ID!): [ID]
    getInitialNotionGraphForCours(coursId: ID, filter: NotionGraphFilter): JSON
    getLogsFor(filter: LogFilter): [Log]
    "QcmConfig Authorized for me"
    getMyAiQcmConfigs: [GPTQcmConfig]
    "Query qui retourne les config mathpix pour l'user en question"
    getMyMathpixConfigs: [MathpixConfig]
    "Permet d'avoir la PostLimitationRule pour l'user en question"
    getMyPostLimitationRule: LimitRule
    "Next course"
    getNextCoursFormation(currentCoursId: ID!): ID
    "check if there is pdf files in specified course, return descriptor like number of pages, if it is crypted in reading"
    getPdfDescriptionForCour(id: ID!): PdfDescription
    getPdfDescriptionForCours(id: [ID]!): [PdfDescription]
    "read the upload and return a pdf descriptor (cf : cours schema)"
    getPdfDescriptionForUpload(pdfFile: Upload): PdfDescription
    "Get planning V2"
    getPlanning(additionalUserIds: [ID], begin: Date, disabledExternalCalendars: [ID], end: Date, forRooms: Boolean, groupIds: [ID], myPlanning: Boolean): [DateDiffusion]
    "Graph histogramme certitude"
    getPointsByCertainty(id_qcm: ID!, sessionId: ID, userId: ID): JSON
    "Permet de récupérer des informations associées au post"
    getPostAdditionalInfos(postId: ID!): AdditionalInfos
    getPostDetailsForNotif(postId: ID!): Post
    "Get post reactions"
    getPostReactions(postId: ID!): [PostReaction]
    "Get question serie stats for user or specific groups (for correction page)"
    getQcmCorrectionStats(groupIds: [ID], id: ID!, sessionId: ID, statId: ID, userId: ID): QcmResult
    "For a qcmId, return qcm informations and each question informations + update History"
    getQcmDetailedInformation(id: ID!): QcmEachQuestionDescription
    "Identifie les couleurs associées aux UE liées aux questions."
    getQuestionColor(id: ID!): [String]
    "Pour un mcqScaleType, retourne les scales associées"
    getQuestionTypeMcqScales(questionType: String!): [McqScale]
    "(Module accès rapide cours) Get exercises preview by predefined cours-type involving Course coursId (used for annales)"
    getQuestionsAnnaleForCours(coursId: ID!): CoursAnnalesResponse
    getTeamsMembersForMe: [User]
    getUserIdsInGroup(groupId: ID!): [ID]
    "Get users by idArray"
    getUsersArray(idArray: [ID]!): [User]!
    getWebhookById(id: ID!): Webhook
    getWebhookEvents(webhookId: ID!): [WebhookEvent]
    getWebhookEventsStats: WebhookEventsStats
    getWebhookLogs(filter: WebhookLogFilter): WebhookLogsResponse
    getYearsAvailableForPostsForCours(coursId: ID): [Int]
    getYearsAvailableForPostsForEvent(eventId: ID): [Int]
    getYearsAvailableForPostsForForum(forumId: ID!): [Int]
    getYearsAvailableForPostsForQcm(qcmId: ID!): [Int]
    globalAnnounceForGroups(groupIds: [ID]): [GlobalAnnounce]
    groupe(id: ID!): Groupe
    groupes: [Groupe!]
    groupsForFormationElementsAccess(formationElementId: ID!): [Groupe]
    groupsForFormationElementsUnlock(formationElementId: ID!): [Groupe]
    groupsForPlanning: [Groupe]
    groupsInFolder(folderId: ID): [Groupe]
    groupsVisualizer: GroupVisualizer
    hasUserAlreadyReviewedCourse(coursId: ID!): Review
    "Pedagogical tree"
    hierarchicalTreeData(forUeId: ID, forUserId: ID, getWatermarkCoursData: Boolean = false, selectable: Boolean, type: String): JSON
    "Hubspot query"
    hubspotQuery(input: HubspotQueryInput): JSON
    "Query qui va regarder si la string input est associé à un fichier dans la BDD, si PDF, et ensuite va essayer de la lire depuis le ./Upload/cours pour analyse."
    isPdfAndStatsInCoursSystemFolder(fileName: String): PdfStats
    "Query qui va regarder si la string input est associé à un fichier dans la BDD, si PDF, et ensuite va essayer de la lire depuis le ./Upload/file pour analyse."
    isPdfAndStatsInFileSystemFolder(fileName: String): PdfStats
    "Return if exercise is editable by me or not"
    isQuestionEditableByMe(id: ID!): Boolean
    latestPosts: [Post]
    "All MCQ scales / Tous les barêmes"
    mcqScales: [McqScale]
    "Retourne un descripteurs sommaire de toutes les scales : nom, type d'exercice ,leur nombre d'exercices liés  "
    mcqScalesDescription: [McqScaleDescription]
    "Petite querie utilisée dans l'import de QCU/QCM par AI"
    mcqScalesQcmAndQcu: [McqScale]
    "Connected user"
    me: User
    "My discussions"
    mesDiscussions: [Discussion]
    mesForfaitsDisponibles(domain: String): [Forfait]
    mesGroupes: [Groupe]
    "User accessible UEs, specify parentId to get child UE. Specify forUser to get user's accessible UE (only parents, admins)"
    mesUEs(forUser: ID, parentId: ID): [UE]
    mesUEs_nocache: [UE]
    message(id: ID!): Message!
    "Discussion's messages"
    messagesInDiscussion(cursor: String, discussionId: ID!, limit: Int): MessageConnection
    "Min mobile version, to force mobile app update if needed"
    minMobileVersion: Int
    modulesQuickAccess(filter: ModuleQuickAccessFilter): [ModuleQuickAccess]
    monClassementPondereQcm(groupIds: [ID], id: ID!): ClassementQCM
    "Get my ranking on specific exercise serie, and for specific groups"
    monClassementQcm(groupIds: [ID], id: ID!, userId: ID): ClassementQCM
    "Planning legacy"
    monPlanning(additionalUserIds: [ID], begin: Date, end: Date, forRooms: Boolean, forSpecificGroups: Boolean, groupIds: [ID]): [DateDiffusion]
    "Get user planning in ueId, and by groupIds (optionnal)"
    monPlanningInUE(groupIds: [ID], ueId: ID!): [DateDiffusion]
    "Get full revision planning by dates"
    monPlanningRevision(begin: Date, end: Date): [RevisionByDay]
    "Users I have blocked"
    myBlockedUsers: [User]
    "Get parent's childs"
    myChilds: [User]
    "Custom courses tabs for mobile, by group access"
    myCustomCoursesTabs(domain: String): JSON
    "Exams done or unfinished by user"
    myExamsSessionsDone(filter: ExamSessionDoneFilter, userId: ID): [ExamSession]
    myFormations: [Formation]
    "User global announces"
    myGlobalAnnounces: [GlobalAnnounce]
    "ID of my groups + my supervised groups (those i'm responsible for)"
    myGroupsAndMySupervisedGroups: [ID]
    myNotifications: [Notification]
    "Notifications paginées (non épinglées)"
    myNotificationsPagined(filter: NotifsFilter): PaginatedNotifications
    "Retourne les notifications épinglées uniquement"
    myPinnedNotifications: [Notification]
    "My responsables"
    myResponsables: [User]
    notification(id: ID!): User
    notifications: [Notification]
    "Get notion by id"
    notion(id: ID!): Notion
    notionGraph(filter: NotionGraphFilter, notionId: ID): JSON
    "Get course notions (auto-added)"
    notionsAutoAddedInCour(id: ID!): [Notion]
    "Get course notions (manually-added)"
    notionsManuallyAddedInCour(id: ID!): [Notion]
    "Payments and subscriptions activity, for admins and commercial"
    payments(filter: PaymentFilter): AdminPaymentResult
    "Get base revision in ueId"
    planningBaseRevisionInUE(ueId: ID!): [BaseRevision]
    post(id: ID!): Post
    "Permet d'avoir la PostLimitationRule pour un userId"
    postLimitationRuleForUserId(userId: ID!): LimitRule
    "Permet d'avoir la PostLimitationRule pour une ruleId"
    postLimitationRuleWithId(id: ID!): LimitRule
    postTypes: [PostType!]
    posts: [Post!]
    "Posts for specific answer"
    postsForAnswer(answerId: ID!, filter: PostFilter): [Post]
    "All answers posts in Mcq"
    postsForAnswersInQcm(filter: PostFilter, qcmId: ID!): [Post]
    "Posts for Cours"
    postsForCours(coursId: ID!, filter: PostFilter): [Post]
    "Posts for Event"
    postsForEvent(eventId: ID!, filter: PostFilter): [Post]
    "Posts for question"
    postsForExercise(filter: PostFilter, questionId: ID!): [Post]
    "Posts for forum"
    postsForForum(filter: PostFilter, forumId: ID!): [Post]
    "Posts for qcm object"
    postsForQcm(filter: PostFilter, qcmId: ID!): [Post]
    previewMassDeleteUsersInGroups(groupIds: [ID]): [User]
    processImage(file: Upload!, mathpixConfigId: ID): Imagepixie
    "Permet de récupérer les données nécessaire à l'affichage de la progression : notament pour le graph de progression"
    progressAnalyse(bin: String = "jour", categoryId: [ID], coursId: [ID], dateDebut: Date = "2020-01-01", dateFin: Date = "2023-06-01", ueId: [ID], userId: ID): [ProgressAnalyseReport]
    "Get exercise serie by id"
    qcm(id: ID!): Qcm
    "User savedState to restore exercise serie if needed"
    qcmSavedState(qcmId: ID, sessionId: ID): QcmSavedState
    "Get exercise series in subcategory id"
    qcmsInSousCategorie(id: ID!): [Qcm]
    "Query massChangeScaleForQuestion => Permet d'avoir le nombre de questions qui seront modifées par la mutation de même nom"
    queryMassChangesScaleForQuestions(queryMassChangesScaleForQuestionsInput: MassChangesScaleForQuestionsInput!): Int
    queryMultiplesQuestions(id: [ID]!): [Question]
    "Get exercise by ID"
    question(id: ID!): Question
    reportedContent: [Log]
    "Permet de récupérer les données nécessaire à l'affichage des moyennes : notament pour le graphRadar / Histogramme"
    resumeAnalyse(categoryId: [ID], coursId: [ID], dateDebut: Date = "2020-01-01", dateFin: Date = "2023-06-01", ueId: [ID], userId: ID): [ResumeAnalyseReport]
    "Revision module (legacy will be removed)"
    revisionTips(filter: RevisionTipsFilter): RevisionTipsResponse
    "Room by id"
    room(id: ID!): Room
    "Get scheduled task by id"
    scheduledTask(id: ID!): ScheduledTask
    "Get all scheduled tasks"
    scheduledTasks(filter: ScheduledTasksFilter): [ScheduledTask]
    "Get a schema by id"
    schema(id: ID): Schema
    "Get all schemas or filter them by id, name"
    schemas(filter: SearchSchemasFilter): SchemaList
    "Search text in all associated models of date diffusion (cours, qcm, event, exam)"
    searchDatesDiff(filter: DateDiffSearchFilter): [DateDiffusion]
    "Admin search events paginated, events and folders"
    searchEvents(filter: EventFilter): EventPaginated
    searchForfaits(filter: ForfaitSearchFilter): [Forfait]
    "Search in NotionKeywords"
    searchNotionKeywords(filter: NotionKeywordSearchFilter): [NotionKeyword]
    "Search notions using NotionSearchFilter"
    searchNotions(filter: NotionSearchFilter): NotionsResult
    "LEGACY WILL BE REMOVE search exercise serie with search filter for user, used in MCQs tab and forum"
    searchQcms(filter: QcmSearchFilter): [Qcm]
    "Paginated Exercise series search with filter for user"
    searchQcmsV2(filter: QcmSearchFilter): PaginatedExerciseSeriesResult
    "Search exercises by filter"
    searchQuestions(filter: QuestionSearchFilter): SearchResponse
    "Search among all users with UserSearchFilter"
    searchUsers(filter: UserSearchFilter): UserSearchResult
    "Search by username, for autocomplete"
    searchUsersByUsername(domain: String, role: String, username: String!): [User]
    sectionsInFormation(id: ID!): [FormationSection]
    "Exam session linked to the questionSerie"
    sessionForMcqExam(examQuestionSerieId: ID!): QcmSession
    "Find QcmSession by id"
    sessionQcm(id: ID!): QcmSession
    "Sessions for specific element (single exercice element, etc.)"
    sessionsForElementId(formationElementId: ID!): [QcmSession]
    "Classic or fullscreen exercise sessions query"
    sessionsForMcq(id_qcm: ID!): [QcmSession]
    sessionsHistory(filter: QcmSessionFilter): [QcmSession]
    "Get subcategories in UE"
    sousCategories(ueId: ID): [UECategory]
    statistiques: Statistique
    stepsInSection(id: ID!): [FormationStep]
    "Get all users with TUTEUR role"
    tuteurs: [User!]
    typeQcm(id: ID!): TypeQcm
    "Get UE by id"
    ue(id: ID!): UE
    "Get subcategories in UE"
    ueCategories(ueId: ID!): [UECategory]
    "Get subcategories by IDs"
    ueCategoriesAmongIds(ids: [ID!]): [UECategory]
    "Get subcategories in UEs"
    ueCategoriesIn(ueIds: [ID!]): [UECategory]
    "Get subcategory by ID"
    ueCategory(id: ID!): UECategory
    "Single ue module progress"
    ueModuleProgress(ueModuleId: ID!, userId: ID): UEModuleProgress
    "User progress for an ueId (formation)"
    ueModuleProgressInUE(ueId: ID!, userId: ID): [UEModuleProgress]
    "Get UE modules by UE id (formation modules)"
    ueModules(ueId: ID!): [UEModule]
    "UEs by ids"
    ues(ids: [ID!]): [UE]
    "Returns first undone mandatory form id to complete, for current user."
    undoneMandatoryFormIdToComplete: ID
    unresolvedForumPosts: [Post]
    unseenPrivateMessagesCount: Int
    "Get user by ID"
    user(id: ID!): User
    userAnswerPosts(limit: Int, offset: Int, userId: ID): PaginatedPostsResponse
    "User's discussions (admin)"
    userDiscussions(userId: ID!): [Discussion]
    "Get user ids matching search filter"
    userIdsMatchingFilter(filter: UserSearchFilter): [ID]
    "User notification preferences"
    userNotificationSettings: NotificationSettings
    "User profile, used for profile cards"
    userProfile(id: ID, username: String): User
    userPropertiesFolders: [UserPropertyFolder]
    userThreadPosts(limit: Int, offset: Int, userId: ID): PaginatedPostsResponse
    "All users"
    users: [User!]
    "Find all users asking for deletion"
    usersAskingForDeletion: [User]
    "user reCAPTCHA post"
    verifyRecaptcha(inputToken: String!): RecaptchaResult
    "Get course video links (will be deleted)"
    videosInCour(id: ID!): [File]
    watermark(id: ID!): Watermark!
}

"MCQ Question"
type Question {
    aiGenerationHasBeenValidated: Boolean
    allowComments: Boolean
    annale: Boolean
    annee: Int
    answerHistory(sessionId: ID, statId: ID, userId: ID): QcmStatsQuestion
    answers: [Answer]
    authorId: ID
    "Notion auto add"
    autoAddNotions: Boolean
    "Calculated difficulty of the question"
    calculatedDifficulty: Float
    childrensQuestions: [Question]
    "Comment la question a - t - elle été créée (par AI ? Par humain, etc...) "
    creationType: String
    date_creation: String
    date_modif: String
    "Admin defined difficulty of the question"
    definedDifficulty: Float
    "If exercise is deletable or not"
    deletable: Boolean
    doneByMe: Boolean
    "Evaluate certainty or not"
    evaluateCertainty: Boolean
    explications: String
    "MCQ ID"
    id_qcm: ID
    "Question ID"
    id_question: ID
    id_sous_categorie: ID
    isAiGenerated: Boolean
    isAiGenerationError: Boolean
    "Free text"
    isAnswerFreeText: Boolean
    "Multiple choices in long list (select)"
    isAnswerMultipleChoiceInList: Boolean
    "Choice in long list (select)"
    isAnswerUniqueChoiceInList: Boolean
    "Checboxes if true, radio buttons if false"
    isCheckbox: Boolean
    "IsLinkedToCourses"
    isLinkedToCourses: Boolean
    isPublished: Boolean
    "legacy TODO remove after mobile app upgrade (use linkedCours instead)"
    linkCoursId: ID
    linkedCours: [Cours]
    "Si généré par importation de QCM, le mathpixie ID qui a servi à la génération"
    mathpixieId: ID
    maxPoints: Float
    "MCQ scale associated with the question (barême question)"
    mcqScale: McqScale
    "Unused old field"
    name: String
    notions: [Notion]
    "Question order in current MCQ"
    order: Int
    "First parent exercise serie found"
    parentQcm: Qcm
    "All parent exercise series using this exercise"
    parentQcms: [Qcm]
    parentsQuestions: [Question]
    "Posts (comments, for exercises type Schema & fill the blanks)"
    posts: [Post]
    "Question itself"
    question: String
    "Question Number In Mcq (needed only when querying question alone)"
    questionNumberInMcq: Int
    question_de: String
    question_en: String
    question_es: String
    question_it: String
    "Linked schema ID"
    schemaLibraryId: ID
    "Settings (optional, for schemas)"
    settings: JSON
    sousCategorie: UECategory
    successPercent: JSON
    "Alphanumerical type: can be alphanumerical or numerical or null"
    type: String
    "Type(s) de question"
    typeQuestion: [TypeQuestion]
    "Mcq types"
    types: [TypeQcm]
    url_image_explication: String
    url_image_q: String
}

"New type that compile Author and modifications for a question"
type QuestionEditHistory {
    "the linked question"
    question: Question
    "the collection of modification log"
    questionEditHistory: [Log]
}

type QuestionMappingToResolve {
    coursId: ID
    description: String!
    exerciseTypeId: ID
    mcqScaleId: ID
}

"Exercise result"
type QuestionResult {
    answerHistory: QcmStatsQuestion
    erreurs: Int
    id_question: ID
    note: Float
    pointsPerQuestion: Float
    reponsesJustes: Int
}

type QuestionSerieWithCoefficient {
    coefficient: Float
    questionSerie: ExamQuestionSeries
}

type RecaptchaResult {
    action: String
    score: Float
    success: Boolean
}

type ResumeAnalyseReport {
    "Description de la structure"
    description: String
    "Id de la structure"
    id: ID
    "image"
    image: String
    "is a folder or not"
    isFolder: Boolean
    "Nom de la structure"
    name: String
    "Node de progression qui correspond à la moyenne de tous les résultats de la structure choisie"
    structureResume: ProgressNode
    "Type de la structure (ue,cours,category)"
    type: String
    "Node de progression qui correspond à la moeynne de tous les résultats pour l'user choisi"
    userResume: ProgressNode
}

type Review {
    coursId: ID
    createdAt: Date
    dislikes: Int
    id: Int
    lastEditionDate: Date
    likeScore: Int
    likes: Int
    mark: Float!
    review: String
    user: User
    userId: ID!
}

type Revision {
    cours: Cours
    date: Date
    date_diffusion: DateDiffusion
    derniereRevision: Date
    occurence: Int
    premiereRevision: Date
    totalOccurences: Int
}

type RevisionByDay {
    date: Date
    events: [Revision]
}

"Legacy revision module response, will be removed soon. Use exerciseModulePreview instead."
type RevisionTipsResponse {
    allQuestions: [Question]
    allQuestionsCount: Int
    countUndoneExercises: Int
    questions: [Question]
    questionsAnswered: Int
    schemaImagesPreview: [String]
}

type Room {
    building: Building
    buildingId: ID
    createdAt: Date
    description: String
    id: ID!
    image: String
    name: String
    seats: Int
    updatedAt: Date
}

type ScheduledTask {
    createdAt: Date
    elementId: ID
    executionDate: Date
    groupId: ID
    groupsState: [Groupe]
    id: ID
    name: String
    state: JSON
    status: String
    updatedAt: Date
    userId: ID
}

type Schema {
    authorId: Int
    autoExerciseFillInLegendId: ID
    autoExercisePointAndClickId: ID
    createdAt: Date
    deletedAt: Date
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    id: Int
    image: String
    imageCorrection: String
    isPublished: Boolean
    "Les légendes et leurs points"
    legends: JSON
    "Les lignes supplémentaires dessinées"
    lines: JSON
    "Cours lié à ce schema"
    linkedCourses: [Cours]
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    "Les réglages (taille canvas, etc)"
    settings: JSON
    "Le texte ajouté par l'utilisateur"
    text: JSON
    types: [TypeQcm]
    updatedAt: Date
}

type SchemaList {
    count: Int
    schemas: [Schema]
}

type SearchResponse {
    count: Int
    questions: [Question]
}

"Smart MCQ score and performance Aptoria"
type SmartMcqScore {
    performance: Float
    score1: Float
    score2: Float
    score3: Float
}

type Statistique {
    connected: Int
    countEvents: Int
    countExams: Int
    countGroups: Int
    countQuestionsDone: Int
    countUEs: Int
    cours: Int
    fiches: Int
    messages: Int
    notions: Int
    qcms: Int
    registered: Int
    seenClasses: Int
}

"Good and bad answers for given user and notion"
type StatsNotionUser {
    badAnswers: Int
    createdAt: Date
    goodAnswers: Int
    id: ID
    updatedAt: Date
}

type StatsQuestionAnswer {
    answerId: ID
    id: ID
    isGood: Boolean
    statsQuestionId: ID
    value: String
}

"Ligher type that return already pre-processed stats"
type StatsReport {
    "Nombre d'userActifs dans le batch"
    activeUsers: Int
    "La moyenne des progressions de chaque user sans borne temporelle du batch pour donner une progression moyenne "
    allTimeBatchProgress: Float
    "All time - Nombre de cours Unique visible par le batch au total"
    allTimeBatchSeeableClassesIds: Int
    "la médiane des progressions de chaque user sans borne temporelle du batch)"
    allTimeProgressMedian: Float
    "Nombre moyen de séries d'exercice réalisés par le batch et sur la période"
    averageMcqDone: Float
    averageSessionCount: Float
    averageSessionMinutes: Float
    "Nombre de classes unique vu par le batch et sur la période"
    batchUniqueSeenClassesPeriode: Int
    "Nombre de fichier téléchargés par le batch et sur la période"
    downloadedFiles: Int
    "Le nombre total de mcqDone (série d'exercice ?) par le batch et sur la période => pas d'unicité"
    mcqDone: Int
    "Nombre median de file téléchargé par le batch"
    medianDownloadedFiles: Float
    "La moyenne des progression de chaque user Sur la période temporelle donnée"
    periodeBatchProgress: Float
    "la médiane des progressions de chaque user sur la période temporelle donnée"
    periodeProgressMedian: Float
    "Nombre de posts réalisés par le batch"
    postsSent: Int
    "Nombre de questions réalisées par le batch"
    questionsDone: Int
    "Le nombre total de classes vu par le batch et sur la période => pas d'unicité"
    seenClasses: Int
    sessionCount: Int
    sessionMinutes: Int
}

type StepsWithEvents {
    date: Date
    events: [Event]
    hoursMinutes: Date
    j: Int
    name: String
}

type Subscription {
    _: Boolean
    "La souscription qui retourne un index + une questionId , mais qui filtre sur le frontendToken défini"
    generateAiQuestions(frontendToken: String!): SubscriptionOutput
    messageCreated(discussionId: ID!): Message!
}

"Type de retour de la subscription"
type SubscriptionOutput {
    arrayIndex: Int
    errorMessage: String
    eventType: String!
    questionId: Int
}

type TempUserResult {
    id: ID
    paymentSystem: String
}

"Title type, with a name and differents settings"
type Title {
    backgroundColor: String
    color1: String
    color2: String
    createdAt: Date
    fontWeight: Int
    id: ID!
    level: Int
    name: String
    offset: Int
    size: Int
    updatedAt: Date
}

type Token {
    token: String!
}

type TokenWithUser {
    me: User
    token: String!
}

type TotalTimeSpentByUE {
    "seconds"
    time: Int
}

"Type QCM"
type TypeQcm {
    contentType: String
    createdAt: Date
    description: String
    groupes: [Groupe]
    id: ID
    name: String
    updatedAt: Date
}

type TypeQuestion {
    id: ID
    name: String
}

type UE {
    "Children UE"
    children: [UE]
    color: String
    "deprecated, not used anymore, will be deleted"
    color2: String
    "Number of accessibles courses user"
    countAccessiblesCourses: Int
    "Number of accessibles exercices (questions)"
    countAccessiblesExercises: Int
    "Cours dans UE"
    cours: [Cours]
    createdAt: Date
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    exercisesResultsSummary(userId: ID): GoodAnswersStatsUserSynthesis
    groupes: [Groupe]
    id: ID!
    image: String
    "True if it is a folder not a subject"
    isFolder: Boolean
    "Toggle global visibility"
    isVisible: Boolean
    "Last forum post"
    lastPost: Post
    lastPostInAnnale: Post
    lastPostInCours: Post
    lastPostInQcm: Post
    "HTML description, for formation only"
    long_description_html: String
    "Connected user progress stats"
    myProgression: UEProgressionStats
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    notifiedOnNewSubjects: Boolean
    "Display order"
    order: Int
    "Parent UE"
    parent: UE
    "ID of parent UE, if any"
    parentId: ID
    "Number of posts"
    postsNumber: UEPostNumberResponse
    postsWithoutAnswer: [Post]
    qcmFaits(filter: TranscriptFilter): [Qcm]
    "Settings used when ue has formation type (access conditions, etc.)"
    settings: JSON
    totalTimeSpentExercising(userId: ID!): TotalTimeSpentByUE
    "Tutors users"
    tuteurs: [User]
    "Type de categorie : FORMATION ou SUBJECT"
    type: String
    "Categories inside this UE"
    ueCategories: [UECategory]
    unresolvedPosts: [Post]
    updatedAt: Date
    "User progress stats"
    userProgression(userId: ID!): UEProgressionStats
    userQcmFaits(filter: TranscriptFilter, userId: ID!): [Qcm]
}

type UECategory {
    "Children categories"
    children: [UECategory]
    "First color"
    color: String
    countAccessiblesCourses: Int
    countAccessiblesExercises: Int
    "Classes in this category"
    cours: [Cours]
    createdAt: Date
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    exercisesResultsSummary(userId: ID): GoodAnswersStatsUserSynthesis
    "Authorized groups"
    groupes: [Groupe]
    id: ID!
    "Image"
    image: String
    "Is this category visible"
    isVisible: Boolean
    "Last forum post for classes"
    lastPost: Post
    "Last forum post for MCQs"
    lastPostInQcm: Post
    "Connected user's progress"
    myProgression: UECategoryProgressionStats
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    "Number of questions (admin only)"
    nombreQuestions: Int
    "Unlinked number of connections (admin only)"
    nombreQuestionsNonConnectees: Int
    "Show order"
    order: Int
    "Parent categorie"
    parent: UECategory
    "ID of parent category, if any"
    parentId: ID
    "Total number of posts"
    postsNumber: Int
    "Parent UE"
    ue: UE
    updatedAt: Date
    "Specific user progress"
    userProgression(userId: ID!): UECategoryProgressionStats
}

type UECategoryProgressionStats {
    badAnswers: Int
    bonnesReponses: Int
    goodAnswers: Int
    mauvaisesReponses: Int
    moyenneGenerale: Float
    moyenneUser: Float
}

"Formation module"
type UEModule {
    "Number of steps in the module (only for courses with steps)"
    countSteps: Int
    cours: Cours
    "cours id optional"
    coursId: ID
    createdAt: Date
    "module display settings"
    displaySettings: JSON
    element: FormationElement
    "elementId optional"
    elementId: ID
    id: ID
    "order in parent formation"
    order: Int
    "parent formation"
    ueId: ID
    updatedAt: Date
    "module validation settings"
    validationSettings: JSON
}

"General progress for a module in a formation"
type UEModuleProgress {
    "true if module is completed"
    completed: Boolean
    createdAt: Date
    id: ID
    "seconds spent on module"
    seconds: Int
    "number of steps completed (only for courses with steps)"
    stepsCompleted: Int
    "total number of steps (only for courses with steps)"
    totalSteps: Int
    "parent formation"
    ueId: ID
    "module id"
    ueModuleId: ID
    updatedAt: Date
    "user id"
    userId: ID
}

"Detailed progress logs for a module or element in a formation"
type UEModuleProgressLogs {
    "block id if applicable"
    blockId: Int
    completed: Boolean
    createdAt: Date
    id: ID
    "Type of log operation"
    logOperation: String
    "seconds spent on module"
    seconds: Int
    "parent formation"
    ueId: ID
    "module id"
    ueModuleId: ID
    updatedAt: Date
    "user id"
    userId: ID
}

type UEPostNumberResponse {
    annale: Int
    cours: Int
    qcm: Int
    total: Int
}

"Used for user's progress"
type UEProgressionStats {
    badAnswers: Int
    "Seen courses in UE"
    coursVus: Float
    goodAnswers: Int
    "General average"
    moyenneGenerale: Float
    "User average"
    moyenneUser: Float
    "Questions done"
    qcmFaits: Int
    "Total courses in UE"
    totalCours: Float
    "Total questions accessibles by user"
    totalQcm: Int
}

type User {
    addressline1: String
    addressline2: String
    appearsInSubjects: Boolean
    "For Admin/Teachers only"
    appearsInTeam: Boolean
    "User asking for deletion"
    asksForDeletion: Boolean
    avatar: String
    "User's background data"
    background: JSON
    banned: Boolean
    bills: [Bill]
    bio: String
    birthdate: Date
    bot: Boolean
    bot_personality: String
    "User can have childs (if it's a parent)"
    childs: [User]
    city: String
    "companies linked to the user"
    companiesDescriptions(userId: ID): [CompanyDescription]
    country: String
    createdAt: Date
    credits: Int
    deletedAt: Date
    email: String
    "True if user is Exoteach staff (cannot be deleted)"
    exostaff: Boolean
    externalId: String
    firstName: String
    fromUE: [UE]
    gender: String
    groups: [Groupe]
    "If user has accepted last CGU version"
    hasAcceptedCGU: Boolean
    hasSetupPassword: Boolean
    hasSetupUsername: Boolean
    hasValidAvatar: Boolean
    id: ID!
    individualGroup: Groupe
    ip: String
    isActive: Boolean
    isBlocked: Boolean
    "If the user can have extra time (tier-temps)"
    isExtraTime: Boolean
    "Whether user can receive PM from others. If disabled, user will not appears on search"
    isReachableByPrivateMessage: Boolean
    lang: String
    likesReceived: Int
    messages: [Message]
    messagesSent: Int
    myGroupsResponsibility: [Groupe]
    name: String
    nationality: String
    notifications: [Notification]
    "User can have parents (if it's a child)"
    parents: [User]
    parentsEmail: String
    parentsPhone: String
    parentsProfession: String
    "User's phone number"
    phone: String
    phoneCountryCode: String
    postcode: String
    resetPasswordToken: String
    responsibleForUeIds: [ID]
    role: String
    stats(endDate: Date, startDate: Date): UserStats
    statsByDay(endTime: Date, startTime: Date): [UserTimeSpentByDay]
    title: String
    "Auth token version"
    tokenVersion: Int
    totalTimeSpentExercising(filter: UserTimeSpentFilter): [UserTimeSpentExercising]
    updatedAt: Date
    "Additional unique identifier, can be used to import user exercises results"
    userCodeName: String
    "User's mobile devices, for notifications"
    user_devices: [Device]
    "Unique username used to login"
    username: String!
    warningReceived: Int
}

"Return type for handleUserDoUEModule"
type UserModuleProgressLogResponse {
    ueModuleProgressLogId: ID
}

type UserPropertyData {
    createdAt: Date
    element: FormationElement
    "Element ID associé"
    elementId: ID
    "Si les champs remplis sont dans un formulaire, id du formulaire"
    formId: ID
    id: ID!
    updatedAt: Date
    userId: ID
    value: String
    values: [String]
}

"User property folder, used to store user's custom data"
type UserPropertyFolder {
    createdAt: Date
    "Input elements in folder"
    elements: [FormationElement]
    id: ID!
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    updatedAt: Date
}

type UserSearchResult {
    count: Int
    users: [User]
}

type UserStats {
    accountConnections: Int
    downloadedFiles: Int
    likesGiven: Int
    likesReceived: Int
    mcqDone: Int
    postsSent: Int
    privateMessagesReceived: Int
    privateMessagesSent: Int
    questionsDone: Int
    seenAnnales: Int
    seenClasses: Int
    seenMcq: Int
    sessionCount: Int
    sessionMinutes: Int
    totalSeeableClasses: Int
    totalUniqueSeenClasses: Int
    uniqueSeenClasses: Int
    userId: ID
}

type UserTimeSpentByDay {
    "Date of the day"
    date: Date
    id: ID!
    "seconds"
    time: Int
    userId: ID!
}

type UserTimeSpentExercising {
    coursId: ID
    createdAt: Date
    id: ID!
    "seconds"
    time: Int
    ueCategoryId: ID
    ueId: ID
    updatedAt: Date
    userId: ID!
}

type Watermark {
    createdAt: Date
    description: String
    id: Int
    name: String
    order: Int
    settings: JSON
    updatedAt: Date
}

type Webhook {
    createdAt: Date
    events: [String]
    id: ID!
    secret: String!
    updatedAt: Date
    url: String!
}

type WebhookEvent {
    createdAt: Date
    "Nombre d'erreurs associées à l'événement"
    errors: Int
    event: String!
    id: ID!
    payload: JSON
    status: String!
    updatedAt: Date
}

type WebhookEventsStats {
    errors: Int
    pending: Int
    processed: Int
    total: Int
}

type WebhookLog {
    createdAt: Date
    id: ID!
    response: JSON
    status: String
    updatedAt: Date
    webhook: Webhook
    webhookId: ID
}

type WebhookLogsResponse {
    logs: [WebhookLog]
    total: Int
}

enum PostSortModeType {
    MOST_RECENT
    TOP
}

enum UserType {
    ADMIN
    TUTEUR
    USER
}

scalar Date

scalar JSON

scalar Upload

input ActiveUsersFilter {
    companyFilter: [ID]! = []
    coursIdsFilter: [ID]! = []
    endDate: Date!
    groupIdArray: [ID]! = []
    pseudoFilter: String = ""
    roleFilter: [String] = []
    startDate: Date!
}

"Input pour le activeUserGraphFilter => notament ici, comment discrétiser le temps"
input ActiveUsersGraphFilter {
    "Indication on how to discretize the time"
    timeDiscretization: String
}

"Informations supplémentaire associé au text à transformer, dans le cas d'un énoncé de QCM, si l'énoncé est vrai ou faux"
input AdditionalInputJson {
    isTrue: Boolean
}

"Le type de filter à fournir pour avoir les Posts"
input AdminPostSearchFilter {
    "Filtre sur la catégorie à laquelle est relié le Post (Cours, QcmAnswer, Event, Forum)"
    category: [String]
    "Filtre la catégorie Event sur leur type de contenu d'exercice (évènements Médibox, évènements Galien)"
    categoryEventAdvancedFilter: [ID]
    "Filtre la catégorie Exercice sur leur type de contenu d'exercice (Annale, Médibox, Galien)"
    categoryExerciceAdvancedFilter: [ID]
    "Filtre pour que la string 'contentFilter' soit contenu dans le text du Post"
    contentFilter: String
    "Filtre strict sur les cours Id"
    coursIds: [ID]
    endDateCreationFilter: Date
    "Filtre inclusif sur les cours : On va chercher les QcmAnswerID / EventId reliés à ces cours là"
    generalCoursIds: [ID]
    "Boolean pour filtrer si répondu par l'IA ou pas"
    iaAnswered: Boolean
    "Boolean pour filtrer les questions nécessitant une aide humaine"
    isAskingForHumanHelp: Boolean
    "Si il s'agit de la première question"
    isQuestion: Boolean
    "Si il s'agit d'autre chose que la première question"
    isReply: Boolean
    "Si le thread est indiqué comme résolved"
    isResolved: Boolean
    isResolvedByAi: Boolean
    limit: Int!
    "Pagination Options"
    offset: Int!
    "User ids participants"
    participantsUserIds: [ID]
    "Les deux dates de filtre"
    startDateCreationFilter: Date
    "Filtre pour que la string 'titreFilter' soit contenu dans le titre du Post"
    titreFilter: String
    "Filtre sur le type de commantaire ('erreur sur la question',   'La correction est ambigue'   , 'La Question est hors concours')"
    typeIds: [ID]
    "Filtration pour les UserIds ayant écrit les Posts"
    userIds: [ID]
}

input AdminQcmSearchFilter {
    annees: [ID]
    chronometre: Boolean
    coursId: [ID]
    dateCreationEnd: Date
    dateCreationStart: Date
    dateLastModifEnd: Date
    dateLastModifStart: Date
    deleted: Boolean
    isPublished: Boolean
    titre: String
    typeQcms: [ID]
    ueIds: [ID]
    userIds: [ID]
}

input AdminQcmSearchFilterPaginated {
    annees: [ID]
    chronometre: Boolean
    coursId: [ID]
    dateCreationEnd: Date
    dateCreationStart: Date
    dateLastModifEnd: Date
    dateLastModifStart: Date
    deleted: Boolean
    isPublished: Boolean
    limit: Int
    offset: Int
    titre: String
    typeQcms: [ID]
    ueIds: [ID]
    userIds: [ID]
}

input AnalyticsInput {
    time: Float
}

input AnswerInput {
    "Notion auto add"
    autoAddNotions: Boolean
    "Answer explanation"
    explanation: String
    explanation_de: String
    explanation_en: String
    explanation_es: String
    explanation_it: String
    "Explanation image binary"
    image_explanation: Upload
    "All points or nothing"
    isAllPointsOrNothing: Boolean
    "Hors concours"
    isHorsConcours: Boolean
    "Is answer true or false"
    isTrue: Boolean
    "Question ID"
    questionId: ID
    "Answer text"
    text: String
    text_de: String
    text_en: String
    text_es: String
    text_it: String
    "Item image"
    url_image: Upload
}

input ApiKeyInput {
    description: String
    key: String
    name: String
    permissions: JSON
    status: String
    userId: ID
}

input BaseRevisionInput {
    courId: ID
    dateDebut: Date
    dateDiffusionId: ID
}

input BuildingInput {
    address: String
    city: String
    country: String
    image: Upload
    name: String
    postCode: String
}

input CalendarInput {
    color: String
    file: Upload
    name: String
    showOnTheSide: Boolean
    type: String
    url: String
}

input ChallengeConditionInput {
    challengeId: ID
    contentType: String
    coursId: ID
    description: String
    name: String
    qcmId: ID
    settings: JSON
    successConditions: JSON
    type: String
}

input ChallengeInput {
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    folder: String
    image: Upload
    isPublished: Boolean
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    rewards: JSON
    typeIds: [ID]
}

input ChallengeUserProgressFilter {
    challengeId: ID
    coursId: ID
    ueCategoryId: ID
    ueId: ID
    userId: ID
}

"les paramètres nécessaire pour demander à chatGPT de transformer une query"
input ChatGptQueryInput {
    "JSON additionnel qui renseigne sur le context de la demande. Exemple : si on veut améliorer une justification, contiendra la question."
    context: JSON
    "Une string correspondant à des informations custom, exemple : la langue dans laquelle traduire un texte, un input customisé de l'user"
    customInstructions: String = ""
    "Set d'exemple que l'on fourni à chatGPT"
    exemple: String!
    "format de réponse attendu"
    formatAttendu: String!
    "format de réponse donné"
    formatInput: String!
    "La QcmConfigId associé à la demande => permet notament de récupérer l'API KEY"
    gptQcmConfigId: ID!
    "le modèle Id associé à QCMConfigId => permet de récupérer le modèle et ses spécifications depuis QcmConfigId"
    gptQcmModelId: ID!
    "L'objet complexe que l'on demande à chatGPT de changer"
    objectToModify: [ChatGptTextObjectInput!]!
    "description de l'objectif de chatGPT"
    objectif: String!
    "description du rôle de chatGPT"
    role: String!
}

"L'objet complexe de query à chatGPT"
input ChatGptTextObjectInput {
    "Des informations complémentaire associé au text à transformer, notament si c'est un énoncé de QCM, si l'énoncé est vrai ou faux."
    additionalInputJson: AdditionalInputJson
    "Une string correspondante à un ID, dans ce cas, peut ne pas être unique"
    id: String!
    "Le text à transformer "
    text: String!
    "Une string qui correspond à un ID Unique"
    uniqueId: String!
}

input CompletedChallengeFilter {
    ueId: ID
    userId: ID
}

input CoursFilter {
    deleted: Boolean
    layout: String
    limit: Int
    name: String
    offset: Int
    uecategoryId: ID
}

input CoursInput {
    authorId: ID
    customImage: Upload
    date: Date
    deleteCustomImage: Boolean
    deleted: Boolean
    difficulty: Float
    duration: Int
    epub: Upload
    formationId: ID
    gptPrecisionPrompt: String
    groupsToAdd: [ID]
    isAnnale: Boolean
    isEPUBUpdate: Boolean
    isEnAvant: Boolean
    "Si les reviews sont visible par tout le monde"
    isFeedbackVisible: Boolean
    isPDFUpdate: Boolean
    "Si les reviews sont énabled"
    isReviewEnabled: Boolean
    isVisible: Boolean
    layout: String
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    "Show order"
    order: Int
    pdf: Upload
    settings: JSON
    targetCoursId: ID
    "Description text"
    text: String
    text_de: String
    text_en: String
    text_es: String
    text_it: String
    tips: String
    type: String
    ueId: ID
    uecategoryId: ID
    updateInfos: String
    usefulLinks: String
    version: String
    video: String
    views: Int
    workTime: String
}

input CoursSupportInput {
    authorId: ID
    coursId: ID
    name: String
    order: Int
}

input CoursTypesQcmSettingsInput {
    coursId: ID
    coursModuleType: String
    typeQcmId: ID
}

input CreateOrImport {
    acceptSmallErrors: Boolean
    correctionImprovement: Boolean
    coursIdArray: [ID]
    createOrImportFromLocalRessourceData: CreateOrImportFromLocalRessourceData
    createOrImportFromPdf: CreateOrImportFromPdf
    createOrImportFromPictureData: [CreateOrImportFromPictureData!]
    createOrImportFromRawTextData: CreateOrImportFromRawTextData
    customPrompt: String
    exempleExerciseId: [ID]
    exerciceTag: [ID]
    exerciseFormat: String
    exerciseType: [ID]
    flashcardResponseType: String
    ignoreSpecialChar: Boolean
    isCaseSensible: Boolean
    numberOfQuestions: Int
    pixId: ID
    qcmConfigConfigId: String
    qcmConfigModelId: String
    "Optional scaleId, if not renseigned, it will be infered"
    scaleId: ID
}

input CreateOrImportFromLocalRessourceData {
    dataType: String!
    localRessourceUrl: String!
}

input CreateOrImportFromPdf {
    dataType: String!
    file: Upload!
}

input CreateOrImportFromPictureData {
    dataType: String!
    pictureFile: Upload!
}

input CreateOrImportFromRawTextData {
    dataType: String!
    text: String!
}

input CustomFieldsInput {
    elementId: ID
    file: Upload
    value: String
    values: [String]
}

input CustomLinkForfaitInput {
    isPublished: Boolean
    link: String
    name: String
}

input CustomPlanningInput {
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    explanation: String
    explanation_de: String
    explanation_en: String
    explanation_es: String
    explanation_it: String
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    steps: JSON
}

input DateDiffSearchFilter {
    text: String
}

input DateDiffusionInput {
    allDay: Boolean
    availability: String
    buildingId: ID
    calendarId: ID
    courId: ID
    "Custom title (if any, outside ical)"
    customTitle: String
    date: Date
    dateEnd: Date
    eventId: ID
    examSessionId: ID
    hasRecurrence: Boolean
    link: String
    qcmId: ID
    recurrenceRule: JSON
    recurringEndDate: Date
    recurringPeriod: String
    roomId: ID
    show: Boolean
    timezone: String
    updateInfos: String
}

input DiscussionInput {
    authorId: ID
    createdAt: Date
    destinataireId: ID
    image: Upload
    isGroup: Boolean
    name: String
    participants: [ID]
    updatedAt: Date
}

input DisplaySetting {
    type: String
}

"Legacy input type, will be removed"
input EdtInput {
    courId: ID
    date: Date
    deuxieme_cours: String
    premier_cours: String
    qcmId: ID
}

input EmailUserImportSettings {
    body: String
    sendEmail: Boolean
    sendUsernameAndPassword: Boolean
    subject: String
}

input EventFilter {
    folderId: ID
    limit: Int
    offset: Int
}

input EventInput {
    coursIds: [ID]
    customFields: JSON
    customFields_de: JSON
    customFields_en: JSON
    customFields_es: JSON
    customFields_it: JSON
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    folderId: ID
    image: Upload
    link: String
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    organizers: JSON
    showDiscussions: Boolean
    typeIDs: [ID]
}

input ExamFilterInput {
    folderId: ID
    name: String
}

input ExamInput {
    authorId: ID
    color1: String
    color2: String
    date: Date
    dateEnd: Date
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    duration: String
    folderId: ID
    image: String
    isOpen: Boolean
    isPublished: Boolean
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    order: Int
    showFinalResults: Boolean
    typeIds: [ID]
}

input ExamQuestionSeriesInput {
    authorId: ID
    coefficient: Int
    color1: String
    color2: String
    date_begins: Date
    date_end: Date
    description: String
    examId: ID
    examSessionId: ID
    image: String
    mcqId: ID
    name: String
    order: Int
    settings: JSON
    type: String
}

input ExamScaleInput {
    authorId: ID
    description: String
    examId: ID
    isDefault: Boolean
    name: String
}

input ExamScaleQuestionSerie {
    coefficient: Float
    examQuestionSerieId: ID!
    examScaleId: ID!
}

input ExamSessionDoneFilter {
    dateBegin: Date
    dateEnd: Date
    qcmSeriesTypes: [ID]
}

input ExamSessionInput {
    authorId: ID
    date: Date
    description: String
    duration: String
    examId: ID
    isOpen: Boolean
    name: String
    order: Int
    successConditions: JSON
}

input ExerciseModulePreviewFilter {
    coursId: ID
    module: String
    moduleQuickAccessId: ID
    notionId: ID
    schemaAllOrCustom: String
    schemaVariants: [String]
}

input FeedbackToAiAnswerInput {
    feedback: String!
    "message optionnel si refus"
    message: String
    originalThreadPostId: ID
    postId: ID!
}

input FicheInput {
    courId: ID
    deleted: Boolean
    file: Upload
    image: String
    isAccessible: Boolean
    name: String
}

input FileInput {
    courId: ID
    externalLink: String
    file: Upload
    image: String
    messageId: ID
    mimetype: String
    name: String
    notionId: ID
    postId: ID
    type: String
}

"Filter for folders"
input FolderFilter {
    parentId: ID
    type: String
}

"Input for folder"
input FolderInput {
    name: String
    parentId: ID
    type: String
}

input ForfaitInput {
    canExpire: Boolean
    createdAt: Date
    creditCost: Int
    creditGiven: Int
    daysBeforeExpiration: Int
    defaultChecked: Boolean
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    emailSettings: JSON
    folderId: ID
    hasPromoCode: Boolean
    id: ID
    image: Upload
    isLocked: Boolean
    isPublished: Boolean
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    order: Int
    parentId: ID
    paymentSettings: JSON
    price: String
    products: JSON
    promoCodes: JSON
    requiredForfaitId: ID
    requiredGroups: [ID]
    time: Date
    type: String
    updatedAt: Date
}

input ForfaitSearchFilter {
    folderId: ID
    parentId: ID
    requiredForfaitId: ID
    type: String
}

input FormInput {
    backgroundImage: String
    color: String
    isMandatory: Boolean
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    oneAnswerByUser: Boolean
}

input FormResultFilter {
    formId: ID
    userId: ID
}

input FormationBlockInput {
    authorId: ID
    coursId: ID
    createdAt: Date
    formationStepId: ID
    name: String
    order: Int
    settings: JSON
    titleId: ID
    type: String
    updatedAt: Date
}

input FormationElementInput {
    authorId: ID
    "Parent Block in Formation"
    blockId: ID
    "Challenge id"
    challengeId: ID
    "Config ID (for mobile app)"
    configId: ID
    "Correction schema library id elements link (if any)"
    correctionSchemaLibraryId: ID
    "The target element course"
    coursId: ID
    "References associated course (supports secondaires)"
    coursSupportId: ID
    "Element description"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    diapoSyntheseElementAfterId: ID
    diapoSyntheseElementBeforeId: ID
    "Do single exercise ID"
    doQuestionId: ID
    "References associated event"
    eventId: ID
    file: Upload
    "Question footer elements"
    footerQuestionId: ID
    "Forfait ID"
    forfaitId: ID
    "Target form id (if type is form)"
    formId: ID
    formationStepId: ID
    "If element is a global announce, this is the type of global announce"
    globalAnnounceType: String
    gptPrecisionPrompt: String
    "MCQ with element header"
    headerMcqId: ID
    image: Upload
    "If element is accessible by all groups or not"
    isAccessible: Boolean
    mcqId: ID
    "Element name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    objectId: ID
    order: Int
    "Question with element header"
    questionId: ID
    settings: JSON
    "Target event id when element represent an event"
    targetEventId: ID
    "Target exam id when element represent an exam"
    targetExamId: ID
    "Element text content, or link"
    text: String
    text_de: String
    text_en: String
    text_es: String
    text_it: String
    "If this is a title element, references the title type"
    titleId: ID
    type: String
    "If true, the file will be uploaded in the public folder"
    uploadInPublicFolder: Boolean
    userPropertyFolderId: ID
    validationSettings: JSON
}

"Formation type input, not used anymore"
input FormationInput {
    authorId: ID
    color1: String
    color2: String
    coursId: ID
    description: String
    image: Upload
    isPublished: Boolean
    name: String
    order: Int
}

input FormationMappingResolved {
    mappingTitle: [Mapping]
}

input FormationSectionInput {
    authorId: ID
    description: String
    formationId: ID
    image: Upload
    isPublished: Boolean
    name: String
    order: Int
    parentSectionId: ID
}

input FormationStepInput {
    authorId: ID
    description: String
    icon: Upload
    image: Upload
    isPublished: Boolean
    name: String
    order: Int
    sectionId: ID
}

input ForumCategoryInput {
    color: String
    color2: String
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    forumId: ID
    isPinned: Boolean
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    tag: String
    views: Int
}

input ForumInput {
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    image: Upload
    isPinned: Boolean
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    parentId: ID
    tag: String
    views: Int
}

input GPTConfigInput {
    "exoteach user Id"
    aiUserId: ID
    "Les ids des companies associées au bot"
    companyId: [ID]
    frequency_penalty: Int
    "integration config id"
    integrationId: ID
    logit_bias: Int
    max_tokens: Int
    model: String
    presence_penalty: Int
    temperature: Float
    templates: JSON
    top_p: Float
    "chat gpt complemental user name"
    user: String
}

"Il s'agit du type "
input GPTQcmConfigInput {
    "les groupes authorisés à utiliser cette intégration"
    authorizedGroupesIds: [ID]
    "les userIds authorisés à utiliser cette intégration"
    authorizedUserIds: [ID]
    "intégration ID pour retrieve le token d'authorisation"
    integrationId: ID
    "les models et configs des models défini dans cette intégration "
    models: [GptQcmInput]
    "le nom de la config gpt"
    name: String
    "Type (correction ou creation)"
    type: String
}

input GeneratorParams {
    anneeDebut: String
    anneeFin: String
    annees: [ID]
    categoriesIds: [ID]
    coursIds: [ID]
    createQcmSession: Boolean
    goToNextQuestionOnTimerEnd: Boolean
    hasTimer: Boolean
    hasTimerPerQuestion: Boolean
    infiniteQuestionByQuestion: Boolean
    isAnswerOrderRandom: Boolean
    isQuestionOrderRandom: Boolean
    nombreQuestions: String
    secondsPerExercise: Int
    typeQcms: [String]
    ueIds: [ID]
}

input GlobalAnnounceInput {
    id: ID
    text: String
}

input GptQcmInput {
    frequency_penalty: Int
    id: ID
    logit_bias: Int
    max_tokens: Int
    "string correspondant au model, doit être strictement unique et correspondre au nom à utiliser dans l'API chatGPT"
    model: String
    "nom du model"
    name: String
    presence_penalty: Int
    "config de l'API"
    temperature: Float
    top_p: Float
}

input GroupeInput {
    folderId: ID
    image: Upload
    isIndividual: Boolean
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    role: String
}

input HubspotQueryInput {
    hubspotIntegrationId: ID!
    query: String
}

input ImportQcmResolvedMapping {
    formationElementMappingResolved: FormationMappingResolved
    qcmMappingResolved: QcmMappingResolved
    questionMappingResolved: QuestionMappingResolved
}

input ImportQuestionWithAIInput {
    chatGptIntegrationId: ID
    correction: String
    mode: String
    model: String
    prompt: String
    qcmId: ID
    "For mode 1"
    subjectWithCorrection: String
    "For mode 2"
    subjectWithoutCorrection: String
    type: String
}

input ImportWithAiPdfData {
    dataType: String!
    file: Upload!
}

input ImportWithAiPictureData {
    dataType: String!
    pictureFile: Upload!
}

input ImportWithAiRawTextData {
    dataType: String!
    text: String!
}

"Input Fields of LimitRuleInput"
input LimitRuleInput {
    isActive: Boolean
    limitNumber: Int
    timeWindow: Int
    userId: ID
}

input LogFilter {
    apiKeyId: ID
    "Course"
    coursId: ID
    dateBegin: Date
    dateEnd: Date
    logOperation: String
    orderBy: String
    "MCQ serie"
    qcmId: ID
    "MCQ question"
    questionId: ID
    "scale "
    scaleId: ID
    "User"
    userId: ID
}

input Mapping {
    newCoursId: ID
    newExerciseTypeIdArray: [ID]
    newMcqScale: ID
    newTitleId: ID
    "Le UE ID du qcm Pour l'importation"
    newUeId: ID
    oldCoursId: ID
    oldExerciseTypeId: ID
    oldMcqScale: ID
    oldTitleId: ID
    "Le UE Id du QCM avant importation"
    oldUeId: ID
}

input MappingScale {
    questionType: String!
    scaleId: ID!
}

input MassChangesScaleForQuestionsInput {
    mappingScale: [MappingScale!]!
    selectedCoursIds: [ID]!
    selectedQuestionIds: [ID]!
}

input McqScaleInput {
    "Is it default scale"
    isDefault: Boolean
    "Name of the rule"
    name: String
    "Points obtained when nothing is checked, if null, ignored"
    pointsObtainedWhenNothingChecked: Float
    "MCQ or SCQ"
    questionType: String
    "The JSON rules"
    rules: McqScaleRulesInput
    "Type dynamic or manual"
    type: String
    ueIds: [ID]
}

input McqScaleRulesInput {
    flashcardBareme: JSON
    minimumGrade: Float
    "Default or custom notation"
    notation: String
    numberOfErrors: Int
    "Points lost for default notation"
    pointsLostPerError: [Float]
    "Points lost for custom notation: false answer is checked (false)"
    pointsLostPerErrorIfFalseAnswerIsCheckedTrue: [Float]
    pointsLostPerErrorIfFalseAnswerIsCheckedTrueSettings: String
    "Points lost for custom notation: false answer is undefined (null or undefined)"
    pointsLostPerErrorIfFalseAnswerIsUndefined: [Float]
    pointsLostPerErrorIfFalseAnswerIsUndefinedSettings: String
    "Points lost for custom notation: true answer is checked (true)"
    pointsLostPerErrorIfTrueAnswerIsCheckedFalse: [Float]
    "'fixed' or 'variableByTotalAnswers' or 'variableByTrueAnswers'"
    pointsLostPerErrorIfTrueAnswerIsCheckedFalseSettings: String
    "Points lost for custom notation: true answer is undefined (null or undefined)"
    pointsLostPerErrorIfTrueAnswerIsUndefined: [Float]
    pointsLostPerErrorIfTrueAnswerIsUndefinedSettings: String
    pointsPerQuestion: Float
}

input MessageInput {
    discussionId: ID!
    file: Upload
    fileImage: Upload
    "multi fichiers"
    fileList: [Upload]
    isDeleted: Boolean
    tag: String
    text: String
}

input ModuleQuickAccessFilter {
    challengeId: ID
    coursId: ID
    ueCategoryId: ID
    ueId: ID
}

input ModuleQuickAccessInput {
    challengeId: ID
    coursId: ID
    description: String
    name: String
    settings: JSON
    type: String
    ueCategoryId: ID
    ueId: ID
}

input NotifsFilter {
    "cours concernés"
    coursIds: [ID]
    "utilisateur source de notif"
    fromUserId: ID
    limit: Int
    offset: Int
    "Ordre de tri : 'ASC' ou 'DESC' (plus récent ou plus ancien"
    order: String
    "True pour les notifs lues, 'all', 'seen' ou 'unseen'"
    seen: String
    "types de notif"
    types: [String]
}

input NotionGraphFilter {
    showChildsNotion: Boolean
    showParentsNotion: Boolean
}

input NotionInput {
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    formula: String
    image: Upload
    keywords: [NotionKeywordInput]
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
}

input NotionKeywordInput {
    name: String
    notionId: ID
}

"Used to search notions keywords"
input NotionKeywordSearchFilter {
    keywords: String
    name: String
}

"Used to search notions"
input NotionSearchFilter {
    inputSetting: String
    keywords: String
    limit: Int
    name: String
    offset: Int
}

input PaymentFilter {
    beginDate: Date
    endDate: Date
    forfaitIds: [ID]
    isNewUser: Boolean
    limit: Int
    offset: Int
    paymentType: String
    state: String
}

input PaymentInput {
    paymentType: String
    state: String
}

input PostFilter {
    sort: PostSortModeType
    text: String
    year: Int
}

input PostInput {
    answerId: ID
    answeredByAi: Boolean
    courId: ID
    eventId: ID
    "legacy file"
    file: Upload
    "legacy file image"
    fileImage: Upload
    "Multi fichiers"
    fileList: [Upload]
    forumId: ID
    isAskingForHumanHelp: Boolean
    isResolved: Boolean
    isResolvedByAi: Boolean
    likes: Int
    "Mentioned user ids to notify"
    mentionedUserIds: [ID]
    parentId: ID
    postTypeId: ID
    qcmId: ID
    qcmIdQcm: ID
    questionId: ID
    state: String
    tag: String
    text: String
    title: String
    userId: ID
    verifiedBy: ID
    views: Int
}

input PostTypeInput {
    firstAnswerByChatGPT: Boolean
    image: Upload
    name: String
    otherAnswersByChatGPT: Boolean
    precisionPromptForChatGPT: String
    type: String
}

input PreferenceInput {
    appearsInSubjects: Boolean
    appearsInTeam: Boolean
    isActive: Boolean
    limitNumber: Int
    timeWindow: Int
}

input PromoCodeInput {
    code: String
    forfaitId: ID
}

input QcmInput {
    UEId: Int
    annale: Boolean
    annee: Int
    chronoByQuestionOrGlobal: String
    chronometre: Boolean
    correctionConfig: JSON
    date_creation: String
    date_modif: String
    defaultQuestionsTypeQcmIDS: [ID]
    deleted: Boolean
    "MCQ Description"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    difficulty: Float
    "True if correction ai analysis is activated"
    enableAiAnalysis: Boolean
    external: ID
    goToNextQuestionWhenTimesUp: Boolean
    groupQuestionsByTheme: Boolean
    hasCheckboxes: Boolean
    hasExternalQuestions: Boolean
    id_createur: ID
    "if authors has been manually set"
    isAuthorChanged: Boolean
    isFullscreen: Boolean
    isPublished: Boolean
    link: String
    pseudo_createur: String
    public: Boolean
    questionPickingStrategy: String
    "Randomize questions order"
    randomizeQuestions: Boolean
    "Randomize questions order"
    randomizeQuestionsAnswers: Boolean
    shouldResumeTime: Boolean
    "If fullscreen, will show correction at each step"
    showCorrectionAtEachStep: Boolean
    timer_delay: Int
    timesItCanBeDone: Int
    titre: String
    titre_de: String
    "MCQ Title"
    titre_en: String
    titre_es: String
    titre_it: String
    typeQcmIDS: [ID]
    ue: Int
    url_image: String
    views: Int
}

"Le mapping résolu pour l'importation de QCM"
input QcmMappingResolved {
    "Le mapping des UE"
    mappingUe: [Mapping]
    "La nouvelle description du qcm"
    newQcmDescription: String
    "le nouveau Titre du qcm"
    newQcmTitre: String
    "la nouvelle array de question par default"
    newQuestionDefaultTypes: [ID]!
    "La nouvelle Array de Serie Id"
    newSerieTypeId: [ID]!
}

input QcmReponsesInput {
    "Answers checked as 'true'"
    answers: [ID]
    "Answers checked as 'false'"
    answers_false: [ID]
    "Niveau de confiance utilisateur"
    certainty: Int
    "Question ID"
    id_question: Int
    jsonAnswers: JSON
}

input QcmSearchFilter {
    annale: Boolean
    annee: String
    annees: [ID]
    chronometre: Boolean
    coursId: ID
    deleted: Boolean
    isPublished: Boolean
    limit: Int
    offset: Int
    searchText: String
    sousCategorieId: ID
    titre: String
    typeQcms: [ID]
    ue: Int
    ueId: ID
}

input QcmSessionFilter {
    isFinished: Boolean
    userId: ID
}

input QcmSessionInput {
    challengeId: ID
    createdAt: Date
    currentCoursId: ID
    currentState: String
    doneQuestionsCount: Int
    examQuestionSerieId: ID
    examSessionId: ID
    formationElementId: ID
    goodQuestionsCount: Int
    isActive: Boolean
    isFinished: Boolean
    qcmId: ID
    questionsIdsDone: [ID]
    settings: JSON
    updatedAt: Date
    userId: ID
}

"MCQ Question input"
input QuestionInput {
    allowComments: Boolean
    "user ID who created the question"
    authorId: ID
    "Notion auto add"
    autoAddNotions: Boolean
    "Calculated difficulty of the question"
    calculatedDifficulty: Float
    date_creation: Date
    date_modif: Date
    "Admin defined difficulty of the question"
    definedDifficulty: Float
    "If exercise is deletable or not"
    deletable: Boolean
    "Evaluate certainty or not"
    evaluateCertainty: Boolean
    "Explanation text - LEGACY unused field"
    explications: String
    "MCQ ID"
    id_qcm: ID
    id_sous_categorie: ID
    "Explanation Image"
    imageExplication: Upload
    imageQuestion: Upload
    "Free text answer"
    isAnswerFreeText: Boolean
    "Multiple choices in long list (select)"
    isAnswerMultipleChoiceInList: Boolean
    "Choice in long list (select)"
    isAnswerUniqueChoiceInList: Boolean
    "Checboxes if true, radio buttons if false"
    isCheckbox: Boolean
    "Indicate if isLinkedToCourses"
    isLinkedToCourses: Boolean
    isPublished: Boolean
    "Linked course ID"
    linkCoursId: ID
    maxPoints: Float
    "Scale ID for grade calculation"
    mcqScaleId: ID
    "Order number in MCQ"
    order: Int
    "Question text"
    question: String
    question_de: String
    question_en: String
    question_es: String
    question_it: String
    "Schema library ID"
    schemaLibraryId: ID
    "Settings (optional, for schemas)"
    settings: JSON
    "Alphanumerical type: can be alphanumerical or numerical or null"
    type: String
    "Explanation image url"
    url_image_explication: String
    "Image url"
    url_image_q: String
}

input QuestionMappingResolved {
    mappingCours: [Mapping]
    mappingScaleId: [Mapping]
    newExercisesTypes: [Mapping]!
}

input QuestionSearchFilter {
    "Comme linkCoursIds / WithoutLInkedCours/ LinkedToDeletedCourses sont mutuellement exclusif, cette string permet de savoir quel filter choisir : cours, coursless, deletedcours"
    coursPointer: String
    dateCreationBegin: Date
    dateCreationEnd: Date
    dateModifBegin: Date
    dateModifEnd: Date
    "Les types d'exercices que l'on veut avoir : ['Texte libre','QCU','QCM','Alphanumérique','Numérique', 'Texte à trous'] "
    formatExercices: [String]
    id: ID
    inputSetting: String
    isPublished: Boolean
    limit: Int
    linkCoursIds: [ID]!
    name: String
    offset: Int
    "Le chiffre pour la comparaison"
    seriesComparisonNumber: Int
    "Le signe de comparaison à utiliser si seriesFiltration est True"
    seriesComparisonSign: String
    "Indique si on effectue ou pas la filtration sur le nombre de séries linkées"
    seriesFiltration: Boolean
    typeIds: [ID]
    "Les créateurs des exercices"
    userIds: [ID]
}

input ReportPostInput {
    postId: ID
    reason: String!
    userId: ID
}

input ReviewInput {
    coursId: ID
    mark: Float!
    review: String
    userId: ID!
}

input RevisionTipsFilter {
    coursId: ID
    endDate: Date
    notionId: ID
    startDate: Date
}

input RoomInput {
    buildingId: ID
    description: String
    image: Upload
    name: String
    seats: Int
}

input SaveQcmInput {
    formValues: JSON
    qcmId: ID
    sessionId: ID
    time: Int
}

input ScheduledTaskInput {
    elementId: ID
    executionDate: Date
    groupId: ID
    name: String
    state: JSON
    status: String
}

input ScheduledTasksFilter {
    elementId: ID
    executionDate: Date
    groupId: ID
    status: String
    userId: ID
}

input SchemaInput {
    authorId: Int
    deletedAt: Date
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    image: Upload
    imageCorrection: Upload
    isPublished: Boolean
    "Les légendes et leurs points"
    legends: JSON
    "Les lignes supplémentaires dessinées"
    lines: JSON
    "ID cours liés à ce schema"
    linkedCoursesIds: [ID]
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    "Les réglages (taille canvas, etc)"
    settings: JSON
    "Le texte ajouté par l'utilisateur"
    text: JSON
}

input SearchSchemasFilter {
    deleted: Boolean
    "Search by id"
    id: ID
    limit: Int
    "Search by name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    offset: Int
}

input TitleInput {
    backgroundColor: String
    color1: String
    color2: String
    fontWeight: Int
    level: Int
    name: String
    offset: Int
    size: Int
}

input TranscriptFilter {
    dateBegin: Date
    dateEnd: Date
    qcmSeriesTypes: [ID]
}

"Type QCM input"
input TypeQcmInput {
    contentType: String
    description: String
    name: String
}

input TypeQuestionInput {
    name: String
}

input UECategoryInput {
    color: String
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    groupsToAdd: [ID]
    image: Upload
    isVisible: Boolean
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    "Show order"
    order: Int
    "ID of parent UE, if any"
    parentId: ID
    ueId: ID
}

input UEInput {
    color: String
    color2: String
    "Description text"
    description: String
    description_de: String
    description_en: String
    description_es: String
    description_it: String
    groupsToAdd: [ID]
    image: Upload
    "True if it is a folder not a subject"
    isFolder: Boolean
    isVisible: Boolean
    "HTML description, for formation only"
    long_description_html: String
    "The name"
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
    order: Int
    "ID of parent UE, if any"
    parentId: ID
    "Settings used when ue has formation type (access conditions, etc.)"
    settings: JSON
    type: String
}

input UEModuleInput {
    coursId: ID
    displaySettings: JSON
    elementId: ID
    order: Int
    ueId: ID
    validationSettings: JSON
}

input UserCustomPlanningInput {
    customPlanningId: ID!
    j0: Date
}

input UserInput {
    addressline1: String
    addressline2: String
    appearsInSubjects: Boolean
    "For Admin/Teachers only"
    appearsInTeam: Boolean
    asksForDeletion: Boolean
    avatar: String
    banned: Boolean
    bio: String
    birthdate: Date
    bot: Boolean
    bot_personality: String
    city: String
    "company linked to the user. It's becoming mandatory to inform"
    companyId: [ID]
    confirm: String
    country: String
    credits: Int
    deletedAt: Date
    email: String
    firstName: String
    gender: String
    "If user has accepted CGU"
    hasAcceptedCGU: Boolean
    ip: String
    isExtraTime: Boolean
    "If user is reachable by private message"
    isReachableByPrivateMessage: Boolean
    lang: String
    likesReceived: Int
    name: String
    nationality: String
    parentsEmail: String
    parentsPhone: String
    parentsProfession: String
    password: String
    phone: String
    phoneCountryCode: String
    postcode: String
    resetPasswordToken: String
    role: String
    title: String
    userCodeName: String
    username: String
    warningReceived: Int
}

input UserPropertyDataInput {
    elementId: ID
    file: Upload
    "Si les champs remplis sont dans un formulaire, id du formulaire"
    formId: ID
    userId: ID
    value: String
    values: [String]
}

input UserPropertyFolderInput {
    name: String
    name_de: String
    name_en: String
    name_es: String
    name_it: String
}

input UserSearchFilter {
    banned: Boolean
    deleted: Boolean
    email: String
    everywhere: String
    firstName: String
    groupId: ID
    "IDs de groupes"
    groupIds: [ID]
    "type de relation (or, and, not in)"
    groupRelationType: String
    isActive: Boolean
    limit: Int
    name: String
    offset: Int
    role: [String]
    sortField: String
    sortOrder: String
    username: String
}

input UserTimeSpentFilter {
    coursId: ID
    ueCategoryId: ID
    ueId: ID
}

input WatermarkInput {
    description: String
    name: String!
    settings: JSON!
}

input WebhookInput {
    events: [String]
    secret: String
    url: String!
}

input WebhookLogFilter {
    limit: Int
    offset: Int
    status: String
    webhookId: ID
}
