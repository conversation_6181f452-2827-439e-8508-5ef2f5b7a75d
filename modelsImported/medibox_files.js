/* jshint indent: 2 */

module.exports = function(sequelize, DataTypes) {
  return sequelize.define('medibox_files', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      references: {
        model: 'phpbb_users',
        key: 'user_id'
      }
    },
    file_path: {
      type: DataTypes.STRING(200),
      allowNull: false
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP')
    },
    ip_address: {
      type: DataTypes.STRING(39),
      allowNull: false
    },
    host_name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    quad_id: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    }
  }, {
    tableName: 'medibox_files'
  });
};
