const MEDIBACK_GIT = '*****************:mediboxteam/mediback.git'
const apps = require('./apps.js')
const helper = require('./helpers.js');
const HOST = 'be-air.exoteach.com'
const USER = 'debian'
const SUBDOMAIN = 'be-air'

const CURRENT_ECOSYSTEM_PATH = `deployment/ecosystem-${SUBDOMAIN}.config.js`

// NON PROD env
const USER_STAGING = ''
const HOST_STAGING = ''

const DOMAIN = `${SUBDOMAIN}.exoteach.com`;
const htmlFolder = 'html'

const UPLOAD_PATH_QCM = `/var/www/${htmlFolder}/qcm/uploads`
const ENV_PROD = {

  NODE_ENV: 'production',
  CURRENT_ENV: 'production',
  DOMAIN: DOMAIN,
  FRONT_URL: `https://${DOMAIN}/elearning/`, // For sending emails
  DATABASE_HOST: '127.0.0.1',
  DAT<PERSON>ASE: 'exoteach',
  DATABASE_USER: 'root',
  DATABASE_PASSWORD: 'KbqbsJezfXEf',
  SEQUELIZE_DIALECT: 'mysql',
  SECRET: 'BaXsFaR.EeCaB.AibeHW.MpO23',
  ENGINE_API_KEY: '',
  SEQUELIZE_CHARSET: 'utf8mb4',
  WIPE_BDD: 'FALSE', // DO NOT TOUCH
  UPLOAD_PATH_QCM: UPLOAD_PATH_QCM,

  GOOGLE_APPLICATION_CREDENTIALS: 'medibox-marseille-firebase-certificat.json', // Exoteach app
  GOOGLE_APPLICATION_URL: 'https://medibox-marseille.firebaseio.com', // récupérer l'addresse dans le google-services du dossier mediphone/android/app

  S3_BUCKET_NAME: 'be-air.exoteach.com-20ab7692',
  S3_DEV_ACCESS_KEY_ID: '********************',
  S3_DEV_SECRET_KEY: 'wUzfLUpg2KLun7bYlf7FUAVBboENzJrNbbWZ1WCU',
  S3_BUCKET_REGION: 'eu-west-3',
}

const ENV_STAGING = {}
const NODE_HOME_STAGING = `/home/<USER>
const NODE_HOME = `/home/<USER>
module.exports = {
  apps: [
    apps.mediback(ENV_STAGING, ENV_PROD),
    apps.scheduler(ENV_STAGING, ENV_PROD)
  ],
  deploy: {
    production: {
      user: USER,
      host: HOST,
      ref: 'origin/master',
      repo: MEDIBACK_GIT,
      path: NODE_HOME,
      'pre-setup': 'ssh-keyscan bitbucket.org >> ~/.ssh/known_hosts',
      'pre-deploy': helper.preDeploy(ENV_PROD.DATABASE_PASSWORD), // prévient les erreurs de pull
      'ssh_options': ['StrictHostKeyChecking=no'],
      'post-deploy': `npm i && npm run build && npx sequelize-cli db:migrate && pm2 reload ${CURRENT_ECOSYSTEM_PATH} --env production --update-env`,
      env: {
        ...ENV_PROD,
      },
    },
  },
}
