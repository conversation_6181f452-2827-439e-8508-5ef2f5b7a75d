#!/bin/bash
# Setup new deployment from ecosystem file passed as parameter, after ecosystem-PLATFORM.config.js creation
# Usage:  bash deployment/setup.sh deployment/ecosystem-PLATFORM.config.js PLATFORM_DOMAIN.exoteach.com <DBPassword>

# Will clone repository
pm2 deploy $1 production setup
#pm2 deploy $1 production exec "npm rebuild sharp" # rebuild sharp to avoid issues at API launch

bash sql/setupDB.sh $2 $3

# Initial prod deployment
pm2 deploy $1 production --update-env && echo "First deployment complete"