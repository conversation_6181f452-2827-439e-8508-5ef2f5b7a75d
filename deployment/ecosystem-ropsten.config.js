const MEDIBACK_GIT = '*****************:mediboxteam/mediback.git'
const apps = require('./apps.js')
const helper = require('./helpers.js');

const DOMAIN = 'test.exoteach.com';
const HOST = '************'
const USER = 'debian'
const CURRENT_ECOSYSTEM_PATH = 'deployment/ecosystem-ropsten.config.js'

// NON PROD env
const USER_STAGING = ''
const HOST_STAGING = ''

const htmlFolder = 'html'

const UPLOAD_PATH_QCM = `/var/www/${htmlFolder}/qcm/uploads`
const ENV_PROD = {
  NODE_ENV: 'production',
  CURRENT_ENV: 'production',
  DOMAIN: DOMAIN,
  FRONT_URL: `https://${DOMAIN}/elearning/`, // For sending emails
  DATABASE_HOST: 'localhost',
  DATABASE: 'exoteach',
  DATABASE_USER: 'root',
  DATABASE_PASSWORD: '2jP3HPNC2Par',
  SEQUELIZE_DIALECT: 'mysql',
  SECRET: 'RoPsTeN.KeBaB.GhNvnW.MpO23',
  ENGINE_API_KEY: '',
  SEQUELIZE_CHARSET: 'utf8mb4',
  WIPE_BDD: 'FALSE', // DO NOT TOUCH
  UPLOAD_PATH_QCM: UPLOAD_PATH_QCM,

  STRIPE_PK: 'pk_test_51LL3xkF6ZLwJ9dzYjDQFC2nfJRVrl1k2r1lk81wDW1GqIpRnYA3CqsfE7iKzdqgFxghXBFPYajHdS9gxzVLEugwI00uNzLhwSA',
  STRIPE_SK: 'sk_test_51LL3xkF6ZLwJ9dzYwhHjFFqDo7qKWogfDGyl9PuW82AC3goqyxm5IXgipzkgx4mEG440OV7Ei52Pl2uKK8aTyah900XjwvoXoK',
  STRIPE_HOOK_SECRET: 'whsec_10bJE10ACPlEWXcOiUl7jcI6rQV7314a',
  S3_BUCKET_NAME: 'test.exoteach.com-25765986',
  S3_DEV_ACCESS_KEY_ID: '********************',
  S3_DEV_SECRET_KEY: 'wUzfLUpg2KLun7bYlf7FUAVBboENzJrNbbWZ1WCU',
  S3_BUCKET_REGION: 'eu-west-3',
}

const ENV_STAGING = {}
const NODE_HOME_STAGING = `/home/<USER>
const NODE_HOME = `/home/<USER>
module.exports = {
  apps: [
    apps.mediback(ENV_STAGING, ENV_PROD),
    apps.scheduler(ENV_STAGING, ENV_PROD)
  ],
  deploy: {
    production: {
      user: USER,
      host: HOST,
      //ref: 'origin/feature/new-calendar',
      ref:'origin/feature/exo-reorder-elements',
      repo: MEDIBACK_GIT,
      path: NODE_HOME,
      'pre-setup': 'ssh-keyscan bitbucket.org >> ~/.ssh/known_hosts',
      'pre-deploy': helper.preDeploy(ENV_PROD.DATABASE_PASSWORD), // prévient les erreurs de pull
      'ssh_options': ['StrictHostKeyChecking=no'],
      'post-deploy': `npm i --force && npm run build && npx sequelize-cli db:migrate && pm2 reload ${CURRENT_ECOSYSTEM_PATH} --env production --update-env`,
      env: {
        ...ENV_PROD,
        S3_BUCKET_NAME: 'test.exoteach.com-25765986',
        S3_DEV_ACCESS_KEY_ID: '********************',
        S3_DEV_SECRET_KEY: 'wUzfLUpg2KLun7bYlf7FUAVBboENzJrNbbWZ1WCU',
        S3_BUCKET_REGION: 'eu-west-3',},
    },
  },
}
