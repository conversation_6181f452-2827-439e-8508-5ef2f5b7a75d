import pandas as pd
import numpy as np
import math
import random


# =============================
#  IMPORTS
# =============================

# import relation parents enfants (sous forme de dict)
df_parents_and_children = pd.read_csv('parents_and_children.csv', index_col=0)
parents_and_children = df_parents_and_children.T.to_dict('list')
for q in parents_and_children:
	parents_and_children[q] = [int(x) for x in parents_and_children[q] if not math.isnan(x)]

# import relation parents enfants pour les erreurs (sous forme de dict)
df_error_parents_and_children = pd.read_csv('error_parents_and_children.csv', index_col=0)
error_parents_and_children = df_error_parents_and_children.T.to_dict('list')
for q in error_parents_and_children:
    error_parents_and_children[q] = [int(x) for x in error_parents_and_children[q] if not math.isnan(x)]

# import relation double parents enfants (sous forme de dict)
df_double_parents_and_children = pd.read_csv('double_parents_and_children.csv', index_col=0)
double_parents_and_children = df_double_parents_and_children.T.to_dict('list')
for q in double_parents_and_children:
	double_parents_and_children[q] = [int(x) for x in double_parents_and_children[q] if not math.isnan(x)]

# import relation double parents enfants pour les erreurs (sous forme de dict)
df_double_error_parents_and_children = pd.read_csv('double_error_parents_and_children.csv', index_col=0)
double_error_parents_and_children = df_double_error_parents_and_children.T.to_dict('list')
for q in double_error_parents_and_children:
    double_error_parents_and_children[q] = [int(x) for x in double_error_parents_and_children[q] if not math.isnan(x)]

# import liste ordonnée des questions à poser (sous forme de list)
df_ordered_questions_list = pd.read_csv('ordered_questions_list.csv', index_col=0)
ordered_questions_list = list(df_ordered_questions_list['question_id'].values)


# IMPORTANT :
# ------------
# Si de nouvelles questions ont été ajoutées plus récemment (et qu'elles ne figurent donc pas dans ordered_questions_list)
# il faut les ajouter à ordered_questions_list (en les répartissant si possible aléatoirement dans la liste)

# Tout ce dont on a besoin est maintenant dans :
# ----------------------------------------------
# - parents_and_children (dict de list)
# - error_parents_and_children (dict de list)
# - double_parents_and_children (dict de list)
# - double_error_parents_and_children (dict de list)
# - ordered_questions_list (list)


# =============================
#  USEFUL FUNCS
# =============================

# renvoie tous les ancêtres d'une question, sur 1 ou plusieurs générations
def get_ancestors (question_id, nb_generations = 1):
    ancestors = []
    question_ids = [question_id]    
    for i in range(nb_generations):
        for q_id_1 in question_ids:
            for q_id_2 in parents_and_children:
                if q_id_1 in parents_and_children[q_id_2]:
                    ancestors.append(q_id_2) if q_id_2 not in ancestors else ancestors
        question_ids = ancestors.copy()
    return ancestors


# renvoie tous les descendants d'une question, sur 1 ou plusieurs générations
def get_descendants (question_id, nb_generations = 1):
    descendants = []
    question_ids = [question_id]    
    for i in range(nb_generations):
        for q_id_1 in question_ids:
            for q_id_2 in parents_and_children[q_id_1]:
                descendants.append(q_id_2) if q_id_2 not in descendants else descendants
        question_ids = descendants.copy()
    return descendants


# renvoie tous les ancêtres-erreur d'une question, sur 1 ou plusieurs générations
def get_error_ancestors (question_id, nb_generations = 1):
    ancestors = []
    question_ids = [question_id]    
    for i in range(nb_generations):
        for q_id_1 in question_ids:
            for q_id_2 in error_parents_and_children:
                if q_id_1 in error_parents_and_children[q_id_2]:
                    ancestors.append(q_id_2) if q_id_2 not in ancestors else ancestors
        question_ids = ancestors.copy()
    return ancestors


# renvoie tous les descendants-erreur d'une question, sur 1 ou plusieurs générations
def get_error_descendants (question_id, nb_generations = 1):
    descendants = []
    question_ids = [question_id]    
    for i in range(nb_generations):
        for q_id_1 in question_ids:
            for q_id_2 in error_parents_and_children[q_id_1]:
                descendants.append(q_id_2) if q_id_2 not in descendants else descendants
        question_ids = descendants.copy()
    return descendants


# intersection de deux listes
def get_common_elements(list_1, list_2):
    return list(set(list_1).intersection(list_2))


# liste des doublons (valeurs apparaissant 2 fois ou plus)
def get_duplicates(my_list):
    return [item for item in set(my_list) if my_list.count(item) > 1]


# =============================
#  PARAMS DU QUIZ
# =============================

# nb (fixe) de questions posées
nb_questions_asked = 30

# taux moyen de questions supplémentaires posées au hasard parmi toutes les questions restantes et présumées réussies / échouées
# ce pour continuer à récupérer de la data sur les relations parents-enfants et pour que toutes les questions de la base soient posées
# NB : si random_questions_rate = 0.5, on réintègre donc une question au hasard en moyenne une fois sur 2
random_questions_rate = 0.67


# =====================================
#  DEBUT DU QUIZ DE POSITIONNEMENT
# =====================================

# Tout ce dont a besoin de lister au cours du quiz
remaining_questions = ordered_questions_list.copy() # deep copy, on ne veut pas modifier ordered_questions_list !
answered_questions = []
correct_questions = []
lucky_correct_questions = []
failed_questions = []
assumed_correct_questions = []
assumed_failed_questions = []

# juste pour voir si le mec est atypique
assumed_correct_and_failed_questions_count = []

# Boucle principale = tant qu'il reste des questions dans la liste et que le nb questions posées n'a pas atteint nb_questions_asked
while (remaining_questions and len(answered_questions) < nb_questions_asked):

    # on prend la première question restant dans la liste
    question_id = remaining_questions[0]

    # on note qu'on a répondu à cette question
    answered_questions.append(question_id)

    # on supprime la question qui vient d'être posée
    remaining_questions.remove(question_id)

    # tirage aléatoire réussite à la question (1 chance sur 2)
    # à remplacer par la réponse donnée par l'apprenant !
    success = (np.random.uniform() < 0.5)

    # tirage aléatoire niveau de confiance à la question (0, 1, 2 ou 3)
    # à remplacer par le niveau de confiance donné par l'apprenant !
    confidence = random.randrange(4)

    # si succès avec un niveau de confiance >=2
    if (success and confidence >= 2):           

        # ajout à la liste des questions réussies avec haut niveau de confiance
        correct_questions.append(question_id)

        # liste des questions que l'on va présumer réussies à cette étape
        assumed_correct_this_step = []

        # questions enfants de la question, que l'on présume réussies
        assumed_correct_this_step += get_descendants(question_id, nb_generations = 1)

        # contraposée des relations parents enfants sur les erreurs
        assumed_correct_this_step += get_error_ancestors(question_id, nb_generations = 1)

        # enfants issus des doubles probas QA x QB => QC
        # on regarde toutes les autres questions où on l'apprenant a répondu correctement et on présume les enfants de QA x QB réussies
        for correct_question_id in correct_questions:
            if (question_id > correct_question_id):
                key = str(correct_question_id) + 'x' + str(question_id)
            else:
                key = str(question_id) + 'x' + str(correct_question_id)            

            if (key in double_parents_and_children):
                assumed_correct_this_step += double_parents_and_children[key]

        # suppression des doublons
        assumed_correct_this_step = list(set(assumed_correct_this_step))           

        # on retire de la liste des questions présupposées réussies (pour cette étape) celles dejà posées
        assumed_correct_this_step = [q_id for q_id in assumed_correct_this_step if q_id not in answered_questions]
        
        # on retire des questions à poser toutes les questions présumées correctes
        remaining_questions = [q_id for q_id in remaining_questions if q_id not in assumed_correct_this_step]                      
                    
        # on ajoute les questions enfants à la liste les questions présupposées correctes
        assumed_correct_questions += assumed_correct_this_step
        assumed_correct_questions = list(set(assumed_correct_questions))  # suppression des doublons

    # sinon, en cas d'échec ou niveau de confiance nul, la question est jugée échouée
    elif not success or confidence == 0:

        # on ajoute la question à la liste des questions avec réponse fausse
        failed_questions.append(question_id)

        # liste des questions que l'on va présumer échouées à cette étape
        assumed_failed_this_step = []

        # questions enfants-erreur de la questions, que l'on présume échouées
        assumed_failed_this_step += get_error_descendants(question_id, nb_generations = 1)

        # contraposée des relations parents-enfants sur les réussites
        assumed_failed_this_step += get_ancestors(question_id, nb_generations = 1)

        # enfants issus des doubles probas QA x QB => QC sur les erreurs
        # on regarde toutes les autres questions où on l'apprenant a répondu faux et on présume les enfants de QA x QB fausses
        for failed_question_id in failed_questions:
            if (question_id > failed_question_id):
                key = str(failed_question_id) + 'x' + str(question_id)
            else:
                key = str(question_id) + 'x' + str(failed_question_id)            

            if (key in double_error_parents_and_children):
                assumed_failed_this_step += double_error_parents_and_children[key]

        
        # suppression des doublons
        assumed_failed_this_step = list(set(assumed_failed_this_step)) 

        # on retire de la liste des questions présupposées échouées (pour cette étape) celles dejà posées
        assumed_failed_this_step = [q_id for q_id in assumed_failed_this_step if q_id not in answered_questions] 
        
        # on retire des questions à poser toutes ces questions
        remaining_questions = [q_id for q_id in remaining_questions if q_id not in assumed_failed_this_step]

        # on ajoute les questions parentes à la liste les questions présupposées fausses
        assumed_failed_questions += assumed_failed_this_step
        assumed_failed_questions = list(set(assumed_failed_questions))  # suppression des doublons

    # si réussite avec confidence 1, on note ça comme un coup de bol, et on ne fait rien car on juge que cette réponse ne donne pas d'info
    elif success and confidence == 1 : 
        lucky_correct_questions.append(question_id)


    # Réintégration (avec proba random_questions_rate) d'une question au hasard parmi les questions restantes et les questions présumées réussies / échouées
    # ce pour continuer à récupérer de la data sur les relations parents-enfants
    random_questions = assumed_correct_questions + assumed_failed_questions + remaining_questions
    random_questions = list(set(random_questions)) # drop duplicates (a priori y'a pas de doublons mais bon, ceinture et bretelles) 
    
    if (np.random.uniform() < random_questions_rate):
        
        # on tire la question au hasard
        random_question_id = random_questions[random.randrange(len(random_questions))]
        
        # on dégage cette question des questions présumées échouées ou réussies
        assumed_correct_questions = [q_id for q_id in assumed_correct_questions if q_id != random_question_id]
        assumed_failed_questions = [q_id for q_id in assumed_failed_questions if q_id != random_question_id]
        
        # on réintègre cette question en tête des questions à poser
        remaining_questions = [q_id for q_id in remaining_questions if q_id != random_question_id] # on supprimer la question des questions restantes  
        remaining_questions = [random_question_id] + remaining_questions # et on rajoute la question en tête des quesitons à poser


    # Questions indécidables 
    # on regarde à ce stade si des questions sont présumées à la fois réussies et échouée (=indécidables)
    assumed_correct_and_failed_questions = get_common_elements(assumed_correct_questions, assumed_failed_questions)
    assumed_correct_and_failed_questions_count.append(len(assumed_correct_and_failed_questions))

    # on ajoute ces questions indécidables en tête des questions à poser (remaining_questions) afin de pouvoir statuer
    # et on supprime ces questions de assumed_correct_questions et assumed_failed_questions
    if (assumed_correct_and_failed_questions) :
        remaining_questions = assumed_correct_and_failed_questions + remaining_questions
        assumed_correct_questions = [q_id for q_id in assumed_correct_questions if q_id not in assumed_correct_and_failed_questions]
        assumed_failed_questions = [q_id for q_id in assumed_failed_questions if q_id not in assumed_correct_and_failed_questions]


# Boucle secondaire = dans le cas (très rare) où il n'y a plus rien dans remaining questions = il a déjà été possible d'inférer toute les réponses à l'ensemble des questions de la base
# on pose donc le nb de questions qu'il nous reste au hasard parmi assumed_correct_questions et assumed_failed_questions
# le cas échéant cela permettra là aussi de continuer à récupérer de la data sur les relations parents-enfants
if (not remaining_questions and len(answered_questions) < nb_questions_asked):
        
    # nb de questions encore à poser
    nb_questions_left = nb_questions_asked - len(answered_questions)
    questions_to_choose_in = assumed_correct_questions + assumed_failed_questions
    questions_to_choose_in = list(set(questions_to_choose_in)) # a priori pas de doublons mais bon, ceinture et bretelles...
    
    for j in range(nb_questions_left):           
        
        # tirage de la question au hasard parmi les questions restantes
        question_id = questions_to_choose_in[random.randrange(len(questions_to_choose_in))]
        
        # on ajoute la question à la liste des questions auxquelles on a répondu
        answered_questions.append(question_id)            
        
        # on retire la question de celles à poser au coup d'après
        questions_to_choose_in.remove(question_id)
        
        # on retire la question des questions présumées réussies ou échouées (vu qu'on la pose !)
        assumed_correct_questions = [q_id for q_id in assumed_correct_questions if q_id != question_id]
        assumed_failed_questions = [q_id for q_id in assumed_failed_questions if q_id != question_id]    
        
        # tirage aléatoire réussite à la question (1 chance sur 2)
        # à remplacer par la réponse donnée par l'apprenant !
        success = (np.random.uniform() > 0.5)

        # tirage aléatoire niveau de confiance
        # à remplacer par celui donné par l'apprenant
        confidence = random.randrange(4)

        # même traitement que dans la boucle principale pour le classement du résultat de la question
        if (success and confidence >= 2) : 
            correct_questions.append(question_id)
        if (success and confidence == 1) :   
            lucky_correct_questions.append(question_id)
        if (not success or confidence == 0) :    
            failed_questions.append(question_id)



# =====================================
#  DISPLAY RESULTS / SCORES / CHECKS
# =====================================

# results
print("\nnb_questions_total = {} | nb_questions_asked = {} | random_questions_rate = {:.0f}%".format(len(ordered_questions_list), nb_questions_asked, 100.*random_questions_rate))
print("-----------------------------------------------------------------------------------")
print("\nNB QUESTIONS REUSSIES (CONFIANCE 2 ou 3) = {}".format(len(correct_questions)))
print("\nNB QUESTIONS REUSSIES CHANCEUSEMENT (CONFIANCE 1) = {}".format(len(lucky_correct_questions)))
print("\nNB QUESTIONS ECHOUEES (OU REUSSIES AVEC CONFIANCE 0) = {}".format(len(failed_questions)))
print("\nNB QUESTIONS PRESUMEES REUSSIES = {}".format(len(assumed_correct_questions)))
print("\nNB QUESTIONS PRESUMEES ECHOUEES = {}".format(len(assumed_failed_questions)))

quiz_perf = (len(answered_questions) + len(assumed_correct_questions) + len(assumed_failed_questions)) / nb_questions_asked
print("\nPERF DU TEST = NB QUESTIONS DECIDABLES / NB QUESTIONS POSEES  = {:.2f}".format(quiz_perf))

# scores
print("\n\nSCORES\n----------")
print("SCORE SUR LES {} QUESTIONS (pour info) = {:.1f} / 100\n".format(nb_questions_asked, 100.*len(correct_questions)/nb_questions_asked))

nb_decided_questions = len(correct_questions) + len(failed_questions) + len(assumed_correct_questions) + len(assumed_failed_questions)
nb_success = len(correct_questions) + len(assumed_correct_questions)
score_1 = 100. * nb_success / nb_decided_questions
print("SCORE 1 = {:.1f} / 100".format(score_1))

nb_decided_questions = len(correct_questions) + len(lucky_correct_questions) + len(failed_questions) + len(assumed_correct_questions) + len(assumed_failed_questions)
nb_success = len(correct_questions) + 0.5 * len(lucky_correct_questions) + len(assumed_correct_questions)
score_2 = 100. * nb_success / nb_decided_questions
nb_success = len(correct_questions) + len(lucky_correct_questions) + len(assumed_correct_questions)
score_3 = 100. * nb_success / nb_decided_questions
print("SCORE 2 = {:.1f} / 100  (en comptant questions chanceuses avec un coef 0.5)".format(score_2))
print("SCORE 3 = {:.1f} / 100  (en comptant questions chanceuses avec un coef 1.0)\n".format(score_3))


print("ASSUMED CORRECT & FAILED COUNT : ", assumed_correct_and_failed_questions_count)

# checks
print("\nCHECKS\n----------")
print("NB QUESTIONS POSEES :", len(answered_questions), '?=', len(correct_questions) + len(lucky_correct_questions) + len(failed_questions))
print("NB TOTAL QUESTIONS :", len(ordered_questions_list), '?=',  len(answered_questions) + len(assumed_correct_questions) + len(assumed_failed_questions) + len(remaining_questions))
print("sounds good...\n\n")