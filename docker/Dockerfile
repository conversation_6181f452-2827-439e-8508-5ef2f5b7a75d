FROM node:14.18.2-bullseye-slim

# Create app directory
RUN mkdir -p /usr/src/app

WORKDIR /usr/src/app

# Install deps
RUN apt update && DEBIAN_FRONTEND=noninteractive apt-get install -y \
    make g++ libvips imagemagick ghostscript libvips \
    python python3

COPY package*.json /usr/src/app/

ENV NODE_ENV 'development'

# Upgrade npm
RUN npm install -g npm

RUN npm install --quiet

RUN npm install nodemon @babel/core @babel/node @babel/cli @babel/preset-env @babel/register -g

# TODO problème avec sharp en local sur mac ('darwin-arm64v8' binaries cannot be used on the 'darwin-x64' platform)
# RUN rm -rf node_modules/sharp
# RUN npm install --arch=x64 --platform=darwin sharp
# RUN npm install --arch=x64 --platform=linux sharp

COPY . .

EXPOSE 8080 8081 8000

CMD ["npm", "start"]
# ./node_modules/.bin/nodemon --exec babel-node src/index.js