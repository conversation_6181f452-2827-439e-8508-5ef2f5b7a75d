{"name": "root", "version": "1.0.1969", "private": true, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.2.2", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/preset-env": "^7.3.1", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/jest": "^24.0.0", "@types/react": "^16.8.2", "@types/react-dom": "^16.8.0", "@types/react-router-dom": "^4.3.1", "@typescript-eslint/parser": "^1.3.0", "babel-eslint": "^10.0.0", "babel-jest": "^24.0.0", "babel-loader": "^8.0.0", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.0.6", "jest": "^24.0.0", "lerna": "^3.22.1", "source-map-loader": "^0.2.3", "tslint": "^5.12.1", "tslint-config-prettier": "^1.18.0", "tslint-plugin-prettier": "^2.0.1", "tslint-react": "^3.6.0", "typescript": "^3.3.3", "webpack": "^4.0.0", "webpack-cli": "^3.0.0", "webpack-dev-server": "^3.0.0"}, "scripts": {"bootstrap": "lerna bootstrap", "lint": "eslint --cache --ignore-path .gitignore ./packages/*/src/**.jsx", "lint:ts": "npx tslint -c tslint.json -p tsconfig-lint.json --fix", "test": "jest", "build": "npx tsc -b && lerna run build:webpack", "dev": "lerna run dev --stream", "deploy": "gh-pages -d packages/router/dist", "install": "cd ./packages/shared && npm i --legacy-peer-deps && cd ../mediweb && npm i --legacy-peer-deps && cd ../medisupmobile && npm i --legacy-peer-deps cd ../mediphone && npm i --legacy-peer-deps"}}