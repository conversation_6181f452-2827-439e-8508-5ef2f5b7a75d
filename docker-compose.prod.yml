version: '3.3'

services:
  webserver:
    image: nginx:mainline-alpine
    container_name: webserver
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - web-root:/var/www/html
      - ./docker/nginx-conf:/etc/nginx/conf.d
      - certbot-etc:/etc/letsencrypt
      - certbot-var:/var/lib/letsencrypt
      #- dhparam:/etc/ssl/certs
    depends_on:
      - mediback
    networks:
      - app-network
  certbot:
    image: certbot/certbot
    container_name: certbot
    volumes:
      - certbot-etc:/etc/letsencrypt
      - certbot-var:/var/lib/letsencrypt
      - web-root:/var/www/html
    depends_on:
      - webserver
    command: certonly --webroot --webroot-path=/var/www/html --email <EMAIL> --agree-tos --no-eff-email --staging -d example.com  -d www.example.com
  redis:
    image: "redis:alpine"
    ports:
      - '6379:6379'
    networks:
      - app-network
  mediback:
    command: npm run dev
    container_name: mediback
    depends_on:
      - redis
    build:
      context: .
      dockerfile: ./docker/Dockerfile.production
    #network_mode: "host"
    working_dir: /usr/src/app
    ports:
      - 8080:8080
      - 8000:8000
      - 8081:8081
    environment:
      - CURRENT_ENV=$CURRENT_ENV
      - NODE_ENV=$NODE_ENV
      - SECRET=$SECRET
      - DATABASE=$DATABASE
      - DATABASE_URL=$DATABASE_URL
      - DATABASE_LEGACY=$DATABASE_LEGACY
      - FRONT_URL=$FRONT_URL
      - DATABASE_USER=$DATABASE_USER
      - DATABASE_HOST=$DATABASE_HOST
      - DATABASE_PASSWORD=$DATABASE_PASSWORD
      - SEQUELIZE_DIALECT=$SEQUELIZE_DIALECT
      - UPLOAD_PATH_QCM=$UPLOAD_PATH_QCM
      - SECRET=SECRET
    expose:
      - 8000 #graphql
      - 8001 #admin
      - 8080 #express api
    networks:
      - app-network
    restart: unless-stopped

volumes:
  certbot-etc:
  certbot-var:
  web-root:
    driver: local
    driver_opts:
      type: none
      o: bind

networks:
  app-network:
    driver: bridge