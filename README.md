# Mediback

- <PERSON>de, Express.js, <PERSON><PERSON><PERSON>, Apollo Server (GraphQL)

## Installation
* Use Node 18
* `npm config set legacy-peer-deps true`
* `npm i`
* `npm i -g @babel/core @babel/node`
* fill out *.env file* : `cp .env.local .env`
* visit `http://localhost:8000` for GraphQL playground


## Architecture 
* src/
    * graph-api : contains schema, resolvers, services
    * config: app config
    * cron: cron jobs (update users activity...)
    * models: Sequelize models
    * public: express public folder (user avatars)
    * rest: Express REST API for files
    * util: setup
* uploads/ : unexposed uploaded files

## Deploy with pm2
### Setup deployment
- Add your SSH public key to the server authorized key
- Setup new host: `pm2 deploy deployment/ecosystem.config.js production setup` 

###Deploy
- deploy staging auto: `npm run autoDeployStaging`
- exec remote command (check app status): `pm2 deploy deployment/ecosystem.config.js staging exec "pm2 status"`
## Other

####  Graph Manager
service check: 
`npx apollo service:check --endpoint=http://localhost:8000/graphql`

### Cache
- Run redis locally: `docker run --name redis -p 6379:6379 -d redis:alpine`
- install with apt on prod

####Sequelize cheat sheet
hasOne mixins
Prefix: sequelizeHasOne
Methods: get, set, create

hasMany mixins
Prefix: sequelizeHasMany
Methods: get, set, addMany, add, create, remove, removeMany, has, hasMany, count

belongsTo mixins
Prefix: sequelizeBelongsTo
Methods: get, set, create

belongsToMany mixins
Prefix: sequelizeBelongsToMany
Methods: get, set, addMany, add, create, remove, removeMany, has, hasMany, count

### Sequelize migrations

1) Generate empty migration
    - ```npx sequelize-cli migration:generate --name update-user-phone-number ```
2) Write migration
3) Apply migration
    - ```npx sequelize-cli db:migrate ```
    
- Check migration status:
    - ```npx sequelize db:migrate:status```


#Setup production
Debian install (as root)
- ```apt update && apt install -y curl g++ redis-server software-properties-common && curl -sL https://deb.nodesource.com/setup_12.x | sudo bash - ; apt install -y nodejs && npm i -g pm2 && pm2 startup```
- `ssh-keygen` enter enter enter enter, copy public key `cat ~/.ssh/id_rsa.pub` to bitbucket 

Now, you can use node, npm and npx commands.

###Use with Apache example
add proxy for the API and Websockets in sites-enabled/yourConf.conf
```
# Proxy API
ProxyPass "/medibox2-api" "http://localhost:8000"
ProxyPassReverse "/medibox2-api" "http://localhost:8000"

# old way
#ProxyPass "/medibox2-ws" "ws://localhost:8000"
#ProxyPassReverse "/medibox2-ws" "ws://localhost:8000"

# Redirect websockets
RewriteEngine On
RewriteCond %{HTTP:Connection} Upgrade [NC]
RewriteCond %{HTTP:Upgrade} websocket [NC]
RewriteRule ^/medibox2-ws/(.*) ws://localhost:8000/$1 [P,L]
```

Enable apache modules (install with apt if missing)
```
a2enmod proxy && a2enmod proxy_http && a2enmod proxy_wstunnel
service apache2 restart
```

Enable Mysql large indexes on older version:
```
 [mysqld]
 innodb_file_format = Barracuda
 innodb_large_prefix = 1
 innodb_file_per_table = ON
 innodb_default_row_format = 'DYNAMIC'
````

## GraphQL Server Architecture

```mermaid
graph TD
    %% Main Components
    Client["Client"] -- "GraphQL Query/Mutation/Subscription" --> Apollo["Apollo Server"]
    
    Apollo -- "Processes Request" --> Schema["Schema (schemas.js)"]
    Apollo -- "Executes" --> Resolvers["Resolvers (resolvers.js)"]
    Apollo -- "Provides Context" --> Context["Context (me, models, loaders)"]
    Apollo -- "Uses" --> Authorization["Authorization (authorization.js)"]
    
    %% Schema Aggregation
    Schema -- "Imports" --> DomainSchemas["Domain Schemas (*/schema.js)"]
    DomainSchemas -- "Includes" --> TypeDefs["Type Definitions"]
    DomainSchemas -- "Includes" --> Queries["Query Definitions"]
    DomainSchemas -- "Includes" --> Mutations["Mutation Definitions"]
    DomainSchemas -- "Includes" --> Subscriptions["Subscription Definitions"]
    
    %% Resolvers Organization
    Resolvers -- "Imports" --> DomainResolvers["Domain Resolvers (*/resolvers.js)"]
    DomainResolvers -- "Includes" --> QueryResolvers["Query Resolvers"]
    DomainResolvers -- "Includes" --> MutationResolvers["Mutation Resolvers"]
    DomainResolvers -- "Includes" --> SubscriptionResolvers["Subscription Resolvers"]
    DomainResolvers -- "Includes" --> TypeResolvers["Type Resolvers"]
    
    %% Services & Authorization
    Authorization -- "Protects" --> QueryResolvers
    Authorization -- "Protects" --> MutationResolvers
    QueryResolvers -- "Uses" --> DomainServices["Domain Services (*/service.js)"]
    MutationResolvers -- "Uses" --> DomainServices
    SubscriptionResolvers -- "Uses" --> DomainServices
    TypeResolvers -- "Uses" --> DomainServices
    
    %% Data Loaders
    Context -- "Includes" --> DataLoaders["Data Loaders (loaders.js)"]
    DataLoaders -- "Imports" --> DomainLoaders["Domain Loaders (*/loaders.js)"]
    DomainLoaders -- "Optimizes" --> BatchFunctions["Batch Functions"]
    
    %% Domain Modules
    subgraph "Domain Modules (src/graph-api/*)"
        UserModule["User Module"]
        QcmModule["QCM Module"]
        MessageModule["Message Module"]
        CoursModule["Cours Module"]
        OtherModules["36+ Other Domain Modules..."]
    end
    
    %% Domain Module Structure (Example)
    subgraph "Example Domain Module Structure"
        ModuleSchema["domain-schema.js"]
        ModuleResolvers["domain-resolvers.js"]
        ModuleService["domain-service.js"]
        ModuleLoaders["domain-loaders.js (optional)"]
    end
    
    %% Data Access
    DomainServices -- "Uses" --> SequelizeModels["Sequelize Models"]
    BatchFunctions -- "Batch Queries" --> SequelizeModels
    SequelizeModels -- "Queries" --> Database[("MySQL Database")]
    
    class Client client;
    class Apollo,Context,Authorization,DataLoaders server;
    class Schema,DomainSchemas,TypeDefs,Queries,Mutations,Subscriptions schema;
    class Resolvers,DomainResolvers,QueryResolvers,MutationResolvers,SubscriptionResolvers,TypeResolvers resolvers;
    class UserModule,QcmModule,MessageModule,CoursModule,OtherModules,ModuleSchema,ModuleResolvers,ModuleService,ModuleLoaders domain;
    class DomainServices,BatchFunctions,SequelizeModels,Database data;
```

This diagram illustrates the GraphQL server architecture, showing how schemas, resolvers, services, and data loaders are organized across domain modules. Each domain module (like User, QCM, Message) follows a similar structure with schema definitions, resolvers, and services. Authorization rules protect access to resolvers, and data loaders optimize database queries by batching and caching.

## Deployment Architecture

```mermaid
graph TD
    %% Production Environment
    subgraph "Production Server"
        subgraph "PM2 Process Manager"
            direction LR
            PM2Master["PM2 Daemon\n(pm2 CLI)"]
            
            subgraph "Application Instances"
                AppCluster["Mediback API Instances\n(Cluster Mode)"]
                AppCluster1["Instance 1\n(Express + Apollo)"]
                AppCluster2["Instance 2\n(Express + Apollo)"]
                AppCluster3["Instance 3\n(Express + Apollo)"]
                AppCluster4["Instance n\n(Express + Apollo)"]
                
                Scheduler["Scheduler\n(Single Instance)"]
            end
            
            PM2Logs["PM2 Logs"]
            PM2Metrics["PM2 Metrics Dashboard"]
        end
        
        subgraph "External Services"
            MySQL[("MySQL Database")]
            Redis[("Redis Cache")]
            FileStorage["File Storage\n(uploads/)"]
        end
        
        Nginx["Nginx/Apache\n(Reverse Proxy)"]
    end
    
    %% Deployment Process
    subgraph "Deployment Process"
        GitRepo["Git Repository"]
        DeployScript["Deployment Scripts\n(pm2 deploy)"]
    end
    
    %% External Clients
    Clients["Client Applications"]
    
    %% Connections - PM2 Management
    PM2Master -- "Starts/Manages" --> AppCluster
    PM2Master -- "Starts/Manages" --> Scheduler
    AppCluster -- "Contains" --> AppCluster1
    AppCluster -- "Contains" --> AppCluster2
    AppCluster -- "Contains" --> AppCluster3
    AppCluster -- "Contains" --> AppCluster4
    AppCluster1 -- "Writes" --> PM2Logs
    AppCluster2 -- "Writes" --> PM2Logs
    AppCluster3 -- "Writes" --> PM2Logs
    AppCluster4 -- "Writes" --> PM2Logs
    Scheduler -- "Writes" --> PM2Logs
    AppCluster -- "Reports" --> PM2Metrics
    Scheduler -- "Reports" --> PM2Metrics
    
    %% Connections - External Services
    AppCluster1 -- "Connects to" --> MySQL
    AppCluster1 -- "Caches in" --> Redis
    AppCluster1 -- "Reads/Writes" --> FileStorage
    
    %% Connections - Traffic
    Clients -- "HTTP/WS Requests" --> Nginx
    Nginx -- "Load Balances" --> AppCluster
    
    %% Connections - Deployment
    DeployScript -- "Executes" --> PM2Master
    
    %% Styling

    class PM2Master,PM2Logs,PM2Metrics pm2;
    class AppCluster,AppCluster1,AppCluster2,AppCluster3,AppCluster4,Scheduler instance;
    class MySQL,Redis,FileStorage external;
    class Nginx infra;
    class GitRepo,DeployScript,Clients deployment;
```

This deployment diagram illustrates how PM2 manages multiple mediback instances in a production environment:

1. **PM2 Process Manager:**
   - The PM2 daemon controls all application processes
   - Multiple API instances run in cluster mode for load balancing and high availability
   - A single Scheduler instance handles cron jobs and scheduled tasks
   - PM2 provides monitoring through logs and metrics

2. **Application Instances:**
   - Multiple Express/Apollo server instances (the number depends on server CPUs)
   - Each instance handles GraphQL and REST API requests
   - The scheduler runs as a separate process to avoid duplicate scheduled tasks

3. **External Services:**
   - All instances connect to the same MySQL database
   - Redis is used for caching and session storage

4. **Deployment Process:**
   - Code is deployed from the Git repository
   - PM2 deployment scripts handle the update process
   - Zero-downtime deployment is possible with PM2 reload
   - Stateless backend


## REST API Architecture

```mermaid
graph TD
    %% Main Components
    Client["Client Applications"] --> ExpressApp["Express.js Application"]
    
    %% Middleware Chain
    ExpressApp --> Middleware["Middleware Chain"]
    Middleware --> Routes["REST Routes"]
    
    subgraph "Authentication & Authorization"
        ValidateUserRest["validateUserRestRessource"]
        ValidateAPIKey["validateApiKey"]
        ValidateExternalAPIKey["validateExternalApiKey"]
    end
    
    %% Main REST Endpoints
    subgraph "REST Endpoints"
        Files["/files"]
        Users["/users"]
        SSO["/sso"]
        Stripe["/stripe"]
        Notions["/notions"]
        ExternalAPI["/external/v1"]
        Docs["/docs"]
        Static["Static Files"]
    end
    
    %% Controllers & Implementation
    Files --> FilesRouter["filesRouter (files.js)"]
    Users --> UsersRouter["userRouter (users.js)"]
    SSO --> SSORouter["ssoRouter (sso.js)"]
    Stripe --> StripeRouter["stripeRouter (stripe.js)"]
    Notions --> NotionsRouter["notionsRouter (notions.js)"]
    ExternalAPI --> ExternalAPIRouter["externalApi (external/api.js)"]
    Docs --> SwaggerUI["Swagger UI"]
    Static --> PublicDir["PUBLIC_DIR"]
    
    %% Authentication Flow
    Routes --> ValidateUserRest
    Routes --> ValidateAPIKey
    Routes --> ValidateExternalAPIKey
    
    ValidateUserRest --> Files
    ValidateAPIKey --> Users
    ValidateExternalAPIKey --> ExternalAPI
    
    %% File Handling Endpoints
    subgraph "File Handling Endpoints (/files)"
        GetFile["GET /:fileId"]
        GetFiche["GET /fiche/:fileId"]
        GetCours["GET /cours/:fileId"]
        GetMathpixies["GET /pixies/:mathpixieId"]
        GetCoursVideo["GET /coursvideo/:coursId"]
        GetQCMExport["GET /qcm/:qcmId"]
        GetFormExport["GET /form/:formId"]
        GetUserExport["GET /export/:exportId"]
    end
    
    FilesRouter --> GetFile
    FilesRouter --> GetFiche
    FilesRouter --> GetCours
    FilesRouter --> GetMathpixies
    FilesRouter --> GetCoursVideo
    FilesRouter --> GetQCMExport
    FilesRouter --> GetFormExport
    FilesRouter --> GetUserExport
    
    %% Payment Processing
    subgraph "Payment Processing (/stripe)"
        CreateCheckout["POST /create-checkout-session/:id"]
        StripeWebhook["POST /webhook"]
    end
    
    StripeRouter --> CreateCheckout
    StripeRouter --> StripeWebhook
    
    %% User Management
    subgraph "User Management (/users)"
        CreateUser["POST /create"]
        CheckUser["POST /check"]
        AddToGroup["POST /addtogroup"]
        CreateFromMonetico["POST /createuserfrommonetico"]
        GetUserAuthQCM["POST /getuserauthorizationforqcm"]
    end
    
    UsersRouter --> CreateUser
    UsersRouter --> CheckUser
    UsersRouter --> AddToGroup
    UsersRouter --> CreateFromMonetico
    UsersRouter --> GetUserAuthQCM
    
    %% External API
    subgraph "External API (/external/v1)"
        ExternalControllers["Controllers/*.js"]
    end
    
    ExternalAPIRouter --> ExternalControllers
    
    %% Services Used by REST Controllers
    FilesRouter --> Services["Domain Services"]
    UsersRouter --> Services
    StripeRouter --> Services
    NotionsRouter --> Services
    ExternalAPIRouter --> Services
    
    Services --> Models["Sequelize Models"]
    Models --> Database[("MySQL Database")]
    
    %% File Storage
    FilesRouter --> FileSystem["File System\n(uploads/)"]
    
    class Client client;
    class ExpressApp,Middleware express;
    class Routes,Files,Users,SSO,Stripe,Notions,ExternalAPI,Docs,Static route;
    class FilesRouter,UsersRouter,SSORouter,StripeRouter,NotionsRouter,ExternalAPIRouter,SwaggerUI controller;
    class ValidateUserRest,ValidateAPIKey,ValidateExternalAPIKey auth;
    class GetFile,GetFiche,GetCours,GetMathpixies,GetCoursVideo,GetQCMExport,GetFormExport,GetUserExport,CreateCheckout,StripeWebhook,CreateUser,CheckUser,AddToGroup,CreateFromMonetico,GetUserAuthQCM,ExternalControllers endpoint;
    class Services,Models service;
    class Database,FileSystem storage;
```

This diagram illustrates the REST API architecture of the mediback application:

1. **Authentication & Authorization:**
   - Different middleware validators protect different routes
   - `validateUserRestRessource`: Protects file access
   - `validateApiKey`: Protects user management APIs
   - `validateExternalApiKey`: Protects external partner APIs

2. **Main REST Endpoints:**
   - **/files**: Protected file serving (PDFs, videos, exports)
   - **/users**: User management API (creation, validation, group assignment)
   - **/stripe**: Payment processing with Stripe
   - **/sso**: Single Sign-On functionality
   - **/notions**: Notion synchronization between platforms
   - **/external/v1**: External API for partners
   - **/docs**: Swagger documentation
   - **Static Files**: Public resources (avatars, etc.)

3. **File Handling:**
   - Various endpoints for serving different types of files
   - Support for file transformation (e.g., adding watermarks to PDFs)
   - Tracking file access and usage statistics

4. **Payment Processing:**
   - Supports one-time payments and installment plans
   - Webhook handler for asynchronous payment events
   - Integration with Stripe Checkout

5. **Architecture Pattern:**
   - Follows Express.js routing patterns
   - Controllers interact with domain services
   - Domain services interact with Sequelize models
   - Models interact with the MySQL database

