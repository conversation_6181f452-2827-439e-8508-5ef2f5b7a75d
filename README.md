# ExoTeach frontend (web & mobile)
initial

## Développement local

### 1) Installer les dépendances

- In packages/shared: `npm i`
- In packages/mediweb: `npm i`
- In packages/mediphone: `npm i`

### 2) Lancer le frontend

### Web only (Lancer simultanément)
- In packages/shared: `npm run watch:sync`
- (Web version) In packages/mediweb: `npm start`

### Mobile only (Lancer simultanément)
- In packages/shared: `npm run watch:sync`
- (Mobile version) In packages/mediphone: `npm start`


### Important: la plupart du code source est dans le dossier `packages/shared/src`
Le point d'entrée de la version web est dans mediweb.
Le point d'entrée de la version mobile est dans mediphone.
Les deux points d'entrée importent beaucoup de code source de `packages/shared/`.

### Structure de src/:

#### 1) Pages et composants
- components: composants réutilisables
- pages: les pages de l'application, et components spécifiques à ces pages (pas toujours mais c'est mieux)
- pages/account: mise à jour forfait et réglages profil utilisateur
- pages/annales: n'est plus utilisé, il faudra le supprimer
- pages/cours: navigation dans les cours, catégories, matières, et détail de cours
- pages/event: navigation dans évènements
- pages/exam: navigation dans exams
- pages/formations: navigation dans formations et composants associés aux Éléments de formation réutilisés un peu partout
- pages/forum: pages de forum  
- pages/home: page d'accueil, avec contenus du jour, annonces globales, dashboard user  
- pages/messages: messagerie privée  
- pages/notions: notions  
- pages/planning: emploi du temps  
- pages/profile: profil utilisateur, TODO à déplacer  
- pages/progression: Pages progression, relevé de notes, etc.
- pages/qcm: tout ce qui concerne les Exercices, et QCMs, la plus grosse partie probablement
- pages/terms: CGU, mentions légales, etc.
- pages/user: pages de login, register, notifications, etc.
- pages/admin: pages des panneaux admin

#### 2) graphql
- graphql: requêtes/mutations graphql, et fragments utilisés dans les requêtes

#### 3) hooks
- hooks: hooks custom, et hooks Apollo

#### 4) layouts
- layouts: layouts de pages, composants haut niveau (HOC) qui englobent les pages (layout racine BlankLayout, layout page login, et layout principal))
- Contient aussi contexte global de l'application (GlobalContextProvider)

#### 5) services
- services: constantes et fonctions pures, utilitaires, utilisées un peu partout.

#### 6) translations

#### 7) utils
- utils: contient quelques hooks qu'il faudrait déplacer ailleurs, les fonctions utilitaires globales à l'application, la configuration de client apollo.

## Historique du découpage web/mobile
Le projet est divisé en 3 packages :
- shared
- mediphone
- mediweb

shared contient la plupart des fichiers sources de l'application originale. Si un fichier devait être modifié pour une plateforme spécifique, il est retiré de shared et placé dans mediphone et mediweb, avec ses modifications spécifiques. Ce n'est pas l'idéal pour la duplication de code mais cela a permis une intégration des commits plus facile lors du développement en parallèle avec le projet original.

Il est à noter que les packages ne sont pas des node modules. Le package shared est copié dans les fichiers sources de mediweb et mediphone. Cela est la seule solution trouvée à une problématique de contexte. Les packages nodes sont isolés du contexte de l'application, et ne partagent donc pas les variables d'environnements par exemple. Le problème vient qu'UMI n'est pas conçu pour être découpé, et la séparation en node module casse completement l'application. C'est la raison pour laquelle Lerna ne nous sert pas pour la gestion des packages. 

On pourrait utiliser Lerna pour factoriser les dépendances de mediweb et mediphone mais il s'avère que certains packages tel que antd-react supporte mal le hoist de Lerna. Une approche directement par le package-json est recommandé pour faire cela.

Un script npm dans shared est défini afin de synchroniser le package avec mediweb et mediphone : npm sync. il existe également sync:web et sync:phone pour sync uniquement dans les packages concernés.

## Le développement

Le développement ne s'est pas fait avec docker compose, mais avec une base de donnée MySQL locale et un container Redis local géré à l'aide de l'extension docker Microsoft de VS Code.
Une modification du fichier de config a été fait pour héberger l'hôte sur l'ip de la machine sur le réseau afin de pouvoir tester l'application sur le téléphone et le serveur sur l'ordinateur. Ce paramètre est très certainement à changer.
Il est intéressant de noter que chrome permet d'inspecter les téléphones android connecté et obtenir la webview sur l'ordinateur en direct, permettant ainsi d'avoir les outils d'inspection d'une page web normale, et donc de pouvoir éditer les valeur CSS en temps réel, très pratique pour ajuster des glitchs graphiques.

## Le portage app mobile

La solution de portage téléphone s'est porté sur Ionic Capacitor en mode React.
https://capacitorjs.com/docs
Ionic créé un projet de développement mobile en tout point identique à un projet classique de la plateforme visée, en rajoutant un serveur local qui héberge l'application dans une web app. Il y a donc quand même besoin de paramétrer par exemple le manifest android pour des permissions.
packages/mediphone/android/app/src/main/AndroidManifest.xml
Le framework front est toujours antd, et pas la version mobile pour des raisons de compatibilités avec l'existant, il n'y avait jusqu'à présent pas de besoin réel de component mobile.

## Les notifications

L'utilisation des notifications s'est fait au travers d'un module Ionic et Firebase. Les notifications doivent être gérée nativement car 95% du temps, l'application est en réalité fermée, Android étant très agressif sur la fermeture des applications par soucis de consommation de batterie. Même fonctionnement pour iOS.
https://capacitorjs.com/docs/apis/push-notifications
Il est à noter que Firebase Cloud Messaging gère autant le format FCM pour android que APN pour apple. D'ailleurs il s'occupe de la conversion, à l'exception des attributs spécifique à une plateforme. Tout est bien détaillé dans la doc si-dessus.
Les notifications sont gérés par un service côté React (src/services/NotificationService.jsx). C'est un singleton, faute de DI. Un petit trick a du être opéré : Apollo ne donne un accès à son API que par le biais de Hook, et donc à un component. Mais le service de notification ne peut être un component car global et unique à l'application (l'injection dans le layer global ne marche pas à cause de réactualisation intempestives). Donc un dummy component est injecté dans le layer global et instancie le singleton. C'est aussi le component qui récupère les fonctions d'API par Apollo et les fournies au singleton.
L'avantage d'opérer ainsi est multiple : on a une unique instance globale à l'application qui permet de gérer les notifications et contrairement à un web worker, ce service a accès au DOM de l'app et peut donc afficher des pop ups directement.
Il y avait différentes manières d'envoyer des notifications à un utilisateur : par un système de pub sub géré par Firebase, ou par envoie direct à un device par un token d'enregistrement. La deuxième méthode a été retenu pour une meilleure souplesse d'utilisation. Cela nécessite de gérer les tokens en bases mais cela permet aussi une implémentation de la messagerie bien plus facile.
Un service coté serveur a été créé afin d'encapsuler l'API Firebase et permettre un possible changement de technologie futur sans gros changement dans le code.
Le nombre de devices dans la page d'administration compte le nombre de token pour un appareil. Il serait intéressant d'envoyer lors de l'ouverture de session un token d'identification indépendant de la notification spécifique à Android et iOS afin de pouvoir faire des stats d'utilisation iOS et Android.
Il est à noter également que Firebase limite l'envoie de notification à 500 tokens à la fois, donc si on veut envoyer plus de notifications, il faut faire des batchs et envoyer plusieurs requête. Cela n'est à l'heure actuelle pas géré mais je le fait très rapidement.
Documentation Firebase Cloud Messaging :
https://firebase.google.com/docs/cloud-messaging
En général, l'erreur de firebase ENOTFOUND signifie qu'il ne s'est pas authentifier correctement, 100% du temps c'etait du à une mauvaise configuration de la variable d'environnement :

- `export GOOGLE_APPLICATION_CREDENTIALS="link/to/the/auth-file.json"`
 Cela concerne le backend. Seul le backend dialogue avec Firebase, les devices ne font que recevoir. Il est aussi à noter qu'Android n'a besoin de demander les permissions pour recevoir des notifications, iOS oui. 


 ## Cheat sheet
 - `npm run watch:sync` -> shared folder
 - `npm run build` -> mediphone folder
 - `npx cap copy` -> copie le resultat du build dans le dossier res pour l'hébergement local de l'app
 - `npx cap open` -> ouvre le projet via une interface en CLI pour choisir entre ios et android