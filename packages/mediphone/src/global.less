/* @import '~antd/es/style/themes/default.less';*/
@import '~antd-mobile/dist/antd-mobile.css';
// or 'antd-mobile/dist/antd-mobile.less'
@import './shared/assets/animations.less';
@import "./shared/assets/exoquill.less";
@import "./shared/assets/clickableimage.less";
@import "./shared/assets/reorderElements.less";
@import "./shared/components/Vidstack/vidstackStyle.less";

.ant-layout, .ant-layout-footer, body {
  background: #ffffff !important;
}

/*
.mbsc-calendar-body {
  height: 100vh !important;
}
*/

.am-navbar {
  width: 100%;
  position: fixed !important;
  top: env(safe-area-inset-top)
}

.am-tab-bar-bar {
  position: fixed !important;
  padding: env(safe-area-inset-bottom) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
}

.am-tab-bar {
  /*
  padding:
          env(safe-area-inset-top)
          env(safe-area-inset-right)
          env(safe-area-inset-bottom)
          env(safe-area-inset-left);
   */
}

.exoteach-rich-text img {
  max-width: 100%;
  height: auto;
}

// Preview image fullscreen mobile
.ant-image-preview-body {
  top: 46px !important;
}


html,
body,
#root {
  height: 100%;
  /* Default Antd 5 font family, remis ici car non appliqué sur certains éléments mobile */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
  'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
  'Noto Color Emoji';
}

/* Default custom antd variables, doesnt work with custom colors */
//@border-radius-base: 4px;
@btn-border-radius-base: 20px;
@card-radius: 10px;

.ant-btn {
  white-space: normal !important;
  //height: auto !important; // Fix button height on mobile (multi-line) (mais fait bug la height des boutons dans un popover)
}

.colorWeak {
  filter: invert(80%);
}

// Global layout settings
@import './shared/layouts/globalLayout.less';

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*
ul,
ol {
  list-style: none;
}
*/

@media only print {
  .ant-layout-header {
    display: none;
  }
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

// IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

@mainBorderRadius: 20px;
@mediumBorderRadius: 15px;
@lowBorderRadius: 10px;

.ant-card {
  border-radius: 20px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
}

iframe {
  display: block;
  border-style: none;
}

.ant-card-bordered {
  //border: 0 !important;
  //border: 1px solid #dadada !important
}

.ql-snow img {
  max-width: 100%;
}

.ant-card-actions {
  border-bottom-right-radius: @mainBorderRadius;
  border-bottom-left-radius: @mainBorderRadius;
}

/*
// Mieux mais à terminer
.ant-select, .ant-select-selector, .ant-picker {
  border-radius: 15px !important;
}
.ant-select-dropdown {
  border-bottom-left-radius: 15px !important;
  border-bottom-right-radius: 15px !important;
}
*/

/* Fix mobile pages "floating" due to default margin -4px */
.ant-row {
  margin: 0 !important;
}

/*
.ant-form-item {
  margin-bottom: 5px !important;
}
*/

// Fix les messages qui passent sous la navbar
.ant-message-notice {
  margin-top: 50px;
}

.ant-picker, .ant-input-affix-wrapper, .ant-input, .ant-select-selector, .ant-dropdown-menu {
  border-radius: @lowBorderRadius !important;
}

// Quill message
.message-editor-wrapper {
  position: relative;
  border: 1px solid lightgrey !important;

  .ql-tooltip {
    top: 0 !important;
  }
}

.chat-editor-wrapper {
  position: relative;

  .ql-tooltip {
    top: -25px !important;
  }
}

.message-editor-container {
  //z-index: 999;
}

.ql-tooltip {
  border: none !important;
  box-shadow: none !important;
  position: relative !important;
  width: 100%;
}

.message-editor-container .ql-picker-options {
  //z-index: 999;
}

.message-editor-container .ql-toolbar.ql-snow {
  // position: absolute;
  //z-index: 999;
  //bottom: 0;
  width: 100%;
  //transform: translateY(100%);
  //border: 1px solid #e6e6e6 !important;
}

.message-editor-container .ql-container.ql-snow {
  //border: 1px solid #e6e6e6 !important;
  border: none !important;
  //z-index: 999;
}

.ql-editor {
  //min-height: 100px;
}

.ant-drawer-content {
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

// preview pdf
.modalPdf {
  width: fit-content !important;
}

.modalPdf .ant-modal-content {
  width: fit-content;
}

@import './shared/pages/messages/components/Messaging/global.less';

:global .antd-pro-components-global-header-index-action {
  max-width: ~'calc(100vw - 270px)';
}

:global .ant-pro-global-header {
  padding: 0;
}

.ant-breadcrumb {
  padding-left: 10px;
  padding-top: 10px;
}

.ant-comment-nested {
  margin-left: 22px !important;
}

.ant-comment-inner {
  padding: 8px 0 !important;
}

.ant-comment {
  z-index: 3;
}

.ant-comment-content-detail img {
  max-width: 100%;
  margin: auto;
}

.ant-comment-actions {
  margin-top: 4px !important;
}

.ant-list-split .ant-list-header {
  border-bottom: none !important;
}

.ant-list-header .ant-list-header {
  padding-top: 0;
  padding-bottom: 4px;
}

.vertical-bar-comment {
  display: block;
  border-right: 2px solid #d5d5d5 !important;
  height: 100%;
  width: 50%;
}

.vertical-bar-container {
  box-sizing: border-box;
  display: inline-block;
  height: 100%;
  margin-left: 8px;
  vertical-align: top;
  margin-top: 45px;
  width: 16px;
}

.vertical-bar-wrapper {
  bottom: 0;
  left: 0;
  position: absolute;
  top: 0;
  z-index: 0;
}


.ant-layout-content {
  margin: 0 !important;
  /* Always full width */
  min-width: 100% !important;
  /* Fix when content has element with high width (canvas) */
  width: fit-content !important;
}

.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
  background-color: transparent !important;
}

// Mention CSS override
.mention {
  height: 24px;
  width: 65px;
  border-radius: 6px;
  background-color: #c3c3c361 !important;
  padding: 3px 0;
  margin-right: 2px;
  -webkit-user-select: all;
  -moz-user-select: all;
  -ms-user-select: all;
  user-select: all;
}

/* ReorderElementsCorrection.less - Styles for reorder elements correction interface */
.reorder-elements-correction {
  &__container {
    margin-top: 16px;
  }

  &__no-data {
    padding: 16px;
    text-align: center;
    color: #999;
  }

  &__summary-card {
    margin-bottom: 16px;

    .ant-card-body {
      padding: 16px;
    }
  }

  &__score {
    &--perfect {
      color: #52c41a;
    }

    &--partial {
      color: #fa8c16;
    }
  }

  &__comparison-container {
    display: grid;
    gap: 16px;
  }

  &__comparison-title {
    margin: 0 0 12px 0;
    color: #262626;
    font-size: 16px;
    font-weight: 500;
  }

  &__position-group {
    display: grid;
    gap: 8px;
  }

  &__position-label {
    font-size: 14px;
    font-weight: 500;
    color: #595959;
    margin-bottom: 4px;
  }

  &__answers-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  &__answer-section {
    &-label {
      font-size: 12px;
      color: #8c8c8c;
      margin-bottom: 4px;
    }
  }

  &__element {
    padding: 12px;
    border-radius: 6px;
    min-height: 50px;
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid;
    transition: all 0.2s ease;

    &--correct {
      background-color: #f6ffed;
      border-color: #b7eb8f;
    }

    &--incorrect {
      background-color: #fff2f0;
      border-color: #ffccc7;
    }

    &-content {
      flex: 1;
    }

    &-icon {
      font-size: 16px;

      &--correct {
        color: #52c41a;
      }

      &--incorrect {
        color: #ff4d4f;
      }
    }

    &-missing-content {
      color: #999;
      font-style: italic;
    }

    &-tag {
      margin: 0;
    }
  }

  &__legend {
    margin-top: 16px;
    padding: 12px;
    background-color: #fafafa;
    border-radius: 6px;
    font-size: 12px;
    color: #595959;

    &-icon {
      &--correct {
        color: #52c41a;
      }

      &--incorrect {
        color: #ff4d4f;
      }
    }
  }
}

// Responsive adjustments for correction
@media (max-width: 768px) {
  .reorder-elements-correction {
    &__answers-grid {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    &__summary-card {
      .ant-space {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;

        .ant-space-item {
          width: 100%;
        }
      }
    }
  }
}


.dynamic-delete-button {
  position: relative;
  top: 4px;
  margin: 0 8px;
  color: #999;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s;
}

.dynamic-delete-button:hover {
  color: #777;
}

.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

.ant-radio-wrapper {
  word-wrap: normal !important; /* Allow multi-line radio choice */
  display: inline-flex !important; // Align multi line with flexbox
  align-items: flex-start;
}

.ant-radio {
  margin-top: 3px !important; // Fix radio button vertical alignment caused by flex-start
}


.ant-form-item-label > label {
  display: inline-flex !important;
  height: auto !important; // Fix default min height
  align-items: flex-start !important; // Align label for multi line flexbox
}

.ant-checkbox-wrapper {
  word-wrap: normal !important;
  align-items: flex-start;
}

.ant-checkbox {
  margin-top: 3px !important;
}

.ant-form-item-control-input {
  min-height: auto !important;
}

.g6-component-tooltip {
  width: fit-content;
  padding: 10px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.65);
  border-radius: 4px;
  box-shadow: rgb(174, 174, 174) 0 0 10px;
}

.g6-tooltip {
  border: 1px solid #e2e2e2;
  border-radius: 4px;
  font-size: 12px;
  color: #545454;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 5px 5px;
  box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  max-width: 350px;
  //max-height: 300px;
}

.ant-image-img {
  width: 100%;
  max-height: 150px;
  object-fit: contain;
}

.image-description {
  padding: 15px;
  text-align: center;
}

.checkboxExoteach .ant-checkbox {
  display: none !important;
  white-space: normal;
}

.checkboxExoteach .ant-radio {
  display: none !important;
  white-space: normal;
}

.checkboxExoteach .ant-checkbox .ant-checkbox-inner {
  display: none !important;
  white-space: normal;
  width: 100%;
}

// Fix width limitation for item with image
.checkboxExoteach span {
  //display: block !important;
  //width: 100% !important;
}

// Fix notion detection taking all line width
.exo-notion-text span {
  display: inline !important;
}

.qcmAnswerFormItem {
  margin-bottom: 14px !important;
}

.ant-card-head-title {
  white-space: normal !important;
  line-height: 1.2;
}

/* Fix avatar group on mobile, TODO à voir si il faut le virer */
.ant-avatar-group {
  display: block;
}


.selectedCollapseTitle > .ant-collapse-header {
  background-color: #0a80ed;
  color: white !important;
}

.titleWithGradient {
  font-size: 72px;
  font-weight: 700 !important;
  background: -webkit-linear-gradient(45deg, #004fa0, #00add4 80%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.centered-text-on-image {
  position: absolute;
  top: 50%;
  left: 50%;
  color: white;
  transform: translate(-50%, -50%);
}

@import "./shared/assets/mobiscroll-custom.less";


.profile-card-avatar-wrapper {
  .profile-card-avatar-overlay {
    transition: all .3s ease-in-out;
    opacity: 0;
  }
}

.profile-card-avatar-wrapper:hover {
  .profile-card-avatar-overlay {
    opacity: 1;
  }
}

.bigSelector {
  .ant-select-selector {
    min-height: 58px;
  }
}

.lucide {
  width: 1em;
  height: 1em;
  stroke-width: 1.25;
}

.ant-tag > .lucide + span, .ant-tag > span + .lucide {
  margin-inline-start: 7px;
}

.solved-switch .ant-switch-checked {
  background: #F6FFEC !important;
}

.solved-switch .ant-switch-inner-checked {
  color: #389E0E !important;
}


@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.4);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(0, 255, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 0, 0);
  }
}


/* Fix collapse */
.ant-layout-sider-trigger {
  bottom: calc(env(safe-area-inset-bottom) + 50px);
}

/* tag break spaces if too long (important for small screens) */
.ant-tag {
  white-space: break-spaces;
}

.ant-radio-group {
  font-size: inherit !important; // Fix antd text correction exercice QCU not showing text
}

.mbsc-ios.mbsc-eventcalendar .mbsc-calendar-header, .mbsc-ios.mbsc-eventcalendar .mbsc-calendar-week-days {
  background: white !important;
}

.mbsc-ios.mbsc-eventcalendar-schedule .mbsc-calendar-day, .mbsc-ios.mbsc-schedule-wrapper {
  background: white !important;
}

.mbsc-ios.mbsc-eventcalendar .mbsc-calendar-header, .mbsc-ios.mbsc-eventcalendar .mbsc-calendar-week-days {
  background: white !important;
}

// Permet de cacher le bouton download dans le FE Diapo pour les pdf
#diapoViewerWithoutDownload{
  #pdf-download {
    display: none !important;
  }

  #image-renderer {
    background-image: none !important;
  }

  #no-renderer-download {
    display: none !important;
  }
}