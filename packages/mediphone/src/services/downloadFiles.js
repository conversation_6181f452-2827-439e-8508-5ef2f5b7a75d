import { getAuthToken } from '@/shared/utils/authority.js'
import { GlobalConfig } from '@/shared/utils/utils.js';
import request,{ extend } from 'umi-request'
import { Plugins } from '@capacitor/core'
const { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } = Plugins;
const getDownloadTokenUrl = `${GlobalConfig.get().FILES_URL}getdownloadtoken`

import { FileOpener } from '@capacitor-community/file-opener';
import { Filesystem, Directory } from '@capacitor/filesystem';


const downloadAndOpenAndroid = async (url) => {
    try {
      const res = await Filesystem.downloadFile({
        path: 'telechargement.pdf',
        url: url,
        directory: Directory.Documents,
      });
      try {
        let result = await FileOpener.open({
          filePath: res.path,
          contentType: 'application/pdf',
        })
      } catch (error) {
        alert('Erreur lors de l\'ouverture du fichier. Votre fichier a été téléchargé dans le dossier Téléchargements.')
      }

    } catch (error) {
      console.error('There was an error:', error);
    }
}

export const openInBrowser = async (url) => {
  await Browser.open({ url });
}


export const getSupportProtectedS3File = async (url) => {
  try {
    const response = await request(url, {
      method: 'GET',
      responseType: 'json',
      headers: {
        'x-token': getAuthToken()
      }
    });

    return response?.signedUrl;
  } catch (e) {
    console.error(e);
    return null;
  }
};

export const downloadS3File=async (url) => {
  try {
    // 1️⃣ – Appelle l’API qui renvoie l’URL S3 pré-signée
    const response = await request(url, {
      method: 'GET',
      responseType: 'json',
      headers: { 'x-token': getAuthToken() },
    });

    const signedUrl = response?.signedUrl;

    if (typeof signedUrl === 'string' && signedUrl.length) {
      const { platform } = await Device.getInfo();

      if (platform === 'ios' || platform === 'android') {
        await Browser.open({ url: signedUrl });
      } else {
        window.location = signedUrl
      }
      return 'ok';
    }
    return 'ko';

  }catch(e){
    console.error(e)
    return null
  }
}


export const getProtectedFileUrl=async (url)=>{
  try {
    const request = await extend({
      responseType: 'json',
      headers: {
        // 'Access-Control-Allow-Origin': '*',
        'x-token': getAuthToken(),
      },
    })
    const downloadToken = await request(getDownloadTokenUrl)
    if (downloadToken && downloadToken.token) {
      const downloadURI = `${url}?tk=${encodeURI(downloadToken.token)}`

      return downloadURI
    }
    return 'ko'

  } catch (e) {
    console.error(e)
    return 'ko'
  }
}

export const getHrefProtectedFileUrl = async (url) => {
  try {

    let fullUrl=await getProtectedFileUrl(url)
    if (fullUrl !=='ko'){
      const { platform } = await Device.getInfo()

      if (platform === 'ios') {
        await Browser.open({ url: fullUrl });
      } else if(platform === 'android') {
        await downloadAndOpenAndroid(fullUrl);
      } else {
        window.location = fullUrl
      }
      return "ok"
    }else {
      return 'ko'
    }

/*
          Filesystem.downloadFile({
            path: 'telechargement.pdf',
            url: url,
            directory: Directory.Documents,
          }).then((res) => {
            // Open file:
            const filePath = res.path
            const fileOpenerOptions = {
              filePath: filePath,
              contentType: 'application/pdf',
            };

            //this._openFileWithType(res.path, this.attachment.attachmentType);
          });


        const location = await Downloader.download({
          uri: fullUrl,
          mimeType: 'application/pdf',
          visibleInDownloadsUi: true,
          notificationVisibility: NotificationVisibility.Visible,
          destinationInExternalFilesDir: {
            dirType: 'Downloads',
            subPath: 'telechargement.pdf'
          }
        })
        let result = await FileOpener.showOpenWithDialog(location, 'application/pdf')
*/

  } catch (e) {
    console.error(e)
    return null
  }
}
