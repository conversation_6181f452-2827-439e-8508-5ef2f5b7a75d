import { CommentList } from '@/shared/components/Commentaires/CommentList.jsx'
import CreatePost from '@/components/Commentaires/CreatePost.jsx'
import { COMMENT_FILTER, CommentairesType } from '@/shared/services/commentaires.js'
import { Space, Avatar, Card, Divider, Empty, List, Skeleton } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import { useQuery } from '@apollo/client'
import { ErrorResult } from '@/shared/components/ErrorResult'
import { CommentRenderer } from '@/shared/components/Commentaires/CommentRenderer'
import {
  getDataObjectFromType,
  getQueryFromCommentaireType,
  getQueryVariablesFromCommentaireType,
} from '@/shared/services/commentaires'
import MessageOutlined from '@ant-design/icons/lib/icons/MessageOutlined'
import Button from 'antd/es/button'
import Modal from 'antd/es/modal'
import router from 'umi/router.js'
import { useTranslation } from 'react-i18next';

export default function({ type, id, refetch: refetchCours, isDetailPage = false, postId = null, isForum = false, newCommentPosition = 'top', onGetSubject = null }) {
  const {t} = useTranslation();
  const { loading, error, data, refetch } =
    useQuery(getQueryFromCommentaireType(type), {
      variables: getQueryVariablesFromCommentaireType(type, id),
      fetchPolicy: 'no-cache',
      skip: !id,
    })
  const messagesEnd = useRef(null)
  const dataObject = data && !error && getDataObjectFromType(type, data)
  const isCommentListLoaded = () => dataObject && dataObject.length > 0
  const autoNestPosts = (items, itemId = null) =>
    items
      .filter(item => item.parentId === itemId)
      .map(item => (
        { ...item, typeId: id, refetch, children: autoNestPosts(items, item.id) }
      ))

  const getMainPost = (items) => items && items.find(item => item.id === postId)
  const [editAnnonceVisible, setEditAnnonceVisible] = useState(false)
  const [filterType, setFilterType] = useState(COMMENT_FILTER.TOP)

  const CancelComment = () => {
    setEditAnnonceVisible(false)
  }
  const showModalComment = () => {
    setEditAnnonceVisible(true)
  }

  const renderModalCreation = () => (
    <Modal
      footer={null}
      onCancel={CancelComment}
      visible={editAnnonceVisible}
      closable
      confirmLoading={false}
      title={t('AskNewQuestion')}
      style={{
        marginBottom: 10,
        marginLeft: 'auto',
        marginRight: 'auto',
      }}
    >
      <div style={{ fontSize: '15px', color: 'grey' }}>
        {t('CheckExistingQuestion')} <span role="img" aria-label={t('general.emoji')}>😊</span>
      </div>
      <CreatePost
        type={type}
        refetch={refetch}
        typeId={id}
        parentId={null}
        callScroll={() => {
          //scrollToRef(messagesEnd)
        }}
        withTitle
        afterSubmit={() => setEditAnnonceVisible(false)}
      />
    </Modal>
  )

  const mainPost = getMainPost(dataObject)

  useEffect(() => {
    if (dataObject && onGetSubject && isCommentListLoaded()) {
      onGetSubject(mainPost)
    }
  }, [dataObject])

  const [showCommentSectionThread, setShowCommentSectionThread] = useState(false)

  // Thread / Topic page
  const renderDetailPage = () => {
    if (loading) {
      return <Card>{renderLoading()}</Card>
    }
    if (!mainPost) {
      return (
        <Empty description={t('CantFindTopic')}/>
      )
    }
    return (
      <>
        {isCommentListLoaded() && (
          <Card>
            <CommentRenderer
              {...mainPost}
              typeId={id}
              refetch={refetch}
              type={type}
              isDetailPage
            />
            <Divider/>
            {showCommentSectionThread ? (
              <React.Fragment>
                <h4 style={{ marginLeft: 15, marginBottom: -4 }}>Commenter</h4>
                <CreatePost
                  isAnswer
                  type={type}
                  refetch={refetch}
                  typeId={id}
                  parentId={postId}
                  callScroll={() => {
                    //scrollToRef(messagesEnd)
                  }}
                  onCancel={() => setShowCommentSectionThread(false)}
                />
              </React.Fragment>
            ) : (
              <Button type="primary" onClick={() => setShowCommentSectionThread(true)} shape="round"
                      icon={<MessageOutlined/>} size="large">
                Commenter...
              </Button>
            )}

          </Card>
        )}
        {dataObject && dataObject.length === 0 && (
          <Empty description={t('NoTopics')}/>
        )}
        {!dataObject && (
          <Empty description={t('CantFindTopic')}/>
        )}
      </>
    )
  }

  const renderBoutonVoirCoursQcmInForum = () => {
    switch (type) {
      case CommentairesType.COURS:
        return <Button type="ghost" onClick={() => router.push(`/cours/${id}`)}>
          {t('SeeRelatedCourse')}
        </Button>
      case CommentairesType.QCM:
        return <Button type="ghost" onClick={() => router.push(`/qcm/${id}`)}>
          {t('SeeRelatedExercice')}
        </Button>
      default:
        return <></>
    }
  }

  const renderBoutonNouveauSujet = () => (
    <Button type="primary" onClick={showModalComment} shape="round" icon={<MessageOutlined/>} size="middle">
      {t('NewTopic')}
    </Button>
  )

  const renderBoutonPoserQuestion = () => {
    if (isForum) {
      return (
        <div style={{ textAlign: 'center', marginTop: '10px' }}>
          <Space>
            {renderBoutonNouveauSujet()}
            {renderBoutonVoirCoursQcmInForum()}
          </Space>
          {renderModalCreation()}
        </div>
      )
    }
    return (
      <div style={{marginRight: 5, marginLeft: 5}}>
        <Button block type="primary" onClick={showModalComment} shape="round" icon={<MessageOutlined/>} size="large">
          {t('AskQuestion')}
        </Button>
        {renderModalCreation()}
      </div>
    )
  }

  const getCardProps = () => {
    if (isForum) {
      return {
        type: 'inner',
        //className: style.forumCard,
        bodyStyle: { padding: '0px' },
      }
    }
    return {
      bodyStyle: {
        paddingRight: 5, paddingLeft: 5,
      },
    }
  }

  const renderLoading = () => (
    <>
      <Skeleton loading active avatar>
        <List.Item.Meta
          avatar={<Avatar/>}
          title=""
          description=" "
        />
      </Skeleton>
    </>
  )

  return (
    <>
      {/* Sujet ou bouton poser question (dans cours, qcm) */}
      <div style={{ marginBottom: 10 }}>
        {isDetailPage ? renderDetailPage() : renderBoutonPoserQuestion()}
      </div>

      {/* Liste commentaires */}
      <Card {...getCardProps()}>
        {!loading && isCommentListLoaded() && (
          <>
            {dataObject && dataObject.length > 0 ? (
              <CommentList
                comments={autoNestPosts(dataObject, postId)}
                type={type}
                isForum={isForum}
              />
            ) : (
              <Empty description={t('none')}/>
            )}
            <div ref={messagesEnd} style={{ float: 'left', clear: 'both' }}/>
          </>
        )}

        {/* ERROOOOR :'( avec BOUTON pour REESSAAYER */}
        {!loading && error && (
          <ErrorResult refetch={refetch} error={error}/>)
        }
        {/* LOADING liste de réponses */}
        {loading && !error && renderLoading()}
      </Card>
    </>
  )
}
