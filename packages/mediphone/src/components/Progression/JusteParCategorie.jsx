import { getProgressionFromUe } from '@/pages/profile/components/Progression.jsx';
import { Chart } from '@antv/f2';
import { Spin } from 'antd';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

//import image from '../../assets/book.svg'

function calculMax(actual, previous) {
  if (actual > previous) {
    return actual;
  }
  return previous;
}

export default function({ id, ueCategories, type = 'juste', userId }) {
  const { t } = useTranslation();
  const key = id;
  let max = 0;
  const initChart = () => {
    const data = ueCategories.map(ueCategory => {
      let myProgression = getProgressionFromUe(ueCategory, userId);
      //console.log(myProgression)
      const { bonnesReponses, mauvaisesReponses, moyenneUser } = myProgression;
      const { name, image, id } = ueCategory;
      if (type === 'juste') {
        if (moyenneUser && bonnesReponses > 0) {
          max = calculMax(bonnesReponses, max);
          return {
            image,
            name,
            value: parseInt(bonnesReponses)
          };
        }
      } else if (type === 'faux') {
        if (moyenneUser && mauvaisesReponses > 0) {
          max = calculMax(mauvaisesReponses, max);
          return {
            image,
            name,
            value: parseInt(mauvaisesReponses)
          };
        }
      }
      return {};
    });

    const getImage = id => {
      return ueCategories.find(ue => ue.id === id).image;
    };

    const chart = new Chart({
      id: type + key,
      pixelRatio: window.devicePixelRatio
    });
    chart.source(data, {
      score: {
        min: 0,
        max: max * 1.33,
        nice: true,
        tickCount: 4
      }
    });
    chart.legend(false);
    chart.axis('value', false);
    chart.tooltip({
      showMarkers: false
    });
    chart
      .point()
      .position('name*value')
      .size('value')
      .color('name');
    chart.render();
  };

  useEffect(() => {
    if (ueCategories && key) {
      initChart();
    }
  }, [ueCategories, key]);

  /*registerShape(
    'point',
    'image', {
      draw(cfg, container) {
        cfg.points = this.parsePoints(cfg.points)
        const coord = this.coordinate
        container.addShape('line', {
          attrs: {
            x1: cfg.points[0].x,
            y1: cfg.points[0].y,
            x2: cfg.points[0].x,
            y2: coord.start.y,
            stroke: '#ccc',
            lineWidth: 1,
            lineDash: [4, 2],
          },
        })
        return container.addShape('image', {
          attrs: {
            x: cfg.points[0].x - (12 * cfg.size / 2),
            y: cfg.points[0].y - 12 * cfg.size,
            width: 12 * cfg.size,
            height: 12 * cfg.size,
            img: cfg.shape[1],
          },
        })
      },
    })*/

  if (!key) {
    return <Spin />;
  }
  return (
    <React.Fragment>
      <canvas
        width={0.9 * Math.min(window.innerWidth, window.innerHeight)}
        height={0.9 * Math.min(window.innerWidth, window.innerHeight)}
        id={type + key}
      />
    </React.Fragment>
  );
}
