import emptyNotif2 from '@/shared/assets/emptynotif2.svg';
import { ExoPullToRefresh } from '../../components/ExoPullToRefresh.jsx';
import styles from './NoticeList.less';
import ExoAvatar from '@/shared/components/User/ExoAvatar.jsx';
import { displayDirectHtml } from '@/shared/utils/utils.js';
import { useMutation, useQuery } from '@apollo/client';
import classNames from 'classnames';
import {
  GET_MY_NOTIFICATIONS,
  MARK_ALL_NOTIFICATIONS_SEEN,
  SET_NOTIFICATION_SEEN
} from '@/shared/graphql/notifications';
import { connect } from 'dva';
import { blue } from '@ant-design/colors';
import React from 'react';
import { Card, List } from 'antd';
import { Badge, Button, Tabs } from 'antd-mobile';

import { goToNotifTarget } from '@/shared/services/notification';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import NotificationsList from '@/shared/components/NoticeIcon/NotificationsList';

const notifsPage = function(props) {
  const { t } = useTranslation();
  const [
    setNotificationSeen,
    { data: setSeenResult, loading: mutationLoading, error: mutationError }
  ] = useMutation(SET_NOTIFICATION_SEEN);
  const [markAllSeen] = useMutation(MARK_ALL_NOTIFICATIONS_SEEN);
  const { data: { myNotifications = [] } = {}, refetch: refetchNotifs, loading } = useQuery(
    GET_MY_NOTIFICATIONS
  );
  /*
    TODO fetch user + notifs from global redux?
   */
  return (
    <ExoPullToRefresh
      onRefresh={async () => {
        await refetchNotifs();
      }}
    >
      <FullMediParticlesBreadCrumb title={t('MyNotifications')} />
      <Button
        type={'ghost'}
        style={{ margin: 10 }}
        block
        onClick={async () => {
          await markAllSeen();
          await refetchNotifs();
        }}
      >
        {t('MarkAllAsRead')}
      </Button>
      <br />
      <Card bodyStyle={{ paddingRight: 5, paddingLeft: 5, paddingTop: 0 }}>
        <NotificationsList
          data={myNotifications}
          pinnedNotifications
          onClick={async notif => {
            await setNotificationSeen({ variables: { id: notif.id } });
            await refetchNotifs();
            goToNotifTarget(notif);
          }}
          onClear
          title
          onViewMore
          emptyText
          showClear
          clearText
          viewMoreText={null}
          showViewMore
          filter
          setFilter
          loading={loading}
          fullHeight
        />
      </Card>
    </ExoPullToRefresh>
  );
};

const MobileNotificationsList = ({
  data = [],
  onClick,
  onClear,
  title,
  onViewMore,
  emptyText,
  showClear = true,
  clearText,
  viewMoreText,
  showViewMore = false,
  loading = false
}) => {
  const { t } = useTranslation();

  // TODO fetch user + notifs from global redux
  const { data: { myNotifications = [] } = {} } = useQuery(GET_MY_NOTIFICATIONS, {
    pollInterval: 10000,
    skip: data
  });

  if (!data || (data && data.length === 0)) {
    data = myNotifications;
  }

  if (data && data.length === 0) {
    return (
      <div className={styles.notFound}>
        <img src={emptyNotif2} alt={t('general.untreacable')} />
        <div>{emptyText}</div>
      </div>
    );
  }

  const notifsMessagesList = data.filter(notif => notif.type === 'NEW_MESSAGE');
  const notificationsListWithoutMessages = data.filter(notif => notif.type !== 'NEW_MESSAGE');

  const tabs = [
    {
      title: (
        <Badge text={notificationsListWithoutMessages.filter(n => !n.seen)?.length}>
          {t('general.Notifications')}
        </Badge>
      )
    },
    { title: <Badge text={notifsMessagesList.filter(n => !n.seen)?.length}>{t('Messages')}</Badge> }
  ];

  const renderNotifList = list => (
    <>
      <List
        loading={loading}
        className={styles.list}
        dataSource={list}
        renderItem={(item, i) => {
          const itemCls = classNames(styles.item, {
            [styles.read]: item.seen
          }); // eslint-disable-next-line no-nested-ternary
          const leftIcon = (
            <ExoAvatar
              avatar={item.fromUser && item.fromUser.avatar}
              size="large"
              className={styles.avatar}
            />
          );
          return (
            <List.Item
              className={itemCls}
              key={item.key || i}
              onClick={() => onClick && onClick(item)}
            >
              <List.Item.Meta
                className={styles.meta}
                avatar={leftIcon}
                title={
                  <div className={styles.title}>
                    {item.text}
                    <div className={styles.extra}>
                      {item.seen === false && <Badge color={blue} dot />}
                    </div>
                  </div>
                }
                description={
                  <div>
                    <div className={styles.description}>{displayDirectHtml(item.value)}</div>
                    <div className={styles.datetime}>{dayjs(item.updatedAt).fromNow()}</div>
                  </div>
                }
              />
            </List.Item>
          );
        }}
      />
      <div className={styles.bottomBar}>
        {showClear ? <div onClick={onClear}>{clearText}</div> : null}
        {showViewMore ? (
          <div
            onClick={e => {
              if (onViewMore) {
                onViewMore(e);
              }
            }}
          >
            {viewMoreText}
          </div>
        ) : null}
      </div>
    </>
  );
  return (
    <div>
      <Tabs
        tabs={tabs}
        initialPage={0}
        //onChange={(tab, index) => { console.log('onChange', index, tab); }}
        //onTabClick={(tab, index) => { console.log('onTabClick', index, tab); }}
      >
        <div>{renderNotifList(notificationsListWithoutMessages)}</div>

        <div>{renderNotifList(notifsMessagesList)}</div>
      </Tabs>
    </div>
  );
};

export default connect(({ user, global }) => ({
  currentUser: user.currentUser,
  myNotifications: global.myNotifications,
  notificationsCount: global.unreadNotificationsCount,
  collapsed: global.collapsed
}))(notifsPage);
