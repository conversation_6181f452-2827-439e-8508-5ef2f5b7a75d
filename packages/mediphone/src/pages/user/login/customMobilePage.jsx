import { GET_CUSTOM_MOBILE_CONFIG } from "@/shared/graphql/home.js";
import FormationElement from "@/shared/pages/formations/components/FormationElement.jsx";
import { FormationContextProvider } from "@/shared/pages/formations/context/FormationContext.jsx";
import { getCurrentDomain } from "@/shared/services/config.js";
import { useQuery } from "@apollo/client";
import React from "react";
import { useTranslation } from "react-i18next";

export default function(props) {
  const {
    loading: loadingCustomMobileConfig,
    data: dataCustomMobileConfig
  } = useQuery(GET_CUSTOM_MOBILE_CONFIG, {
    fetchPolicy: "no-cache",
    variables: {
      domain: getCurrentDomain()
    }
  });

  const customMobileConfig = dataCustomMobileConfig?.customMobileConfig || {};

  const { t } = useTranslation();
  const elements = customMobileConfig?.elements || [];
  const background = customMobileConfig?.backgroundImage;

  return (
    <div
      style={{
        backgroundColor: "white",
        backgroundImage: `url(${CONFIGS?.serverUrl + background})`,
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        height: "100vh",
        width: "100vw"
      }}
    >
      <FormationContextProvider>
        <div
          style={{
            textAlign: "center",
            paddingTop: "60px",
            paddingLeft: "24px",
            paddingRight: "24px"
          }}
        >
          {elements?.map((elem, k) => (
            <div key={elem?.id}>
              <FormationElement element={elem} />
            </div>
          ))}
        </div>
      </FormationContextProvider>
    </div>
  );
}
