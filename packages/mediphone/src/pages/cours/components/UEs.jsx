import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import useLocalStorage from '@/shared/hooks/useLocalStorage.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import ButtonCreateSubject from '@/shared/pages/cours/components/ButtonCreateSubject.jsx';
import { UEItemsList } from '@/shared/pages/cours/components/ListViews/UEItemsList.jsx';
import { HierarchyElement } from '@/shared/pages/cours/components/PedagogicalHierarchyElements/HierarchyElement.jsx';
import { UE_TYPES } from '@/shared/services/ues.js';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils.js';
import { BookOutlined, ReadOutlined } from '@ant-design/icons';
import React, { Fragment, useContext } from 'react';
import { useQuery } from '@apollo/client';
import { Tabs, Typography } from 'antd';
import { isAdmin } from '@/shared/utils/authority';
import { ErrorResult } from '@/shared/components/ErrorResult';
import { QUERY_MES_UES } from '@/shared/graphql/cours.js';
import { useRefetchOnLoad } from '@/shared/utils/hooks/useRefetchOnLoad.js';
import { useTranslation } from 'react-i18next';
import subjectPlaceholder from '@/shared/assets/SubjectPlaceholder.svg';

export const renderUeImage = image => {
  return (
    <img
      src={
        image ? getUrlProtectedRessource(GlobalConfig.get().FILES_URL + image) : subjectPlaceholder
      }
      style={{ height: '100%', width: '100%', borderRadius: '50%' }}
      alt=""
    />
  );
};

export const UEsLoadingView = ({ loading }) => (
  <HierarchyElement
    title={''}
    description=""
    coursId="1"
    qcmId="1"
    ueId={''}
    color={''}
    order={1}
    loading={loading}
  />
);

const UECardsContent = ({ data, loading, refetch }) => {
  const { t } = useTranslation();

  // Matières ou dossiers racines
  const mesUEs = data?.mesUEs?.filter(ue => ue?.parentId === null);
  // Matières Seulement
  const mySubjects = data?.mesUEs?.filter(ue => ue?.isFolder === false);
  const hasSubjects = mySubjects?.length > 0;

  const { appearance, listOrCardNavigation } = useContext(GlobalContext);
  const hasCustomBackgroundPlatform = !!appearance?.backgroundImagePlatform;

  const [tabSelected, setTabSelected] = useLocalStorage('tab-libraryOrSubjects-selected', '1');

  const titleProps = {
    level: 2,
    style: {
      marginLeft: '20px',
      color: hasCustomBackgroundPlatform ? 'white' : ''
    }
  };

  const renderContent = items => {
    const hasItems = items?.length > 0;
    return (
      <>
        {hasItems && (
          <>
            <div
              style={
                listOrCardNavigation === 'Card'
                  ? {
                      display: 'flex',
                      alignContent: 'center',
                      margin: 'auto',
                      gap: '30px',
                      flexWrap: 'wrap',
                      justifyContent: 'center'
                    }
                  : {
                      //display: 'flex',
                      //flexWrap: 'wrap',
                      //alignContent: 'center',
                    }
              }
            >
              <UEItemsList ues={items} refetch={refetch} loading={loading} />
            </div>
          </>
        )}
      </>
    );
  };

  // A accès a au moins un cours, on affiche les Dossiers ou Matières accessibles
  return (
    <>
      <div style={{ textAlign: 'center', margin: 'auto', width: '100%' }}>
        <Tabs
          centered
          defaultActiveKey={tabSelected}
          onChange={activeKey => setTabSelected(activeKey)}
          size="large"
          style={{ display: 'flex', justifyContent: 'center' }}
        >
          <Tabs.TabPane
            tab={
              <>
                <BookOutlined />
                {t('MySubjects')}
              </>
            }
            key="1"
            style={{ margin: 'auto', textAlign: 'center', justifyContent: 'center' }}
          >
            <Typography.Title {...titleProps}>{t('MySubjects')}</Typography.Title>
            {renderContent(mySubjects)}
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <>
                <ReadOutlined />
                {t('MyLibrary')}
              </>
            }
            key="2"
            style={{ margin: 'auto', textAlign: 'center', justifyContent: 'center' }}
          >
            <Typography.Title {...titleProps}>{t('MyLibrary')}</Typography.Title>
            {renderContent(mesUEs)}
          </Tabs.TabPane>
        </Tabs>
      </div>

      {/* Créer matière ou dossier depuis racine */}
      {isAdmin() && (
        <div
          style={{
            marginTop: 16,
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'center',
            gap: '10px'
          }}
        >
          <ButtonCreateSubject refetch={refetch} />
          <ButtonCreateSubject refetch={refetch} shouldCreateFolder />
        </div>
      )}
    </>
  );
};

export const UEs = props => {
  const { loading, error, data, refetch } = useQuery(QUERY_MES_UES, {
    fetchPolicy: 'cache-and-network'
  });

  return (
    <Fragment>
      {/* DONE LOADING */}
      {loading && !error && !data && <SpinnerCentered />}
      {data?.mesUEs && <UECardsContent data={data} loading={loading} refetch={refetch} />}
      {/* ERROOOOR :'( */}
      {!loading && !data && error && <ErrorResult refetch={refetch} error={error} />}
    </Fragment>
  );
};
