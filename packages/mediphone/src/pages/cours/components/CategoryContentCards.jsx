import { QUERY_UE_CATEGORY_ID_WITH_CHILDREN } from '@/shared/graphql/cours.js';
import {ButtonCreateCategoryOrFolder} from '@/shared/pages/cours/components/ButtonCreateCategoryOrFolder.jsx';
import ButtonCreateCourse from '@/shared/pages/cours/components/ButtonCreateCourse.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import ButtonCreatePage from '@/shared/pages/cours/components/ButtonCreatePage.jsx';
import ButtonImportCourse from '@/shared/pages/cours/components/ButtonImportCourse.jsx';
import { CategoryCard } from '@/shared/pages/cours/components/PedagogicalHierarchyElements/CategoryCard.jsx';
import { CoursCard } from '@/shared/pages/cours/components/PedagogicalHierarchyElements/CoursCard.jsx';
import { useQuery } from '@apollo/client';
import { Empty } from 'antd';
import { isAdmin } from '@/shared/utils/authority';
import { ErrorResult } from '@/shared/components/ErrorResult';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { SpringListItemTransition } from '@/shared/assets/transitions/SpringListItemTransition';

/* Dans une catégorie, affiche catégories filles et cours */
export const CategoryContentCards = ({ loading, error, data, refetch, categoryId, ueType }) => {
  const { t } = useTranslation();
  const { listOrCardNavigation } = useContext(GlobalContext);
  const ueCategory =
    !error && data && data.coursInUECategory && data.coursInUECategory[0]
      ? data.coursInUECategory[0]
      : null;

  // Query category and children
  const {
    data: dataCurrentCategory,
    loading: loadingCurrentCategory,
    refetch: refetchCategoryWithChildren
  } = useQuery(QUERY_UE_CATEGORY_ID_WITH_CHILDREN, {
    variables: { id: categoryId },
    fetchPolicy: 'cache-and-network'
  });
  const currentCategory = dataCurrentCategory?.ueCategory;
  const childrenCategories = currentCategory?.children;
  const parentUe = currentCategory?.ue;

  const color = parentUe?.color || currentCategory?.parent?.ue?.color;
  const color2 = parentUe?.color2 || currentCategory?.parent?.ue?.color;

  const renderCours = (
    cours, // Current
    targetCours // Original
  ) => (
    <React.Fragment key={cours?.id}>
      {(cours.isVisible || isAdmin()) && (
        <div style={{ display: 'flex', width: listOrCardNavigation === 'List' ? '100%' : 'auto' }}>
          <CoursCard
            cours={cours}
            targetCours={targetCours}
            color={color}
            color2={color2}
            refetch={refetch}
            ueType={ueType}
          />
        </div>
      )}
    </React.Fragment>
  );

  const loadingSomething = (loading || loadingCurrentCategory) && (!data || !dataCurrentCategory);

  /* Reload everything */
  const refetchAll = () => {
    refetch();
    refetchCategoryWithChildren();
  };

  const hasNothingInCategory =
    !loading && !error && data?.coursInUECategory?.length === 0 && childrenCategories?.length === 0;

  return (
    <>
      {/* <QueueAnim type={['top', 'down']}>*/}
      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: listOrCardNavigation === 'Card' ? '30px' : '2px',
          justifyContent: 'center',
          width: '100%'
        }}
      >
        {/* LOADING */}
        {loadingSomething && <SpinnerCentered />}

        {/* Category list */}
        {childrenCategories?.map((category, index) => (
          <React.Fragment key={category?.id}>
            {(category.isVisible || isAdmin()) && (
              <div style={listOrCardNavigation === 'List' ? { width: '100%' } : {}}>
                <SpringListItemTransition
                  key={category?.id}
                  uniqueId={category?.id}
                  initialScale={0.8}
                  delayMultiplier={0.25}
                  index={index}
                  layout={false}
                  fullWidth={listOrCardNavigation === 'List'}
                >
                  <CategoryCard
                    category={category}
                    name={category.name}
                    categoryId={category.id}
                    image={category.image}
                    views="0"
                    loading={loading}
                    ueId={category?.ue?.id}
                    refetch={refetch}
                    size="large"
                    color={color}
                    color2={color2}
                    description={category.description}
                    order={category.order}
                    isVisible={category.isVisible}
                    countAccessiblesCourses={category?.countAccessiblesCourses}
                    countAccessiblesExercises={category?.countAccessiblesExercises}
                  />
                </SpringListItemTransition>
              </div>
            )}
          </React.Fragment>
        ))}

        {/* Cours list */}
        {data?.coursInUECategory?.map((cours, index) => (
          <SpringListItemTransition
            key={cours?.id}
            uniqueId={cours?.id}
            initialScale={0.8}
            delayMultiplier={0.25}
            index={index + childrenCategories?.length}
            layout={false}
            fullWidth={listOrCardNavigation === 'List'}
          >
            {renderCours(cours, cours?.targetCours)}
          </SpringListItemTransition>
        ))}

        {hasNothingInCategory && (
          <Empty description={t('NothingInThisCategory')} style={{ padding: 30 }} />
        )}

        {/* ERROOOOR :'( */}
        {!loadingSomething && error && <ErrorResult refetch={refetch} error={error} />}

        {/* BUTTONS CREATION (TODO show all buttons but disable inaccessible buttons */}
        {isAdmin() && (
          <>
            <ButtonCreateCategoryOrFolder
              refetch={refetchAll}
              ueId={parentUe?.id}
              parentCategory={currentCategory}
            />
            <ButtonCreateCourse
              refetch={refetch}
              ueType={ueType}
              categoryId={categoryId}
              ueId={null}
            />
            <ButtonCreatePage
              refetch={refetch}
              ueType={ueType}
              categoryId={categoryId}
              ueId={null}
            />
            {/*
            <ButtonImportCourse
              refetch={refetch}
              ueType={ueType}
              categoryId={categoryId}
              ueId={null}
            />
            */}
          </>
        )}
      </div>
    </>
  );
};
