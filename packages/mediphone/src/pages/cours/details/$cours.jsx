import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import React from 'react';
import { Divider, Spin, Typography } from 'antd';
import { useQuery } from '@apollo/client';
import { DetailCoursCard } from '@/shared/pages/cours/details/components/DetailCoursCard';
import { ErrorResult } from '@/shared/components/ErrorResult';
import { QUERY_DETAILS_COURS } from '@/shared/graphql/cours.js';
import { CoursCardsLoadingView } from '@/shared/pages/cours/details/components/CoursCardsLoadingView';
import { useTranslation } from 'react-i18next';
import Commentaires from '../../../components/Commentaires/Commentaires';
import { QcmCoursCards } from '@/shared/pages/cours/details/components/QcmCours';
import { CommentairesType } from '@/shared/services/commentaires';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import { QcmAnnaleCards } from '@/shared/pages/cours/details/components/AnnaleCours';
import { ExoPullToRefresh } from '../../../components/ExoPullToRefresh.jsx';
import CoursBreadcrumb from '@/shared/components/Cours/CoursBreadcrumb';

// TODO unused anymore will be deleted
export default function(props) {
  useEffectScrollTop();
  const { cours } = props.match.params;
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_DETAILS_COURS, {
    fetchPolicy: 'no-cache',
    variables: { id: cours }
  });
  const getUeName = c => `${c.ueCategory.ue.name}`;
  const getCategoryName = c => `${c.ueCategory.name}`;
  const getTitleData = c => `${c.ueCategory.ue.name}/${c.ueCategory.name}`;
  const dataOkaay = () =>
    !loading && !error && data && data.cour && data.cour.ueCategory && data.cour.ueCategory.ue;
  const isCoursLoaded = () => !loading && !error && data && data.cour;
  const coursName = data && data.cour && data.cour.name;
  return (
    <ExoPullToRefresh
      onRefresh={async () => {
        await refetch();
      }}
    >
      <FullMediParticlesBreadCrumb title={!loading ? coursName : 'Chargement...'} />
      <React.Fragment>
        <CoursBreadcrumb
          ue={data && data.cour && data.cour.ueCategory && data.cour.ueCategory.ue}
          ueCategory={data && data.cour && data.cour.ueCategory}
          coursName={coursName}
          style={{ paddingBottom: 7 }}
        />
        {/* LOADING */}
        {loading && !data && <CoursCardsLoadingView loading={loading} />}
        {/* ERROOOOR :'( avec BOUTON pour REESSAAYER */}
        {!loading && error ? <ErrorResult refetch={refetch} error={error} /> : ''}
        {/* DONE LOADING */}
        {isCoursLoaded() && (
          <>
            <a id="to_download_medibox" href="" style={{ display: 'none' }} />
            <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}>
              <div id="PartieGauche" style={{ minWidth: 300, flexGrow: 1, flexBasis: 1 }}>
                <div>
                  <DetailCoursCard
                    title={data.cour.name}
                    name={data.cour.text}
                    type={data.cour.type}
                    pdf={data.cour.pdf}
                    epub={data.cour.epub}
                    refetch={refetch}
                    id={cours}
                    data={data}
                    error={error}
                  />
                </div>
              </div>
              <div id="PartieDroite" style={{ minWidth: 300, flexGrow: 1, flexBasis: 1 }}>
                {isCoursLoaded() && (
                  <QcmCoursCards qcms={data.cour.qcms} coursId={cours} refetch={refetch} />
                )}
                <QcmAnnaleCards coursId={cours} refetch={refetch} />
              </div>
            </div>
          </>
        )}
      </React.Fragment>
      <Divider />
      <Typography.Title level={2} style={{ textAlign: 'center' }}>
        {t('DiscussionSpace')}
      </Typography.Title>

      {!loading && !error && data && data.cour && data.cour.id && (
        <Commentaires id={data.cour.id} type={CommentairesType.COURS} refetch={refetch} />
      )}
      {loading && !error && <Spin size="large" />}
    </ExoPullToRefresh>
  );
}
