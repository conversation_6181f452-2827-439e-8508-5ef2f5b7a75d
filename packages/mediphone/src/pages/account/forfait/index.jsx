import { MUTATION_ADD_CREDIT_USER_IAP } from '@/shared/graphql/forfaits.js'
import { GET_MY_GROUPS } from '@/shared/graphql/user'
import { UpdateForfait } from '@/shared/pages/account/forfait/UpdateForfait.jsx'
import { useMutation, useQuery } from '@apollo/client'
import { InAppPurchase2 } from '@ionic-native/in-app-purchase-2'
import { PageHeader } from '@ant-design/pro-layout';
import { Button, Card, Col, Row, Spin, Tabs, Tag, message } from 'antd';
import React, { useEffect, useState } from 'react'
import { router } from 'umi'
import QueueAnim from 'rc-queue-anim'
import { useTranslation } from 'react-i18next';


const BuyCreditIAP = ({ refetch }) => {
  const {t} = useTranslation();
  const [AddCreditIAP, { loading: loadingAddCreditIAP }] = useMutation(MUTATION_ADD_CREDIT_USER_IAP)
  const [store, setStore] = useState(InAppPurchase2)
  const [productsFromStore, setProductsFromStore] = useState([])

  useEffect(() => {
    // store.verbosity = store.DEBUG
    // REGISTER PRODUCTS
    const CREDIT_ID = 'credit01'
    store.register({
      id: CREDIT_ID,
      alias: '1 Crédit',
      type: store.CONSUMABLE,
    })
    //////////////////////////////////////////////////////////////
    // Listeners
    store.when(CREDIT_ID)
      .approved((p) => {
        // Handle the product deliverable
        if (p.id === CREDIT_ID) {
          // A accès
          console.log(CREDIT_ID + ' Approved')
          console.log({ p })
        }
        return p.verify()
      }).verified(async (p) => {

      await AddCreditIAP({
        variables: {
          key: p.id,
        },
      })
      await refetch()
      message.success('Crédit acheté')
      return p.finish()
    })
    // Specific query for one ID
    store.when(CREDIT_ID).owned((p) => {
      //console.log({ p })
      //this.isPro = true;
    })
    ////////////////////////////////////////////////////
    store.ready(() => {
      const product = store.get(CREDIT_ID)
      setProductsFromStore([product])
    })
    store.refresh()

  }, [])

  return (
    <>

      <p>
        {t('BuyCreditExplanation')}
      </p>
      <p>
        {productsFromStore?.map(product => (
          <>
            {product?.title && (
              <div>
                - {product.title} : {product.price} {product.currency}
              </div>
            )}
          </>
        ))}
      </p>
      <p>
        <Button block size={'large'} onClick={() => {
          store.order('credit01')
        }}>
          {t('Buy1Credit')}
        </Button>
      </p>

    </>
  )
}

export default function(props) {
  // Get groups and credits
  const {t} = useTranslation();
  const { data, loading, refetch, error } = useQuery(GET_MY_GROUPS, { fetchPolicy: 'no-cache' })
  const mesGroupes = data && data.me && data.me.groups
  const mesCredits = data && data.me && data.me.credits

  return (
    <>
      <PageHeader
        onBack={() => router.goBack()}
        title={t('MySubscription')}
      />
      <QueueAnim type={['top', 'down']}>
        <Row justify="center" type="flex" key="1">
          <Col xl={12} lg={12} md={12} sm={22} xs={22}>
            <Card style={{ marginTop: 5, marginBottom: 10 }}
                  loading={loading}
            >
              <h1>{t('CurrentSubscription')}</h1>
              {loading && !error && <Spin/>}

              {!loading && !error && mesGroupes && mesGroupes?.map(group => (
                <Tag color="geekblue" key={group.id}>{group.name}</Tag>
              ))}
              {(mesCredits && mesCredits > 0) ? (
                <div style={{ marginTop: 20 }}>
                  <Tag color="#108ee9">{mesCredits} crédit{mesCredits > 1 ? 's' : ''}</Tag>
                </div>
              ) : ''}
              {(!mesCredits || mesCredits === 0) && (
                <div style={{ marginTop: 20 }}>
                  <Tag color="#108ee9">0 crédit</Tag>
                </div>
              )}
            </Card>

            <Card style={{ marginTop: 5, padding: 10 }}>
              <React.Fragment>
                <h1>{t('Update')}</h1>
                <p>
                  {t('HowToBuyCredit')}
                </p>

                <Tabs defaultActiveKey="2">
                  {/*
                    <Tabs.TabPane tab="Achat de crédit" key={1}>
                      <BuyCreditIAP refetch={refetch}/>
                    </Tabs.TabPane>
                  */}
                  <Tabs.TabPane tab="Mettre à jour mon forfait" key={2}>
                    {/*
                    {(mesCredits && mesCredits > 0) ? (
                      <UpdateForfait mesGroupes={mesGroupes} mesCredits={mesCredits} showOnlyCreditValue/>
                    ) : (
                      <h3>{t('NoCreditAvailable')}</h3>
                    )}
                    */}

                    <UpdateForfait mesGroupes={mesGroupes} mesCredits={mesCredits} />
                  </Tabs.TabPane>
                </Tabs>
                {/*
                  <Button href={CONFIGS.baseWebsiteUrl}>Cliquez ici</Button>
                */}
              </React.Fragment>
            </Card>
          </Col>
        </Row>
      </QueueAnim>
    </>
  )
}
