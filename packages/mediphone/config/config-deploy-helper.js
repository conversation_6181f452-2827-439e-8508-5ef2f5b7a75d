// TODO get configs from EXOTEACH-META-NEXUS

export const getProductionConfigForApp = (exoteachName) => {
  let config
  switch (exoteachName) {
    case 'GALIEN_MARSEILLE':
      const domain = 'cours-galien-marseille.exoteach.com'
      config = {
        baseWebsiteUrl: `https://${domain}/`,
        serverUrl: `https://${domain}/medibox2-api/`,
        graphqlServerUrl: `https://${domain}/medibox2-api/graphql`,
        webSocketsGraphqlServerUrl: `wss://${domain}/medibox2-ws/graphql`,
        publicPath: '/elearning/',
        appName: 'Cours Galien',
        logoFilename: 'cg.png'
      }
      console.log({ config })
      return config


    case 'MEDIBOX_CLERMONT':
      config = {
        baseWebsiteUrl: 'https://medibox-clermont.fr/',
        serverUrl: 'https://medibox-clermont.fr/medibox2-api/',
        graphqlServerUrl: 'https://medibox-clermont.fr/medibox2-api/graphql',
        webSocketsGraphqlServerUrl: 'wss://medibox-clermont.fr/medibox2-ws/graphql',
        publicPath: '/elearning/',
        appName: 'Médibox',
        logoFilename: 'logo2.png'
      }
      console.log({ config })
      return config
  }
}