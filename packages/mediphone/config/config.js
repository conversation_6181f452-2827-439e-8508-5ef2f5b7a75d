import { getProductionConfigForApp } from './config-deploy-helper.js';
import slash from 'slash2';
import webpackPlugin from './plugin.config';
import routes from './routes';

const { theme } = require('antd/lib');
const { convertLegacyToken } = require('@ant-design/compatible/lib');
const { defaultAlgorithm, defaultSeed } = theme;
const mapToken = defaultAlgorithm(defaultSeed);
const v4Token = convertLegacyToken(mapToken);


const { REACT_APP_ENV, REACT_APP_VERSION, EXOTEACH_NAME, MOBILE } = process.env;

// Alex met ça à false pour utiliser backend de STAGING
const UTILISER_BACKEND_LOCAL_DEV = true;

const localDevConfig = {
  baseWebsiteUrl: 'http://localhost/medibox/', // DOIT FINIR PAR '/'
  serverUrl: 'http://localhost:8000/',
  graphqlServerUrl: 'http://localhost:8000/graphql',
  webSocketsGraphqlServerUrl: 'ws://localhost:8000/graphql',
  qcmBase: 'http://localhost/medibox/qcm/', // Pour images qcm (depuis système legacy)
  publicPath: '/',
  appName: 'Médibox',
  logoFilename: 'logo2.png',
};
const stagingConfig = {
  baseWebsiteUrl: 'https://medibox.fr/',
  serverUrl: 'https://medibox.fr/medibox2-api/', // DOIT FINIR PAR /
  graphqlServerUrl: 'https://medibox.fr/medibox2-api/graphql',
  webSocketsGraphqlServerUrl: 'wss://medibox.fr/medibox2-ws/graphql',
  publicPath: '/medibox2/',
  appName: 'Médibox',
};
export const configs = {
  dev: UTILISER_BACKEND_LOCAL_DEV ? localDevConfig : stagingConfig,
  staging: stagingConfig,
  production: !EXOTEACH_NAME && {
    baseWebsiteUrl: 'https://medibox-marseille.fr/',
    serverUrl: 'https://medibox-marseille.fr/medibox2-api/',
    graphqlServerUrl: 'https://medibox-marseille.fr/medibox2-api/graphql',
    webSocketsGraphqlServerUrl: 'wss://medibox-marseille.fr/medibox2-ws/graphql',
    publicPath: '/',
    appName: 'Médibox',
    logoFilename: 'logo2.png',
  } || getProductionConfigForApp(EXOTEACH_NAME),

}[REACT_APP_ENV || 'dev'];

export default {
  plugins: [
    ['umi-plugin-antd-icon-config', {}],
    [
      'umi-plugin-react',
      {
        antd: false,
        dva: {
          hmr: true, // Hot module replacement
        },
        locale: {
          enable: true,
          default: 'fr-FR',
          // default true, when it is true, will use `navigator.language` overwrite default
          baseNavigator: true,
        },
        dynamicImport: {
          loadingComponent: './shared/components/PageLoading/index',
          webpackChunkName: true,
          level: 3,
        },
        fastClick: false,
        pwa: false,
        /*
        workboxPluginMode: 'InjectManifest',
        workboxOptions: {
          importWorkboxFrom: 'local',
          swSrc: 'path/to/service-worker.js')
      }
         */

      },
    ],
    ['umi-plugin-pro-block', {
      moveMock: false,
      moveService: false,
      modifyRequest: true,
      autoAddMenu: true, //todo change?
    },
    ],
  ],
  hash: true, // hash file suffix
  /*
  targets: {
    ie: 11,
  },
  */
  // umi routes: https://umijs.org/zh/guide/router.html
  routes: routes,
  // Theme for antd: https://ant.design/docs/react/customize-theme
  /*
  theme: {
    '@primary-color': '#1890ff',
    '@border-radius-base': '4px', // major border radius
    '@btn-border-radius-base': '20px',
    '@card-radius': '10px',
  },
  */
  define: {
    REACT_APP_ENV: REACT_APP_ENV || false,
    REACT_APP_VERSION: REACT_APP_VERSION || '?',
    ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: '',
    MOBILE: MOBILE || 0,
    // preview.pro.ant.design only do not use in your production ; preview.pro.ant.design。
    CONFIGS: configs,

    FILES_URL: configs.serverUrl + 'files/',
    FICHES_URL: configs.serverUrl + 'files/fiche/',
    COURS_URL: configs.serverUrl + 'files/cours/',
    BILLS_URL: configs.serverUrl + 'files/bill/',
    AVATARS_URL: configs.serverUrl + 'avatars/',
    PUBLIC_URL: configs.serverUrl,
  },
  ignoreMomentLocale: true,
  lessLoaderOptions: {
    javascriptEnabled: true,
    modifyVars: v4Token,
  },
  disableRedirectHoist: false,
  cssLoaderOptions: {
    modules: true,
    getLocalIdent: (context, _, localName) => {
      if (
        context.resourcePath.includes('node_modules') ||
        context.resourcePath.includes('ant.design.pro.less') ||
        context.resourcePath.includes('global.less') ||
        context.resourcePath.includes('katex.min.css')
      ) {
        return localName;
      }
      const match = context.resourcePath.match(/src(.*)/);
      if (match && match[1]) {
        const antdProPath = match[1].replace('.less', '');
        const arr = slash(antdProPath)
          .split('/')
          .map(a => a.replace(/([A-Z])/g, '-$1'))
          .map(a => a.toLowerCase());
        return `antd-pro${arr.join('-')}-${localName}`.replace(/--/g, '-');
      }
      return localName;
    },
  },
  manifest: {
    basePath: configs.publicPath,
  },
  chainWebpack: webpackPlugin,
  // runtimePublicPath: configs.publicPath,
  publicPath: configs.publicPath,
  // proxy: proxy[REACT_APP_ENV || 'dev'],
  /*
  exportStatic: {
    htmlSuffix: true,
    dynamicRoot: true,
  },
  */

  extraBabelPlugins: [
    '@babel/plugin-proposal-nullish-coalescing-operator',
    '@babel/plugin-proposal-logical-assignment-operators'
  ],

  extraBabelIncludes: [
    /node_modules[\\/]tsparticles-/,
    /node_modules[\\/]react-quill-new/,
    /node_modules[\\/]quill/,
    /node_modules[\\/]use-long-press/,
    /node_modules[\\/]@cyntler[\\/]react-doc-viewer/,
  ],

  history: 'hash',
};

