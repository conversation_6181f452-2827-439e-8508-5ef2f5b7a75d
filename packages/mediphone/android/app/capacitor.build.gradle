// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-file-opener')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-device')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-push-notifications')
    implementation "androidx.webkit:webkit:1.4.0"
    implementation "com.android.billingclient:billing:6.0.1"
}
apply from: "../../node_modules/cordova-plugin-badge/src/android/badge.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
