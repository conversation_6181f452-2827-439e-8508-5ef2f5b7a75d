{
  mode: 'development',
  devtool: 'cheap-module-source-map',
  node: {
    setImmediate: false,
    process: 'mock',
    dgram: 'empty',
    fs: 'empty',
    net: 'empty',
    tls: 'empty',
    child_process: 'empty'
  },
  output: {
    path: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/dist',
    filename: '[name].js',
    chunkFilename: '[name].async.js',
    publicPath: '/',
    devtoolModuleFilenameTemplate: function () { /* omitted long function */ },
    pathinfo: true
  },
  resolve: {
    symlinks: true,
    alias: {
      dva: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/dva',
      'dva-loading': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/dva-loading/dist/index.js',
      'path-to-regexp': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-plugin-dva/node_modules/path-to-regexp/index.js',
      'object-assign': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/object-assign/index.js',
      'umi/locale': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-plugin-locale/lib/locale.js',
      'react-intl': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/react-intl',
      react: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/react',
      'react-dom': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/react-dom',
      'react-router': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-build-dev/node_modules/react-router',
      'react-router-dom': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/react-router-dom',
      'react-router-config': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/react-router-config',
      history: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-history',
      '@': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/config/src',
      '@tmp': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/pages/.umi',
      '@@': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/pages/.umi',
      umi: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi',
      'regenerator-runtime': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-build-dev/node_modules/regenerator-runtime',
      antd: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/antd',
      'antd-mobile': '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/antd-mobile'
    },
    extensions: [
      '.web.js',
      '.wasm',
      '.mjs',
      '.js',
      '.web.jsx',
      '.jsx',
      '.web.ts',
      '.ts',
      '.web.tsx',
      '.tsx',
      '.json'
    ],
    modules: [
      'node_modules',
      '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/af-webpack/node_modules',
      '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/'
    ]
  },
  resolveLoader: {
    modules: [
      'node_modules',
      '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/af-webpack/node_modules'
    ]
  },
  module: {
    rules: [
      /* config.module.rule('exclude') */
      {
        exclude: [
          /\.json$/,
          /\.(js|jsx|ts|tsx|mjs|wasm)$/,
          /\.(graphql|gql)$/,
          /\.(css|less|scss|sass|styl(us)?)$/
        ],
        use: [
          /* config.module.rule('exclude').use('url-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-url-pnp-loader/dist/cjs.js',
            options: {
              limit: 10000,
              name: 'static/[name].[hash:8].[ext]'
            }
          }
        ]
      },
      /* config.module.rule('mjs-require') */
      {
        test: /\.mjs$/,
        type: 'javascript/auto',
        include: [
          '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone'
        ]
      },
      /* config.module.rule('mjs') */
      {
        test: /\.mjs$/,
        include: [
          '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone'
        ],
        use: [
          /* config.module.rule('mjs').use('babel-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-loader/lib/index.js',
            options: {
              presets: [
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/index.js',
                  {
                    targets: {
                      chrome: 49,
                      firefox: 64,
                      safari: 10,
                      edge: 13,
                      ios: 10,
                      ie: 11
                    },
                    env: {
                      useBuiltIns: 'entry',
                      corejs: 2
                    }
                  }
                ]
              ],
              plugins: [
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-build-dev/lib/plugins/afwebpack-config/lockCoreJSVersionPlugin.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd-mobile',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd-mobile'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'ant-design-pro',
                    libraryDirectory: 'lib',
                    style: true,
                    camel2DashComponentName: false
                  },
                  'ant-design-pro'
                ],
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-dva-hmr/lib/index.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-named-asset-import/index.js',
                  {
                    loaderMap: {
                      svg: {
                        ReactComponent: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/af-webpack/lib/svgr.js?-prettier,-svgo![path]'
                      }
                    }
                  }
                ]
              ],
              sourceType: 'unambiguous',
              cacheDirectory: true,
              babelrc: false,
              customize: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/webpack-overrides.js'
            }
          }
        ]
      },
      /* config.module.rule('js') */
      {
        test: /\.js$/,
        include: [
          '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone'
        ],
        exclude: [
          /node_modules/
        ],
        use: [
          /* config.module.rule('js').use('babel-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-loader/lib/index.js',
            options: {
              presets: [
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/index.js',
                  {
                    targets: {
                      chrome: 49,
                      firefox: 64,
                      safari: 10,
                      edge: 13,
                      ios: 10,
                      ie: 11
                    },
                    env: {
                      useBuiltIns: 'entry',
                      corejs: 2
                    }
                  }
                ]
              ],
              plugins: [
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-build-dev/lib/plugins/afwebpack-config/lockCoreJSVersionPlugin.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd-mobile',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd-mobile'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'ant-design-pro',
                    libraryDirectory: 'lib',
                    style: true,
                    camel2DashComponentName: false
                  },
                  'ant-design-pro'
                ],
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-dva-hmr/lib/index.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-named-asset-import/index.js',
                  {
                    loaderMap: {
                      svg: {
                        ReactComponent: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/af-webpack/lib/svgr.js?-prettier,-svgo![path]'
                      }
                    }
                  }
                ]
              ],
              sourceType: 'unambiguous',
              cacheDirectory: true,
              babelrc: false,
              customize: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/webpack-overrides.js'
            }
          }
        ]
      },
      /* config.module.rule('jsx') */
      {
        test: /\.jsx$/,
        include: [
          '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone'
        ],
        use: [
          /* config.module.rule('jsx').use('babel-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-loader/lib/index.js',
            options: {
              presets: [
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/index.js',
                  {
                    targets: {
                      chrome: 49,
                      firefox: 64,
                      safari: 10,
                      edge: 13,
                      ios: 10,
                      ie: 11
                    },
                    env: {
                      useBuiltIns: 'entry',
                      corejs: 2
                    }
                  }
                ]
              ],
              plugins: [
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-build-dev/lib/plugins/afwebpack-config/lockCoreJSVersionPlugin.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd-mobile',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd-mobile'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'ant-design-pro',
                    libraryDirectory: 'lib',
                    style: true,
                    camel2DashComponentName: false
                  },
                  'ant-design-pro'
                ],
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-dva-hmr/lib/index.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-named-asset-import/index.js',
                  {
                    loaderMap: {
                      svg: {
                        ReactComponent: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/af-webpack/lib/svgr.js?-prettier,-svgo![path]'
                      }
                    }
                  }
                ]
              ],
              sourceType: 'unambiguous',
              cacheDirectory: true,
              babelrc: false,
              customize: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/webpack-overrides.js'
            }
          }
        ]
      },
      /* config.module.rule('extraBabelInclude_0') */
      {
        test: /\.jsx?$/,
        include: [
          '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-plugin-ui/bubble'
        ],
        use: [
          /* config.module.rule('extraBabelInclude_0').use('babel-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-loader/lib/index.js',
            options: {
              presets: [
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/index.js',
                  {
                    targets: {
                      chrome: 49,
                      firefox: 64,
                      safari: 10,
                      edge: 13,
                      ios: 10,
                      ie: 11
                    },
                    env: {
                      useBuiltIns: 'entry',
                      corejs: 2
                    }
                  }
                ]
              ],
              plugins: [
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-build-dev/lib/plugins/afwebpack-config/lockCoreJSVersionPlugin.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd-mobile',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd-mobile'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'ant-design-pro',
                    libraryDirectory: 'lib',
                    style: true,
                    camel2DashComponentName: false
                  },
                  'ant-design-pro'
                ],
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-dva-hmr/lib/index.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-named-asset-import/index.js',
                  {
                    loaderMap: {
                      svg: {
                        ReactComponent: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/af-webpack/lib/svgr.js?-prettier,-svgo![path]'
                      }
                    }
                  }
                ]
              ],
              sourceType: 'unambiguous',
              cacheDirectory: true,
              babelrc: false,
              customize: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/webpack-overrides.js'
            }
          }
        ]
      },
      /* config.module.rule('extraBabelInclude_1') */
      {
        test: /\.jsx?$/,
        include: [
          function () { /* omitted long function */ }
        ],
        use: [
          /* config.module.rule('extraBabelInclude_1').use('babel-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-loader/lib/index.js',
            options: {
              presets: [
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/index.js',
                  {
                    targets: {
                      chrome: 49,
                      firefox: 64,
                      safari: 10,
                      edge: 13,
                      ios: 10,
                      ie: 11
                    },
                    env: {
                      useBuiltIns: 'entry',
                      corejs: 2
                    }
                  }
                ]
              ],
              plugins: [
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-build-dev/lib/plugins/afwebpack-config/lockCoreJSVersionPlugin.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd-mobile',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd-mobile'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'ant-design-pro',
                    libraryDirectory: 'lib',
                    style: true,
                    camel2DashComponentName: false
                  },
                  'ant-design-pro'
                ],
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-dva-hmr/lib/index.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-named-asset-import/index.js',
                  {
                    loaderMap: {
                      svg: {
                        ReactComponent: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/af-webpack/lib/svgr.js?-prettier,-svgo![path]'
                      }
                    }
                  }
                ]
              ],
              sourceType: 'unambiguous',
              cacheDirectory: true,
              babelrc: false,
              customize: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/webpack-overrides.js'
            }
          }
        ]
      },
      /* config.module.rule('ts') */
      {
        test: /\.tsx?$/,
        use: [
          /* config.module.rule('ts').use('babel-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-loader/lib/index.js',
            options: {
              presets: [
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/index.js',
                  {
                    targets: {
                      chrome: 49,
                      firefox: 64,
                      safari: 10,
                      edge: 13,
                      ios: 10,
                      ie: 11
                    },
                    env: {
                      useBuiltIns: 'entry',
                      corejs: 2
                    }
                  }
                ]
              ],
              plugins: [
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/umi-build-dev/lib/plugins/afwebpack-config/lockCoreJSVersionPlugin.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'antd-mobile',
                    libraryDirectory: 'es',
                    style: true
                  },
                  'antd-mobile'
                ],
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-import/lib/index.js',
                  {
                    libraryName: 'ant-design-pro',
                    libraryDirectory: 'lib',
                    style: true,
                    camel2DashComponentName: false
                  },
                  'ant-design-pro'
                ],
                '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-dva-hmr/lib/index.js',
                [
                  '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-plugin-named-asset-import/index.js',
                  {
                    loaderMap: {
                      svg: {
                        ReactComponent: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/af-webpack/lib/svgr.js?-prettier,-svgo![path]'
                      }
                    }
                  }
                ]
              ],
              sourceType: 'unambiguous',
              cacheDirectory: true,
              babelrc: false,
              customize: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/babel-preset-umi/lib/webpack-overrides.js'
            }
          },
          /* config.module.rule('ts').use('ts-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/ts-loader/index.js',
            options: {
              configFile: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/tsconfig.json',
              transpileOnly: true,
              errorFormatter: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('graphql') */
      {
        test: /\.(graphql|gql)$/,
        exclude: [
          /node_modules/
        ],
        use: [
          /* config.module.rule('graphql').use('graphql-tag-loader') */
          {
            loader: 'graphql-tag/loader'
          }
        ]
      },
      /* config.module.rule('cssModulesExcludes_0') */
      {
        test: function () { /* omitted long function */ },
        use: [
          /* config.module.rule('cssModulesExcludes_0').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('cssModulesExcludes_0').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('cssModulesExcludes_0').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('cssModulesExcludes_1') */
      {
        test: function () { /* omitted long function */ },
        use: [
          /* config.module.rule('cssModulesExcludes_1').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('cssModulesExcludes_1').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('cssModulesExcludes_1').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('cssModulesExcludes_2') */
      {
        test: function () { /* omitted long function */ },
        use: [
          /* config.module.rule('cssModulesExcludes_2').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('cssModulesExcludes_2').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('cssModulesExcludes_2').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('cssModulesExcludes_3') */
      {
        test: function () { /* omitted long function */ },
        use: [
          /* config.module.rule('cssModulesExcludes_3').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('cssModulesExcludes_3').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('cssModulesExcludes_3').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('cssModulesExcludes_4') */
      {
        test: function () { /* omitted long function */ },
        use: [
          /* config.module.rule('cssModulesExcludes_4').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('cssModulesExcludes_4').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('cssModulesExcludes_4').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('cssModulesExcludes_4').use('less-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/less-loader/dist/cjs.js',
            options: {
              modifyVars: {
                '@primary-color': '#1890ff',
                '@border-radius-base': '4px',
                '@btn-border-radius-base': '20px',
                '@card-radius': '20px'
              },
              javascriptEnabled: true
            }
          }
        ]
      },
      /* config.module.rule('cssModulesExcludes_5') */
      {
        test: function () { /* omitted long function */ },
        use: [
          /* config.module.rule('cssModulesExcludes_5').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('cssModulesExcludes_5').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('cssModulesExcludes_5').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('css') */
      {
        test: /\.css$/,
        exclude: [
          function () { /* omitted long function */ }
        ],
        use: [
          /* config.module.rule('css').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('css').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ },
              localIdentName: '[name]__[local]___[hash:base64:5]'
            }
          },
          /* config.module.rule('css').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('css-in-node_modules') */
      {
        test: /\.css$/,
        include: [
          /node_modules/
        ],
        use: [
          /* config.module.rule('css-in-node_modules').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('css-in-node_modules').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('css-in-node_modules').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('less') */
      {
        test: /\.less$/,
        exclude: [
          function () { /* omitted long function */ }
        ],
        use: [
          /* config.module.rule('less').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('less').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ },
              localIdentName: '[name]__[local]___[hash:base64:5]'
            }
          },
          /* config.module.rule('less').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('less').use('less-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/less-loader/dist/cjs.js',
            options: {
              modifyVars: {
                '@primary-color': '#1890ff',
                '@border-radius-base': '4px',
                '@btn-border-radius-base': '20px',
                '@card-radius': '20px'
              },
              javascriptEnabled: true
            }
          }
        ]
      },
      /* config.module.rule('less-in-node_modules') */
      {
        test: /\.less$/,
        include: [
          /node_modules/
        ],
        use: [
          /* config.module.rule('less-in-node_modules').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('less-in-node_modules').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('less-in-node_modules').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('less-in-node_modules').use('less-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/less-loader/dist/cjs.js',
            options: {
              modifyVars: {
                '@primary-color': '#1890ff',
                '@border-radius-base': '4px',
                '@btn-border-radius-base': '20px',
                '@card-radius': '20px'
              },
              javascriptEnabled: true
            }
          }
        ]
      },
      /* config.module.rule('sass') */
      {
        test: /\.(sass|scss)$/,
        exclude: [
          function () { /* omitted long function */ }
        ],
        use: [
          /* config.module.rule('sass').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('sass').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ },
              localIdentName: '[name]__[local]___[hash:base64:5]'
            }
          },
          /* config.module.rule('sass').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('sass-in-node_modules') */
      {
        test: /\.(sass|scss)$/,
        include: [
          /node_modules/
        ],
        use: [
          /* config.module.rule('sass-in-node_modules').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('sass-in-node_modules').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('sass-in-node_modules').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('stylus') */
      {
        test: /\.styl(us)?$/,
        exclude: [
          function () { /* omitted long function */ }
        ],
        use: [
          /* config.module.rule('stylus').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('stylus').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ },
              localIdentName: '[name]__[local]___[hash:base64:5]'
            }
          },
          /* config.module.rule('stylus').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      },
      /* config.module.rule('stylus-in-node_modules') */
      {
        test: /\.styl(us)?$/,
        include: [
          /node_modules/
        ],
        use: [
          /* config.module.rule('stylus-in-node_modules').use('extract-css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/mini-css-extract-plugin/dist/loader.js',
            options: {
              publicPath: '/',
              hmr: true
            }
          },
          /* config.module.rule('stylus-in-node_modules').use('css-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/css-loader-1/index.js',
            options: {
              importLoaders: 1,
              sourceMap: true,
              modules: true,
              getLocalIdent: function () { /* omitted long function */ }
            }
          },
          /* config.module.rule('stylus-in-node_modules').use('postcss-loader') */
          {
            loader: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/postcss-loader/src/index.js',
            options: {
              ident: 'postcss',
              plugins: function () { /* omitted long function */ }
            }
          }
        ]
      }
    ]
  },
  optimization: {
    splitChunks: {
      chunks: 'async',
      name: 'vendors',
      maxInitialRequests: Infinity,
      minSize: 0,
      cacheGroups: {
        vendors: {
          test: function () { /* omitted long function */ },
          name: function () { /* omitted long function */ }
        }
      }
    },
    runtimeChunk: false
  },
  plugins: [
    /* config.plugin('extract-css') */
    new MiniCssExtractPlugin(
      {
        filename: '[name].css',
        chunkFilename: '[name].chunk.css'
      }
    ),
    /* config.plugin('define') */
    new DefinePlugin(
      {
        'process.env': {
          NODE_ENV: '"development"'
        },
        'process.env.BASE_URL': '"/"',
        __IS_BROWSER: 'true',
        __UMI_BIGFISH_COMPAT: undefined,
        __UMI_HTML_SUFFIX: 'false',
        REACT_APP_ENV: 'false',
        REACT_APP_VERSION: '"?"',
        ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: '""',
        CONFIGS: '{"baseWebsiteUrl":"http://**************/medibox/","serverUrl":"http://**************:8000/","graphqlServerUrl":"http://**************:8000/graphql","webSocketsGraphqlServerUrl":"ws://**************:8000/graphql","qcmBase":"http://**************/medibox/qcm/","publicPath":"/","appName":"Médibox"}',
        FILES_URL: '"http://**************:8000/files/"',
        FICHES_URL: '"http://**************:8000/files/fiche/"',
        COURS_URL: '"http://**************:8000/files/cours/"',
        BILLS_URL: '"http://**************:8000/files/bill/"',
        AVATARS_URL: '"http://**************:8000/avatars/"'
      }
    ),
    /* config.plugin('progress') */
    new WebpackBarPlugin(
      {
        color: 'green',
        reporters: [
          'fancy'
        ]
      }
    ),
    /* config.plugin('ignore-moment-locale') */
    new IgnorePlugin(
      /^\.\/locale$/,
      /moment$/
    ),
    /* config.plugin('copy-public') */
    new CopyPlugin(
      [
        {
          from: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/public',
          to: '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/dist',
          toType: 'dir'
        }
      ]
    ),
    /* config.plugin('filter-css-conflicting-warnings') */
    new FilterCSSConflictingWarning(),
    /* config.plugin('friendly-errors') */
    new FriendlyErrorsWebpackPlugin(
      {
        clearConsole: false
      }
    ),
    /* config.plugin('hmr') */
    new HotModuleReplacementPlugin(),
    /* config.plugin('umi-ui-compile-status') */
    new CompileStatusWebpackPlugin()
  ],
  entry: {
    umi: [
      '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/node_modules/af-webpack/lib/webpackHotDevClient.js',
      '/home/<USER>/Independant/Medibox/medifrontsplitted/packages/mediphone/pages/.umi/umi.js'
    ]
  }
}
