PODS:
  - Capacitor (5.4.1):
    - Capac<PERSON><PERSON><PERSON><PERSON>
  - Capacitor<PERSON>rowser (5.1.0):
    - Capacitor
  - Capac<PERSON><PERSON>ordova (5.4.1)
  - CapacitorDevice (5.0.6):
    - Capacitor
  - CapacitorPushNotifications (5.1.0):
    - Capacitor
  - CordovaPlugins (5.4.1):
    - CapacitorCordova
  - Firebase/Analytics (10.3.0):
    - Firebase/Core
  - Firebase/Core (10.3.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.3.0)
  - Firebase/CoreOnly (10.3.0):
    - FirebaseCore (= 10.3.0)
  - Firebase/Messaging (10.3.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.3.0)
  - FirebaseAnalytics (10.3.0):
    - FirebaseAnalytics/AdIdSupport (= 10.3.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.3.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.3.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (10.3.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.3.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.3.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.3.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement (10.3.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.3.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.3.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.3.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.3.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.2.0):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.10.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.10.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.10.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.10.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.10.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.10.0)"
  - GoogleUtilities/Reachability (7.10.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.10.0):
    - GoogleUtilities/Logger
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - PromisesObjC (2.1.1)

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorBrowser (from `../../node_modules/@capacitor/browser`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorPushNotifications (from `../../node_modules/@capacitor/push-notifications`)"
  - CordovaPlugins (from `../capacitor-cordova-ios-plugins`)
  - Firebase/Analytics
  - Firebase/Messaging

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorBrowser:
    :path: "../../node_modules/@capacitor/browser"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorPushNotifications:
    :path: "../../node_modules/@capacitor/push-notifications"
  CordovaPlugins:
    :path: "../capacitor-cordova-ios-plugins"

SPEC CHECKSUMS:
  Capacitor: 4c0681b47eebeba1f902093d24149cf2f518393e
  CapacitorBrowser: 7a0fb6a1011abfaaf2dfedfd8248f942a8eda3d6
  CapacitorCordova: 1222fdd78bfd0d323cc22bf254eed1484fe6c92a
  CapacitorDevice: 2c968f98a1ec4d22357418c1521e7ddc46c675e6
  CapacitorPushNotifications: b31e326c6e4eb216a622041d6ca21a973f34943f
  CordovaPlugins: d806eeb55c2d120b2bbea71de9aa2bae979ba5e3
  Firebase: f92fc551ead69c94168d36c2b26188263860acd9
  FirebaseAnalytics: 036232b6a1e2918e5f67572417be1173576245f3
  FirebaseCore: 988754646ab3bd4bdcb740f1bfe26b9f6c0d5f2a
  FirebaseCoreInternal: 29b76f784d607df8b2a1259d73c3f04f1210137b
  FirebaseInstallations: e2f26126089dcf41e215f7b8925af8d953c7d602
  FirebaseMessaging: e345b219fd15d325f0cf2fef28cb8ce00d851b3f
  GoogleAppMeasurement: c7d6fff39bf2d829587d74088d582e32d75133c3
  GoogleDataTransport: 1c8145da7117bd68bbbed00cf304edb6a24de00f
  GoogleUtilities: bad72cb363809015b1f7f19beb1f1cd23c589f95
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  PromisesObjC: ab77feca74fa2823e7af4249b8326368e61014cb

PODFILE CHECKSUM: ae2fa48aab79b2f0e31276ea422af7179902adff

COCOAPODS: 1.12.1
