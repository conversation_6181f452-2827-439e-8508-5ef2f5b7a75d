import { GET_ANNEES, GET_CONFIG } from '@/shared/graphql/home.js';
import { QUERY_UNSEEN_PRIVATE_MESSAGES_COUNT } from '@/shared/graphql/notifications';
import useFavicon from '@/shared/hooks/useFavicon.js';
import useLocalStorage from '@/shared/hooks/useLocalStorage.js';
import { GET_FIRST_MANDATORY_FORM_ID_TO_COMPLETE, GET_ME_CGU } from '@/shared/models/user.js';
import { CONFIG_KEYS, DEFAULT_CONFIG_VARIABLES, getPublicSrc } from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import {
  getLatestYear,
  setLastSelectedYear,
  setLastSelectedYearForQcm
} from '@/shared/services/qcm.js';
import {
  IS_DEV,
  isDiploma,
  isMobile,
  isTestServer,
  TimeZoneManager,
  tryParseJSONObject
} from '@/shared/utils/utils.js';
import { ApolloProvider, useQuery } from '@apollo/client';
import router from 'umi/router';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone'; // dependent on utc plugin
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';

import moment from 'moment-timezone';
import React, { useEffect, useState } from 'react';
import { ConfigProvider } from 'antd';
import frFR from 'antd/es/locale/fr_FR';
import enUS from 'antd/es/locale/en_US';
import deDE from 'antd/es/locale/de_DE';
import esES from 'antd/es/locale/es_ES';
import itIT from 'antd/es/locale/it_IT';
import 'dayjs/locale/fr';
import 'dayjs/locale/en';
import { client } from '@/shared/utils/apolloClient';
import '@/shared/i18n.js';
import { useTranslation } from 'react-i18next';

import 'moment/locale/es'; // without this line it didn't work
import 'moment/locale/en-gb'; // without this line it didn't work
import 'moment/locale/it'; // without this line it didn't work

import isBetween from 'dayjs/plugin/isBetween';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { PageLoading } from '@ant-design/pro-layout';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(duration);
dayjs.extend(isBetween);
dayjs.extend(advancedFormat);
dayjs.extend(relativeTime);

// Global CONTEXT
export const GlobalContext = React.createContext(undefined);
// Create a provider for components to consume and subscribe to changes
export const GlobalContextProvider = (props) => {
  const { i18n } = useTranslation();

  const [ueName, setUEName] = useState('');
  const [ueCategory, setUECategory] = useState('');
  const [navigationStyle, setNavigationStyle] = useState('card');
  const [globalBannerText, setGlobalBannerText] = useState('');
  const [globalBannerSubtitle, setGlobalBannerSubtitle] = useState('');
  const [ueId, setUeId] = useState();
  const [categoryId, setCategoryId] = useState();
  const [coursId, setCoursId] = useState();

  // Feature flags
  const features = {
    enableFormation: IS_DEV || isDiploma || isTestServer
  };

  const [listOrCardNavigation, setListOrCardNavigation] = useLocalStorage(
    'user-navigation-list-or-card',
    isMobile ? 'List' : 'Card'
  );

  const [currentTimeZone, setCurrentTimezone] = useLocalStorage(
    TimeZoneManager.getLocalStorageTimeZoneKey(),
    TimeZoneManager.getDefaultTimeZone()
  );

  moment.tz.setDefault(currentTimeZone);

  //dayjs.tz.setDefault(currentTimeZone);

  const [nextCours, setNextCours] = useState();
  const [breadCrumbImage, setBreadCrumbImage] = useState(null);
  const [breadCrumbImageType, setBreadCrumbImageType] = useState(null);

  /* Setup global default year */
  const {
    data: { annees = null } = {},
    loading,
    error
  } = useQuery(GET_ANNEES, {
    fetchPolicy: 'cache-and-network'
  });

  const {
    data: dataMe,
    loading: loadingMe,
    error: errorMe,
    refetch: refetchMe
  } = useQuery(GET_ME_CGU, {
    fetchPolicy: 'cache-and-network',
    pollInterval: 7200000 // toutes les 2h
  });

  const { data: { unseenPrivateMessagesCount = 0 } = {}, refetch: refetchPrivateMessagesCount } =
    useQuery(QUERY_UNSEEN_PRIVATE_MESSAGES_COUNT, {
      fetchPolicy: 'cache-and-network'
      //pollInterval: 480000 // toutes les 8min
    });

  const {
    data: dataForm,
    loading: loadingForm,
    refetch: refetchMandatoryFormsToComplete
  } = useQuery(GET_FIRST_MANDATORY_FORM_ID_TO_COMPLETE, {
    fetchPolicy: 'no-cache',
    // pollInterval:7200000, // toutes les 2h
    skip: !dataMe?.me
  });
  const formIdToComplete = dataForm?.undoneMandatoryFormIdToComplete;

  useEffect(() => {
    if (dataMe?.me) {
      const { lang } = dataMe?.me;
      i18n.changeLanguage(lang);
    }
  }, [dataMe]);

  /* Global config TODO virer partout et récup de là */
  const { data: dataConfig } = useQuery(GET_CONFIG, {
    fetchPolicy: 'cache-and-network',
    ...DEFAULT_CONFIG_VARIABLES
  });
  const config = dataConfig?.config;
  const colorsHeader =
    tryParseJSONObject(getValueFromKeyConfigData(config, CONFIG_KEYS.COLORS_BREADCRUMB)) || {};
  const webSiteName = getValueFromKeyConfigData(config, CONFIG_KEYS.WEBSITE_NAME);
  const logoMenuBar = getValueFromKeyConfigData(config, CONFIG_KEYS.LOGO_MENUBAR);
  const enabledLanguages = tryParseJSONObject(
    getValueFromKeyConfigData(config, CONFIG_KEYS.ENABLED_LANGUAGES_FOR_CONTENT)
  );
  const appearance = tryParseJSONObject(getValueFromKeyConfigData(config, CONFIG_KEYS.APPEARANCE));
  const defaultAvatars = tryParseJSONObject(
    getValueFromKeyConfigData(config, CONFIG_KEYS.DEFAULT_AVATARS)
  );

  // update favicon
  useFavicon(appearance?.faviconUrl ? getPublicSrc(appearance?.faviconUrl) : null);

  useEffect(() => {
    if (appearance?.primaryColor) {
      ConfigProvider.config({
        //prefixCls: 'custom',
        /*
        theme: {
          primaryColor: appearance?.primaryColor,
          errorColor: appearance?.errorColor || '', // '#f55454',
          successColor: appearance?.successColor || '', // '#5ec751',
          infoColor: appearance?.infoColor || '', //'#75A5D5',
          linkColor: appearance?.primaryColor, //'#d87c22',
          headingColor: '#ffffff',
          borderRadiusBase: '20px',
          // LESS USEFUL
          //textColor:
          //textColorSecondary: '#000000',
          //borderRadiusBase: '#000000',
          //borderColorBase: '#000000',
          //boxShadowBase: '#000000',
        },
         */
      });
    }
  }, [dataConfig]);

  const [localArrayAnnees, setLocalArrayAnnees] = useLocalStorage(
    'localArrayAnnees',
    'initValueBlankLayer'
  );

  useEffect(() => {
    if (annees) {
      const latestYear = getLatestYear(annees?.map((annee) => annee.annee));
      //setLocalArrayAnnees([latestYear])
      setLastSelectedYearForQcm(latestYear);
      setLastSelectedYear(latestYear);
    }
  }, [annees]);

  const { language } = i18n;
  const [configLocale, setconfigLocale] = useState(frFR);

  useEffect(() => {
    switch (language) {
      case 'fr':
        setconfigLocale(frFR);
        moment.locale('fr');
        dayjs.locale('fr');
        break;
      case 'en':
        setconfigLocale(enUS);
        moment.locale('en');
        dayjs.locale('en');
        break;
      case 'es':
        setconfigLocale(esES);
        moment.locale('es');
        dayjs.locale('es');
        break;
      case 'de':
        setconfigLocale(deDE);
        moment.locale('de');
        dayjs.locale('de');
        break;
      case 'it':
        setconfigLocale(itIT);
        moment.locale('it');
        dayjs.locale('it');
        break;
      default:
        setconfigLocale(frFR);
        moment.locale('fr');
        dayjs.locale('fr');
        break;
    }
  }, [language]);

  // Permet de rediriger vers '/CGU', lorsque l'user est log, et qu'il n'a pas accepté les CGU
  if (
    !loadingMe &&
    !errorMe &&
    dataMe?.me !== null &&
    dataMe?.me?.hasAcceptedCGU === false &&
    window.location.hash !== '#/CGU'
  ) {
    router.push('/CGU');
  }
  // Rediriger vers premier formulaire obligatoire et qui n'a pas été fait si le user a accepté les CGU
  if (
    dataMe?.me?.hasAcceptedCGU === true &&
    formIdToComplete &&
    window.location.hash !== `#/form/${formIdToComplete}`
  ) {
    router.push(`/form/${formIdToComplete}`);
  }

  return (
    <GlobalContext.Provider
      value={{
        ueName,
        setUEName,
        ueCategory,
        setUECategory,
        navigationStyle,
        setNavigationStyle,
        globalBannerText,
        globalBannerSubtitle,
        setGlobalBannerText,
        setGlobalBannerSubtitle,
        ueId,
        setUeId,
        categoryId,
        setCategoryId,
        coursId,
        setCoursId,
        breadCrumbImage,
        setBreadCrumbImage,
        breadCrumbImageType,
        setBreadCrumbImageType,

        nextCours,
        setNextCours,
        annees,
        colorsHeader,

        webSiteName,
        logoMenuBar,
        appearance,
        enabledLanguages,
        listOrCardNavigation,
        setListOrCardNavigation,

        //chatGPTEnabled,
        localArrayAnnees,
        setLocalArrayAnnees,

        me: dataMe?.me,
        refetchMe,
        defaultAvatars,

        unseenPrivateMessagesCount,
        refetchPrivateMessagesCount,
        features
      }}
    >
      <ConfigProvider
        locale={configLocale}
        theme={{
          token: {
            colorPrimary: appearance?.primaryColor || '#1677ff',
            colorSuccess: appearance?.successColor || '#52c41a',
            colorWarning: appearance?.warningColor || '#faad14',
            colorError: appearance?.errorColor || '#ff4d4f',
            borderRadius: 10
            //colorInfo: appearance?.infoColor || '#1677ff',
          }
        }}
      >
        {props.children}
      </ConfigProvider>
    </GlobalContext.Provider>
  );
};

// Global LAYOUT
const Layout = ({ children }) => {
  const [isReady, setIsReady] = useState(false);

  // Permet d'attendre que tout le react soit monté avant d'afficher la page
  useEffect(() => {
    setIsReady(true);
  }, []);

  if (!isReady) {
    return <PageLoading />;
  }

  return (
    <ApolloProvider client={client}>
      <GlobalContextProvider>{children}</GlobalContextProvider>
    </ApolloProvider>
  );
};
export default Layout;
