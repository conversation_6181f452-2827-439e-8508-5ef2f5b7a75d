import React, { useContext } from 'react';
import { Config<PERSON><PERSON><PERSON>, Divider, Menu } from 'antd';
import { Link } from 'umi';
import { useTranslation } from 'react-i18next';

import { isAdmin, isCommercial, isParent, isTuteur } from '@/shared/utils/authority';
import { getApparenceAttribute } from '@/shared/services/config';
import { tr } from '@/shared/services/translate';
import { useQuery } from '@apollo/client';
import { GET_CONFIG } from '@/shared/graphql/home.js';
import { CONFIG_KEYS } from '@/shared/services/config.js';
import { tryParseJSONObject } from '@/shared/utils/utils.js';
import {
  BookMarked,
  CalendarDays,
  ChartCandlestick,
  CircleHelp,
  CircleUserRound,
  Megaphone,
  ShieldEllipsis,
  Users
} from 'lucide-react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';

const AppMenu = ({ location, siderCollapsed }) => {
  const { t, i18n } = useTranslation();

  const { appearance: localAppearance } = useContext(GlobalContext);
  const primaryColor = localAppearance?.primaryColor;

  const selectedKey = location.pathname;
  const getSelectedKey = () => {
    if (selectedKey.startsWith('/cours')) return ['/cours'];
    if (selectedKey.startsWith('/qcm')) return ['/qcm'];
    if (selectedKey.startsWith('/planning')) return ['/planning'];
    if (selectedKey.startsWith('/discussions')) return ['/discussions'];
    if (selectedKey.startsWith('/profile')) return ['/profile'];
    if (selectedKey.startsWith('/tuteurs')) return ['/tuteurs'];
    if (selectedKey.startsWith('/admin')) return ['/admin/dashboard'];
    return [selectedKey];
  };
  const defaultOpenKey = '/' + selectedKey.split('/')[1];

  const { data } = useQuery(GET_CONFIG);
  const appearance = tryParseJSONObject(
    data?.config?.find((item) => item.key === CONFIG_KEYS.APPEARANCE)?.value
  );
  const navbarLabels = getApparenceAttribute(appearance, 'navbarLabels');

  const showPlanning = getApparenceAttribute(appearance, 'navbarShowPlanning');

  const getLabel = (name) => {
    return navbarLabels?.[tr(name)] || i18n.t(name);
  };
  const isParentOrCommercial = isParent() || isCommercial();

  // Check if the user is a parent or a commercial, if so, return null
  if (isParentOrCommercial) {
    return null;
  }

  const canBeShown = (name) => {
    const showItemKey = `navbarShow${name}`;
    return getApparenceAttribute(appearance, showItemKey) !== false;
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Menu: {
            itemSelectedBg: primaryColor,
            itemSelectedColor: '#ffffff',
            itemActiveBg: primaryColor,
            itemHeight: 55,
            collapsedIconSize: 28,
            iconSize: 24
          }
        }
      }}
    >
      <Menu
        mode="inline"
        inlineIndent={0}
        style={{ height: '100%', borderRight: 0 }}
        selectedKeys={getSelectedKey()}
        defaultOpenKeys={[defaultOpenKey]}
      >
        {canBeShown('Courses') && (
          <>
            <Menu.Item key="/cours" icon={<BookMarked />} style={{ paddingLeft: 26 }}>
              <Link to="/cours">{getLabel('Courses')}</Link>
            </Menu.Item>
            <Divider style={{ margin: '5px 0' }} />
          </>
        )}

        {canBeShown('Exercices') && (
          <>
            <Menu.Item key="/qcm" icon={<ChartCandlestick />} style={{ paddingLeft: 26 }}>
              <Link to="/qcm">{getLabel('Exercices')}</Link>
            </Menu.Item>
            <Divider style={{ margin: '5px 0' }} />
          </>
        )}

        {showPlanning && canBeShown('Agenda') && (
          <>
            <Menu.Item key="/planning" icon={<CalendarDays />} style={{ paddingLeft: 26 }}>
              <Link to="/planning">{getLabel('Agenda')}</Link>
            </Menu.Item>
            <Divider style={{ margin: '5px 0' }} />
          </>
        )}

        {canBeShown('Forum') && (
          <>
            <Menu.Item key="/discussions" icon={<Megaphone />} style={{ paddingLeft: 26 }}>
              <Link to="/discussions">{getLabel('Forum')}</Link>
            </Menu.Item>
            <Divider style={{ margin: '5px 0' }} />{' '}
          </>
        )}

        {canBeShown('MyProfile') && (
          <>
            <Menu.Item key="/profile" icon={<CircleUserRound />} style={{ paddingLeft: 26 }}>
              <Link to="/profile">{getLabel('MyProfile')}</Link>
            </Menu.Item>
            <Divider style={{ margin: '5px 0' }} />{' '}
          </>
        )}

        {canBeShown('Team') && (
          <>
            <Menu.Item key="/tuteurs" icon={<Users />} style={{ paddingLeft: 26 }}>
              <Link to="/tuteurs">{getLabel('Team')}</Link>
            </Menu.Item>
            <Divider style={{ margin: '5px 0' }} />{' '}
          </>
        )}

        {isAdmin() && (
          <>
            <Menu.Item key="/admin/dashboard" icon={<ShieldEllipsis />} style={{ paddingLeft: 26 }}>
              <Link to="/admin/dashboard">{getLabel('Admin')}</Link>
            </Menu.Item>
            <Divider style={{ margin: '5px 0' }} />
          </>
        )}

        {isTuteur() && (
          <>
            <Menu.Item
              key="tuteur-panel/users "
              icon={<ShieldEllipsis />}
              style={{ paddingLeft: 26 }}
            >
              <Link to="tuteur-panel/users">{getLabel('Admin')}</Link>
            </Menu.Item>
            <Divider style={{ margin: '5px 0' }} />
          </>
        )}

        {(isAdmin() || isTuteur()) && (
          <>
            <Menu.Item key="admin-help" icon={<CircleHelp />} style={{ paddingLeft: 26 }}>
              <a
                href="https://exoteach.notion.site/Guide-Administrateurs-d3c975278ba34036aba19409cd0a881b?pvs=4"
                target="_blank"
                rel="noopener noreferrer"
              >
                {t('general.Help')}
              </a>
            </Menu.Item>
            <Divider style={{ margin: '5px 0' }} />
          </>
        )}
      </Menu>
    </ConfigProvider>
  );
};

export default AppMenu;
