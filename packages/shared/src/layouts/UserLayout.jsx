import { GET_CONFIG } from '@/shared/graphql/home.js';
import useFavicon from '@/shared/hooks/useFavicon.js';
import {
  CONFIG_KEYS,
  DEFAULT_CONFIG_VARIABLES,
  getParticlesParamsToShow,
  getPublicSrc,
} from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import { client } from '@/shared/utils/apolloClient.js';
import { GlobalConfig, tryParseJSONObject } from '@/shared/utils/utils.js';
import { getMenuData, getPageTitle } from '@ant-design/pro-layout';
import { ApolloProvider, useQuery } from '@apollo/client';
import { Helmet } from 'react-helmet';
import React, { useCallback } from 'react';
import { connect } from 'dva';
import Particles from 'react-particles';
import { loadFull } from "tsparticles";
import styles from '@/shared/layouts/UserLayout.less';

const UserLayout = props => {
  const {
    route = {
      routes: [],
    },
  } = props;
  const { routes = [] } = route;
  const {
    children,
    location = {
      pathname: '',
    },
  } = props;
  const { breadcrumb } = getMenuData(routes);
  const title = getPageTitle({
    pathname: location.pathname,
    breadcrumb,
    ...props,
  });

  const getStyleContainer = () => {
    if (GlobalConfig.get().appName === 'Aptoria') {
      return styles.containerAptoria;
    }
    return styles.container;
  };

  const {
    loading: loadingConfig,
    error: errorConfig,
    data: dataConfig,
    refetch: refetchConfig,
  } = useQuery(GET_CONFIG, { fetchPolicy: 'cache-and-network', ...DEFAULT_CONFIG_VARIABLES });

  const config = dataConfig?.config;
  const colors = tryParseJSONObject(getValueFromKeyConfigData(config, CONFIG_KEYS.COLORS_LOGINPAGE)) || {};
  const appearance = tryParseJSONObject(getValueFromKeyConfigData(config, CONFIG_KEYS.APPEARANCE)) || {};
  const params = getParticlesParamsToShow(appearance, 'loginPageBackgroundAnimation');
  // update favicon
  useFavicon(appearance?.faviconUrl ? getPublicSrc(appearance?.faviconUrl) : null);
  const hasCustomBackgroundLoginPage = !!(appearance?.backgroundImageForLoginPage);

  const particlesInit = useCallback(async engine => {
    await loadFull(engine);
  }, [])

  return (
    <>
      <Helmet>
        <meta name="description" content={title}/>
        <title>{title}</title>
      </Helmet>

      <div
        className={getStyleContainer()}
        style={{
          background: hasCustomBackgroundLoginPage ? `linear-gradient(to bottom, rgba(255,255,255,0) 70%, rgba(255,255,255,1) 100%),url(${getPublicSrc(appearance?.backgroundImageForLoginPage)}) no-repeat center center` : `linear-gradient(90deg, ${colors?.color} 0%, ${colors?.color2} 100%) no-repeat 50% 50%`,
          backgroundSize: 'cover',
          width: '100%',
          height: '100%',
          //zIndex: 10
        }}
      >
        <div className={styles.divParticle}>
          {params && (
            <Particles
              init={particlesInit}
              height="100vh"
              options={params}
            />
          )}
        </div>
        {children}
      </div>
    </>
  );
};


const UserLayoutWrapper = props => {
  //console.log('UserLayoutWrapper shared', props)
  return (
    <ApolloProvider client={client}>
      <UserLayout {...props} />
    </ApolloProvider>
  );
};


export default connect(({ settings }) => ({ ...settings }))(UserLayoutWrapper);
