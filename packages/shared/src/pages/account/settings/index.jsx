import BlockedUsers from '@/shared/pages/account/settings/components/BlockedUsers.jsx';
import DeleteMyAccount from '@/shared/pages/account/settings/components/DeleteMyAccount.jsx';
import { NotificationsParUE } from '@/shared/pages/account/settings/components/NotificationsParUE.jsx';
import { isAptoria } from '@/shared/utils/utils.js';
import React, { useEffect, useRef, useState } from 'react';
import { Menu } from 'antd';
import BaseView from './components/profil';
import PreferencesView from './components/notification';
import ChangePasswordView from './components/security';
import styles from './style.less';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import { GET_ME_FULL_PROFILE, GET_ME_MY_BILLS } from '@/shared/graphql/user';
import { UserBillsTable } from '@/shared/components/User/UserBillsTable';

const { Item } = Menu;

export const MyBillsView = () => {
  const { t } = useTranslation();

  const { data, loading, refetch, error } = useQuery(GET_ME_MY_BILLS, {
    fetchPolicy: 'cache-and-network'
  });
  const me = data?.me;
  const bills = me?.bills;

  return (
    <UserBillsTable loading={loading} bills={bills} refetch={refetch} showRegenerate={false} />
  );
};

const Settings = (props) => {
  const { t } = useTranslation();
  const main = useRef(null);
  const [mode, setMode] = useState('inline');
  const [menuMap, setMenuMap] = useState({});
  const [selectKey, setSelectKey] = useState('profil');

  const resize = () => {
    if (!main) {
      return;
    }
    requestAnimationFrame(() => {
      if (!main) {
        return;
      }
      let mode = 'inline';
      const { offsetWidth } = main;
      if (main?.offsetWidth < 641 && offsetWidth > 400) {
        mode = 'horizontal';
      }
      if (window.innerWidth < 768 && offsetWidth > 400) {
        mode = 'horizontal';
      }
      setMode(mode);
    });
  };

  useEffect(() => {
    let tmpMenuMap = {};
    if (isAptoria) {
      tmpMenuMap = {
        profil: <>{t('general.Profile')}</>,
        bills: <>{t('Invoices')}</>,
        security: <>{t('general.Security')}</>
      };
    } else {
      tmpMenuMap = {
        profil: <>{t('MyAccount')}</>,
        /*
        activity: (
          <>Activité</>
        ),
         */
        security: <>{t('general.Security')}</>,
        bills: <>{t('Invoices')}</>,
        preferences: <>{t('general.preferences')}</>,
        notifications: <>{t('general.Notifications')}</>,
        blockedUsers: <>{t('BlockedUsers')}</>,
        deleteMyAccount: <>{t('DeleteMyAccount')}</>
      };
    }
    setMenuMap(tmpMenuMap);
    window.addEventListener('resize', resize);
    return () => {
      // componentwillunmount in functional component.
      // Anything in here is fired on component unmount.
      window.removeEventListener('resize', resize);
    };
  }, []);

  const renderChildren = () => {
    switch (selectKey) {
      case 'profil':
        return <BaseView />;
      case 'security':
        return <ChangePasswordView />;
      case 'bills':
        return <MyBillsView />;
      case 'preferences':
        return <PreferencesView />;
      case 'notifications':
        return <NotificationsParUE />;
      case 'blockedUsers':
        return <BlockedUsers />;
      case 'deleteMyAccount':
        return <DeleteMyAccount />;
      default:
        break;
    }
    return null;
  };

  const getMenu = () => {
    return Object.keys(menuMap).map((item) => <Item key={item}>{menuMap[item]}</Item>);
  };

  const getRightTitle = () => {
    return menuMap[selectKey];
  };

  return (
    <div className={styles.main} ref={main}>
      <div className={styles.leftMenu}>
        <Menu mode={mode} selectedKeys={[selectKey]} onClick={({ key }) => setSelectKey(key)}>
          {getMenu()}
        </Menu>
      </div>
      <div className={styles.right}>
        <div className={styles.title}>{getRightTitle()}</div>
        {renderChildren()}
      </div>
    </div>
  );
};

export default Settings;
