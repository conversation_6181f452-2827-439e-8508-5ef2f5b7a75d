import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import ExoAvatar from '@/shared/components/User/ExoAvatar.jsx';
import { QUERY_MY_BLOCKED_USERS } from '@/shared/graphql/user/index.js';
import { useQuery } from '@apollo/client';
import { List } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { UserProfileCard } from '@/shared/components/User/UserProfileCard/UserProfileCard';

export default function (props) {
  const { t } = useTranslation();
  const { data, loading, error, refetch } = useQuery(QUERY_MY_BLOCKED_USERS, {
    variables: {},
    fetchPolicy: 'cache-and-network'
  });

  const users = data?.myBlockedUsers;
  const countUsers = data?.myBlockedUsers?.length;

  return (
    <>
      {loading && <SpinnerCentered />}
      {countUsers > 0 && (
        <List
          itemLayout="horizontal"
          dataSource={users}
          renderItem={(user) => (
            <List.Item>
              <List.Item.Meta
                title={
                  <UserProfileCard userId={user?.id} username={user?.username}>
                    <span>{user?.username}</span>
                  </UserProfileCard>
                }
                avatar={
                  <UserProfileCard userId={user?.id} username={user?.username}>
                    <ExoAvatar avatar={user?.avatar} />
                  </UserProfileCard>
                }
              />
            </List.Item>
          )}
        />
      )}

      {countUsers === 0 && <p>{t('NoUserBlocked')}</p>}
    </>
  );
}
