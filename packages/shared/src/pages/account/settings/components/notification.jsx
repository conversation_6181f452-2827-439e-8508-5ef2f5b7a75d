import { GET_ME } from '@/shared/models/user.js';
import { isAdmin, isTuteur } from '@/shared/utils/authority.js';
import { List, Switch } from 'antd';
import React, { Fragment, useState, useMemo } from 'react';
import { GET_USER_SETTINGS, SET_USER_SETTINGS, UPDATE_MY_PROFILE } from '@/shared/graphql/user/index';
import { useMutation, useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';

const NotificationView = (props) => {
  const { loading: loadingQuery, error, data, refetch } = useQuery(GET_USER_SETTINGS);
  const { loading: loadingQueryMe, error: errorMe, data: dataMe, refetch: refetchMe } = useQuery(GET_ME);
  const me = dataMe?.me;
  const {t} = useTranslation();

  const [setSettings, { loading: loadingMutation, errort, data: dataSetting }] = useMutation(SET_USER_SETTINGS);
  const [updateMyProfile] = useMutation(UPDATE_MY_PROFILE);

  const [privateMessageEmailNotification, setPrivateMessageEmailNotification] = useState(true);
  const [coursUpdateEmailNotification, setCoursUpdateEmailNotification] = useState(true);
  const [coursUpdateDeviceNotification, setCoursUpdateDeviceNotification] = useState(true);
  const [qcmUpdateDeviceNotification, setQcmUpdateDeviceNotification] = useState(true);
  const [reachableByPrivateMessage, setReachableByPrivateMessage] = useState(true);

  useMemo(() => {
    if (data != null && data.userNotificationSettings != null) {
      setPrivateMessageEmailNotification(data ? data.userNotificationSettings.privateMessageEmailNotification : true);
      setCoursUpdateDeviceNotification(data ? data.userNotificationSettings.coursUpdateDeviceNotification : true);
      setCoursUpdateEmailNotification(data ? data.userNotificationSettings.coursUpdateEmailNotification : true);
      setQcmUpdateDeviceNotification(data ? data.userNotificationSettings.qcmUpdateDeviceNotification : true);
    }
  }, [data, dataSetting]);
  
  useMemo(() => {
    if(me) {
      setReachableByPrivateMessage(me?.isReachableByPrivateMessage);
    }
  }, [dataMe]);

  function onSwitchCoursUpdateEmailNotification(event) {
    setCoursUpdateEmailNotification(event);
    setSettings({
      variables: {
        privateMessageEmailNotification,
        coursUpdateEmailNotification: event,
        coursUpdateDeviceNotification,
        qcmUpdateDeviceNotification,
      },
    });
    refetch();
  }

  function onSwitchPrivateMessageEmailNotification(event) {
    setPrivateMessageEmailNotification(event);
    setSettings({
      variables: {
        privateMessageEmailNotification: event,
        coursUpdateEmailNotification,
        coursUpdateDeviceNotification,
        qcmUpdateDeviceNotification,
      },
    });
    refetch();
  }

  function onSwitchCoursUpdateDeviceNotification(event) {
    setCoursUpdateDeviceNotification(event);
    setSettings({
      variables: {
        privateMessageEmailNotification,
        coursUpdateEmailNotification,
        coursUpdateDeviceNotification: event,
        qcmUpdateDeviceNotification,
      },
    });
    refetch();
  }

  function onSwitchQcmUpdateDeviceNotification(event) {
    setQcmUpdateDeviceNotification(event);
    setSettings({
      variables: {
        privateMessageEmailNotification,
        coursUpdateEmailNotification,
        coursUpdateDeviceNotification,
        qcmUpdateDeviceNotification: event,
      },
    });
    refetch();
  }

  const onSwitchContactByMp = async (event) => {
    setReachableByPrivateMessage(event);
    await updateMyProfile({
      variables: {
        isReachableByPrivateMessage: event,
      }
    })
    refetchMe();
  }

  const getData = () => {
    const NoticeCoursEmail = (
      <Switch
        loading={loadingQuery || loadingMutation}
        checkedChildren="Oui"
        unCheckedChildren="Non"
        checked={coursUpdateEmailNotification}
        onChange={onSwitchCoursUpdateEmailNotification}
      />
    );
    const NoticeMessageEmail = (
      <Switch
        loading={loadingQuery || loadingMutation}
        checkedChildren="Oui"
        unCheckedChildren="Non"
        checked={privateMessageEmailNotification}
        onChange={onSwitchPrivateMessageEmailNotification}
      />
    );
    const NoticeCoursTel = (
      <Switch
        loading={loadingQuery || loadingMutation}
        checkedChildren="Oui"
        unCheckedChildren="Non"
        checked={coursUpdateDeviceNotification}
        onChange={(event) => onSwitchCoursUpdateDeviceNotification(event)}
      />
    );
    const NoticeQCMTel = (
      <Switch
        loading={loadingQuery || loadingMutation}
        checkedChildren="Oui"
        unCheckedChildren="Non"
        checked={qcmUpdateDeviceNotification}
        onChange={onSwitchQcmUpdateDeviceNotification}
      />
    );

    const ContactableByMP = (
      <Switch
        loading={loadingQuery || loadingMutation || loadingQueryMe}
        checkedChildren="Oui"
        unCheckedChildren="Non"
        checked={reachableByPrivateMessage}
        onChange={onSwitchContactByMp}
      />
    );

    const array = [
      /*
      {
        title: 'Notifications de cours par email',
        description: 'Recevoir un email quand un nouveau cours est mis en ligne',
        actions: [NoticeCoursEmail],
      },
      {
        title: 'Notification messages privés par email',
        description: 'Recevoir un email quand je reçois un message privé',
        actions: [NoticeMessageEmail],
      },
      */
      {
        title: t('CourseNotificationOnMobile'),
        description: t('ReceiveNotificationWhenNewCourseIsPublished'),
        actions: [NoticeCoursTel],
      },
      {
        title: t('ExercisesNotificationsOnMobile'),
        description: t('ReceiveNotificationWhenNewExerciseIsPublished'),
        actions: [NoticeQCMTel],
      },
    ];

    if (isTuteur() || isAdmin()) {
      array.push({
        title: t('ContactableByPrivateMessage'),
        description: t('UsersCanSendYouPrivateMessages'),
        actions: [ContactableByMP],
      });
    }

    return array;
  };

  return (
    <Fragment>
      <List
        itemLayout="horizontal"
        dataSource={getData()}
        renderItem={item => (
          <List.Item actions={item.actions}>
            <List.Item.Meta title={item.title} description={item.description}/>
          </List.Item>
        )}
      />
    </Fragment>
  );
};

export default NotificationView;
