import { isAdmin, isTuteur } from '@/shared/utils/authority';
import { GroupOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Popover } from 'antd';
import { useTranslation } from 'react-i18next';
import React from 'react';

export default function ForfaitGroupsList({ forfait }) {
  const { t } = useTranslation();

  const isTuteurOrAdmin = isTuteur() || isAdmin();
  if (!isTuteurOrAdmin) return null;

  const groupes = forfait?.requiredGroups?.filter((g) => !g.isIndividual) || [];
  const everyone = forfait?.requiredGroups?.length === 0;

  return (
    <>
      <Popover
        title={t('GroupsSeeingThisOffer')}
        content={
          <div>
            {everyone ? (
              <div>{t('Everyone')}</div>
            ) : (
              <div>
                {groupes?.map((groupe) => (
                  <div key={groupe.id}>{groupe.name}</div>
                ))}
              </div>
            )}
          </div>
        }
      >
        <Button type="link">
          <GroupOutlined />
        </Button>
      </Popover>
    </>
  );
}
