import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import defaultSettings from '@/shared/config/defaultSettings.js';
import { QUERY_EXAM_BY_ID } from '@/shared/graphql/exam/exam_queries.js';
import styles from '@/shared/pages/cours/details/components/style.less';
import ExamQuestionSerieCard from '@/shared/pages/exam/components/ExamQuestionSerieCard.jsx';
import ExamSessionCard from '@/shared/pages/exam/components/ExamSessionCard.jsx';
import { EXAMSESSION_AVAILABILITY } from '@/shared/services/exam.js';
import { tr } from '@/shared/services/translate.js';
import { isAdmin, isTuteur } from '@/shared/utils/authority.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { DATE_FORMATS, getColoredUEHeadStyle } from '@/shared/utils/utils.js';
import { Card, Empty } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { useQuery } from '@apollo/client';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import { DownCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

export default function (props) {
  const { examId } = props.match.params;
  const { t } = useTranslation();
  useEffectScrollTop();
  const { loading, error, data, refetch } = useQuery(QUERY_EXAM_BY_ID, {
    fetchPolicy: 'no-cache',
    variables: { id: examId }
  });
  const exam = data?.exam;
  const examSessions = exam?.examSessions;

  const smallDivider = (
    <div style={{ fontSize: '40px' }}>
      <DownCircleOutlined />
    </div>
  );

  const isSessionAvailable = (session) => {
    if (isAdmin() || isTuteur()) {
      return true;
    }

    const datesDiff = session?.datesDiffusion;
    let isAvailable = false;
    datesDiff.forEach((dateDiff) => {
      if (dateDiff.availability === EXAMSESSION_AVAILABILITY.allTheTime) {
        isAvailable = true;
      }
      const now = dayjs();
      if (dateDiff.availability === EXAMSESSION_AVAILABILITY.date) {
        // Date de début mais pas de fin
        if (dayjs(dateDiff?.date).isBefore(now)) {
          isAvailable = true;
        }
      }
      if (dateDiff.availability === EXAMSESSION_AVAILABILITY.period) {
        // Date de début et de fin
        if (dayjs(dateDiff?.date).isBefore(now) && dayjs(dateDiff?.dateEnd).isAfter(now)) {
          isAvailable = true;
        }
      }
    });
    return isAvailable;
  };

  const getAvailbilityDates = (session) => {
    const datesDiff = session?.datesDiffusion;
    const availabilityDatesString = [];
    datesDiff.forEach((dateDiff) => {
      if (dateDiff.availability === EXAMSESSION_AVAILABILITY.date) {
        // Date de début mais pas de fin
        availabilityDatesString.push(
          `Cet examen sera disponible à partir de ${dayjs(dateDiff?.date).format(DATE_FORMATS.FULL_FR)}`
        );
      } else if (dateDiff.availability === EXAMSESSION_AVAILABILITY.period) {
        // Date de début et de fin
        availabilityDatesString.push(
          `Cet examen sera disponible à partir de ${dayjs(dateDiff?.date).format(DATE_FORMATS.FULL_FR)} et se terminera ${dayjs(dateDiff?.dateEnd).format(DATE_FORMATS.FULL_FR)}`
        );
      } else {
        // No availability set
        availabilityDatesString.push(
          `Cet examen est disponible selon les dates de diffusion de chaque série`
        );
      }
    });
    return availabilityDatesString;
  };

  return (
    <>
      <FullMediParticlesBreadCrumb
        title={!loading ? `${exam?.[tr('name')] || exam?.name || ''}` : t('general.Loading...')}
      />
      <ExoteachLayout>
        {!loading && !exam && (
          <Card>
            <Empty description={t('ExamNotFound')} />
          </Card>
        )}
        {examSessions?.map((session, key) => (
          <Card
            key={key}
            className={styles.card}
            headStyle={getColoredUEHeadStyle(
              exam?.color1 || defaultSettings.colorCard,
              exam?.color2 || defaultSettings.colorCard
            )}
            loading={loading}
            bodyStyle={{
              padding: '5px'
            }}
          >
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
              {/* Left part: exam details and final result if any */}
              <div style={{ minWidth: '350px', flexGrow: 1, alignContent: 'center' }}>
                <ExamSessionCard session={session} exam={exam} refetchExam={refetch} />
              </div>

              {/* Right part */}
              <div style={{ flexBasis: '50%', flexGrow: 1, textAlign: 'center' }}>
                {isSessionAvailable(session) ? (
                  <>
                    {session?.examQuestionSeries?.map((questionSerie, k) => (
                      <div key={k}>
                        <ExamQuestionSerieCard
                          refetch={refetch}
                          questionSerie={questionSerie}
                          available
                        />
                        {k + 1 !== session?.examQuestionSeries?.length && smallDivider}
                      </div>
                    ))}
                  </>
                ) : (
                  <Card>
                    {getAvailbilityDates(session).map((availabilityDate, k) => (
                      <div key={k}>
                        <p>{availabilityDate}</p>
                        <br />

                        {session?.examQuestionSeries?.map((questionSerie, k) => (
                          <div key={k}>
                            <ExamQuestionSerieCard
                              refetch={refetch}
                              questionSerie={questionSerie}
                              available={false}
                            />
                            {k + 1 !== session?.examQuestionSeries?.length && smallDivider}
                          </div>
                        ))}
                      </div>
                    ))}
                  </Card>
                )}
              </div>
            </div>
          </Card>
        ))}
      </ExoteachLayout>
    </>
  );
}
