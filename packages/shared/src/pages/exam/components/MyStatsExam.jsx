import McqRanking from '@/shared/components/Mcq/McqRanking.jsx';
import MaxGrade from '@/shared/pages/qcm/components/correction/MaxGrade.jsx';
import MinGrade from '@/shared/pages/qcm/components/correction/MinGrade.jsx';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function MyStatsExam({ myStats, nombreQuestions, maxPoints }) {
  const {t} = useTranslation();
  return (
    <>
      <MinGrade minGrade={myStats?.minGrade} nombreQuestions={maxPoints || nombreQuestions}/>
      <MaxGrade maxGrade={myStats?.maxGrade} nombreQuestions={maxPoints || nombreQuestions}/>

      <div style={{ display: 'flex', flexDirection: 'column', flexGrow: 1, textAlign: 'center' }}>
        <McqRanking myRank={myStats?.classement} total={myStats?.total}/>
      </div>
    </>
  );
}