import {
  MUTATION_DELETE_EXAM_SCALE,
} from '@/shared/graphql/exam/exam_mutations.js'
import { ModalType } from '@/shared/pages/admin/groupes/CreateEditGroupeModal.jsx'
import { CreateEditExamBaremeModal } from '@/shared/pages/exam/admin/modals/CreateEditExamBaremeModal.jsx'
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { useMutation } from '@apollo/client'
import { Alert, Button, Divider, message, Popconfirm, Table, Tag } from 'antd'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next';

const ExamBaremeActions = ({ record, refetch }) => {
  const [editVisible, setEditVisible] = useState(false)
  const [mutationDeleteExamQuestionSerie] = useMutation(MUTATION_DELETE_EXAM_SCALE, { variables: { id: record.id } })
  const {t} = useTranslation();
  const deleteExamScale = async () => {
    try {
      await mutationDeleteExamQuestionSerie()
      message.success(t('DeletedWithSuccess'))
      refetch()
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e)
      console.error(e)
    }
  }
  return (
    <span>
      <Button
        // size="large"
        onClick={() => {
          setEditVisible(true)
        }}
        style={{ marginRight: 16 }}
        type="primary"
        shape="circle"
        icon={<EditOutlined/>}
      />

      <CreateEditExamBaremeModal
        examScale={record}
        isModalVisible={editVisible}
        refetch={refetch}
        modalType={ModalType.UPDATE}
        closeModalHandler={() => {
          setEditVisible(false)
          refetch() // Load new modifications
        }}
      />

      <Popconfirm
        title={t('SureToRemoveScale')}
        onConfirm={deleteExamScale}
        okText={t('general.yes')}
        cancelText={t('general.no')}
      >
        <Button
          shape="circle"
          style={{ marginRight: 16 }}
          type="danger"
          icon={<DeleteOutlined/>}
        />
      </Popconfirm>
    </span>
  )
}

export const EditExamScales = ({ refetch, exam, examScales }) => {
  const {t} = useTranslation();
  const [selectedMcqId, setSelectedMcqId] = useState()
  const [showMcqSelector, setShowMcqSelector] = useState(false)
  const [createVisible, setCreateVisible] = useState(false)

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Par défaut',
      dataIndex: 'isDefault',
      key: 'isDefault',
      render: (isDefault, f) => <Tag>{isDefault ? "Oui" : "Non"}</Tag>
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => (
        <ExamBaremeActions
          record={record}
          refetch={refetch}
          key={record.id}
        />
      ),
    },
  ]

  const defaultScales = examScales?.filter(s => s?.isDefault === true)
  const hasADefaultScale = defaultScales?.length === 1
  const hasNoDefaultScale = defaultScales?.length === 0
  const hasMoreThanOneDefaultScale = defaultScales?.length > 1

  return (
    <>
      <Button
        onClick={() => setCreateVisible(true)}
        type="primary"
      >
        {t('CreateNewScale')}
      </Button>

      <Divider>{t('ExamScales')}</Divider>

      {/*
      {hasNoDefaultScale && (
        <Alert
          message="Attention"
          description="Vous n'avez pas défini de barême par défaut pour cet examen"
          type="warning"
          showIcon
          closable
        />
      )}
      */}
      {hasMoreThanOneDefaultScale && (
        <Alert
          message="Attention"
          description="Vous avez plusieurs barêmes par défaut pour cet examen, veuillez en définir qu'un seul."
          type="warning"
          showIcon
          closable
        />
      )}

      <Table
        columns={columns}
        dataSource={examScales}
        scroll={{ x: true }}
      />

      <CreateEditExamBaremeModal
        modalType="CREATE"
        refetch={refetch}
        exam={exam}
        closeModalHandler={() => {
          setCreateVisible(false)
          refetch()
        }}
        isModalVisible={createVisible}
      />

    </>
  )
}