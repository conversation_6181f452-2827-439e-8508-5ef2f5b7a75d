import { MUTATION_ADD_TYPE_TO_EXAM, MUTATION_REMOVE_TYPE_FROM_EXAM } from '@/shared/graphql/exam/exam_mutations.js';
import { CONTENT_TYPE_VALUES } from '@/shared/pages/admin/qcm/types-qcm/index.jsx';
import { useMutation } from '@apollo/client'
import { message, Select, Tag } from 'antd'
import React from 'react'
import { useTranslation } from 'react-i18next';

export const ExamTypeManager = ({ typesQcm, examId, dataTypeQcm, onChange = null }) => {
  const [addTypeQcmToQcm, addGroupData] = useMutation(MUTATION_ADD_TYPE_TO_EXAM)
  const [removeTypeQcmFromQcm, removeGroupData] = useMutation(MUTATION_REMOVE_TYPE_FROM_EXAM)
  const {t} = useTranslation();

  const examSerieTypes = dataTypeQcm?.data?.allTypeQcm?.filter(typeQcm => typeQcm?.contentType === CONTENT_TYPE_VALUES.EXAMS)

  const groupeTagRender = ({ label, value, closable, onClose, key }) => (
    <Tag value={value} key={key} color="red" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
      {label}
    </Tag>
  )

  const handleSelect = async (_, option) => {
    try {
      if (onChange) {
        return
      }
      await addTypeQcmToQcm({ variables: { examId, typeQcmId: option.key } })
      message.success(`Type ${option.value} ajouté à cet examen`)
    } catch (e) {
      message.error(`Type ${option.value} n'a pas été ajouté`)
      console.error(e)
    }
  }
  const handleDeselect = async (_, option) => {
    try {
      if (onChange) {
        return
      }
      await removeTypeQcmFromQcm({ variables: { examId, typeQcmId: option.key } })
      message.success(`Type ${option.value} enlevé de cet examen`)
    } catch (e) {
      message.error(`Type ${option.value} n'a pas été enlevé`)
      console.error(e)
    }
  }

  return (
    <Select
      showArrow
      mode="multiple"
      style={{ maxWidth: '380px', minWidth: '380px' }}
      tagRender={groupeTagRender}
      placeholder={t('ChooseContentType')}
      defaultValue={typesQcm?.map(typeQuestion => typeQuestion.name)}
      loading={dataTypeQcm.loading || addGroupData.loading || removeGroupData.loading}
      options={examSerieTypes?.map(typeQcm => ({
        value: typeQcm.name,
        key: typeQcm.id,
      }))}
      onChange={onChange}
      onDeselect={handleDeselect}
      onSelect={handleSelect}
    />

  )
}