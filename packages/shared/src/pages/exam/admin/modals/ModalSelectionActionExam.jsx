import { EXAMS_MASS_ACTIONS } from '@/shared/graphql/exam/exam_mutations.js';
import { MUTATION_UPDATE_FOLDER } from '@/shared/graphql/folders.js';
import { FOLDER_TYPES as FolderType } from '@/shared/hooks/folders/useQueryFolders.jsx';
import FoldersTreeSelect from '@/shared/pages/admin/folders/components/FoldersTreeSelect.jsx';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { useMutation } from '@apollo/client';
import { Alert, Button, message, Modal, notification } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

const ModalSelectionActionExam = ({ isVisible, closeModalHandler, ids, itemAction }) => {
  const { t } = useTranslation();

  const [Mutation, { loading, data, error }] = useMutation(EXAMS_MASS_ACTIONS);
  const [updateFolder] = useMutation(MUTATION_UPDATE_FOLDER);

  const [folderIdState, setFolderIdState] = React.useState(null);

  const [isLoading, setIsLoading] = React.useState(false);

  const onMassMove = async () => {
    try {
      setIsLoading(true);
      notification.info({
        message: 'Déplacement en cours...',
        description: 'Veuillez patienter...',
        key: 'deplacement',
      });
      const realIds = ids.map(id => id.replace('Exam-', ''));
      const folderIds = ids?.map((id) => id?.replace('Folder-', ''));

      // Vérification ne pas déplacer un dossier dans lui même
      await Promise.all(folderIds.map(async (folderId) => {
        // Vérifie si le dossier n'est pas déplacé dans lui-même
        if (folderId === folderIdState) {
          message.error('Impossible de déplacer un dossier dans lui-même. Vérifiez le dossier cible');
          throw new Error('Impossible de déplacer un dossier dans lui-même. Vérifiez le dossier cible');
        }
      }));

      if(realIds?.length > 0) {
        await Mutation({
          variables: {
            ids: realIds,
            input: {
              folderId: folderIdState,
            },
            action: itemAction,
          },
        });
      }

      if(folderIds?.length > 0) {
        await Promise.all(folderIds.map(async (folderId) => {
          await updateFolder({
            variables: {
              id: folderId,
              folder: {
                parentId: folderIdState, // Deplace dans le dossier folderIdState
              },
            },
          });
        }));
      }

      notification.success({
        message: 'Déplacement terminé',
        description: 'Les éléments ont été déplacés',
        key: 'deplacement',
      });
      setIsLoading(false);
      closeModalHandler();
    } catch (e) {
      if(e.message === 'Impossible de déplacer un dossier dans lui-même. Vérifiez le dossier cible') {
        message.error(e.message);
        return;
      }
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  const entityIds = ids.filter(id => id.startsWith('Exam-'));
  const folderIds = ids.filter(id => id.startsWith('Folder-'));

  return (
    <>
      <Modal
        title={t('general.Edit')}
        open={isVisible}
        onCancel={closeModalHandler}
        footer={null}
        closable
        confirmLoading={false}
        destroyOnClose
      >
        {entityIds?.length > 0 && (
          <Alert type="info" message={`${entityIds?.length} exams sélectionnés`} />
        )}
        {folderIds?.length > 0 && (
          <Alert type="info" message={`${folderIds?.length} dossiers sélectionnés`} />
        )}

        {itemAction === 'move' && (
          <>
            <br />
            {t('general.Move')} {t('general.to')} :
            <br />
            <FoldersTreeSelect
              defaultValue={folderIdState}
              onClick={(shouldAdd, newFolderId) => {
                setFolderIdState(newFolderId);
              }}
              folderType={FolderType.EXAM}
            />
            <br />
            <br />
            <Button type={'primary'} loading={isLoading} onClick={onMassMove}>
              OK
            </Button>
          </>
        )}
      </Modal>
    </>
  );
};

export default ModalSelectionActionExam;
