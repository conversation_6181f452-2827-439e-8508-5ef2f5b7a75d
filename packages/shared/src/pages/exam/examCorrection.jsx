import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { QUERY_EXAM_BY_ID } from '@/shared/graphql/exam/exam_queries.js';
import {
  GET_CORRECTION_SESSION_QCM,
  GET_CORRECTION_SESSION_QCM_GRADE_ONLY,
  GET_SESSIONS_QCM_EXAM
} from '@/shared/graphql/qcm.js';
import ExamGrade from '@/shared/pages/exam/components/ExamGrade.jsx';
import CorrectionQcm from '@/shared/pages/qcm/components/correction/CorrectionQcm.jsx';
import { RepartitionErreursCorrection } from '@/shared/pages/qcm/components/correction/RepartitionErreursCorrection.jsx';
import { isAdmin, isTuteur } from '@/shared/utils/authority.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { Card, Col, Form, Popover, Row, Select, Table, Tabs } from 'antd';
import React, { useRef, useState } from 'react';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';

/* Note ou moyenne de série de question exam, affiché dans table */
const NoteQuestionSerie = ({ scale, type = 'grade', outOf20 = true }) => {
  const { t } = useTranslation();
  const questionSerie = scale?.questionSerie;
  const { data, loading, error } = useQuery(GET_SESSIONS_QCM_EXAM, {
    fetchPolicy: 'no-cache',
    variables: { examQuestionSerieId: questionSerie?.id }
  });
  const session = data?.sessionForMcqExam;
  const nombreQuestions = questionSerie?.qcm?.nombreQuestions;
  const maximumPoints = questionSerie?.qcm?.maximumPoints;
  const qcmId = questionSerie?.mcqId;

  const myGradeFromQuestionSerie = questionSerie?.myStats?.myGrade;

  const {
    data: dataCorrection,
    loading: loadingCorrection,
    error: errorCorrection
  } = useQuery(GET_CORRECTION_SESSION_QCM_GRADE_ONLY, {
    fetchPolicy: 'no-cache',
    variables: {
      id: qcmId,
      sessionId: session?.id
      // userId,
    },
    skip: !session
  });
  const qcm = dataCorrection?.qcm;

  const formatNote = (note) => parseFloat(note).toFixed(2);
  const maNote = () => {
    return formatNote(myGradeFromQuestionSerie);
  }; // From backend stats
  const moyenneSur20 =
    nombreQuestions && ((qcm?.resultat?.moyenne / maximumPoints) * 20)?.toFixed(2);
  nombreQuestions && ((qcm?.resultat?.note / maximumPoints) * 20)?.toFixed(2);
  const myGradeFromQuestionSerieOutOf20 = (
    (myGradeFromQuestionSerie / maximumPoints) *
    20
  )?.toFixed(2);
  return (
    <>
      {loading || loadingCorrection ? (
        <SpinnerCentered />
      ) : (
        <Popover
          content={
            <>
              {type === 'grade' ? maNote() : moyenneSur20} / {maximumPoints}
            </>
          }
          trigger="hover"
        >
          {type === 'grade' ? myGradeFromQuestionSerieOutOf20 : moyenneSur20} / 20
        </Popover>
      )}
    </>
  );
};

export default function (props) {
  useEffectScrollTop();
  const { examSessionId } = props.match.params;
  const { examId } = props.match.params;
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_EXAM_BY_ID, {
    fetchPolicy: 'no-cache',
    variables: {
      id: examId,
      sessionId: examSessionId // TODO check
      // todo add userID to see for a specific user (optionnal)
    }
  });
  const exam = data?.exam;
  const scales = exam?.examScales;
  const examSession = exam?.examSessions?.find((s) => s?.id === examSessionId);

  // QUERY exam session result
  // QUERY exam all

  const [selectedQuestionSerie, setSelectedQuestionSerie] = useState(null);

  // Navigation
  const [targetOffset, setTargetOffset] = useState(undefined);
  const [loadingPdf, setLoadingPdf] = useState(false);
  const [selectedGroupIds, setSelectedGroupIds] = useState([]);
  const refContent = useRef();
  const espaceCommentaireRef = useRef();
  const noteRef = useRef();
  const refCorrection = useRef();
  // --

  const qcmSessionId = selectedQuestionSerie?.qcmSession?.id;
  /* QUERY CORRECTION */
  const {
    loading: loadingCorrection,
    error: errorCorrection,
    data: dataCorrection,
    refetch: refetchCorrection
  } = useQuery(GET_CORRECTION_SESSION_QCM, {
    variables: {
      id: selectedQuestionSerie?.mcqId,
      sessionId: qcmSessionId
    },
    skip: !selectedQuestionSerie,
    fetchPolicy: 'cache-and-network'
  });

  const getQcm = () => dataCorrection?.qcm;
  const getQuestions = () => dataCorrection?.qcm?.questions;
  const correctionConfig = dataCorrection?.qcm?.correctionConfig;
  const shouldShowAnalysis =
    correctionConfig?.showAnalysis === undefined || correctionConfig?.showAnalysis;
  const shouldShowStrengthWeaknesses =
    correctionConfig?.showStrengthsWeaknesses === undefined ||
    correctionConfig?.showStrengthsWeaknesses;
  const isTuteurOrAdmin = isTuteur() || isAdmin();
  /* ---------------------- */

  const columns = [
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name',
      render: (_, scale) => scale?.questionSerie?.qcm?.titre
    },
    {
      title: 'Coefficient',
      dataIndex: 'coefficient',
      key: 'coefficient'
    },
    {
      title: t('general.Grade'),
      dataIndex: 'grade',
      key: 'questionSerie',
      render: (_, scale) => <NoteQuestionSerie scale={scale} />
    },
    {
      title: t('general.Average'),
      dataIndex: 'moyenne',
      key: 'questionSerie',
      render: (_, scale) => <NoteQuestionSerie scale={scale} type="moyenne" />
    }
  ];

  return (
    <>
      <FullMediParticlesBreadCrumb title={(loading && 'Chargement...') || `Résultat examen`} />

      <ExoteachLayout>
        {/* eslint-disable-next-line no-undef */}
        <Row justify="center" gutter={MOBILE === 1 ? 0 : [1, 1]} key="1">
          <Col xl={24} lg={24} md={24} sm={24} xs={24}>
            <Card loading={loading} title={`Résultat ${examSession?.name}`}>
              <div
                id="mcq-result"
                // style={{ display: 'flex', justifyContent: 'center', flexGrow: 2, marginBottom: 10 }}
              >
                <Tabs type="line" defaultActiveKey="0">
                  {scales?.map((scale, key) => (
                    <Tabs.TabPane tab={scale?.name} key={key}>
                      <ExamGrade scale={scale} session={examSession} width={130} showClassement />
                      <br />
                      <h4>{t('general.Details')}</h4>
                      <Table
                        columns={columns}
                        dataSource={scale?.examQuestionSeries}
                        scroll={{ x: true }}
                      />
                    </Tabs.TabPane>
                  ))}
                </Tabs>
              </div>

              <br />
              <h2>{t('SelectCorrection')}</h2>
              <Form layout="vertical">
                <Form.Item label={t('SeeCorrectionOf')}>
                  <Select
                    onChange={(value, opt) => {
                      setSelectedQuestionSerie(
                        examSession?.examQuestionSeries?.find((e) => e.id === value)
                      );
                    }}
                  >
                    {examSession?.examQuestionSeries?.map((questionSerie, key) => (
                      <Select.Option key={questionSerie?.id} value={questionSerie?.id}>
                        {questionSerie?.qcm?.titre}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Form>
            </Card>

            {loadingCorrection ? (
              <SpinnerCentered />
            ) : (
              <>
                <>
                  {/* Analyse de vos résultats */}
                  {selectedQuestionSerie?.qcmSession && (
                    <RepartitionErreursCorrection
                      id_qcm={getQcm()?.id_qcm}
                      sessionId={qcmSessionId}
                      loadingPdf={loadingPdf}
                      userId={selectedQuestionSerie?.qcmSession?.userId}
                      isSmartMcq={false}
                      shouldShowAnalysis={shouldShowAnalysis}
                      shouldShowStrengthWeaknesses={shouldShowStrengthWeaknesses}
                      isTuteurOrAdmin={isTuteurOrAdmin}
                      selectedGroupIds={selectedGroupIds}
                    />
                  )}
                  <br />
                </>

                {getQuestions() && correctionConfig?.showCorrection && (
                  <div id="correction-qcm" ref={refCorrection}>
                    <CorrectionQcm
                      qcm={getQcm()}
                      questions={getQuestions()}
                      setTargetOffset={() => {}}
                      hasRedoneMCQ={false}
                      isInSession
                      refetch={refetchCorrection}
                      showEditionButton
                    />
                  </div>
                )}
              </>
            )}
          </Col>
        </Row>
      </ExoteachLayout>
    </>
  );
}
