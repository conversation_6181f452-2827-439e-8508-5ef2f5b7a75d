import { TopicsList } from '@/shared/components/Commentaires/TopicsList';
import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { QUERY_FORUM_CATEGORY } from '@/shared/graphql/forum.js';
import { EditCreateForumCategoryModal } from '@/shared/pages/forum/components/EditCreateForumCategoryModal.jsx';
import { ForumBreadCrumb } from '@/shared/pages/forum/components/ForumBreadcrumb.jsx';
import { ForumCard } from '@/shared/pages/forum/components/ForumCard.jsx';
import { ForumCategory } from '@/shared/pages/forum/components/ForumCategory.jsx';
import { ForumSubjects } from '@/shared/pages/forum/components/ForumSubjects.jsx';
import { LatestPosts } from '@/shared/pages/forum/components/LatestPosts.jsx';
import { UEForums } from '@/shared/pages/forum/components/UEMatieresForumCategory.jsx';
import { CommentairesType } from '@/shared/services/commentaires.js';
import { ForumType } from '@/shared/services/forum.js';
import { isAdmin } from '@/shared/utils/authority.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { useQuery } from '@apollo/client';
import React, { useState } from 'react';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { PlusSquareOutlined } from '@ant-design/icons';
import CoursBreadcrumb from '@/shared/components/Cours/CoursBreadcrumb';

export default function (props) {
  useEffectScrollTop();
  const forumId = props.match.params.forumId || null;
  const type = props.match.params.type || ForumType.FORUM;
  const typeId = props.match.params.typeId || null;
  const { t } = useTranslation();

  const isForum = type === ForumType.FORUM;
  const matieresActivated = true;

  const { loading, error, data, refetch } = useQuery(QUERY_FORUM_CATEGORY, {
    pollInterval: 10000,
    fetchPolicy: 'cache-and-network',
    variables: {
      parentId: forumId
    }
  });

  const getForumCategories = () => data && data.forumCategories && data.forumCategories.categories;
  const getForumParents = () => data && data.forumCategories && data.forumCategories.parents;
  const [createForumCategoryVisible, setCreateForumCategoryVisible] = useState(false);
  const closeModalHandler = () => {
    setCreateForumCategoryVisible(false);
    refetch(); // Load new modifications
  };
  const breadCrumbName = () => {
    if (getForumParents()) {
      const [lastForum] = getForumParents().slice(-1);
      return (lastForum && lastForum.name) || 'Forum';
    }
    return '';
  };

  const renderAdminCreateForumCategory = () => (
    <>
      <div style={{ width: '3OOpx', textAlign: 'left', marginLeft: '15px' }}>
        <Button
          style={{ minHeight: '25px', marginBottom: 16 }}
          type="primary"
          icon={<PlusSquareOutlined />}
          onClick={() => {
            setCreateForumCategoryVisible(true);
          }}
        >
          {t('AddForumSection')}
        </Button>
      </div>

      <EditCreateForumCategoryModal
        closeModalHandler={closeModalHandler}
        isVisible={createForumCategoryVisible}
        modalType="CREATE"
        refetch={refetch}
        loading={loading}
        forumId={forumId}
      />
    </>
  );

  return (
    <>
      <FullMediParticlesBreadCrumb title={breadCrumbName()} />
      <ExoteachLayout>
        {type === ForumType.FORUM ? (
          <ForumBreadCrumb
            type={type}
            typeId={typeId}
            forumId={forumId}
            parents={getForumParents()}
          />
        ) : (
          <>
            <CoursBreadcrumb
              ueId={type === ForumType.UE ? typeId : null}
              categoryId={type === ForumType.UECategory ? typeId : null}
              coursId={type === ForumType.COURS ? typeId : null}
              inForum
              withRoot
            />
          </>
        )}
        {isForum && isAdmin() && renderAdminCreateForumCategory()}
        <div>
          <div>
            {isForum &&
              getForumCategories() &&
              getForumCategories().map((forumCategory) => (
                <ForumCategory
                  type={ForumType.FORUM}
                  key={forumCategory.id}
                  forumCategory={forumCategory}
                  refetch={refetch}
                >
                  {forumCategory.forums &&
                    forumCategory.forums.map((forum) => <ForumCard forum={forum} key={forum.id} />)}
                </ForumCategory>
              ))}

            {/* root matieres - DISABLED UNTIL FIXED FOR INFINITE HIERARCHY */}

            {matieresActivated && !forumId && (
              <UEForums forumId={forumId} type={type} typeId={typeId} />
            )}

            {forumId && (
              <ForumSubjects>
                <TopicsList id={forumId} type={CommentairesType.FORUM} refetch={refetch} isForum />
                {/*
                <Commentaires
                  isForum
                  id={forumId}
                  type={CommentairesType.FORUM}
                  refetch={refetch}
                />
                */}
              </ForumSubjects>
            )}
          </div>

          <div>
            <LatestPosts forumId={forumId} type={type} typeId={typeId} />
          </div>
        </div>
      </ExoteachLayout>
    </>
  );
}
