import { UEForumBreadCrumb } from '@/shared/components/Commentaires/UEForumBreadCrumb.jsx';
import { ExoPullToRefresh } from '@/shared/components/ExoPullToRefresh.jsx';
import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { QUERY_COURS_POST_DETAIL } from '@/shared/graphql/cours.js';
import { QUERY_EVENT_BY_ID } from '@/shared/graphql/events.js';
import { QUERY_FORUM_PARENTS } from '@/shared/graphql/forum.js';
import {
  QUERY_ANSWER_POST_DETAIL,
  QUERY_QCM_POST_DETAIL,
  QUERY_QUESTION_POST_DETAIL
} from '@/shared/graphql/qcm.js';
import { ApercuQuestionCorrection } from '@/shared/pages/admin/qcm/components/modal/components/ApercuQuestion.jsx';
import { ForumBreadCrumb } from '@/shared/pages/forum/components/ForumBreadcrumb.jsx';
import {
  CommentairesType,
  getDataObjectFromType,
  getMutationVariablesForCommentaire,
  getQueryFromCommentaireType,
  getQueryVariablesFromCommentaireType
} from '@/shared/services/commentaires.js';
import { ForumType } from '@/shared/services/forum.js';
import { tr } from '@/shared/services/translate.js';
import { isAdmin, isTuteur } from '@/shared/utils/authority.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { useMutation, useQuery } from '@apollo/client';
import { Alert, Breadcrumb, Collapse, notification } from 'antd';
import React, { useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'umi/index.js';
import { TopicDetail } from '@/shared/components/Commentaires/TopicDetail';
import { isMobile, showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { CREATE_POST } from '@/shared/graphql/posts';
import { ChatBar } from '@/shared/components/Commentaires/ChatBar';
import CoursBreadcrumb from '@/shared/components/Cours/CoursBreadcrumb';

export const ThreadPost = ({
  typeId,
  postId,
  type,
  showBanner = true,
  enablePullToRefresh = true
}) => {
  const { t } = useTranslation();

  const childRef = useRef();
  const [replyTo, setReplyTo] = useState(null);
  const [subject, setSubject] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editorContent, setEditorContent] = useState('');
  const [fileContent, setFileContent] = useState(null);
  const [fileImageContent, setFileImageContent] = useState(null);
  // New file list
  const [fileList, setFileList] = useState([]);

  const [createPost] = useMutation(CREATE_POST);

  // Si dans un forum, on récupère les parents
  const { data: dataForum } = useQuery(QUERY_FORUM_PARENTS, {
    fetchPolicy: 'cache-and-network',
    variables: {
      parentId: typeId
    },
    skip: type !== 'FORUM'
  });

  // Si post dans un cours, on récupère infos du cours
  const { data: dataCours } = useQuery(QUERY_COURS_POST_DETAIL, {
    fetchPolicy: 'cache-and-network',
    variables: {
      id: typeId
    },
    skip: type !== 'COURS'
  });
  const cours = dataCours && dataCours.cour;

  // Si post dans un event, on récupère infos
  const { data: dataEvent } = useQuery(QUERY_EVENT_BY_ID, {
    fetchPolicy: 'cache-and-network',
    variables: {
      id: typeId
    },
    skip: type !== 'EVENT'
  });
  const event = dataEvent && dataEvent.event;

  // Si post dans une serie, on récupère infos
  const { data: dataQcm } = useQuery(QUERY_QCM_POST_DETAIL, {
    fetchPolicy: 'no-cache',
    variables: {
      id: typeId
    },
    skip: type !== 'QCM'
  });

  // Si post sur un exercice, on récupère infos
  const { data: dataQuestion } = useQuery(QUERY_QUESTION_POST_DETAIL, {
    fetchPolicy: 'no-cache',
    variables: {
      id: typeId
    },
    skip: type !== CommentairesType.EXERCISE
  });

  const isQuestionAnswer = [
    CommentairesType.QUESTION_ANSWER,
    CommentairesType.QUESTION_ANSWERS_IN_QCM
  ].includes(type);
  const { data: dataAnswer } = useQuery(QUERY_ANSWER_POST_DETAIL, {
    fetchPolicy: 'no-cache',
    variables: {
      id: typeId
    },
    skip:
      type !== CommentairesType.QUESTION_ANSWER && type !== CommentairesType.QUESTION_ANSWERS_IN_QCM
  });
  const answer = dataAnswer?.answer;
  const question = dataQuestion?.question;
  const hasDoneQuestion = answer?.question?.doneByMe;
  const canSeeComments = isQuestionAnswer ? isAdmin() || isTuteur() || hasDoneQuestion : true;

  const getForumParents = () =>
    dataForum && dataForum.forumCategories && dataForum.forumCategories.parents;

  const getBannerTitle = () => {
    if (getForumParents()) {
      const [lastForum] = getForumParents().slice(-1);
      return (lastForum && lastForum.name) || 'Forum';
    }
    if (type === CommentairesType.COURS) {
      return cours?.[tr('name') || ''];
    }
    if (type === CommentairesType.EVENT) {
      return event?.[tr('name') || ''];
    }
    if (
      [CommentairesType.QUESTION_ANSWERS_IN_QCM, CommentairesType.QUESTION_ANSWER].includes(type)
    ) {
      return answer?.question?.parentQcm?.titre || '';
    }
    if (type === CommentairesType.EXERCISE) {
      return question?.parentQcm?.titre || '';
    }
    return '';
  };
  const getBannerSubtitle = () => {
    if (type === CommentairesType.COURS) {
      return cours?.[tr('text') || ''];
    }
    return '';
  };
  const getBannerImage = () => {
    if (type === CommentairesType.COURS) {
      const originalCours = cours;
      return originalCours?.ueCategory?.image || originalCours?.ueCategory?.ue?.image;
    }

    return null;
  };
  const getBannerImageType = () => {
    if (type === 'QCM') {
    }
    return undefined;
  };

  const { loading, error, data, refetch } = useQuery(getQueryFromCommentaireType(type), {
    variables: getQueryVariablesFromCommentaireType(type, typeId, {}),
    fetchPolicy: 'no-cache',
    skip: !typeId
  });
  const { data: dataQcmToMerge, error: errorQcmToMerge } = useQuery(
    getQueryFromCommentaireType(CommentairesType.QCM),
    {
      variables: getQueryVariablesFromCommentaireType(CommentairesType.QCM, typeId, {}),
      fetchPolicy: 'no-cache',
      skip: type !== CommentairesType.QUESTION_ANSWERS_IN_QCM
    }
  );

  const dataObject = (data && !error && getDataObjectFromType(type, data)) || [];
  const dataQcmObject =
    (dataQcmToMerge &&
      !errorQcmToMerge &&
      getDataObjectFromType(CommentairesType.QCM, dataQcmToMerge)) ||
    [];
  const isCommentListLoadedAndNotEmpty =
    (dataObject && dataObject.length > 0) || (dataQcmObject && dataQcmObject.length > 0);
  const autoNestPosts = (items, itemId = null) =>
    items
      .filter((item) => item.parentId === itemId)
      .map((item) => ({
        ...item,
        typeId: typeId,
        refetch,
        children: autoNestPosts(items, item.id)
      }));

  const topics =
    (isCommentListLoadedAndNotEmpty && autoNestPosts([...dataObject, ...dataQcmObject])) || [];

  const selectedTopic = useMemo(
    () => topics.find((t) => t.id === postId),
    [dataObject, dataQcmObject, postId]
  );
  const messageBeingReplied = useMemo(() => {
    return [...dataObject, ...dataQcmObject].find((message) => message.id === replyTo);
  }, [replyTo]);

  function sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  const submitMessage = async () => {
    try {
      const newPost = await getMutationVariablesForCommentaire(
        editorContent,
        type,
        typeId,
        replyTo !== null ? replyTo : selectedTopic.id,
        true,
        fileContent,
        fileImageContent,
        null,
        null,
        fileList
      );
      const { data: dataResult } = await createPost({ variables: { post: newPost } });
      notification.success({
        message: 'Message posté !'
        //description: '' //TODO voir si on ajoute un bouton VOIR
      });
      await refetch();
      setEditorContent(''); // vide l'éditeur
      setReplyTo(null);
      setFileList([]); // Reset file list
      if (dataResult && dataResult.createPost) {
        const { id } = dataResult.createPost;
        await sleep(150);
        const comm = document.getElementById(`mpost_${id}`);
        if (comm) {
          comm.scrollIntoView({
            behavior: 'smooth'
          });
        }
      }
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error({ e });
    }
  };

  const renderBreadCrumb = () => {
    switch (type) {
      case CommentairesType.FORUM:
        return (
          <ForumBreadCrumb
            type={ForumType.FORUM}
            forumId={typeId}
            parents={getForumParents()}
            subject={subject}
          />
        );
      case CommentairesType.COURS:
        return (
          <CoursBreadcrumb
            showLastItem
            coursId={cours?.id}
            //ue={ueCategory && ueCategory.ueCategory && ueCategory.ueCategory.ue}
            //ueCategory={ueCategory && ueCategory.ueCategory}
          />
        );
      case CommentairesType.QCM:
        return (
          <Breadcrumb style={{ fontSize: 22, marginLeft: '12px', fontWeight: 'bold' }}>
            <Breadcrumb.Item>
              <Link to={`/qcm/${typeId}`}>{t('SeeMcq')}</Link>
            </Breadcrumb.Item>
          </Breadcrumb>
        );
      case CommentairesType.EVENT:
        return (
          <Breadcrumb style={{ fontSize: 22, marginLeft: '12px', fontWeight: 'bold' }}>
            <Breadcrumb.Item>
              <Link to={`/event/${typeId}`}>{t('SeeEvent')}</Link>
            </Breadcrumb.Item>
          </Breadcrumb>
        );
      case CommentairesType.QUESTION_ANSWER:
      case CommentairesType.QUESTION_ANSWERS_IN_QCM:
        return (
          <>
            <UEForumBreadCrumb
              ue={answer?.question?.parentQcm?.UE}
              qcm={answer?.question?.parentQcm}
              showLastItem
            />
            <br />
          </>
        );
      case CommentairesType.EXERCISE:
        return (
          <>
            <UEForumBreadCrumb
              ue={question?.parentQcm?.UE}
              qcm={question?.parentQcm}
              showLastItem
            />
            <br />
          </>
        );
      default:
        return <></>;
    }
  };

  return (
    <>
      <ExoPullToRefresh
        enable={enablePullToRefresh}
        onRefresh={async () => {
          await refetch();
        }}
      >
        {showBanner && (
          <FullMediParticlesBreadCrumb
            title={getBannerTitle()}
            subtitle={getBannerSubtitle()}
            image={getBannerImage()}
            imageType={getBannerImageType()}
          />
        )}

        <>
          <ExoteachLayout>
            <br />
            {renderBreadCrumb()}
            &nbsp;
            {canSeeComments ? (
              <>
                {answer && (
                  <div style={{ width: 'auto' }}>
                    <Collapse defaultActiveKey={['1']}>
                      <Collapse.Panel header="Exercice" key="1">
                        <ApercuQuestionCorrection
                          question={answer.question}
                          shouldRefetch
                          highlightAnswerId={answer.id}
                          closeModalHandler={() => setIsModalVisible(false)}
                          showCorrectionLink
                        />
                      </Collapse.Panel>
                    </Collapse>
                  </div>
                )}
                {question && (
                  <div style={{ width: 'auto' }}>
                    <Collapse defaultActiveKey={['1']}>
                      <Collapse.Panel header="Question" key="1">
                        <ApercuQuestionCorrection
                          question={question}
                          shouldRefetch
                          closeModalHandler={() => setIsModalVisible(false)}
                          showCorrectionLink
                        />
                      </Collapse.Panel>
                    </Collapse>
                  </div>
                )}

                {selectedTopic && (
                  <div
                    style={
                      {
                        // Ajoute un flottement désagréable sur mobile
                        //maxHeight: 'calc(100vh - 90px)'
                        //overflowY: 'scroll'
                      }
                    }
                  >
                    <TopicDetail
                      refetch={refetch}
                      selectedTopic={selectedTopic}
                      type={type}
                      typeId={typeId}
                      setReplyTo={setReplyTo}
                    />
                    <ChatBar
                      replyTo={replyTo}
                      messageBeingReplied={messageBeingReplied}
                      setReplyTo={setReplyTo}
                      loading={loading}
                      editorContent={editorContent}
                      setEditorContent={setEditorContent}
                      setFileContent={setFileContent}
                      setFileImageContent={setFileImageContent}
                      fileList={fileList}
                      setFileList={setFileList}
                      submitMessage={submitMessage}
                      fileContent={fileContent}
                      fileImageContent={fileImageContent}
                      padding={'0 12px'}
                    />
                  </div>
                )}
              </>
            ) : (
              <Alert
                message="Commentaires et questions"
                description={
                  <>
                    <Link to={`/qcm/${answer?.question?.parentQcm?.id_qcm}`}>
                      {t('faitesCetExerciceAuMoinsUneFois')}
                    </Link>{' '}
                    {t('pourVoirCeCommentaireOuPoserUneQuestion')}
                  </>
                }
                type="info"
                showIcon
              />
            )}
            {isMobile && <div style={{ height: 158, width: '100%' }} />}
          </ExoteachLayout>
        </>
      </ExoPullToRefresh>
    </>
  );
};

export default function ThreadPostPage(props) {
  useEffectScrollTop();

  // id du post et typeId provenant de l'url
  const { typeId, postId, type } = props.match.params;

  return <ThreadPost {...props} typeId={typeId} postId={postId} type={type} />;
}
