import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import ExoAvatar from '@/shared/components/User/ExoAvatar.jsx';
import { QUERY_LATESTS_POSTS } from '@/shared/graphql/posts.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import {
  forumCategoryBodyStyle,
  forumCategoryStyle
} from '@/shared/pages/forum/components/ForumCategory.jsx';
import style from '@/shared/pages/forum/forumStyle.less';
import { buildThreadLinkFromComment } from '@/shared/services/commentaires.js';
import { renderPostParentName, sliceLastMessage } from '@/shared/services/forum.js';
import { displayDirectHtml, getColoredUEHeadStyle } from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { Card, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';

export const LatestPosts = (props) => {
  const { loading, data, error, refetch } = useQuery(QUERY_LATESTS_POSTS, { pollInterval: 2000 });
  const { colorsHeader = {} } = useContext(GlobalContext);
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  const { t } = useTranslation();
  return (
    <Card
      loading={loading}
      title={t('lastMessages')}
      headStyle={getColoredUEHeadStyle(primaryColor, primaryColor)}
      bodyStyle={forumCategoryBodyStyle}
      style={forumCategoryStyle}
    >
      {data?.latestPosts?.map((post) => (
        <Card
          key={post.id}
          hoverable
          type="inner"
          className={style.forumCard}
          onClick={() => router.push(buildThreadLinkFromComment(post))}
        >
          {post?.threadId ? (
            <Tag color={'geekblue'}>{t('Comment')}</Tag>
          ) : (
            <Tag color={'purple'}>{t('Post')}</Tag>
          )}
          <div style={{ maxHeight: 65, overflow: 'scroll', lineHeight: '18px' }}>
            <RenderQuillHtml>{post.title || post.text}</RenderQuillHtml>
          </div>
          <div style={{ fontSize: '11px' }}>
            {t('by')} <ExoAvatar size={20} avatar={post?.user?.avatar} />
            &nbsp;{post.user && post.user.username} {dayjs(post.updatedAt).fromNow()}
            <div style={{ fontSize: '12px' }}>{renderPostParentName(post)}</div>
          </div>
        </Card>
      ))}
    </Card>
  );
};
