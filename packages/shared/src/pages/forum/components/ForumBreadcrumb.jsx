import { ForumType } from '@/shared/services/forum.js';
import { EllipsisOutlined, HomeOutlined } from '@ant-design/icons';
import { Breadcrumb } from 'antd';
import React, { useState } from 'react';
import { Link } from 'umi';
import { useTranslation } from 'react-i18next';
import { ArrowRight } from 'lucide-react';

export const ForumBreadCrumb = ({ type, typeId, forumId, parents = [], subject = null }) => {
  const { t } = useTranslation();
  const [expanded, setExpanded] = useState(false);

  const isForum = type === ForumType.FORUM;
  const items = [];

  // ROOT
  items.push({
    key: 'home',
    title: (
      <Link to="/discussions" style={{ height: 36 }}>
        <HomeOutlined style={{ fontSize: 22 }} /> {t('Forum')}
      </Link>
    )
  });

  const total = parents.length;

  const buildParentItem = (forum) => ({
    key: forum.id,
    title: (
      <Link to={`/discussions/${forum.id}`} style={{ height: 36 }}>
        {forum.name}
      </Link>
    )
  });

  const buildCollapsedItems = () => {
    if (total <= 3 || expanded) {
      return parents.slice(0, subject ? total : total - 1).map(buildParentItem);
    }

    const first = buildParentItem(parents[0]);
    const penultimate = buildParentItem(parents[total - 1]);

    return [
      first,
      {
        key: 'ellipsis',
        title: (
          <a onClick={() => setExpanded(true)} style={{ height: 36 }}>
            <EllipsisOutlined />
          </a>
        )
      },
      penultimate
    ];
  };

  if (isForum && parents.length > 0) {
    items.push(...buildCollapsedItems());
  }

  if (isForum && subject) {
    items.push({
      key: 'subject',
      title: <span style={{ height: 36 }}>{subject}</span>
    });
  }

  // Fallback (non-FORUM types)
  if (!isForum) {
    if (type === ForumType.UE) {
      items.push({ key: 'ue', title: <span>UE</span> });
    }
    // autres types à ajouter ici si nécessaires
  }

  return (
    <Breadcrumb
      separator={<ArrowRight style={{ height: 36 }} />}
      items={items}
      style={{ marginLeft: 16, marginTop: 16, marginBottom: 16, fontWeight: 600, fontSize: 22 }}
    />
  );
};
