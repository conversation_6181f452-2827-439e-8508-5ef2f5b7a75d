import { QUERY_COURS_LIGHT } from '@/shared/graphql/cours.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { ForumType } from '@/shared/services/forum.js';
import { getColoredUEHeadStyle } from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { Card } from 'antd';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';

export const ForumSubjects = ({
  children,
  loading,
  numberOfDiscussions,
  type = ForumType.FORUM,
  typeId
}) => {
  const { t } = useTranslation();

  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  const { colorsHeader = {} } = useContext(GlobalContext);

  const { data: dataCours, loading: loadingCours } = useQuery(QUERY_COURS_LIGHT, {
    variables: {
      id: typeId
    },
    fetchPolicy: 'cache-and-network',
    skip: type !== ForumType.COURS
  });
  const cours = dataCours?.cour;

  const title = loading
    ? t('Loading...')
    : type === ForumType.COURS
      ? t('TitleDiscussionsInCourse', {
          courseName: cours?.name,
          number: numberOfDiscussions || 0
        })
      : t('PostList');

  return (
    <Card
      loading={loading}
      title={title}
      headStyle={getColoredUEHeadStyle(primaryColor, primaryColor)}
      bodyStyle={{ padding: '0' }}
      style={{ marginBottom: 20 }}>
      {children}
    </Card>
  );
};
