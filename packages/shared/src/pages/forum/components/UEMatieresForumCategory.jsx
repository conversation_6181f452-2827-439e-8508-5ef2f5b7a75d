import Commentaires from '@/shared/components/Commentaires/Commentaires.jsx';
import {
  QUERY_COURS_IN_UECATEGORY_FORUM,
  QUERY_COURSES_IN_UE,
  QUERY_MES_UES_FORUM,
  QUERY_UE_CATEGORIES_COURS_FOR_UE_FORUMS,
  QUERY_UE_CATEGORY_ID,
  QUERY_UE_CATEGORY_ID_WITH_CHILDREN,
  QUERY_UE_ID_FORUM
} from '@/shared/graphql/cours.js';
import {
  QUERY_ALL_QCM_TYPE,
  QUERY_SEARCH_QCM_WITH_RESULT_FOR_FORUM,
  QUERY_UE_CATEGORIES_QCM_FOR_UE_FORUMS
} from '@/shared/graphql/qcm.js';
import { ForumCard } from '@/shared/pages/forum/components/ForumCard.jsx';
import { ForumCategory } from '@/shared/pages/forum/components/ForumCategory.jsx';
import { ForumSubjects } from '@/shared/pages/forum/components/ForumSubjects.jsx';
import style from '@/shared/pages/forum/forumStyle.less';
import { AnneesQcmSelect } from '@/shared/pages/qcm/components/AnneesQcmSelect.jsx';
import { CommentairesType } from '@/shared/services/commentaires.js';
import { ForumType } from '@/shared/services/forum.js';
import {
  getLastSelectedQcmTypes,
  getLastSelectedYearForQcm,
  setLastSelectedYearForQcm
} from '@/shared/services/qcm.js';
import { useQuery } from '@apollo/client';
import { Card, Select, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

const forumCategoryBodyStyle = { padding: '0' };
const forumCategoryStyle = { marginBottom: 20 };

const LoadingForumCategory = ({ loading }) => (
  <Card bodyStyle={forumCategoryBodyStyle} style={forumCategoryStyle} loading={loading} />
);

export const UEForums = ({ type, typeId, forumId }) => {
  if (forumId) return <></>;

  if (type === ForumType.CHOIX_COURS_QCM_ANNALE) {
    // dans UE CHOIX COURS QCM ANNALES
    return <UEChoixCoursQcmAnnales type={type} ueId={typeId} />;
  }

  // COURS //
  if (type === ForumType.UE && typeId) {
    // COURS DANS UE
    return <UECategoriesForum type={type} ueId={typeId} />;
  }
  if (type === ForumType.UECategory && typeId) {
    // COURS
    return <CoursInCategoryForum type={type} ueCategoryId={typeId} />;
  }

  // QCMS //
  if (type === ForumType.UE_QCM && typeId) {
    // QCM de l'UE
    return <UECategoriesQCMForum type={type} ueId={typeId} />;
  }
  if (type === ForumType.UECategoryQCM && typeId) {
    // dans categorie, affiche qcms en fonction années
    return <QcmInUeForum type={type} sousCategorieId={typeId} />;
  }

  // ANNALES //
  if (type === ForumType.ANNALE && typeId) {
    // ANNALES de toute l'UE (affiche QCMS)
    return <AnnalesInUEForum type={type} ueId={typeId} />;
  }

  const [numberOfDiscussions, setNumberOfDiscussions] = useState(null);

  // DISCUSSIONS COURS
  if (type === ForumType.COURS && typeId) {
    return (
      <ForumSubjects type={type} typeId={typeId} numberOfDiscussions={numberOfDiscussions}>
        <Commentaires
          isForum
          id={typeId}
          type={CommentairesType.COURS}
          onGetNumberOfDiscussions={(v) => setNumberOfDiscussions(v)}
        />
      </ForumSubjects>
    );
  }

  return <UEMatiereForum type={type} typeId={typeId} />;
};

export const AnnalesInUEForum = ({ type, ueId }) => {
  const { loading, error, data, refetch } = useQuery(QUERY_SEARCH_QCM_WITH_RESULT_FOR_FORUM, {
    fetchPolicy: 'no-cache',
    variables: {
      filter: {
        ue: parseInt(ueId, 10),
        annale: true
      }
    }
  });

  return (
    <ForumCategory
      loading={loading}
      type={ForumType.ANNALE}
      forumCategory={{ name: 'Annales UE', description: ' ' }}
      refetch={refetch}>
      {!loading &&
        !error &&
        data &&
        data.searchQcms &&
        data.searchQcms.map((qcm) => (
          <ForumCard type={ForumType.QCM} forum={qcm} key={qcm.id} noViews />
        ))}
    </ForumCategory>
  );
};

// Liste matières
export const UEMatiereForum = () => {
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_MES_UES_FORUM, {
    fetchPolicy: 'no-cache'
  });
  const mesUEs = data?.mesUEs?.filter((ue) => !ue?.isFolder);

  return (
    <ForumCategory
      loading={loading}
      type={ForumType.MATIERES}
      forumCategory={{ name: t('general.Subjects'), description: ' ' }}
      refetch={refetch}>
      {!loading &&
        !error &&
        mesUEs?.map((ue) => <ForumCard type={ForumType.UE} forum={ue} key={ue.id} noViews />)}
    </ForumCategory>
  );
};

export const QcmInUeForum = ({ type, sousCategorieId }) => {
  const [anneeSelected, setAnneeSelected] = useState(getLastSelectedYearForQcm());
  const { t } = useTranslation();
  const { data: dataUe, loading: loadingUE } = useQuery(QUERY_UE_CATEGORY_ID, {
    fetchPolicy: 'no-cache',
    variables: { id: sousCategorieId }
  });

  const dataTypeQcm = useQuery(QUERY_ALL_QCM_TYPE, { fetchPolicy: 'cache-and-network' });
  const ueCategory = dataUe && dataUe.ueCategory;
  const [choixTypesQcm, setChoixTypeQcm] = useState(getLastSelectedQcmTypes());
  const [choixTypeQcmValue, setChoixTypeQcmValue] = useState();

  useEffect(() => {
    if (dataTypeQcm) {
      setChoixTypeQcmValue(dataTypeQcm?.data?.allTypeQcm.map((typeQcm) => typeQcm.name));
      setChoixTypeQcm(dataTypeQcm?.data?.allTypeQcm.map((typeQcm) => typeQcm.id));
    }
  }, [dataTypeQcm]);

  const { loading, error, data, refetch } = useQuery(QUERY_SEARCH_QCM_WITH_RESULT_FOR_FORUM, {
    fetchPolicy: 'no-cache',
    skip: !anneeSelected,
    variables: {
      filter: {
        sousCategorieId,
        annale: false,
        annee: anneeSelected,
        typeQcms: choixTypesQcm
      }
    }
  });

  const renderAnneeSelection = () => (
    <div style={{ marginBottom: 10, textAlign: 'center' }}>
      <AnneesQcmSelect
        onAnneeSelect={(_, item) => {
          setLastSelectedYearForQcm(item.key);
          setAnneeSelected(item.key);
        }}
      />
    </div>
  );

  const typeSelection = (
    <div style={{ margin: 'auto', textAlign: 'center' }}>
      <div>{t('ExercicesType')}</div>
      <Select
        showArrow
        mode="multiple"
        size={'large'}
        style={{ width: 300, marginBottom: 20, marginTop: 10 }}
        virtual={false}
        tagRender={({ label, value, closable, onClose, key }) => (
          <Tag
            value={value}
            key={key}
            color="geekblue"
            closable={closable}
            onClose={onClose}
            style={{ marginRight: 3 }}>
            {label}
          </Tag>
        )}
        value={choixTypeQcmValue}
        placeholder={t('ChooseMCQType')}
        loading={dataTypeQcm?.loading}
        options={dataTypeQcm?.data?.allTypeQcm.map((typeQcm) => ({
          value: typeQcm.name,
          key: typeQcm.id
        }))}
        onChange={(checked, option) => {
          setChoixTypeQcm(option.map((o) => o.key));
          setChoixTypeQcmValue(option.map((o) => o.value));
        }}
      />
    </div>
  );

  return (
    <div>
      <ForumCategory
        loading={loadingUE}
        type={type}
        forumCategory={
          (ueCategory && {
            name: 'QCMs - ',
            description: ueCategory.name,
            color: ueCategory.ue.color,
            color2: ueCategory.ue.color2
          }) || { name: 'Chargement' }
        }
        refetch={refetch}>
        <Card type="inner" className={style.forumCard}>
          {renderAnneeSelection()}
          &nbsp;
          {typeSelection}
        </Card>

        {!loading &&
          !error &&
          data &&
          data.searchQcms &&
          data.searchQcms.map((qcm) => (
            <ForumCard type={ForumType.QCM} forum={qcm} key={qcm.id} noViews />
          ))}
      </ForumCategory>
    </div>
  );
};

export const UEChoixCoursQcmAnnales = ({ type, ueId }) => {
  const { data, error, loading, refetch } = useQuery(QUERY_UE_ID_FORUM, {
    variables: { id: ueId },
    pollInterval: 20000
  });
  const ue = data && data.ue;

  return (
    <ForumCategory loading={loading} type={type} forumCategory={ue && ue} refetch={refetch}>
      <ForumCard
        type={ForumType.UE}
        forum={{
          id: ueId,
          name: 'Cours',
          lastPost: ue && ue.lastPostInCours,
          postsNumber: ue && ue.postsNumber.cours
        }}
        key={1}
        noViews
      />
      <ForumCard
        type={ForumType.UE_QCM}
        forum={{
          id: ueId,
          name: 'QCM',
          lastPost: ue && ue.lastPostInQcm,
          postsNumber: ue && ue.postsNumber.qcm
        }}
        key={2}
        noViews
      />
    </ForumCategory>
  );
};

// IN UE ueId
export const UECategoriesForum = ({ type, ueId = null }) => {
  const { loading, error, data, refetch } = useQuery(QUERY_UE_CATEGORIES_COURS_FOR_UE_FORUMS, {
    fetchPolicy: 'no-cache',
    variables: { ueId },
    skip: !ueId
  });

  const { data: dataCoursesInUe, loading: loadingCourses } = useQuery(QUERY_COURSES_IN_UE, {
    variables: { ueId },
    skip: !ueId,
    fetchPolicy: 'no-cache'
  });

  const coursesInUe = dataCoursesInUe && dataCoursesInUe.coursInUE;

  const { data: dataUE, loading: loadingUE } = useQuery(QUERY_UE_ID_FORUM, {
    variables: { id: ueId },
    pollInterval: 20000,
    skip: !ueId
  });
  const ue = dataUE && dataUE.ue;

  return (
    <ForumCategory
      loading={loadingUE}
      type={ForumType.CHOIX_COURS_QCM_ANNALE}
      forumCategory={
        (ue && {
          ...ue,
          name: `${ue.name} ${ue.description}`,
          description: ``
        }) || { name: 'Chargement' }
      }
      refetch={refetch}>
      {data?.ueCategories?.map((category) => (
        <ForumCard type={ForumType.UECategory} forum={category} key={category.id} noViews />
      ))}
      {coursesInUe?.map((course) => (
        <ForumCard type={ForumType.COURS} forum={course} key={course.id} />
      ))}
    </ForumCategory>
  );
};

export const UECategoriesQCMForum = ({ type, ueId = null }) => {
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_UE_CATEGORIES_QCM_FOR_UE_FORUMS, {
    fetchPolicy: 'no-cache',
    variables: { ueId },
    skip: !ueId
  });

  const { data: dataUE, loading: loadingUE } = useQuery(QUERY_UE_ID_FORUM, {
    variables: { id: ueId },
    pollInterval: 20000,
    skip: !ueId
  });
  const ue = dataUE && dataUE.ue;

  return (
    <ForumCategory
      loading={loading}
      type={ForumType.UECategory}
      forumCategory={
        (ue && {
          ...ue,
          name: 'QCMs - ',
          description: `${ue.name} ${ue.description}`
        }) || { name: 'Chargement' }
      }
      refetch={refetch}>
      {!loading &&
        !error &&
        data &&
        data.ueCategories &&
        data.ueCategories.map((category) => (
          <ForumCard
            type={ForumType.UECategoryQCM}
            forum={{
              ...category,
              lastPost: category && category.lastPostInQcm
            }}
            key={category.id}
            noViews
          />
        ))}
    </ForumCategory>
  );
};

// CATEGORY CONTENT
export const CoursInCategoryForum = ({ type, ueCategoryId = null }) => {
  const { loading, error, data, refetch } = useQuery(QUERY_COURS_IN_UECATEGORY_FORUM, {
    fetchPolicy: 'no-cache',
    variables: { ueCategoryId },
    skip: !ueCategoryId
  });
  const coursInUeCateg = data?.coursInUECategory;

  // Query category in category

  const { data: dataUeCateg, loading: loadingUE } = useQuery(QUERY_UE_CATEGORY_ID_WITH_CHILDREN, {
    fetchPolicy: 'no-cache',
    variables: { id: ueCategoryId }
  });
  const ueCategory = dataUeCateg?.ueCategory;
  const childrenCategs = ueCategory?.children;

  return (
    <ForumCategory
      loading={loadingUE}
      type={ForumType.COURS}
      forumCategory={
        (ueCategory && {
          name: ueCategory.name,
          color: ueCategory?.ue?.color,
          color2: ueCategory?.ue?.color2
        }) || { name: 'Chargement' }
      }
      refetch={refetch}>
      {coursInUeCateg?.map((cours) => (
        <ForumCard type={ForumType.COURS} forum={cours} key={cours.id} />
      ))}

      {childrenCategs?.map((categ) => (
        <ForumCard type={ForumType.UECategory} forum={categ} key={categ.id} />
      ))}
    </ForumCategory>
  );
};
