import { QUERY_ALL_GROUPS } from '@/shared/graphql/cours.js';
import { QUERY_EDT_FOR_UE } from '@/shared/graphql/edt.js';
import { isAdmin, isTuteur } from '@/shared/utils/authority.js';
import { DATE_FORMATS } from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { Checkbox, Col, Row, Select, Table, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import Link from 'umi/link.js';
import { useTranslation } from 'react-i18next';

const UEPlanningDateFormat = DATE_FORMATS.FULL_FR;

const PlanningGroupsSelector = ({ groupes, onChange }) => {
  const dataGroupes = useQuery(QUERY_ALL_GROUPS, { fetchPolicy: 'no-cache' });
  const { t } = useTranslation();

  const groupeTagRender = ({ label, value, closable, onClose, key }) => (
    <Tag
      value={value}
      key={key}
      color="geekblue"
      closable={closable}
      onClose={onClose}
      style={{ marginRight: 3 }}
    >
      {label}
    </Tag>
  );
  const [myPlanning, setMyPlanning] = useState(true);
  const [ids, setIds] = useState([]);

  useEffect(() => {
    setIds([]);
  }, []);

  useEffect(() => {
    onChange(ids);
  }, [ids]);

  const handleSelect = async (_, option) => {
    setIds([...ids, option.key]);
  };
  const handleDeselect = async (_, option) => {
    setIds(ids.filter((id) => id !== option.key));
  };

  return (
    <div>
      <Row>
        <Col span={7}>
          <Checkbox
            defaultChecked
            value={myPlanning}
            onChange={() => {
              setMyPlanning(!myPlanning);
              setIds([]);
              onChange(ids);
            }}
          >
            {t('OnlyDisplayMyGroupAgenda')}
          </Checkbox>
          &nbsp;
        </Col>
        <Col span={7}>
          {!myPlanning && (
            <Select
              showArrow
              mode="multiple"
              style={{ minWidth: '150px', width: '100%' }}
              tagRender={groupeTagRender}
              placeholder={t('ChooseGroups')}
              // defaultValue={groupes.map(groupe => groupe.name)}
              options={
                !dataGroupes.error &&
                dataGroupes.data &&
                dataGroupes.data.allGroupes &&
                dataGroupes.data.allGroupes.map((groupe) => ({
                  value: groupe.name,
                  key: groupe.id
                }))
              }
              onDeselect={handleDeselect}
              onSelect={handleSelect}
            />
          )}
        </Col>
      </Row>
      &nbsp;
      <br />
    </div>
  );
};

export const UEPlanning = ({ ueId }) => {
  const [groupIds, setGroupIds] = useState(null);

  const { data, error, loading, refetch } = useQuery(QUERY_EDT_FOR_UE, {
    variables: { ueId, groupIds },
    fetchPolicy: 'no-cache'
  });

  const planning = data && data.monPlanningInUE;

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (text) => dayjs(text).format(UEPlanningDateFormat)
    },
    {
      title: 'Cours',
      dataIndex: 'cour',
      key: 'cour',
      render: (cour) => (
        <>
          <Link to={`/cours/${cour.id}`}>
            {cour.name} {cour.text && cour.text}
          </Link>
        </>
      )
    },
    {
      title: 'Catégorie',
      dataIndex: 'cour',
      key: 'cour',
      render: ({ ueCategory: category }) => (
        <Link to={`/cours/categorie/${category.id}`}>{category.name}</Link>
      )
    }
  ];

  if (isAdmin() || isTuteur()) {
    columns.push({
      title: 'Groupes',
      dataIndex: 'groupes',
      key: 'groupes',
      render: (groupes) => groupes && groupes.map((groupe) => <Tag>{groupe.name}</Tag>)
    });
  }

  return (
    <div>
      {(isAdmin() || isTuteur()) && (
        <PlanningGroupsSelector
          onChange={(ids) => {
            setGroupIds(ids);
          }}
        />
      )}

      <Table
        loading={loading}
        columns={columns}
        dataSource={data && planning}
        scroll={{ x: true }}
        pagination={false}
      />
    </div>
  );
};
