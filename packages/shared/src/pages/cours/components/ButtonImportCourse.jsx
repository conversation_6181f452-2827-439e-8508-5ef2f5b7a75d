import { EditCoursCardModal } from '@/shared/pages/cours/components/EditCoursCardModal.jsx';
import { Button, Popover, Space } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FileDown } from 'lucide-react';

/* Bouton importer cours */
export default function ButtonImportCourse({
  refetch,
  ueType = null,
  categoryId = null,
  ueId = null
}) {
  const { t } = useTranslation();
  const [createVisible, setCreateVisible] = useState(false);

  const closeModalHandler = () => {
    setCreateVisible(false);
    refetch(); // Load new modifications
  };

  return (
    <div>
      <Popover
        content={
          <p style={{ maxWidth: 350 }}>
            {t('ImportCourseExplanation')}
            <br />
            {t('ImportCourseExplanation2')}
          </p>
        }
      >
        <Button
          style={{ height: 'auto', padding: 24, width: 250 }}
          type="text"
          onClick={() => setCreateVisible(true)}
          block
        >
          <Space direction={'vertical'} size={'small'}>
            <FileDown style={{ fontSize: 48 }} />
            <b>{t('ImportExistingCourse')}</b>
          </Space>
        </Button>
      </Popover>
      {createVisible && (
      <EditCoursCardModal
        isVisible={createVisible}
        closeModalHandler={closeModalHandler}
        uecategoryId={categoryId}
        modalType="CREATE"
        ueType={ueType}
        cours={{}}
        ueId={ueId}
        imported
      />
      )}
    </div>
  );
}
