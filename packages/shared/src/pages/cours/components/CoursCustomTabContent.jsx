import React, { useContext } from 'react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { useQuery } from '@apollo/client';
import {
  QUERY_COURS_BY_IDS_WITH_PARENTS,
  QUERY_UE_CATEGORIES_BY_IDS,
  QUERY_UES_IDS
} from '@/shared/graphql/cours';
import { isAdmin } from '@/shared/utils/authority';
import { CoursCard } from '@/shared/pages/cours/components/PedagogicalHierarchyElements/CoursCard';
import { UEItemsList } from '@/shared/pages/cours/components/ListViews/UEItemsList';
import { CategoryCard } from '@/shared/pages/cours/components/PedagogicalHierarchyElements/CategoryCard';

export default function (props) {
  const { appearance, listOrCardNavigation } = useContext(GlobalContext);
  const items = appearance.customTabItems;
  const ueIds = items?.ueIds;
  const coursIds = items?.coursIds;
  const categoryId = items?.categoryIds;

  const {
    data: dataUes,
    loading: loadingUes,
    error: errorUes,
    refetch: refetchUes
  } = useQuery(QUERY_UES_IDS, {
    variables: { ids: ueIds },
    fetchPolicy: 'cache-and-network',
    skip: !ueIds
  });
  const {
    data: dataCategories,
    loading: loadingCategories,
    error: errorCategories,
    refetch: refetchCategs
  } = useQuery(QUERY_UE_CATEGORIES_BY_IDS, {
    variables: { ids: categoryId },
    fetchPolicy: 'cache-and-network',
    skip: !categoryId
  });
  const {
    data: dataCours,
    loading: loadingCours,
    error: errorCours,
    refetch: refetchCourses
  } = useQuery(QUERY_COURS_BY_IDS_WITH_PARENTS, {
    variables: { ids: coursIds },
    fetchPolicy: 'cache-and-network',
    skip: !coursIds
  });

  const loading = loadingUes || loadingCategories || loadingCours;

  const ues = dataUes?.ues?.filter((ue) => ue !== null); // Si il n'a pas accès à l'UE, il ne faut pas l'afficher
  const categories = dataCategories?.ueCategoriesAmongIds?.filter((category) => category !== null); // Si il n'a pas accès à la catégorie, il ne faut pas l'afficher
  const courses = dataCours?.coursIds?.filter((cours) => cours !== null); // Si il n'a pas accès au cours, il ne faut pas l'afficher

  const refetchAll = () => {
    refetchUes();
    refetchCategs();
    refetchCourses();
  };

  const renderCours = (
    cours, // Current
    targetCours // Original
  ) => (
    <React.Fragment key={cours?.id}>
      {(cours?.isVisible || isAdmin()) && (
        <div style={{ display: 'flex', width: listOrCardNavigation === 'List' ? '100%' : 'auto' }}>
          <CoursCard
            cours={cours}
            targetCours={targetCours}
            color={cours?.ue?.color || cours?.ueCategory?.ue?.color}
            color2={cours?.ue?.color2 || cours?.ueCategory?.ue?.color2}
            //ueType={ue && ue?.type}
            refetch={refetchAll}
          />
        </div>
      )}
    </React.Fragment>
  );

  const renderCategories = () =>
    categories?.map((category, index) => (
      <React.Fragment key={index}>
        {(category.isVisible || isAdmin()) && (
          <div style={listOrCardNavigation === 'List' ? { width: '100%' } : {}}>
            <CategoryCard
              category={category}
              name={category.name}
              categoryId={category.id}
              image={category.image}
              views="0"
              loading={loading}
              ueId={category?.ue?.id || null}
              refetch={refetchAll}
              size="large"
              color={category?.ue?.color || null}
              color2={category?.ue?.color2 || null}
              description={category.description}
              order={category.order}
              isVisible={category.isVisible}
              countAccessiblesCourses={category?.countAccessiblesCourses}
              countAccessiblesExercises={category?.countAccessiblesExercises}
            />
          </div>
        )}
      </React.Fragment>
    ));

  return (
    <>
      <div
        style={
          listOrCardNavigation === 'Card'
            ? {
                display: 'flex',
                alignContent: 'center',
                margin: 'auto',
                gap: '30px',
                flexWrap: 'wrap',
                justifyContent: 'center'
              }
            : {
                // display: 'flex',
                // flexWrap: 'wrap',
                // alignContent: 'center',
              }
        }
      >
        {/* UEs list */}
        <UEItemsList ues={ues} refetch={refetchAll} loading={loadingUes} />
        {/* Categories list */}
        {renderCategories()}
        {/* Cours list  */}
        {courses?.map((cours) => (
          <React.Fragment key={cours?.id}>{renderCours(cours, cours?.targetCours)}</React.Fragment>
        ))}
      </div>
    </>
  );
}
