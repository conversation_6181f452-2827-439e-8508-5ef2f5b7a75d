import { EditCreateUeCardModal } from '@/shared/pages/cours/components/EditUECardModal.jsx';
import { Button, Popover, Space } from 'antd';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GitBranchPlus } from 'lucide-react';

/* Bouton créer formation */
export default function ButtonCreateFormation({ refetch, parentUe = {}, small = false }) {
  const { t } = useTranslation();

  const [createUEVisible, setCreateUEVisible] = useState(false);

  const closeModalHandler = () => {
    setCreateUEVisible(false);
    window.location.reload(); // TODO: Remove this line when the refetch is working
    refetch(); // Load new modifications
  };

  return (
    <div>
      <Popover content={<p style={{ maxWidth: 350 }}>{t('CreateFormationExplanation')}</p>}>
        <Button
          style={{ height: 'auto', padding: 24, width: 250 }}
          type="text"
          onClick={() => {
            setCreateUEVisible(true);
          }}
        >
          <Space direction={'vertical'} size={'small'}>
            <GitBranchPlus style={{ fontSize: 48 }} />
            <b>{t('createNewFormation')}</b>
          </Space>
        </Button>
      </Popover>

      <EditCreateUeCardModal
        closeModalHandler={closeModalHandler}
        isModalVisible={createUEVisible}
        modalType="CREATE"
        type={'FORMATION'}
        refetch={refetch}
        parentUe={parentUe}
      />
    </div>
  );
}
