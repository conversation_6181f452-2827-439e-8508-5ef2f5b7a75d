import { QUERY_COUNT_UEMODULES, QUERY_UE_MODULE_PROGRESS_IN_UE } from '@/shared/graphql/ue_modules';
import { useQuery } from '@apollo/client';
export const useQueryFormationProgressPreview = (ue, isFormation) => {
  const ueId = ue?.id;
  // Query formation global progress if it is a formation
  const { data: progressData } = useQuery(QUERY_UE_MODULE_PROGRESS_IN_UE, {
    variables: {
      ueId
    },
    skip: !isFormation || !ueId,
    fetchPolicy: 'cache-and-network'
  });
  const totalModulesCompleted =
    progressData?.ueModuleProgressInUE?.filter((module) => module?.completed === true)?.length || 0;
  const { data: countUeModulesData, loading: loadingCountUeModules } = useQuery(
    QUERY_COUNT_UEMODULES,
    {
      variables: {
        ueId
      },
      skip: !isFormation || !ueId,
      fetchPolicy: 'cache-and-network'
    }
  );
  const totalModules = countUeModulesData?.countUEModules || 0;
  const progressPercent = totalModules > 0 ? (totalModulesCompleted / totalModules) * 100 : 0;
  ////////////

  return {
    // garder que 2 decimales apres la virgule
    progressPercent: progressPercent?.toFixed(2) || 0
  };
};
