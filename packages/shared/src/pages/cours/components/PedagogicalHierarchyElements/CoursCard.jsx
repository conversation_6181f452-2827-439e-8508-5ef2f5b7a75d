import { EditCoursCardModal } from '@/shared/pages/cours/components/EditCoursCardModal.jsx';
import { tr } from '@/shared/services/translate.js';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils.js';
import { Badge, Button, Card, Divider, Statistic } from 'antd';
import { isAdmin } from '@/shared/utils/authority';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import Link from 'umi/link';
import React, { useContext, useState } from 'react';
import { EditOutlined } from '@ant-design/icons';
import CalendarTwoTone from '@ant-design/icons/lib/icons/CalendarTwoTone';
import BookTwoTone from '@ant-design/icons/lib/icons/BookTwoTone';
import { PdfPreview } from '@/shared/pages/cours/components/PdfPreview.jsx';
import videoLogo from '@/shared/assets/video_logo.png';
import { Textfit } from 'react-textfit';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { CandlestickChart, MessageSquareText, View } from 'lucide-react';

/* Course item web & mobile */
export const CoursCard = ({ loading, color, color2, cours, targetCours, refetch, ueType }) => {
  const { t } = useTranslation();
  const coursToShow = targetCours || cours;
  const { listOrCardNavigation } = useContext(GlobalContext);
  const [editVisible, setEditVisible] = useState(false);
  const editHandler = (e) => {
    // Affiche modale
    e.preventDefault();
    setEditVisible(true);
  };
  const closeModalHandler = () => {
    setEditVisible(false);
    refetch(); // Load new modifications
  };
  const datesDiffusion = coursToShow?.datesDiffusion;
  const pdfPreviews = coursToShow?.pdfPreviews;
  const hasPdfPreview = coursToShow && coursToShow?.layout === 'pdf' && pdfPreviews?.length > 0;
  const customImage = coursToShow?.customImage;
  const isVideo = coursToShow?.layout === 'video';

  const maxHeightImage = 150;
  const statsSize = 22;

  const isImportedCours = targetCours?.id !== null;

  // List only
  const imageSize = '170px';
  const bandeauGaucheWidth = '15px';

  const renderPdfPreviews = (images) => (
    <PdfPreview images={images} maxHeightImage={maxHeightImage} maxWidthImage={148} />
  );

  const statistiquesCours = (
    <div
      style={
        listOrCardNavigation === 'Card'
          ? {
              // Card style
              position: 'absolute',
              display: 'flex',
              justifyContent: 'center',
              bottom: '10px',
              margin: 'auto',
              fontWeight: '600',
              width: '100%',
              marginLeft: '-24px'
            }
          : {
              // List style
              display: 'flex',
              bottom: '10px',
              fontWeight: '600',
              width: '100%'
            }
      }
    >
      <Statistic
        style={{ marginRight: 30 }}
        valueStyle={{ fontSize: `${statsSize}px` }}
        suffix={<View style={{ fontSize: `${statsSize}px`, marginBottom: -3 }} />}
        value={coursToShow?.views}
      />

      <Statistic
        style={{ marginRight: 30 }}
        valueStyle={{ fontSize: `${statsSize}px` }}
        suffix={<CandlestickChart style={{ fontSize: `${statsSize}px`, marginBottom: -3 }} />}
        value={coursToShow?.countAccessiblesExercises}
      />

      <Statistic
        style={{ fontSize: `${statsSize}px` }}
        valueStyle={{ fontSize: `${statsSize}px` }}
        suffix={<MessageSquareText style={{ fontSize: `${statsSize}px`, marginBottom: -3 }} />}
        value={coursToShow?.numberOfComments || '0'}
      />
    </div>
  );

  const renderDatesDiffusion = (
    <>
      {datesDiffusion &&
        datesDiffusion.map((dateDiff) => (
          <h4 style={{ textAlign: 'center', color: 'grey' }}>
            <CalendarTwoTone /> Date : {dayjs(dateDiff.date).format('DD/MM/YYYY à HH:SS')}
          </h4>
        ))}
    </>
  );

  const renderImageCours = (
    <div style={{ margin: 'auto', marginTop: '10px' }}>
      {customImage === null && hasPdfPreview ? (
        renderPdfPreviews(pdfPreviews)
      ) : (
        <>
          {customImage ? (
            <img
              src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + customImage)}
              style={{
                maxHeight: `147px`,
                maxWidth: `148px`
              }}
              alt="aperçu"
            />
          ) : (
            <>
              {isVideo ? (
                <>
                  <img src={videoLogo} style={{ height: '87px' }} alt="aperçu" />
                </>
              ) : (
                <BookTwoTone style={{ fontSize: 80 }} twoToneColor={color} />
              )}
            </>
          )}
        </>
      )}
    </div>
  );

  return (
    <div style={{ width: '100%' }}>
      {listOrCardNavigation === 'List' ? (
        <>
          <div
            style={{
              borderRadius: '3px',
              height: 'auto',
              width: '100%',
              overflow: 'hidden',
              display: 'flex'
            }}
          >
            <Link style={{ width: '100%' }} to={`/cours/${cours?.id}`}>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <div
                  style={{
                    backgroundColor: color,
                    height: imageSize,
                    width: bandeauGaucheWidth,
                    position: 'relative',
                    left: '2px',
                    borderRadius: '2px 0px 0px 2px'
                  }}
                />

                <div style={{ marginLeft: '10px', height: imageSize }} id="imageCours">
                  {renderImageCours}
                </div>

                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    marginLeft: bandeauGaucheWidth,
                    width: '100%',
                    height: imageSize
                  }}
                >
                  <div
                    id="TitreDescription"
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      marginLeft: '5px',
                      width: '100%',
                      justifyContent: 'space-around'
                    }}
                  >
                    <div
                      id="titre"
                      style={{
                        color: 'black',
                        overflow: 'hidden',
                        fontWeight: '700',
                        display: 'flex',
                        flexDirection: 'row',
                        width: '100%'
                      }}
                    >
                      <div>
                        <Textfit
                          mode="multi" // "single" ou "multi" pour plusieurs lignes
                          max={22} // max font size en px (par défaut prend le plus de place possible)
                          min={16}
                        >
                          {coursToShow?.[tr('name')] || coursToShow?.name}
                          {isAdmin() && (
                            <Button
                              size="small"
                              onClick={editHandler}
                              shape="circle"
                              icon={<EditOutlined />}
                            />
                          )}
                        </Textfit>
                      </div>
                    </div>

                    <div
                      id="description"
                      style={{
                        fontWeight: '700',
                        color: '#9A9A9A',
                        maxHeight: '45px',
                        width: '100%',
                        overflow: 'hidden'
                      }}
                    >
                      <Textfit
                        mode="multi" // "single" ou "multi" pour plusieurs lignes
                        max={14} // max font size en px (par défaut prend le plus de place possible)
                        min={11}
                      >
                        {coursToShow?.[tr('text')] || coursToShow?.text}
                      </Textfit>
                    </div>

                    <div
                      id="statUe"
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        width: '100%'
                      }}
                    >
                      {statistiquesCours}
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
          <Divider style={{ margin: '4px 0' }} />
        </>
      ) : (
        <div>
          <Badge.Ribbon
            style={{
              background: 'red',
              color: 'white',
              placement: 'start',
              marginTop: '40px',
              visibility: coursToShow?.isNew ? 'visible' : 'hidden'
            }}
            text="NEW"
          >
            <Link to={`/cours/${cours?.id}`}>
              <Card
                cover={
                  <div
                    style={{
                      height: '30px',
                      backgroundColor: color,
                      borderRadius: '10px 10px 0px 0px'
                    }}
                  />
                }
                loading={loading}
                hoverable
                style={{
                  width: '275px',
                  height: '330px',
                  textAlign: 'center',
                  opacity: !cours?.isVisible && isAdmin() ? '0.5' : '1'
                }}
              >
                {isAdmin() && (
                  <Button
                    onClick={editHandler}
                    shape="circle"
                    icon={<EditOutlined />}
                    style={{ position: 'absolute', top: '5px', right: '5px' }}
                  />
                )}

                <div style={{ height: '70px' }}>
                  <div
                    style={{
                      fontSize: '20px',
                      fontWeight: '700',
                      color: 'black',
                      maxHeight: '40px',
                      marginTop: '-20px',
                      lineHeight: '20px',
                      overflow: 'hidden'
                    }}
                  >
                    {coursToShow?.[tr('name')] || coursToShow?.name}
                  </div>

                  <div
                    style={{
                      fontSize: '13px',
                      fontWeight: '700',
                      height: '30px',
                      marginTop: '5px',
                      lineHeight: '15px',
                      color: '#9A9A9A',
                      overflow: 'hidden'
                    }}
                  >
                    {coursToShow?.[tr('text')] || coursToShow?.text}
                  </div>
                </div>
                {renderImageCours}
                {statistiquesCours}
              </Card>
            </Link>
          </Badge.Ribbon>
        </div>
      )}

      {isAdmin() && editVisible && (
        <EditCoursCardModal
          isVisible={editVisible}
          closeModalHandler={closeModalHandler}
          cours={cours}
          ueType={ueType}
          targetCours={targetCours}
          color={color}
        />
      )}
    </div>
  );
};
