import { Button, Popover, Space } from 'antd';
import React, { useState } from 'react';
import { EditCreateCategoryModal } from '@/shared/pages/cours/components/EditCategorieCardModal';
import { useTranslation } from 'react-i18next';
import { FolderPlus } from 'lucide-react';

/* Bouton créer catégorie (dans matière) */
export const ButtonCreateCategoryOrFolder = ({ refetch, ueId, parentCategory }) => {
  const { t } = useTranslation();
  const [createVisible, setCreateVisible] = useState(false);

  return (
    <div>
      <Popover
        content={<p style={{ maxWidth: 350 }}>{t('AllowsYouToCreateEditLearningContent')}</p>}
      >
        <Button
          style={{ height: 'auto', padding: 24, width: 250 }}
          type="text"
          block
          onClick={() => {
            setCreateVisible(true);
          }}
        >
          <Space direction={'vertical'} size={'small'}>
            <FolderPlus style={{ fontSize: 48 }} />
            <b>{t('CreateNewCategory')}</b>
          </Space>
        </Button>
      </Popover>
      {createVisible && (
        <EditCreateCategoryModal
          isVisible={createVisible}
          modalType="CREATE"
          closeModalHandler={() => {
            setCreateVisible(false);
            refetch();
          }}
          ueId={ueId}
          parentCategory={parentCategory}
        />
      )}
    </div>
  );
};
