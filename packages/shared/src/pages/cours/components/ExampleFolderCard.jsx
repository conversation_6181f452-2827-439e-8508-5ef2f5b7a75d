import { Badge, Card, Statistic } from 'antd';
import { BookMarked, CandlestickChart } from 'lucide-react';
import React from 'react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import categoryPlaceHolder from '@/shared/assets/CategoryPlaceholder.svg';

export const ExampleFolderCard = ({
  title,
  description,
  customImageUrl,
  fileCustomImage,
  color
}) => {
  const { appearance } = React.useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  return (
    <Badge.Ribbon
      style={{ background: 'red', color: 'white', placement: 'start', marginTop: 10 }}
      text="NEW"
    >
      <Card
        cover={
          <div
            style={{
              height: '30px',
              backgroundColor: color ? color : primaryColor,
              borderRadius: '10px 10px 0px 0px'
            }}
          />
        }
        style={{
          width: '275px',
          height: '330px',
          textAlign: 'center'
        }}
      >
        <div style={{ height: '70px' }}>
          <div
            style={{
              fontSize: '20px',
              fontWeight: '700',
              color: 'black',
              maxHeight: '40px',
              marginTop: '-20px',
              lineHeight: '20px',
              overflow: 'hidden'
            }}
          >
            {title}
          </div>

          <div
            style={{
              fontSize: '13px',
              fontWeight: '700',
              height: '30px',
              marginTop: '5px',
              lineHeight: '15px',
              color: '#9A9A9A',
              overflow: 'hidden'
            }}
          >
            {description}
          </div>
        </div>
        <div style={{ margin: 'auto', marginTop: '10px' }}>
          <img
            src={
              fileCustomImage
                ? URL.createObjectURL(fileCustomImage)
                : customImageUrl
                  ? customImageUrl
                  : categoryPlaceHolder
            }
            alt="preview"
            style={{
              maxHeight: `147px`,
              maxWidth: `148px`
            }}
          />
        </div>
        <div
          id="stats"
          style={{
            position: 'absolute',
            width: '100%',
            fontWeight: '600',
            display: 'flex',
            justifyContent: 'center',
            gap: '40px',
            bottom: '5px',
            marginLeft: '-24px'
          }}
        >
          <Statistic
            valueStyle={{ fontSize: 22 }}
            suffix={<BookMarked style={{ fontSize: 22, marginBottom: -3 }} />}
            value={'—'}
          />

          <Statistic
            valueStyle={{ fontSize: 22 }}
            suffix={<CandlestickChart style={{ fontSize: 22, marginBottom: -3 }} />}
            value={'—'}
          />
        </div>
      </Card>
    </Badge.Ribbon>
  );
};
