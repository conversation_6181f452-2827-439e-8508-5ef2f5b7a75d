import React, { useContext } from 'react';
import {ButtonCreateCategoryOrFolder} from '@/shared/pages/cours/components/ButtonCreateCategoryOrFolder';
import ButtonCreateSubject from '@/shared/pages/cours/components/ButtonCreateSubject';
import ButtonCreateCourse from '@/shared/pages/cours/components/ButtonCreateCourse';
import { FEATURE_FLAGS } from '@/shared/utils/utils';
import ButtonImportCourse from '@/shared/pages/cours/components/ButtonImportCourse';
import ButtonCreatePage from '@/shared/pages/cours/components/ButtonCreatePage';
import { useQuery } from '@apollo/client';
import {
  QUERY_COURS_IN_UECATEGORY,
  QUERY_COURSES_IN_UE,
  QUERY_MES_UES,
  QUERY_UE_BY_ID_WITH_CHILDREN_AND_TEACHERS,
  QUERY_UE_CATEGORY_ID_WITH_CHILDREN
} from '@/shared/graphql/cours';
import ButtonCreateFormation from '@/shared/pages/cours/components/ButtonCreateFormation';
import { GlobalContext } from '@/shared/layouts/BlankLayout';

export const CoursesToolBar = ({ ueId, categoryId, coursId }) => {
  const { data: dataUE, refetch: refetchUE } = useQuery(QUERY_UE_BY_ID_WITH_CHILDREN_AND_TEACHERS, {
    variables: { id: ueId },
    skip: !ueId,
    fetchPolicy: 'cache-and-network'
  });

  const { refetch: refetchCourses } = useQuery(QUERY_COURSES_IN_UE, {
    fetchPolicy: 'cache-and-network',
    skip: !ueId,
    variables: { ueId }
  });

  const ue = dataUE?.ue;

  const { refetch: refetchCategory } = useQuery(QUERY_COURS_IN_UECATEGORY, {
    variables: { ueCategoryId: categoryId },
    skip: !categoryId,
    fetchPolicy: 'cache-and-network'
  });

  const { data: dataCurrentCategory, refetch: refetchCategoryWithChildren } = useQuery(
    QUERY_UE_CATEGORY_ID_WITH_CHILDREN,
    {
      variables: { id: categoryId },
      skip: !categoryId,
      fetchPolicy: 'cache-and-network'
    }
  );
  const currentCategory = dataCurrentCategory?.ueCategory;
  const parentUe = currentCategory?.ue;
  const ueType = currentCategory?.ue?.type;

  const refetchAllCategory = () => {
    refetchCategory();
    refetchCategoryWithChildren();
  };

  const { features } = useContext(GlobalContext);

  const { refetch: refetchRoot } = useQuery(QUERY_MES_UES, {
    fetchPolicy: 'cache-and-network'
  });

  {
    /* @TODO : handle tabSelected !== '3' here -> globalcontext ? */
  }

  return (
    <div
      style={{
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        gap: '16px',
        flexWrap: 'wrap',
        alignItems: 'center'
      }}
    >
      {/***** MATIERE *****/}
      {ueId && (
        <>
          {/* Créer catégorie seulement si dans matière */}
          {!ue?.isFolder && (
            <ButtonCreateCategoryOrFolder
              refetch={refetchUE}
              ueId={ueId}
              //parentCategory={parentCategory}
            />
          )}

          {/* Créer matière, ou nouveau dossier, seulement si parent est un dossier et pas une matière */}
          {ue?.isFolder && (
            <>
              <ButtonCreateSubject refetch={refetchUE} shouldCreateFolder parentUe={ue} />
              <ButtonCreateSubject refetch={refetchUE} parentUe={ue} />
            </>
          )}

          {/* Créer cours seulement si matière */}
          {!ue?.isFolder && (
            <>
              <ButtonCreateCourse refetch={refetchCourses} ueId={ue?.id} />
            </>
          )}

          {/* Importer cours possible dans dossier ou matière */}
          {FEATURE_FLAGS.EnableImportedCourses && (
            <ButtonImportCourse refetch={refetchUE} ueId={ue?.id} />
          )}

          {/* Créer page possible dans dossier ou matière */}
          <ButtonCreatePage refetch={refetchCourses} ueId={ue?.id} />
        </>
      )}

      {/***** CATEGORY *****/}
      {categoryId && (
        <>
          <ButtonCreateCategoryOrFolder
            refetch={refetchAllCategory}
            ueId={parentUe?.id}
            parentCategory={currentCategory}
          />
          <ButtonCreateCourse
            refetch={refetchCategory}
            ueType={ueType}
            categoryId={categoryId}
            ueId={null}
          />
          <ButtonCreatePage
            refetch={refetchCategory}
            ueType={ueType}
            categoryId={categoryId}
            ueId={null}
          />
          {FEATURE_FLAGS.EnableImportedCourses && (
            <ButtonImportCourse
              refetch={refetchCategory}
              ueType={ueType}
              categoryId={categoryId}
              ueId={null}
            />
          )}
        </>
      )}

      {/***** ROOT *****/}
      {!ueId && !categoryId && !coursId && (
        <>
          {features?.enableFormation && <ButtonCreateFormation />}
          <ButtonCreateSubject refetch={refetchRoot} />
          <ButtonCreateSubject refetch={refetchRoot} shouldCreateFolder />
        </>
      )}
    </div>
  );
};
