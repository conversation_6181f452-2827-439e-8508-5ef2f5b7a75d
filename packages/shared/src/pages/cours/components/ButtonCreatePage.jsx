import { EditCoursCardModal } from '@/shared/pages/cours/components/EditCoursCardModal.jsx';
import { Button, Popover, Space } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FilePen } from 'lucide-react';

/* Bouton créer page */
export default function ButtonCreatePage({
  refetch,
  ueType = null,
  categoryId = null,
  ueId = null,
  small = false
}) {
  const { t } = useTranslation();
  const [createVisible, setCreateVisible] = useState(false);

  const closeModalHandler = () => {
    setCreateVisible(false);
    refetch(); // Load new modifications
  };

  return (
    <div>
      <Popover
        content={
          <p style={{ maxWidth: 350 }}>
            {t('CreatePageExplanation')} <br /> {t('CreatePageExplanation2')}
          </p>
        }
      >
        <Button
          style={{ height: 'auto', padding: 24, width: 250 }}
          type="text"
          onClick={() => setCreateVisible(true)}
          block
        >
          <Space direction={'vertical'} size={'small'}>
            <FilePen style={{ fontSize: '2.8rem' }} />
            <b>{t('CreateNewPage')}</b>
          </Space>
        </Button>
      </Popover>
      {createVisible && (
      <EditCoursCardModal
        isVisible={createVisible}
        closeModalHandler={closeModalHandler}
        uecategoryId={categoryId}
        modalType="CREATE"
        ueType={ueType}
        cours={{}}
        ueId={ueId}
        isPage
      />
      )}
    </div>
  );
}
