import { isAdmin } from '@/shared/utils/authority';
import { Button, Dropdown, Menu } from 'antd';
import DownOutlined from '@ant-design/icons/lib/icons/DownOutlined';
import React from 'react';
import { router } from 'umi';
import { useTranslation } from 'react-i18next';

export const CategoryAdminMenu = ({ ue }) => {
  const { t } = useTranslation();
  const handleMenuClick = (e) => {
    if (e.key === 'addCategory') {
      // console.log('Ajout UE')
    } else if (e.key === 'tuteurs') {
      router.push('/admin/permissions');
    }
  };

  const adminMenu = (
    <Menu onClick={handleMenuClick}>
      <Menu.Item key="tuteurs">{t('ManageTeacher')}</Menu.Item>
    </Menu>
  );

  return (
    isAdmin() && [
      <Dropdown overlay={adminMenu}>
        <Button type="primary">
          {t('Admin')} <DownOutlined />
        </Button>
      </Dropdown>
    ]
  );
};
