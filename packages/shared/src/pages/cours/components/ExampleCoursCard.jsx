import { Badge, Card, Statistic } from 'antd';
import { BookMarked, CandlestickChart, File, Image, MessageSquareText, View } from 'lucide-react';
import React, { useContext } from 'react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';

export const ExampleCoursCard = ({
  title,
  description,
  customImage,
  previewType,
  fileCustomImage,
  customImageUrl = null,
  color = null
}) => {
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  return (
    <Badge.Ribbon
      style={{ background: 'red', color: 'white', placement: 'start', marginTop: 10 }}
      text="NEW"
    >
      <Card
        cover={
          <div
            style={{
              height: '30px',
              backgroundColor: color ? color : primaryColor,
              borderRadius: '10px 10px 0px 0px'
            }}
          />
        }
        style={{
          width: '275px',
          height: '330px',
          textAlign: 'center'
        }}
      >
        <div style={{ height: '70px' }}>
          <div
            style={{
              fontSize: '20px',
              fontWeight: '700',
              color: 'black',
              maxHeight: '40px',
              marginTop: '-20px',
              lineHeight: '20px',
              overflow: 'hidden'
            }}
          >
            {title}
          </div>

          <div
            style={{
              fontSize: '13px',
              fontWeight: '700',
              height: '30px',
              marginTop: '5px',
              lineHeight: '15px',
              color: '#9A9A9A',
              overflow: 'hidden'
            }}
          >
            {description}
          </div>
        </div>

        <div style={{ margin: 'auto', marginTop: '10px' }}>
          {customImage === null && (previewType === 'pdf') === 'image' ? (
            <File style={{ fontSize: 80 }} />
          ) : (
            <>
              {previewType === 'image' ? (
                fileCustomImage || customImageUrl ? (
                  <img
                    src={
                      fileCustomImage
                        ? URL.createObjectURL(fileCustomImage)
                        : customImageUrl
                          ? customImageUrl
                          : ''
                    }
                    alt="preview"
                    style={{
                      maxHeight: `147px`,
                      maxWidth: `148px`
                    }}
                  />
                ) : (
                  <Image style={{ fontSize: 80 }} />
                )
              ) : (
                <BookMarked style={{ fontSize: 80 }} />
              )}
            </>
          )}
        </div>

        <div
          style={{
            position: 'absolute',
            display: 'flex',
            justifyContent: 'center',
            bottom: '10px',
            margin: 'auto',
            fontWeight: '600',
            width: '100%',
            marginLeft: '-24px',
            gap: 30
          }}
        >
          <Statistic
            valueStyle={{ fontSize: 18 }}
            suffix={<View style={{ fontSize: 22, marginBottom: -3 }} />}
            value={'—'}
          />

          <Statistic
            valueStyle={{ fontSize: 18 }}
            suffix={<CandlestickChart style={{ fontSize: 22, marginBottom: -3 }} />}
            value={'—'}
          />

          <Statistic
            valueStyle={{ fontSize: 18 }}
            suffix={<MessageSquareText style={{ fontSize: 22, marginBottom: -3 }} />}
            value={'—'}
          />
        </div>
      </Card>
    </Badge.Ribbon>
  );
};
