import {
  PedagogicCascaderSelector,
  PedagogicCascaderSelectorTarget,
  SelectorType
} from '@/shared/components/Cours/PedagogicCascaderSelector.jsx';
import {
  DELETE_UECATEGORY,
  QUERY_ALL_GROUPS,
  QUERY_COURS_IN_UECATEGORY_MINIMAL,
  QUERY_UE_CATEGORY_ID_WITH_GROUPS
} from '@/shared/graphql/cours.js';
import { QUERY_ALL_FOLDERS } from '@/shared/graphql/folders.js';
import { ADD_GROUP_UECATEG, REMOVE_GROUP_UECATEG } from '@/shared/graphql/groupes.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager.jsx';
import IndividualPermissionsManager from '@/shared/pages/admin/permissions/IndividualPermissionsManager.jsx';
import { mapGroupsForTreeSelection } from '@/shared/services/groupes.js';
import { getLanguageName, tr } from '@/shared/services/translate.js';
import { isSuperAdmin } from '@/shared/utils/authority.js';
import {
  getUrlProtectedRessource,
  GlobalConfig,
  isMobile,
  showGqlErrorsInMessagePopupFromException
} from '@/shared/utils/utils.js';
import {
  Alert,
  Button,
  Divider,
  Drawer,
  Form,
  Input,
  InputNumber,
  message,
  notification,
  Popconfirm,
  Tabs,
  Tag,
  TreeSelect
} from 'antd';
import { useApolloClient, useMutation, useQuery } from '@apollo/client';
import React, { useContext, useEffect, useState } from 'react';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult';
import PropTypes from 'prop-types';
import { CREATE_UECATEGORY, UPDATE_UECATEGORY } from '@/shared/graphql/cours';
import { DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { ExampleFolderCard } from '@/shared/pages/cours/components/ExampleFolderCard';
import SingleFileUploader from '@/shared/components/SingleFileUploader';

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE'
};

const getMutationFromModalType = (modalType) => {
  switch (modalType) {
    case ModalType.CREATE:
      return CREATE_UECATEGORY;
    case ModalType.UPDATE:
      return UPDATE_UECATEGORY;
    default:
      return UPDATE_UECATEGORY;
  }
};

const propTypes = {
  closeModalHandler: PropTypes.func.isRequired,
  isVisible: PropTypes.bool.isRequired,
  modalType: PropTypes.oneOf(Object.values(ModalType))
};

/* Déplacer Category, dans matières (non-dossier), et aussi dans catégorie */
export const MoveCategory = ({ category, closeModalHandler, multiple = false, ids = [] }) => {
  const { t, i18n } = useTranslation();
  const [UECategMutation, { loading, data, error }] = useMutation(UPDATE_UECATEGORY);

  const [selectedUeId, setSelectedUeId] = useState();
  const [selectedCategoryId, setSelectedCategoryId] = useState();
  const [selectedItemName, setSelectedItemName] = useState();

  const handleMove = async () => {
    try {
      const categoryEdited = {};

      // Si déplace dans categorie, elle perd sa matière parente
      if (selectedCategoryId) {
        categoryEdited.parentId = selectedCategoryId;
        categoryEdited.ueId = null;
      }

      // Si déplace dans matière
      if (selectedUeId) {
        categoryEdited.ueId = selectedUeId;
        categoryEdited.parentId = null;
      }

      if (multiple && ids?.length > 0) {
        for (const id of ids) {
          const i = ids.indexOf(id);
          notification.info({
            message: `Mise à jour item ${i + 1} / ${ids?.length}`,
            description: 'Veuillez patienter',
            key: 'updating'
            // duration: 0,
          });
          await UECategMutation({
            variables: {
              id,
              category: categoryEdited
            }
          });
        }
        notification.success({
          message: `Terminé`,
          description: 'Mise à jour effectuée avec succès',
          key: 'updating'
        });
      } else {
        await UECategMutation({
          variables: {
            id: category?.id,
            category: categoryEdited
          }
        });
        message.success({ content: t('general.Updated!'), key: 'editUe' });
      }

      closeModalHandler();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const handleSelectCategoryOrUE = ({ label, value, type }) => {
    setSelectedItemName(label);
    if (type === 'ue') {
      setSelectedUeId(value);
    } else if (type === 'category') {
      setSelectedCategoryId(value);
    }
  };

  return (
    <>
      <>
        <p>Sélectionnez le dossier ou la matière</p>

        <PedagogicCascaderSelector
          onSelectTarget={handleSelectCategoryOrUE}
          acceptedTypes={[
            PedagogicCascaderSelectorTarget.Category,
            PedagogicCascaderSelectorTarget.UE
          ]}
          placeholder="Sélectionner un dossier ou matière"
          selectorType={SelectorType.TreeSelect}
        />

        <br />
        <br />
        {selectedItemName && (
          <p>
            Sera déplacé dans :<br />
            <b>{selectedItemName}</b>
          </p>
        )}
      </>

      <br />
      <br />
      {error && <SmallErrorsAlert error={error} />}
      <Button onClick={handleMove} loading={loading}>
        {t('general.Continue')}
      </Button>
    </>
  );
};

export const EditCreateCategoryModal = ({
  closeModalHandler,
  modalType,
  isVisible,
  name,
  ueId,
  id,
  categoryVisibility,
  category = {},
  parentCategory = null,
  color = null
}) => {
  const { t, i18n } = useTranslation();
  const { enabledLanguages } = useContext(GlobalContext);
  const [form] = Form.useForm();
  const [CategoryMutation, { loading, error }] = useMutation(getMutationFromModalType(modalType));

  const [addGroupToUECateg, addGroupData] = useMutation(ADD_GROUP_UECATEG);
  const [removeGroupToUECateg, removeGroupData] = useMutation(REMOVE_GROUP_UECATEG);

  const [deleteCategory] = useMutation(DELETE_UECATEGORY);
  const client = useApolloClient();
  const [fileImage, setFileImage] = useState(null);
  const [isCategoryVisible, setIsCategoryVisible] = useState(categoryVisibility);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);

  const values = Form.useWatch([], form);

  const getTitle = (lang) => values?.[tr('name', lang)] || '';
  const getDescription = (lang) => values?.[tr('description', lang)] || '';
  const getCustomImageUrl = () => {
    return category?.image
      ? getUrlProtectedRessource(GlobalConfig.get().FILES_URL + category.image)
      : null;
  };

  const renderPreview = () => {
    return (
      <div
        style={{
          flex: '0 0 300px',
          minWidth: 300,
          display: 'flex',
          justifyContent: 'center'
        }}
      >
        <ExampleFolderCard
          title={getTitle(selectedLanguage)}
          description={getDescription(selectedLanguage)}
          fileCustomImage={fileImage}
          customImageUrl={getCustomImageUrl()}
          color={color}
        />
      </div>
    );
  };

  useEffect(() => {
    setIsCategoryVisible(categoryVisibility);
  }, []);

  // Query uecateg groups
  const {
    data: dataUeCategGroups,
    loading: loadingUeGroups,
    error: errorUeGroups,
    refetch: refetchCategGroups
  } = useQuery(QUERY_UE_CATEGORY_ID_WITH_GROUPS, {
    fetchPolicy: 'no-cache',
    skip: !id,
    variables: {
      id
    }
  });
  const ueGroups = dataUeCategGroups?.ueCategory?.groupes?.filter((g) => !g?.isIndividual);
  const ueGroupsIndividual = dataUeCategGroups?.ueCategory?.groupes?.filter((g) => g?.isIndividual);

  // All Folders query
  const {
    loading: loadingFolders,
    error: errorFolders,
    data: dataFolders,
    refetch: refetchFolders
  } = useQuery(QUERY_ALL_FOLDERS, { fetchPolicy: 'cache-and-network' });
  const folders = dataFolders?.folders;
  const dataGroupes = useQuery(QUERY_ALL_GROUPS, { fetchPolicy: 'no-cache' });
  const allGroupes = dataGroupes?.data?.allGroupes;
  const allGroups = dataGroupes?.data?.allGroupes;
  const foldersIds = allGroupes?.map((gr) => gr?.folderId);
  const foldersToShow = folders?.filter((f) => foldersIds?.includes(f.id));
  const [targetGroupsIds, setTargetGroupsIds] = React.useState([]);

  const handleFinish = async (data) => {
    try {
      let newCategory = { ...data, isVisible: isCategoryVisible };
      if (fileImage) {
        newCategory = { ...newCategory, image: fileImage };
      } else {
        newCategory = { ...newCategory };
      }
      if (parentCategory) {
        newCategory = { ...newCategory, parentId: parentCategory.id };
      } else {
        newCategory = { ...newCategory, ueId };
      }

      if (modalType === ModalType.UPDATE) {
        await CategoryMutation({ variables: { id, category: newCategory } });
        message.success(t('Updated'));
      } else {
        // Create
        newCategory.groupsToAdd = targetGroupsIds;
        await CategoryMutation({ variables: { category: newCategory } });
        message.success(t('Created'));
      }
      await closeModalHandler();
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const onDeleteCategory = async () => {
    try {
      const { data } = await client.query({
        query: QUERY_COURS_IN_UECATEGORY_MINIMAL,
        variables: { ueCategoryId: id },
        fetchPolicy: 'no-cache'
      });
      const { coursInUECategory } = data;
      if (coursInUECategory.length > 0) {
        message.error({
          content: 'Cette catégorie contient des cours et ne peut pas être supprimée',
          key: 'update'
        });
      } else {
        await deleteCategory({ variables: { id } });
        message.success(t('DeletedWithSuccess'));
        closeModalHandler();
      }
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const handleSelect = async (_, option) => {
    setTargetGroupsIds([...targetGroupsIds, option.key]);
    //setTargetGroupsNames([...targetGroupsNames, option.value]);
    return true;
  };
  const handleDeselect = async (_, option) => {
    setTargetGroupsIds(targetGroupsIds.filter((id) => id !== option.key));
    //setTargetGroupsNames(targetGroupsNames.filter(name => name !== option.value));
    return true;
  };
  const handleChange = async (shouldAdd, groupId) => {
    const option = { key: groupId, value: allGroups.find((g) => g.id === groupId)?.name };
    if (shouldAdd) {
      return await handleSelect(null, option);
    } else {
      return await handleDeselect(null, option);
    }
  };

  const groupeTagRender = ({ label, value, closable, onClose, key }) => {
    const canClose = allGroups.map((g) => g.name).includes(value);
    return (
      <Tag
        value={value}
        key={key}
        color="geekblue"
        closable={closable && canClose}
        onClose={onClose}
        style={{ marginRight: 3 }}
      >
        {label}
      </Tag>
    );
  };

  return (
    <Drawer
      placement={'bottom'}
      onClose={() => closeModalHandler()}
      title={modalType === ModalType.UPDATE ? `${t('Edit')} ${name}` : t('Create')}
      open={isVisible}
      footer={null}
      height={isMobile ? 'calc(100% - 109px)' : 'calc(100% - 64px)'}
    >
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab={t('tab.General')} key="1">
          {/* Show small error(s) if needed */}
          <SmallErrorsAlert error={error} loading={loading} />
          <Form
            layout="vertical"
            onFinish={handleFinish}
            form={form}
            initialValues={modalType === ModalType.UPDATE ? category : {}}
          >
            <p>
              <b>Type : </b>
              <Tag>Dossier</Tag>
            </p>
            <Alert
              type="info"
              showIcon
              message="Un dossier peut contenir des matières. Une matière ne peut pas contenir d'autres matières. Une matière peut contenir des dossiers et des cours."
            />
            <div
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 24,
                justifyContent: 'space-between',
                marginTop: 24
              }}
            >
              <div
                style={{
                  flex: '1 1 300px',
                  minWidth: 300,
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 24
                }}
              >
                <Tabs
                  defaultActiveKey={i18n.language}
                  style={{ minWidth: 300 }}
                  onChange={(a) => setSelectedLanguage(a)}
                >
                  {enabledLanguages &&
                    enabledLanguages?.map((lang) => (
                      <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                        <Form.Item
                          name={tr('name', lang)}
                          label={t('CategoryName')}
                          rules={[{ min: 1, message: `Veuillez entrer le nom du dossier` }]}
                        >
                          <Input type="text" placeholder={t('ExempleCategoryName')} />
                        </Form.Item>

                        <Form.Item name={tr('description', lang)} label={t('Description')}>
                          <Input type="text" placeholder={t('placeholderDescription')} />
                        </Form.Item>
                      </Tabs.TabPane>
                    ))}
                </Tabs>

                <Form.Item label={t('CategoryImage')}>
                  <SingleFileUploader
                    file={fileImage}
                    setFile={setFileImage}
                    fileUrl={getCustomImageUrl()}
                  />
                </Form.Item>

                <Form.Item name="order" label={t('DisplayOrder')}>
                  <InputNumber type="number" />
                </Form.Item>
              </div>

              {renderPreview()}

              <Divider />

              <Form.Item>
                <Button
                  style={{ marginLeft: 'auto' }}
                  htmlType="submit"
                  type="primary"
                  loading={loading}
                >
                  {modalType === 'UPDATE' ? t('Update') : t('general.Create')}
                </Button>
              </Form.Item>

              {isSuperAdmin() && modalType === ModalType.UPDATE && (
                <Form.Item>
                  <Popconfirm
                    title={t('SureOfDeletion')}
                    onConfirm={(e) => onDeleteCategory()}
                    okText={t('general.yes')}
                    cancelText={t('general.no')}
                  >
                    <Button type="primary" danger icon={<DeleteOutlined />}>
                      {t('Delete')}
                    </Button>
                  </Popconfirm>
                </Form.Item>
              )}
            </div>
          </Form>
        </Tabs.TabPane>

        {modalType === ModalType.UPDATE && (
          <Tabs.TabPane tab={t('tab.Move')} key="2">
            <MoveCategory category={category} closeModalHandler={closeModalHandler} />
          </Tabs.TabPane>
        )}

        <Tabs.TabPane tab={t('Permissions')} key="3">
          {/* Sélection des groupes ayant accès à cet ue, need query groupes sur ue */}
          <p>Groupes ayant accès :</p>
          <br />
          {modalType === ModalType.UPDATE && (
            <>
              <AbstractGroupsManager
                //entityName={''}
                entityId={category?.id}
                groupes={ueGroups}
                addGroupMutation={ADD_GROUP_UECATEG}
                removeGroupMutation={REMOVE_GROUP_UECATEG}
                groupParameterName="groupId"
                entityParameterName="ueCategoryId"
              />

              <br />
              <Divider />
              <IndividualPermissionsManager
                individualGroups={ueGroupsIndividual}
                onAdd={async (individualGroupId) => {
                  await addGroupToUECateg({
                    variables: {
                      ueCategoryId: id,
                      groupId: individualGroupId
                    }
                  });
                  await refetchCategGroups();
                }}
                onRemove={async (groupId) => {
                  await removeGroupToUECateg({
                    variables: {
                      ueCategoryId: id,
                      groupId
                    }
                  });
                  await refetchCategGroups();
                }}
              />
            </>
          )}
          {modalType === ModalType.CREATE && (
            <>
              {allGroups && foldersToShow && (
                <TreeSelect
                  treeCheckable
                  treeNodeFilterProp="title"
                  placeholder={t('ChooseGroups')}
                  treeData={mapGroupsForTreeSelection(foldersToShow, allGroups)}
                  style={{ width: '100%' }}
                  onChange={async (newValue, label, extra) => {
                    const groupId = extra.triggerValue;
                    const shouldAdd = extra.checked;
                    if (groupId?.startsWith('folder')) {
                      // Find groups in folder
                      const folder = mapGroupsForTreeSelection(foldersToShow, allGroups)?.find(
                        (g) => g?.key === groupId
                      );
                      // Multiple group
                      let nextGroupsIds = targetGroupsIds;
                      await folder?.children?.forEach((group) => {
                        const option = {
                          key: group?.key,
                          value: allGroups.find((g) => g.id === group?.key)?.name
                        };
                        if (shouldAdd) {
                          nextGroupsIds = [...nextGroupsIds, option.key];
                        } else {
                          nextGroupsIds = nextGroupsIds.filter((id) => id !== option.key);
                        }
                      });
                      setTargetGroupsIds(nextGroupsIds);
                    } else {
                      // Single group
                      await handleChange(shouldAdd, groupId);
                    }
                  }}
                  tagRender={allGroups && groupeTagRender}
                />
              )}
            </>
          )}
        </Tabs.TabPane>
      </Tabs>
    </Drawer>
  );
};

EditCreateCategoryModal.propTypes = propTypes;
EditCreateCategoryModal.defaultProps = {
  modalType: ModalType.CREATE
};
