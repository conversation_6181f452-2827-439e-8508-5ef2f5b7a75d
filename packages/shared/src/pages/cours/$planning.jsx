import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import { QUERY_UE_ID } from '@/shared/graphql/cours.js';
import { UEPlanning } from '@/shared/pages/cours/components/UEPlanning.jsx';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import React from 'react';
import { PageHeader } from '@ant-design/pro-layout';
import { useQuery } from '@apollo/client';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import { router } from 'umi';
import CoursBreadcrumb from '@/shared/components/Cours/CoursBreadcrumb';

export default function (props) {
  useEffectScrollTop();
  const ueId = props.match.params.ue;
  const { data, error, loading, refetch } = useQuery(QUERY_UE_ID, {
    variables: { id: ueId },
    pollInterval: 20000
  });
  const ue = !error && !loading && data ? data.ue : null;

  return (
    <>
      <FullMediParticlesBreadCrumb title={`Planning ${ue && ue.name}`} />
      <ExoteachLayout>
        <div style={{ marginLeft: '24px', marginTop: '10px' }}>
          <CoursBreadcrumb ue={ue} ueId={ue?.id} />
        </div>
        <PageHeader
          onBack={() => router.goBack()}
          title={`Planning ${ue && ue.name}`}
          subTitle={ue && ue.description}
          style={{ padding: '8px 24px 16px 24px' }}
        />

        <UEPlanning ueId={ueId} />
      </ExoteachLayout>
    </>
  );
}
