import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';
import {
  MUTATION_ADD_GROUPE_DATE_DIFFUSION,
  MUTATION_ADD_ORGANIZER_DATE_DIFFUSION,
  MUTATION_ADD_PARTICIPANT_DATE_DIFFUSION,
  MUTATION_CREATE_DATE_DIFFUSION,
  MUTATION_EDIT_DATE_DIFFUSION,
  MUTATION_REMOVE_DATE_DIFFUSION,
  MUTATION_REMOVE_GROUPE_DATE_DIFFUSION,
  MUTATION_REMOVE_ORGANIZER_DATE_DIFFUSION,
  MUTATION_REMOVE_PARTICIPANT_DATE_DIFFUSION,
  QUERY_EDT_FOR_COURS,
  QUERY_EDT_FOR_EVENT,
  QUERY_EDT_FOR_EXAM,
  QUERY_EDT_FOR_QCM
} from '@/shared/graphql/edt.js';
import { QUERY_ALL_QCM_TYPE } from '@/shared/graphql/qcm.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import AbstractGroupsAndIndividualGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsAndIndividualGroupsManager';
import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager.jsx';
import IndividualPermissionsManager from '@/shared/pages/admin/permissions/IndividualPermissionsManager.jsx';
import CourseActivityLogs from '@/shared/pages/cours/details/components/CourseActivityLogs.jsx';
import { EditCours } from '@/shared/pages/cours/details/components/EditCoursModal.jsx';
import { TypeQcmCoursSeriesManager } from '@/shared/pages/cours/details/components/TypeQcmCours/TypeQcmCoursManager.jsx';
import { CoursTypesQcmSettings_MODULE_TYPES } from '@/shared/services/cours.js';
import { EXAMSESSION_AVAILABILITY } from '@/shared/services/exam.js';
import { groupByKey } from '@/shared/services/qcm.js';
import { getLanguageName, tr } from '@/shared/services/translate.js';
import {
  DATE_FORMATS,
  IS_DEV,
  isMobile,
  showGqlErrorsInMessagePopupFromException,
  TimeZoneManager
} from '@/shared/utils/utils.js';
import InfoCircleOutlined from '@ant-design/icons/lib/icons/InfoCircleOutlined';
import PlusOutlined from '@ant-design/icons/lib/icons/PlusOutlined.js';
import {
  CalendarOutlined,
  CompassOutlined,
  LockOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  SmileOutlined,
  TeamOutlined
} from '@ant-design/icons';
import {
  Alert,
  Button,
  Card,
  Checkbox,
  Collapse,
  DatePicker,
  Divider,
  Drawer,
  Form,
  Input,
  message,
  Popconfirm,
  Radio,
  Select,
  Slider,
  Space,
  Tabs,
  Tooltip
} from 'antd';
import { useApolloClient, useMutation, useQuery } from '@apollo/client';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import 'dayjs/locale/fr';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult';
import { UPDATE_COURS } from '@/shared/graphql/cours';
import { useTranslation } from 'react-i18next';
import { AgnosticWatermarkModalModule } from '@/shared/components/WatermarkComponants/AgnosticWatermarkModalModule.jsx';
import {
  supportedWatermarkPdfTypes,
  verifyWatermarkModuleForm
} from '@/shared/services/watermark.js';
import { ExoUserLight } from '@/shared/components/User/ExoUserLight';
import SearchUser from '@/shared/components/User/SearchUser';
import { QUERY_ALL_BUILDINGS } from '@/shared/graphql/events';
import {
  ADD_GROUP_COURS,
  QUERY_USER_IDS_IN_GROUP,
  REMOVE_GROUP_COURS
} from '@/shared/graphql/groupes';
import { QUERY_COURS_GROUPS } from '@/shared/graphql/cours.js';

import { NOTIFY_COURSE_UPDATE } from '@/shared/graphql/notifications';
import NotificationForm from '@/shared/components/Notification/NotificationForm';
import { EventRepeatRuleEditor } from '@/shared/pages/event/components/EventRepeatRuleEditor/EventRepeatRuleEditor';

dayjs.locale('fr');
dayjs.extend(customParseFormat);

const DateDiffusionGroupsManager = ({ groupes, dateDiffusionId, refetch }) => {
  return (
    <AbstractGroupsAndIndividualGroupsManager
      groupes={groupes}
      abstractGroupsManagerProps={{
        title: <h5>Groupes</h5>,
        entityId: dateDiffusionId,
        addGroupMutation: MUTATION_ADD_GROUPE_DATE_DIFFUSION,
        removeGroupMutation: MUTATION_REMOVE_GROUPE_DATE_DIFFUSION,
        entityParameterName: 'dateDiffusionId',
        groupParameterName: 'groupId'
      }}
      individualPermissionsManagerProps={{
        title: <h5>Utilisateurs</h5>,
        entityId: dateDiffusionId,
        addGroupMutation: MUTATION_ADD_GROUPE_DATE_DIFFUSION,
        removeGroupMutation: MUTATION_REMOVE_GROUPE_DATE_DIFFUSION,
        entityParameterName: 'dateDiffusionId',
        groupParameterName: 'groupId',
        showText: false,
        refetchGroup: refetch
      }}
    />
  );
};

const DateDiffusionEdition = ({ dateDiff, refetch, examSessionId = null, settings }) => {
  const { t } = useTranslation();
  const [EditDateDiffusion, { loading: loadingMut }] = useMutation(MUTATION_EDIT_DATE_DIFFUSION);
  const [RemoveDateDiffusion] = useMutation(MUTATION_REMOVE_DATE_DIFFUSION);

  const [addOrganizer] = useMutation(MUTATION_ADD_ORGANIZER_DATE_DIFFUSION);
  const [removeOrganizer] = useMutation(MUTATION_REMOVE_ORGANIZER_DATE_DIFFUSION);

  const [addParticipant] = useMutation(MUTATION_ADD_PARTICIPANT_DATE_DIFFUSION);
  const [removeParticipant] = useMutation(MUTATION_REMOVE_PARTICIPANT_DATE_DIFFUSION);

  const [form] = Form.useForm();
  const [availability, setAvailability] = useState(
    dateDiff?.availability || EXAMSESSION_AVAILABILITY.allTheTime
  );
  const [showDates, setShowDates] = useState(true);

  const [showOrganizerSearch, setShowOrganizerSearch] = useState(false);
  const [showParticipantSearch, setShowParticipantSearch] = useState(false);

  const [showGroupSearch, setShowGroupSearch] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState(false);

  const client = useApolloClient();

  const [organizers, setOrganizers] = useState(dateDiff?.organizersIds || []);
  const [participantsIds, setParticipantsIds] = useState(dateDiff?.participantsIds || []);

  const {
    data: dataBuildings,
    loading: loadingBuildings,
    error: errorBuildings
  } = useQuery(QUERY_ALL_BUILDINGS, { fetchPolicy: 'cache-and-network' });
  const [buildingId, setBuildingId] = useState(dateDiff?.buildingId);
  const [roomId, setRoomId] = useState(dateDiff?.roomId);
  const allBuildings = dataBuildings?.allBuildings;
  const [currentTimeZone, setCurrentTimezone] = useState(
    dateDiff?.timezone || TimeZoneManager.getDefaultTimeZone()
  );

  const [recurrenceRule, setRecurrenceRule] = useState(dateDiff?.recurrenceRule);

  IS_DEV && console.log('re-rendering DateDiffusionEdition');

  const handleRecurrenceChange = useCallback((rule) => {
    setRecurrenceRule((prevRule) => {
      if (JSON.stringify(prevRule) !== JSON.stringify(rule)) {
        console.log('Récurrence mise à jour:', rule);
        return rule;
      }
      return prevRule;
    });
  }, []);

  useEffect(() => {
    if (dateDiff?.currentTimeZone) {
      setCurrentTimezone(dateDiff?.currentTimeZone);
    }
    setAvailability(dateDiff?.availability || EXAMSESSION_AVAILABILITY.allTheTime);
    setBuildingId(dateDiff?.buildingId);
    setRoomId(dateDiff?.roomId);
    setOrganizers(dateDiff?.organizersIds || []);
    setParticipantsIds(dateDiff?.participantsIds || []);
  }, [dateDiff]);

  const onAddOrganizer = async (userId) => {
    await addOrganizer({ variables: { dateDiffusionId: dateDiff.id, userId } });
    await refetch();
  };
  const onRemoveOrganizer = async (userId) => {
    await removeOrganizer({ variables: { dateDiffusionId: dateDiff.id, userId } });
    await refetch();
  };
  const onAddParticipant = async (userId) => {
    await addParticipant({ variables: { dateDiffusionId: dateDiff.id, userId } });
    await refetch();
  };
  const onRemoveParticipant = async (userId, shouldRefetch = true) => {
    await removeParticipant({ variables: { dateDiffusionId: dateDiff.id, userId } });
    if (shouldRefetch) {
      await refetch();
    }
  };

  const deleteAllParticipants = async () => {
    const participants = dateDiff?.participantsIds || [];
    await Promise.all(
      participants.map(async (id) => {
        await onRemoveParticipant(id, false);
      })
    );
    await refetch();
  };

  const getDateBegin = () => {
    if (currentTimeZone !== TimeZoneManager.getDefaultTimeZone()) {
      return (dateDiff?.date && dayjs(dateDiff?.date).tz(currentTimeZone)) || undefined;
    }
    return (dateDiff?.date && dayjs(dateDiff.date)) || undefined;
  };
  const getDateEnd = () => {
    if (currentTimeZone !== TimeZoneManager.getDefaultTimeZone()) {
      return (dateDiff?.dateEnd && dayjs(dateDiff?.dateEnd).tz(currentTimeZone)) || undefined;
    }
    return (dateDiff?.dateEnd && dayjs(dateDiff.dateEnd)) || undefined;
  };

  const handleFinish = async (formData) => {
    try {
      await EditDateDiffusion({
        variables: {
          dateDiffusionId: dateDiff.id,
          dateDiffusion: {
            updateInfos: formData.updateInfos,
            allDay: formData?.allDay || false,
            link: formData?.link || null,
            availability,
            timezone: currentTimeZone,
            roomId,
            buildingId,
            recurrenceRule
          }
        }
      });
      message.success(t('Updated'));
    } catch (e) {
      message.error('Erreur lors de la mise à jour, réessayez');
      console.error(e);
    }
  };

  // TODO add mutation add/delete organizer with refetch

  const renderGroupSearch = (type) => {
    return (
      <>
        {showGroupSearch && (
          <div style={{ marginBottom: '20px', textAlign: 'center' }}>
            <AbstractGroupsManager
              treeCheckable={false}
              enableFolderChecking={false}
              onChange={(shouldAdd, groupId) => {
                setSelectedGroupId(groupId);
              }}
              showNumberOfUsers
            />

            {/*
            {allGroupes && foldersToShow && (
              <TreeSelect
                treeNodeFilterProp="title"
                showSearch
                placeholder="Choisir groupe..."
                treeData={mapGroupsForTreeSelectionWithNumberOfUsers(foldersToShow, allGroupes)}
                style={{ width: '100%' }}
                onChange={async (newValue, label, extra) => {
                  const groupId = extra.triggerValue;
                  if (groupId.startsWith('folder')) {
                    message.error('Vous ne pouvez pas choisir un dossier, veuillez choisir un groupe');
                  } else {
                    setSelectedGroupId(groupId);
                  }
                }}
                tagRender={allGroupes && groupeTagRender}
              />
            )}
            */}

            <br />
            <br />
            <Button
              block
              disabled={!selectedGroupId}
              type="primary"
              onClick={async () => {
                message.loading({ content: 'Ajout en cours', key: 'add-group' });
                // Get user ids in selected group
                const { data: dataUserIds } = await client.query({
                  query: QUERY_USER_IDS_IN_GROUP,
                  variables: { groupId: selectedGroupId },
                  fetchPolicy: 'no-cache'
                });
                const userIds = dataUserIds?.getUserIdsInGroup;
                if (userIds) {
                  // Deduplicate
                  const uniqueUsers = [...new Set([...userIds])];
                  await Promise.all(
                    uniqueUsers.map(async (id) => {
                      await onAddParticipant(id);
                    })
                  );

                  await refetch();
                  message.success({ content: 'Ajouté avec succès', key: 'add-group' });
                }
                setShowGroupSearch(false);
                setSelectedGroupId(false);
              }}
            >
              Ajouter tous les membres de ce groupe
            </Button>
          </div>
        )}
      </>
    );
  };

  const { Panel } = Collapse;

  const addTime = async (dd, number, type) => {
    await EditDateDiffusion({
      variables: {
        dateDiffusion: {
          dateEnd: dayjs(dd?.date).add(number, type).toDate()
        },
        dateDiffusionId: dd.id
      }
    });
    await refetch();
  };

  return (
    <Collapse defaultActiveKey={['1']}>
      <Panel
        style={{ fontWeight: '700' }}
        key="1"
        header={
          getDateBegin() && getDateEnd()
            ? dayjs(getDateBegin()).isSame(dayjs(getDateEnd()), 'day')
              ? `${getDateBegin().format('Do MMMM YYYY : HH[h]mm')} à ${getDateEnd().format(
                  'HH[h]mm'
                )}`
              : `${getDateBegin().format('Do MMMM YYYY : HH[h]mm')} - ${getDateEnd().format(
                  'Do MMMM YYYY : HH[h]mm'
                )}`
            : getDateBegin()
              ? `⚠️ ${getDateBegin().format('Do MMMM YYYY : HH[h]mm')} - Date à définir ⚠`
              : `⚠️ Date à définir - ${
                  getDateEnd() ? getDateEnd().format('Do MMMM YYYY : HH[h]mm') : 'Date à définir ⚠'
                }`
        }
      >
        <Card style={{ marginBottom: '10px' }}>
          <Form
            onFinish={handleFinish}
            form={form}
            layout={'horizontal'}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 14 }}
            initialValues={{
              allDay: dateDiff.allDay,
              updateInfos: dateDiff.updateInfos,
              link: dateDiff.link
            }}
          >
            <Divider orientation={'left'}>
              <CalendarOutlined /> {t('Dates')}
            </Divider>

            {examSessionId && (
              <Form.Item label={t('Availability')}>
                <Radio.Group
                  onChange={async (e) => {
                    setAvailability(e.target.value);
                    await EditDateDiffusion({
                      variables: {
                        dateDiffusionId: dateDiff.id,
                        dateDiffusion: {
                          availability: e.target.value
                        }
                      }
                    });
                    //refetch();
                  }}
                  value={availability}
                  buttonStyle="solid"
                >
                  <Radio.Button value={EXAMSESSION_AVAILABILITY.allTheTime}>
                    {t('AllTheTime')}
                  </Radio.Button>
                  <Radio.Button value={EXAMSESSION_AVAILABILITY.date}>{t('From...')}</Radio.Button>
                  <Radio.Button value={EXAMSESSION_AVAILABILITY.period}>
                    {t('definedPeriod')}
                  </Radio.Button>
                </Radio.Group>
              </Form.Item>
            )}

            <Form.Item label={t('Localization')} style={{ width: 'auto' }}>
              <Select
                dropdownStyle={{ maxWidth: 'calc(100vw - 140px)' }}
                style={{ minWidth: '190px', maxWidth: 'calc(100vw - 140px)' }}
                showSearch
                //size="small"
                value={currentTimeZone}
                onSelect={(value, item) => {
                  setCurrentTimezone(value);
                }}
              >
                {Intl?.supportedValuesOf('timeZone')?.map((tz) => (
                  <Select.Option value={tz} key={tz} toSearch={tz}>
                    {tz}
                  </Select.Option>
                ))}
              </Select>

              <Button
                type="link"
                size={'medium'}
                onClick={() => {
                  setCurrentTimezone(TimeZoneManager.getDefaultTimeZone());
                }}
              >
                {t('ActualLocalization')}
              </Button>
            </Form.Item>

            <Form.Item label={t('AllDay')} name="allDay" valuePropName="checked">
              <Checkbox
                onChange={async (e) => {
                  const checked = e.target.checked;
                  await EditDateDiffusion({
                    variables: {
                      dateDiffusion: { allDay: checked },
                      dateDiffusionId: dateDiff.id
                    }
                  });
                }}
              />
            </Form.Item>

            {showDates && (
              <>
                {!form.getFieldValue('allDay') ? (
                  <>
                    <Form.Item label={t('BeginDate')} style={{ width: '100%' }}>
                      <DatePicker
                        value={getDateBegin()}
                        //locale={frFR}
                        style={{ width: '100%' }}
                        showTime={{
                          format: 'HH:mm'
                          //, defaultValue: dayjs('12:00', 'HH:mm')
                        }}
                        format={DATE_FORMATS.FULL_FR}
                        onChange={async (value) => {
                          try {
                            await EditDateDiffusion({
                              variables: {
                                dateDiffusion: { date: value.toDate() },
                                dateDiffusionId: dateDiff.id
                              }
                            });
                            message.success(t('Updated'));
                            await refetch();
                          } catch (e) {
                            showGqlErrorsInMessagePopupFromException(e);
                          }
                          form.setFieldsValue({ date: value });
                        }}
                      />
                    </Form.Item>
                    <Form.Item
                      // name="dateEnd"
                      label={t('EndDate')}
                    >
                      <DatePicker
                        value={getDateEnd()}
                        style={{ width: '100%' }}
                        //locale={frFR}
                        disabledDate={(current) => {
                          return current && current < dayjs(dateDiff.date).startOf('day');
                        }}
                        showTime={{
                          format: 'HH:mm'
                          //defaultValue: dayjs('12:00', 'HH:mm')
                        }}
                        format={DATE_FORMATS.FULL_FR}
                        renderExtraFooter={() => (
                          <div>
                            <Button
                              size="small"
                              type="link"
                              onClick={async () => addTime(dateDiff, 30, 'minute')}
                            >
                              +30m
                            </Button>
                            <Button
                              size="small"
                              type="link"
                              onClick={async () => addTime(dateDiff, 1, 'hour')}
                            >
                              +1h
                            </Button>
                            <Button
                              size="small"
                              type="link"
                              onClick={async () => addTime(dateDiff, 1.5, 'hour')}
                            >
                              +1h30
                            </Button>
                            <Button
                              size="small"
                              type="link"
                              onClick={async () => addTime(dateDiff, 2, 'hour')}
                            >
                              +2h
                            </Button>
                            <Button
                              size="small"
                              type="link"
                              onClick={async () => addTime(dateDiff, 2.5, 'hour')}
                            >
                              +2h30
                            </Button>
                            <Button
                              size="small"
                              type="link"
                              onClick={async () => addTime(dateDiff, 3, 'hour')}
                            >
                              +3h
                            </Button>
                          </div>
                        )}
                        onChange={async (value) => {
                          try {
                            await EditDateDiffusion({
                              variables: {
                                dateDiffusion: {
                                  dateEnd: value.toDate()
                                },
                                dateDiffusionId: dateDiff.id
                              }
                            });
                            message.success(t('Updated'));
                            await refetch();
                          } catch (e) {
                            showGqlErrorsInMessagePopupFromException(e);
                          }
                          form.setFieldsValue({ date: value });
                        }}
                      />
                    </Form.Item>
                  </>
                ) : (
                  <>
                    <Form.Item label={t('BeginDate')} style={{ width: '100%' }}>
                      <DatePicker
                        key={`allday-${dateDiff.allDay}-${dateDiff.date}`}
                        value={getDateBegin()}
                        //locale={frFR}
                        style={{ width: '100%' }}
                        format="DD/MM/YYYY"
                        onChange={async (value) => {
                          try {
                            await EditDateDiffusion({
                              variables: {
                                dateDiffusion: { date: value.toDate() },
                                dateDiffusionId: dateDiff.id
                              }
                            });
                            message.success(t('Updated'));
                            await refetch();
                          } catch (e) {
                            showGqlErrorsInMessagePopupFromException(e);
                          }
                          form.setFieldsValue({ date: value });
                        }}
                      />
                    </Form.Item>
                    <Form.Item
                      // name="dateEnd"
                      label={t('EndDate')}
                    >
                      <DatePicker
                        value={getDateEnd()}
                        style={{ width: '100%' }}
                        //locale={frFR}
                        disabledDate={(current) => {
                          return current && current < dayjs(dateDiff.date);
                        }}
                        format="DD/MM/YYYY"
                        onChange={async (value) => {
                          try {
                            await EditDateDiffusion({
                              variables: {
                                dateDiffusion: {
                                  dateEnd: value.toDate()
                                },
                                dateDiffusionId: dateDiff.id
                              }
                            });
                            message.success(t('Updated'));
                            await refetch();
                          } catch (e) {
                            showGqlErrorsInMessagePopupFromException(e);
                          }
                          form.setFieldsValue({ date: value });
                        }}
                      />
                    </Form.Item>
                  </>
                )}
              </>
            )}

            {dateDiff?.date &&
              dateDiff?.dateEnd &&
              dayjs(dateDiff?.date) > dayjs(dateDiff?.dateEnd) && (
                <div style={{ marginBottom: '14px' }}>
                  <Alert
                    type="error"
                    message="Attention, la date de fin est AVANT la date de début !"
                  />
                </div>
              )}

            <Form.Item name="eventRepeat" label={t('date.Repeat')}>
              <EventRepeatRuleEditor
                dateDiff={dateDiff}
                onRecurrenceChange={handleRecurrenceChange}
              />
            </Form.Item>

            <Divider orientation={'left'}>
              {' '}
              <SmileOutlined /> {t('Participants')}{' '}
              <Tooltip title={t('ParticipantsWillSeeThisInTheirPlanning')}>
                <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
              </Tooltip>
            </Divider>
            <DateDiffusionGroupsManager
              groupes={dateDiff?.groupes || []}
              dateDiffusionId={dateDiff.id}
              refetch={refetch}
            />

            <br />

            <br />
            <Form.Item name="updateInfos" label={t('UpdateInfos')}>
              <Input.TextArea />
            </Form.Item>

            {/* Buildings and rooms */}
            {settings?.showBuildingsAndRooms && (
              <>
                <Divider orientation={'left'}>
                  {' '}
                  <CompassOutlined /> {t('eventLocation')}
                </Divider>
                <Form.Item label={t('general.Building')}>
                  <Select
                    loading={loadingBuildings}
                    placeholder="Choisir un bâtiment"
                    onChange={async (value, o) => {
                      setBuildingId(value);
                      await EditDateDiffusion({
                        variables: {
                          dateDiffusionId: dateDiff.id,
                          dateDiffusion: {
                            buildingId: value,
                            roomId
                          }
                        }
                      });
                      await refetch();
                    }}
                    value={buildingId}
                  >
                    {allBuildings?.map((building) => (
                      <Select.Option value={building?.id} key={building?.id}>
                        {building?.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>

                {buildingId && (
                  <Form.Item label={t('Room')}>
                    <Select
                      placeholder="Choisir une salle"
                      onChange={async (value, o) => {
                        setRoomId(value);
                        await EditDateDiffusion({
                          variables: {
                            dateDiffusionId: dateDiff.id,
                            dateDiffusion: {
                              buildingId,
                              roomId: value
                            }
                          }
                        });
                        await refetch();
                      }}
                      value={roomId}
                    >
                      {allBuildings
                        ?.find((b) => b?.id === buildingId)
                        ?.rooms?.map((room) => (
                          <Select.Option value={room?.id} key={room?.id}>
                            {room?.name} - {room?.seats} {t('Seats')}
                          </Select.Option>
                        ))}
                    </Select>
                  </Form.Item>
                )}
              </>
            )}

            <Form.Item label={t('Link')} name="link">
              <Input placeholder={'https://exoteach.com/'} />
            </Form.Item>

            {settings?.showOrganizers && (
              <div style={{ textAlign: 'left' }}>
                <Divider orientation={'left'}>
                  <TeamOutlined /> {t('Organizers')}
                </Divider>
                <Space>
                  <Button
                    style={{ height: 'auto' }}
                    onClick={() => setShowOrganizerSearch((v) => !v)}
                    icon={showOrganizerSearch ? null : <PlusOutlined />}
                  >
                    {showOrganizerSearch ? 'Annuler' : 'Ajouter un organisateur'}
                  </Button>
                </Space>

                {organizers?.map((userId, key) => (
                  <div key={key} style={{ margin: 20, textAlign: 'left', fontWeight: '700' }}>
                    <ExoUserLight id={userId} />
                    <Tooltip title={t('general.Remove')}>
                      <MinusCircleOutlined
                        className="dynamic-delete-button"
                        onClick={() => onRemoveOrganizer(userId)}
                      />
                    </Tooltip>
                  </div>
                ))}

                <br />

                {showOrganizerSearch && (
                  <div style={{ marginBottom: '20px', textAlign: 'center' }}>
                    <SearchUser
                      onSelectUser={async (value, option) => {
                        // setOrganizers([...organizers, option?.key])
                        await onAddOrganizer(option?.key);
                      }}
                    />
                  </div>
                )}
              </div>
            )}

            {/* End form */}
            <Form.Item style={{ marginTop: '15px' }}>
              <Button
                style={{ height: 'auto' }}
                onClick={() => form.submit()}
                type="primary"
                loading={loadingMut}
              >
                {t('general.save')}
              </Button>
            </Form.Item>

            <Popconfirm
              title={t('SureOfDeletion')}
              onConfirm={async () => {
                try {
                  await RemoveDateDiffusion({
                    variables: {
                      dateDiffusionId: dateDiff.id
                    }
                  });
                  message.success(t('DeletedWithSuccess'));
                  await refetch();
                } catch (e) {
                  showGqlErrorsInMessagePopupFromException(e);
                }
              }}
              okText={t('general.yes')}
              cancelText={t('general.no')}
            >
              <Button danger size="small" type="text">
                {t('DeleteThisDate')}
              </Button>
            </Popconfirm>
          </Form>
        </Card>
      </Panel>
    </Collapse>
  );
};

// TODO move to a dedicated file
export const EditDatesDiffusion = ({
  courId = null,
  qcmId = null,
  eventId = null,
  examSessionId = null,
  settings = {
    showOrganizers: false,
    showParticipants: false,
    showBuildingsAndRooms: false
  }
}) => {
  const { t } = useTranslation();
  const [CreateDateDiffusion] = useMutation(MUTATION_CREATE_DATE_DIFFUSION);
  const [datesDiffusionByYear, setDatesDiffusionByYear] = useState([]);
  const [datesDiffWithoutDate, setDatesDiffusionWithoutDate] = useState([]);
  const [allYears, setAllYears] = useState([]);
  const [tabSelection, setTabSelection] = useState(String(dayjs().year()));

  const getVariables = () => {
    if (courId) {
      return { courId };
    }
    if (qcmId) {
      return { qcmId };
    }
    if (eventId) {
      return { eventId };
    }
    if (examSessionId) {
      return { examSessionId };
    }
    return {};
  };

  const getQuery = () => {
    if (courId) {
      return QUERY_EDT_FOR_COURS;
    }
    if (qcmId) {
      return QUERY_EDT_FOR_QCM;
    }
    if (eventId) {
      return QUERY_EDT_FOR_EVENT;
    }
    if (examSessionId) {
      return QUERY_EDT_FOR_EXAM;
    }
    return undefined;
  };

  const { loading, data, error, refetch } = useQuery(getQuery(), {
    fetchPolicy: 'no-cache',
    variables: getVariables()
  });

  const datesDiffusion = data?.dateDiffusions;

  const add = async () => {
    try {
      await CreateDateDiffusion({
        variables: {
          dateDiffusion: {
            ...getVariables(),
            date: dayjs().hour(12).minute(0).toDate(),
            dateEnd: dayjs().hour(12).minute(0).add(1, 'hour').toDate()
          }
        }
      });
      await refetch();
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  useEffect(() => {
    //  filter dates diffusion by year
    const dates = [];
    const years = [];
    if (datesDiffusion) {
      setDatesDiffusionWithoutDate(datesDiffusion?.filter((d) => d?.date === null));
      datesDiffusion.forEach((d) => {
        const momentDate = dayjs(d.date);
        const year = momentDate.year();
        dates.push({
          annee: year,
          ...d
        });
        if (!years.includes(year)) {
          years.push(year);
        }
      });
      setAllYears(years);
    }

    const datesDiffByYear = groupByKey(dates, 'annee')
      .map((q, k) => {
        return {
          annee: k,
          datesDiffusion: q
        };
      })
      .reverse();
    setDatesDiffusionByYear(datesDiffByYear);
  }, [datesDiffusion]);

  const renderDateDiffEdition = (dateDiff) => (
    <DateDiffusionEdition
      settings={settings}
      dateDiff={dateDiff}
      refetch={refetch}
      examSessionId={examSessionId}
      key={dateDiff?.id}
    />
  );

  const datesDiff = (year) => {
    const datesDiffFiltered = datesDiffusionByYear?.find((d) => String(d?.annee) === String(year));
    return datesDiffFiltered?.datesDiffusion?.map((dateDiff) => renderDateDiffEdition(dateDiff));
  };

  if (loading) {
    return <SpinnerCentered />;
  }
  return (
    <Form>
      <Form.Item>
        <Button type="primary" icon={<PlusCircleOutlined />} onClick={add} block>
          {t('AddDate')}
        </Button>
      </Form.Item>

      {datesDiffusionByYear?.length > 0 && allYears?.length > 0 && (
        <Tabs
          onChange={(activeKey) => {
            setTabSelection(activeKey);
          }}
          activeKey={tabSelection}
          type="card"
        >
          {allYears?.map((year, k) => (
            <Tabs.TabPane tab={Number.isNaN(year) ? 'Non-défini' : `${year}`} key={year}>
              {datesDiff(year)}
            </Tabs.TabPane>
          ))}
        </Tabs>
      )}

      {datesDiffWithoutDate?.map((d) => renderDateDiffEdition(d))}

      <br />
    </Form>
  );
};

export const EditInformationsModal = ({
  closeModalHandler,
  isVisible,
  id,
  date,
  workTime,
  duration,
  updateInfos,
  version,
  difficulty,
  refetch,
  cours
}) => {
  const { t, i18n } = useTranslation();
  const { enabledLanguages } = useContext(GlobalContext);

  const [formGeneral] = Form.useForm();
  const [formModule] = Form.useForm();
  const [Mutation, { loading, data, error }] = useMutation(UPDATE_COURS);
  const [fileImage, setFileImage] = useState(null);
  const dataTypeQcm = useQuery(QUERY_ALL_QCM_TYPE, { fetchPolicy: 'cache-and-network' });

  // Query cours groups
  const {
    data: dataCoursGroups,
    loading: loadingUeGroups,
    error: errorUeGroups,
    refetch: refetchCoursGroups
  } = useQuery(QUERY_COURS_GROUPS, {
    fetchPolicy: 'no-cache',
    skip: !cours?.id,
    variables: {
      id: cours?.id
    }
  });
  const coursGroups = dataCoursGroups?.cour?.groupes?.filter((g) => !g?.isIndividual);
  const coursGroupsIndividual = dataCoursGroups?.cour?.groupes?.filter((g) => g?.isIndividual);
  const [addGroupToCours, addGroupData] = useMutation(ADD_GROUP_COURS);
  const [removeGroupToCours, removeGroupData] = useMutation(REMOVE_GROUP_COURS);

  const handleFinish = async (formData) => {
    try {
      if (formData && formData.duration) {
        // eslint-disable-next-line no-param-reassign
        formData.duration = parseInt(formData.duration, 10);
      }
      let cours;
      if (fileImage) {
        cours = { ...formData, image: fileImage };
      } else {
        cours = { ...formData };
      }
      await Mutation({ variables: { id, cours } });
      message.success(t('Updated'));
      // await closeModalHandler()
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const handleFinishModules = async (formData) => {
    try {
      const watermarkCheck = await verifyWatermarkModuleForm(formData);
      if (watermarkCheck != null) {
        message.error(t(watermarkCheck));
        throw new Error('watermarkCheck failed');
      }

      const input = { settings: formData };
      await Mutation({ variables: { id, cours: input } });
      message.success(t('Updated'));
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const [isReviewEnabled, setIsReviewEnabled] = useState(cours?.isReviewEnabled || false);
  const notificationMessage = { courseId: id, route: `/cours/${id}` };
  const { name, text } = cours || {};

  return (
    <Drawer
      title={`${name} - ${text}`}
      open={isVisible}
      onClose={closeModalHandler}
      closable
      placement="bottom"
      height={isMobile ? 'calc(100% - 109px)' : 'calc(100% - 64px)'}
      styles={{ body: { padding: '0 24px' } }}
    >
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab={t('tab.Supports')} key="1">
          <EditCours modalType="UPDATE" refetch={refetch} loading={loading} id={id} cours={cours} />
        </Tabs.TabPane>
        <Tabs.TabPane tab={t('tab.General')} key="2">
          {/* Show small error(s) if needed */}
          <SmallErrorsAlert error={error} loading={loading} />
          <Form layout="vertical" onFinish={handleFinish} form={formGeneral} initialValues={cours}>
            <Tabs defaultActiveKey={i18n.language}>
              {enabledLanguages &&
                enabledLanguages?.map((lang) => (
                  <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                    <Form.Item
                      name={tr('name', lang)}
                      label={t('CourseTitle')}
                      rules={[{ min: 1, message: `Veuillez entrer le nom` }]}
                    >
                      <Input type="text" placeholder={t('ExempleCourseTitle')} />
                    </Form.Item>
                    <Form.Item name={tr('text', lang)} label={t('Description')}>
                      <Input type="text" placeholder=" " />
                    </Form.Item>
                  </Tabs.TabPane>
                ))}
            </Tabs>

            <Form.Item name={'type'} label={'Type'}>
              <Radio.Group type="solid">
                <Radio.Button value={'step'}>Page</Radio.Button>
                <Radio.Button value={null}>{t('general.Lesson')}</Radio.Button>
              </Radio.Group>
            </Form.Item>

            <Form.Item name="duration" label={t('CourseLength')}>
              <Input type="number" placeholder="60, 90, 120..." />
            </Form.Item>

            {/*<Form.Item
              name="workTime"
              label={t('WorkTimeAdvised')}
            >
              <Input type="text" placeholder="7h"/>
            </Form.Item>*/}

            <Form.Item name="difficulty" label={t('CourseDifficulty')}>
              <Slider
                min={0}
                max={5}
                step={0.1}
                tooltip={{
                  formatter: (value) => `${value}/5`
                }}
              />
            </Form.Item>
            <Form.Item name="isReviewEnabled" valuePropName="checked">
              <Checkbox onChange={(e) => setIsReviewEnabled(e.target.checked)}>
                {t('Review.EnableCoursReview')}{' '}
              </Checkbox>
            </Form.Item>

            {/*
            <Form.Item
              name="isMarkVisible"
              label={t('trad isMarkVisible')}
              valuePropName="checked"
            >
              <Checkbox disabled={!isReviewEnabled}>{t('trad isMarkVisible?')} </Checkbox>
            </Form.Item>
            */}
            <Form.Item
              name="isFeedbackVisible"
              valuePropName="checked"
              style={{ marginLeft: '20px' }}
            >
              <Checkbox disabled={!isReviewEnabled}>{t('Review.EnablePublicReview')}</Checkbox>
            </Form.Item>

            <Form.Item name="gptPrecisionPrompt" label={t('PromptPrecision')}>
              <Input.TextArea rows={5} />
            </Form.Item>

            {/* NEWS - Affiche page d'accueil */}
            <Divider orientation={'left'}>{t('News')}</Divider>
            <Form.Item
              name="isEnAvant"
              valuePropName={'checked'}
              help={
                "Affichera un aperçu de cette page sur la page d'accueil pour les groupes concernés"
              }
            >
              <Checkbox>{t('ShowInNews')}</Checkbox>
            </Form.Item>
            <br />

            <Form.Item>
              <>
                <Button onClick={() => formGeneral.submit()} type="primary" loading={loading}>
                  {t('UpdateGeneral')}
                </Button>
              </>
            </Form.Item>
          </Form>
        </Tabs.TabPane>
        <Tabs.TabPane tab={t('tab.Modules')} key="3">
          <Form
            layout="vertical"
            onFinish={handleFinishModules}
            form={formModule}
            initialValues={cours?.settings}
          >
            <h2>{t('EnableOrDisableCourseModules')}</h2>
            <Divider>{t('RightPart')}</Divider>
            <Form.Item valuePropName="checked" name="showNotions">
              <Checkbox>{t('NotionModule')}</Checkbox>
            </Form.Item>

            <Form.Item valuePropName="checked" name="showQuizz">
              <Checkbox>{t('ExerciceSerieOfCourse')}</Checkbox>
            </Form.Item>
            <Form.Item label={t('TypesToShow')}>
              <TypeQcmCoursSeriesManager
                coursModuleType={CoursTypesQcmSettings_MODULE_TYPES.qcmSeries}
                dataTypeQcm={dataTypeQcm} // All types
                typeQcmSettings={cours?.typeQcmSettings}
                coursId={cours?.id}
              />
            </Form.Item>

            <Divider />

            <Form.Item valuePropName="checked" name="showRevision">
              <Checkbox>{t('ExplanationModuleRevision')}</Checkbox>
            </Form.Item>
            <Form.Item label={t('TypesToShow')}>
              <TypeQcmCoursSeriesManager
                coursModuleType={CoursTypesQcmSettings_MODULE_TYPES.revision}
                dataTypeQcm={dataTypeQcm} // All types
                typeQcmSettings={cours?.typeQcmSettings}
                coursId={cours?.id}
              />
            </Form.Item>

            <Form.Item valuePropName="checked" name="showAnnales">
              <Checkbox>{t('ModuleQuickAccessExplanation')}</Checkbox>
            </Form.Item>
            <Form.Item label={t('TypesToShow')}>
              <TypeQcmCoursSeriesManager
                coursModuleType={CoursTypesQcmSettings_MODULE_TYPES.training}
                dataTypeQcm={dataTypeQcm} // All types
                typeQcmSettings={cours?.typeQcmSettings}
                coursId={cours?.id}
              />
            </Form.Item>

            <Form.Item valuePropName="checked" name="showEvents">
              <Checkbox>{t('ModuleEventExplanation')}</Checkbox>
            </Form.Item>
            <Form.Item label={t('TypesToShow')}>
              <TypeQcmCoursSeriesManager
                coursModuleType={CoursTypesQcmSettings_MODULE_TYPES.events}
                dataTypeQcm={dataTypeQcm}
                typeQcmSettings={cours?.typeQcmSettings}
                coursId={cours?.id}
              />
            </Form.Item>

            <Form.Item valuePropName="checked" name="showSchemas">
              <Checkbox>{t('ModuleSchemaTraining')}</Checkbox>
            </Form.Item>
            <Form.Item label={t('TypesToShow')}>
              <TypeQcmCoursSeriesManager
                coursModuleType={CoursTypesQcmSettings_MODULE_TYPES.schemas}
                dataTypeQcm={dataTypeQcm}
                typeQcmSettings={cours?.typeQcmSettings}
                coursId={cours?.id}
              />
            </Form.Item>

            <Divider>{t('BottomPart')}</Divider>
            <Form.Item valuePropName="checked" name="showDiscussions">
              <Checkbox>{t('DiscussionModuleExplanation')}</Checkbox>
            </Form.Item>
            <Form.Item valuePropName="checked" name="enableAIPdfAccess">
              <Checkbox>{t('AllowAIToAccessCourse')}</Checkbox>
            </Form.Item>
            <Divider>
              <LockOutlined /> {t('general.Security')}
            </Divider>
            <AgnosticWatermarkModalModule
              agnosticIdArray={[cours?.id]}
              fileString={cours?.pdf}
              settings={cours?.settings}
              form={formModule}
              type={supportedWatermarkPdfTypes.WATERMARK_COURS}
            />

            <Form.Item>
              <Button onClick={() => formModule.submit()} type="primary" loading={loading}>
                {t('UpdateModule')}
              </Button>
            </Form.Item>
          </Form>
        </Tabs.TabPane>
        <Tabs.TabPane tab={t('tab.Dates')} key="4">
          <EditDatesDiffusion courId={id} />
        </Tabs.TabPane>

        {/*<Tabs.TabPane tab="🌀 Notions" key="5">
          <Tabs defaultActiveKey="1" type="card" style={{ paddingTop: 0 }}>
            <Tabs.TabPane tab={t('ManuallyAdded')} key="1">
              <EditNotionsLink type={NotionTarget.COURS} typeId={id} autoAdded={false}/>
            </Tabs.TabPane>
            <Tabs.TabPane tab={t('AutomaticallyAdded')} key="2">
              <EditNotionsLink type={NotionTarget.COURS} typeId={id} autoAdded/>
            </Tabs.TabPane>
          </Tabs>
        </Tabs.TabPane>*/}

        <Tabs.TabPane tab={t('tab.Activity')} key="6">
          <CourseActivityLogs coursId={id} />
        </Tabs.TabPane>

        <Tabs.TabPane tab={t('tab.Notifications')} key="7">
          <NotificationForm
            GQLFunc={NOTIFY_COURSE_UPDATE}
            message={notificationMessage}
            title={t('courseUpdated')}
            body={name && text ? `${name} : ${text}` : `${name}`}
          />
        </Tabs.TabPane>

        <Tabs.TabPane tab={t('Permissions')} key="8">
          <p>
            <b>Groupes ayant accès à ce cours : </b>
          </p>
          <br />

          <div>
            <AbstractGroupsManager
              //entityName={''}
              entityId={cours?.id}
              groupes={coursGroups}
              addGroupMutation={ADD_GROUP_COURS}
              removeGroupMutation={REMOVE_GROUP_COURS}
              groupParameterName="groupId"
              entityParameterName="coursId"
            />

            <br />
            <Divider />
            <IndividualPermissionsManager
              individualGroups={coursGroupsIndividual}
              onAdd={async (individualGroupId) => {
                await addGroupToCours({
                  variables: {
                    coursId: cours?.id,
                    groupId: individualGroupId
                  }
                });
                await refetchCoursGroups();
              }}
              onRemove={async (groupId) => {
                await removeGroupToCours({
                  variables: {
                    coursId: cours?.id,
                    groupId
                  }
                });
                await refetchCoursGroups();
              }}
            />
          </div>
        </Tabs.TabPane>
      </Tabs>
    </Drawer>
  );
};
