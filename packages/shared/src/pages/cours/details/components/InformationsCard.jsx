import { ExoDifficulty } from '@/shared/components/ExoDifficulty.jsx'
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx'
import { CalendarTwoTone } from '@ant-design/icons'
import { Collapse, Tag, Timeline } from "antd";
import React from 'react'
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

export const InformationsCard = (
  {
    cours,
    loading,
    refetch,
    id,
    data,
    error,
    datesDiffusion,
    loadingDatesDiff,
  }) => {
  const { t } = useTranslation();

  const { difficulty, duration, date, version, updateInfos, updatedAt, workTime } = cours

  const renderDatesDiffusion = (
    <>
      {datesDiffusion?.map(dateDiff => (
        <h4 style={{ color: 'grey' }}>
          <CalendarTwoTone/> {t('Dates')} : {dayjs(dateDiff.date).format('DD/MM/YYYY à HH:SS')}
        </h4>
      ))}
    </>
  )

  const hasDateDiffusion = datesDiffusion && datesDiffusion?.length > 0
  const { Panel } = Collapse;


  return (
    <>
      {difficulty && (
        <div style={{textAlign:'center'}}>
          <ExoDifficulty difficulty={difficulty}/>
        </div>
      )}
      {hasDateDiffusion && (
        <>
          {/*<div style={{ textAlign: 'center', fontWeight: 'bold' }}>
            <CalendarTwoTone/> {t('DateAndUpdate')}
          </div>*/}
          <Collapse bordered={false}>
            <Panel
              header= {datesDiffusion?.slice(0, 1).map(dd => (
                <>
                  <Tag>
                    {dayjs(datesDiffusion[datesDiffusion.length - 1].date).format('DD/MM/YYYY')}
                  </Tag>
                  {datesDiffusion[datesDiffusion.length - 1].updateInfos}
                </>
              ))}
              key="1">
              {loadingDatesDiff && <SpinnerCentered/>}
              <Timeline style={{ height: 'auto', paddingTop: '10px' }} mode="left" reverse>
                {datesDiffusion && datesDiffusion.length > 0 && (
                  datesDiffusion.slice(0, datesDiffusion.length - 1).map(dd => (
                    <Timeline.Item>
                      <Tag>
                        {dayjs(dd.date).format('DD/MM/YYYY')}
                      </Tag>
                      {dd.updateInfos}
                    </Timeline.Item>
                  ))
                )}
              </Timeline>
            </Panel>
          </Collapse>
        </>
      )}

    </>
  )
}
