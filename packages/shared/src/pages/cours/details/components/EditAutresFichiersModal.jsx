import { Button, Form, Input, message, Modal, Tabs, Upload } from 'antd'
import { useMutation } from '@apollo/client'
import React, { useState } from 'react'
import { SmallErrorsAlert } from '@/shared/components/ErrorResult'
import { CREATE_FILE, DELETE_FILE, UPDATE_FILE } from '@/shared/graphql/cours'
import UploadOutlined from '@ant-design/icons/lib/icons/UploadOutlined'
import LoadingOutlined from '@ant-design/icons/lib/icons/LoadingOutlined'
import { FileListToDownload } from '@/shared/pages/cours/details/components/FileListToDownload'
import { useTranslation } from 'react-i18next';

const { TabPane } = Tabs

const CreateOneFile = ({ coursId, notionId, refetch, closeModalHandler, dataType = 'file' }) => {
  const {t} = useTranslation();
  const [form] = Form.useForm()
  const [createFileMutation, { loading, data, error }] = useMutation(CREATE_FILE)
  const [file, setFile] = useState(null)
  const isVideo = dataType === 'video'
  const isImage = dataType === 'image'
  const isLink = dataType === 'link'

  const handleFinish = async formData => {
    try {
      let newFile
      if (notionId) {
        newFile = { ...formData, file, notionId } // notionId needed!
      } else {
        newFile = { ...formData, file, courId: coursId } // courId needed!
      }
      if (dataType === 'video') {
        newFile.type = 'video'
      } else if (dataType === 'image') {
        newFile.type = 'image'
      } else if (dataType === 'link') {
        newFile.type = 'link'
      }
      message.loading({ content: 'Mise à jour fichier et actualisation...', key: 'update' })
      await createFileMutation({ variables: { file: newFile } })
      await refetch()
      message.success({ content: 'Terminé', key: 'update', duration: 2 })
      form.resetFields()
      setFile(null)
    } catch (e) {
      console.error(e)
      message.error({ content: 'Erreur', key: 'update', duration: 2 })
    }
  }

  let mainLabel
  if (isVideo) {
    mainLabel = 'Titre de la vidéo'
  } else if (isImage) {
    mainLabel = 'Titre de l\'image'
  } else if (isLink) {
    mainLabel = 'Titre du lien'
  } else {
    mainLabel = 'Nom du fichier'
  }

  let labelLink
  if (isVideo) {
    labelLink = 'Lien vers la vidéo'
  } else if (isLink) {
    labelLink = 'Lien'
  }
  return (
    <>
      <SmallErrorsAlert
        error={error}
        loading={loading}
      />
      <Form
        layout="vertical"
        onFinish={handleFinish}
        form={form}
      >
        <Form.Item
          label={mainLabel}
          name="name"
          rules={[{ required: true, message: 'Champ requis', type: 'string' }]}
        >
          <Input type="text" placeholder={t('Description')}/>
        </Form.Item>

        {(isVideo || isLink) && (
          <Form.Item
            label={labelLink}
            name="externalLink"
          >
            <Input type="text" placeholder=""/>
          </Form.Item>
        )}

        {(!isVideo && !isLink) && (
          <Form.Item
            label="Fichier"
          >
            <Upload.Dragger
              name="file"
              showUploadList={{
                showPreviewIcon: false,
                showDownloadIcon: false,
                showRemoveIcon: false,
              }}
              beforeUpload={uploadFile => {
                setFile(uploadFile)
                return false
              }}
              fileList={file ? [file] : []}
              onRemove={() => setFile('')}
            >
              <div>
                {loading ? <LoadingOutlined/> : <UploadOutlined/>}
                <div className="ant-upload-text">Upload</div>
              </div>
            </Upload.Dragger>
          </Form.Item>
        )}

        <Form.Item>
          <>
            <Button htmlType="submit" type="primary" loading={loading}>
              {t('general.add')}
            </Button>
            <Button style={{ marginLeft: 10 }} onClick={() => closeModalHandler()}>
              {t('Cancel')}
            </Button>
          </>
        </Form.Item>
      </Form>
    </>
  )
}

const EditOneFile = ({ previousFile, goBack, refetch, dataType = 'file' }) => {
  const [form] = Form.useForm()
  const [file, setFile] = useState(null)
  const [updateFileMutation, { loading, data, error }] = useMutation(UPDATE_FILE)

  const isVideo = dataType === 'video'
  const isImage = dataType === 'image'
  const isLink = dataType === 'link'

  let labelLink
  if (isVideo) {
    labelLink = 'Lien vers la vidéo'
  } else if (isLink) {
    labelLink = 'Lien'
  }

  const handleFinish = async formData => {
    try {
      let updatedFile
      if (file) {
        updatedFile = { ...formData, file }
      } else {
        updatedFile = { ...formData }
      }
      if (isVideo) {
        updatedFile.type = 'video'
      } else if (isImage) {
        updatedFile.type = 'image'
      } else if (isLink) {
        updatedFile.type = 'link'
      }
      message.loading({ content: 'Mise à jour fichier et actualisation...', key: 'update' })
      await updateFileMutation({ variables: { id: previousFile.id, file: updatedFile } })
      await refetch()
      message.success({ content: 'Terminé!', key: 'update', duration: 2 })
      await goBack()

    } catch (e) {
      console.error(e)
      message.error('Erreur serveur, veuillez réessayer')
    }
  }

  const defaultFileList = [{
    uid: previousFile.id,
    name: previousFile.name,
    url: previousFile.file,
    status: previousFile.file ? 'done' : 'error',
  }]

  return (
    <>
      <SmallErrorsAlert error={error} loading={loading}/>
      <Form
        layout="vertical"
        onFinish={handleFinish}
        form={form}
        initialValues={{
          name: previousFile.name,
          externalLink: isVideo ? previousFile.externalLink : '',
        }}
      >
        <Form.Item
          label={isVideo ? 'Titre de la vidéo' : 'Nom du fichier'}
          name="name"
        >
          <Input type="text" placeholder={t('Title')}/>
        </Form.Item>


        {(isVideo || isLink) && (
          <Form.Item
            label={labelLink}
            name="externalLink"
          >
            <Input type="text" placeholder=""/>
          </Form.Item>
        )}

        {(!isVideo && !isLink) && (
          <Form.Item
            label="Fichier"
            extra={!file && `Actuel: ${previousFile.file}`}
          >
            <Upload.Dragger
              name="file"
              listType="picture"
              defaultFileList={defaultFileList}
              showUploadList={{
                showPreviewIcon: false,
                showDownloadIcon: false,
                showRemoveIcon: true,
              }}
              beforeUpload={uploadFile => {
                setFile(uploadFile)
                return false
              }}
              fileList={file ? [file] : []}
              onRemove={() => setFile('')}
            >
              <div>
                {loading ? <LoadingOutlined/> : <UploadOutlined/>}
                <div className="ant-upload-text">Upload</div>
              </div>
            </Upload.Dragger>
          </Form.Item>
        )}

        <Form.Item>
          <>
            <Button htmlType="submit" type="primary" loading={loading}>
              {t('Update')}
            </Button>
            <Button style={{ marginLeft: 10 }} onClick={() => goBack()}>{t('Cancel')}</Button>
          </>
        </Form.Item>
      </Form>

    </>
  )
}

// View and delete files (files list)
export const EditAutresFichiersModal = (
  {
    closeModalHandler,
    isVisible,
    coursId,
    notionId,
    files,
    refetch,
    dataType = 'file',
  }) => {
  const {t} = useTranslation();
  const [deleteMutation,
    { loading: loadingDelete, data: dataDelete, error: errorDelete }] = useMutation(DELETE_FILE)
  const [fileSelected, setFileSelected] = useState(null)

  const onEditFile = (idToFind) => {
    setFileSelected(files.find(file => file.id === idToFind))
  }

  const onDeleteFile = async (e, idToDelete) => {
    try {
      message.loading({ content: 'Suppression fichier et actualisation...', key: 'update' })
      await deleteMutation({ variables: { id: idToDelete } })
      await refetch()
      message.success({ content: 'Suppression fichier et actualisation...', key: 'update', duration: 2 })
    } catch (e) {
      message.error('Erreur suppression fichier (voir console)')
      console.error(e)
    }
  }

  const isVideo = dataType === 'video'
  const isImage = dataType === 'image'
  const isLink = dataType === 'link'

  let modalTitle = isVideo ? 'Editer vidéos' : 'Editer autres fichiers'
  if (isImage) {
    modalTitle = 'Éditer images'
  }
  if (isLink) {
    modalTitle = 'Éditer liens'
  }
  return (
    (<Modal
      title={modalTitle}
      open={isVisible}
      onCancel={closeModalHandler}
      footer={[
        <Button key="back" onClick={closeModalHandler}>
          {t('Cancel')}
        </Button>,
      ]}
      closable
      confirmLoading={false}
      bodyStyle={{ paddingTop: '0' /* Because of the tabs */ }}
    >
      <Tabs defaultActiveKey="1">
        <TabPane tab={t('Edit')} key="1">
          {/* File list */}
          {!fileSelected && (
            <FileListToDownload
              data={files}
              isDeletable
              isEditable
              onEdit={onEditFile}
              onDelete={onDeleteFile}
            />
          )}

          {/* Edit selection */}
          {fileSelected && (
            <>
              <EditOneFile
                previousFile={fileSelected}
                goBack={() => setFileSelected(null)}
                refetch={refetch}
                dataType={dataType}
              />
            </>
          )}
        </TabPane>
        <TabPane tab="Créer" key="2">
          <CreateOneFile
            refetch={refetch}
            closeModalHandler={closeModalHandler}
            coursId={coursId}
            notionId={notionId}
            dataType={dataType}
          />
        </TabPane>
      </Tabs>
    </Modal>)
  );
}
