import { Button, Popover, Tag } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Group, User } from 'lucide-react';

export default function CoursGroupsInfos({ cours }) {
  const { t } = useTranslation();
  const coursGroups = cours?.groupes?.filter((g) => !g?.isIndividual);
  const coursGroupsIndividual = cours?.groupes?.filter((g) => g?.isIndividual);

  return (
    <>
      <Popover
        //style={{ width: 300 }}
        title={t('CourseGroupsAccess', { groupsCount: coursGroups?.length || 0 })}
        content={
          <div style={{ maxHeight: '200px', overflowY: 'auto', width: '300px' }}>
            <div>
              {coursGroups?.map((g, i) => (
                <Tag key={i} color="blue">
                  {g?.name}
                </Tag>
              ))}
            </div>
            <div style={{ marginTop: 12 }}>
              {t('PlusUsersCount', { usersCount: coursGroupsIndividual?.length })}
            </div>
          </div>
        }
      >
        <Button variant="text" color="primary">
          {coursGroups?.length} <Group fontSize={16} /> {coursGroupsIndividual?.length}{' '}
          <User fontSize={16} />
        </Button>
      </Popover>
    </>
  );
}
