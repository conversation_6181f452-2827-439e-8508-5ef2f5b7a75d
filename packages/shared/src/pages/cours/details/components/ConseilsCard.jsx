import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import { EditOutlined } from '@ant-design/icons';
import { Divider } from 'antd';
import React, { useState } from 'react';
import { displayDirectHtml } from '@/shared/utils/utils';
import { isAdmin } from '@/shared/utils/authority';
import { EditConseils } from '@/shared/pages/cours/details/components/EditConseils';
import 'react-quill-new/dist/quill.snow.css';
import { useTranslation } from 'react-i18next'; // ES6

export const ConseilCard = ({ loading, error, data, id, refetch }) => {
  const { t } = useTranslation();
  const [editVisible, setEditVisible] = useState(false);
  const isDoneLoading = () => !loading && !error && data && data.cour;
  const canEdit = isAdmin();
  const isConseilsVisible = () => (data.cour && data.cour.tips !== null) || canEdit;

  if (isConseilsVisible()) {
    return (
      <>
        <Divider>
          {t('general.Advices')}{' '}
          {canEdit && (
            <a onClick={() => setEditVisible(true)}>
              <EditOutlined />
            </a>
          )}
        </Divider>
        <br />
        &nbsp;
        {isDoneLoading() && (
          <>
            {data.cour.tips && !editVisible && <RenderQuillHtml>{data.cour.tips}</RenderQuillHtml>}

            {canEdit && editVisible && (
              <EditConseils
                tips={data.cour.tips}
                closeModalHandler={() => {
                  setEditVisible(false);
                }}
                id={id}
                refetch={refetch}
                isVisible={editVisible}
              />
            )}
          </>
        )}
      </>
    );
  }
  return <></>;
};
