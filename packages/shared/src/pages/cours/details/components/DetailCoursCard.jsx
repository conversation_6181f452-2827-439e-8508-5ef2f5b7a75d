import EmbeddedVideo from '@/shared/components/Video/EmbeddedVideo.jsx';
import { QUERY_COURS_DATES_DIFFUSION, QUERY_COURS_VIDEOS } from '@/shared/graphql/cours.js';
import { QUERY_BLOCKS_IN_COURS_ENRICHI } from '@/shared/graphql/formations.js';
import useMediaQuery from '@/shared/hooks/useMediaQuery.jsx';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { PdfPreview } from '@/shared/pages/cours/components/PdfPreview.jsx';
import BlocksInCoursEnrichi from '@/shared/pages/cours/details/components/CoursEnrichi/BlocksInCoursEnrichi.jsx';
import SommaireCoursEnrichi from '@/shared/pages/cours/details/components/CoursEnrichi/SommaireCoursEnrichi.jsx';
import CourseSupportsManager from '@/shared/pages/cours/details/components/CourseSupportsManager.jsx';
import { InformationsCard } from '@/shared/pages/cours/details/components/InformationsCard.jsx';
import AdminCoursStepButton from '@/shared/pages/cours/formation/Admin/Modules/Cours/AdminCoursStepButton';
import { FormationContextProvider } from '@/shared/pages/formations/context/FormationContext.jsx';
import { CoursType as CourseType } from '@/shared/services/cours.js';
import { tr } from '@/shared/services/translate.js';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { Button, Collapse } from 'antd';
import { ArrowRightOutlined, FileTwoTone } from '@ant-design/icons';
import React, { useContext, useEffect, useState } from 'react';
import { downloadFile, FILE_TYPE, openDownloadedFile } from '@/shared/services/file';
import { router } from 'umi';
import { useTranslation } from 'react-i18next';
import ReviewComponent from '@/shared/pages/cours/components/ReviewComponent';

const { Panel } = Collapse;

//TODO refactor props
export const DetailCoursCard = ({
  id, // id du cours actuel
  loading,
  refetch,
  cours, // Cours à afficher et modifier
  data,
  error,
  coursSupports,
  refetchSecondarySupports,
  isEditing,
  setIsEditing
}) => {
  const { t } = useTranslation();
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [pdfBlobUrl, setPdfBlobUrl] = useState(null);

  const { loading: loadingDateDiff, data: dataDateDiffusion } = useQuery(
    QUERY_COURS_DATES_DIFFUSION,
    {
      variables: { id: cours?.id },
      fetchPolicy: 'cache-and-network'
    }
  );
  const dateDiffs = dataDateDiffusion?.datesDiffusionInCour;

  const { loading: loadingVideo, data: dataVideo } = useQuery(QUERY_COURS_VIDEOS, {
    variables: { id: cours?.id },
    fetchPolicy: 'cache-and-network'
  });

  const pdf = cours?.pdf;

  useEffect(() => {
    if (pdfBlobUrl) {
      openDownloadedFile(pdfBlobUrl);
    } else {
      setPdfBlobUrl(null);
    }
  }, [pdfBlobUrl]);

  const onDownloadPdf = async (e) => {
    setDownloadLoading(true);
    await downloadFile(FILE_TYPE.COURS, pdf);
    setDownloadLoading(false);
  };

  // TODO WIP use reader page
  const onSeePDF = async (e) => {
    router.push(`/cours/${id}/reader/pdf/${btoa(unescape(encodeURIComponent(pdf)))}`);
  };

  /* LAYOUT TYPE */
  const shouldRenderPdfDownload = cours?.layout === 'pdf';
  const shouldRenderHeaderVideo = cours?.layout === 'video';
  const shouldRenderCoursEnrichi = cours?.layout === 'formation';

  const isStepInFormation = cours?.type === 'step'; // old formation
  const isModuleCoursSteps = cours?.layout === 'steps'; // new formation steps

  const {
    loading: loadingBlock,
    data: dataBlocks,
    refetch: refetchBlocks
  } = useQuery(QUERY_BLOCKS_IN_COURS_ENRICHI, {
    variables: { id: cours?.id },
    fetchPolicy: 'cache-and-network',
    skip: !shouldRenderCoursEnrichi
  });
  const coursBlock = dataBlocks?.formationBlocksInCours;

  const { nextCours } = useContext(GlobalContext);

  useMediaQuery('(min-width: 960px)');
  const onNextCours = () => {
    // todo mutation finish formation si fini par exemple
    router.push(`/cours/${nextCours?.id}`);
  };

  return (
    <div style={{ margin: '5px', padding: '5px', borderRadius: '4px', maxWidth: '100vw' }}>
      {cours?.layout === 'pdf' && (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '5px'
          }}
        >
          <div id="Support principal Fichier Ou vidéo">
            {shouldRenderPdfDownload && (
              <>
                {/* PDF TYPE */}
                {cours?.customImage === null &&
                cours?.pdfPreviews &&
                cours?.pdfPreviews?.length > 0 ? (
                  <div onClick={onDownloadPdf} style={{ cursor: 'pointer' }}>
                    <PdfPreview
                      images={cours?.pdfPreviews}
                      maxHeightImage={500}
                      maxWidthImage={500}
                    />
                  </div>
                ) : (
                  <>
                    {cours?.customImage ? (
                      <div onClick={onDownloadPdf} style={{ cursor: 'pointer' }}>
                        <img
                          src={getUrlProtectedRessource(
                            GlobalConfig.get().FILES_URL + cours?.customImage
                          )}
                          style={{
                            maxHeight: `350px`,
                            maxWidth: `300px`
                          }}
                          alt="aperçu"
                        />
                      </div>
                    ) : (
                      <FileTwoTone
                        onClick={onDownloadPdf}
                        style={{ fontSize: '70px', margin: '30px' }}
                      />
                    )}
                  </>
                )}
              </>
            )}
          </div>

          <div
            id="infos générales du cours"
            style={{
              display: 'flex',
              justifyContent: 'center',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '10px',
              maxWidth: '80%',
              width: 'auto'
            }}
          >
            <InformationsCard
              refetch={refetch}
              id={cours?.id}
              data={data}
              cours={cours}
              error={error}
              datesDiffusion={dateDiffs}
              loadingDatesDiff={loadingDateDiff}
            />
            <ReviewComponent cours={cours} />
          </div>
        </div>
      )}

      {cours?.layout === 'video' && (
        <div
          style={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center'
          }}
        >
          <div style={{ height: 'auto', width: '85%', margin: 'auto' }}>
            {shouldRenderHeaderVideo && <EmbeddedVideo video={cours?.video} />}
          </div>

          <div
            id="infos générales du cours"
            style={{
              display: 'flex',
              justifyContent: 'center',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '10px',
              maxWidth: '80%',
              width: 'auto'
            }}
          >
            {/* TODO refactor props with cours=cours */}
            <InformationsCard
              refetch={refetch}
              id={cours?.id}
              data={data}
              cours={cours}
              error={error}
              datesDiffusion={dateDiffs}
              loadingDatesDiff={loadingDateDiff}
            />
            <ReviewComponent cours={cours} />
          </div>
        </div>
      )}

      {cours?.layout === 'formation' && (
        <div
          style={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center'
          }}
        >
          <div
            id="infos générales du cours"
            style={{
              display: 'flex',
              justifyContent: 'center',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '10px',
              maxWidth: '80%',
              width: 'auto'
            }}
          >
            {/* TODO refactor props with cours=cours */}
            <InformationsCard
              refetch={refetch}
              id={cours?.id}
              data={data}
              cours={cours}
              error={error}
              datesDiffusion={dateDiffs}
              loadingDatesDiff={loadingDateDiff}
            />
            <ReviewComponent cours={cours} />
          </div>
        </div>
      )}

      {/* Supports de cours (pour cours, pas pour page) */}
      <div style={{ marginBottom: '20px' }}>
        <FormationContextProvider isEditing={isEditing} setIsEditing={setIsEditing}>
          {/* list supports secondaires elements */}
          {!isStepInFormation && !isModuleCoursSteps && (
            <CourseSupportsManager
              isEditing={isEditing}
              coursSupports={coursSupports}
              refetch={refetch}
              coursId={cours?.id}
              refetchSecondarySupports={refetchSecondarySupports}
            />
          )}
        </FormationContextProvider>
      </div>

      {/* Cours à étapes, dans formation => déplacer dans edition formation */}
      {/*
      {isModuleCoursSteps && (
        <>
          <AdminCoursStepButton cours={cours} ue={cours?.ue} />
        </>
      )}
      */}

      <div style={{ height: '40px' }}>
        {nextCours && (
          <>
            <div style={{ margin: 'auto', textAlign: 'center' }}>
              <Button
                style={{ width: 'auto' }}
                type="primary"
                size="medium"
                block
                //loading={loadingFinishStep}
                onClick={onNextCours}
              >
                {t('Next')} : {nextCours?.[tr('name')] || nextCours?.name} <ArrowRightOutlined />
              </Button>
            </div>
          </>
        )}
      </div>

      {shouldRenderCoursEnrichi && (
        <div style={{ marginTop: '10px', maxWidth: '100vw' }}>
          <FormationContextProvider isEditing={isEditing} setIsEditing={setIsEditing}>
            {/* Sommaire seulement si cours enrichi, pas de sommaire pour les pages */}
            {cours?.type !== CourseType.PAGE && <SommaireCoursEnrichi coursId={cours?.id} />}
            <BlocksInCoursEnrichi refetch={refetchBlocks} blocks={coursBlock} coursId={cours?.id} />
          </FormationContextProvider>
        </div>
      )}
    </div>
  );
};
