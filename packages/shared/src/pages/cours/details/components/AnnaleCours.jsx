import { MUTATION_LAUNCH_QCM_SESSION_ANNALE, MUTATION_LAUNCH_QCM_SESSION_COURS_MODULE } from '@/shared/graphql/qcm.js';
import { CoursTypesQcmSettings_MODULE_TYPES } from '@/shared/services/cours.js';
import { useMutation } from '@apollo/client';
import { Badge, Button, Card, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';
import React from 'react';
import {
  getColoredUEButtonStyle,
  getColoredUEHeadStyle,
  showGqlErrorsInMessagePopupFromException,
} from '@/shared/utils/utils';
import { PlayCircleOutlined } from '@ant-design/icons';

export const QcmAnnaleCards = ({ queryAnnales, loading, qcms, coursId, refetch, color, color2, cours }) => {
  const { loading: loadingAnnales, data, error } = queryAnnales;
  const {t} = useTranslation();

  const exercisesCount = data?.getQuestionsAnnaleForCours?.questionsCount;

  const typesQcmSettings = cours?.typeQcmSettings?.filter(ty => ty.coursModuleType === CoursTypesQcmSettings_MODULE_TYPES.training);
  const typesSelectedForThisModule = typesQcmSettings?.map(({ typeQcm }) => typeQcm);

  const stringTypesSelectedForThisModule = typesSelectedForThisModule?.map(({ name }) => name).join(', ');

  /* LAUNCH GENERATOR SESSION */
  const [launchMcqSession, { loading: loadingSession }] = useMutation(MUTATION_LAUNCH_QCM_SESSION_COURS_MODULE, {
    variables: {
      coursId,
      includeAlreadyDone: true,
      module: CoursTypesQcmSettings_MODULE_TYPES.training,
    },
  });

  const onClickActionButton = async () => {
    try {
      const { data: dataSession } = await launchMcqSession();
      const { generateExerciseSessionForCourseModule } = dataSession;
      router.push(`/generateurqcm/do/${generateExerciseSessionForCourseModule?.qcmSessionId}`);
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  return (
    <Card
      style={{boxShadow: 'rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px'}}
      headStyle={getColoredUEHeadStyle(color, color2)}
      title={stringTypesSelectedForThisModule}
    >
      ANNALE COURS
      <div style={{ textAlign: 'center', fontSize: '13px' }}>
        {!loadingAnnales && (
          <>
            <Badge style={{ backgroundColor: '#143747', fontWeight: 'bold' }} count={exercisesCount}/> {t('general.questions')} {t('ofType')} {stringTypesSelectedForThisModule} {t('haveBeenFoundForThisCourse')}! 🎉🥳
          </>
        )}
      </div>
      <br/>

      {exercisesCount !== 0 && (
        <>
          <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column', alignContent: 'center' }}>
            <div>
              <Tooltip placement="top" title={t('DoQuestions')}>
                  <Button
                    shape="round"
                    type="primary"
                    size="large"
                    style={getColoredUEButtonStyle(color, color2)}
                    icon={<PlayCircleOutlined/>}
                    onClick={onClickActionButton}
                    loading={loadingSession}
                  >
                    {t('TestYourself')}
                  </Button>
              </Tooltip>
            </div>
            <br/>
            {/*
            <div>
              <Tooltip placement="top" title={t('ShowQuestionCorrection')}>
                <Link to={`/qcm/annaleducours/${coursId}/result`}>
                  <Button
                    shape="round"
                    type="default"
                    size="large"
                    icon={<EyeOutlined/>}
                  >
                    {t('SeeCorrection')}
                  </Button>
                </Link>
              </Tooltip>
            </div>
            */}
          </div>
        </>
      )}
    </Card>
  );
};
