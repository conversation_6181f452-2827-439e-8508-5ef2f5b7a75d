import eventIcon from '@/shared/assets/event.svg';
import { ProtectedImage } from '@/shared/components/ProtectedImage';
import { QUERY_EVENTS_COURSE_MODULE } from '@/shared/graphql/events';
import { tr } from '@/shared/services/translate';
import { getColoredUEHeadStyle } from '@/shared/utils/utils';
import { ClockCircleOutlined } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { Button, Card, Popover, Space } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';

const EventCours = ({ event }) => {
  const { t } = useTranslation();

  const imageStyle = {
    height: '100%',
    width: '100%',
    borderRadius: '50%'
  };

  const getImage = () => {
    if (event?.image) {
      return <ProtectedImage src={event?.image} alt="img evenement" style={imageStyle} />;
    }
    return <img src={eventIcon} style={imageStyle} alt="" />;
  };

  const renderDateDiff = (date) => {
    const time = date?.allDay
      ? t('AllDay')
      : `${dayjs(date.date).format('DD/MM/YYYY HH:mm')} - ${dayjs(date.dateEnd).format('DD/MM/YYYY HH:mm')} `;
    return (
      <div key={date?.id}>
        <ClockCircleOutlined /> <b>{time}</b>
      </div>
    );
  };

  const gotoEventProps = {
    onClick: () => {
      router.push(`/event/${event?.id}`);
    },
    style: { cursor: 'pointer' }
  };

  return (
    <div>
      <div
        style={{
          width: '100%',
          display: 'flex',
          // gap: '10px',
          alignContent: 'space-between',
          justifyContent: 'space-between',
          marginBottom: '5px'
        }}
      >
        {/* Groupes si prof/admin, et heure (début - fin) */}
      </div>

      <div
        style={{
          width: '100%',
          display: 'flex',
          gap: '8px',
          alignContent: 'space-between',
          // justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        {/* image */}
        <div {...gotoEventProps}>
          <div style={{ borderRadius: '6px', width: '70px', height: 'auto' }}>{getImage()}</div>
        </div>

        {/* Titre */}
        <div
          style={{
            flexGrow: 2 // Sinon si le titre est court les elements de droite sont collés
          }}
        >
          <b {...gotoEventProps}>{event?.[tr('name')] || event?.name}</b>
          <br />
          <span
            style={{
              fontSize: '12px',
              color: '#6b6b6b',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
          >
            {event?.datesDiffusion?.[0] && renderDateDiff(event?.datesDiffusion?.[0])}
            {event?.datesDiffusion?.length > 1 && (
              <>
                <Popover
                  content={
                    <Space direction={'vertical'} size={'4'}>
                      {/* Only others*/}
                      {event?.datesDiffusion?.slice(1).map((date, k) => (
                        <span key={k}>{renderDateDiff(date)}</span>
                      ))}
                    </Space>
                  }
                  trigger="click"
                >
                  <Button type="link">{event?.datesDiffusion?.length - 1} autres dates</Button>
                </Popover>
              </>
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

export default function EventsCoursModule({ color, color2, coursId, typeQcmSettings, module }) {
  const { t } = useTranslation();

  // For this module: get the types names
  const typesQcmSettingsModule = typeQcmSettings?.filter((ty) => ty.coursModuleType === module);
  const typesSelectedForThisModule = typesQcmSettingsModule?.map(({ typeQcm }) => typeQcm);
  const stringTypesSelectedForThisModule = typesSelectedForThisModule
    ?.map(({ name }) => name)
    .join(', ');

  /* Query preview */
  const { loading, data, error, refetch } = useQuery(QUERY_EVENTS_COURSE_MODULE, {
    variables: {
      coursId
    },
    skip: !coursId,
    fetchPolicy: 'cache-and-network'
  });

  const events = data?.eventsCourseModule;

  // If events is empty, we don't display the card
  if (events?.length === 0) {
    return null;
  }
  return (
    <>
      <Card
        loading={loading && !data}
        title={`${t('Events')} (${stringTypesSelectedForThisModule})`}
        style={{
          boxShadow: 'rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px'
        }}
        bodyStyle={{ padding: 12 }}
        headStyle={getColoredUEHeadStyle(color, color2)}
      >
        {events?.map((event) => (
          <div key={event?.id}>
            <EventCours event={event} />
          </div>
        ))}
      </Card>
    </>
  );
}
