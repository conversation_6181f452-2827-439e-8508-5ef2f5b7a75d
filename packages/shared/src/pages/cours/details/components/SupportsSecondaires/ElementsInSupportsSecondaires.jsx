import { SimpleMoveTransition } from '@/shared/assets/transitions/SimpleMoveTransition.jsx';
import { QUERY_ELEMENTS_IN_SUPPORT_COURS } from '@/shared/graphql/cours.js';
import FormationEditableElement from '@/shared/pages/formations/components/FormationEditableElement.jsx';
import { CreateEditFormationElementModal } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal.jsx';
import { FormationContext } from '@/shared/pages/formations/context/FormationContext.jsx';
import { ELEMENTS_TYPE } from '@/shared/services/formations.js';
import { isAdmin } from '@/shared/utils/authority.js';
import { PlusCircleTwoTone } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { Button } from 'antd';
import React, { useContext, useState } from 'react';
import { AnimatePresence } from 'framer-motion/dist/framer-motion';
import { useTranslation } from 'react-i18next';

export default function ElementsInSupportsSecondaires({ supportCategory, refetch: refetchParent }) {
  const { t } = useTranslation();
  const {
    data,
    loading,
    error,
    refetch: refetch
  } = useQuery(QUERY_ELEMENTS_IN_SUPPORT_COURS, {
    variables: {
      id: supportCategory?.id
    },
    fetchPolicy: 'cache-and-network'
  });
  const elements = data?.elementsInCoursSupport;

  const { isEditing } = useContext(FormationContext);

  /* STATE */
  const [createVisible, setCreateVisible] = useState(false);
  const canShowElementCreation = isEditing && isAdmin();

  const [position, setPosition] = useState(null);

  const canEdit = isEditing && isAdmin();

  const refetchAll = () => {
    refetchParent();
    refetch(); // elements
  };

  const elementCreationComponents = (
    <>
      {createVisible && (
        <div
          style={
            canEdit
              ? {
                  border: '1px dashed #b5b5b5',
                  borderRadius: '11px',
                  margin: 5,
                  marginBottom: '15px'
                }
              : {
                  marginBottom: '15px',
                  margin: 5
                }
          }
        >
          <div style={{ margin: '15px' }}>
            <CreateEditFormationElementModal
              isModalVisible={createVisible}
              modalType="CREATE"
              position={position}
              coursSupportId={supportCategory?.id}
              block={null}
              //coursSupportId={currentElement?.coursSupportId}
              closeModalHandler={() => {
                setCreateVisible(false);
                refetchAll();
              }}
              elementsTypesToShow={{
                [ELEMENTS_TYPE.TITLE]: true,
                [ELEMENTS_TYPE.IMAGE]: true,
                [ELEMENTS_TYPE.MCQ]: true,
                [ELEMENTS_TYPE.DO_EXERCISE]: true,
                [ELEMENTS_TYPE.LINK]: true,
                [ELEMENTS_TYPE.HTML]: true,
                [ELEMENTS_TYPE.COURS]: false,
                [ELEMENTS_TYPE.COURSE_SHORTCUT]: false,
                [ELEMENTS_TYPE.FILE]: true,
                [ELEMENTS_TYPE.RICH_TEXT]: true,
                [ELEMENTS_TYPE.VIDEO]: false, // obsolète
                [ELEMENTS_TYPE.VIDSTACK_VIDEO]: true,
                [ELEMENTS_TYPE.VIDSTACK_AUDIO]: true,
                [ELEMENTS_TYPE.DIAPO]: true,
                [ELEMENTS_TYPE.CALLOUT]: true,
                [ELEMENTS_TYPE.SCORM]:true,
              }}
            />
          </div>
        </div>
      )}
    </>
  );

  const renderFormationElementCreation = (elementPosition = null) => (
    <>
      {elementPosition === position && elementCreationComponents}

      {!createVisible && (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
          <div style={{ width: '90%' }}>
            <Button
              icon={<PlusCircleTwoTone />}
              style={{ marginTop: 10, minHeight: '50px' }}
              type="dashed"
              block
              onClick={() => {
                setPosition(elementPosition);
                setCreateVisible(true);
              }}
            >
              {t('AddElementHere')}
            </Button>
          </div>
        </div>
      )}
    </>
  );

  const renderElement = (element, previousElement, nextElement, key, columnPosition) => (
    <FormationEditableElement
      style={{ transition: 'transform .35s ease-in-out' }}
      key={key}
      element={element}
      nextElement={nextElement}
      previousElement={previousElement}
      columnPosition={columnPosition}
      canEdit={canEdit}
      refetchAll={refetchAll}
      coursSupportId={supportCategory?.id}
    />
  );

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
      <AnimatePresence mode="popLayout">
        {elements?.map((elem, k) => (
          <SimpleMoveTransition id={elem.id} key={elem.id}>
            {renderElement(elem, elements[k - 1], elements[k + 1], elem?.id)}
          </SimpleMoveTransition>
        ))}
        {canShowElementCreation && renderFormationElementCreation()}
      </AnimatePresence>
    </div>
  );
}
