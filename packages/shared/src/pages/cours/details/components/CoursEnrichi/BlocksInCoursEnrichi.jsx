import Title from '@/shared/components/Formation/Title.jsx';
import ElementsInBlocksCours from '@/shared/pages/cours/details/components/CoursEnrichi/ElementsInBlocksCours.jsx';
import { CreateEditFormationBlockModal } from '@/shared/pages/formations/components/modal/CreateEditFormationBlockModal.jsx';
import { FormationContext } from '@/shared/pages/formations/context/FormationContext.jsx';
import { getElementOrBlockStyle } from '@/shared/services/formations.js';
import { isAdmin } from '@/shared/utils/authority.js';
import { EditTwoTone, PlusCircleTwoTone } from '@ant-design/icons';
import { Button, Divider, Typography } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function ({ refetch, formationId, blocks, coursId }) {
  const { t } = useTranslation();
  const { isEditing, setIsEditing, summary, setSummary } = useContext(FormationContext);

  const [createBlockVisible, setCreateBlockVisible] = useState(false);
  const [updateVisible, setUpdateVisible] = useState(false);
  const [currentBlock, setCurrentBlock] = useState(null);
  const canEdit = isEditing && isAdmin();
  const canShowBlockCreation = isEditing && isAdmin();

  const refetchAll = () => {
    refetch();
  };

  useEffect(() => {
    let summaryCopy = [...summary];
    if (blocks) {
      summaryCopy.push(blocks);
    }
  }, [blocks]);

  const formationBlockCreation = canShowBlockCreation && (
    <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
      <div style={{ width: '90%' }}>
        <Button
          style={{ marginTop: 10, minHeight: '50px' }}
          type="dashed"
          block
          onClick={() => {
            setCreateBlockVisible(true);
          }}
        >
          <PlusCircleTwoTone style={{ fontSize: '1.1rem' }} />
          {t('AddBlock')}
        </Button>
        <CreateEditFormationBlockModal
          isModalVisible={createBlockVisible}
          modalType="CREATE"
          coursId={coursId}
          closeModalHandler={() => {
            setCreateBlockVisible(false);
            refetchAll();
          }}
        />
      </div>
    </div>
  );

  const modalBlockUpdate = (
    <>
      {updateVisible && currentBlock && (
        <CreateEditFormationBlockModal
          isModalVisible={updateVisible}
          modalType="UPDATE"
          block={currentBlock}
          coursId={coursId}
          closeModalHandler={() => {
            setUpdateVisible(false);
            refetchAll();
          }}
        />
      )}
    </>
  );

  return (
    <>
      {/* MODAL for admin */}
      {modalBlockUpdate}
      {/* Renders blocks */}
      {blocks?.map((block) => (
        <div key={block?.id} id={`fcBlock-${block.id}`} style={getElementOrBlockStyle(block)}>
          {/* BLOCK TITLE */}
          {block?.titleId ? (
            <Title
              element={block}
              title={block?.title}
              showDescription={false}
              rightComponent={
                canEdit && (
                  <Button
                    type="text"
                    onClick={() => {
                      setUpdateVisible(true);
                      setCurrentBlock(block);
                    }}
                  >
                    <EditTwoTone />
                  </Button>
                )
              }
            />
          ) : (
            <Typography.Title level={2}>{block?.name || ''}</Typography.Title>
          )}

          {/* BLOCK ELEMENTS */}
          <ElementsInBlocksCours refetch={refetch} block={block} />
          <Divider />
        </div>
      ))}

      {/* Block creation */}
      {formationBlockCreation}
    </>
  );
}
