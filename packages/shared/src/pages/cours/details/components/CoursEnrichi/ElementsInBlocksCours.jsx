import { SimpleMoveTransition } from '@/shared/assets/transitions/SimpleMoveTransition.jsx';
import { ErrorResult } from '@/shared/components/ErrorResult.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { QUERY_ELEMENTS_IN_BLOCK_FORMATION } from '@/shared/graphql/formations.js';
import FormationEditableElement from '@/shared/pages/formations/components/FormationEditableElement.jsx';
import { CreateEditFormationElementModal } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal.jsx';
import { FormationContext } from '@/shared/pages/formations/context/FormationContext.jsx';
import { BLOCK_TYPE, ELEMENTS_TYPE } from '@/shared/services/formations.js';
import { isAdmin } from '@/shared/utils/authority.js';
import { PlusCircleTwoTone } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { But<PERSON> } from 'antd';
import { AnimatePresence } from 'framer-motion/dist/framer-motion';
import React, { useContext, useEffect, useState } from 'react';
import 'react-quill-new/dist/quill.snow.css'; // ES6
import { useTranslation } from 'react-i18next';

export default function ({ refetch = null, block }) {
  const { t } = useTranslation();

  // QUERY CURRENT ELEMENT FROM STEP
  const {
    loading,
    error,
    data,
    refetch: refetchElements
  } = useQuery(QUERY_ELEMENTS_IN_BLOCK_FORMATION, {
    fetchPolicy: 'cache-and-network',
    variables: {
      id: block?.id
    },
    skip: !block?.id
  });
  const elements = data?.elementsInBlock;
  const { isEditing, setIsEditing, summary, setSummary } = useContext(FormationContext);

  useEffect(() => {
    let summaryCopy = [...summary];
    const titlesInBlock = elements?.filter((e) => e?.type === ELEMENTS_TYPE.TITLE);
    let currentBlock = summaryCopy?.find((b) => b.id === block.id);
    if (currentBlock) {
      currentBlock.titles = titlesInBlock;
      setSummary(summaryCopy);
    }
  }, [block, elements]);

  /* STATE */
  const [createVisible, setCreateVisible] = useState(false);
  const [position, setPosition] = useState(null);

  const canEdit = isEditing && isAdmin();

  const hasElements = elements?.length > 0;
  const canShowElementCreation = isEditing && isAdmin();

  const refetchAll = () => {
    refetch();
    refetchElements();
  };

  const elementCreationComponents = (
    <>
      {createVisible && (
        <div
          style={
            canEdit
              ? {
                  border: '1px dashed #b5b5b5',
                  borderRadius: '11px',
                  margin: 5,
                  marginBottom: '15px'
                }
              : {
                  marginBottom: '15px',
                  margin: 5
                }
          }
        >
          <div style={{ margin: '15px' }}>
            <CreateEditFormationElementModal
              isModalVisible={createVisible}
              modalType="CREATE"
              block={block}
              position={position}
              closeModalHandler={() => {
                setCreateVisible(false);
                refetchAll();
              }}
            />
          </div>
        </div>
      )}
    </>
  );

  const renderFormationElementCreation = (elementPosition = null) => (
    <>
      {elementPosition === position && elementCreationComponents}

      {!createVisible && (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
          <div style={{ width: '90%' }}>
            <Button
              icon={<PlusCircleTwoTone />}
              style={{ marginTop: 10, minHeight: '50px' }}
              type="dashed"
              block
              onClick={() => {
                setPosition(elementPosition);
                setCreateVisible(true);
              }}
            >
              {t('AddElementHere')}
            </Button>
          </div>
        </div>
      )}
    </>
  );

  const renderElement = (element, previousElement, nextElement, key, columnPosition) => (
    <FormationEditableElement
      style={{ transition: 'transform .35s ease-in-out' }}
      key={key}
      element={element}
      nextElement={nextElement}
      previousElement={previousElement}
      block={block}
      columnPosition={columnPosition}
      canEdit={canEdit}
      refetchAll={refetchAll}
    />
  );

  const leftElements =
    block?.type === BLOCK_TYPE.DOUBLE && elements?.filter((e) => e?.settings.position === 'left');
  const rightElements =
    block?.type === BLOCK_TYPE.DOUBLE && elements?.filter((e) => e?.settings.position === 'right');

  return (
    <div>
      {loading && <SpinnerCentered />}
      {error && <ErrorResult error={error} refetch={refetchElements} />}

      {block?.type === BLOCK_TYPE.SINGLE && (
        <AnimatePresence mode="popLayout">
          {elements?.map((elem, k) => (
            <SimpleMoveTransition id={elem.id} key={elem.id}>
              {renderElement(elem, elements[k - 1], elements[k + 1], elem?.id)}
            </SimpleMoveTransition>
          ))}
          {canShowElementCreation && renderFormationElementCreation()}
        </AnimatePresence>
      )}
      {block?.type === BLOCK_TYPE.DOUBLE && (
        <div
          // justify={'center'}
          style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}
        >
          <AnimatePresence mode="popLayout">
            <div
              style={{
                flexBasis: '50%',
                flexGrow: 1
                //minWidth: '230px'
              }}
            >
              {leftElements?.map((elem, k) => (
                <SimpleMoveTransition id={elem.id} key={elem.id}>
                  <div key={elem?.id}>
                    {renderElement(
                      elem,
                      leftElements[k - 1],
                      leftElements[k + 1],
                      elem?.id,
                      'left'
                    )}
                  </div>
                </SimpleMoveTransition>
              ))}

              {canShowElementCreation && renderFormationElementCreation('left')}
            </div>
            <div style={{ flexBasis: '50%', flexGrow: 1 }}>
              {rightElements?.map((elem, k) => (
                <SimpleMoveTransition id={elem.id} key={elem.id}>
                  <div key={elem?.id}>
                    {renderElement(
                      elem,
                      rightElements[k - 1],
                      rightElements[k + 1],
                      elem?.id,
                      'right'
                    )}
                  </div>
                </SimpleMoveTransition>
              ))}
              {canShowElementCreation && renderFormationElementCreation('right')}
            </div>
          </AnimatePresence>
        </div>
      )}

      {/* END ELEMENTS */}
    </div>
  );
}
