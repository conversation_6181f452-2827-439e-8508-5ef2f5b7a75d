import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import QuickAccessModuleList from '@/shared/components/QuickAccessModule/QuickAccessModuleList.jsx';
import { QUERY_UE_BY_ID_WITH_CHILDREN_AND_TEACHERS } from '@/shared/graphql/cours.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import Formation from '@/shared/pages/cours/formation/Formation';
import { tr } from '@/shared/services/translate.js';
import { UE_TYPES } from '@/shared/services/ues.js';
import { isAdmin } from '@/shared/utils/authority.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { isMobile } from '@/shared/utils/utils.js';
import { AppstoreOutlined, BarsOutlined, PlusOutlined } from '@ant-design/icons';
import React, { useContext, useEffect } from 'react';
import { Button, Segmented } from 'antd';
import { UEContentCards } from '@/pages/cours/components/UEContentCards';
import { useQuery } from '@apollo/client';
import CalendarTwoTone from '@ant-design/icons/lib/icons/CalendarTwoTone';
import Link from 'umi/link';
import { useTranslation } from 'react-i18next';
import CoursBreadcrumb from '@/shared/components/Cours/CoursBreadcrumb';

// used by web and mobile
/**
 * UEContent
 *
 * Display the content of a UE (can be a subject, a root folder before a subject, or a Formation)
 *
 * @param ueId
 * @returns {JSX.Element}
 * @constructor
 */
export const UEContent = ({ ueId }) => {
  const { t } = useTranslation();
  const { data, error, loading, refetch } = useQuery(QUERY_UE_BY_ID_WITH_CHILDREN_AND_TEACHERS, {
    variables: { id: ueId },
    skip: !ueId,
    fetchPolicy: 'cache-and-network'
  });

  const ue = data?.ue;

  const isFormation = ue?.type === UE_TYPES.FORMATION;

  const {
    globalBannerText,
    setGlobalBannerText,
    globalBannerSubtitle,
    setGlobalBannerSubtitle,
    setCoursId,
    setUeId,
    ueId: ueIdContext,
    coursId: coursIdContext,
    setCategoryId,
    breadCrumbImage,
    setBreadCrumbImage,
    breadCrumbImageType,
    listOrCardNavigation,
    setListOrCardNavigation
  } = useContext(GlobalContext);

  useEffect(() => {
    setUeId(ueId);

    setCategoryId(null);
    if (coursIdContext !== null) {
      setCoursId(null);
    }
    if (breadCrumbImage !== 'ue?.image') {
      setBreadCrumbImage(ue?.image);
    }
    const strGlbBnrTxt = ue?.[tr('name')] || '';
    if (globalBannerText !== strGlbBnrTxt) {
      setGlobalBannerText(strGlbBnrTxt);
    }
    const strGlbBnrSubTxt = ue?.[tr('description')] || '';
    if (globalBannerSubtitle !== strGlbBnrSubTxt) {
      setGlobalBannerSubtitle(`${ue?.[tr('description')] || ''}`);
    }
  }, [
    ueId, // quand matière/dossier change
    ueIdContext,
    ue,
    coursIdContext
    // categoryIdContext // cause bug dans navig lateral
  ]);

  const [createModuleVisible, setCreateModuleVisible] = React.useState(false);

  const getExtraHeader = () => {
    let extra = [];
    if (ue?.type === UE_TYPES.SUBJECT) {
      extra.push(
        <Link key="1" to={`/cours/ue/${ueId}/planning`} style={{ margin: 5 }}>
          <Button size="large" icon={<CalendarTwoTone />}>
            {t('Planning')}
          </Button>
        </Link>
      );
    }
    if (isAdmin()) {
      extra.push(
        <Button size="large" onClick={() => setCreateModuleVisible(true)}>
          <PlusOutlined />
          {t('AddModule')}
        </Button>
      );
    }
    return extra;
  };

  return (
    <ExoteachLayout>
      {isMobile && (
        <FullMediParticlesBreadCrumb
          title={globalBannerText}
          subtitle={globalBannerSubtitle}
          image={breadCrumbImage}
          imageType={breadCrumbImageType}
        />
      )}

      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
          marginBottom: '15px',
          marginTop: '-15px'
        }}
      >
        <div />
      </div>

      {isFormation ? (
        <>
          <Formation ue={ue} refetchUE={refetch} />
        </>
      ) : (
        <>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              flexWrap: 'wrap'
            }}
          >
            <CoursBreadcrumb ue={ue} ueId={ueId} />
            <Segmented
              value={listOrCardNavigation}
              onChange={setListOrCardNavigation}
              options={[
                {
                  value: 'Card',
                  icon: <AppstoreOutlined />
                },
                {
                  value: 'List',
                  icon: <BarsOutlined />
                }
              ]}
            />
          </div>

          <div
            style={{
              display: 'flex',
              flexWrap: isMobile ? 'wrap' : 'nowrap', // for mobile, but maybe there is better solution
              flexDirection: 'row',
              justifyContent: 'space-between'
            }}
          >
            <div
              style={{
                flexGrow: 1,
                gap: '40px'
              }}
            >
              {/* UE Content cards */}
              <UEContentCards ue={ue} ueId={ueId} setCreateModuleVisible={setCreateModuleVisible} />
            </div>

            <div
              style={{
                textAlign: 'center',
                flexShrink: 0
              }}
            >
              <QuickAccessModuleList
                ueId={ueId}
                createModuleVisible={createModuleVisible}
                setCreateModuleVisible={setCreateModuleVisible}
              />
            </div>
          </div>
        </>
      )}
    </ExoteachLayout>
  );
};

export default function (props) {
  useEffectScrollTop();
  const ueId = props.match.params.ue;

  return <UEContent ueId={ueId} />;
}
