import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import React, { useEffect, useState } from 'react';
import { PageHeader } from '@ant-design/pro-layout';
import { Button, Card, Col, Form, Input, message, Rate, Row, Upload, Radio, Slider } from 'antd';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';
import QueueAnim from 'rc-queue-anim';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult';
import LoadingOutlined from '@ant-design/icons/lib/icons/LoadingOutlined';
import { useMutation } from '@apollo/client';
import { CREATE_COURS } from '@/shared/graphql/cours';
import { UeCategorySelector } from '@/shared/pages/admin/components/UeCategorySelector';
import FileTextOutlined from '@ant-design/icons/lib/icons/FileTextOutlined';
import FilePdfOutlined from '@ant-design/icons/lib/icons/FilePdfOutlined';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';

export default function(props) {
  useEffectScrollTop();
  const { t } = useTranslation();

  const [form] = Form.useForm();
  const [createCours, { loading, data, error }] = useMutation(CREATE_COURS);
  const [filePDF, setFilePDF] = useState(null);
  const [fileEPUB, setFileEPUB] = useState(null);
  const [uecategoryId, setUecategoryId] = useState(undefined);

  const [isAnnale, setIsAnnale] = useState(false);
  const categoryId = props.match.params.categorie || undefined;

  if (data && data.createCours && data.createCours.id) {
    router.push(`/cours/${data.createCours.id}`);
  }

  const handleChangeCategory = (id) => {
    setUecategoryId(id);
  };

  const handleFinish = async (formData) => {
    try {
      let newCours = { ...formData, isAnnale };
      if (filePDF) {
        newCours = { ...newCours, pdf: filePDF };
      }
      if (fileEPUB) {
        newCours = { ...newCours, epub: fileEPUB };
      }
      if (uecategoryId) {
        // Ajoute la catégorie
        newCours = { ...newCours, uecategoryId };
        await createCours({ variables: { cours: newCours } });
        message.success(t('Created'));
      } else {
        await message.error('Veuillez choisir une catégorie où placer ce cours.');
      }
    } catch (e) {
      console.error(e);
      // message.error('Erreur serveur, veuillez réessayer')
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const beforeUpload = (file, fileList) => {
    setFilePDF(file);
    return false;
  };
  const beforeUploadEpub = (file, fileList) => {
    setFileEPUB(file);
    return false;
  };

  const [layout, setLayout] = useState('pdf');

  return (
    (<ExoteachLayout>
      <PageHeader
        className="site-page-header"
        onBack={() => router.goBack()}
        title={t('createCourse')}
        subTitle={"Les champs seront modifiables après création"}
        extra={[]}
      />
      <div>
        <QueueAnim type={['top', 'down']}>
          <Row justify="center" type="flex" key="1">
            <>
              <Col xl={18} lg={18} md={24} sm={24} xs={24}>
                <Card>
                  {/* Show small error(s) if needed */}
                  <SmallErrorsAlert error={error} loading={loading}/>

                  <Form
                    layout="vertical"
                    onFinish={handleFinish}
                    form={form}
                  >
                    <Form.Item
                      name="name"
                      label={t('Title')}
                      rules={[
                        { required: true, message: t('pleaseEnterCourseName') },
                      ]}
                    >
                      <Input type="text" placeholder={t('placeholderTitle')}/>
                    </Form.Item>

                    <Form.Item
                      name="text"
                      label={t('Description')}
                    >
                      <Input type="text" placeholder={t('placeholderDescription')}/>
                    </Form.Item>

                    <Form.Item name="difficulty" label={t('Difficulty')} help="Difficulté entre 0 et 5">
                      <Slider min={0} max={5} step={0.1} tooltip={{
                        formatter: (value) => `${value}/5`
                      }}/>
                    </Form.Item>

                    <br/>
                    <Form.Item name="layout" label={t('CourseType')}>
                      <Radio.Group
                        onChange={(e) => setLayout(e.target.value)}
                        value={layout}
                        style={{ marginBottom: 8 }}
                      >
                        <Radio.Button value="pdf">{t('File')}</Radio.Button>
                        <Radio.Button value="video">{t('Video')}</Radio.Button>
                      </Radio.Group>
                    </Form.Item>

                    {layout === 'pdf' && (
                      <>
                        <Form.Item
                          label={t('File')}
                        >
                          <Upload.Dragger
                            name="pdf"
                            listType="text"
                            showUploadList
                            accept="*/*"
                            beforeUpload={beforeUpload}
                            fileList={filePDF ? [filePDF] : []}
                            onRemove={file => setFilePDF('')}
                          >
                            <div>
                              {loading ? <LoadingOutlined/> : <FilePdfOutlined/>}
                              <div className="ant-upload-text">Upload</div>
                            </div>
                          </Upload.Dragger>
                        </Form.Item>

                      </>
                    )}

                    {layout === 'video' && (
                      <Form.Item
                        name="video"
                        label="Lien embedded de vidéo principale"
                      >
                        <Input type="textarea" placeholder=""/>
                      </Form.Item>
                    )}

                    <Form.Item label={t('Category')}>
                      <UeCategorySelector
                        onChangeCategory={handleChangeCategory}
                        defaultCategoryId={categoryId}
                      />
                    </Form.Item>

                    <Form.Item>
                      <Button size="large" htmlType="submit" type="primary" loading={loading}>
                        {t('Create')}
                      </Button>
                    </Form.Item>
                    <p>
                      {t('YouCanAddMoreAfterCourseCreation')}
                    </p>
                  </Form>
                </Card>
              </Col>
            </>
          </Row>
        </QueueAnim>
      </div>
    </ExoteachLayout>)
  );
}
