import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import { FileImage } from '@/shared/components/FileImage.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import QuickAccessModuleList from '@/shared/components/QuickAccessModule/QuickAccessModuleList.jsx';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { tr } from '@/shared/services/translate.js';
import { UE_TYPES } from '@/shared/services/ues.js';
import { isAdmin } from '@/shared/utils/authority.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { isAptoria, isMobile } from '@/shared/utils/utils.js';
import PlusOutlined from '@ant-design/icons/lib/icons/PlusOutlined.js';
import React, { useContext, useEffect } from 'react';
import { But<PERSON>, Divider, Segmented } from 'antd';
import { useTranslation } from 'react-i18next';
import { Link } from 'umi';
import { CategoryContentCards } from '@/pages/cours/components/CategoryContentCards';
import { useQuery } from '@apollo/client';
import {
  QUERY_COURS_IN_UECATEGORY,
  QUERY_UE_CATEGORY_ID_WITH_CHILDREN
} from '@/shared/graphql/cours.js';
import CalendarTwoTone from '@ant-design/icons/lib/icons/CalendarTwoTone';
import { AppstoreOutlined, BarsOutlined } from '@ant-design/icons';
import CoursBreadcrumb from '@/shared/components/Cours/CoursBreadcrumb';

/* COURSES IN CATEGORY LIST */
export const CategoryListView = ({ categoryId }) => {
  const { t } = useTranslation();
  const { appearance, listOrCardNavigation, setListOrCardNavigation } = useContext(GlobalContext);

  /* QUERY COURS */
  const queryResult = useQuery(QUERY_COURS_IN_UECATEGORY, {
    variables: { ueCategoryId: categoryId },
    fetchPolicy: 'cache-and-network'
  });
  const { error, data, loading, refetch } = queryResult;

  const { data: dataCurrentCategory, loading: loadingCurrentCategory } = useQuery(
    QUERY_UE_CATEGORY_ID_WITH_CHILDREN,
    {
      variables: { id: categoryId },
      fetchPolicy: 'cache-and-network'
    }
  );
  const currentCategory = dataCurrentCategory?.ueCategory;
  const childrenCategories = currentCategory?.children;

  const {
    globalBannerText,
    setGlobalBannerText,
    setGlobalBannerSubtitle,
    globalBannerSubtitle,
    setCoursId,
    setUeId,
    setCategoryId,
    //categoryId: categoryIdFromContext,
    ueId: ueIdContext,
    coursId: coursIdContext,
    breadCrumbImage,
    setBreadCrumbImage,
    breadCrumbImageType
  } = useContext(GlobalContext);

  const ueCategory =
    data?.coursInUECategory && data?.coursInUECategory[0] ? data?.coursInUECategory[0] : null;
  const getUeName = () => ueCategory?.ueCategory?.ue?.[tr('name')];
  const getUeCategoryName = () => ueCategory?.ueCategory?.[tr('name')];
  const getUeId = () => ueCategory?.ueCategory?.ue?.id;
  const breadCrumbText = getUeName() ? `${getUeCategoryName()}` : '';
  const [createModuleVisible, setCreateModuleVisible] = React.useState(false);

  const ueType = currentCategory?.ue?.type;

  useEffect(() => {
    setCategoryId(categoryId);
    setCoursId(null);
    if (ueCategory?.ueCategory?.image) {
      setBreadCrumbImage(ueCategory?.ueCategory?.image);
    }
    if (getUeId()) {
      setUeId(getUeId());
      setGlobalBannerText(breadCrumbText);
      setGlobalBannerSubtitle(ueCategory?.ueCategory?.[tr('description') || '']);
    }
  }, [data, ueIdContext, coursIdContext]);

  return (
    <ExoteachLayout>
      {isMobile && (
        <FullMediParticlesBreadCrumb
          title={globalBannerText}
          subtitle={globalBannerSubtitle}
          image={breadCrumbImage}
          imageType={breadCrumbImageType}
        />
      )}
      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
          marginBottom: '15px',
          marginTop: '10px'
        }}
      >
        <div>
          <CoursBreadcrumb
            categoryId={categoryId}
            //ue={ueCategory && ueCategory.ueCategory && ueCategory.ueCategory.ue}
            //ueCategory={ueCategory && ueCategory.ueCategory}
          />
        </div>
        {!isAptoria && ueType === UE_TYPES.SUBJECT && (
          <div>
            <Link to={`/cours/ue/${getUeId()}/planning`}>
              <Button key="3" size="large" icon={<CalendarTwoTone />}>
                {t('TimeSchedule')}
              </Button>
            </Link>
            {isAdmin() && (
              <Button size="large" onClick={() => setCreateModuleVisible(true)}>
                <PlusOutlined />
                {t('AddModule')}
              </Button>
            )}
            <Segmented
              value={listOrCardNavigation}
              onChange={setListOrCardNavigation}
              options={[
                {
                  value: 'Card',
                  icon: <AppstoreOutlined />
                },
                {
                  value: 'List',
                  icon: <BarsOutlined />
                }
              ]}
            />
          </div>
        )}
        <FileImage image={ueCategory?.image} useAntdComponent={false} />
      </div>

      {loading && <SpinnerCentered />}

      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          flexWrap: isMobile ? 'wrap' : 'nowrap',
          gap: '5px'
        }}
      >
        {/* Courses list */}
        <div
          style={{
            // minWidth: '400px',
            justifyContent: 'center',
            display: 'flex',
            width: '100%',
            flexGrow: 1,
            order: 1
          }}
        >
          {/* COURS */}
          <CategoryContentCards {...queryResult} ueType={ueType} categoryId={categoryId} />
        </div>

        {/* Side Modules */}
        <div
          style={{
            textAlign: 'center',
            flexGrow: 1,
            order: 2,
            flexShrink: 0
          }}
        >
          <QuickAccessModuleList
            ueCategoryId={categoryId}
            createModuleVisible={createModuleVisible}
            setCreateModuleVisible={setCreateModuleVisible}
          />
        </div>
      </div>

      <Divider />
    </ExoteachLayout>
  );
};
export default function (props) {
  useEffectScrollTop();

  const { categorie } = props.match.params;

  return (
    <React.Fragment>
      <CategoryListView categoryId={categorie} />
    </React.Fragment>
  );
}
