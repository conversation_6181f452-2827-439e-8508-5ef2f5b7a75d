import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import { FileImage } from '@/shared/components/FileImage';
import ExoAvatar from '@/shared/components/User/ExoAvatar';
import { UserProfileCard } from '@/shared/components/User/UserProfileCard/UserProfileCard';
import { QUERY_UEMODULES } from '@/shared/graphql/ue_modules';
import { EditFormation } from '@/shared/pages/cours/formation/Admin/EditFormation';
import { EditFormationContextProvider } from '@/shared/pages/cours/formation/context/EditFormationContext';
import FormationModulesList from '@/shared/pages/cours/formation/FormationModules/FormationModulesList';
import { useQueryFormationProgress } from '@/shared/pages/cours/formation/hooks/useQueryFormationProgress';
import {
  BANNER_DEFAULT_BACKGROUND_COLOR,
  formatTimeFormation
} from '@/shared/pages/cours/formation/utils/formation_utils';
import { tr } from '@/shared/services/translate';
import { isAdmin } from '@/shared/utils/authority';
import { useQuery } from '@apollo/client';
import { Button, Card, Progress, Space, Typography } from 'antd';
import { ClockCircleOutlined, EditOutlined } from '@ant-design/icons';
import React from 'react';
import { useTranslation } from 'react-i18next';
import useMediaQuery from '@/shared/hooks/useMediaQuery';
import { isMobile } from '@/shared/utils/utils';

const { Title } = Typography;

export default function Formation({ ue, refetchUE }) {
  const { t } = useTranslation();
  const isSmallScreen = useMediaQuery('(max-width: 600px)');

  const [editMode, setEditMode] = React.useState(false);

  const { progressData, totalModulesCompleted, formattedTimeSpent } = useQueryFormationProgress(
    ue?.id
  );

  const handleCloseEdition = () => {
    setEditMode(false);
    refetchUE && refetchUE();
  };

  const canEditFormation = isAdmin();

  // Modules query
  const { data: modulesData } = useQuery(QUERY_UEMODULES, {
    variables: {
      ueId: ue.id
    },
    fetchPolicy: 'cache-and-network'
  });
  const modules = modulesData?.ueModules || [];
  // module peut être lié:
  // - soit à un élément de formation
  // - soit à un cours (coursId), non modifiable

  const totalModules = modules.length;

  const progressPercent = totalModules > 0 ? (totalModulesCompleted / totalModules) * 100 : 0;

  const bannerBackgroundColor = ue?.settings?.backgroundColor || BANNER_DEFAULT_BACKGROUND_COLOR;

  return (
    <Card bodyStyle={{ padding: 0 }}>
      <div style={{ background: bannerBackgroundColor, color: 'white', padding: '32px' }}>
        {editMode && (
          <EditFormationContextProvider ue={ue}>
            <EditFormation isVisible={editMode} handleClose={handleCloseEdition} />
          </EditFormationContextProvider>
        )}

        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <Title level={2} style={{ color: 'white', margin: '0 0 15px 0' }}>
            {ue?.[tr('name')]}
            {canEditFormation && (
              <Button
                style={{ marginLeft: 16 }}
                size={'middle'}
                shape="circle"
                ghost
                icon={<EditOutlined />}
                onClick={() => setEditMode(true)}
              />
            )}
          </Title>

          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ minWidth: 275 }}>
              <Space direction="vertical" size={4}>
                <div style={{ marginBottom: 8 }}>
                  {ue?.tuteurs?.map((user, k) => (
                    <UserProfileCard userId={user.id} username={user.username} key={user?.id}>
                      <ExoAvatar
                        avatar={user && user.avatar}
                        size={42}
                        isActive={user && user.isActive}
                      />
                    </UserProfileCard>
                  ))}
                </div>
                <div
                  style={{ display: 'flex', flexDirection: 'row', gap: 20, alignItems: 'center' }}
                >
                  {/*
                <div>
                  <Progress type={'circle'} percent={95} width={48} strokeColor="#52c41a" />
                </div>
                */}
                  <div>
                    {ue?.settings?.minimumTimeEnabled && (
                      <div>
                        <ClockCircleOutlined /> {t('Formation.EstimatedTime')}{' '}
                        {formatTimeFormation(ue?.settings?.minimumTimeSeconds)}
                      </div>
                    )}
                    <div>
                      <ClockCircleOutlined /> {t('Formation.MyTimeSpent')} {formattedTimeSpent}
                    </div>
                  </div>
                </div>
              </Space>
              <div style={{ marginTop: 12 }}>
                {t('Progress')}{' '}
                <Progress percent={progressPercent} showInfo={false} strokeColor="#69c06d" />
              </div>
            </div>
            {!isSmallScreen && !isMobile && ue?.image && (
              <>
                <FileImage image={ue.image} style={{ maxHeight: 200 }} />
              </>
            )}
          </div>
        </div>
      </div>

      <div style={{ padding: 24 }}>
        <Title level={4} style={{ textAlign: 'center', marginBottom: 24 }}>
          <RenderQuillHtml>{ue?.long_description_html}</RenderQuillHtml>
        </Title>

        <FormationModulesList modules={modules} progressData={progressData} ue={ue} />
      </div>
    </Card>
  );
}
