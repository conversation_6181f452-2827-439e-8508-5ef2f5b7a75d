import { QUERY_UE_MODULE_PROGRESS_IN_UE } from '@/shared/graphql/ue_modules';
import { useQuery } from '@apollo/client';

export const formatTimeSpent = (totalTimeSpentInSeconds) => {
  const hours = Math.floor(totalTimeSpentInSeconds / 3600);
  const minutes = Math.floor((totalTimeSpentInSeconds % 3600) / 60);
  const seconds = totalTimeSpentInSeconds % 60;
  const formattedTimeSpent = `${hours}h ${minutes}m ${seconds}s`;
  // Hide hours if 0
  if (hours === 0) {
    return `${minutes}m ${seconds} s`;
  }
  return formattedTimeSpent;
};

export const useQueryFormationProgress = (ueId, userId = null) => {
  // Global formation progress
  const progressData = useQuery(QUERY_UE_MODULE_PROGRESS_IN_UE, {
    variables: {
      ueId
    },
    fetchPolicy: 'cache-and-network'
  });

  const progress = progressData?.data?.ueModuleProgressInUE;

  const totalTimeSpentInSeconds = progress?.reduce((acc, module) => {
    return acc + (module?.seconds || 0);
  }, 0);

  const totalModulesCompleted =
    progress?.filter((module) => module?.completed === true)?.length || 0;

  const formattedTimeSpent = formatTimeSpent(totalTimeSpentInSeconds);

  return {
    formattedTimeSpent,
    totalTimeSpentInSeconds,
    totalModulesCompleted,
    progressData
  };
};
