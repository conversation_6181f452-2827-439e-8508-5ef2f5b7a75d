import { QUERY_EVENT_BY_ID } from '@/shared/graphql/events';
import { QUERY_FORM_BY_ID_ADMIN } from '@/shared/graphql/forms';
import { QUERY_QCM_POST_DETAIL, QUERY_QUESTION_BY_ID_FOR_FORMATION } from '@/shared/graphql/qcm';
import { ELEMENTS_TYPE } from '@/shared/services/formations';
import { tr } from '@/shared/services/translate';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { BookCheck, Video, ListChecks, SquareCheck, Table, CalendarDays } from 'lucide-react';
import React from 'react';

export const useModuleDetails = (module) => {
  const { t } = useTranslation();

  // Query useQuery QCM si item?.element?.type === ELEMENTS_TYPE.MCQ
  const { data: dataQcm } = useQuery(QUERY_QCM_POST_DETAIL, {
    variables: {
      id: module?.element?.mcqId
    },
    skip: module?.element?.type !== ELEMENTS_TYPE.MCQ,
    fetchPolicy: 'cache-and-network'
  });

  // Query exercice si item?.element?.type === ELEMENTS_TYPE.DO_EXERCISE
  const { data: dataExercice } = useQuery(QUERY_QUESTION_BY_ID_FOR_FORMATION, {
    variables: {
      id: module?.element?.doQuestionId
    },
    skip: module?.element?.type !== ELEMENTS_TYPE.DO_EXERCISE,
    fetchPolicy: 'cache-and-network'
  });

  const { data: dataForm } = useQuery(QUERY_FORM_BY_ID_ADMIN, {
    variables: {
      id: module?.element?.formId
    },
    skip: module?.element?.type !== ELEMENTS_TYPE.FORM,
    fetchPolicy: 'cache-and-network'
  });

  // ELEMENT EVENT
  const { data: dataEvent } = useQuery(QUERY_EVENT_BY_ID, {
    variables: {
      id: module?.element?.targetEventId
    },
    skip: module?.element?.type !== ELEMENTS_TYPE.EVENT,
    fetchPolicy: 'cache-and-network'
  });

  // TODO si c'est un cours il faut afficher nombre de steps et durée

  let label = '',
    icon = null,
    stepNumber = 1;
  if (module?.element) {
    label = module?.element?.[tr('name')];
    // todo si c'est mcqId il faut recup nom série
    switch (module?.element?.type) {
      case ELEMENTS_TYPE.MCQ:
        icon = <ListChecks />;
        label = dataQcm?.qcm?.[tr('titre')] || '';
        break;
      case ELEMENTS_TYPE.DO_EXERCISE:
        icon = <SquareCheck />;
        /*
        let questionTitle = dataExercice?.question?.[tr('question')];
        // Limit question title to 50 characters
        if (questionTitle?.length > 20) {
          questionTitle = `${questionTitle.slice(0, 20)}...`;
        }
        label = `${t('general.Exercice')} ${module?.element?.doQuestionId}` || '';
        */
        // Titre de l'élément
        label = module?.element?.[tr('name')] || '';
        break;
      case ELEMENTS_TYPE.FORM:
        icon = <Table />;
        label = dataForm?.form?.[tr('name')] || '';
        break;

      case ELEMENTS_TYPE.VIDSTACK_VIDEO:
        icon = <Video />;
        label = module?.element?.settings?.vidstackTitle;
        break;
      case ELEMENTS_TYPE.EVENT:
        icon = <CalendarDays />;
        label = dataEvent?.event?.[tr('name')] || '';
        break;
      /*
      case ELEMENTS_TYPE.EXAM:
        icon = <GraduationCap />;
        break;
      case ELEMENTS_TYPE.FORM:
        icon = <Table />;
        break;
      case ELEMENTS_TYPE.HOMEWORK:
        icon = <GraduationCap />;
        break;
      */
    }
  } else if (module?.cours) {
    const realCours = module?.cours?.targetCours || module?.cours;
    label = realCours?.[tr('name')];
    icon = <BookCheck />;
    stepNumber = module?.countSteps;
  }

  return {
    label,
    icon,
    stepNumber,
    dataQcm,
    dataExercice,
    dataForm
  };
};
