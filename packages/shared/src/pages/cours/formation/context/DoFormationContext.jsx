import { QUERY_UE_ID } from '@/shared/graphql/cours';
import { QUERY_BLOCKS_WITH_ELEMENTS_IN_FORMATION_COURS_STEP } from '@/shared/graphql/formations';
import {
  MUTATION_HANDLE_NEXT_UEMODULE,
  MUTATION_SAVE_TIME_SPENT_ON_MODULE,
  QUERY_UE_MODULE_PROGRESS,
  QUERY_UEMODULES
} from '@/shared/graphql/ue_modules';
import { useHandleUserStartModule } from '@/shared/pages/cours/formation/hooks/useHandleUserStartModule';
import { useTimeTracking } from '@/shared/pages/cours/formation/hooks/useTimeTracking';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { useMutation, useQuery } from '@apollo/client';
import React, { useEffect, useState } from 'react';

export const DoFormationContext = React.createContext(undefined);

export const DoFormationContextProvider = ({ ueId, ueModuleId, children }) => {
  const [currentSelection, setCurrentSelection] = useState([]);
  const [selectedCoursMenuItem, setSelectedCoursMenuItem] = useState(null);

  // State to track if the module has been started
  const [started, setStarted] = useState(false);

  // selected block id, only in cours with steps
  const [currentBlockId, setCurrentBlockId] = useState(null);

  // Permet d'initialiser le logging du module en cours (handleUserDoUEModule) si pas déjà fait
  const ueModuleProgressLogId = useHandleUserStartModule(ueModuleId, ueId, started, currentBlockId);

  // Query ue by id
  const {
    data: ueData,
    loading: ueLoading,
    refetch: refetchUe
  } = useQuery(QUERY_UE_ID, {
    variables: {
      id: ueId
    },
    fetchPolicy: 'cache-and-network'
  });

  const currentUE = ueData?.ue;

  // QUERY_UEMODULES
  const {
    data: modulesData,
    loading: loadingModules,
    error,
    refetch: refetchModules
  } = useQuery(QUERY_UEMODULES, {
    variables: {
      ueId: ueId
    },
    fetchPolicy: 'cache-and-network'
  });
  const modules = modulesData?.ueModules || [];
  const currentModule = modules.find((module) => module.id === ueModuleId);
  const hasSteps = currentModule?.cours?.layout === 'steps'; // Module with steps
  const nextModuleId = modules[modules.findIndex((module) => module.id === ueModuleId) + 1]?.id;
  const previousModuleId = modules[modules.findIndex((module) => module.id === ueModuleId) - 1]?.id;
  const coursId = currentModule?.coursId;

  // QUERY BLOCKS (=steps) in cours, each block contain one element for now
  const {
    loading: loadingBlock,
    data: dataBlocks,
    refetch: refetchBlocks
  } = useQuery(QUERY_BLOCKS_WITH_ELEMENTS_IN_FORMATION_COURS_STEP, {
    variables: { id: coursId },
    fetchPolicy: 'cache-and-network',
    skip: !coursId
  });
  const courseBlocks = dataBlocks?.formationBlocksInCours || [];
  ///////////////////////

  const [mutationHandleNextUEModule, { loading: loadingHandleNextModule }] = useMutation(
    MUTATION_HANDLE_NEXT_UEMODULE
  );

  // Quand on change de module, on reset le currentBlockId
  useEffect(() => {
    if (courseBlocks.length > 0 && !currentBlockId) {
      // n'arrive jamais quand on est dans cours step chargé
      // Quand on charge les blocks, si y'en a, et qu'on a pas de currentBlockId, on prend le premier
      setCurrentBlockId(courseBlocks?.[0].id);
    } else {
      // Arrive quand currentModule change et qu'on n'a pas de blocks
      if (currentBlockId !== null) {
        setCurrentBlockId(null); //TODO idéalement il faudrait que la personne qui reprend module à étape retombe sur curentBlockId
      }
    }
  }, [currentModule, courseBlocks]);

  const currentBlock = courseBlocks.find((block) => block.id === currentBlockId);

  // QUERY CURRENT PROGRESS for this module
  const {
    data: progressData,
    loading: loadingProgress,
    error: errorProgress
  } = useQuery(QUERY_UE_MODULE_PROGRESS, {
    variables: {
      ueModuleId: ueModuleId
    },
    fetchPolicy: 'cache-and-network'
  });
  const myProgress = progressData?.ueModuleProgress;

  const loadingSomething =
    loadingModules || loadingBlock || ueLoading || loadingHandleNextModule || loadingProgress;

  const handleClickOnNextModule = async () => {
    try {
      await mutationHandleNextUEModule({
        variables: {
          ueId,
          ueModuleId,
          ueModuleProgressLogId: ueModuleProgressLogId,
          currentBlockId: currentBlockId ?? null // Pass current block id if exists, otherwise null
        }
      });
      console.log('Next module handled successfully');
    } catch (err) {
      console.error('Error handling next module:', err);
      showGqlErrorsInMessagePopupFromException(err);
    }
  };

  /* TIME TRACKING */
  const [saveTimeSpent] = useMutation(MUTATION_SAVE_TIME_SPENT_ON_MODULE);
  const { isActive } = useTimeTracking({
    started,
    intervalSeconds: 30, // TODO voir si besoin de mettre 60 pour la prod?
    onSave: async (seconds) => {
      try {
        await saveTimeSpent({
          variables: {
            ueModuleId,
            seconds,
            ueModuleProgressLogId,
            currentBlockId
          }
        });
      } catch (err) {
        console.error('[TRACKING] Error saving time:', err);
      }
    }
  });
  /* END TIME TRACKING */

  return (
    <DoFormationContext.Provider
      value={{
        currentSelection,
        setCurrentSelection,

        ue: currentUE,
        modules,
        currentModule,

        refetchModules,
        refetchUe,
        ueLoading,

        selectedCoursMenuItem,
        setSelectedCoursMenuItem,

        nextModuleId,
        previousModuleId,
        hasSteps,
        courseBlocks,
        currentBlockId,
        currentBlock,
        setCurrentBlockId,
        myProgress,
        isActive,
        started,
        setStarted,
        handleClickOnNextModule,
        loadingSomething
      }}
    >
      {children}
    </DoFormationContext.Provider>
  );
};
