import { CREATE_COURS, QUERY_UE_ID, UPDATE_COURS, UPDATE_UE } from '@/shared/graphql/cours';
import {
  MUTATION_CREATE_FORMATION_BLOCK,
  MUTATION_DELETE_FORMATION_BLOCK,
  MUTATION_UPDATE_FORMATION_BLOCK,
  QUERY_BLOCKS_WITH_ELEMENTS_IN_FORMATION_COURS_STEP
} from '@/shared/graphql/formations';
import {
  MUTATION_CREATE_UEMODULE,
  MUTATION_DELETE_UEMODULE,
  MUTATION_UPDATE_UEMODULE,
  QUERY_UEMODULES
} from '@/shared/graphql/ue_modules';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { useMutation, useQuery } from '@apollo/client';
import { Form } from 'antd';
import React, { useEffect, useState } from 'react';

export const EditFormationContext = React.createContext(undefined);

//TODO Do custom hooks

export const EditFormationContextProvider = (props) => {
  const [currentSelection, setCurrentSelection] = useState([]);
  const [form] = Form.useForm();

  const [selectedItem, setSelectedItem] = useState('formation');
  const [selectedCoursMenuItem, setSelectedCoursMenuItem] = useState(null);

  const ueId = props.ue.id;

  // Query ue by id
  const {
    data: ueData,
    loading: ueLoading,
    refetch: refetchUe
  } = useQuery(QUERY_UE_ID, {
    variables: {
      id: ueId
    },
    fetchPolicy: 'cache-and-network'
  });

  const [currentUE, setCurrentUE] = useState(ueData?.ue);

  useEffect(() => {
    setCurrentUE(ueData?.ue);
  }, [ueData]);

  // QUERY_UEMODULES
  const {
    data: modulesData,
    loading: loadingModules,
    error,
    refetch: refetchModules
  } = useQuery(QUERY_UEMODULES, {
    variables: {
      ueId: ueId
    },
    fetchPolicy: 'cache-and-network'
  });
  const [modules, setModules] = useState(modulesData?.ueModules || []);
  useEffect(() => {
    setModules(modulesData?.ueModules || []);
  }, [modulesData]);
  ///////////////////////

  // Current Module
  const module = selectedItem && modules?.find((item) => item.id === selectedItem);
  // Current cours
  const cours = module?.cours;

  // QUERY BLOCKS (=steps) in cours, each block contain one element for now
  const {
    loading: loadingBlock,
    data: dataBlocks,
    refetch: refetchBlocks
  } = useQuery(QUERY_BLOCKS_WITH_ELEMENTS_IN_FORMATION_COURS_STEP, {
    variables: { id: cours?.id },
    fetchPolicy: 'cache-and-network',
    skip: !cours
  });
  const [courseBlocks, setCourseBlocks] = useState(dataBlocks?.formationBlocksInCours || []);
  useEffect(() => {
    if (cours && dataBlocks?.formationBlocksInCours) {
      setCourseBlocks(dataBlocks?.formationBlocksInCours || []);
    }
    console.log('effect courseBlocks', dataBlocks?.formationBlocksInCours);
  }, [cours, dataBlocks]);
  ///////////////////////

  const [currentBlockId, setCurrentBlockId] = useState(null); // selected block id

  // MUTATION for formation settings
  const [editUE, { loading: loadingEditUE }] = useMutation(UPDATE_UE);

  // MUTATION COURS
  const [createCours, { loading: loadingCreateCours }] = useMutation(CREATE_COURS);
  const [editCours, { loading: loadinEditCours }] = useMutation(UPDATE_COURS);

  // MUTATIONS Modules
  const [createModule, { loading: loadingCreateModule }] = useMutation(MUTATION_CREATE_UEMODULE);
  const [updateModule, { loading: loadingUpdateModule }] = useMutation(MUTATION_UPDATE_UEMODULE);
  const [deleteModule, { loading: loadingDeleteModule }] = useMutation(MUTATION_DELETE_UEMODULE);

  // MUTATIONS BLOCKS (for cours module)
  const [createFormationBlock, { loading: loadingCreateFormationBlock }] = useMutation(
    MUTATION_CREATE_FORMATION_BLOCK
  );
  const [updateFormationBlock, { loading: loadingUpdateFormationBlock }] = useMutation(
    MUTATION_UPDATE_FORMATION_BLOCK
  );
  const [deleteFormationBlock, { loading: loadingDeleteFormationBlock }] = useMutation(
    MUTATION_DELETE_FORMATION_BLOCK
  );
  const loadingMutationFormationBloc =
    loadingCreateFormationBlock || loadingUpdateFormationBlock || loadingDeleteFormationBlock;

  const handleEditModuleField = async (moduleId, field, value) => {
    try {
      await updateModule({
        variables: {
          id: moduleId,
          input: {
            [field]: value
          }
        }
      });
    } catch (error) {
      console.error('Error updating module field:', error);
      showGqlErrorsInMessagePopupFromException(error);
    }
  };

  const handleEditUeField = async (field, value) => {
    try {
      await editUE({
        variables: {
          id: ueId,
          ue: {
            [field]: value
          }
        }
      });
    } catch (error) {
      console.error('Error updating UE field:', error);
      showGqlErrorsInMessagePopupFromException(error);
    }
  };

  return (
    <EditFormationContext.Provider
      value={{
        selectedItem,
        setSelectedItem,
        currentSelection,
        setCurrentSelection,
        form,
        ue: currentUE,
        modules,
        module, // current module
        setModules,
        refetchModules,

        editUE,
        loadingEditUE,

        createModule,
        updateModule,
        deleteModule,

        handleEditUeField,
        handleEditModuleField,

        refetchUe,
        ueLoading,

        createCours,
        editCours,

        selectedCoursMenuItem,
        setSelectedCoursMenuItem,

        loadingModules,

        //Course related
        cours, // If in cours steps edition
        courseBlocks, // blocks in cours (steps)
        setCourseBlocks,
        createFormationBlock,
        updateFormationBlock,
        deleteFormationBlock,
        loadingMutationFormationBloc,
        refetchBlocks
      }}
    >
      {props.children}
    </EditFormationContext.Provider>
  );
};
