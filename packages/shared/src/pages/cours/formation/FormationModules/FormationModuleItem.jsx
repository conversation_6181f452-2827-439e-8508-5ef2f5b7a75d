import { useModuleDetails } from '@/shared/pages/cours/formation/hooks/useModuleDetails';
import { formatTimeFormation } from '@/shared/pages/cours/formation/utils/formation_utils';
import {
  ClockCircleOutlined,
  FileTextOutlined,
  PlayCircleOutlined,
  LockOutlined
} from '@ant-design/icons';
import { Avatar, Button, Card, Flex, Progress, Tooltip, Typography, message } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import router from 'umi/router';

const Text = Typography.Text;

export default function FormationModuleItem({
  module,
  index,
  progress = null,
  modules = [],
  ue = null,
  progressData = null
}) {
  const { t } = useTranslation();

  const { label, icon, stepNumber } = useModuleDetails(module);

  const validationSettings = module?.validationSettings;
  const isCompleted = progress?.completed === true;

  const getProgressPercentage = () => {
    // Cas de steps
    if (stepNumber > 1 && progress?.stepsCompleted != null) {
      return ((progress.stepsCompleted / stepNumber) * 100).toFixed(0);
    }
    // Cas module complété
    if (isCompleted) {
      return 100;
    }
    console.log({ progress });
    // Cas module avec temps: calculer le pourcentage selon temps passé / temps total
    if (
      validationSettings?.minimumTime === true &&
      validationSettings?.minimumTimeSeconds > 0 &&
      progress?.seconds != null
    ) {
      const percentage = Math.min(
        (progress.seconds / validationSettings.minimumTimeSeconds) * 100,
        100
      );
      return percentage.toFixed(0);
    }
    return 0;
  };

  const progressPercentage = getProgressPercentage();

  // Vérifier si l'accès est non-linéaire
  const isLinearAccess = ue?.settings?.nonLinearAccess === false; // By default it is false => only linear

  // Trouver le module précédent et sa progression
  const previousModule = index > 0 ? modules[index - 1] : null;
  const previousModuleProgress = previousModule
    ? progressData?.data?.ueModuleProgressInUE?.find((p) => p.ueModuleId === previousModule.id)
    : null;
  const isPreviousModuleCompleted = previousModuleProgress?.completed === true;

  // Déterminer si l'accès au module est autorisé
  const isAccessAllowed = !isLinearAccess || index === 0 || isPreviousModuleCompleted;

  const handleModuleClick = () => {
    if (!isAccessAllowed) {
      message.warning(t('Formation.ModuleAccessBlocked'));
      return;
    }
    router.push(`/cours/ue/${module?.ueId}/module/${module?.id}`);
  };

  return (
    <>
      <Card
        onClick={handleModuleClick}
        key={index}
        size="small"
        style={{
          cursor: isAccessAllowed ? 'pointer' : 'not-allowed',
          opacity: isAccessAllowed ? 1 : 0.7
        }}
      >
        <Flex justify="space-between" align="center" wrap>
          <Flex align="center" gap="small">
            <Avatar
              shape="square"
              size={48}
              icon={!isAccessAllowed ? <LockOutlined /> : icon}
              style={{ backgroundColor: !isAccessAllowed ? '#d9d9d9' : '#2e588d' }}
            />
            <div>
              <Text strong>{label}</Text>
              <div>
                <Text type="secondary">
                  {progress?.stepsCompleted||0}/{stepNumber} {t('Formation.steps', { count: stepNumber })}
                </Text>

                {validationSettings?.minimumTime === true && (
                  <>
                    &nbsp;|
                    <ClockCircleOutlined style={{ margin: '0 4px' }} />
                    <Text type="secondary">
                      {formatTimeFormation(validationSettings.minimumTimeSeconds)}
                    </Text>
                  </>
                )}
                {!isAccessAllowed && (
                  <Tooltip title={t('Formation.CompletePreviousModule')}>
                    <Text type="secondary" style={{ marginLeft: 8 }}>
                      <LockOutlined /> {t('Formation.Locked')}
                    </Text>
                  </Tooltip>
                )}
              </div>
            </div>
          </Flex>

          <Flex align="center" gap="middle" wrap>
            {module.resources !== undefined && (
              <Tooltip title="Ressources">
                <Text>
                  <FileTextOutlined /> {module.resources}
                </Text>
              </Tooltip>
            )}

            <Button
              icon={<PlayCircleOutlined />}
              shape={'circle'}
              type={'primary'}
              disabled={!isAccessAllowed}
            />

            <Progress type="circle" percent={progressPercentage} width={48} strokeColor="#52c41a" />
          </Flex>
        </Flex>
      </Card>
    </>
  );
}
