import { Input, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function HoursMinutesSecondsInput({ defaultSeconds, onChange }) {
  const { t } = useTranslation();

  // Time conversion states
  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);

  // Convert seconds to hours, minutes, seconds when component loads or ue changes
  useEffect(() => {
    if (defaultSeconds) {
      const totalSeconds = defaultSeconds;
      setHours(Math.floor(totalSeconds / 3600));
      setMinutes(Math.floor((totalSeconds % 3600) / 60));
      setSeconds(totalSeconds % 60);
    }
  }, [defaultSeconds]);

  const handleTimeChange = (type, value) => {
    const numValue = parseInt(value) || 0;
    let nextHours = hours;
    let nextMinutes = minutes;
    let nextSeconds = seconds;
    switch (type) {
      case 'hours':
        nextHours = Math.min(Math.max(0, numValue), 999);
        setHours(nextHours);
        break;
      case 'minutes':
        nextMinutes = Math.min(Math.max(0, numValue), 59);
        setMinutes(nextMinutes);
        break;
      case 'seconds':
        nextSeconds = Math.min(Math.max(0, numValue), 59);
        setSeconds(nextSeconds);
        break;
    }
    // Use the new values to calculate totalSeconds
    const totalSeconds = nextHours * 3600 + nextMinutes * 60 + nextSeconds;
    onChange(totalSeconds);
  };

  return (
    <>
      <Space>
        <Input
          type="number"
          min={0}
          max={999}
          value={hours}
          onChange={(e) => handleTimeChange('hours', e.target.value)}
          addonAfter={t('Hours')}
          style={{ width: 140 }}
        />
        <Input
          type="number"
          min={0}
          max={59}
          value={minutes}
          onChange={(e) => handleTimeChange('minutes', e.target.value)}
          addonAfter={t('Minutes')}
          style={{ width: 140 }}
        />
        <Input
          type="number"
          min={0}
          max={59}
          value={seconds}
          onChange={(e) => handleTimeChange('seconds', e.target.value)}
          addonAfter={t('Seconds')}
          style={{ width: 140 }}
        />
      </Space>
    </>
  );
}
