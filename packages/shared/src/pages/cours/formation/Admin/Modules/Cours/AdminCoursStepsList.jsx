import { EditFormationContext } from '@/shared/pages/cours/formation/context/EditFormationContext';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { DeleteOutlined, DragOutlined, PlusOutlined } from '@ant-design/icons';
import { closestCenter, DndContext } from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Flex, message, Popconfirm, Spin, Tooltip, Typography } from 'antd';
import { SquareCheck } from 'lucide-react';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';

const AdminCoursStepItem = ({ item, selectedItem, setSelectedItem, index, handleDeleteStep }) => {
  const { t } = useTranslation();

  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    border: selectedItem === item?.id ? '2px solid #1677ff' : '1px solid #d9d9d9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    cursor: 'pointer',
    background: 'white'
  };

  let icon = <SquareCheck />;
  let label = '';

  label = item?.name;

  return (
    <div
      ref={setNodeRef}
      style={style}
      key={item.id}
      onClick={() => {
        setSelectedItem(item?.id);
      }}
    >
      <Flex align="center" justify="space-between" style={{ width: '100%' }}>
        <Flex align="center" gap={8}>
          <span {...attributes} {...listeners} style={{ cursor: 'grab' }}>
            <DragOutlined />
          </span>
          <span>{index + 1}</span>
          {item.checked !== undefined && <input type="checkbox" checked={item.checked} readOnly />}
          {icon && icon}
          <Typography.Text>{label}</Typography.Text>
        </Flex>

        <Popconfirm title={t('SureOfDeletion')} onConfirm={() => handleDeleteStep(item.id)}>
          <Tooltip title={t('Delete')}>
            <DeleteOutlined style={{ color: 'red' }} />
          </Tooltip>
        </Popconfirm>
      </Flex>
    </div>
  );
};

export default function AdminCoursStepsList() {
  const {
    courseBlocks,
    setSelectedCoursMenuItem,
    setCourseBlocks,
    refetchBlocks,
    selectedCoursMenuItem,
    updateFormationBlock,
    deleteFormationBlock
  } = useContext(EditFormationContext);
  const { t } = useTranslation();

  const handleDeleteStep = async (id) => {
    try {
      //TODO delete block + element in it ?
      await deleteFormationBlock({
        variables: { id }
      });
      await refetchBlocks();
      message.success(t('DeletedWithSuccess'));
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const handleDragEnd = async (event) => {
    const { active, over } = event;
    if (active.id !== over?.id) {
      const oldIndex = courseBlocks.findIndex((item) => item.id === active.id);
      const newIndex = courseBlocks.findIndex((item) => item.id === over?.id);
      const newOrder = arrayMove(courseBlocks, oldIndex, newIndex);
      // Recalculer l'ordre
      const reorderedModules = newOrder.map((item, index) => ({
        ...item,
        order: index + 1 // ou index si on veut 0-based
      }));
      // Mise à jour front instantanée
      setCourseBlocks(reorderedModules);
      // Mise à jour backend
      try {
        // Met à jour chaque module individuellement en base
        //TODO opti update en batch
        await Promise.all(
          reorderedModules.map((mod) =>
            updateFormationBlock({
              variables: {
                id: mod.id,
                input: {
                  order: mod.order
                }
              }
            })
          )
        );
        await refetchBlocks();
      } catch (err) {
        console.error('Erreur pendant le reorder:', err);
        showGqlErrorsInMessagePopupFromException(err);
        refetchBlocks(); // Recharger les modules en cas d'erreur
      }
    }
  };

  if (!courseBlocks) {
    return (
      <div>
        <Spin />
      </div>
    );
  }
  return (
    <>
      <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext
          items={courseBlocks.map((m) => m.id)}
          strategy={verticalListSortingStrategy}
        >
          {courseBlocks?.map((item, index) => {
            return (
              <AdminCoursStepItem
                item={item}
                key={item?.id}
                index={index}
                selectedItem={selectedCoursMenuItem}
                setSelectedItem={setSelectedCoursMenuItem}
                handleDeleteStep={handleDeleteStep}
              />
            );
          })}
        </SortableContext>
      </DndContext>

      <br />
      <Button
        type="dashed"
        block
        icon={<PlusOutlined />}
        style={{ marginTop: 12 }}
        onClick={() => setSelectedCoursMenuItem('addStep')}
      >
        {t('Formation.AddStep')}
      </Button>
    </>
  );
}
