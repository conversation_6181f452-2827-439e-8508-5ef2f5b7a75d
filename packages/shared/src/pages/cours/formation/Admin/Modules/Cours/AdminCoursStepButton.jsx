import AdminCoursSteps from '@/shared/pages/cours/formation/Admin/Modules/Cours/AdminCoursSteps';
import { EditFormationContextProvider } from '@/shared/pages/cours/formation/context/EditFormationContext';
import { Button } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function AdminCoursStepButton({ cours, ue }) {
  const { t } = useTranslation();

  const [adminVisible, setAdminVisible] = useState(false);

  const handleClose = () => {
    setAdminVisible(false);
  };

  return (
    <>
      <Button type="primary" style={{ marginBottom: 16 }} onClick={() => setAdminVisible(true)}>
        {t('Formation.EditCoursSteps')}
      </Button>
      <AdminCoursSteps handleClose={handleClose} isVisible={adminVisible} />
    </>
  );
}
