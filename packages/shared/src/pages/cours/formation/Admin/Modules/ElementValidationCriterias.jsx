import { MUTATION_UPDATE_FORMATION_ELEMENT } from '@/shared/graphql/formations';
import { EditFormationContext } from '@/shared/pages/cours/formation/context/EditFormationContext';
import { ELEMENTS_TYPE } from '@/shared/services/formations';
import { useMutation } from '@apollo/client';
import { Input, Switch, Typography } from 'antd';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Component to display and edit validation criterias (validationSettings) for an entire "formation MODULE" (in formation) or an element inside a "course step by step" (which is inside formation).
 * Formation => module => validationSettings
 * Formation => courseModule => element => validationSettings
 *
 * @param element
 * @param module
 * @returns {JSX.Element}
 * @constructor
 */
export default function ElementValidationCriterias({ element, module = null }) {
  const { t } = useTranslation();

  const { handleEditModuleField, refetchModules, loadingModules, refetchBlocks } =
    useContext(EditFormationContext);

  const [updateFormationElement, { loading: loadingUpdateElement }] = useMutation(
    MUTATION_UPDATE_FORMATION_ELEMENT
  );
  const editValidationCriteria = async (obj) => {
    if (module) {
      // Dans le cas de module entier de formation => update module
      await handleEditModuleField(module.id, 'validationSettings', {
        ...module.validationSettings,
        ...obj
      });
      await refetchModules();
    } else {
      // Dans le cas de élément dans un cours => update élément
      await updateFormationElement({
        variables: {
          id: element.id,
          input: {
            validationSettings: {
              ...element.validationSettings,
              ...obj
            }
          }
        }
      });
      await refetchBlocks();
    }
  };

  const validationSettings = module?.validationSettings || element?.validationSettings;

  return (
    <>
      {/* Élément visuels sans condition de validation */}
      {[
        ELEMENTS_TYPE.RICH_TEXT,
        ELEMENTS_TYPE.HTML,
        ELEMENTS_TYPE.CALLOUT,
        ELEMENTS_TYPE.DATE_AND_TIME_PICKER,
        ELEMENTS_TYPE.DATE_PICKER,
        ELEMENTS_TYPE.AVATAR_SELECT,
        ELEMENTS_TYPE.IMAGE,
        ELEMENTS_TYPE.LINK,
        ELEMENTS_TYPE.TITLE,
        ELEMENTS_TYPE.FILE,
        ELEMENTS_TYPE.DIAPO_SYNTHESE,
        ELEMENTS_TYPE.COURS,
        ELEMENTS_TYPE.COURSE_SHORTCUT
      ].includes(element.type) && (
        <div>
          <Typography.Text strong={false} style={{ fontSize: 16 }}>
            {t('Formation.NoValidationCriteriaClickingNextWillValidate')}
          </Typography.Text>
        </div>
      )}

      {/* Element vidéo: à voir avec vidstack */}
      {[ELEMENTS_TYPE.VIDEO].includes(element.type) && <div>En construction...</div>}

      {ELEMENTS_TYPE.EVENT === element.type && <>En construction...</>}
      {ELEMENTS_TYPE.EXAM === element.type && <>En construction...</>}

      {[ELEMENTS_TYPE.FORM].includes(element.type) && (
        <>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              cursor: 'pointer',
              marginBottom: 10
            }}
          >
            <Typography.Text strong={false} style={{ fontSize: 16 }}>
              {t('Formation.FinishForm')}
            </Typography.Text>
            <Switch loading={loadingModules} checked={true} disabled />
          </div>
        </>
      )}

      {[ELEMENTS_TYPE.MCQ, ELEMENTS_TYPE.DO_EXERCISE].includes(element.type) && (
        <>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              cursor: 'pointer',
              marginBottom: 10
            }}
          >
            <Typography.Text strong={false} style={{ fontSize: 16 }}>
              {t(
                ELEMENTS_TYPE.DO_EXERCISE === element.type
                  ? 'Formation.FinishExercise'
                  : 'Formation.FinishSerie'
              )}
            </Typography.Text>
            <Switch
              loading={loadingModules}
              checked={validationSettings?.finishSerie}
              onChange={async (checked) => {
                await editValidationCriteria({
                  finishSerie: checked,
                  obtainMinimumGrade: false
                });
              }}
            />
          </div>

          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              cursor: 'pointer'
            }}
          >
            <Typography.Text strong={false} style={{ fontSize: 16 }}>
              {t('Formation.ObtainAtLeast')}
            </Typography.Text>
            <Switch
              loading={loadingModules}
              checked={validationSettings?.obtainMinimumGrade}
              onChange={async (checked) => {
                await editValidationCriteria({
                  finishSerie: false,
                  obtainMinimumGrade: checked
                });
              }}
            />
          </div>

          {validationSettings?.obtainMinimumGrade && (
            <>
              <span>{t('Formation.SpecificGrade')}</span>
              <Input
                type="number"
                min={0}
                max={20}
                value={validationSettings?.minimumGrade}
                onChange={async (e) => {
                  await editValidationCriteria({
                    minimumGrade: parseFloat(e.target.value) || 0
                  });
                }}
                style={{ width: 60, margin: '0 4px' }}
              />
              /20
            </>
          )}
        </>
      )}

      <br />
      <br />
      <br />
    </>
  );
}
