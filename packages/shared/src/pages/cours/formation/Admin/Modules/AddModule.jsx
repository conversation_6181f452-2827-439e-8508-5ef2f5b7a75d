import { HierarchySelecter, validesTypes } from '@/shared/components/HierarchySelecter';
import ModuleButtons from '@/shared/pages/cours/formation/Admin/Modules/ModuleButtons';
import { UE_MODULES_TYPES } from '@/shared/pages/cours/formation/Admin/Modules/modulesTypes';
import { EditFormationContext } from '@/shared/pages/cours/formation/context/EditFormationContext';
import { CreateEditFormationElementModal } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal';
import { ELEMENTS_TYPE } from '@/shared/services/formations';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import React, { useContext } from 'react';
import { Button, Input, Select, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { Radio } from 'antd';

/**
 * Add module in formation
 * @param props
 * @returns {JSX.Element}
 * @constructor
 */
export default function AddModule(props) {
  const { t } = useTranslation();
  const [selectedModule, setSelectedModule] = React.useState(null);
  const [coursIdArray, setCoursIdArray] = React.useState(null); // move to context
  const [creationType, setCreationType] = React.useState(null); // move to context

  const { ue, modules, refetchModules, createModule, updateModule, deleteModule, createCours } =
    useContext(EditFormationContext);

  const handleAddExistingCourse = async () => {
    try {
      await createModule({
        variables: {
          input: {
            ueId: ue.id,
            elementId: null,
            coursId: coursIdArray[0],
            order: modules.length + 1 // A voir
          }
        }
      });
      await refetchModules();
      setSelectedModule(null);
    } catch (e) {
      console.error('Error adding existing course:', e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const handleFinishModuleElementCreation = async (elementId) => {
    // create corresponding module and refetch modules
    try {
      await createModule({
        variables: {
          input: {
            ueId: ue.id,
            elementId,
            order: modules.length + 1 // à voir
          }
        }
      });
      await refetchModules();
      setSelectedModule(null);
    } catch (e) {
      console.error('Error creating module:', e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const [coursToCreate, setCoursToCreate] = React.useState({
    name: '',
    ueId: ue.id
  });

  const [displaySettings, setDisplaySettings] = React.useState({
    display: 'classic'
  });
  const [validationSettings, setValidationSettings] = React.useState({});

  const handleCreateCours = async () => {
    try {
      let cours = {
        name: coursToCreate.name,
        ueId: ue.id, // pour gérer permissions et lier aux séries
        layout: displaySettings?.display === 'classic' ? 'pdf' : 'steps' //
      };
      const { data } = await createCours({ variables: { cours } });
      const createdCoursId = data?.createCours?.id;
      if (createdCoursId) {
        await createModule({
          variables: {
            input: {
              ueId: ue.id,
              elementId: null,
              coursId: createdCoursId,
              displaySettings, // Pas sûr de garder type affichage ici (step/pas step)
              validationSettings,
              order: modules.length + 1 // A voir
            }
          }
        });
        await refetchModules();
      }
    } catch (e) {
      console.error('Error creating course:', e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  return (
    <div style={{ marginLeft: 24 }}>
      <div style={{ marginBottom: 24 }}>
        <Typography.Title level={3}>{t('Formation.AddModuleTitle')}</Typography.Title>
      </div>

      <ModuleButtons onClick={(module) => setSelectedModule(module)} selected={selectedModule} />

      {[UE_MODULES_TYPES.cours].includes(selectedModule) && (
        <>
          <Typography.Title level={5}>{t('Formation.CoursModule')}</Typography.Title>

          <Radio.Group
            onChange={(e) => setCreationType(e.target.value)}
            value={creationType}
            style={{ marginTop: 16 }}
          >
            <Radio value="new">{t('CreateNewCourse')}</Radio>
            <Radio value="import">{t('ImportExistingCourse')}</Radio>
          </Radio.Group>

          {creationType === 'import' && (
            <>
              <div style={{ marginTop: 16 }}>{t('ImportExistingCourse')}</div>
              {/* Sélection cours */}
              <HierarchySelecter
                multiple={false}
                setterHookSelection={setCoursIdArray}
                useTreeSelect
                rankToRemoveIfLeaf={[
                  validesTypes.CTYPE_UNKNOWN,
                  validesTypes.CTYPE_FOLDER,
                  validesTypes.CTYPE_CATEGORY,
                  validesTypes.CTYPE_PAGE,
                  validesTypes.CTYPE_UE
                ]}
                disabledTypes={[
                  validesTypes.CTYPE_FOLDER,
                  validesTypes.CTYPE_UE,
                  validesTypes.CTYPE_PAGE,
                  validesTypes.CTYPE_UNKNOWN,
                  validesTypes.CTYPE_CATEGORY
                ]}
                simplificationFeature={validesTypes.CTYPE_COURS}
                additionalTreeProps={{
                  placement: 'topLeft',
                  listHeight: 500,
                  style: { minWidth: '700px', marginRight: '5px' },
                  popupMatchSelectWidth: false,
                  treeLine: true,
                  placeholder: t('ImportExistingCourse')
                }}
              />

              <br />
              <br />
              <div style={{ marginTop: 16 }}>
                <Button onClick={handleAddExistingCourse} type="primary">
                  {t('ImportExistingCourse')}
                </Button>
              </div>
            </>
          )}

          {creationType === 'new' && (
            <>
              {/* Nom du cours, et bouton créer */}
              <div style={{ marginTop: 16 }}>
                <Typography.Title level={5}>{t('Name')}</Typography.Title>
                <Input
                  value={coursToCreate.name}
                  onChange={(e) => setCoursToCreate({ ...coursToCreate, name: e.target.value })}
                />
              </div>

              {/* Affichage */}
              <Typography.Title level={5}>{t('general.Display')}</Typography.Title>
              <Select
                value={displaySettings.display}
                style={{ width: 200 }}
                onChange={(value) => {
                  setDisplaySettings({ ...displaySettings, display: value });
                }}
              >
                <Select.Option value={'classic'}>Affichage classique</Select.Option>
                <Select.Option value={'steps'}>Affichage séquentiel</Select.Option>
              </Select>

              <div style={{ marginTop: 16 }}>
                <Button onClick={handleCreateCours} type="primary">
                  {t('Create')}
                </Button>
              </div>
            </>
          )}
        </>
      )}

      {selectedModule === UE_MODULES_TYPES.exerciseSeries && (
        <>
          {/*
            <Typography.Title level={5}>{t('AddExerciseSerieModule')}</Typography.Title>
          */}
          <CreateEditFormationElementModal
            inFormation
            isModalVisible={true}
            modalType={'CREATE'}
            showElementChoice={false}
            preselectedElementType={ELEMENTS_TYPE.MCQ}
            closeModalHandler={handleFinishModuleElementCreation}
          />
        </>
      )}

      {selectedModule === UE_MODULES_TYPES.exercise && (
        <>
          {/*
            <Typography.Title level={5}>{t('AddExerciseSerieModule')}</Typography.Title>
          */}
          <CreateEditFormationElementModal
            inFormation
            isModalVisible={true}
            modalType={'CREATE'}
            showElementChoice={false}
            preselectedElementType={ELEMENTS_TYPE.DO_EXERCISE}
            closeModalHandler={handleFinishModuleElementCreation}
          />
        </>
      )}

      {selectedModule === UE_MODULES_TYPES.form && (
        <>
          {/*
            <Typography.Title level={5}>{t('AddExerciseSerieModule')}</Typography.Title>
          */}
          <CreateEditFormationElementModal
            inFormation
            isModalVisible={true}
            modalType={'CREATE'}
            showElementChoice={false}
            preselectedElementType={ELEMENTS_TYPE.FORM}
            closeModalHandler={handleFinishModuleElementCreation}
          />
        </>
      )}

      {selectedModule === UE_MODULES_TYPES.event && (
        <>
          {/*
           <Typography.Title level={5}>{t('AddExerciseSerieModule')}</Typography.Title>
          */}
          <CreateEditFormationElementModal
            inFormation
            isModalVisible={true}
            modalType={'CREATE'}
            showElementChoice={false}
            preselectedElementType={ELEMENTS_TYPE.EVENT}
            closeModalHandler={handleFinishModuleElementCreation}
          />
        </>
      )}
      {selectedModule === UE_MODULES_TYPES.video && (
        <>
          {/*
           <Typography.Title level={5}>{t('AddExerciseSerieModule')}</Typography.Title>
          */}
          <CreateEditFormationElementModal
            inFormation
            isModalVisible={true}
            modalType={'CREATE'}
            showElementChoice={false}
            preselectedElementType={ELEMENTS_TYPE.VIDSTACK_VIDEO}
            closeModalHandler={handleFinishModuleElementCreation}
          />
        </>
      )}

      {selectedModule === UE_MODULES_TYPES.scorm && (
        <>
          <CreateEditFormationElementModal
            inFormation
            isModalVisible={true}
            modalType={'CREATE'}
            showElementChoice={false}
            preselectedElementType={ELEMENTS_TYPE.SCORM}
            closeModalHandler={handleFinishModuleElementCreation}
          />
        </>
      )}

      {/* TODO exam */}
      {selectedModule === UE_MODULES_TYPES.exam && (
        <>
          {/*
           <Typography.Title level={5}>{t('AddExerciseSerieModule')}</Typography.Title>
          */}
          En construction, bientôt disponible
        </>
      )}

      {/* TODO homework */}
      {selectedModule === UE_MODULES_TYPES.homework && (
        <>
          {/*
           <Typography.Title level={5}>{t('AddExerciseSerieModule')}</Typography.Title>
          */}
          En construction, bientôt disponible
        </>
      )}
    </div>
  );
}
