import { EditFormationContext } from '@/shared/pages/cours/formation/context/EditFormationContext';
import { Button, Input, Typography } from 'antd';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import React from 'react';

export default function AddStepInCours() {
  const { t } = useTranslation();

  const {
    ue,
    createFormationBlock,
    courseBlocks,
    cours,
    loadingMutationFormationBloc,
    setSelectedCoursMenuItem,
    refetchBlocks
  } = useContext(EditFormationContext);

  const handleAddStep = async () => {
    // Create block in cours
    /*
      if(!hasBorder) {
        delete data?.settings?.borderRadius
        delete data?.settings?.borderSize
        delete data?.settings?.borderColor
      }
      const newFormationBlock = { ...data };
      if (newFormationBlock && newFormationBlock.order) {
        newFormationBlock.order = parseInt(newFormationBlock.order, 10);
      }
      newFormationBlock.formationStepId = formationStep?.id;
      newFormationBlock.coursId = coursId;
      newFormationBlock.type = selectedBlockType;
      newFormationBlock.titleId = selectedTitleId;
   */

    const { data } = await createFormationBlock({
      variables: {
        input: {
          coursId: cours?.id,
          name: stepName,
          type: 'single',
          order: courseBlocks.length + 1 // A voir
        }
      }
    });

    const createdBlockId = data?.createFormationBlock?.id;
    // refetch blocks
    await refetchBlocks();
    setSelectedCoursMenuItem(createdBlockId);
  };

  // Créer bloc, refetch, le mettre sélectionné, si pas d'élément, créer élément.
  const [stepName, setStepName] = React.useState('');

  return (
    <div>
      {/* Nom de l'étape */}
      <div style={{ margin: 24 }}>
        <Typography.Title level={4}>{t('Formation.StepName')}</Typography.Title>
        <Input
          type={'text'}
          placeholder={t('Formation.StepName')}
          value={stepName}
          onChange={(e) => {
            setStepName(e.target.value);
          }}
        />
        <Button
          style={{ marginTop: 12 }}
          onClick={handleAddStep}
          type={'primary'}
          loading={loadingMutationFormationBloc}
        >
          {t('Formation.AddStep')}
        </Button>
      </div>
    </div>
  );
}
