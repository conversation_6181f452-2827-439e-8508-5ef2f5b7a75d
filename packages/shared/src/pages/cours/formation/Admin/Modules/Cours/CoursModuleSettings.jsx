import HoursMinutesSecondsInput from '@/shared/pages/cours/formation/Admin/HoursMinutesSecondsInput';
import AdminCoursStepButton from '@/shared/pages/cours/formation/Admin/Modules/Cours/AdminCoursStepButton';
import { EditFormationContext } from '@/shared/pages/cours/formation/context/EditFormationContext';
import { tr } from '@/shared/services/translate';
import { Button, Switch, Tag, Typography, Space, Alert } from 'antd';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import router from 'umi/router';

export default function CoursModuleSettings({ cours, module, inCoursSteps = false }) {
  const { t } = useTranslation();

  const { refetchModules, handleEditModuleField, deleteModule, editCours } =
    useContext(EditFormationContext);

  return (
    <div style={{ marginLeft: 12 }}>
      <Space direction="horizontal" size={8} style={{ marginBottom: 16 }}>
        <Typography.Title
          level={3}
          style={{ marginBottom: 16 }}
          editable={{
            onChange: async (value) => {
              //TODO: handle edit cours name
              //setTitle(value);
              //await handleEditUeField('name', value);
              await editCours({
                variables: {
                  id: cours.id,
                  cours: {
                    name: value
                  }
                }
              });
              await refetchModules();
            },
            triggerType: ['text', 'icon']
          }}
        >
          {cours?.[tr('name')]}
        </Typography.Title>
        {/*<EditOutlined style={{ marginLeft: 8 }} />*/}

        {/* Type d'affichage du cours */}
        <div>
          {cours?.layout === 'steps' && <Tag>{t('CourseTypes.Steps')}</Tag>}
          {cours?.layout === 'pdf' && <Tag>{t('CourseTypes.PDF')}</Tag>}
          {cours?.layout === 'video' && <Tag>{t('CourseTypes.Video')}</Tag>}
          {cours?.layout === 'formation' && <Tag>{t('CourseTypes.EnrichedCourse')}</Tag>}
        </div>
      </Space>

      {/* Si on est pas déjà dans edition cours à étape et que c'est un cours à étapes (spécifique aux formations), on peut éditer les étapes */}
      {!inCoursSteps && cours?.layout === 'steps' && (
        <div>
          <AdminCoursStepButton cours={cours} ue={cours?.ue} />
        </div>
      )}
      {!inCoursSteps && cours?.layout !== 'steps' && (
        <div>
          {/* Si c'est un cours "classique" on édite depuis la page détail cours */}
          <div>
            <Button
              type={'primary'}
              onClick={() => {
                router.push(`/cours/${cours.id}`);
              }}
            >
              {t('Formation.SeeOrEditCourse')}
            </Button>
          </div>
        </div>
      )}
      <Typography.Title level={5} style={{ color: 'lightgray' }}>
        {cours?.[tr('description')]}
      </Typography.Title>
      {/*
        <Typography.Title level={5}>{t('general.Display')}</Typography.Title>
        <Select defaultValue={'classic'}>
          <Select.Option value={'classic'}>Affichage classique</Select.Option>
        </Select>
      */}
      <div>
        <Typography.Title level={5}>{t('Formation.ValidationCriterias')}</Typography.Title>
        <div onClick={() => console.log('clicked: accès non linéaire')}>
          <Space direction="horizontal" size={8}>
            <Typography.Text strong={false} style={{ fontSize: 16 }}>
              {t('Formation.MinimumTime')}
            </Typography.Text>
            <Switch
              //loading={ueLoading}
              //checked={module?.validationSettings?.minimumTime}
              value={module?.validationSettings?.minimumTime}
              onChange={async (checked) => {
                await handleEditModuleField(module.id, 'validationSettings', {
                  ...module.validationSettings,
                  minimumTime: checked,
                  validatedSteps: false
                });
                await refetchModules();
              }}
            />
          </Space>
          {module?.validationSettings?.minimumTime === true && (
            <div style={{ marginTop: 24 }}>
              {/*
              <TimePicker
                allowClear={false}
                format={'HH:mm'}
                value={
                  typeof module?.validationSettings?.minimumTimeDuration === 'string'
                    ? dayjs(module?.validationSettings?.minimumTimeDuration, 'HH:mm')
                    : dayjs('00:00', 'HH:mm')
                }
                onChange={(time) => {
                  const durationInMinutes = time.hour() * 60 + time.minute();
                  handleEditModuleField(module.id, 'validationSettings', {
                    ...module.validationSettings,
                    minimumTimeDuration: time.format('HH:mm')
                  });
                  refetchModules();
                }}
              />
              */}
              <HoursMinutesSecondsInput
                defaultSeconds={module?.validationSettings?.minimumTimeSeconds}
                onChange={(totalSeconds) => {
                  handleEditModuleField(module.id, 'validationSettings', {
                    ...module.validationSettings,
                    minimumTimeSeconds: totalSeconds
                  });
                  refetchModules();
                }}
              />
            </div>
          )}
        </div>

        <div style={{ marginTop: 24, marginBottom: 24 }}>{t('Formation.OR')}</div>

        <div>
          <Space direction="horizontal" size={8}>
            <Typography.Text strong={false} style={{ fontSize: 16 }}>
              {t('Formation.ValidatedSteps')}
            </Typography.Text>
            <Switch
              //loading={ueLoading}
              //checked={module?.validationSettings?.minimumTime}
              value={module?.validationSettings?.validatedSteps}
              onChange={async (checked) => {
                await handleEditModuleField(module.id, 'validationSettings', {
                  ...module.validationSettings,
                  validatedSteps: checked,
                  minimumTime: false
                });
                await refetchModules();
              }}
            />
          </Space>

          <div>
            <Alert
              message={t('Formation.InfoValidationStepsCours')}
              type="info"
              showIcon
              style={{ marginTop: 8 }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
