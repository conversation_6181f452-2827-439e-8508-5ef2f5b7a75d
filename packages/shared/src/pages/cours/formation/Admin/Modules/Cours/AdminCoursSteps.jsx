import AddStepInCours from '@/shared/pages/cours/formation/Admin/Modules/Cours/AddStepInCours';
import AdminCoursStepsList from '@/shared/pages/cours/formation/Admin/Modules/Cours/AdminCoursStepsList';
import CoursModuleSettings from '@/shared/pages/cours/formation/Admin/Modules/Cours/CoursModuleSettings';
import EditStepInCours from '@/shared/pages/cours/formation/Admin/Modules/Cours/EditStepInCours';
import { EditFormationContext } from '@/shared/pages/cours/formation/context/EditFormationContext';
import { isMobile } from '@/shared/utils/utils';
import { Button, Drawer } from 'antd';
import { useTranslation } from 'react-i18next';
import React, { useContext } from 'react';
import { Settings2 } from 'lucide-react';

// Module => [(CoursStep=Block)] => Element

export default function AdminCoursSteps({ isVisible, handleClose }) {
  const { t } = useTranslation();

  const { ue, modules, refetchModules, selectedCoursMenuItem, setSelectedCoursMenuItem, cours } =
    useContext(EditFormationContext);

  const hasSelectedStep =
    selectedCoursMenuItem !== 'settings' &&
    selectedCoursMenuItem !== 'display' &&
    selectedCoursMenuItem !== 'addStep';

  // Trouver le module associé au cours
  const module = modules?.find((item) => item.coursId === cours?.id);

  return (
    <>
      <Drawer
        open={isVisible}
        footer={null}
        placement="bottom"
        onCancel={handleClose}
        onClose={handleClose}
        closable
        destroyOnClose
        height={isMobile ? 'calc(100% - 109px)' : 'calc(100% - 64px)'}
      >
        <div style={{ display: 'flex', height: '100%' }}>
          {/* Sidebar */}
          <div style={{ minWidth: 280, borderRight: '1px solid #f0f0f0', padding: 16 }}>
            <Button
              block
              icon={<Settings2 />}
              style={{
                marginBottom: 24,
                color: selectedCoursMenuItem === 'settings' ? '#1677ff' : 'auto',
                border:
                  selectedCoursMenuItem === 'settings' ? '2px solid #1677ff' : '1px solid #d9d9d9'
              }}
              onClick={() => {
                setSelectedCoursMenuItem('settings');
              }}
            >
              {t('Formation.ModuleSettings')}
            </Button>

            {/* Bibliothèque support : <NotebookText /> */}

            {/* Affichage : <MonitorPlay /> */}

            <div style={{ fontWeight: 'bold', marginBottom: 12 }}>
              {t('Formation.CoursSteps')} {/* <PlusOutlined style={{ fontWeight: 'normal' }} /> */}
            </div>

            <AdminCoursStepsList />
          </div>

          {/* Contenu principal droite */}

          {selectedCoursMenuItem === 'settings' && cours && (
            <CoursModuleSettings cours={cours} module={module} inCoursSteps />
          )}

          {selectedCoursMenuItem === 'display' && <>DISPLAY</>}

          {/* Ajouter nouvelle étape */}
          {selectedCoursMenuItem === 'addStep' && <AddStepInCours />}

          {hasSelectedStep && <EditStepInCours />}
        </div>
      </Drawer>
    </>
  );
}
