import ExoQuillEditor from '@/shared/components/ExoQuill/ExoQuillEditor';
import { ExoQuillToolbarPresets } from '@/shared/components/ExoQuill/utils';
import ExoFormImage from '@/shared/components/Forms/ExoFormImage';
import { MUTATION_UPDATE_BANNER_IMAGE_UE } from '@/shared/graphql/ue_modules';
import HoursMinutesSecondsInput from '@/shared/pages/cours/formation/Admin/HoursMinutesSecondsInput';
import { EditFormationContext } from '@/shared/pages/cours/formation/context/EditFormationContext';
import { getAntdHexColorIfExists } from '@/shared/utils/utils';
import { useMutation } from '@apollo/client';
import {
  Button,
  ColorPicker,
  Divider,
  Input,
  message,
  Popover,
  Space,
  Switch,
  Typography
} from 'antd';
import React, { useContext, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Info } from 'lucide-react';
import { BANNER_DEFAULT_BACKGROUND_COLOR } from '@/shared/pages/cours/formation/utils/formation_utils';

export default function FormationSettings() {
  const { t } = useTranslation();

  const { ue, handleEditUeField, refetchUe, ueLoading, loadingEditUE } =
    useContext(EditFormationContext);

  const [editorContent, setEditorContent] = React.useState(ue?.long_description_html || '');
  const [description, setDescription] = React.useState(ue?.description || '');
  const [title, setTitle] = React.useState(ue?.name || '');
  const [backgroundColor, setBackgroundColor] = React.useState(
    ue?.settings?.backgroundColor || BANNER_DEFAULT_BACKGROUND_COLOR
  );

  const [updateImageBannerUE, { loading: loadingUpload }] = useMutation(
    MUTATION_UPDATE_BANNER_IMAGE_UE
  );

  useEffect(() => {
    setTitle(ue?.name || '');
    setBackgroundColor(ue?.settings?.backgroundColor || BANNER_DEFAULT_BACKGROUND_COLOR);
  }, [ue?.name, ue?.settings?.backgroundColor]);

  return (
    <>
      <div style={{ flex: 1, padding: 24, overflowY: 'auto' }}>
        <Typography.Title
          editable={{
            onChange: async (value) => {
              setTitle(value);
              await handleEditUeField('name', value);
              await refetchUe();
            },
            triggerType: ['text', 'icon']
          }}
          level={2}
        >
          {title || t('Nom de la formation')}
        </Typography.Title>

        <div style={{ marginTop: 24 }}>
          <Space direction={'horizontal'} align={'center'}>
            <Typography.Text strong>{t('Formation.BackgroundColor')}</Typography.Text>
            <ColorPicker
              format="hex"
              value={backgroundColor}
              onChange={async (color) => {
                const hexColor = getAntdHexColorIfExists(color);
                setBackgroundColor(hexColor);
                await handleEditUeField('settings', {
                  ...ue.settings,
                  backgroundColor: hexColor
                });
                await refetchUe();
              }}
            />
          </Space>
        </div>

        <div style={{ marginTop: 24 }}>
          <>
            <Typography.Text strong>{t('Formation.BackgroundImage')}</Typography.Text>
            <br />
            <ExoFormImage
              name={null}
              beforeUpload={async (file) => {
                try {
                  await updateImageBannerUE({
                    variables: {
                      ueId: ue.id,
                      file,
                      action: 'upload'
                    }
                  });
                  await refetchUe();
                } catch (e) {
                  console.error(e);
                }
              }}
              onDelete={async (file) => {
                try {
                  await updateImageBannerUE({
                    variables: {
                      ueId: ue.id,
                      file: null,
                      action: 'delete'
                    }
                  });
                  await refetchUe();
                } catch (e) {
                  console.error(e);
                }
              }}
              label={`Image de bannière`}
              defaultValue={ue?.image}
              loading={loadingUpload}
            />
          </>
        </div>

        <div style={{ marginTop: 24 }}>
          <Typography.Text strong>{t('ShortDescription')}</Typography.Text>
          <Input
            type={'text'}
            value={description}
            onChange={(v) => {
              setDescription(v.target.value);
            }}
          />
        </div>

        <div style={{ marginTop: 24 }}>
          <Typography.Text strong>{t('LongDescription')}</Typography.Text>

          <ExoQuillEditor
            defaultValue={ue?.long_description_html}
            onChange={(editorData) => {
              setEditorContent(editorData);
            }}
            modules={ExoQuillToolbarPresets.globalAnnounce}
          />

          <Button
            type="primary"
            loading={ueLoading || loadingEditUE}
            style={{ marginTop: 16 }}
            onClick={() => {
              handleEditUeField('long_description_html', editorContent);
              handleEditUeField('description', description);
              refetchUe();
              message.success(t('Saved'));
            }}
          >
            {t('general.save')}
          </Button>
        </div>

        <Divider style={{ marginTop: 32, marginBottom: 24 }} />

        {/* Settings */}

        <div style={{ display: 'flex', flexDirection: 'column', gap: 16, marginTop: 32 }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              cursor: 'pointer'
            }}
          >
            <Typography.Text strong style={{ fontSize: 17 }}>
              {t('Formation.MinimumTime')}
            </Typography.Text>
            <Switch
              loading={ueLoading || loadingEditUE}
              checked={ue?.settings?.minimumTimeEnabled}
              onChange={(checked) => {
                handleEditUeField('settings', {
                  ...ue.settings,
                  minimumTimeEnabled: checked
                });
                refetchUe();
              }}
            />
          </div>
          {ue?.settings?.minimumTimeEnabled && (
            <div style={{ marginTop: 16, marginLeft: 24 }}>
              <HoursMinutesSecondsInput
                defaultSeconds={ue?.settings?.minimumTimeSeconds}
                onChange={(totalSeconds) => {
                  handleEditUeField('settings', {
                    ...ue.settings,
                    minimumTimeSeconds: totalSeconds
                  });
                }}
              />
            </div>
          )}

          {/* Accès non linéaire */}
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              cursor: 'pointer'
            }}
          >
            <Typography.Text strong style={{ fontSize: 17 }}>
              {t('Formation.NonLinearAccess')}
            </Typography.Text>
            <Switch
              loading={ueLoading || loadingEditUE}
              checked={ue?.settings?.nonLinearAccess}
              onChange={async (checked) => {
                await handleEditUeField('settings', {
                  ...ue.settings,
                  nonLinearAccess: checked
                });
                await refetchUe();
              }}
            />
          </div>

          {/* Certificat */}
          <div
            onClick={() => console.log('clicked: certificat')}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              cursor: 'pointer'
            }}
          >
            <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Typography.Text strong style={{ fontSize: 17 }}>
                {t('Formation.Certificate')}
              </Typography.Text>
              <Popover content={t('UnavailableFeature')}>
                <Info style={{ fontSize: 24 }} />
              </Popover>
            </span>
            <Switch
              disabled
              loading={ueLoading || loadingEditUE}
              checked={ue?.settings?.certificate}
              onChange={(checked) => {
                handleEditUeField('settings', {
                  ...ue.settings,
                  certificate: checked
                });
                refetchUe();
              }}
            />
          </div>

          {/* Période d'accessibilité */}
          <div
            onClick={() => console.log('clicked: accessibilité')}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              cursor: 'pointer'
            }}
          >
            <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Typography.Text strong style={{ fontSize: 17 }}>
                {t('Formation.AccessibilityPeriod')}
              </Typography.Text>
              <Popover content={t('UnavailableFeature')}>
                <Info style={{ fontSize: 24 }} />
              </Popover>
            </span>
            <Switch
              disabled
              loading={ueLoading || loadingEditUE}
              checked={ue?.settings?.accessibilityPeriod}
              onChange={(checked) => {
                handleEditUeField('settings', {
                  ...ue.settings,
                  accessibilityPeriod: checked
                });
                refetchUe();
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
}
