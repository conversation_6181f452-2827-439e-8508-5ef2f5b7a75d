import { CoursPage } from '@/shared/pages/cours/details/$cours';
import {
  DoFormationContext,
  DoFormationContextProvider
} from '@/shared/pages/cours/formation/context/DoFormationContext';
import { LiveTimer } from '@/shared/pages/cours/formation/DoModule/LiveTimer';
import ModuleIntro from '@/shared/pages/cours/formation/DoModule/ModuleIntro';
import { useModuleDetails } from '@/shared/pages/cours/formation/hooks/useModuleDetails';
import { formatTimeSpent } from '@/shared/pages/cours/formation/hooks/useQueryFormationProgress';
import { formatTimeFormation } from '@/shared/pages/cours/formation/utils/formation_utils';
import FormationElement from '@/shared/pages/formations/components/FormationElement';
import { IS_DEV } from '@/shared/utils/utils';
import React, { useContext } from 'react';
import { Button, Divider, Empty, Space, Steps, Tag, Typography } from 'antd';
import { ClockCircleOutlined } from '@ant-design/icons';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { ArrowLeft } from 'lucide-react';

import { useTranslation } from 'react-i18next';
import { router } from 'umi';

const { Title, Text } = Typography;

/**
 * FormationStepView - User launched a formation module
 * @returns {JSX.Element}
 * @constructor
 */
const FormationStepView = () => {
  const { t } = useTranslation();

  const {
    ue, // formation
    hasSteps, // TODO pas sûr, peut être plutôt type de module?
    courseBlocks, // Si c'est un cours enrichi, steps de cours
    modules, // tous les modules de la formation
    nextModuleId, // id du prochain module
    previousModuleId, // id du module précédent
    currentModule, // module courant

    // Étapes de cours à étapes
    currentBlock,
    currentBlockId,
    setCurrentBlockId,
    myProgress,
    isActive,
    started,
    setStarted,
    handleClickOnNextModule,
    loadingSomething
  } = useContext(DoFormationContext);

  const { label, icon, stepNumber } = useModuleDetails(currentModule);

  // STEPS /////////
  const blockIdToIndex = courseBlocks?.reduce((acc, block, index) => {
    acc[block.id] = index;
    return acc;
  }, {});
  const indexToBlockId = courseBlocks?.map((block) => block.id) || [];
  const stepsItems = courseBlocks?.map((c) => ({
    title: c?.name
  }));

  // Sidebar steps: si pas de steps, on affiche une seule step avec le nom du module
  const sidebarStepsItems = hasSteps ? stepsItems : [{ title: label }];
  const sidebarCurrentStepIndex = hasSteps ? blockIdToIndex?.[currentBlockId] ?? 0 : 0;
  const sidebarOnChange = hasSteps ? onChangeStep : undefined;

  // Trouver l'index du module courant
  const currentModuleIndex = modules?.findIndex((m) => m.id === currentModule?.id) ?? -1;
  // Vérifier si l'accès est non-linéaire
  const isLinearAccess = ue?.settings?.nonLinearAccess === false; // By default it is false => only linear
  // Vérifier si le module actuel est complété
  const isCurrentModuleCompleted = myProgress?.completed === true;
  // Déterminer si l'accès au module suivant est autorisé

  if (IS_DEV) {
    console.log({ isCurrentModuleCompleted, isLinearAccess, currentModuleIndex });
  }

  // Only for cours with steps
  const checkIfAuthorizedForNextStepCours = () => {
    //TODO check si condition validation du module est remplie
  };

  // Renvoie true si l'accès au module suivant est autorisé
  const checkIfNextModuleAccessAllowed = () => {
    // Si l'accès est non-linéaire, user peut se balader comme il veut
    const isNonLinearAccess = isLinearAccess === false;

    if (hasSteps) {
      // LES CAS DE MODULE COURS A ÉTAPES
      const authorizedForNextStepCours = checkIfAuthorizedForNextStepCours();

      return isNonLinearAccess || authorizedForNextStepCours;
    } else {
      // LES CAS DE MODULE FORMATION
      if (!nextModuleId) {
        return false; // Pas de module suivant, donc pas d'accès autorisé
      }
      return isNonLinearAccess || isCurrentModuleCompleted;
    }

    //return isNonLinearAccess || isCurrentModuleCompleted;
  };

  const onChangeStep = (index) => {
    // Lorsqu'on clique sur une step (index), on met à jour le blockId
    const newBlockId = indexToBlockId[index];
    if (newBlockId) {
      setCurrentBlockId(newBlockId);
    }
  };

  const goToPreviousModule = () => {
    if (previousModuleId) {
      setStarted(false);
      router.push(`/cours/ue/${ue?.id}/module/${previousModuleId}`);
    }
  };

  const handleClickNext = async () => {
    // Backend mutation to check validation
    await handleClickOnNextModule();

    if (hasSteps) {
      // go to next step in course
      const currentIndex = blockIdToIndex?.[currentBlockId] ?? 0; // fallback au premier step pour initialisation ou premiere ouverture
      const nextIndex = currentIndex + 1;
      if (nextIndex < indexToBlockId.length) {
        const nextBlockId = indexToBlockId[nextIndex];
        if (nextBlockId) {
          setCurrentBlockId(nextBlockId);
        }
      }
      // Si on est à la dernière étape, on va au prochain module
      if (nextIndex > indexToBlockId.length - 1) {
        //if (checkIfNextModuleAccessAllowed()) {
        goToNextModule();
        //} else {
        // Si on n'est pas autorisé à aller au prochain module, on affiche un message ou on fait rien
        //console.warn('Accès au prochain module non autorisé');
        //}
      }
    } else {
      goToNextModule();
    }
  };
  const goToNextModule = () => {
    if (nextModuleId) {
      setStarted(false);
      router.push(`/cours/ue/${ue?.id}/module/${nextModuleId}`);
    }
  };

  const goBackToFormation = () => {
    router.push(`/cours/ue/${ue?.id}`);
  };

  const isCourseWithSteps =
    currentModule?.cours && currentModule?.cours?.layout === 'steps' && currentBlock;

  const isCourseWithoutSteps = currentModule?.cours && currentModule?.cours?.layout !== 'steps';

  const stepTitle = currentBlock?.name;

  const currentModuleMinimumTimeDuration =
    currentModule?.validationSettings?.minimumTimeSeconds &&
    formatTimeFormation(currentModule?.validationSettings?.minimumTimeSeconds);

  // Si pas autorisé, désactiver le bouton "Suivant"
  const disableNextButton = !checkIfNextModuleAccessAllowed();

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* Header */}
      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '16px',
          borderBottom: '1px solid #f0f0f0',
          columnGap: 24
        }}
      >
        <Space direction={'horizontal'}>
          <Button type={'link'} onClick={goBackToFormation} style={{ marginLeft: 0 }}>
            <ArrowLeft style={{ fontSize: 22 }} />
          </Button>
          {icon && icon}
          <Text strong style={{ fontSize: 16 }}>
            {label}
          </Text>
          {myProgress?.completed && <Tag color={'#1f9d0d'}>{t('Formation.ModuleCompleted')}</Tag>}
        </Space>

        {/* Always show timer, show total duration only if exists */}
        <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginLeft: 'auto' }}>
          <ClockCircleOutlined />
          <Text>
            <LiveTimer
              baseSeconds={myProgress?.seconds || 0}
              paused={!isActive} // stopper le timer quand onglet inactif
              format={formatTimeSpent}
            />{' '}
            {currentModuleMinimumTimeDuration && <>/ {currentModuleMinimumTimeDuration}</>}
          </Text>
        </div>

        <div style={{ margin: 'auto' }}>
          <Button
            style={{ marginRight: 8 }}
            onClick={goToPreviousModule} // on peut toujours revenir au module précédent si il existe
            disabled={!previousModuleId} // désactiver si pas de module précédent
            loading={loadingSomething}
          >
            {t('Previous')}
          </Button>
          <Button
            type="primary"
            disabled={disableNextButton} // désactiver si pas autorisé à aller au prochain module
            onClick={handleClickNext}
            loading={loadingSomething}
          >
            {t('Next')}
          </Button>
        </div>
      </div>
      {/* End header */}

      {/* Main content */}
      <div style={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
        {/* Sidebar */}
        {started && (
          <div
            style={{
              width: '250px',
              borderRight: '1px solid #f0f0f0',
              padding: '16px 8px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between'
            }}
          >
            <div>
              <Steps
                direction="vertical"
                size="small"
                current={sidebarCurrentStepIndex}
                onChange={sidebarOnChange}
                items={sidebarStepsItems}
              />
            </div>

            {/* TODO voir si on garde le timer dans la sidebar
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <ClockCircleOutlined />
                <Text>5 min / 50 min</Text>
              </div>
            */}

            {/*
            <Button type="default" style={{ marginTop: 16 }}>
              Modifier
            </Button>
            */}
          </div>
        )}

        {/* Step Content */}
        <div style={{ flex: 1, padding: 24, display: 'flex', flexDirection: 'column' }}>
          {/* Current step title, only if we have more than 1 step*/}
          {hasSteps && <Title level={3}>{stepTitle}</Title>}

          {started ? (
            <div
              style={{
                //flex: 1,
                //border: '1px solid #ccc',
                //display: 'flex',
                //alignItems: 'center',
                //justifyContent: 'center',
                marginTop: 16
              }}
            >
              {/* module élément */}
              {currentModule?.elementId && (
                <>
                  <FormationElement element={currentModule?.element} isEditing={false} />
                  {/*
                <FormationElementsInBlock
                  refetch={refetch}
                  sections={sections}
                  formationId={formationId}
                  block={block}
                />
                */}
                </>
              )}

              {/* module cours */}
              {isCourseWithSteps && (
                <>
                  {currentBlock?.elements?.length === 0 ? (
                    <Empty description={'Aucun élément dans cette étape'} />
                  ) : (
                    <FormationElement element={currentBlock?.elements?.[0]} isEditing={false} />
                  )}
                </>
              )}

              {isCourseWithoutSteps && (
                <CoursPage coursId={currentModule?.coursId} hideBanner={true} hideBreadCrumb />
              )}
            </div>
          ) : (
            <ModuleIntro label={label} setStarted={setStarted} />
          )}

          <Divider style={{ margin: '16px 0' }} />

          {/* Discussion is already included in classic course, might be added on other types */}
          {/*
          <div>
            <Text strong>Espace Discussion (40)</Text>
          </div>
          */}
        </div>
      </div>
    </div>
  );
};

export default function DoModulePage(props) {
  const { t } = useTranslation();
  useEffectScrollTop();

  // recup depuis params
  const { ueModuleId, ue } = props.match.params;

  return (
    <DoFormationContextProvider ueModuleId={ueModuleId} ueId={ue}>
      <FormationStepView />
    </DoFormationContextProvider>
  );
}
