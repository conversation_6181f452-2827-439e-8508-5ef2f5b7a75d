import { TopicsList } from '@/shared/components/Commentaires/TopicsList';
import { ExoPullToRefresh } from '@/shared/components/ExoPullToRefresh.jsx';
import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';
import { QUERY_ELEMENTS_IN_EVENT } from '@/shared/graphql/cours.js';
import {
  QUERY_BUILDING_BY_ID,
  QUERY_EVENT_BY_ID,
  QUERY_ROOM_BY_ID
} from '@/shared/graphql/events.js';
import { CommentairesType } from '@/shared/services/commentaires.js';
import { isAdmin } from '@/shared/utils/authority.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { useQuery } from '@apollo/client';
import { Button, Divider, Typography } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { EventLeftPanel } from '@/shared/components/Events/EventLeftPanel/EventLeftPanel';
import { EditFilled, MessageOutlined } from '@ant-design/icons';
import { EventRightPanel } from '@/shared/components/Events/EventRightPanel/EventRightPanel';
import useMediaQuery from '@/shared/hooks/useMediaQuery';
import { CreateEditEventModal } from '@/shared/pages/event/admin/components/CreateEditEventModal';
import { stripHtml } from '@/shared/utils/utils';
import { ModalType } from '@/shared/pages/admin/groupes/CreateEditGroupeModal';

export const Building = ({ id, nameOnly = false }) => {
  const { data, loading, error } = useQuery(QUERY_BUILDING_BY_ID, {
    variables: {
      id: id
    },
    fetchPolicy: 'cache-and-network'
  });

  const building = data?.building;

  return (
    <div>
      <span style={{ fontWeight: '700' }}> 🏛️ {building?.name} </span>
      {!nameOnly && (
        <>
          <br />
          📍
          {building?.address || building?.city || building?.country ? (
            <a
              href={`https://www.google.com/maps?q=${encodeURIComponent(
                `${building?.address} ${building?.city} ${building?.country}`
              )}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              {building?.address} {building?.city} {building?.country}
            </a>
          ) : (
            'Adresse non disponible'
          )}
        </>
      )}
    </div>
  );
};

export const BuildingName = ({ id = false }) => {
  const { data, loading, error } = useQuery(QUERY_BUILDING_BY_ID, {
    variables: {
      id: id
    },
    fetchPolicy: 'cache-and-network'
  });

  const building = data?.building;
  return <>{building?.name}</>;
};

export const Room = ({ id, showSeats = true }) => {
  const { t } = useTranslation();
  const { data, loading, error } = useQuery(QUERY_ROOM_BY_ID, {
    variables: {
      id: id
    },
    fetchPolicy: 'cache-and-network'
  });
  const room = data?.room;
  return (
    <div>
      🪑{room?.name}{' '}
      {showSeats && (
        <>
          ({room?.seats} {t('Seats')})
        </>
      )}
    </div>
  );
};

export const RoomName = ({ id }) => {
  const { data, loading, error } = useQuery(QUERY_ROOM_BY_ID, {
    variables: {
      id: id
    },
    fetchPolicy: 'cache-and-network'
  });
  const room = data?.room;
  return <>{room?.name}</>;
};

export const EventPage = ({ eventId, showBreadcrumb = true }) => {
  const { t } = useTranslation();

  const [editExamModalVisible, setEditExamModalVisible] = useState(false);
  /* Query event */
  const { loading, error, data, refetch } = useQuery(QUERY_EVENT_BY_ID, {
    variables: { id: eventId },
    fetchPolicy: 'cache-and-network'
  });
  const event = data?.event;
  const canEdit = isAdmin();
  const isWrapped = useMediaQuery('(max-width: 799px)');

  const {
    data: dataElements,
    loading: loadingElements,
    error: errorElements,
    refetch: refetchElements
  } = useQuery(QUERY_ELEMENTS_IN_EVENT, {
    variables: {
      id: eventId
    },
    fetchPolicy: 'cache-and-network'
  });
  const elements = dataElements?.elementsInEvent;

  const showDiscussions = event?.showDiscussions;

  return (
    <>
      <ExoPullToRefresh
        onRefresh={async () => {
          await refetch();
        }}
      >
        <>
          {showBreadcrumb && (
            <FullMediParticlesBreadCrumb
              title={event?.name}
              subtitle={stripHtml(event?.description)}
              image={event?.image || null}
              imageType={'event'}
              actionButton={
                canEdit && (
                  <Button
                    shape="circle"
                    ghost
                    onClick={() => setEditExamModalVisible(true)}
                    icon={<EditFilled />}
                  />
                )
              }
            />
          )}
          <ExoteachLayout>
            {loading && !event ? (
              <SpinnerCentered />
            ) : (
              <div style={{ marginTop: 8, padding: 8 }}>
                <div
                  style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '5px'
                  }}
                >
                  <EventLeftPanel canEdit={canEdit} event={event} refetch={refetch} />

                  {!isWrapped && <Divider type="vertical" style={{ height: 'auto' }} />}

                  {(elements?.length > 0 || event?.coursIds?.length > 0) && (
                    <EventRightPanel
                      key={event?.id}
                      event={event}
                      elements={elements}
                      refetch={refetch}
                    />
                  )}
                </div>
              </div>
            )}

            {showDiscussions && !loading && event?.id && (
              <>
                <Divider orientation={'center'}>
                  <Typography.Title level={2}>
                    {t('DiscussionSpace')} <MessageOutlined />
                  </Typography.Title>
                </Divider>
                <TopicsList id={event.id} type={CommentairesType.EVENT} />
              </>
            )}
            {event && canEdit && (
              <CreateEditEventModal
                event={event}
                folderId={event?.folderId}
                isModalVisible={editExamModalVisible}
                refetch={refetch}
                modalType={ModalType.UPDATE}
                closeModalHandler={() => {
                  setEditExamModalVisible(false);
                  refetch(); // Load new modifications
                }}
              />
            )}
          </ExoteachLayout>
        </>
      </ExoPullToRefresh>
    </>
  );
};

export default function EventPageWrapper(props) {
  useEffectScrollTop();
  const { eventId } = props.match.params;
  return <EventPage key={eventId} eventId={eventId} />;
}
