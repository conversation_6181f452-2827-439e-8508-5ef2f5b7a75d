import dayjs from 'dayjs';
import React, { useState, useEffect } from 'react';
import { List, Button, DatePicker, Modal, Form, message, Typography } from 'antd';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;
const { confirm } = Modal;

const ExceptionManager = ({ exceptions, setExceptions }) => {
  const { t } = useTranslation();
  const [localExceptions, setLocalExceptions] = useState(() => exceptions || []);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingIndex, setEditingIndex] = useState(null);
  const [form] = Form.useForm();

  // S'assurer que `localExceptions` reste synchronisé avec `exceptions`
  useEffect(() => {
    if (exceptions !== undefined) {
      setLocalExceptions(exceptions);
    }
  }, [exceptions]);

  console.log({ exceptions, localExceptions });

  // Ajoute une exception
  const handleOk = () => {
    form.validateFields().then((values) => {
      const dateString = values.date.format('YYYY-MM-DD');
      const newExceptions = localExceptions ? [...localExceptions] : [];

      // Vérifie si l'exception existe déjà
      if (editingIndex === null && newExceptions.includes(dateString)) {
        message.warning('Cette exception existe déjà.');
        return;
      }

      if (editingIndex !== null) {
        newExceptions[editingIndex] = dateString;
      } else {
        newExceptions.push(dateString);
      }

      setLocalExceptions(newExceptions);
      setExceptions([...newExceptions]); // Propagation vers le parent
      setIsModalVisible(false);
      message.success(editingIndex !== null ? 'Exception modifiée' : 'Exception ajoutée');
    });
  };

  // Supprime une exception
  const handleDelete = (index) => {
    confirm({
      title: 'Supprimer cette exception ?',
      icon: <ExclamationCircleOutlined />,
      content: 'Cette action est irréversible.',
      okText: 'Oui',
      okType: 'danger',
      cancelText: 'Non',
      onOk() {
        const newExceptions = localExceptions.filter((_, i) => i !== index);
        setLocalExceptions(newExceptions);
        setExceptions([...newExceptions]); // Mise à jour vers le parent
        message.success('Exception supprimée');
      }
    });
  };

  return (
    <>
      <List
        bordered
        dataSource={localExceptions}
        style={{ width: '100%' }}
        locale={{ emptyText: t('date.NoExceptionYet') }}
        renderItem={(item, index) => (
          <List.Item
            actions={[
              <Button
                type="text"
                key={1}
                icon={<EditOutlined />}
                onClick={() => {
                  setEditingIndex(index);
                  form.setFieldsValue({ date: dayjs(localExceptions[index]) });
                  setIsModalVisible(true);
                }}
              />,
              <Button
                type="text"
                key={2}
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(index)}
              />
            ]}
          >
            <Text>{dayjs(item).format('DD/MM/YYYY')}</Text>
          </List.Item>
        )}
      />

      <Button
        type="dashed"
        onClick={() => {
          setEditingIndex(null);
          form.resetFields();
          setIsModalVisible(true);
        }}
        icon={<PlusOutlined />}
        style={{ width: '100%', marginTop: 16 }}
      >
        {t('date.AddException')}
      </Button>

      <Modal
        title={editingIndex !== null ? t('date.EditException') : t('date.AddException')}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={() => setIsModalVisible(false)}
        okText={t('general.save')}
        cancelText={t('Cancel')}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="date"
            label={t('date.ExceptionDate')}
            rules={[{ required: true, message: t('date.PleaseSelectDate') }]}
          >
            <DatePicker format="DD/MM/YYYY" style={{ width: '100%' }} allowClear={false} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ExceptionManager;
