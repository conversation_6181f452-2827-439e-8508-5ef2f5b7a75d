/*@import '~antd/es/style/themes/default.less';*/

.logo{
  width: 55%;
  margin-top: 40px;
  margin-left: auto;
  margin-right: auto;
}

.logoRopsten {
  width: 100%;
  margin-top: 50px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50px;
}

.logoAptoria {
  width: 100%;
  margin: auto;
  margin-bottom: 50px;
}
.logoAptoria img {
  display: block;
  width: 100%;
  max-width: 300px;
  height: auto;
  margin: auto;
}


.logo img {
  display: block;
  width: 100%;
  max-width: 230px;
  height: auto;
  margin: auto;
}

.logoRopsten img {
  display: block;
  width: 100%;
  max-width: 300px;
  height: auto;
  margin: auto;
}

.title {
  color: white;
  font-weight: 200;
  font-size: 50px;
  //font-family: "Helvetica Neue", serif;
  line-height: normal;
  text-align: center;
}

.main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  //width: 100%;
  //height: 100vh;

  .icon {
    margin-left: 16px;
    color: rgba(0, 0, 0, 0.2);
    font-size: 24px;
    vertical-align: middle;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: @primary-color;
    }
  }

  .titreForm{
    color : white;
    text-align: center;
  }

  .divLogo {
    display: flex;
    justify-content: center;
  }

  .connexionAuto{
    display: inline-block;
    color : white;
    font-size: medium;
  }

  .other {
    display: flex;
    color : white;
    margin-top: 24px;
    line-height: 22px;
  }

  .register {
    color: white;
    text-decoration: underline;
    text-underline: white;
  }

  :global {
    .antd-pro-login-submit {
      width: 100%;
      margin-top: 24px;
    }
  }
}
