import { GET_CONFIG } from '@/shared/graphql/home.js';
import { CONFIG_KEYS, DEFAULT_CONFIG_VARIABLES, getPublicSrc } from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import {
  GlobalConfig,
  isAptoria, recaptchaSiteKey,
  showGqlErrorsInMessagePopupFromException, tryParseJSONObject,
} from '@/shared/utils/utils.js';
import { Alert, Button, Card, Checkbox, Input } from 'antd';

import React, { useState , useRef } from 'react';
import router from 'umi/router';
import { connect } from 'dva';
import styles from './style.less';
import { useMutation, useQuery } from '@apollo/client';
import Form from 'antd/es/form';
import UserOutlined from '@ant-design/icons/lib/icons/UserOutlined';
import LockOutlined from '@ant-design/icons/lib/icons/LockOutlined';
import { setAuthToken, setUserInfo } from '@/shared/utils/authority';
import {RECAPTCHA_LOGIN_MUTATION } from '@/shared/graphql/user';
import { useTranslation } from 'react-i18next';
import ReCAPTCHA from "react-google-recaptcha" ;

const LoginMessage = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24,
    }}
    message={content}
    type="error"
    showIcon
  />
);

const Login = props => {
  const { dispatch } = props;
  const [type, setType] = useState('account');
  const [sendLogin, { loading, error, data }] = useMutation(RECAPTCHA_LOGIN_MUTATION);
  const { t } = useTranslation();

  const {
    loading: loadingConfig,
    error: errorConfig,
    data: dataConfig,
    refetch: refetchConfig,
  } = useQuery(GET_CONFIG, { fetchPolicy: 'cache-and-network', ...DEFAULT_CONFIG_VARIABLES });
  const config = dataConfig?.config;
  const webSiteName = getValueFromKeyConfigData(config, CONFIG_KEYS.WEBSITE_NAME);
  const logoLoginPage = getValueFromKeyConfigData(config, CONFIG_KEYS.LOGO_LOGINPAGE);

  const appearance = tryParseJSONObject(getValueFromKeyConfigData(config, CONFIG_KEYS.APPEARANCE));
  const showPlatformNameLoginPage = appearance?.showPlatformNameLoginPage;
  const showLogoLoginPage = appearance?.showLogoLoginPage;


  const handleForgotPasswordButton = async () => {
    router.push('forgot-password');
  };

  const getLogo = () => {
    return getPublicSrc(logoLoginPage);
  };

  const getClassNameLogo = () => {
    if (GlobalConfig.get().appName === 'Aptoria') {
      return styles.logoAptoria;
    }
    return styles.logo;
  };

  const getNewAccountLink = () => {
    if (isAptoria) {
      return '#/user/register';
    }
    return `${GlobalConfig.get().baseWebsiteUrl}inscription`;
  };

  const recaptchaRef = useRef(); // On créé une référence qui sera lié au composant reCaptcha

  const verifyRecaptchaTokenRework=async ()=>{
    /* fonction async qui gère l'objet reCaptcha afin de générer un nouveau token valide */
    recaptchaRef.current.reset(); // reset du module recaptcha car un module peut générer uniquement un captcha token
    const tempToken=await recaptchaRef.current.executeAsync() // Creation du token
    return tempToken // return du token
  }

  // First get token
  const handleSubmitRecaptcha = async ({login, password, e }) => {
    /* Fonction de submit du form avec la génération de token reCaptcha + vérification de celui-ci */
    try {
      const recaptchaToken=await verifyRecaptchaTokenRework()

      const res = await sendLogin({variables: {recaptchaToken,login, password}});
      if (res && res.data.signInWithUser.token) {
        await setAuthToken(res.data.signInWithUser.token);
        await setUserInfo(res.data.signInWithUser.me);

        dispatch({
          type: 'user/login',
          payload: {...res.data.signInWithUser.me, type},
        });
      } else {
        console.error('no token');
      }
    } catch (e) {
      console.error(e);
      if (e.graphQLErrors)
        showGqlErrorsInMessagePopupFromException(e);
    }
  };


  return (
    <div
      className={styles.main}
    >
      <Card style={{ background: '#0000002b', border: 'none', marginTop: '55px' }}>
        <div style={{ position: 'relative', maxWidth: 307 }}>
          <>
            {showPlatformNameLoginPage && (
              <div className={styles.title}>
                {webSiteName}
              </div>
            )}
          </>
          {showLogoLoginPage ? (
            <div className={getClassNameLogo()}>
              {/* eslint-disable-next-line no-undef */}
              <img alt={t('general.logo')} src={getLogo()}/>
            </div>
          ) : (
            <div style={{marginBottom: '128px'}}></div>
          )}
        </div>

        <div>
          <Form
            name="basic"
            onFinish={handleSubmitRecaptcha} // la fonction qui va être appelée lorsque l'on va cliquer sur le boutton html 'submit'
            initialValues={{ remember: true }}
            style={{ maxWidth: 307 }}
          >
            <br />
            <br />
            {error && !loading && (
              <LoginMessage content={t('login.error')}/>
            )}
            <Form.Item name="login" rules={[{ required: true, message: t('login.enterYourLogin') }]}>
              <Input size="large" prefix={<UserOutlined/>} placeholder={t('login.username')}/>
            </Form.Item>
            <Form.Item name="password" rules={[{ required: true, message: t('login.enterYourPassword') }]}>
              <Input.Password size="large" prefix={<LockOutlined/>} placeholder={t('password')}/>
            </Form.Item>
            <div>
              <Form.Item name="remember" valuePropName="checked">
                <Checkbox>
                  <div className={styles.connexionAuto}>{t('login.autoConnect')} </div>
                </Checkbox>
              </Form.Item>
            </div>
            <Form.Item>

              <Button size="large" type="primary" block htmlType="submit" loading={loading}> {t('Login')} </Button>
              <ReCAPTCHA
                ref={recaptchaRef}
                size="invisible"
                sitekey={recaptchaSiteKey}
              />

              <div style={{ marginTop: 20 }}>
                <hr/>
              </div>

              <React.Fragment>
                <Button size="medium" type="link" block href={getNewAccountLink()}>
                  <span className={styles.register}>{t('login.subscribeMessage')}</span>
                </Button>
                <Button size="medium" type="link" block onClick={handleForgotPasswordButton}>
                  <span className={styles.register}>{t('login.forgotPassword')}</span>
                </Button>
              </React.Fragment>
            </Form.Item>
          </Form>
        </div>
      </Card>
    </div>
  );
};

export default connect(({ user }) => ({ user }))(Login);
