import { GET_ME } from '@/shared/models/user.js'
import { PageLoading } from '@ant-design/pro-layout'
import { useQuery } from '@apollo/client'
import React, { useEffect } from 'react'
import { Redirect } from 'umi/index.js'

export default function(props) {
  const { loading, data: { me } = {}, error } = useQuery(GET_ME, { fetchPolicy: 'no-cache' })

  useEffect(() => {
    if (!loading && !error && me == null) { // me is null
      console.log('legacy session me null');
      return <Redirect to="/user/login"/>
    }
  }, [error, loading, me])

  return (
    <PageLoading />
  )
}
