import { getPublicSrc } from '@/shared/services/config.js';
import { displayDirectHtml } from '@/shared/utils/utils.js';
import { gql, useMutation, useQuery } from '@apollo/client';
import { Button, Col, Layout, Row, Statistic, message, Divider, Result } from 'antd';
import Card from 'antd/es/card';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import router from 'umi/router';
import { loadStripe } from '@stripe/stripe-js';

const QUERY_FORFAIT_SELECTED = gql`
  query forfaitBeforePayment($id: ID!) {
    forfaitBeforePayment(id: $id)
  }
`;

// Log clicked on pay later
const MUTATION_PAY_LATER = gql`
  mutation payLater($id: ID!) {
    payLater(id: $id)
  }
`;

// Create or Update user
const MUTATION_CREATE_UPDATE_USER = gql`
  mutation createFreeUser($id: ID!) {
    createFreeUser(id: $id) {
      id
    }
  }
`;

/**
 * Avant paiement Stripe, récapitulatif de la commande
 *
 * @param props
 * @returns {JSX.Element}
 */
export default function(props) {
  // Query
  const { t } = useTranslation();
  const id = props.match.params.id;
  const { data, error, loading, refetch } = useQuery(QUERY_FORFAIT_SELECTED, { variables: { id } });
  const [mutationCreateUser, { loading: loadingMut }] = useMutation(MUTATION_CREATE_UPDATE_USER, {
    variables: { id },
  });

  const [mutationPayLater, { loading: loadingMutPayLater }] = useMutation(MUTATION_PAY_LATER, {
    variables: { id },
  });

  const userData = data?.forfaitBeforePayment?.log;
  const paymentType = userData?.paymentType; // 'once' or 'installments'
  const publicKey = data?.forfaitBeforePayment?.publicKey;

  const enableDeferredPayment = data?.forfaitBeforePayment?.enableDeferredPayment;
  const differedPaymentTexts = data?.forfaitBeforePayment?.differedPaymentTexts;

  const [loadingCheckout, setIsLoadingCheckout] = useState(false);
  const numberOfInstallments = userData?.numberOfInstallments;

  const [payLaterSuccess, setPayLaterSuccess] = useState(false);
  const onPayLater = async () => {
    try {
      await mutationPayLater();
      setPayLaterSuccess(true);
    } catch (e) {
      console.error(e);
    }
  }

  /* Bouton payer */
  const handleFinish = async () => {
    // Price = 0 => FREE
    if (userData.price === 0) {
      // FREE
      setIsLoadingCheckout(true);
      // Free user
      const result = await mutationCreateUser();
      if (result?.data?.createFreeUser?.id) {
        setIsLoadingCheckout(false);
        router.push('/user/register/success');
      } else {
        alert('Un problème est survenu, veuillez contacter un administrateur');
      }
    } else {
      // PAID
      setIsLoadingCheckout(true);
      // Get Stripe.js instance
      const stripePromise = loadStripe(publicKey || '');
      const stripe = await stripePromise;
      // Call your backend to create the Checkout Session
      // eslint-disable-next-line no-undef
      const response = await fetch(getPublicSrc(`stripe/create-checkout-session/${id}`), {
        method: 'POST',
      });
      const session = await response.json();
      // When the customer clicks on the button, redirect them to Checkout.
      const result = await stripe.redirectToCheckout({
        sessionId: session.id,
      });
      if (result.error) {
        // If `redirectToCheckout` fails due to a browser or network
        // error, display the localized error message to your customer
        // using `result.error.message`.
        message.error(`Erreur lors de la création de session de paiement: ${result.error.message}`);
        setIsLoadingCheckout(false);
      }
    }
  };


  if(payLaterSuccess) {
    return (
        <Layout>
          <br />
          &nbsp;
          <Row justify="center" type="flex" key="1">
            <Col xl={12} lg={12} md={12} sm={22} xs={22}>
              <Card title={t('OrderConfirmation')}>
                <Result status="success">
                  Votre demande de paiement différé a bien été prise en compte !
                </Result>
                <br />
                {differedPaymentTexts?.map((text, index) => (
                  <div key={index}>
                    {displayDirectHtml(text)}
                  </div>
                ))}
              </Card>
            </Col>
          </Row>
        </Layout>
    )
  }

  return (
    <Layout>
      <br />
      &nbsp;
      <Row justify="center" type="flex" key="1">
        <Col xl={12} lg={12} md={12} sm={22} xs={22}>
          <Card title={t('OrderConfirmation')}>
            {userData && (
              <div>
                <p>
                  {t('general.Username')}: {userData.login}
                </p>
                <p>
                  {t('general.email')}: {userData.email}
                </p>
                <br />

                {userData.forfaitsText}
                <br />
                <br />
                {userData.price !== 0 && (
                  <Statistic
                    title={t('general.total')}
                    value={userData.price}
                    precision={2}
                    suffix="€"
                  />
                )}

                {paymentType === 'installments' && (
                  <b>
                    <p>
                    Règlement en plusieurs fois : ({numberOfInstallments} x {(userData.price / numberOfInstallments)?.toFixed(2)}€ mensuel)
                    </p>
                    <p style={{fontSize: '20px'}}>
                    À payer maintenant: {(userData.price / numberOfInstallments)?.toFixed(2)}€
                    </p>
                    <p style={{fontSize: '11px'}}>
                      Rappel : Dès le paiement de la première échéance, vous vous engagez à régler la totalité de la somme due
                    </p>
                  </b>
                )}

                <br />

                <Button
                  size="large"
                  type="primary"
                  block
                  htmlType="submit"
                  loading={loadingMut || loadingCheckout}
                  onClick={handleFinish}
                >
                  {userData?.price === 0 ? t('general.Continue') : (
                    <>
                      {paymentType === 'installments' ? t('PayByInstallments') : t('PayTotal')}
                    </>
                  )}

                </Button>

                {enableDeferredPayment && (
                  <div style={{marginTop: '24px'}}>
                    <Divider orientation={'left'}>
                      {t('PayLater')}
                    </Divider>

                    {differedPaymentTexts?.map((text, index) => (
                      <div key={index}>
                        {displayDirectHtml(text)}
                      </div>
                    ))}

                    <Button
                      size="large"
                      block
                      htmlType="submit"
                      loading={loadingMut || loadingCheckout || loadingMutPayLater}
                      onClick={onPayLater}
                    >
                      {t('PayLater')}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </Card>
        </Col>
      </Row>
      <Layout.Footer></Layout.Footer>
    </Layout>
  );
}
