/* @import '~antd/es/style/themes/default.less'; */

.main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;

  .form{
    width : 80vw;
    max-width: 400px;
  }

  h3 {
    margin-bottom: 20px;
    font-size: 16px;
  }

  .password {
    margin-bottom: 24px;
    :global {
      .ant-form-item-explain {
        display: none;
      }
    }
  }

  .getCaptcha {
    display: block;
    width: 100%;
  }

  .submit {
    display: flex;
    justify-content: center;
    text-align: center;
    width: 50%;
    margin:auto;
  }

  .login {
    color: white;
    line-height: @btn-height-lg;
  display: flex;
    justify-content: center;

  }
}

.success,
.warning,
.error {
  transition: color 0.3s;
}

.success {
  color: @success-color;
}

.warning {
  color: @warning-color;
}

.error {
  color: @error-color;
}

.progress-pass > .progress {
  :global {
    .ant-progress-bg {
      background-color: @warning-color;
    }
  }
}
