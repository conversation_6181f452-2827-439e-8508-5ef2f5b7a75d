import { GET_CONFIG } from '@/shared/graphql/home.js';
import { UpdateForfait } from '@/shared/pages/account/forfait/UpdateForfait.jsx';
import { RegisterFormItems } from '@/shared/pages/user/register/components/RegisterFormItems.jsx';
import { CONFIG_KEYS, DEFAULT_CONFIG_VARIABLES, getPublicSrc } from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import { useQuery } from '@apollo/client';
import { Layout, Form, Button, Col, Row } from 'antd';
import Menu from 'antd/es/menu';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Link from 'umi/link';
import styles from './style.less';

// NOT USED
export const RegisterForm = props => {
  const { t } = useTranslation();
  const [count, setcount] = useState(0);
  const [form] = Form.useForm();
  const onFinish = values => {};

  return (
    <div>
      <Form form={form} name="UserRegister" onFinish={onFinish}>
        <RegisterFormItems form={form} />

        <Form.Item>
          <Button
            size="large"
            // loading={submitting}
            className={styles.submit}
            type="primary"
            htmlType="submit"
          >
            S'inscrire
          </Button>
          <div style={{ marginTop: 20 }}>
            <hr></hr>
          </div>
          <Link className={styles.login} to="/user/login">
            {t('AlreadyHaveAccount')}
          </Link>
        </Form.Item>
      </Form>
    </div>
  );
};

const Register = props => {
  const [current, setCurrent] = useState('1');
  const { t } = useTranslation();

  const {
    loading: loadingConfig,
    error: errorConfig,
    data: dataConfig,
    refetch: refetchConfig,
  } = useQuery(GET_CONFIG, { fetchPolicy: 'cache-and-network', ...DEFAULT_CONFIG_VARIABLES });
  const config = dataConfig?.config;
  const webSiteName = getValueFromKeyConfigData(config, CONFIG_KEYS.WEBSITE_NAME);
  const logoInMenuBar = getValueFromKeyConfigData(config, CONFIG_KEYS.LOGO_MENUBAR);


  return (
    <Layout>
      <Menu theme="dark" mode="horizontal" onClick={s => {}} selectedKeys={[current]}>
        <Menu.Item key="1">{t('general.signUp')}</Menu.Item>
      </Menu>
      {/* Platform logo */}
      <div style={{textAlign: 'center', marginTop: '24px', marginBottom: '40px'}}>
        <img src={getPublicSrc(logoInMenuBar)} alt={'logo'} style={{maxWidth: '64px'}}/>
      </div>

      <UpdateForfait withRegisterForm showChoiceSummary />

      <Layout.Footer></Layout.Footer>
    </Layout>
  );
};

export default Register;
