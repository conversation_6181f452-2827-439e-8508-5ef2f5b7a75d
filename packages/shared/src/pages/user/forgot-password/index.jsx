import { GET_CONFIG } from '@/shared/graphql/home.js';
import { CONFIG_KEYS, DEFAULT_CONFIG_VARIABLES } from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { Alert, Button, Card, Input, message } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'umi';
import router from 'umi/router';
import styles from './style.less';
import { useMutation, useQuery } from '@apollo/client';
import Form from 'antd/es/form';
import UserOutlined from '@ant-design/icons/lib/icons/UserOutlined';
import { PASSWORD_RESET_MUTATION } from '@/shared/graphql/user';

const LoginMessage = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24
    }}
    message={content}
    type="error"
    showIcon
  />
);

export default function ForgotPassword(props) {
  const { dispatch } = props;
  const [sendPasswordReset, { loading, error, data }] = useMutation(PASSWORD_RESET_MUTATION);
  const { t } = useTranslation();

  const {
    loading: loadingConfig,
    error: errorConfig,
    data: dataConfig,
    refetch: refetchConfig
  } = useQuery(GET_CONFIG, { fetchPolicy: 'cache-and-network', ...DEFAULT_CONFIG_VARIABLES });
  const config = dataConfig?.config;
  const webSiteName = getValueFromKeyConfigData(config, CONFIG_KEYS.WEBSITE_NAME);

  // First get token
  const handleSubmit = async ({ email }) => {
    try {
      const res = await sendPasswordReset({ variables: { email }, fetchPolicy: 'no-cache' });
      if (res.data.forgotPassword) {
        if (res.data.forgotPassword == 'ok')
          message.success('Un email vous a été envoyé. Veuillez consulter votre boîte mail.');
        else if (res.data.forgotPassword == 'User not found') {
          message.error('Erreur: Aucun utilisateur ne correspond à cette addresse email');
        } else if (res.data.forgotPassword == 'ko') {
          message.error('Erreur: Les services sont indisponibles. Veuillez réessayer plus tard.');
        } else console.error(error);
      }
    } catch (e) {
      console.error(e);
      if (e.graphQLErrors) showGqlErrorsInMessagePopupFromException(e);
    }
  };

  return (
    <div className={styles.main}>
      <Card style={{ background: '#0000002b', border: 'none' }}>
        <div style={{ position: 'relative' }}>
          <Link to="/">
            <div className={styles.title}>{webSiteName}</div>
          </Link>
          <div className={styles.logo}>{/* eslint-disable-next-line no-undef */}</div>
        </div>
        <div>
          <Form name="basic" onFinish={handleSubmit} initialValues={{ remember: true }}>
            <div className={styles.titreForm}>{t('login.tellYourEmail')}</div>
            {error && !loading && <LoginMessage content={t('login.forgotPasswordError')} />}
            <Form.Item
              name="email"
              rules={[{ required: true, message: t('login.enterYourEmail') }]}
            >
              <Input size="large" prefix={<UserOutlined />} placeholder={t('email')} />
            </Form.Item>
            <Form.Item>
              <Button size="large" type="primary" block htmlType="submit" loading={loading}>
                {t('send')}
              </Button>
              <Button style={{ marginTop: 24 }} block onClick={() => router.goBack()}>
                {t('back')}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </Card>
    </div>
  );
}
