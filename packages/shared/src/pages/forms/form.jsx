import { ErrorResult } from '@/shared/components/ErrorResult.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { QUERY_FORM_BY_UUID_FOR_USER } from '@/shared/graphql/forms.js';
import InputElementsInFormElement from '@/shared/pages/formations/components/InputElementsInFormElement.jsx';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { useQuery } from '@apollo/client';
import { Alert, Button } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import router from 'umi/router.js';

export default function(props) {
  const { t } = useTranslation();
  useEffectScrollTop();
  const uuid = props.match.params.uuid;
  const { data, loading, error, refetch } = useQuery(QUERY_FORM_BY_UUID_FOR_USER, {
    fetchPolicy: 'no-cache',
    variables: {
      uuid,
    },
  });
  const form = data?.form;
  const canDoForm = (form?.oneAnswerByUser && !form?.completedByMe) || !form?.oneAnswerByUser;

  return (
    <>
      <FullMediParticlesBreadCrumb title={!loading ? form?.name : t('general.Loading...')} />
      {!loading && error ? <ErrorResult refetch={refetch} error={error} /> : ''}
      {loading && <SpinnerCentered />}

      <div
        style={{
          position: 'relative',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-evenly',
          alignItems: 'center',
          width: '80%',
          marginLeft: 'auto',
          marginRight: 'auto',
          marginBottom: '32px',
          marginTop: '32px',
          flexWrap: 'wrap',
          gap: '10px',
        }}
      >
        <div>
          {canDoForm ? (
            <div style={{ marginTop: 64 }}>
              <InputElementsInFormElement formId={form?.id} showBackToHome />
            </div>
          ) : (
            <>
              <Alert
                message={t('FormAlreadyAnswered')}
                description={t('YouCantAnswerFormAgain')}
                type="info"
                showIcon
              />
              <br />
              <Button type="primary" onClick={() => router.push('/')}>
                {t('backToHome')}
              </Button>
            </>
          )}
        </div>
      </div>
    </>
  );
}
