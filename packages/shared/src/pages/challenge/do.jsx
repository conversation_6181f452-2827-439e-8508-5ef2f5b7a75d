import { ErrorResult } from '@/shared/components/ErrorResult.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { QUERY_CHALLENGE } from '@/shared/graphql/challenges.js';
import { DoMcqContextProvider } from '@/shared/pages/qcm/context/DoMcqContext.jsx';
import { DoQuestionInSession } from '@/shared/pages/qcm/doMcq/DoQuestionInSession.jsx';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { useQuery } from '@apollo/client';
import React from 'react';
import { useTranslation } from 'react-i18next';

// Do challenge using session
export default function(props) {
  useEffectScrollTop();
  const { t } = useTranslation();
  const { challengeId, sessionId } = props.match.params;

  const { loading, error, data, refetch } = useQuery(QUERY_CHALLENGE, {
    variables: {
      id: challengeId,
    },
    fetchPolicy: 'cache-and-network',
  });
  const challenge = data?.challenge;

  return (
    <React.Fragment>
      <FullMediParticlesBreadCrumb title={!loading ? challenge?.name : t('general.Loading...')}/>
      {error && (<ErrorResult error={error} loading={loading} refetch={refetch}/>)}

      {sessionId && (
        <DoMcqContextProvider>
          <DoQuestionInSession
            sessionId={sessionId}
            //qcm={qcm}
            correctionInSamePage
            challenge={challenge}
            setCurrentSessionId={() => {
            }}
          />
        </DoMcqContextProvider>
      )}

    </React.Fragment>
  );
}