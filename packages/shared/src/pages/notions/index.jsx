import { ErrorResult } from '@/shared/components/ErrorResult.jsx'
import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx'
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx'
import { NotionTag } from '@/shared/components/Notion/NotionTag.jsx'
import { SEARCH_NOTIONS_WITH_COURS_QUESTIONS_LIGHT } from '@/shared/graphql/notions.js'
import { useQuery } from '@apollo/client'
import { PageHeader } from '@ant-design/pro-layout';
import { Button, Col, Input, Popover, Row, Table, Tag, Tooltip } from 'antd';
import React, { useState } from 'react'
import { router } from 'umi'
import { useTranslation } from 'react-i18next';

export default function(props) {
  const {t} = useTranslation();
  const defaultPageSize = 30
  const [filter, setFilter] = useState({
    limit: defaultPageSize,
    offset: 0,
  })
  const { loading, error, data, refetch } = useQuery(SEARCH_NOTIONS_WITH_COURS_QUESTIONS_LIGHT, {
    fetchPolicy: 'cache-and-network',
    variables: { filter: filter || {} },
  })

  const columns = [
    {
      title: 'Notion',
      dataIndex: 'name',
      key: 'name',
      render: (name, notion) => (
        <Tooltip title={t('SeeDetails')}>
          <NotionTag notion={notion}/>
        </Tooltip>
      ),
    },
    {
      title: 'Cours contenant cette notion',
      dataIndex: 'cours',
      key: 'cours',
      hideInSearch: true,
      render: (cours, record) => (
        <Popover content={
          <>
            {cours && cours.map(c => <Tag key={c.id}>{c.name} {c.text}</Tag>) || '(Aucun)'}
          </>
        }>
          <Button size="small" type="link">
            {cours && cours.length || '0'}
          </Button>
        </Popover>
      ),
    },
    {
      title: 'Questions contenant cette notion',
      dataIndex: 'questions',
      key: 'questions',
      hideInSearch: true,
      render: (questions, record) => (
        <Popover content={
          <>
          </>
        }>
          <Button size="small" type="link">
            {questions && questions.length || '0'}
          </Button>
        </Popover>
      ),
    },
    {
      title: t('masteryLevel'),
      dataIndex: 'keywords',
      key: 'keywords',
      sorter: (a, b) => a.id - b.id,
      render: (keywords, record) => (
        <>
          {record.id}%
        </>
      ),
    },
  ]

  const getTableDataSource = () => {
    return data && data.searchNotions?.notions && data.searchNotions?.notions?.map((notion, key) => ({
      ...notion,
      key,
    }))
  }
  const totalResults = data?.searchNotions?.count

  const onSearch = (name) => {
    setFilter({ name })
  }
  return (

    <>
      <FullMediParticlesBreadCrumb title={t('Notions')}/>
      <ExoteachLayout>
        <PageHeader
          onBack={() => router.goBack()}
          title={t('Notions')}
        />
        <Row justify="center" type="flex" key="1">
          <Col xl={12} lg={12} md={12} sm={22} xs={22}>
            <Input.Search
              enterButton
              allowClear
              onSearch={onSearch}
              size="large"
              placeholder={t('SearchNotions')}
            />
          </Col>
        </Row>

        <br/>

        {!error && (
          <>
            <Table
              loading={loading}
              columns={columns}
              dataSource={getTableDataSource()}
              pagination={{
                defaultCurrent: 1,
                pageSize: defaultPageSize,
                showTotal: (total) => `${total} résultats`,
                onChange: (page, pageSize) => {
                  setFilter({
                    ...filter,
                    limit: pageSize,
                    offset: (page - 1) * pageSize,
                  })
                },
                total: totalResults,
                size: 'large',
                responsive: true,
              }}
              onSubmit={(params) => {
                setFilter(params)
              }}
              search={{
                labelWidth: 'auto',
              }}
              showHeader
              scroll={{ x: true }}
            />
          </>
        )}

        {error && !loading && (<ErrorResult refetch={refetch} error={error}/>)}
      </ExoteachLayout>
    </>
  )
}
