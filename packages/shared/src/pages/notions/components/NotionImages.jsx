import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils.js';
import { Image } from 'antd'
import React from 'react'

export default function({ images }) {

  return (
    <div
      className="image-gallery"
      style={{
        display: 'flex', flexWrap: 'wrap',
      }}
    >
      {images?.map(image => (
        <div style={{ textAlign: 'center' }}>
          <Image
            title={image?.name}
            width={160}
            //style={{ height: '150px' }}
            src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + image.file)}
            //alt={image?.name}
          />
          <div className="image-description">{image?.name}</div>
        </div>
      ))}

    </div>
  )
}