import { QUERY_MON_PLANNING_BETWEEN } from '@/shared/graphql/edt.js';
import useLocalStorage from '@/shared/hooks/useLocalStorage.js';
import { GET_ME } from '@/shared/models/user.js';
import EventAgendaItem from '@/shared/pages/planning/components/EventAgendaItem.jsx';
import { getMobiscrollPlanningFromData } from '@/shared/pages/planning/getMobiscrollPlanningFromData.js';
import { getMobiScrollLanguage, getPlanningLink } from '@/shared/pages/planning/index.jsx';
import { isMobile, TimeZoneManager } from '@/shared/utils/utils.js';
import { useLazyQuery, useQuery } from '@apollo/client';
import React, { useEffect, useState } from 'react';
import { router } from 'umi';
import dayjs from 'dayjs';
import moment from 'moment-timezone';

import {
  Eventcalendar,
  CalendarNav,
  Button,
  CalendarToday,
  momentTimezone
} from '@mobiscroll/react';
import { useTranslation } from 'react-i18next';

momentTimezone.moment = moment;

const calculateBeginning = (date) => dayjs(date).subtract('30', 'days');
const calculateEnd = (date) => dayjs(date).add('30', 'days');

export default function PlanningHomepage(props) {
  const { t, i18n } = useTranslation();
  const { language } = i18n;
  const [begin, setBegin] = useState(calculateBeginning(dayjs()));
  const [end, setEnd] = useState(calculateEnd(dayjs()));
  const [hasStarted, setStarted] = useState(false);
  const [showTimeZoneSelect, setShowTimeZoneSelect] = useState(false);
  const [currentTimeZone, setCurrentTimezone] = useLocalStorage(
    TimeZoneManager.getLocalStorageTimeZoneKey(),
    TimeZoneManager.getDefaultTimeZone()
  );

  const CALENDAR_VIEW_SCHEDULE_WEEK = {
    calendar: { type: 'week' },
    agenda: { type: 'day' }
  };

  const [view, setView] = useState('schedule'); //  calendar, scheduleByGroups, schedule
  const [currentDate, setCurrentDate] = useState(new Date());
  const [groupsView, setGroupsView] = useState(false);
  const [calView, setCalView] = useState(CALENDAR_VIEW_SCHEDULE_WEEK);
  const { data: { me } = {} } = useQuery(GET_ME, { fetchPolicy: 'cache-first' });

  /* Queries */
  const [queryPlanning, { loading, error, data, refetch }] = useLazyQuery(
    QUERY_MON_PLANNING_BETWEEN,
    {
      variables: {
        begin: begin.toDate(),
        end: end.toDate(),
        myPlanning: true,
        disabledExternalCalendars: ['6', '1']
      },
      skip: !begin && !end,
      fetchPolicy: 'cache-and-network'
    }
  );

  const [mobiscrollDataPlanning, setMobiscrollDataPlanning] = React.useState([]);
  /* Load mobiscroll planning from data */
  useEffect(() => {
    const { dataPlanning, datesToAdd } = getMobiscrollPlanningFromData(
      t,
      view,
      data?.getPlanning,
      [],
      currentTimeZone
    );
    setMobiscrollDataPlanning([...dataPlanning, ...datesToAdd]);
  }, [data, view]);

  /* Load fresh data */
  const onPageLoading = (event, inst) => {
    const firstDay = event?.firstDay;
    const lastDay = event?.lastDay;
    // Pour fetch les évènements 30 jours avant, 30 jours après, pour prendre en compte les évènements longs (ex: vacances, révisions)
    setBegin(dayjs(firstDay).subtract(30, 'days'));
    setEnd(dayjs(lastDay).add(30, 'days'));
    queryPlanning();
  };

  useEffect(() => {
    if (!hasStarted) {
      queryPlanning();
      setStarted(true);
    }
  }, []);

  // Custom event render for mobile
  const renderEventProp = {
    renderEvent: React.useCallback((e) => {
      return <EventAgendaItem key={e?.id} currentEvent={e} />;
    }, [])
  };
  const onSelectedDateChange = React.useCallback(
    (event) => {
      const value = event?.date;
      setCurrentDate(value);
    },
    [setCurrentDate]
  );
  const onEventClickGoToEvent = (event, inst) => {
    const item = event?.event;
    const url = getPlanningLink(item);
    // Vérifie si Ctrl (Windows/Linux) ou Cmd (macOS) est pressé pendant le clic
    if (event.domEvent.ctrlKey || event.domEvent.metaKey) {
      // Ouvre le lien dans un nouvel onglet
      window.open(`${window.location.href.split('#')[0]}#${url}`, '_blank');
    } else {
      // Navigation normale dans l'application
      router.push(url);
    }
    // router.push(getPlanningLink(item));
  };

  const navigatePage = (prev) => {
    let nextDate;
    if (prev) {
      nextDate = dayjs(currentDate).subtract(1, 'day').toDate();
    } else {
      nextDate = dayjs(currentDate).add(1, 'day').toDate();
    }
    setCurrentDate(nextDate);
  };

  const customWithNavButtons = () => {
    return (
      <>
        {/* Nav mois + nav jour */}
        <div
          style={{
            backgroundColor: 'white',
            display: 'flex',
            width: '100%',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          <div>
            <CalendarNav />
          </div>

          <div>
            <Button
              onClick={() => navigatePage(true)}
              icon="material-arrow-back"
              variant="flat"
              className="md-custom-header-button"
            ></Button>
            <CalendarToday className="md-custom-header-today" />
            <Button
              onClick={() => navigatePage(false)}
              icon="material-arrow-forward"
              variant="flat"
              className="md-custom-header-button"
            ></Button>
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <div
        style={{
          width: '100%',
          border: '#f7f7f7 1px solid',
          borderRadius: '4px'
        }}
      >
        <div
          style={
            isMobile
              ? {
                  // Mobile: auto height (default)
                }
              : {
                  // Desktop: fixed width
                  width: '100%',
                  maxWidth: '800px'
                }
          }
        >
          <Eventcalendar
            locale={getMobiScrollLanguage(language)}
            dataTimezone="utc"
            displayTimezone={currentTimeZone}
            timezonePlugin={momentTimezone}
            renderEvent={(e) => <EventAgendaItem key={e?.id} currentEvent={e} />}
            renderHeader={customWithNavButtons}
            theme="ios"
            themeVariant="light"
            data={mobiscrollDataPlanning}
            onSelectedDateChange={onSelectedDateChange}
            selectedDate={currentDate}
            view={calView}
            onEventClick={onEventClickGoToEvent}
            cssClass="md-custom-header"
            onPageLoading={onPageLoading}
          />
        </div>
      </div>
    </>
  );
}
