import { ExoPullToRefresh } from '@/shared/components/ExoPullToRefresh.jsx';
import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import { Footer } from '@/shared/components/Footer';
import { GET_CONFIG } from '@/shared/graphql/home';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import PlanningHomepage from '@/shared/pages/home/<USER>/PlanningHomepage.jsx';
import {
  CONFIG_KEYS,
  DEFAULT_CONFIG_VARIABLES,
  getApparenceAttribute
} from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { useMathJaxScript } from '@/shared/utils/hooks/useScript.js';
import { tryParseJSONObject } from '@/shared/utils/utils';
import { useQuery } from '@apollo/client';
import React, { useContext, useEffect, useRef } from 'react';
import { Button } from 'antd';
import { EditOutlined, PlusCircleTwoTone, DeleteOutlined } from '@ant-design/icons';
import { AnnoncesGeneral } from '@/shared/pages/home/<USER>/AnnoncesGeneral';
import { Statistiques } from '@/shared/pages/home/<USER>/Statistiques';
import MyDashboard from '@/shared/components/UserDashboard/MyDashboard.jsx';
import { useTranslation } from 'react-i18next';
import NewsList from '@/shared/pages/home/<USER>/NewsList';
import { isCommercial, isParent } from '@/shared/utils/authority';
import ParentsHome from '@/shared/pages/home/<USER>';
import { router } from 'umi';

export const adminCardActions = (editHandler, deleteHandler, createHandler) => {
  const actions = [];
  if (editHandler) {
    actions.push(<EditOutlined key="edit" onClick={editHandler} />);
  }
  if (deleteHandler) {
    actions.push(<DeleteOutlined key="delete" onClick={deleteHandler} />);
  }
  if (createHandler) {
    actions.push(<PlusCircleTwoTone key="create" onClick={createHandler} />);
  }
  return actions;
};

export const dateFormat = 'DD/MM/YYYY';

export default function (props) {
  useEffectScrollTop();
  const { t } = useTranslation();
  const { appearance } = useContext(GlobalContext);

  const {
    loading: loadingConfig,
    error: errorConfig,
    data: dataConfig,
    refetch: refetchConfig
  } = useQuery(GET_CONFIG, { fetchPolicy: 'cache-and-network', ...DEFAULT_CONFIG_VARIABLES });
  const config = dataConfig?.config;
  const webSiteName = getValueFromKeyConfigData(config, CONFIG_KEYS.WEBSITE_NAME);
  const hasCustomBackgroundPlatform = !!appearance?.backgroundImagePlatform;

  const launchMathJax = useMathJaxScript();
  useEffect(() => {
    launchMathJax(); // Load Mathjax première fois pour initialiser
  }, []);

  const annonceGeneraleRef = useRef();
  const coursDuJourRef = useRef();

  return (
    <ExoPullToRefresh
      onRefresh={async () => {
        await annonceGeneraleRef?.current?.refetchData();
        await coursDuJourRef?.current?.refetchData();
      }}
    >
      {isParent() ? (
        <>
          <ParentsHome />;
        </>
      ) : (
        <>
          {isCommercial() ? (
            <>
              <div style={{ margin: '200px', textAlign: 'center' }}>
                <Button size={'large'} onClick={() => router.push(`/admin/paiements`)}>
                  {t('AccessSubscriptionActivities')}
                </Button>
              </div>
            </>
          ) : (
            <>
              <MyDashboard />
              <ExoteachLayout>
                {/* NEWS : cours ou pages à la une */}
                <NewsList appearance={appearance} />

                <div
                  style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    justifyContent: 'center',
                    gap: '20px',
                    marginTop: '20px'
                  }}
                >
                  {getApparenceAttribute(appearance, 'showGlobalAnnounce') && (
                    <div style={{ maxWidth: 800, width: '90%' }}>
                      <AnnoncesGeneral ref={annonceGeneraleRef} />
                    </div>
                  )}

                  <div
                    style={{
                      width: '100%',
                      maxWidth: '800px'
                      // height: '668px'
                    }}
                  >
                    {/* Nouveau planning mobiscroll */}
                    <PlanningHomepage />
                  </div>
                </div>

                {getApparenceAttribute(appearance, 'showFooterStats') && (
                  <div style={{ marginTop: '20px', marginBottom: '5px' }}>
                    <Statistiques />
                  </div>
                )}
              </ExoteachLayout>
            </>
          )}
        </>
      )}

      <Footer
        appName={webSiteName}
        style={hasCustomBackgroundPlatform ? { backgroundColor: '#ffffff' } : {}}
      />
    </ExoPullToRefresh>
  );
}
