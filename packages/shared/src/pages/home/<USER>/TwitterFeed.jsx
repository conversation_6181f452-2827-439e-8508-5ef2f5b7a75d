import React, { useEffect } from 'react'

export default function({ twitterAccountUrl }) {

  useEffect(() => {
    const anchor = document.createElement("a");
    anchor.setAttribute("class", "twitter-timeline");
    anchor.setAttribute("data-theme", "light");
    anchor.setAttribute("data-tweet-limit", "5");
    anchor.setAttribute("data-width", "350");
    anchor.setAttribute("data-chrome", "noheader nofooter");
    anchor.setAttribute("href", twitterAccountUrl);
    document.getElementsByClassName("twitter-embed")[0].appendChild(anchor);

    const script = document.createElement("script");
    script.setAttribute("src", "https://platform.twitter.com/widgets.js");
    script.charSet="utf-8";
    script.async = true;
    document.getElementsByClassName("twitter-embed")[0].appendChild(script);

  }, []);

  return (
    <>
      <section className="twitterContainer">
        <div className="twitter-embed" />
      </section>
    </>
  )
}
