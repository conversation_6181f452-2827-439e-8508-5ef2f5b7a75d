import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { McqScaleQuestionType } from '@/shared/pages/admin/bareme/componants/subBareme';
import { Alert, Checkbox, Spin, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { MoveRight } from 'lucide-react';
import { EditBaremeWithMcqScaleType } from '@/shared/pages/admin/qcm/components/EditBaremeWithMcqScaleType';
import { useMutation, useQuery } from '@apollo/client';
import {
  MUTATION_UPDATE_SCALE_MASS_CHANGE,
  QUERY_UPDATE_SCALE_MASS_CHANGE
} from '@/shared/graphql/qcm';
import { onErrorShowErrorsFunction } from '@/shared/utils/utils';

const { Text } = Typography;

const BatchUpdateScale = forwardRef(
  (
    {
      // Le dico des keys qui seront modifiées
      scaleEnabled = Object.fromEntries(
        Object.values(McqScaleQuestionType).map((value) => [value, true])
      ),
      coursIds = [],
      questionIds = [],
      previsualizeCount = true // show number of element which will be changed
    },
    ref
  ) => {
    const { t } = useTranslation();

    const [linkingDico, setLinkingDico] = useState(null);
    const [prevCount, setPrevCount] = useState(0); // Contient le résultat de la prévisualisation du nombre d'exercices modifiés

    const createMappingScaleFromLinkingDico = (localLinkingDico) => {
      // Fonction qui build le mappingScale [{questionType:String!,newMcqId :ID!},...] à partir du linkingDico
      if (localLinkingDico) {
        let array = [];

        // Parcourt les valeurs de localLinkingDico
        Object.values(localLinkingDico).forEach((node) => {
          if (node?.disabled === false) {
            array.push({ questionType: node.key, scaleId: node.scaleId });
          }
        });
        return array;
      }
      return [];
    };

    const [mutation] = useMutation(MUTATION_UPDATE_SCALE_MASS_CHANGE, {
      onError: (error) => onErrorShowErrorsFunction(error)
    });

    const {
      data,
      loading,
      refetch: refetchQuery
    } = useQuery(QUERY_UPDATE_SCALE_MASS_CHANGE, {
      onError: onErrorShowErrorsFunction,
      skip: !previsualizeCount || createMappingScaleFromLinkingDico(linkingDico).length <= 0,
      variables: {
        queryMassChangesScaleForQuestionsInput: {
          selectedCoursIds: coursIds,
          selectedQuestionIds: questionIds,
          mappingScale: createMappingScaleFromLinkingDico(linkingDico)
        }
      },
      notifyOnNetworkStatusChange: true,
      fetchPolicy: 'no-cache',
      onCompleted: (data) => {
        const result = data?.queryMassChangesScaleForQuestions;
        if (result !== null && result !== undefined) {
          setPrevCount(result);
        }
      }
    });

    useEffect(() => {
      // Init du dico si il n'est pas défini
      if (linkingDico === null) {
        let dico = {};
        Object.entries(scaleEnabled).forEach(([key, value]) => {
          if (value) {
            dico[key] = { key, scaleId: null, disabled: true };
          }
        });
        setLinkingDico({ ...dico });
      }
    }, [scaleEnabled]);

    const callMutation = async () => {
      // Fonction de mutation
      try {
        const mappingScale = createMappingScaleFromLinkingDico(linkingDico);

        if (mappingScale?.length <= 0) {
          return null;
        }

        const variables = {
          mutationMassChangesScaleForQuestionsInput: {
            selectedCoursIds: coursIds,
            selectedQuestionIds: questionIds,
            mappingScale
          }
        };

        const result = await mutation({ variables });

        if (previsualizeCount) {
          await refetchQuery();
        }
        return result;
      } catch (e) {
        console.error(e);
      }
    };

    const assignScaleIdForKey = (key, scaleIdStructure) => {
      setLinkingDico((prev) => {
        const copie = { ...prev };

        const scaleId = scaleIdStructure?.mcqScaleId;

        copie[key]['scaleId'] = scaleId;
        return copie;
      });
    };

    const changeDisabledForKey = (key, boolean) => {
      setLinkingDico((prev) => {
        const copie = { ...prev };
        copie[key].disabled = boolean;

        return copie;
      });
    };

    useImperativeHandle(ref, () => ({
      test: () => {
        console.log('test !');
      },
      callMutation: async () => {
        return await callMutation();
      }
    }));

    return (
      <>
        {previsualizeCount && (
          <Alert
            type="info"
            style={{ marginBottom: '5px' }}
            message={
              <div style={{ display: 'flex' }}>
                {t('Scale.MassUpdateNumberOfElementsToUpdateLabel')} : &nbsp;
                {loading ? <Spin style={{ display: 'flex' }} /> : JSON.stringify(prevCount)}
              </div>
            }
          />
        )}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column ',
            gap: '10px'
          }}
        >
          {linkingDico &&
            Object.entries(linkingDico).map(([key, node]) => {
              return (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row'
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: '5px'
                    }}
                  >
                    <Checkbox
                      checked={!node?.disabled}
                      onChange={(e) => {
                        changeDisabledForKey(node?.key, !e.target.checked);
                      }}
                    />
                    <Text
                      style={{
                        fontWeight: 'bold',
                        width: '150px',
                        cursor: 'default',
                        color: node?.disabled ? '#bfbfbf' : 'black'
                      }}
                    >
                      {t(`ScaleKey.${node.key}`)}
                    </Text>
                    <MoveRight />
                    <EditBaremeWithMcqScaleType
                      question={null}
                      mcqScaleType={node.key}
                      onChange={(scaleId) => {
                        assignScaleIdForKey(node?.key, scaleId);
                      }}
                      showManageScale={false}
                      disabled={node?.disabled}
                    />
                  </div>
                </div>
              );
            })}
        </div>
      </>
    );
  }
);

export default BatchUpdateScale;
