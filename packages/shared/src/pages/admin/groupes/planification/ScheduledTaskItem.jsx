import CreateEditPlanificationModal from '@/shared/pages/admin/groupes/planification/CreateEditPlanificationModal.jsx';
import { EditOutlined } from '@ant-design/icons';
import { Button, Space, Tag } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function({task, refetchScheduledTasks, groupe, element}) {
  const {t} = useTranslation();

  const [editModalPlanificationVisible, setEditModalPlanificationVisible] = React.useState(false);

  const handleCloseModal = () => {
    setEditModalPlanificationVisible(false);
    refetchScheduledTasks();
  }

  /*
  const items = [{ children: 'sample', label: 'sample' }];
  return <Timeline items={items} />;
  */

  return (
    <>
      <Space>
        <span>
          <div>
            <Space>
              <b>
                {task.name}
              </b>
              <Button
                icon={<EditOutlined />}
                shape="circle"
                type="primary"
                size="small"
                onClick={() => setEditModalPlanificationVisible(true)}
              />
            </Space>
          </div>
          <div>
            {task?.state?.selection?.cours?.length >= 0 && (
              <Tag>
                {task?.state?.selection?.cours?.length} cours
              </Tag>
            )}
          </div>
        </span>
      </Space>

      {editModalPlanificationVisible && (
        <CreateEditPlanificationModal
          closeModalHandler={handleCloseModal}
          modalType="UPDATE"
          isVisible={editModalPlanificationVisible}
          scheduledTask={task}
          groupe={groupe}
          element={element}
        />
      )}
    </>
  );
}