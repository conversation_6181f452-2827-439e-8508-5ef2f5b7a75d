import { DELETE_GROUP_MUTATION } from '@/shared/graphql/groupes.js';
import GroupsMassOperationsModal from '@/shared/pages/admin/groupes/components/GroupsMassOperationsModal.jsx';
import { CreateEditGroupModal, ModalType } from '@/shared/pages/admin/groupes/CreateEditGroupeModal.jsx';
import { downloadFile, FILE_TYPE } from '@/shared/services/file.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { DeleteOutlined, EditOutlined, FileExcelOutlined, UsergroupAddOutlined } from '@ant-design/icons';
import { useMutation } from '@apollo/client';
import { Button, message, notification, Popconfirm, Popover } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';


export const GroupeActions = ({ refetch, record, loading }) => {
  const {t} = useTranslation();
  const [editVisible, setEditVisible] = useState(false);
  const [massAttributionVisible, setMassAttributionVisible] = useState(false);
  const [deleteGroupMutation] = useMutation(DELETE_GROUP_MUTATION, { variables: { id: record.id } });

  const deleteGroup = async () => {
    try {
      await deleteGroupMutation();
      message.success(t('DeletedWithSuccess'))
      refetch();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };
  return (
    <span>
      <Popover
        content="Modifier le groupe"
        trigger="hover"
      >
        <Button
          disabled={record.disabled}
          onClick={() => {
            setEditVisible(true);
          }}
          style={{ marginRight: 16 }}
          type="primary"
          shape="circle"
          icon={<EditOutlined/>}
        />
      </Popover>

      <Popover
        content={t("AddRemoveGroups")}
        trigger="hover"
      >
        <Button
          disabled={record.disabled}
          onClick={() => {
            setMassAttributionVisible(true);
          }}
          style={{ marginRight: 16 }}
          shape="circle"
          icon={<UsergroupAddOutlined />}
        />
      </Popover>

      <Popover
        content={t('ExportToExcel')}
        trigger="hover"
      >
        <Button
          disabled={record.disabled}
          onClick={() => {
            notification.info({ message: 'Exportation des utilisateurs en cours...' });
            downloadFile(FILE_TYPE.FILE, `export-users-by-group/${record.id}`).then(r => {
            });
          }}
          style={{ marginRight: 16, backgroundColor: record.disabled ? 'auto' : '#10793F', color: record.disabled ? 'auto' :"white" }}
          shape="circle"
          icon={<FileExcelOutlined/>}
        />
      </Popover>

      <CreateEditGroupModal
        id={record.id}
        name={record.name}
        role={record.role}
        groupe={record}
        isVisible={editVisible}
        folderId={record.folderId}
        modalType={ModalType.UPDATE}
        closeModalHandler={() => {
          setEditVisible(false);
          refetch(); // Load new modifications
        }}
      />

      {massAttributionVisible && (
        <GroupsMassOperationsModal
          groupe={record}
          isVisible={massAttributionVisible}
          closeModalHandler={() => {
            setMassAttributionVisible(false);
          }}
        />
      )}

      <Popconfirm
        title={t('SureToDeleteGroups')}
        onConfirm={deleteGroup}
        okText={t('general.yes')}
        cancelText={t('general.no')}
      >
        <Button
          shape="circle"
          disabled={record.disabled}
          style={{ marginRight: 16 }}
          danger
          icon={<DeleteOutlined/>}
        />
      </Popconfirm>
    </span>
  );
};
