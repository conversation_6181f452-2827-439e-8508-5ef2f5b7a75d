import {
  CREATE_GROUPE,
  QUERY_ALL_GROUPS_IN_FOLDER,
  QUERY_GROUP_BY_ID
} from '@/shared/graphql/groupes.js';
import { FOLDER_TYPES, useQueryFolders } from '@/shared/hooks/folders/useQueryFolders.jsx';
import { FolderActions } from '@/shared/pages/admin/folders/components/FolderActions.jsx';
import FolderCreation from '@/shared/pages/admin/folders/components/FolderCreation.jsx';
import { FoldersBreadcrumb } from '@/shared/pages/admin/folders/components/FoldersBreadcrumb.jsx';
import { GroupeActions } from '@/shared/pages/admin/groupes/components/GroupeActions.jsx';
import ModalSelectionActionGroups from '@/shared/pages/admin/groupes/components/ModalSelectionActionGroups.jsx';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils.js';
import { useMutation, useQuery } from '@apollo/client';
import React, { useState } from 'react';
import { Link } from 'umi';
import { Button, Card, Dropdown, Image, Popover, Space, Table, Tooltip } from 'antd';
import { ErrorResult } from '@/shared/components/ErrorResult';
import dayjs from 'dayjs';
import {
  CreateEditGroupModal,
  ModalType
} from '@/shared/pages/admin/groupes/CreateEditGroupeModal';
import { DownOutlined, FolderTwoTone, GroupOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';

import groupPlaceholder from '@/shared/assets/group-placeholder.svg';
import { UsersTable } from '@/shared/pages/admin/users/components/UsersTable';

const GroupsTable = ({ folderId }) => {
  const { t } = useTranslation();

  const [createGroupMutation, { loading: loadingCreation }] = useMutation(CREATE_GROUPE);
  /* Groups in current folder */
  const { loading, error, data, refetch } = useQuery(QUERY_ALL_GROUPS_IN_FOLDER, {
    fetchPolicy: 'no-cache',
    variables: {
      folderId
    }
  });
  const groupsInFolder = data?.groupsInFolder || [];

  /* Folders in current folder */
  const {
    folders,
    loading: loadingFolders,
    error: errorFolders,
    refetch: refetchFolders
  } = useQueryFolders(folderId, FOLDER_TYPES.GROUP);

  const [createVisible, setCreateVisible] = useState(false);

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const hasSelected = selectedRowKeys.length > 0;

  const [createdGroup, setCreatedGroup] = useState(null);

  const closeModalHandler = () => {
    setCreateVisible(false);
    refetch(); // Load new modifications
  };
  const handleCreateGroup = async () => {
    setCreateVisible(true);
    const { data } = await createGroupMutation({
      variables: {
        name: 'Nouveau groupe',
        folderId: folderId
      }
    });
    setCreatedGroup(data?.addGroupe);
  };

  const refetchAll = async () => {
    await refetch();
    await refetchFolders();
  };

  // Groups columns
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      sorter: (a, b) => a.id - b.id
    },
    {
      title: 'Image',
      dataIndex: 'image',
      key: 'image',
      render: (image, item) => {
        if (item?.__typename === 'Folder') {
          return <FolderTwoTone style={{ fontSize: '32px' }} />;
        }

        if (!image) {
          return (
            <img
              title={item?.name}
              alt={item?.name}
              style={{ width: 32 }}
              src={groupPlaceholder}
              onError={(e) => {
                e.target.style = 'display: none';
              }}
            />
          );
        }
        return (
          <Image
            title={item?.name}
            width={32}
            src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + image)}
            onError={(e) => {
              e.target.style = 'display: none';
            }}
          />
        );
      }
    },
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name',
      showSearch: true,
      render: (name, item) => {
        const tooltipTitle = "Vous n'avez pas les permissions";
        if (item?.__typename === 'Groupe') {
          if (item.disabled) {
            return (
              <Tooltip title={tooltipTitle}>
                <Link to={`/admin/groupes/${folderId}/${item.id}`}>{name}</Link>
              </Tooltip>
            );
          }
          return <Link to={`/admin/groupes/${folderId}/${item.id}`}>{name}</Link>;
        }
        if (item?.__typename === 'Folder') {
          return <Link to={`/admin/groupes/${item.id}`}>{name}</Link>;
        }
      }
    },
    {
      title: 'Nb groupes / Nb Utilisateurs',
      dataIndex: 'numberOfUsers',
      key: 'numberOfUsers',
      render: (numberOfUsers, item) => {
        if (item?.__typename === 'Folder') {
          return `${item?.countGroups} / ${item?.countUsers}`;
        }
        return (
          <>
            1 /{' '}
            <Popover content="Voir utilisateurs dans ce groupe...">
              <Link to={`/admin/groupes/${folderId}/${item.id}`}>{numberOfUsers}</Link>
            </Popover>
          </>
        );
      },
      sorter: (a, b) => a?.numberOfUsers - b?.numberOfUsers
    },
    {
      title: 'Contenus accessibles',
      dataIndex: 'numberOfCoursesAccessible',
      key: 'numberOfCoursesAccessible',
      render: (numberOfCoursesAccessible, item) => {
        if (item?.__typename === 'Folder') {
          return '...';
        }
        return numberOfCoursesAccessible;
      },
      sorter: (a, b) => a?.numberOfCoursesAccessible - b?.numberOfCoursesAccessible
    },
    {
      title: 'Créé le',
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: (a, b) => dayjs(a.createdAt) - dayjs(b.createdAt),
      render: (text) => dayjs(text).format('DD/MM/YYYY')
    },
    {
      title: 'Modifié le',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      sorter: (a, b) => dayjs(a.updatedAt) - dayjs(b.updatedAt),
      render: (text) => dayjs(text).format('DD/MM/YYYY')
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => {
        if (record?.__typename === 'Folder') {
          return <FolderActions record={record} refetch={refetchFolders} key={record.id} />;
        }
        return <GroupeActions record={record} refetch={refetch} key={record.id} />;
      }
    }
  ];

  const dataSource = [...folders, ...groupsInFolder];

  const [isModalActionsVisible, setModalActionVisible] = useState(false);
  const [action, setAction] = useState(null);
  const handleMenuClick = (e) => {
    setModalActionVisible(true);
    setAction(e.key);
  };

  const ActionKeys = {
    move: 'move'
  };
  const items = [
    {
      label: t('general.Move'),
      key: ActionKeys.move
    }
    /*
      {
        label: 'Supprimer...',
        key: ActionKeys.delete,
      },
    */
  ];

  return (
    <>
      {/* Header actions */}
      <Card style={{ margin: 20 }}>
        {action && (
          <ModalSelectionActionGroups
            isVisible={isModalActionsVisible}
            closeModalHandler={() => {
              // Close modal
              setModalActionVisible(false);
              // Reset selected rows
              setSelectedRowKeys([]);
              // Reset action
              setAction(null);
              // Refetch
              refetchAll();
            }}
            ids={selectedRowKeys}
            itemAction={action}
          />
        )}
        <Space>
          <Button
            size={'large'}
            icon={<GroupOutlined />}
            type={'primary'}
            loading={loadingCreation}
            onClick={handleCreateGroup}
          >
            {t('NewGroup')}
          </Button>

          <FolderCreation type={FOLDER_TYPES.GROUP} parentId={folderId} refetch={refetchFolders} />
        </Space>

        {createVisible && !!createdGroup && (
          <CreateEditGroupModal
            closeModalHandler={closeModalHandler}
            isVisible={createVisible}
            modalType={ModalType.UPDATE}
            groupe={createdGroup}
            name={createdGroup?.name}
            id={createdGroup?.id}
            refetch={refetch}
            loading={loading}
            folderId={folderId}
          />
        )}
      </Card>

      <br />
      <FoldersBreadcrumb
        folderId={folderId}
        type={FOLDER_TYPES.GROUP}
        baseLink={'/admin/groupes'}
      />
      <br />

      {/* TABLE */}
      {!error && (
        <>
          {/* Display only groups in their folders */}
          <Table
            title={() => (
              <>
                {hasSelected && (
                  <Space size={24}>
                    <span>
                      {selectedRowKeys.length} élément{selectedRowKeys.length > 1 && 's'}{' '}
                      {t('selecteds')}
                    </span>

                    <Dropdown
                      menu={{
                        items,
                        onClick: handleMenuClick
                      }}
                    >
                      <Button>
                        <Space>
                          {t('GroupedActions')}
                          <DownOutlined />
                        </Space>
                      </Button>
                    </Dropdown>
                  </Space>
                )}
              </>
            )}
            rowSelection={{
              getCheckboxProps: (record) => ({
                id: record.id,
                disabled: record.disabled
                // Column configuration not to be checked
                // name: record.name,
              }),

              selectedRowKeys,
              onChange: (s) => {
                setSelectedRowKeys(s);
              }
            }}
            loading={loading || loadingFolders}
            columns={columns}
            rowKey={(record) => `${record.__typename}-${record.id}`}
            dataSource={dataSource}
            scroll={{ x: true }}
            pagination={false}
            rowClassName={(record) => record.disabled && 'disabled-row'}
          />
        </>
      )}
      {error && !loading && <ErrorResult refetch={refetch} error={error} />}
    </>
  );
};

const UsersInGroupTable = ({ groupId, folderId }) => {
  // Query group by id
  // Modal edition groupe, bouton ajouter user dans le groupe?
  // Voir pour actions groupées
  useEffectScrollTop();
  const [importModalVisible, setImportUserModalVisble] = useState(false);

  const { data: dataGroup, loading: groupLoading } = useQuery(QUERY_GROUP_BY_ID, {
    fetchPolicy: 'cache-and-network',
    variables: {
      id: groupId
    },
    skip: !groupId
  });
  const selectedGroup = dataGroup?.groupe;

  return (
    <>
      <FoldersBreadcrumb
        folderId={folderId}
        type={FOLDER_TYPES.GROUP}
        baseLink={'/admin/groupes'}
      />

      <UsersTable groupId={groupId} />
    </>
  );
};

export default function (props) {
  const { t } = useTranslation();

  const folderId = props?.match?.params?.folderId || null;
  const groupId = props?.match?.params?.groupId || null; // Users in group page and detail group

  return (
    <>
      <FullMediParticlesBreadCrumb title={t('general.Groups')} />

      {/* Groups table */}
      {groupId ? (
        <>
          <UsersInGroupTable groupId={groupId} folderId={folderId === 'null' ? null : folderId} />
        </>
      ) : (
        <GroupsTable folderId={folderId} />
      )}
    </>
  );
}
