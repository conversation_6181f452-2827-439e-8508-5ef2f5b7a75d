import {
  CREATE_GROUPE,
  DELETE_GROUP_MUTATION,
  MUTATION_ADD_GROUP_RESPONSIBILITY,
  MUTATION_REMOVE_GROUP_RESPONSIBILITY,
  QUERY_GROUP_BY_ID_WITH_TYPE_QCMS,
  UPDATE_GROUP_PERMISSIONS,
  UPDATE_GROUPE
} from '@/shared/graphql/groupes.js';
import FoldersTreeSelect, {
  ROOT_FOLDER_TREE_NODE
} from '@/shared/pages/admin/folders/components/FoldersTreeSelect.jsx';
import PlanificationTab from '@/shared/pages/admin/groupes/planification/PlanificationTab.jsx';
import AbstractGroupsAndIndividualGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsAndIndividualGroupsManager.jsx';
import EditGroupPermissionsTree from '@/shared/pages/admin/permissions/EditGroupPermissionsTree.jsx';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Drawer,
  Form,
  message,
  notification,
  Popconfirm,
  Popover,
  Radio,
  Row,
  Tabs,
  Typography
} from 'antd';
import { useMutation, useQuery } from '@apollo/client';
import React, { useEffect, useState } from 'react';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult';
import { useTranslation } from 'react-i18next';
import { FileExcelOutlined, HomeOutlined, UsergroupAddOutlined } from '@ant-design/icons';
import { PencilLine, Trash2, X } from 'lucide-react';
import { QUERY_FOLDERS_TREE_DATA } from '@/shared/graphql/folders';
import {
  getUrlProtectedRessource,
  GlobalConfig,
  showGqlErrorsInMessagePopupFromException
} from '@/shared/utils/utils';
import { downloadFile, FILE_TYPE } from '@/shared/services/file';
import GroupsMassOperationsModal from '@/shared/pages/admin/groupes/components/GroupsMassOperationsModal';
import { FormImagePreview } from '@/shared/components/Forms/FormImagePreview';

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE'
};

const getMutationFromModalType = (modalType) => {
  switch (modalType) {
    case ModalType.CREATE:
      return CREATE_GROUPE;
    case ModalType.UPDATE:
      return UPDATE_GROUPE;
    default:
      return CREATE_GROUPE;
  }
};

export const CreateEditGroupModal = ({
  closeModalHandler,
  modalType,
  isVisible,
  name,
  role,
  id,
  groupe,
  user,
  folderId = null
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [Mutation, { loading, data, error }] = useMutation(getMutationFromModalType(modalType));
  const [fileImageContent, setFileImageContent] = React.useState(null);
  const [folderIdState, setFolderIdState] = React.useState(folderId);
  const [responsibiltySwitcher, setResponsibilitySwitcher] = useState('responsibleFor');
  const [showGroupSelector, setShowGroupSelector] = useState(false);
  const [deleteGroupMutation] = useMutation(DELETE_GROUP_MUTATION, {
    variables: { id: groupe?.id }
  });
  const [massAttributionVisible, setMassAttributionVisible] = useState(false);
  const [selectedPermissions, setSelectedPermissions] = useState({
    ueIds: [],
    ueCategoryIds: [],
    coursIds: []
  });

  const deleteGroup = async () => {
    try {
      await deleteGroupMutation();
      message.success(t('DeletedWithSuccess'));
      await closeModalHandler();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  useEffect(() => {
    setFolderIdState(folderId);
  }, [folderId]);

  const { data: dataTree } = useQuery(QUERY_FOLDERS_TREE_DATA, {
    fetchPolicy: 'no-cache',
    variables: { folderType: 'GROUP' }
  });

  const [updateGroupPermissions, { loading: loadingUpdateGroupPermissions }] =
    useMutation(UPDATE_GROUP_PERMISSIONS);

  const foldersTreeData = [ROOT_FOLDER_TREE_NODE, ...(dataTree?.foldersTreeData || [])];

  /* GET Group details */
  const {
    data: dataGroup,
    loading: loadingGroup,
    refetch: refetchGroup
  } = useQuery(QUERY_GROUP_BY_ID_WITH_TYPE_QCMS, {
    fetchPolicy: 'cache-and-network',
    variables: {
      id: groupe?.id
    },
    skip: !groupe
  });
  const groupeFetched = dataGroup?.groupe;
  const typeQcmsInGroup = groupeFetched?.typesQcms;

  const isIndividual = groupe?.isIndividual || false;

  const handleFinish = async (data) => {
    try {
      let groupIdToUse = id;
      if (modalType === ModalType.UPDATE) {
        await Mutation({
          variables: {
            id,
            name: data.name,
            role: data.role, // n'est plus utilisé
            folderId: folderIdState,
            image: fileImageContent
          }
        });
      } else {
        // Create
        const result = await Mutation({
          variables: {
            name: data.name,
            role: data.role, // n'est plus utilisé
            folderId: folderIdState,
            image: fileImageContent
          }
        });
        groupIdToUse = result.data?.createGroupe?.id;
      }
      // Appel de la mutation pour mettre à jour les permissions (même logique que handleSave)
      if (groupIdToUse) {
        await updateGroupPermissions({
          variables: {
            groupId: groupIdToUse,
            ueIds: selectedPermissions.ueIds,
            ueCategoryIds: selectedPermissions.ueCategoryIds,
            coursIds: selectedPermissions.coursIds
          }
        });
      }
      message.success(modalType === ModalType.UPDATE ? t('Updated') : 'Groupe créé!');
      await closeModalHandler();
    } catch (e) {
      console.error(e);
      message.error('Erreur serveur, veuillez réessayer');
    }
  };

  const handleDelete = () => {
    deleteGroup();
  };

  const [groupName, setGroupName] = useState(name); // Initialisation avec le nom du groupe existant

  // Fonction récursive pour trouver le chemin dans l'arborescence
  const findPathById = (id, nodes) => {
    for (const node of nodes) {
      if (`${node.id}` === `${id}`) {
        return [node.title];
      }
      if (node.children) {
        const path = findPathById(id, node.children);
        if (path) {
          return [node.title, ...path];
        }
      }
    }
    return null;
  };

  const [selectedFolderPath, setSelectedFolderPath] = useState(
    findPathById(folderId, foldersTreeData)
  );

  // Utilisation de useEffect pour charger le chemin initial dès le chargement
  useEffect(() => {
    if (folderId) {
      // Appel à findPathById pour obtenir le chemin initial
      const initialPath = findPathById(folderId, foldersTreeData);
      if (initialPath) {
        setSelectedFolderPath(initialPath);
      }
    }
  }, [folderId]);

  useEffect(() => {
    if (folderId) {
      const initialPath = findPathById(folderId, foldersTreeData);
      if (initialPath) {
        setSelectedFolderPath(initialPath);
      }
    }
  }, [folderId]);

  return (
    <Drawer
      title={t('EditGroup')}
      footer={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            <Popover content={t('AddRemoveGroups')} trigger="hover">
              <Button
                onClick={() => {
                  setMassAttributionVisible(true);
                }}
                style={{ marginRight: 16 }}
                shape="circle"
                icon={<UsergroupAddOutlined />}
              />
            </Popover>

            <Popover content={t('ExportToExcel')} trigger="hover">
              <Button
                onClick={() => {
                  notification.info({ message: t('ProcessingExport') });
                  downloadFile(FILE_TYPE.FILE, `export-users-by-group/${groupe.id}`).then(
                    (r) => {}
                  );
                }}
                style={{
                  marginRight: 16,
                  backgroundColor: '#10793F',
                  color: 'white'
                }}
                shape="circle"
                icon={<FileExcelOutlined />}
              />
            </Popover>

            <Popconfirm
              title={
                <>
                  <span>{t('SureToDeleteGroup')}</span>
                  <br />
                  <span>{t('SureToDeleteGroup2')}</span>
                </>
              }
              onConfirm={() => handleDelete()}
            >
              <Button variant="outlined" color="danger" icon={<Trash2 />} shape="circle" />
            </Popconfirm>
          </div>

          <Button type="primary" onClick={() => handleFinish({ ...form.values, name: groupName })}>
            {modalType === ModalType.UPDATE ? t('Update') : t('Create')}
          </Button>
        </div>
      }
      visible={isVisible}
      placement={'bottom'}
      onClose={closeModalHandler}
      width={1200}
      height={'100%'}
      bodyStyle={{ paddingTop: 0, paddingLeft: 16, paddingRight: 16 }}
      destroyOnClose
    >
      <div style={{ display: 'flex', alignItems: 'center', margin: '20px 0' }}>
        {/* Image du groupe à gauche avec ExoFormImage stylisé */}
        <div style={{ marginRight: '20px', position: 'relative', width: '100px', height: '100px' }}>
          {!isIndividual && (
            <>
              {/*
                <ExoFormImage
                  name="image"
                  beforeUpload={(file) => setFileImageContent(file)}
                  onDelete={() => setFileImageContent({ shouldDelete: true })}
                  label="Image du groupe"
                  defaultValue={
                    groupe?.image &&
                    getUrlProtectedRessource(GlobalConfig.get().FILES_URL + groupe.image)
                  }
                  loading={loading}
                  size={100} // Taille de l'image
                  round={true} // Rendre l'image ronde
                  showDeleteButton={true} // Masquer le bouton de suppression
                />
              */}
              <FormImagePreview
                name="image"
                beforeUpload={(file) => setFileImageContent(file)}
                defaultValue={
                  groupe?.image &&
                  getUrlProtectedRessource(GlobalConfig.get().FILES_URL + groupe.image)
                }
                onDelete={() => setFileImageContent({ shouldDelete: true })}
                label="Image du groupe"
              />
            </>
          )}
        </div>

        {/* Conteneur pour le titre et le choix de dossier à droite */}
        <div style={{ flex: 1 }}>
          <Typography.Title
            level={3}
            editable={{
              icon: (
                <span style={{ fontSize: 20 }}>
                  <PencilLine />
                </span>
              ),
              onChange: (newName) => setGroupName(newName)
            }}
            style={{ marginBottom: '10px', display: 'flex', alignItems: 'flex-end', gap: 8 }}
          >
            {groupName}
          </Typography.Title>

          {!isIndividual && (
            <>
              <div style={{ display: 'flex', alignItems: 'start' }}>
                <Typography.Title level={4} style={{ margin: 0 }}>
                  {folderId === null ? (
                    ROOT_FOLDER_TREE_NODE.title
                  ) : (
                    <>
                      <HomeOutlined /> / {findPathById(folderId, foldersTreeData)?.join(' / ')}
                    </>
                  )}
                </Typography.Title>
                <Button type="link" onClick={() => setShowGroupSelector(!showGroupSelector)}>
                  {showGroupSelector ? <X /> : <PencilLine />}
                </Button>
              </div>
              {showGroupSelector && (
                <Form.Item
                  label={''}
                  help={
                    <span>
                      {t('AllowsClassificationOnlyForAdmins')}{' '}
                      {selectedFolderPath?.length > 0 && (
                        <>
                          <HomeOutlined /> / {selectedFolderPath.join(' / ')}
                        </>
                      )}
                    </span>
                  }
                  style={{ marginBottom: 0 }}
                >
                  <FoldersTreeSelect
                    defaultValue={folderId}
                    onClick={(shouldAdd, newFolderId) => {
                      setFolderIdState(newFolderId);

                      // Met à jour le chemin sélectionné en utilisant findPathById
                      const path = findPathById(newFolderId, foldersTreeData);
                      if (path) {
                        setSelectedFolderPath(path);
                      } else {
                        setSelectedFolderPath([]); // Réinitialiser si aucun chemin trouvé
                      }
                    }}
                    folderType={'GROUP'}
                  />
                </Form.Item>
              )}
            </>
          )}
        </div>
      </div>

      {/* Affichage des petites erreurs si nécessaire */}
      <SmallErrorsAlert error={error} loading={loading} />

      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab={t('General')} key={1}>
          <Form
            layout="vertical"
            onFinish={(values) => {
              // Ajoute le nom modifié dans les valeurs du formulaire avant soumission
              const updatedValues = { ...values, name: groupName };
              handleFinish(updatedValues);
            }}
            form={form}
            initialValues={{
              ...groupe
            }}
          >
            <Row gutter={[16, 16]}>
              <div style={{ padding: '0 12px', width: '100%' }}>
                {groupe && (
                  <>
                    {/* Arborescence de cours */}
                    {modalType === ModalType.UPDATE && (
                      <Form.Item>
                        <EditGroupPermissionsTree
                          groupeType={groupe.role}
                          forGroupId={id}
                          onChange={setSelectedPermissions}
                        />
                      </Form.Item>
                    )}

                    <h2>{t('Responsability')}</h2>

                    <Form.Item>
                      <Alert
                        message={t('responsabilityGroupExplanation')}
                        type="info"
                        showIcon
                        style={{ marginBottom: '10px' }}
                      />

                      <Radio.Group
                        value={responsibiltySwitcher}
                        onChange={(e) => setResponsibilitySwitcher(e.target.value)}
                      >
                        <Radio.Button value={'responsibleFor'}>
                          {t('ResponsibleFor...')}
                        </Radio.Button>
                        <Radio.Button value={'supervisedBy'}>{t('SupervisedBy...')}</Radio.Button>
                      </Radio.Group>
                      <br />

                      {responsibiltySwitcher === 'responsibleFor' && (
                        <>
                          {groupeFetched?.responsibleFor && (
                            <AbstractGroupsAndIndividualGroupsManager
                              groupes={groupeFetched?.responsibleFor}
                              abstractGroupsManagerProps={{
                                title: <h2>{t('ResponsibilityGroups')}</h2>,
                                entityName: groupe.name,
                                entityId: groupe.id,
                                addGroupMutation: MUTATION_ADD_GROUP_RESPONSIBILITY,
                                removeGroupMutation: MUTATION_REMOVE_GROUP_RESPONSIBILITY,
                                groupParameterName: 'groupId',
                                entityParameterName: 'responsibleOfGroupId',
                                key: 1
                              }}
                              individualPermissionsManagerProps={{
                                title: <h2>{t('ResponsibilityUsers')}</h2>,
                                entityName: groupe.name,
                                entityId: groupe.id,
                                addGroupMutation: MUTATION_ADD_GROUP_RESPONSIBILITY,
                                removeGroupMutation: MUTATION_REMOVE_GROUP_RESPONSIBILITY,
                                groupParameterName: 'groupId',
                                entityParameterName: 'responsibleOfGroupId',
                                showText: false,
                                refetchGroup,
                                key: 2
                              }}
                              key={1}
                            />
                          )}
                        </>
                      )}

                      {responsibiltySwitcher === 'supervisedBy' && (
                        <>
                          {groupeFetched?.supervisedBy && (
                            <AbstractGroupsAndIndividualGroupsManager
                              groupes={groupeFetched?.supervisedBy}
                              abstractGroupsManagerProps={{
                                title: <h2>{t('ResponsibilityGroups')}</h2>,
                                entityName: groupe.name,
                                entityId: groupe.id,
                                addGroupMutation: MUTATION_ADD_GROUP_RESPONSIBILITY,
                                removeGroupMutation: MUTATION_REMOVE_GROUP_RESPONSIBILITY,
                                groupParameterName: 'responsibleOfGroupId',
                                entityParameterName: 'groupId',
                                key: 3
                              }}
                              individualPermissionsManagerProps={{
                                title: <h2>{t('ResponsibilityUsers')}</h2>,
                                entityName: groupe.name,
                                entityId: groupe.id,
                                addGroupMutation: MUTATION_ADD_GROUP_RESPONSIBILITY,
                                removeGroupMutation: MUTATION_REMOVE_GROUP_RESPONSIBILITY,
                                groupParameterName: 'responsibleOfGroupId',
                                entityParameterName: 'groupId',
                                showText: false,
                                refetchGroup,
                                key: 4
                              }}
                              key={2}
                            />
                          )}
                        </>
                      )}

                      <Divider />
                    </Form.Item>
                  </>
                )}
              </div>
            </Row>
          </Form>
        </Tabs.TabPane>

        {groupe && (
          <Tabs.TabPane tab={t('Planification.Planifications')} key={2}>
            <PlanificationTab groupe={groupe} />
          </Tabs.TabPane>
        )}
      </Tabs>
      {massAttributionVisible && (
        <GroupsMassOperationsModal
          groupe={groupe}
          isVisible={massAttributionVisible}
          closeModalHandler={() => {
            setMassAttributionVisible(false);
          }}
        />
      )}
    </Drawer>
  );
};
