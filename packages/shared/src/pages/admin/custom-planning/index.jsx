import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import {
  MUTATION_CREATE_CUSTOM_PLANNING,
  MUTATION_DELETE_CUSTOM_PLANNING,
  QUERY_ALL_CUSTOM_PLANNINGS
} from '@/shared/graphql/customPlanning.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { CreateEditCustomPlanning } from '@/shared/pages/admin/custom-planning/components/CreateEditCustomPlanning.jsx';
import { ModalType } from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal.jsx';
import { getLanguageName, missingTranslation, tr } from '@/shared/services/translate.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Card, message, Popconfirm, Table, Tabs, Tag } from 'antd';
import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';

const CustomPlanningActions = ({ refetch, record, loading }) => {
  const { t } = useTranslation();
  const [editVisible, setEditVisible] = useState(false);
  const [deleteCustomPlanning] = useMutation(MUTATION_DELETE_CUSTOM_PLANNING);

  const handleDelete = async (id) => {
    try {
      await deleteCustomPlanning({ variables: { customPlanningId: id } });
      message.success(t('DeletedWithSuccess'));
      refetch();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  return (
    <span>
      <Button
        onClick={() => {
          setEditVisible(true);
        }}
        style={{ marginRight: 16 }}
        type="primary"
        shape="circle"
        icon={<EditOutlined />}
      />
      <CreateEditCustomPlanning
        record={record}
        isVisible={editVisible}
        modalType={ModalType.UPDATE}
        closeModalHandler={() => {
          setEditVisible(false);
          refetch(); // Load new modifications
        }}
        refetch={refetch}
      />
      <Popconfirm title={t('SureOfDeletion')} onConfirm={() => handleDelete(record.id)}>
        <Button shape="circle" style={{ marginRight: 16 }} danger icon={<DeleteOutlined />} />
      </Popconfirm>
    </span>
  );
};

export default function (props) {
  const { t, i18n } = useTranslation();
  const { enabledLanguages } = useContext(GlobalContext);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);
  const [createVisible, setCreateVisible] = useState(false);

  const [createCustomPlanning] = useMutation(MUTATION_CREATE_CUSTOM_PLANNING);

  useEffectScrollTop();
  const { loading, error, data, refetch } = useQuery(QUERY_ALL_CUSTOM_PLANNINGS, {
    fetchPolicy: 'no-cache'
  });

  const configs = data?.allCustomPlannings;

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: t('Name'),
      dataIndex: tr('name', selectedLanguage),
      key: 'name',
      render: (name, challenge) => name || missingTranslation
    },
    {
      title: t('Description'),
      dataIndex: tr('description', selectedLanguage),
      key: 'description',
      render: (desc, challenge) => desc || missingTranslation
    },
    {
      title: t('general.Groups'),
      key: 'groups',
      render: (_, customPlanning) =>
        customPlanning?.groupes
          ?.filter((g) => !g?.isIndividual)
          ?.map((g) => <Tag>{g?.name}</Tag>) || '-'
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => (
        <CustomPlanningActions record={record} refetch={refetch} key={record.id} />
      )
    }
  ];

  const [currentToEdit, setCurrentToEdit] = useState(null);
  const [editVisible, setEditVisible] = useState(false);

  const autoCreate = async () => {
    try {
      const { data } = await createCustomPlanning({
        variables: {
          customPlanning: {
            name: '',
            steps: []
          }
        }
      });
      if (data?.createCustomPlanning?.id) {
        setCurrentToEdit(data?.createCustomPlanning);
        setEditVisible(true);
      }
    } catch (e) {
      console.error(e);
      message.error('Erreur serveur, veuillez réessayer');
    }
  };
  return (
    <>
      <FullMediParticlesBreadCrumb title={t('CustomPlanning')} />
      <br />

      <Card style={{ margin: 12 }}>
        <Button onClick={autoCreate} type={'primary'}>
          {t('general.add')}
        </Button>
      </Card>

      {editVisible && (
        <CreateEditCustomPlanning
          record={currentToEdit}
          isVisible={editVisible}
          modalType={ModalType.UPDATE}
          closeModalHandler={() => {
            setEditVisible(false);
            refetch(); // Load new modifications
          }}
          refetch={refetch}
        />
      )}

      <Tabs
        defaultActiveKey={i18n.language}
        destroyInactiveTabPane
        onChange={(activeKey) => setSelectedLanguage(activeKey)}
        style={{ marginLeft: '10px' }}
      >
        {enabledLanguages &&
          enabledLanguages?.map((lang) => (
            <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
              <Table
                loading={loading}
                columns={columns}
                dataSource={configs}
                rowKey="id"
                scroll={{ x: true }}
              />
            </Tabs.TabPane>
          ))}
      </Tabs>
    </>
  );
}
