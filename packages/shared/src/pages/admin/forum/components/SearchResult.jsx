import PostTypeTag from '@/shared/components/Commentaires/PostTypeTag';
import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import { FileImage } from '@/shared/components/FileImage';
import ExoAvatar from '@/shared/components/User/ExoAvatar';
import { UserProfileCard } from '@/shared/components/User/UserProfileCard/UserProfileCard';
import { QUERY_ADMIN_POST_SEARCH, UPDATE_POST } from '@/shared/graphql/posts';
import useLocalStorage from '@/shared/hooks/useLocalStorage';
import { CustomInfoTag } from '@/shared/pages/admin/forum/components/CustomInfoTag';
import { TagCollection } from '@/shared/pages/admin/forum/components/TagCollection';
import { ThreadPost } from '@/shared/pages/forum/$post';
import { getCommentaireTypeFromObject, getTypeIdFromObject } from '@/shared/services/commentaires';
import {
  isMobile,
  showGqlErrorsInMessagePopupFromException,
  stripHtml
} from '@/shared/utils/utils';
import { CheckCircleTwoTone, CloseCircleTwoTone } from '@ant-design/icons';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Card, Drawer, Empty, message, Popover, Spin, Statistic, Table } from 'antd';
import dayjs from 'dayjs';
import React, { useMemo, useState } from 'react';
import CountUp from 'react-countup';
import { useTranslation } from 'react-i18next';

export const SearchResult = ({ filter }) => {
  const ComponantChangePostStatus = ({ postId, initResolved }) => {
    /* Component qui lie le resolve / unresolve Button afin de pouvoir faire qqc de plus joli et organique */

    const [updatePostMutation] = useMutation(UPDATE_POST);
    const [stateAction, setStateAction] = useState(null);

    // La fonction de changement de status
    const changeSubjectStatus = async (isResolved, postId) => {
      try {
        await updatePostMutation({
          variables: { id: postId, post: { isResolved } }
        });
        message.success(`${t('TopicMarkedAs')} ${isResolved ? t('Resolved') : t('NonResolved')}`);
        //await refetch();
      } catch (e) {
        showGqlErrorsInMessagePopupFromException(e);
        console.error(e);
      }
    };

    // Le boutton qui active status => true
    const ResolveButton = ({ postId }) => {
      const resolveColor = '#52c41a';

      return (
        <Popover content={t('FilterQuestions.ResolvePost')}>
          <Button
            style={{ backgroundColor: stateAction === true && resolveColor }}
            onClick={() => {
              changeSubjectStatus(true, postId);
              setStateAction(true);
            }}
          >
            <CheckCircleTwoTone twoToneColor={resolveColor} />
          </Button>
        </Popover>
      );
    };

    // Le boutton qui active status => false
    const UnResolveButton = ({ postId }) => {
      const unResolveColor = '#cf1f1f';

      return (
        <Popover content={t('FilterQuestions.UnResolvePost')}>
          <Button
            style={{ backgroundColor: stateAction === false && unResolveColor }}
            onClick={() => {
              changeSubjectStatus(false, postId);
              setStateAction(false);
            }}
          >
            <CloseCircleTwoTone twoToneColor={unResolveColor} />
          </Button>
        </Popover>
      );
    };

    return (
      <div style={{ display: 'flex' }}>
        <ResolveButton postId={postId} />
        <UnResolveButton postId={postId} />
      </div>
    );
  };

  // Gestion de la pagnination
  const [offset, setOffset] = useState(0);
  const [limit, setLimit] = useState(20);
  const [currentPage, setCurrentPage] = useLocalStorage('admin-question-reponse-current-page', 1);

  const [selectedPost, setSelectedPost] = React.useState(null);

  // Important pour reset l'offset à 0
  const subFilter = useMemo(() => {
    const temp = {
      generalCoursIds: filter.coursIds,
      category: filter.category,
      typeIds: filter.typeIds,
      startDateCreationFilter: filter.startDateCreationFilter,
      endDateCreationFilter: filter.endDateCreationFilter,
      userIds: filter.userIds,
      participantsUserIds: filter.participantsUserIds,
      isResolved: filter.isResolved,
      isQuestion: true, // On veut uniquement des questions
      titreFilter: filter.titreFilter,
      contentFilter: filter.contentFilter,
      iaAnswered: filter.iaAnswered,
      isResolvedByAi: filter.isResolvedByAi,
      isAskingForHumanHelp: filter.isAskingForHumanHelp,
      categoryExerciceAdvancedFilter: filter.categoryExerciceAdvancedFilter,
      categoryEventAdvancedFilter: filter.categoryEventAdvancedFilter
    };
    setOffset(0);
    setCurrentPage(1);
    return temp;
  }, [filter]);

  const { t } = useTranslation();
  const { data: { adminSearchPosts = null } = {}, loading } = useQuery(QUERY_ADMIN_POST_SEARCH, {
    fetchPolicy: 'no-cache',
    variables: {
      filter: {
        ...subFilter,
        offset,
        limit
      }
    }
  });

  const totalResults = adminSearchPosts?.count || 0;

  // Composant pour afficher le texte HTML avec un bouton pour expand/reduce
  const ExpandableHtmlText = ({ htmlContent, previewLength = 100 }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    const toggleExpand = () => setIsExpanded(!isExpanded);

    // Fonction pour obtenir un extrait du contenu HTML
    const getPreview = (content) => {
      // Remarque : Cette méthode est très basique et pourrait ne pas fonctionner correctement si votre HTML contient des éléments complexes.
      // Elle est fournie à titre d'exemple simple.
      const strippedContent = content.replace(/<[^>]+>/g, ''); // Supprimer les balises HTML
      return strippedContent.length > previewLength
        ? unescape(strippedContent).slice(0, previewLength) + '...'
        : content;
    };

    return (
      <div>
        {isExpanded ? (
          <RenderQuillHtml>{htmlContent || ''}</RenderQuillHtml>
        ) : (
          <RenderQuillHtml>{getPreview(htmlContent)}</RenderQuillHtml>
        )}
        <Button type="link" onClick={toggleExpand}>
          {isExpanded ? 'Réduire' : 'Lire la suite'}
        </Button>
      </div>
    );
  };

  const formatDate = (date) => {
    const now = dayjs();
    const inputDate = dayjs(date);
    if (now.diff(inputDate, 'hour') < 24 && now.isSame(inputDate, 'day')) {
      return inputDate.format('HH[:]mm');
    } else if (now.subtract(1, 'day').isSame(inputDate, 'day')) {
      return t('general.Yesterday');
    } else {
      return inputDate.format('D MMM');
    }
  };

  const columns = [
    /*
    {
      title: t('general.User'),
      dataIndex: 'user',
      key: 'user',
      render: (user) => {
        if (!user) {
          return <span>no User</span>;
        }
        return (
          <>
            <UserProfileCard userId={user?.id} username={user?.username}>
              <ExoAvatar avatar={user.avatar} size="small" isActive={user.isActive} />
            </UserProfileCard>
            &nbsp;{user.username}
          </>
        );
      }
    },
    */
    {
      title: t('general.Question'),
      dataIndex: 'title2',
      key: 'title2',
      render: (_, record) => {
        const { isResolved, isResolvedByAi, isAskingForHumanHelp, answeredByAi } = record;
        return (
          <div onClick={() => setSelectedPost(record)}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <UserProfileCard userId={record?.user?.id} username={record?.user?.username}>
                  <ExoAvatar
                    avatar={record?.user.avatar}
                    size="small"
                    isActive={record?.user.isActive}
                  />
                </UserProfileCard>
                &nbsp;{record?.user.username}
              </div>
              <div>{dayjs(record.createdAt).format('Do MMMM YYYY')}</div>
            </div>

            <div onClick={() => setSelectedPost(record)} style={{ cursor: 'pointer' }}>
              <h4
                style={{
                  margin: '5px 0px 2px 0px'
                }}
              >
                {stripHtml(record?.title || '')}
              </h4>
              <div>{stripHtml(record.text?.slice(0, 200))}</div>
            </div>

            {/* Tags */}
            <div
              style={{ cursor: 'pointer', display: 'flex', flexWrap: 'wrap' }}
              onClick={() => setSelectedPost(record)}
            >
              <CustomInfoTag postId={record.id} type="courOrForumName" />{' '}
              <PostTypeTag postType={record.type} />
              {(isResolved === true || isResolvedByAi === true) && (
                <TagCollection string={t(`FilterQuestions.Resolved`)} type="resolved" />
              )}
              {isResolved === false && isResolvedByAi === false && (
                <TagCollection string={t(`FilterQuestions.UnResolved`)} type="resolved" />
              )}
              {/* Aide humaine demandée seulement si demandé */}
              {/*
              {isResolvedByAi !== null && (
                <TagCollection
                  string={t(
                    `FilterQuestions.${isResolvedByAi ? 'ResolvedByAi' : 'UnResolvedByAi'}`
                  )}
                  type="resolved"
                />
              )}
              */}
              {isAskingForHumanHelp && (
                <TagCollection string={t(`FilterQuestions.${'AskingHumanHelp'}`)} type="help" />
              )}
              {answeredByAi && (
                <TagCollection string={t(`FilterQuestions.${'AiAnswered'}`)} type="aiAnswered" />
              )}
            </div>
            {/*
              <Link to={buildThreadLinkFromComment(record)}>
                {' '}
                <RenderQuillHtml>{record.text?.slice(0, 200)}</RenderQuillHtml>{' '}
              </Link>
            */}
          </div>
        );
      }
    }
    /*
    {
      title: (
        <div>
          {t('FilterQuestions.LastFeedback')}
          &nbsp;
          <Popover
            content={<div>{t('FilterQuestions.LastFeedbackExplanation')}</div>}
            trigger="hover"
          >
            <QuestionCircleOutlined style={{fontSize: '16px', color: '#d4af37'}}/>
          </Popover>
        </div>
      ),
      render: (_, record) => {
        const text = record?.approuvedResponse?.lastResponseFromNonUserOrBot?.text;

        return (
          <>
            {record?.approuvedResponse &&
              record?.approuvedResponse?.lastResponseRole &&
              record?.approuvedResponse?.lastResponseFromNonUserOrBot && (
                <>
                  <>
                    <Tag
                      color={
                        record?.approuvedResponse?.lastResponseRole === 'ADMIN' ||
                        record?.approuvedResponse?.lastResponseRole === 'SUB_ADMIN'
                          ? 'geekblue'
                          : 'purple'
                      }
                      //style={{ height: 'auto' }}
                    >
                      {record?.approuvedResponse?.lastResponseRole !== 'AI'
                        ? mapRoleToShortRoleName(record?.approuvedResponse?.lastResponseRole)
                        : record?.approuvedResponse?.lastResponseRole}
                    </Tag>
                    <>
                      <UserProfileCard
                        userId={record?.approuvedResponse?.lastResponseFromNonUserOrBot?.user?.id}
                        username={
                          record?.approuvedResponse?.lastResponseFromNonUserOrBot?.user?.username
                        }
                      >
                        <ExoAvatar
                          avatar={
                            record?.approuvedResponse?.lastResponseFromNonUserOrBot?.user?.avatar
                          }
                          size="small"
                          isActive={
                            record?.approuvedResponse?.lastResponseFromNonUserOrBot?.user?.isActive
                          }
                        />
                      </UserProfileCard>
                      &nbsp;
                      {record?.approuvedResponse?.lastResponseFromNonUserOrBot?.user?.username}
                    </>
                  </>
                  <ExpandableHtmlText htmlContent={text} previewLength={200}/>
                </>
              )}
          </>
        );
      }
    },

    {
      title: t('FilterQuestions.CreationDate'),
      dataIndex: 'date',
      key: 'date',
      //sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
      render: (_, record) => {
        const { createdAt } = record;
        return (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              cursor: 'pointer',
            }}
            onClick={() => setSelectedPost(record)}
          >
            {<TagCollection string={dayjs(createdAt).format('DD/MM/YYYY kk:mm')} type="date" />}
            <span>{dayjs(createdAt).fromNow()}</span>
          </div>
        );
      },
    },
    {
      title: t('FilterQuestions.Category'),
      dataIndex: 'type',
      key: 'type',
      render: (_, record) => (
        <div style={{ cursor: 'pointer' }} onClick={() => setSelectedPost(record)}>
          <TagCollection
            string={t(`FilterQuestions.PostType${getCommentaireTypeFromObject(record)}`)}
            type="category"
          />
          <CustomInfoTag postId={record.id} type="categoryType" />
        </div>
      ),
    },
    {
      title: t('FilterQuestions.State'),
      dataIndex: 'état',
      key: 'état',
      render: (_, record) => {
        const { isResolved, isResolvedByAi, isAskingForHumanHelp, answeredByAi } = record;
      :
        return (
          <div style={{ cursor: 'pointer' }} onClick={() => setSelectedPost(record)}>
            {isResolved !== null && (
              <TagCollection
                string={t(`FilterQuestions.${isResolved ? 'Resolved' : 'UnResolved'}`)}
                type="resolved"
              />
            )}
            {isResolvedByAi !== null && (
              <TagCollection
                string={t(`FilterQuestions.${isResolvedByAi ? 'ResolvedByAi' : 'UnResolvedByAi'}`)}
                type="resolved"
              />
            )}
            {isAskingForHumanHelp !== null && (
              <TagCollection
                string={t(
                  `FilterQuestions.${isAskingForHumanHelp ? 'AskingHumanHelp' : 'NotAskingHumanHelp'}`,
                )}
                type="help"
              />
            )}
            {answeredByAi !== null && (
              <TagCollection
                string={t(`FilterQuestions.${answeredByAi ? 'AiAnswered' : 'NotAiAnswered'}`)}
                type="aiAnswered"
              />
            )}
          </div>
        );
      },
    },
    */
    /*
    {
      title: t('FilterQuestions.Actions'),
      dataIndex: 'action',
      key: 'action',
      render: (id, record) => {
        return <ComponantChangePostStatus postId={record.id} initResolved={record.isResolved} />;
      }
    }
    */
  ];

  const formatter = (value) => <CountUp end={value} separator="," />;

  return (
    <>
      <Card style={{ margin: '10px' }} bordered={false}>
        <Statistic
          title={t('FilterQuestions.ResultNumber')}
          value={loading ? <Spin /> : totalResults | 0}
          formatter={formatter}
          valueStyle={{ fontWeight: 'bold' }} // Appliquer le style en gras ici
        />
      </Card>

      <div style={isMobile ? {} : { display: 'grid', gridTemplateColumns: '1fr 1fr' }}>
        <div style={isMobile ? {} : { gridColumn: '1 / 2' }}>
          <Table
            loading={loading}
            columns={columns}
            dataSource={adminSearchPosts?.postResults}
            scroll={{ x: true }}
            pagination={{
              current: currentPage,
              total: totalResults,
              showTotal: false,
              onChange: (page, pageSize) => {
                setCurrentPage(page);
                setOffset((page - 1) * pageSize);
                setLimit(pageSize);
              },
              position: ['bottomLeft'],
              defaultPageSize: 20,
              pageSizeOptions: [10, 20, 50],
              responsive: true,
              size: 'default'
            }}
            size="large"
            search={false}
          />
        </div>

        {isMobile ? (
          <Drawer
            open={selectedPost}
            placement="bottom"
            onClose={() => setSelectedPost(null)}
            height={isMobile ? 'calc(100% - 109px)' : 'calc(100% - 64px)'}
            styles={{ body: { padding: '0 24px' } }}
          >
            {selectedPost && (
              <ThreadPost
                postId={selectedPost?.id}
                typeId={getTypeIdFromObject(
                  selectedPost,
                  getCommentaireTypeFromObject(selectedPost)
                )}
                type={getCommentaireTypeFromObject(selectedPost)}
                showBanner={false} // Ne pas afficher la grosse bannière
                enablePullToRefresh={false}
              />
            )}
          </Drawer>
        ) : (
          <div>
            {selectedPost ? (
              <ThreadPost
                postId={selectedPost?.id}
                typeId={getTypeIdFromObject(
                  selectedPost,
                  getCommentaireTypeFromObject(selectedPost)
                )}
                type={getCommentaireTypeFromObject(selectedPost)}
                showBanner={false} // Ne pas afficher la grosse bannière
                enablePullToRefresh={false}
              />
            ) : (
              <Empty description={t('SelectNotification')} />
            )}
          </div>
        )}
      </div>
    </>
  );
};
