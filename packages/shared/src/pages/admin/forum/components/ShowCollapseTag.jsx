import { TagCollection } from '@/shared/pages/admin/forum/components/TagCollection';
import dayjs from 'dayjs';
import React from 'react';

/* Petit component qui analyse les cours sélectionnés et les show en tag => gère notament le cas où tout est select */
import { useTranslation } from 'react-i18next';

export const ShowCollapseTag = ({
  qcmSearchFilter: {
    coursIds,
    isResolved,
    isResolvedByAi,
    userIds,
    isAskingForHumanHelp,
    iaAnswered,
    category,
    categoryExerciceAdvancedFilter,
    categoryEventAdvancedFilter,
    typeIds,
    startDateCreationFilter,
    endDateCreationFilter
  },
  qcmTypeObject,
  postTypes
}) => {
  const { t } = useTranslation();

  return (
    <>
      <TagCollection
        string={`${coursIds ? coursIds.length : 0} ${t('FilterQuestions.SelectedCourses')}`}
        type="cours"
      >
        {' '}
      </TagCollection>
      {isResolved !== null && (
        <TagCollection
          string={t(`FilterQuestions.${isResolved ? 'Resolved' : 'UnResolved'}`)}
          type="resolved"
        />
      )}
      {isResolvedByAi !== null && (
        <TagCollection
          string={t(`FilterQuestions.${isResolvedByAi ? 'ResolvedByAi' : 'UnResolvedByAi'}`)}
          type="resolved"
        />
      )}
      {userIds && userIds.length > 0 && (
        <TagCollection
          string={`${userIds.length}  ${t('FilterQuestions.Creators')}`}
          type="creators"
        />
      )}
      {isAskingForHumanHelp !== null && (
        <TagCollection
          string={t(
            `FilterQuestions.${isAskingForHumanHelp ? 'AskingHumanHelp' : 'NotAskingHumanHelp'}`
          )}
          type="help"
        />
      )}
      {iaAnswered !== null && (
        <TagCollection
          string={t(`FilterQuestions.${iaAnswered ? 'AiAnswered' : 'NotAiAnswered'}`)}
          type="aiAnswered"
        />
      )}

      {typeIds && typeIds.length > 0 && postTypes && Object.keys(postTypes).length > 0 && (
        <>
          {typeIds.length === postTypes.length ? (
            <TagCollection string={t(`FilterQuestions.AllPostTypeTag`)} type="type" />
          ) : (
            typeIds.map((value) => {
              const foundType = postTypes.find((item) => item.id === value);
              if (foundType) {
                return <TagCollection string={foundType.name} type="type" />;
              }
            })
          )}
        </>
      )}

      {category &&
        category.length > 0 &&
        category.map((value, i) => {
          return (
            <TagCollection key={i} string={t(`FilterQuestions.PostType${value}`)} type="category" />
          );
        })}

      {qcmTypeObject &&
        categoryExerciceAdvancedFilter &&
        categoryExerciceAdvancedFilter.length > 0 &&
        categoryExerciceAdvancedFilter.map((value, i) => {
          const foundType = qcmTypeObject.find((item) => item.id === value);
          if (foundType) {
            return <TagCollection key={i} string={foundType.name} type="categoryType" />;
          }
        })}

      {startDateCreationFilter && endDateCreationFilter && (
        <TagCollection
          string={`${dayjs(startDateCreationFilter).format('DD/MM/YYYY')} -> ${dayjs(endDateCreationFilter).format('DD/MM/YYYY')}`}
          type="date"
        />
      )}
    </>
  );
};
