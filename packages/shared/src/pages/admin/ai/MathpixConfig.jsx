import {useTranslation} from "react-i18next";
import {Table, Button, Modal, Tag} from "antd";
import React, {useEffect, useState} from "react";
import {UpdateMathpixProfil} from "@/shared/pages/admin/ai/AiQcmsConfigComponants/UpdateMathpixProfil";
import {useQuery} from "@apollo/client";
import {onErrorShowErrorsFunction} from "@/shared/utils/utils";
import {QUERY_ADMIN_GET_MATHPIX_INTEGRATIONS} from "@/shared/graphql/mathpix";



export default function MathpixConfig() {

  // Constantes
  const { t } = useTranslation();

  // States
  const [callWrapper, setCallWrapper] = useState(null); // permet de call le modal du bouton 'action' avec les bons arguments
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { data:{adminGetMathpixConfigs:tableData=[]}={}, loading, error, refetch } = useQuery(QUERY_ADMIN_GET_MATHPIX_INTEGRATIONS, {
    onError: (errors) => { onErrorShowErrorsFunction(errors); },
  });

  const updateConfigCaller = (id) => {
    const wantedConfig = tableData.find((value) => value.id === id);
    setCallWrapper({ ...wantedConfig, type: 'UPDATE' });
  };

  useEffect(() => {
    if (callWrapper !== null) {
      setIsModalOpen(true);
    }
  }, [callWrapper]);


  //// Composants
  const Actions = (record)=> {
    return (
      <div key={record?.id} style={{display: 'flex', gap: '5px'}}>
        <Button
          onClick={() => {
            updateConfigCaller(record?.id);
          }}
          type={'primary'}
        >
          {t('QcmConfigTraductionPackage.TableColumnButtonActionLabel')}
        </Button>
      </div>
    )
  }

  const columns=[
    {
      title:t('MathpixIntegration.IntegrationTitleColumnName'),
      render: (record) => {return <div>{record?.name}</div>;},
      width:'80%'
    },
    {
      title:t('Actions'),
      render:Actions,
      width:'20%'
    },
  ]


  return (
    <>
      {tableData?.length===0 && <Tag color={"orange"}>{t('MathpixIntegration.NoMathpixIntegrationForConfigurationTagWarning')}</Tag>}
      <Table
        loading={loading}
        dataSource={tableData}
        columns={columns}
        scroll={{ x: true }}
        pagination={false}
      />

      <Modal
        title={<h2>{t('MathpixIntegration.EditIntegrationModalTitle')} {callWrapper?.name}</h2>}
        open={isModalOpen}
        onOk={() => {
          setIsModalOpen(false);
        }}
        width={'90%'}
        onCancel={() => setIsModalOpen(false)}
        onClick={(event) => {
          event.stopPropagation();
        }}
        destroyOnClose={true}
        footer={null}
      >
        <UpdateMathpixProfil
          callArgument={callWrapper ? callWrapper : {}}
          refetch={refetch}
          closeModalFunction={() => {
            setIsModalOpen(false)
          }}

        />
      </Modal>

    </>
  )
}