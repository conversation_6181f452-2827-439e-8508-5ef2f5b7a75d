import { message, Table, Upload, <PERSON><PERSON>, Di<PERSON><PERSON>, <PERSON> } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery } from '@apollo/client';
import { FileExcelOutlined, LoadingOutlined } from '@ant-design/icons';
import AbstractGroupsAndIndividualGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsAndIndividualGroupsManager';
import ExoAvatar from '@/shared/components/User/ExoAvatar';
import dayjs from 'dayjs';
import { onErrorShowErrorsFunction } from '@/shared/utils/utils';
import {
  MUTATION_ADD_GROUPE_MATHPIX_CONFIG_ACCES,
  MUTATION_DELETE_MATHPIXIE,
  MUTATION_LAUNCH_MATHPIX_UPLOAD,
  MUTATION_REMOVE_GROUPE_MATHPIX_CONFIG_ACCES,
  QUERY_ADMIN_DATA_MATHPIXIES,
  QUERY_AUTHORIZED_GROUPS
} from '@/shared/graphql/mathpix';
import { IS_DEV } from '@/shared/utils/utils.js';

export const UpdateMathpixProfil = ({ callArgument, closeModalFunction, refetch }) => {
  // Constantes
  const { t } = useTranslation();
  const { id } = callArgument;
  const isDev = IS_DEV || false;

  // UseStates
  const [selectedPdfToUpload, setSelectedPdfToUpload] = useState(null);

  const {
    data: { adminGetGroupsAuthorizedForMathpixIntegration = null } = {},
    loading: loadingGroups,
    error: errorGroups,
    refetch: refetchGroups
  } = useQuery(QUERY_AUTHORIZED_GROUPS, {
    variables: { mathpixIntegrationId: id },
    onError: (errors) => {
      onErrorShowErrorsFunction(errors);
    },
    fetchPolicy: 'no-cache'
  });

  // Queries
  const {
    data: { adminQueryMathpixies = null } = {},
    loading: loadingMathpixies,
    error: errorMathpixies,
    refetch: refetchMathpixies
  } = useQuery(QUERY_ADMIN_DATA_MATHPIXIES, {
    variables: { mathpixConfigId: id },
    //skip: !selectedPdfToImport,
    fetchPolicy: 'cache-and-network',
    notifyOnNetworkStatusChange: 'true',
    onError: (errors) => {
      onErrorShowErrorsFunction(errors);
    }
  });

  const handleMathpixieDeletion = (recordId, configId) => {
    wrapperTriggerDeletion({ mathpixieId: recordId, mathpixConfigId: configId });
  };

  const [triggerMutation, { loading: loadingMutation }] = useMutation(
    MUTATION_LAUNCH_MATHPIX_UPLOAD,
    {
      onError: (errors) => {
        onErrorShowErrorsFunction(errors);
      },
      onCompleted: () => {
        refetchMathpixies();
      }
    }
  );

  const [triggerDeletion, {}] = useMutation(MUTATION_DELETE_MATHPIXIE, {
    onError: (errors) => {
      onErrorShowErrorsFunction(errors);
    },
    onCompleted: () => {
      refetchMathpixies();
    }
  });

  function trigger() {
    if (!selectedPdfToUpload) {
      message.error(t('MathpixIntegration.ErrorMessageNeedFile'));
    }
    const result = triggerMutation({
      variables: { file: selectedPdfToUpload, mathpixConfigId: id }
    });

    if (result) {
      refetchMathpixies();
    } else {
      message.error(t('MathpixIntegration.ErrorMessageUpload'));
      refetchMathpixies();
    }
  }

  function wrapperTriggerDeletion({ mathpixieId, mathpixConfigId }) {
    const result = triggerDeletion({ variables: { mathpixieId, mathpixConfigId } });
    if (result) {
      refetchMathpixies();
    } else {
      message.error(t('MathpixIntegration.ErrorMessageDeletion'));
      refetchMathpixies();
    }
  }

  // Componants
  const columns = [
    {
      title: 'id',
      dataIndex: 'id'
    },
    {
      title: t('MathpixIntegration.MathpixMathpixieFilenameColumn'),
      dataIndex: 'fileName'
    },
    {
      title: t('MathpixIntegration.DateCreationColumnName'),
      dataIndex: 'createdAt',
      sorter: (a, b) => dayjs(a.date) - dayjs(b.date),
      render: (date, record) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: t('MathpixIntegration.CreatorColumnName'),
      dataIndex: 'mathpixieAuthor',
      render: (author) => (
        <ExoAvatar isActive={author?.isActive} avatar={author?.avatar} size="small" />
      )
    },
    {
      title: t('MathpixIntegration.MathpixActionColumnName'),
      render: (record) => (
        <Button danger type={'primary'} onClick={() => handleMathpixieDeletion(record?.id, id)}>
          {t('MathpixIntegration.DeleteMathpixButtonLabel')}
        </Button>
      )
    }
  ];

  const uploadItemComponant = (
    <Upload.Dragger
      name="file"
      listType="file"
      showUploadList
      style={{
        height: '10px',
        maxHeight: '100px'
      }}
      beforeUpload={(file, fileList) => {
        if (file.name.endsWith('.pdf')) {
          setSelectedPdfToUpload(file);
          return false;
        } else {
          message.error(t('AiQuestionCreationModal.NeedPdfErrorName'));
          return Upload.LIST_IGNORE;
        }
      }}
      fileList={selectedPdfToUpload ? [selectedPdfToUpload] : []}
      onRemove={() => {
        setSelectedPdfToUpload(null);
      }}
    >
      <div style={{ maxHeight: '100px' }}>
        <div className="ant-upload-text">{t('general.upload')}</div>
      </div>
    </Upload.Dragger>
  );

  return (
    <>
      {isDev && (
        <div style={{ width: '100%' }}>
          <div style={{ width: '200px', height: '100px', margin: 'auto' }}>
            {uploadItemComponant}
          </div>
          <div style={{ marginTop: '30px' }}>
            <Button
              onClick={trigger}
              loading={loadingMutation}
              type={'primary'}
              style={{ margin: 'auto', display: 'flex' }}
              disabled={selectedPdfToUpload === null}
            >
              {t('MathpixIntegration.UploadFileButtonLabel')}
            </Button>
          </div>
        </div>
      )}
      {isDev && (
        <>
          <Divider style={{ marginTop: '20px' }}>
            {t('MathpixIntegration.DivierBeforeMathpixies')}
          </Divider>
          <Table
            columns={columns}
            dataSource={adminQueryMathpixies}
            loading={loadingMathpixies || loadingMutation}
          />
        </>
      )}

      <Divider>{t('Permissions')}</Divider>
      {adminGetGroupsAuthorizedForMathpixIntegration ? (
        <AbstractGroupsAndIndividualGroupsManager
          groupes={adminGetGroupsAuthorizedForMathpixIntegration} // reflete la selection actuelle
          abstractGroupsManagerProps={{
            title: <h3>{t('MathpixIntegration.ConfigPermissionManagementGroupSubtitle')}</h3>,
            entityId: id,
            entityParameterName: 'mathpixIntegrationId',
            groupParameterName: 'groupId',
            refetchGroup: refetch,
            addGroupMutation: MUTATION_ADD_GROUPE_MATHPIX_CONFIG_ACCES,
            removeGroupMutation: MUTATION_REMOVE_GROUPE_MATHPIX_CONFIG_ACCES
          }}
          individualPermissionsManagerProps={{
            title: <h3>{t('MathpixIntegration.ConfigPermissionManagementUsersSubtitle')}</h3>,
            entityId: id,
            addGroupMutation: MUTATION_ADD_GROUPE_MATHPIX_CONFIG_ACCES,
            removeGroupMutation: MUTATION_REMOVE_GROUPE_MATHPIX_CONFIG_ACCES,
            entityParameterName: 'mathpixIntegrationId',
            groupParameterName: 'groupId',
            refetchGroup: refetchGroups,
            showText: false
          }}
        />
      ) : (
        <Spin />
      )}
      <div style={{ display: 'flex', flexDirection: 'row-reverse' }}>
        <Button onClick={closeModalFunction} style={{ marginTop: '5px' }} type={'primary'}>
          {t('general.OK')}
        </Button>
      </div>
    </>
  );
};
