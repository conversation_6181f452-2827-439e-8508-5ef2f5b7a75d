import React from 'react'
import { useMutation, useQuery } from '@apollo/client'
import {
  ADD_TUTEUR_TO_UE,
  QUERY_SEARCH_USERS_LIGHT,
  REMOVE_TUTEUR_FROM_UE,
} from '@/shared/graphql/user'
import { message, Select, Tag } from 'antd'
import { useTranslation } from 'react-i18next';

/* TODO will be removed */
export const TuteursUEManager = ({ tuteurs, ue }) => {
  const {t} = useTranslation();
  const dataUsers = useQuery(QUERY_SEARCH_USERS_LIGHT, {
    fetchPolicy: 'cache-and-network',
    variables: { filter: { role: 'TUTEUR' } || {} },
  })

  const [addTuteurToUE, addTuteurData] = useMutation(ADD_TUTEUR_TO_UE)
  const [removeTuteurFromUE, removeTuteurData] = useMutation(REMOVE_TUTEUR_FROM_UE)

  const tuteurTagRender = ({ label, value, closable, onClose, key }) => (
    <Tag value={value} key={key} color="purple" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
      {label}
    </Tag>
  )

  const handleSelect = async (_, option) => {
    try {
      await addTuteurToUE({ variables: { ueId: ue.id, userId: option.key } })
      message.success(`Tuteur ${option.value} ajouté à ${ue.name}`)
    } catch (e) {
      message.error(`Tuteur ${option.value} n'a pas été ajouté de ${ue.name} `)
      console.error(e)
    }
  }
  const handleDeselect = async (_, option) => {
    try {
      await removeTuteurFromUE({ variables: { ueId: ue.id, userId: option.key } })
      message.success(`Tuteur ${option.value} supprimé de ${ue.name}`)
    } catch (e) {
      message.error(`Tuteur ${option.value} n'a pas été supprimé de ${ue.name} `)
      console.error(e)
    }
  }

  return (
    <span>
        <Select
          showArrow
          mode="multiple"
          style={{ minWidth: '160px', maxWidth: '450px', width: '100%' }}
          tagRender={tuteurTagRender}
          placeholder={t('ChooseReferent')}
          defaultValue={tuteurs.map(user => user.username)}
          loading={dataUsers.loading || addTuteurData.loading || removeTuteurData.loading}
          options={!dataUsers.error && dataUsers?.data?.searchUsers?.users?.map(user => (
            {
              value: user.username,
              key: user.id,
            }
          ))}
          onDeselect={handleDeselect}
          onSelect={handleSelect}
        />
      </span>
  )
}