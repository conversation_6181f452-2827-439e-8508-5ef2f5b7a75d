import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { ButtonCreateCategoryOrFolder } from '@/shared/pages/cours/components/ButtonCreateCategoryOrFolder.jsx';
import ButtonCreateCourse from '@/shared/pages/cours/components/ButtonCreateCourse.jsx';
import ButtonCreateSubject from '@/shared/pages/cours/components/ButtonCreateSubject.jsx';
import ButtonImportCourse from '@/shared/pages/cours/components/ButtonImportCourse.jsx';
import {
  EditCreateCategoryModal,
  MoveCategory
} from '@/shared/pages/cours/components/EditCategorieCardModal.jsx';
import {
  EditCoursCardModal,
  MoveCourse
} from '@/shared/pages/cours/components/EditCoursCardModal.jsx';
import { getLanguageName, missingTranslation, tr } from '@/shared/services/translate.js';
import { isSuperAdmin } from '@/shared/utils/authority.js';
import React, { useContext, useEffect, useState } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import {
  DELETE_COURS,
  DELETE_UE,
  DELETE_UECATEGORY,
  QUERY_ALL_UES,
  QUERY_COURS_IN_UECATEGORY,
  QUERY_COURSES_IN_UE,
  QUERY_UE_BY_ID_WITH_CHILDREN,
  QUERY_UE_CATEGORIES_FOR_UE_WITH_COURSES,
  QUERY_UE_CATEGORY_ID_WITH_CHILDREN
} from '@/shared/graphql/cours';
import { Link } from 'umi';
import {
  Alert,
  Button,
  Card,
  Dropdown,
  message,
  Modal,
  Popconfirm,
  Popover,
  Space,
  Table,
  Tabs,
  Tag
} from 'antd';
import { ProtectedImage } from '@/shared/components/ProtectedImage';
import { EditCreateUeCardModal, MoveUE } from '@/shared/pages/cours/components/EditUECardModal';
import { DeleteOutlined, DownOutlined, EditOutlined } from '@ant-design/icons';
import { ModalType } from '@/shared/pages/admin/groupes/CreateEditGroupeModal';
import { FEATURE_FLAGS, showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import CoursBreadcrumb from '@/shared/components/Cours/CoursBreadcrumb';

const ItemActions = ({ refetch, record, loading }) => {
  const { t } = useTranslation();
  const [editVisible, setEditVisible] = useState(false);
  const [deleteUE] = useMutation(DELETE_UE);
  const [deleteUECateg] = useMutation(DELETE_UECATEGORY);
  const [deleteCours] = useMutation(DELETE_COURS);

  const handleDelete = async (id) => {
    try {
      if (record?.__typename === 'UE') {
        await deleteUE({ variables: { id } });
      }
      if (record?.__typename === 'UECategory') {
        await deleteUECateg({ variables: { id } });
      }
      if (record?.__typename === 'Cours') {
        await deleteCours({ variables: { id } });
      }
      message.success(t('DeletedWithSuccess'));
      refetch();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  /* -- Components -- */
  const editButton = (
    <Button
      onClick={() => {
        setEditVisible(true);
      }}
      style={{ marginRight: 16 }}
      type="primary"
      shape="circle"
      icon={<EditOutlined />}
    />
  );

  const closeModalHandler = () => {
    setEditVisible(false);
    refetch(); // Load new modifications
  };

  const modalUE = (
    <EditCreateUeCardModal
      ueId={record.id}
      title={record.name}
      type={record?.type}
      ue={record}
      isModalVisible={editVisible}
      modalType={ModalType.UPDATE}
      closeModalHandler={closeModalHandler}
      {...record}
    />
  );

  const modalUECategory = (
    <EditCreateCategoryModal
      category={record}
      isVisible={editVisible}
      modalType={ModalType.UPDATE}
      closeModalHandler={closeModalHandler}
      id={record.id}
      ueId={record?.ueId}
      name={record?.name}
      color={record?.color}
      image={record?.image}
      order={record?.order}
      description={record?.description}
      categoryVisibility
    />
  );

  const modalCours = (
    <EditCoursCardModal
      isVisible={editVisible}
      closeModalHandler={closeModalHandler}
      cours={record}
      targetCours={record?.targetCours}
    />
  );

  const deleteButton = (
    <Popconfirm title={t('SureOfDeletion')} onConfirm={() => handleDelete(record.id)}>
      <Button shape="circle" style={{ marginRight: 16 }} danger icon={<DeleteOutlined />} />
    </Popconfirm>
  );

  return (
    <span>
      {editButton}
      {record?.__typename === 'UE' && modalUE}
      {record?.__typename === 'UECategory' && modalUECategory}
      {record?.__typename === 'Cours' && modalCours}
      {isSuperAdmin() && deleteButton}
    </span>
  );
};

const ActionKeys = {
  move: 'move'
};
const Actions = {
  move: 'move'
};

const getTypeAndIdFromKey = (k) => {
  const splitted = k.split('-');
  return { id: splitted[1], type: splitted[0] };
};

export const ModalSelectionAction = ({
  isVisible,
  itemAction,
  ids,
  closeModalHandler,
  selectedType = null
}) => {
  const { t } = useTranslation();

  const handleCancelModal = () => {
    closeModalHandler();
  };

  if (!selectedType) {
    return 'Erreur: Aucun type défini';
  }
  const handleFinish = () => {
    closeModalHandler();
  };

  return (
    <>
      <Modal
        title={t('general.Edit')}
        open={isVisible}
        onCancel={handleCancelModal}
        footer={null}
        closable
        confirmLoading={false}
      >
        <Alert type="info" message={`${ids?.length} items sélectionnés`} />

        <br />

        {itemAction === ActionKeys.move && (
          <>
            {selectedType === 'ue' && (
              <MoveUE ids={ids} multiple closeModalHandler={handleFinish} />
            )}

            {selectedType === 'category' && (
              <MoveCategory ids={ids} multiple closeModalHandler={handleFinish} />
            )}

            {selectedType === 'cours' && (
              <MoveCourse ids={ids} multiple closeModalHandler={handleFinish} />
            )}
          </>
        )}

        <br />
      </Modal>
    </>
  );
};

const ItemsSelectionActions = ({ selectedRowKeys, questionsNumber, refetch, showTotal = true }) => {
  const { t } = useTranslation();
  const [isModalVisible, setModalVisible] = useState(false);
  const [action, setAction] = useState(null);
  const [selectedType, setType] = useState(null);
  const [ids, setIds] = useState(null);

  const closeModalHandler = () => {
    setModalVisible(false);
    refetch();
  };

  // Toujours les mêmes types
  useEffect(() => {
    if (!selectedRowKeys) {
      return;
    }
    const firstKey = selectedRowKeys?.[0];
    const { type } = getTypeAndIdFromKey(firstKey); // Tous on le même type
    setIds(selectedRowKeys?.map((k) => getTypeAndIdFromKey(k)?.id));
    setType(type);
  }, [selectedRowKeys]);

  const items = [
    {
      label: 'Déplacer...',
      key: ActionKeys.move
    }
    /*
    {
      label: 'Supprimer...',
      key: ActionKeys.delete,
    },
    */
  ];

  const handleMenuClick = (e) => {
    setModalVisible(true);
    setAction(e.key);
  };

  return (
    <div>
      {action && selectedType && selectedRowKeys && isModalVisible && (
        <ModalSelectionAction
          isVisible={isModalVisible}
          closeModalHandler={closeModalHandler}
          ids={ids}
          itemAction={action}
          selectedType={selectedType}
        />
      )}

      <Space>
        <span
          style={{
            fontSize: '24px'
          }}
        >
          {selectedRowKeys?.length} items(s) sélectionnés
        </span>
        <Dropdown
          menu={{
            items,
            onClick: handleMenuClick
          }}
        >
          <Button>
            <Space>
              {t('GroupedActions')}
              <DownOutlined />
            </Space>
          </Button>
        </Dropdown>
      </Space>
    </div>
  );
};

export default function (props) {
  const ueId = props.match.params?.ueId;
  const categoryId = props.match.params?.categoryId;

  const { t, i18n } = useTranslation();
  const { enabledLanguages } = useContext(GlobalContext);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);

  // ROOT UE
  const { loading, error, data, refetch } = useQuery(QUERY_ALL_UES, {
    fetchPolicy: 'no-cache',
    skip: ueId || categoryId
  });

  /* ------------ UE CONTENT ------------- */
  const {
    loading: loadingUECategs,
    error: errorUECategs,
    data: dataUeCategs,
    refetch: refetchUECategs
  } = useQuery(QUERY_UE_CATEGORIES_FOR_UE_WITH_COURSES, {
    fetchPolicy: 'cache-and-network',
    variables: { ueId },
    skip: !ueId
  });
  const {
    loading: loadingCourses,
    error: errorCourses,
    data: dataCourses,
    refetch: refetchCourses
  } = useQuery(QUERY_COURSES_IN_UE, {
    fetchPolicy: 'cache-and-network',
    variables: { ueId },
    skip: !ueId
  });
  // UEs enfants (si dossier)
  const {
    data: dataUeWithChilds,
    loading: loadingUEDetails,
    refetch: refetchUEById
  } = useQuery(QUERY_UE_BY_ID_WITH_CHILDREN, {
    variables: { id: ueId },
    skip: !ueId,
    fetchPolicy: 'cache-and-network'
  });

  const currentUE = dataUeWithChilds?.ue || [];
  const childrenUEs = dataUeWithChilds?.ue?.children || [];
  // Cours
  const cours = dataCourses?.coursInUE || [];
  // Catégories de UE parent
  const categories = dataUeCategs?.ueCategories || [];
  /* ------------ END UE CONTENT ------------- */

  /* ------------ CATEGORY CONTENT ------------- */
  // Query category and children
  const {
    data: dataCurrentCategory,
    loading: loadingCurrentCategory,
    refetch: refetchCategoryWithChildren
  } = useQuery(QUERY_UE_CATEGORY_ID_WITH_CHILDREN, {
    variables: { id: categoryId },
    skip: !categoryId,
    fetchPolicy: 'cache-and-network'
  });
  /* QUERY COURS IN CATEG */
  const {
    error: errorCoursInCateg,
    data: dataCoursInCateg,
    loading: loadingCoursInCateg,
    refetch: refetchCoursInCateg
  } = useQuery(QUERY_COURS_IN_UECATEGORY, {
    variables: { ueCategoryId: categoryId },
    skip: !categoryId,
    fetchPolicy: 'cache-and-network'
  });
  const currentCategory = dataCurrentCategory?.ueCategory || {};
  const childrenCategories = currentCategory?.children || [];
  const coursInCategory = dataCoursInCateg?.coursInUECategory || [];
  const hasNothingInCategory =
    dataCoursInCateg?.coursInUECategory?.length === 0 && childrenCategories?.length === 0;
  /* ------------ END CATEGORY CONTENT ------------- */

  const isLoadingSomething =
    (loadingCourses || loading || loadingUECategs || loadingUEDetails) &&
    (!data || !dataCourses || !dataUeCategs);

  // STATE
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [disabledRowKeys, setDisabledRowKeys] = useState([]);

  const hasSelected = selectedRowKeys.length > 0;

  const refetchAll = async () => {
    await Promise.all([
      refetch(),
      refetchUECategs(),
      refetchCourses(),
      refetchCoursInCateg(),
      refetchCategoryWithChildren(),
      refetchUEById()
    ]);
  };

  const columns = [
    {
      title: t('Image'),
      dataIndex: 'image',
      key: 'image',
      render: (image, record) => (
        <ProtectedImage key={record.id} src={image} alt="img" style={{ maxHeight: '50px' }} />
      )
    },
    {
      title: t('Name'),
      dataIndex: tr('name', selectedLanguage),
      key: 'name',
      render: (text, record) => {
        let contentLink;
        if (record?.__typename === 'UE') {
          contentLink = `/admin/permissions/ue/${record.id}`;
        } else if (record?.__typename === 'UECategory') {
          contentLink = `/admin/permissions/categorie/${record.id}`;
        } else if (record?.__typename === 'Cours') {
          contentLink = `/cours/${record.id}`;
        }

        return (
          <>
            <Space>
              <Tag color={record?.isFolder ? 'green' : 'blue'}>
                {record?.__typename === 'UE' && (record?.isFolder ? 'Dossier' : 'Matière')}
                {record?.__typename === 'UECategory' && 'Categorie'}
                {record?.__typename === 'Cours' && 'Cours'}
              </Tag>

              {record?.__typename === 'Cours' ? (
                <Popover content={<Link to={contentLink}>{t('Open')}</Link>} trigger="click">
                  <Button type={'link'}>{text || missingTranslation}</Button>
                </Popover>
              ) : (
                <Link
                  to={contentLink}
                  onClick={() => {
                    setSelectedRowKeys([]);
                    setDisabledRowKeys([]);
                  }}
                >
                  {text || missingTranslation}
                </Link>
              )}
            </Space>
            <br />

            <div style={{ overflow: 'hidden', height: '50px', width: '100%', fontSize: '12px' }}>
              {record?.description || record?.text || ''}
            </div>
          </>
        );
      }
    },

    {
      title: "Ordre d'affichage",
      dataIndex: 'order',
      width: 30,
      render: (order, record) => {
        return order;
      }
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => <ItemActions key={record.id} refetch={refetchAll} record={record} />
    }
  ];

  const getTableDataSource = () => {
    if (ueId) {
      const array = [
        ...childrenUEs?.map((ue, key) => ({
          ...ue,
          key: `ue-${ue?.id}`,
          index: key
        })),
        ...cours?.map((c, key) => ({
          ...c,
          key: `cours-${c?.id}`,
          index: key
        })),
        ...categories?.map((c, key) => ({
          ...c,
          key: `category-${c?.id}`,
          index: key
        }))
      ];

      return array;
    } else if (categoryId) {
      return [
        ...childrenCategories?.map((c, key) => ({
          ...c,
          key: `category-${c?.id}`,
          index: key
        })),
        ...coursInCategory?.map((c, key) => ({
          ...c,
          key: `cours-${c?.id}`,
          index: key
        }))
      ];
    } else {
      // ROOT
      return data?.allUEs
        ?.filter((ue) => ue?.parentId === null)
        ?.map((ue, key) => ({
          ...ue,
          key: `ue-${ue?.id}`,
          index: key
        }));
    }
  };

  const inRoot = !ueId && !categoryId;
  const inUE = ueId && !categoryId;
  const inCategory = !ueId && categoryId;

  const getCheckboxProps = (record) => {
    if (selectedRowKeys.length === 0) {
      return {};
    }
    const firstSelectedRow = getTableDataSource()?.find((item) => item.key === selectedRowKeys[0]);

    if (record?.__typename !== firstSelectedRow?.__typename) {
      return { disabled: true };
    }
    if (
      firstSelectedRow?.__typename === 'UE' &&
      firstSelectedRow?.isFolder &&
      record?.__typename === 'UE' &&
      !record.isFolder
    ) {
      return { disabled: true };
    }
    if (
      firstSelectedRow?.__typename === 'UE' &&
      !firstSelectedRow?.isFolder &&
      record?.__typename === 'UE' &&
      record.isFolder
    ) {
      return { disabled: true };
    }

    return {};
  };

  const onSelectChange = (selectedKeys) => {
    if (selectedKeys.length === 0) {
      setDisabledRowKeys([]);
    }
    setSelectedRowKeys(selectedKeys);
  };

  return (
    <>
      {/*<AdminMenu selected="permissions">*/}
      <FullMediParticlesBreadCrumb title={t('general.Subjects')} />

      <Card style={{ margin: '20px' }}>
        {inRoot && (
          <div
            style={{
              marginTop: 12,
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'center',
              gap: '10px'
            }}
          >
            <ButtonCreateSubject refetch={refetchAll} />
            <ButtonCreateSubject refetch={refetchAll} shouldCreateFolder />
          </div>
        )}
        {inUE && (
          <div
            style={{
              marginTop: 12,
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'center',
              gap: '10px'
            }}
          >
            {/* Créer catégorie seulement si dans matière */}
            {!currentUE?.isFolder && (
              <ButtonCreateCategoryOrFolder refetch={refetchAll} ueId={ueId} small />
            )}
            {/* Créer matière, ou nouveau dossier, seulement si parent est un dossier et pas une matière */}
            {currentUE?.isFolder && (
              <>
                <ButtonCreateSubject refetch={refetchAll} shouldCreateFolder parentUe={currentUE} />
                <ButtonCreateSubject refetch={refetchAll} parentUe={currentUE} />
              </>
            )}
            {/* Créer cours seulement si matière */}
            {!currentUE?.isFolder && (
              <>
                <ButtonCreateCourse refetch={refetchAll} ueId={currentUE?.id} small />
              </>
            )}
            {FEATURE_FLAGS.EnableImportedCourses && (
              <ButtonImportCourse refetch={refetch} ueId={currentUE?.id} small />
            )}
          </div>
        )}
        {inCategory && (
          <div
            style={{
              marginTop: 12,
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'center',
              gap: '10px'
            }}
          >
            <ButtonCreateCategoryOrFolder
              refetch={refetchAll}
              ueId={currentCategory?.ue?.id}
              parentCategory={currentCategory}
              small
            />
            <ButtonCreateCourse refetch={refetchAll} categoryId={categoryId} ueId={null} small />
          </div>
        )}
      </Card>

      {ueId && <CoursBreadcrumb ueId={ueId} withArborescenceAdminLinks withRoot />}
      {categoryId && (
        <CoursBreadcrumb categoryId={categoryId} withArborescenceAdminLinks withRoot />
      )}
      <br />

      <Tabs
        defaultActiveKey={i18n.language}
        destroyInactiveTabPane
        onChange={(activeKey) => setSelectedLanguage(activeKey)}
        style={{ marginLeft: '10px' }}
      >
        {enabledLanguages &&
          enabledLanguages?.map((lang) => (
            <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
              {hasSelected && (
                <ItemsSelectionActions
                  refetch={() => {
                    setSelectedRowKeys([]);
                    setDisabledRowKeys([]);
                    refetchAll();
                  }}
                  selectedRowKeys={selectedRowKeys}
                  showTotal={false}
                />
              )}
              <Table
                rowSelection={{
                  selectedRowKeys,
                  onChange: onSelectChange,
                  getCheckboxProps,
                  hideSelectAll: true
                }}
                loading={isLoadingSomething}
                columns={columns}
                dataSource={getTableDataSource()}
                scroll={{ x: true }}
                pagination={false}
              />
            </Tabs.TabPane>
          ))}
      </Tabs>
    </>
  );
}
