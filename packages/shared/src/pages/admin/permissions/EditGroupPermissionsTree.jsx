import { FadeTransition } from '@/shared/assets/transitions/FadeTransition';
import { QUERY_ALL_GROUPS, QUERY_HIERARCHY_TREE } from '@/shared/graphql/cours.js';
import { UPDATE_GROUP_PERMISSIONS } from '@/shared/graphql/groupes.js';
import { CORE_QCM_TYPE_FRAGMENT } from '@/shared/graphql/qcm';
import GroupAccessibleContentType from '@/shared/pages/admin/groupes/components/GroupAccessibleContentType';
import { renderIcon } from '@/shared/pages/qcm/$index$.jsx';
import { BookTwoTone } from '@ant-design/icons';
import { gql, useQuery, useMutation } from '@apollo/client';
import { Form, Select, Tree, message, Spin, Row, Col, Button, Alert } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const DETAIL_GROUP = gql`
  ${CORE_QCM_TYPE_FRAGMENT}
  query groupe($id: ID!) {
    groupe(id: $id) {
      id
      isIndividual
      ueIds
      ueCategoryIds
      coursIds
      typesQcms {
        ...CoreQcmTypeFields
      }
    }
  }
`;

export const renderTreeNodeIcon = (node) => {
  if (node?.type === 'cours') {
    return <BookTwoTone />;
  }
  return renderIcon(node?.image, node?.type, node?.isFolder);
};

const EditGroupPermissionsTree = ({
  groupeType,
  forGroupId = null,
  isIndividualGroup = false,
  hideTreeTitle = false,
  hideContentType = false,
  onChange
}) => {
  const { t } = useTranslation();
  const { loading, data } = useQuery(QUERY_HIERARCHY_TREE, {
    fetchPolicy: 'cache-and-network',
    variables: {
      selectable: true
    }
  });
  const hierarchyTree = data?.hierarchicalTreeData;

  const [expandedKeys, setExpandedKeys] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [halfCheckedKeys, setHalfCheckedKeys] = useState([]);
  const [initialCheckedKeys, setInitialCheckedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [selectedGroupId, setSelectedGroupId] = useState(forGroupId);
  const [treeData, setTreeData] = useState([]);

  const {
    loading: loadingDetailGroup,
    data: dataDetailGroup,
    refetch: refetchDetailGroupPermissions
  } = useQuery(DETAIL_GROUP, {
    fetchPolicy: 'cache-and-network',
    variables: { id: selectedGroupId },
    skip: !selectedGroupId
  });

  const selectedGroup = dataDetailGroup?.groupe;

  useEffect(() => {
    if (selectedGroup && treeData.length > 0) {
      const { ueIds = [], ueCategoryIds = [], coursIds = [] } = selectedGroup;

      // Helper pour savoir si un id est une feuille
      const isLeaf = (id, type) => {
        const findNode = (nodes) => {
          for (const node of nodes) {
            if (node.key === `${type}-${id}`) return !node.children || node.children.length === 0;
            if (node.children) {
              const found = findNode(node.children);
              if (found !== undefined) return found;
            }
          }
          return undefined;
        };
        return findNode(treeData);
      };

      const checked = [
        ...coursIds.map((id) => `cours-${id}`),
        ...ueIds.filter((id) => isLeaf(id, 'ue')).map((id) => `ue-${id}`),
        ...ueCategoryIds.filter((id) => isLeaf(id, 'category')).map((id) => `category-${id}`)
      ];

      setCheckedKeys(checked);
      setInitialCheckedKeys(checked);
      setSelectedKeys([
        ...ueIds.map((id) => `ue-${id}`),
        ...ueCategoryIds.map((id) => `category-${id}`),
        ...coursIds.map((id) => `cours-${id}`)
      ]);
      if (onChange) {
        onChange({ ueIds, ueCategoryIds, coursIds });
      }
    }
  }, [selectedGroup, dataDetailGroup, treeData]);

  const onExpand = (expandedKeysValue) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  const onSelect = (selectedKeysValue) => {
    setSelectedKeys(selectedKeysValue);
  };

  const onCheck = (checkedKeysValue, event) => {
    setCheckedKeys(checkedKeysValue);
    setHalfCheckedKeys(event.halfCheckedKeys || []);
    if (onChange) {
      const checked = Array.isArray(checkedKeysValue) ? checkedKeysValue : checkedKeysValue.checked;
      const allChecked = [...checked, ...(event.halfCheckedKeys || [])];
      const { ueIds, ueCategoryIds, coursIds } = extractIdsByType(allChecked);
      onChange({ ueIds, ueCategoryIds, coursIds });
    }
  };

  const extractIdsByType = (keys) => {
    const ueIds = [];
    const ueCategoryIds = [];
    const coursIds = [];
    keys.forEach((key) => {
      if (key.startsWith('ue-')) ueIds.push(key.replace('ue-', ''));
      else if (key.startsWith('category-')) ueCategoryIds.push(key.replace('category-', ''));
      else if (key.startsWith('cours-')) coursIds.push(key.replace('cours-', ''));
    });
    return { ueIds, ueCategoryIds, coursIds };
  };

  const [updateGroupPermissions, { loading: loadingUpdate }] =
    useMutation(UPDATE_GROUP_PERMISSIONS);

  const handleSave = async () => {
    if (!selectedGroupId) return;
    const checked = Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked;
    const allChecked = [...checked, ...halfCheckedKeys];
    const { ueIds, ueCategoryIds, coursIds } = extractIdsByType(allChecked);
    try {
      await updateGroupPermissions({
        variables: {
          groupId: selectedGroupId,
          ueIds,
          ueCategoryIds,
          coursIds
        }
      });
      await refetchDetailGroupPermissions();
      message.success({ content: 'Permissions sauvegardées', key: 'permission' });
    } catch (e) {
      message.error({ content: 'Erreur lors de la sauvegarde', key: 'permission' });
    }
  };

  /* Set initial UE data */
  useEffect(() => {
    if (hierarchyTree) {
      setTreeData(hierarchyTree);
      // setExpandedKeys(allUEs?.filter(ue => ue?.parentId === null)?.map(ue => `ue-${ue.id}`));
    }
  }, [hierarchyTree]);

  const showGroupSelection = forGroupId === null;

  const { loading: loadingGroups, data: dataAllGroups } = useQuery(QUERY_ALL_GROUPS, {
    fetchPolicy: 'cache-and-network',
    skip: !showGroupSelection
  });
  const allGroups = dataAllGroups?.allGroupes?.filter((gr) => gr.role === groupeType);
  const loadingAll = loading;

  // Fonction utilitaire pour comparer deux tableaux (ignorer l'ordre)
  const areArraysEqual = (a, b) => {
    if (a.length !== b.length) return false;
    const setA = new Set(a);
    const setB = new Set(b);
    if (setA.size !== setB.size) return false;
    for (let item of setA) if (!setB.has(item)) return false;
    return true;
  };

  // Pour compatibilité Antd v4/v5
  const showSavePrompt = !areArraysEqual(checkedKeys, initialCheckedKeys);

  return (
    <Form layout="vertical">
      {showGroupSelection && (
        <Form.Item label={t('general.Group')}>
          <Select
            onChange={(value, option) => setSelectedGroupId(option?.key)}
            loading={loadingGroups}
            placeholder="Choisir le groupe"
            dropdownMatchSelectWidth={false}
          >
            {allGroups?.map((groupe) => (
              <Select.Option key={groupe.id} value={groupe.name}>
                {groupe.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      )}

      {selectedGroup && (
        <>
          <Row gutter={[16, 16]} justify="space-between">
            <Col xs={24} md={12}>
              {!hideTreeTitle && (
                <h2>
                  {isIndividualGroup ? t('TreeStructureForUser') : t('TreeStructureForGroup')}
                </h2>
              )}

              <Form.Item>
                {loadingAll && treeData?.length === 0 && <Spin />}
                <Tree
                  selectable={false}
                  checkable
                  showLine={true}
                  multiple
                  onExpand={onExpand}
                  expandedKeys={expandedKeys}
                  autoExpandParent={autoExpandParent}
                  onCheck={onCheck}
                  checkedKeys={checkedKeys}
                  onSelect={onSelect}
                  selectedKeys={selectedKeys}
                  treeData={treeData}
                  showIcon
                  icon={renderTreeNodeIcon}
                />
              </Form.Item>

              <FadeTransition
                displayCondition={showSavePrompt}
                style={{ position: 'sticky', bottom: 24 }}
              >
                <Alert
                  style={{ background: '#E7E7E7', borderColor: '#AAAAAA', borderRadius: 100 }}
                  description={t('ModificationsPermissionTreeNotSaved')}
                  type="info"
                  action={
                    <Button
                      loading={loadingUpdate}
                      size="small"
                      type="primary"
                      onClick={handleSave}
                    >
                      {t('general.save')}
                    </Button>
                  }
                />
              </FadeTransition>
            </Col>
            {!hideContentType && (
              <Col xs={24} md={12}>
                <GroupAccessibleContentType
                  groupe={selectedGroup}
                  loadingGroup={loadingDetailGroup}
                  typeQcmsInGroup={selectedGroup?.typesQcms}
                />
              </Col>
            )}
          </Row>
        </>
      )}
    </Form>
  );
};

export default EditGroupPermissionsTree;
