import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager.jsx';
import IndividualPermissionsManager from '@/shared/pages/admin/permissions/IndividualPermissionsManager.jsx';
import React from 'react';

export default function AbstractGroupsAndIndividualGroupsManager({
  groupes = [],
  abstractGroupsManagerProps = {},
  individualPermissionsManagerProps = {},
  mode = { groups: true, individualGroups: true },
  isForeground = true
}) {
  const normalGroups = groupes?.filter((g) => !g?.isIndividual);
  const individualGroups = groupes?.filter((g) => g?.isIndividual);

  // Default always in foreground
  const divStyle = isForeground
    ? {
        flex: 1,
        minWidth: 250,
        zIndex: 99999
      }
    : { flex: 1, minWidth: 250 };

  return (
    <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
      {mode.groups && (
        <div style={divStyle}>
          {abstractGroupsManagerProps.title}
          <AbstractGroupsManager
            {...abstractGroupsManagerProps}
            groupes={normalGroups}
            isForeground
          />
        </div>
      )}

      {mode.individualGroups && (
        <div style={divStyle}>
          {individualPermissionsManagerProps.title}
          <IndividualPermissionsManager
            {...individualPermissionsManagerProps}
            individualGroups={individualGroups}
          />
        </div>
      )}
    </div>
  );
}
