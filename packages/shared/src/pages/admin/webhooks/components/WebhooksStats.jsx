import { QUERY_GET_WEBHOOK_EVENTS_STATS } from '@/shared/graphql/webhooks';
import { useQuery } from '@apollo/client';
import { Card, Col, Row, Statistic, Space, Alert } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function WebhooksStats() {
  const { t } = useTranslation();

  const { loading, data, error, refetch } = useQuery(QUERY_GET_WEBHOOK_EVENTS_STATS, {
    fetchPolicy: 'no-cache'
  });

  const hasTooManyErrors = data?.getWebhookEventsStats?.errors >= 300;

  // Use Antd to display nice stats
  return (
    <div style={{ marginBottom: 10 }}>
      {hasTooManyErrors && (
        <Alert
          message={t('Webhooks.Warning')}
          description={t('Webhooks.TooManyErrors')}
          type="error"
          showIcon
        />
      )}

      <Space
        direction="horizontal"
        //size={20}
        wrap
      >
        <Card style={{ width: 120 }} size={'small'} loading={loading}>
          <Statistic
            title={t('Webhooks.Pending')}
            value={data?.getWebhookEventsStats?.pending || 0}
          />
        </Card>
        <Card size={'small'} loading={loading} style={{ width: 120 }}>
          <Statistic
            title={t('Webhooks.Processed')}
            value={data?.getWebhookEventsStats?.processed || 0}
          />
        </Card>
        <Card size={'small'} loading={loading} style={{ width: 120 }}>
          <Statistic
            title={t('Webhooks.Errors')}
            value={data?.getWebhookEventsStats?.errors || 0}
          />
        </Card>
      </Space>
    </div>
  );
}
