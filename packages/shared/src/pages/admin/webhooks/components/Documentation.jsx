import { Card, Collapse, Select, Tabs } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

const exampleEndpointCode = {
  node: `
import crypto from 'crypto';

function verifyHmacSignature(secret, payload, signatureToCompare) {
  const hash = crypto
    .createHmac('sha256', secret)
    .update(JSON.stringify(payload))
    .digest('hex');
  return hash === signatureToCompare;
}

app.post('/my-webhook-endpoint', express.json(), (req, res) => {
  // Récupérer la signature dans les headers
  const signature = req.headers['X-Webhook-Signature'];
  const payload = req.body;

  // secret qu'on a configuré/manuellement enregistré
  // (le même que celui stocké par le serveur)
  const secret = process.env.WEBHOOK_SECRET || 'clé-fournie-par-le-serveur';

  // Vérifier
  if (!signature || !verifyHmacSignature(secret, payload, signature)) {
    return res.status(400).send('Signature invalide');
  }

  // Si la signature est valide, on traite l'événement
  console.log('Payload reçu:', payload);
  res.sendStatus(200);
});

`,
  ruby: `
require 'sinatra'
require 'openssl'

# ton secret, par exemple récupéré d'une variable d'environnement
WEBHOOK_SECRET = ENV['WEBHOOK_SECRET'] || "secret-d-exemple"

def verify_signature(raw_body, signature)
  # Calcul de la signature HMAC SHA256
  digest = OpenSSL::Digest.new('sha256')
  expected = OpenSSL::HMAC.hexdigest(digest, WEBHOOK_SECRET, raw_body)

  # Comparaison en timing-safe (pour éviter les attaques type timing)
  Rack::Utils.secure_compare(expected, signature)
end

post '/my-webhook-endpoint' do
  # 1. Récupération du header
  header_signature = request.env['HTTP_X_WEBHOOK_SIGNATURE']

  # 2. Corps brut de la requête (sinon request.body.read)
  raw_body = request.body.read

  # 3. Vérification
  unless header_signature && verify_signature(raw_body, header_signature)
    halt 400, "Signature invalide"
  end

  # 4. Traiter le JSON (après la vérif)
  payload = JSON.parse(raw_body)
  # ... logique métier ...
  "OK"
end
  `,

  python: `
from flask import Flask, request, abort
import hmac
import hashlib

app = Flask(__name__)

WEBHOOK_SECRET = "secret-d-exemple"

def verify_hmac_signature(secret, payload, signature):
    # Calcule la signature HMAC SHA-256
    computed_hash = hmac.new(
        secret.encode('utf-8'),
        payload,
        hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(computed_hash, signature)

@app.route('/my-webhook-endpoint', methods=['POST'])
def my_webhook():
    # 1. Récupérer la signature
    header_signature = request.headers.get('X-Webhook-Signature')

    # 2. Corps brut en bytes
    raw_body = request.get_data()  # renvoie un binaire

    # 3. Vérifier
    if not header_signature or not verify_hmac_signature(WEBHOOK_SECRET, raw_body, header_signature):
        abort(400, description="Signature invalide")

    # 4. Charger le JSON pour traitement
    payload = request.json  # ou json.loads(raw_body)
    # ... logique métier ...
    return "OK", 200

if __name__ == '__main__':
    app.run(debug=True, port=5000)


  `,

  phpRaw: `
<?php
// ex: webhook.php
$secret = "secret-d-exemple"; // ou depuis un .env

// 1. Récupération de la signature envoyée (header)
$headerSignature = $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] ?? '';

// 2. Corps brut
$rawBody = file_get_contents('php://input');

// 3. Calcul HMAC
$computedHash = hash_hmac('sha256', $rawBody, $secret);

// 4. Comparaison (timing-safe)
if (!hash_equals($computedHash, $headerSignature)) {
    http_response_code(400);
    echo "Signature invalide";
    exit;
}

// 5. Décoder le JSON
$data = json_decode($rawBody, true);

// ... logique métier ...

// Réponse
http_response_code(200);
echo "OK";

  `,

  phpLaravel: `
public function handleWebhook(Request $request)
{
    $secret = env('WEBHOOK_SECRET', 'secret-d-exemple');
    $headerSignature = $request->header('X-Webhook-Signature');

    $rawBody = $request->getContent(); // corps brut
    $computedHash = hash_hmac('sha256', $rawBody, $secret);

    if (!hash_equals($computedHash, $headerSignature)) {
        return response('Signature invalide', 400);
    }

    // Traiter la charge utile
    $payload = json_decode($rawBody, true);
    // ... logique ...

    return response('OK', 200);
}
  `,

  java: `
@RestController
public class WebhookController {

    // Dans un vrai projet, ce secret viendrait d'une config externe
    private static final String WEBHOOK_SECRET = "secret-d-exemple";

    @PostMapping("/my-webhook-endpoint")
    public ResponseEntity<String> handleWebhook(
        @RequestBody String rawBody,
        @RequestHeader(name = "X-Webhook-Signature", required = false) String signature
    ) {
        if (signature == null || !verifySignature(rawBody, signature)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Signature invalide");
        }

        // Ici, on parse le JSON si besoin
        // ObjectMapper mapper = new ObjectMapper();
        // Map<String, Object> payload = mapper.readValue(rawBody, Map.class);
        // ... logique ...

        return ResponseEntity.ok("OK");
    }

    private boolean verifySignature(String rawBody, String signature) {
        try {
            // Calcul de l'HMAC SHA256
            SecretKeySpec keySpec = new SecretKeySpec(WEBHOOK_SECRET.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(keySpec);
            byte[] rawHmac = mac.doFinal(rawBody.getBytes(StandardCharsets.UTF_8));
            String computedHash = bytesToHex(rawHmac);

            // Comparaison en timing-safe -> en Java, on peut utiliser MessageDigest.isEqual()
            // ou Apache Commons Codec
            return MessageDigest.isEqual(computedHash.getBytes(), signature.getBytes());
        } catch (Exception e) {
            return false;
        }
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}
`
};

export default function WebhooksDocumentation() {
  const { t } = useTranslation();

  const [selectedLanguage, setSelectedLanguage] = React.useState('node');

  return (
    <>
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane key="1" tab={t('Webhooks.EndpointExample')}>
          {/*Conseils communs:
Récupérer le corps brut (sans modification) pour recalculer correctement la signature.
Ne pas oublier d’utiliser exactement le même algorithme (HmacSHA256, par exemple) et le même format pour la chaîne (UTF-8, JSON stringifié).
Comparer la signature reçue et la signature calculée de manière secure compare (protection contre les attaques de timing).
Gérer les erreurs (pas de signature, signature invalide, JSON non valide, etc.) en renvoyant un statut HTTP 4xx.
*/}
          <Select
            value={selectedLanguage}
            onChange={(value) => setSelectedLanguage(value)}
            style={{ width: 220 }}
          >
            <Select.Option value="node">NodeJS</Select.Option>
            <Select.Option value="ruby">Ruby</Select.Option>
            <Select.Option value="python">Python</Select.Option>
            <Select.Option value="phpRaw">PHP (brut)</Select.Option>
            <Select.Option value="phpLaravel">PHP (Laravel)</Select.Option>
            <Select.Option value="java">Java</Select.Option>
          </Select>

          <br />

          {/* With Antd, show code example */}
          <pre>
            <code>{exampleEndpointCode[selectedLanguage]}</code>
          </pre>
        </Tabs.TabPane>

        <Tabs.TabPane key="2" tab={t('Webhooks.Payloads')}>
          <Collapse accordion>
            <Collapse.Panel header="user.created" key="user.created">
              <pre>
                <code>
                  {`
{
  id,
  email, // Email de l'utilisateur
  username, // Pseudo
  firstName, // Prénom
  name, // Nom
  role, // Rôle (ex: USER, TUTEUR, ADMIN)
  addressline1, // Adresse ligne 1
  addressline2, // Adresse ligne 2
  postcode, // Code postal
  phone, // Téléphone
  city, // Ville
  bio, // Biographie
  userCodeName, // Code utilisateur
  parentsEmail, // Email des parents
  parentsPhone, // Téléphone des parents
  parentsProfession // Profession des parents
  isExtraTime, // tiers temps
  title, // Role de l'utilisateur informationnel (Prof de biochimie, etc)
  birthdate, // Date de naissance
  gender, // Genre
  nationality, // Nationalité
  country, // Pays
  hasSetupUsername, // Si l'utilisateur a configuré son pseudo
  hasSetupPassword, // Si l'utilisateur a configuré son mot de passe
  createdAt, // Date de création
  updatedAt, // Date de mise à jour
  // Groupes de l'utilisateur
  groups {
    id
    name
  }
}
`}
                </code>
              </pre>
            </Collapse.Panel>

            <Collapse.Panel header="user.has_setup_password" key="has_setup_password">
              <pre>
                <code>
                  {`
{
  id,
  email,
  username
}
`}
                </code>
              </pre>
            </Collapse.Panel>

            <Collapse.Panel header="form.completed" key="form.completed">
              <pre>
                <code>
                  {`
{
  // Utilisateur ayant complété le formulaire
  user: { 
    id,
    email,
    username,
    firstName,
    name,
    role,
    addressline1,
    addressline2,
    postcode,
    phone,
    city,
    bio,
    userCodeName
    parentsEmail
    parentsPhone
    parentsProfession
    isExtraTime, // tiers temps
    title,
    birthdate,
    gender,
    nationality,
    country,
    hasSetupUsername,
    hasSetupPassword,
    createdAt,
    updatedAt,
  },
  // Formulaire complété
  form: {
    id,
    name, // Nom du formulaire
    createdAt, // Date de création du formulaire
    updatedAt, // Date de mise à jour du formulaire
    uuid, // id unique du formulaire (utilisé pour le lien vers le formulaire)
    isMandatory, // Si le formulaire est obligatoire
    oneAnswerByUser, // Si un seul formulaire peut être complété par utilisateur
  },
 
  formData: [ // Données du formulaire
    // Objet de champ complété
    {
      id, // ID du champ
      value, // Valeur du champ (si unique, type string)
      values, // Valeurs du champ (si multiple, type array)
      createdAt, // Date de complétion du champ (ou du formulaire, selon si le formulaire est découpé en étapes ou pas)
      element: {
        // Element lié au champ (ex: un select, un input, etc.)
        id,
        name, // Nom du champ (exemple: Métier, spécialité, etc)
        description, // Description du champ (optionnel)
        type, // type de champ (shortAnswer pour réponse courte, longAnswer pour réponse longue, singleSelect pour selection unique, multipleSelect pour selection multiple, datePicker pour date, etc)
        settings : {
          // JSON, paramètres du champ: propriété hubspot (hubspotProperty)
          hubspotProperty, // optionnel
          answers: {
            // optionnel, liste des choix possibles
            text, // texte affiché
            groups // array des id de groupes associés à la réponse
          }, 
          isMandatory, // optionnel, si le champ est obligatoire
          groups, // optionnel, groupes ajoutés en fonction de réponse
        },
        formId, // ID du formulaire
        
        ... // Autres propriétés de l'élément
        
        createdAt, // Date de création de l'élément
        updatedAt, // Date de mise à jour de l'élément
      },
      
    },
    ...
  ]
}
`}
                </code>
              </pre>
            </Collapse.Panel>
          </Collapse>
        </Tabs.TabPane>
      </Tabs>
    </>
  );
}
