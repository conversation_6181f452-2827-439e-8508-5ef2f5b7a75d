import { SimpleMoveTransition } from '@/shared/assets/transitions/SimpleMoveTransition.jsx';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import { QUERY_ANNONCE_GENERALES_ELEMENTS } from '@/shared/graphql/formations';
import { useQuery } from '@apollo/client';
import FormationEditableElement from '@/shared/pages/formations/components/FormationEditableElement';
import { FormationContextProvider } from '@/shared/pages/formations/context/FormationContext';
import { CreateEditFormationElementModal } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal';
import { ELEMENTS_TYPE } from '@/shared/services/formations';
import { Button, Card } from 'antd';
import { PlusCircleTwoTone } from '@ant-design/icons';
import { AnimatePresence } from 'framer-motion/dist/framer-motion';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';

export const GLOBAL_ANNOUNCE_TYPES = {
  parents: 'parents',
  users: 'users'
};
export default function (props) {
  const { t } = useTranslation();

  const { data, loading, error, refetch } = useQuery(QUERY_ANNONCE_GENERALES_ELEMENTS, {
    variables: {
      globalAnnounceType: GLOBAL_ANNOUNCE_TYPES.parents
    },
    fetchPolicy: 'cache-and-network'
  });

  const elements = data?.elementsForGlobalAnnounce || [];

  /* Elements related */
  const [createVisible, setCreateVisible] = useState(false);
  const [position, setPosition] = useState(null);

  const renderElement = (element, previousElement, nextElement, key, columnPosition) => (
    <FormationEditableElement
      style={{ transition: 'transform .35s ease-in-out' }}
      key={key}
      element={element}
      nextElement={nextElement}
      previousElement={previousElement}
      globalAnnounceType={GLOBAL_ANNOUNCE_TYPES.parents}
      columnPosition={columnPosition}
      canEdit
      refetchAll={refetch}
    />
  );

  const elementCreationComponents = (
    <>
      {createVisible && (
        <div
          style={{
            border: '1px dashed #b5b5b5',
            borderRadius: '11px',
            margin: 5,
            marginBottom: '15px'
          }}
        >
          <div style={{ margin: '15px' }}>
            <CreateEditFormationElementModal
              globalAnnounceType={GLOBAL_ANNOUNCE_TYPES.parents}
              isModalVisible={createVisible}
              modalType="CREATE"
              position={position}
              closeModalHandler={async (newId) => {
                setCreateVisible(false);
                refetch();
              }}
              elementsTypesToShow={{
                // Affichage
                [ELEMENTS_TYPE.TITLE]: true,
                [ELEMENTS_TYPE.IMAGE]: true,
                [ELEMENTS_TYPE.MCQ]: false,
                [ELEMENTS_TYPE.LINK]: true,
                [ELEMENTS_TYPE.HTML]: true,
                [ELEMENTS_TYPE.COURS]: false,
                [ELEMENTS_TYPE.COURSE_SHORTCUT]: false,
                [ELEMENTS_TYPE.FILE]: true,
                [ELEMENTS_TYPE.RICH_TEXT]: true,
                [ELEMENTS_TYPE.VIDEO]: false, // obsolète
                [ELEMENTS_TYPE.VIDSTACK_VIDEO]: true,
                [ELEMENTS_TYPE.VIDSTACK_AUDIO]: true,
                [ELEMENTS_TYPE.DIAPO]:true,
                [ELEMENTS_TYPE.CALLOUT]: true,
                [ELEMENTS_TYPE.SCORM]:false,
                // Début de "step"
                [ELEMENTS_TYPE.SECTION]: false,
                //-------

                // INPUTS
                [ELEMENTS_TYPE.SHORT_ANSWER]: false,
                [ELEMENTS_TYPE.LONG_ANSWER]: false,
                [ELEMENTS_TYPE.SINGLE_SELECT]: false,
                [ELEMENTS_TYPE.MULTIPLE_SELECT]: false,
                [ELEMENTS_TYPE.INTEGER_NUMBER]: false,
                [ELEMENTS_TYPE.DATE_PICKER]: false,
                [ELEMENTS_TYPE.DATE_AND_TIME_PICKER]: false,
                [ELEMENTS_TYPE.FILE_IMPORT]: false,
                [ELEMENTS_TYPE.AVATAR_SELECT]: false,

                [ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT]: false
              }}
            />
          </div>
        </div>
      )}
    </>
  );

  const renderFormationElementCreation = (elementPosition = null) => (
    <>
      {elementPosition === position && elementCreationComponents}
      {!createVisible && (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
          <div style={{ width: '90%' }}>
            <Button
              style={{ marginTop: 10, minHeight: '50px' }}
              type="dashed"
              block
              icon={<PlusCircleTwoTone />}
              onClick={() => {
                setPosition(elementPosition);
                setCreateVisible(true);
              }}
            >
              {t('AddElementHere')}
            </Button>
          </div>
        </div>
      )}
    </>
  );

  // Query élements annonces générales
  return (
    <>
      <FullMediParticlesBreadCrumb title={t('Parents')} />

      <div style={{ margin: '24px' }}>
        <h2>{t('globalAnnouncesForParents')}</h2>

        <div
          style={{
            marginRight: 'auto',
            marginLeft: 'auto',
            maxWidth: '900px',
            marginBottom: '24px'
          }}
        >
          <Card>
            {loading && !elements && <SpinnerCentered />}

            <div style={{ maxWidth: '800px' }}>
              <FormationContextProvider>
                <AnimatePresence mode="popLayout">
                  {elements.map((elem, k) => (
                    <SimpleMoveTransition id={elem.id} key={elem.id}>
                      {renderElement(elem, elements[k - 1], elements[k + 1], elem?.id)}
                    </SimpleMoveTransition>
                  ))}
                  {renderFormationElementCreation()}
                </AnimatePresence>
              </FormationContextProvider>
            </div>
          </Card>
        </div>
      </div>
    </>
  );
}
