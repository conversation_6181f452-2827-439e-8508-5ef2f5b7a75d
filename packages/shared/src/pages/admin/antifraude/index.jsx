import React, { useState } from 'react';
import { useQuery } from '@apollo/client';
import { QUERY_ALL_GROUPS } from '@/shared/graphql/cours';
import { router } from 'umi';
import { PageHeader } from '@ant-design/pro-layout';
import { Button, Table } from 'antd';
import { ErrorResult } from '@/shared/components/ErrorResult';
import dayjs from 'dayjs';
import MediSpin from '@/shared/components/PageLoading/MediSpin';
import {
  CreateEditGroupModal,
  ModalType
} from '@/shared/pages/admin/groupes/CreateEditGroupeModal';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const GroupeActions = ({ refetch, record, loading }) => {
  const [editVisible, setEditVisible] = useState(false);
  return (
    <span>
      <Button
        // size="large"
        onClick={() => {
          setEditVisible(true);
        }}
        style={{ marginRight: 16 }}
        type="primary"
        shape="circle"
        icon={<EditOutlined />}
      />

      <CreateEditGroupModal
        id={record.id}
        name={record.name}
        isVisible={editVisible}
        modalType={ModalType.UPDATE}
        closeModalHandler={() => {
          setEditVisible(false);
          refetch(); // Load new modifications
        }}
      />

      <Button
        onClick={() => {}}
        shape="circle"
        style={{ marginRight: 16 }}
        type="danger"
        icon={<DeleteOutlined />}
      />
    </span>
  );
};

export default function (props) {
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_ALL_GROUPS, { fetchPolicy: 'no-cache' });
  const [createVisible, setCreateVisible] = useState(false);

  const closeModalHandler = () => {
    setCreateVisible(false);
    refetch(); // Load new modifications
  };
  const openModalHandler = () => {
    setCreateVisible(true);
  };

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Créé le',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text) => dayjs(text).format('DD/MM/YYYY')
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => <GroupeActions record={record} refetch={refetch} key={record.id} />
    }
  ];

  return (
    <>
      <PageHeader onBack={() => router.goBack()} title={t('Admin')} extra={[]} />

      {/*<AdminMenu selected="groupes" />*/}

      {data && data.allGroupes && !error && !loading && (
        <>
          <Table columns={columns} dataSource={data.allGroupes} scroll={{ x: true }} />

          <Button onClick={openModalHandler}>Ajouter</Button>

          <CreateEditGroupModal
            closeModalHandler={closeModalHandler}
            isVisible={createVisible}
            modalType={ModalType.CREATE}
            refetch={refetch}
            loading={loading}
          />
        </>
      )}

      {loading && !error && (
        <div style={{ textAlign: 'center' }}>
          <br />
          &nbsp;
          <MediSpin fontSize="48px" />
        </div>
      )}

      {error && !loading && <ErrorResult refetch={refetch} error={error} />}
    </>
  );
}
