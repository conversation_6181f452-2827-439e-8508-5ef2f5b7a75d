import { FileImage } from '@/shared/components/FileImage.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { QUERY_ALL_CHALLENGES } from '@/shared/graphql/challenges.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { CreateEditChallengeModal } from '@/shared/pages/admin/challenges/modal/CreateEditChallengeModal.jsx';
import { getLanguageName, missingTranslation, tr } from '@/shared/services/translate.js';
import { EditOutlined } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { Button, Card, Table, Tabs, Tag } from 'antd';
import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';

const ChallengeActions = ({ refetch, record, loading }) => {
  const [editVisible, setEditVisible] = useState(false);
  const { t } = useTranslation();
  return (
    <span>
      <Button
        // size="large"
        onClick={() => {
          setEditVisible(true);
        }}
        style={{ marginRight: 16 }}
        type="primary"
        shape="circle"
        icon={<EditOutlined />}
      />

      {editVisible && (
        <CreateEditChallengeModal
          challenge={record}
          isVisible={editVisible}
          modalType={'UPDATE'}
          refetch={refetch}
          closeModalHandler={() => {
            setEditVisible(false);
            refetch(); // Load new modifications
          }}
        />
      )}
    </span>
  );
};

export default function (props) {
  const { t, i18n } = useTranslation();
  const { enabledLanguages } = useContext(GlobalContext);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);

  const { loading, error, data, refetch } = useQuery(QUERY_ALL_CHALLENGES, {
    fetchPolicy: 'cache-and-network'
  });
  const allChallenges = data?.allChallenges || [];
  const [createVisible, setCreateVisible] = useState(false);

  const closeModalHandler = () => {
    refetch(); // Load new modifications
    setCreateVisible(false);
  };
  const openModalHandler = () => {
    setCreateVisible(true);
  };

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: t('Name'),
      dataIndex: tr('name', selectedLanguage),
      key: 'name',
      render: (name, challenge) => name || missingTranslation
    },
    {
      title: t('Description'),
      dataIndex: tr('description', selectedLanguage),
      key: 'description',
      render: (desc, challenge) => (
        <>
          {challenge?.types?.map((type, index) => (
            <Tag key={type?.id} color="blue">
              {type?.name}
            </Tag>
          ))}
          <br />
          <p>{desc || missingTranslation}</p>
        </>
      )
    },
    {
      title: 'Image',
      dataIndex: 'image',
      key: 'image',
      render: (image, c) => <FileImage key={c.id} image={image} style={{ maxHeight: '36px' }} />
    },
    {
      title: 'Publié',
      dataIndex: 'isPublished',
      key: 'isPublished',
      render: (p) =>
        p ? <Tag color="green">{t('general.yes')}</Tag> : <Tag>{t('general.no')}</Tag>
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => (
        <ChallengeActions record={record} refetch={refetch} key={record.id} />
      )
    }
  ];

  return (
    <>
      <FullMediParticlesBreadCrumb title={t('allChallenges')} />

      <Card style={{ margin: '15px' }}>
        <Button onClick={openModalHandler}>{t('general.add')}</Button>
      </Card>

      <Tabs
        defaultActiveKey={i18n.language}
        destroyInactiveTabPane
        onChange={(activeKey) => setSelectedLanguage(activeKey)}
        style={{ marginLeft: '10px' }}
      >
        {enabledLanguages &&
          enabledLanguages?.map((lang) => (
            <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
              <Table
                loading={loading}
                columns={columns}
                dataSource={allChallenges}
                scroll={{ x: true }}
              />
            </Tabs.TabPane>
          ))}
      </Tabs>

      <CreateEditChallengeModal
        isVisible={createVisible}
        modalType="CREATE"
        refetch={refetch}
        closeModalHandler={closeModalHandler}
      />
    </>
  );
}
