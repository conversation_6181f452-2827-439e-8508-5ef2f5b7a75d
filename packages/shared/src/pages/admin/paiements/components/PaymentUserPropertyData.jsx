import { EditableFormElementHandler } from '@/shared/components/User/UserProperties/UserProperties.jsx';
import { tr } from '@/shared/services/translate.js';
import { Table } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

export const PaymentUserPropertyData = ({ userPropertyData, refetch, logData }) => {
  const { t } = useTranslation();

  const columns = [
    /*
          {
            title: t('general.User'),
            dataIndex: 'userId',
            key: 'userId',
            render: (id, record) => {
              if(id) {
                return <ExoUserLight id={id} />;
              } else {
    
              }
            },
          },
        */
    {
      title: 'Item',
      dataIndex: 'element',
      key: 'element_name',
      width: '30%',
      render: (_, record) => <div style={{ fontWeight: 500 }}>{record?.element?.[tr('name')]}</div>,
    },
    // Display value
    {
      title: 'Réponse',
      key: 'value',
      dataIndex: 'id',
      render: (_, record) => (
        <EditableFormElementHandler
          element={record.element}
          canEdit={false}
          refetch={refetch}
          userId={record?.userId}
          id={record.id}
          userPropertyValueProp={record?.value}
          userPropertyValuesProp={record?.values}
        />
      ),
    },
    /*
            {
              title: t('DateOfCompletion'),
              dataIndex: 'createdAt',
              key: 'createdAt',
              render: (text, record) => {
                return dayjs(text).format('DD/MM/YYYY HH:mm');
              },
            },
             */
  ];

  const registerFieldColumns = [
    {
      dataIndex: 'name',
      key: 'name',
    },
    {
      dataIndex: 'value',
      key: 'value',
    },
  ];

  // Ajout des champs registerFields
  const registerFields = [
    {
      id: 'Nom',
      name: 'Nom',
      value: logData?.lastName,
    },
    {
      id: 'Prénom',
      name: 'Prénom',
      value: logData?.firstName,
    },
    {
      id: 'Email',
      name: 'Email',
      value: logData?.email,
    },
    {
      id: 'Téléphone',
      name: 'Téléphone',
      value: logData?.phone,
    },
    {
      id: 'Adresse',
      name: 'Adresse',
      value: logData?.adresse,
    },
    {
      id: 'Code Postal',
      name: 'Code Postal',
      value: logData?.codepostal,
    },
    {
      id: 'Ville',
      name: 'Ville',
      value: logData?.ville,
    },
    {
      id: 'Pays',
      name: 'Pays',
      value: logData?.country,
    },
    {
      id: 'promoCode',
      name: 'Code promo(s) utilisé',
      value: logData?.promoCodes?.join(' '),
    },
  ];

  return (
    <>
      {userPropertyData?.length > 0 && (
        <Table dataSource={userPropertyData} columns={columns} rowKey="id" pagination={false} />
      )}
      <Table
        showHeader={false}
        dataSource={registerFields}
        columns={registerFieldColumns}
        rowKey="id"
        pagination={false}
      />
      <br />
    </>
  );
};
