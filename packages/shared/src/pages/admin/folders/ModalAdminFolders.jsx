import { AdminFolders } from '@/shared/pages/admin/folders/index.jsx';
import { Modal } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function(
  {
    closeModalHandler,
    isVisible,
  }) {
  const {t} = useTranslation();
  return (
    <Modal
      title={t('ManageFolders')}
      open={isVisible}
      onCancel={closeModalHandler}
      footer={null}
      closable
      confirmLoading={false}
      width={1200}
    >
      {isVisible && (
        <AdminFolders/>
      )}
    </Modal>
  );
}