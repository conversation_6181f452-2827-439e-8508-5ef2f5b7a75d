import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { QUERY_FOLDERS_TREE_DATA } from '@/shared/graphql/folders.js';
import { useQuery } from '@apollo/client';
import { Tag, TreeSelect } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

export const ROOT_FOLDER_TREE_NODE = {
  title: '🏠 (Racine)',
  value: 'folder-root',
  id: 0,
  key: 'folder-root',
  type: 'folder',
}

// Pour déplacer une entité dans les dossiers, ou sélectionner un dossier pour une entité.
//
const FoldersTreeSelect = ({ folderType = 'FOLDER', onClick, defaultValue = null }) => {
  const { t } = useTranslation();

  const { loading, error, data, refetch } = useQuery(QUERY_FOLDERS_TREE_DATA, {
    fetchPolicy: 'no-cache',
    variables: { folderType },
  });
  const foldersTreeData = [ROOT_FOLDER_TREE_NODE, ...(data?.foldersTreeData || [])];

  if (loading) {
    return <SpinnerCentered />;
  }

  const tagRender = props => {
    const { value, closable, onClose, key, label } = props;
    return (
      <Tag value={value} key={key} closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
        {label}
      </Tag>
    );
  };

  return (
    <>
      {foldersTreeData && (
        <TreeSelect
          tagRender={tagRender}
          treeLine
          defaultValue={defaultValue ? `folder-${defaultValue}` : null}
          placeholder={t('ChooseFolder')}
          treeNodeFilterProp="title"
          treeData={foldersTreeData}
          style={{ width: '100%' }}
          onChange={async (newValue, label, extra) => {
            const key = extra.triggerValue; // 'folder-123'
            const shouldAdd = extra.checked;
            if(key === 'folder-root') {
              onClick(shouldAdd, null); // null = root folder
              return;
            }
            const id = key?.split('-')[1];
            onClick(shouldAdd, id);
          }}
        />
      )}
    </>
  );
};

export default FoldersTreeSelect;
