import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import { FileImage } from '@/shared/components/FileImage.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { ProtectedImage } from '@/shared/components/ProtectedImage.jsx';
import {
  MUTATION_CREATE_BUILDING,
  MUTATION_DELETE_BUILDING,
  MUTATION_UPDATE_BUILDING,
  QUERY_ALL_BUILDINGS
} from '@/shared/graphql/events.js';
import { ModalType } from '@/shared/pages/admin/config/company/CreateEditCompanyModal.jsx';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import LoadingOutlined from '@ant-design/icons/lib/icons/LoadingOutlined';
import PlusOutlined from '@ant-design/icons/lib/icons/PlusOutlined';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Card, Form, Input, Modal, Table, Upload } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, router } from 'umi';

const getMutationFromModalType = (modalType) => {
  switch (modalType) {
    case ModalType.CREATE:
      return MUTATION_CREATE_BUILDING;
    case ModalType.UPDATE:
      return MUTATION_UPDATE_BUILDING;
    default:
      return MUTATION_CREATE_BUILDING;
  }
};

export const CreateEditBuildingModal = ({ visible, handleCloseModal, building, modalType }) => {
  const [Mutation, { loading, error }] = useMutation(getMutationFromModalType(modalType));
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const [fileImage, setFileImage] = useState(null);

  const onFinish = async (data) => {
    const fileImageParams = fileImage ? { image: fileImage } : {};
    try {
      if (modalType === ModalType.CREATE) {
        await Mutation({
          variables: {
            input: { ...data, ...fileImageParams }
          }
        });
      } else {
        await Mutation({
          variables: {
            id: building.id,
            input: { ...data, ...fileImageParams }
          }
        });
      }
      handleCloseModal();
    } catch (e) {
      console.log(e);
    }
  };
  const beforeUpload = (file, fileList) => {
    setFileImage(file);
    return false;
  };
  return (
    <Modal
      open={visible}
      title={building ? 'Edit building' : 'Create building'}
      okText={building ? 'Update' : 'Create'}
      footer={null}
      onCancel={handleCloseModal}
    >
      <Form layout="vertical" initialValues={building} onFinish={onFinish}>
        <Form.Item name="name" label={t('BuildingName')} rules={[{ required: true }]}>
          <Input />
        </Form.Item>

        <Form.Item name="address" label={t('general.address')}>
          <Input.TextArea rows={2} />
        </Form.Item>
        <Form.Item name="city" label={t('general.city')}>
          <Input />
        </Form.Item>
        <Form.Item name="postCode" label={t('zipCode')}>
          <Input />
        </Form.Item>
        <Form.Item name="country" label={t('general.Country')}>
          <Input />
        </Form.Item>

        <Form.Item label="Image">
          <Upload.Dragger
            name="image"
            listType="picture"
            showUploadList
            accept="image/*"
            beforeUpload={beforeUpload}
            fileList={fileImage ? [fileImage] : []}
            onRemove={() => setFileImage('')}
          >
            {building?.image ? (
              <ProtectedImage src={building?.image} alt={'ue'} style={{ maxWidth: 100 }} />
            ) : (
              ''
            )}
            <div>
              {loading ? <LoadingOutlined /> : <PlusOutlined />}
              <div className="ant-upload-text">{t('general.upload')}</div>
            </div>
          </Upload.Dragger>
        </Form.Item>

        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loading}>
            {building ? t('Update') : t('Create')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

function BuildingActions({ building, refetch }) {
  const [editVisible, setEditVisible] = useState(false);
  const { t } = useTranslation();
  const [deleteBuilding] = useMutation(MUTATION_DELETE_BUILDING);

  const handleDelete = async () => {
    try {
      await deleteBuilding({
        variables: {
          id: building.id
        }
      });
      refetch();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.log(e);
    }
  };
  return (
    <>
      {editVisible && (
        <CreateEditBuildingModal
          visible={editVisible}
          handleCloseModal={() => {
            setEditVisible(false);
            refetch();
          }}
          modalType={ModalType.UPDATE}
          building={building}
        />
      )}
      <Button
        type="primary"
        onClick={() => {
          setEditVisible(true);
          refetch();
        }}
      >
        {t('general.Edit')}
      </Button>
      <Button danger onClick={handleDelete}>
        {t('Delete')}
      </Button>
    </>
  );
}

export default function (props) {
  const { t } = useTranslation();

  const { data, loading, error, refetch } = useQuery(QUERY_ALL_BUILDINGS, {
    fetchPolicy: 'cache-and-network'
  });
  const buildings = data?.allBuildings || [];

  const columns = [
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <>
          <FileImage src={record.image} alt={text} style={{ width: '50px', height: '50px' }} />
          <Link to={`/admin/building/${record.id}`}>{text}</Link>
        </>
      )
    },
    {
      title: t('general.address'),
      dataIndex: 'address',
      key: 'address',
      render: (text, record) => (
        <>
          {record.address} {record?.city} {record?.postCode} {record?.country}
        </>
      )
    },
    {
      title: t('Rooms'),
      dataIndex: 'rooms',
      key: 'rooms',
      render: (text, record) => (
        <Button
          onClick={() => {
            router.push(`/admin/building/${record.id}`);
          }}
        >
          {record.rooms?.length} {t('rooms')}
        </Button>
      )
    },
    {
      title: t('Actions'),
      key: 'actions',
      render: (_, record) => <BuildingActions building={record} refetch={refetch} />
    }
  ];

  const [createVisible, setCreateVisible] = useState(false);

  return (
    <>
      <FullMediParticlesBreadCrumb title={t('general.Buildings')} />
      <ExoteachLayout>
        <CreateEditBuildingModal
          visible={createVisible}
          handleCloseModal={() => {
            setCreateVisible(false);
            refetch();
          }}
          modalType={ModalType.CREATE}
        />
        <br />
        <Card>
          <Button onClick={() => setCreateVisible(true)}>{t('CreateNewBuilding')}</Button>
        </Card>
        <br />

        <Table
          columns={columns}
          dataSource={buildings}
          loading={loading}
          rowKey="id"
          pagination={false}
        />
      </ExoteachLayout>
    </>
  );
}
