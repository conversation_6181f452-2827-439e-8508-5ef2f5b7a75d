import { Button, Divider, Space, Typography } from 'antd';
import React from 'react';

const { Text, Link } = Typography;

const PermissionsSettings = ({ permissions, setPermissions }) => {
  const handlePermissionChange = (category, level) => {
    setPermissions((prevPermissions) => ({
      ...prevPermissions,
      [category]: level
    }));
  };

  return (
    <div style={{ marginBottom: 32 }}>
      <div style={{ marginBottom: 16 }}>
        <Text strong>Utilisateurs</Text>
      </div>

      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text>Gestion des utilisateurs</Text>
          <Space>
            <Button
              type={permissions.users === 'none' ? 'primary' : 'default'}
              onClick={() => handlePermissionChange('users', 'none')}
            >
              Aucune
            </Button>
            <Button
              type={permissions.users === 'read' ? 'primary' : 'default'}
              onClick={() => handlePermissionChange('users', 'read')}
            >
              Lecture
            </Button>
            <Button
              type={permissions.users === 'write' ? 'primary' : 'default'}
              onClick={() => handlePermissionChange('users', 'write')}
            >
              Écriture
            </Button>
          </Space>
        </div>

        <Divider />

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text>Gestion des groupes</Text>
          <Space>
            <Button
              type={permissions.groups === 'none' ? 'primary' : 'default'}
              onClick={() => handlePermissionChange('groups', 'none')}
            >
              Aucune
            </Button>
            <Button
              type={permissions.groups === 'read' ? 'primary' : 'default'}
              onClick={() => handlePermissionChange('groups', 'read')}
            >
              Lecture
            </Button>
            <Button
              type={permissions.groups === 'write' ? 'primary' : 'default'}
              onClick={() => handlePermissionChange('groups', 'write')}
            >
              Écriture
            </Button>
          </Space>
        </div>
      </div>
    </div>
  );
};

export default PermissionsSettings;
