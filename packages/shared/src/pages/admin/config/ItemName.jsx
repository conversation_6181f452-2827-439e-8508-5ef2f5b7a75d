import { useTranslation } from 'react-i18next';
import {
  QUERY_COURS_WITH_PARENT,
  QUERY_UE_CATEGORY_ID_WITH_CHILDREN,
  QUERY_UE_WITH_PARENT,
} from '@/shared/graphql/cours';
import { tr } from '@/shared/services/translate';
import { useQuery } from '@apollo/client';
import React from 'react';

export default function({ coursId = null, categoryId = null, ueId = null }) {
  const { t } = useTranslation();
  const { data: dataCurrentCategory, loading: loadingCurrentCategory } = useQuery(
    QUERY_UE_CATEGORY_ID_WITH_CHILDREN,
    {
      variables: { id: categoryId },
      fetchPolicy: 'cache-and-network',
      skip: !categoryId,
    },
  );
  const currentCategory = dataCurrentCategory?.ueCategory;
  const { data: dataUE, loading: loadingUE } = useQuery(QUERY_UE_WITH_PARENT, {
    variables: { id: ueId },
    fetchPolicy: 'cache-and-network',
    skip: !ueId,
  });
  const currentUE = dataUE?.ue;
  const { data: dataCours, loading: loadingCours } = useQuery(QUERY_COURS_WITH_PARENT, {
    variables: { id: coursId },
    fetchPolicy: 'cache-and-network',
    skip: !coursId,
  });
  const currentCours = dataCours?.cour;

  const itemNameToShow =
    currentUE?.[tr('name')] || currentCategory?.[tr('name')] || currentCours?.[tr('name')];

  const loading = loadingUE || loadingCours || loadingCurrentCategory;

  if (loading) {
    return <div>{t('Loading')}</div>;
  }

  return <div>{itemNameToShow}</div>;
}
