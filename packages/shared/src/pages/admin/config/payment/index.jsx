import { useExoteachPaymentMethodsQuery } from '@/shared/hooks/config/useConfigHooks.js';
import { CreateEditPaymentModal } from '@/shared/pages/admin/config/payment/CreateEditPaymentModal.jsx';
import { useMutation } from '@apollo/client';
import {
  MUTATION_DELETE_CONFIG_BY_ID,
  MUTATION_UPDATE_CONFIG,
  MUTATION_UPLOAD_FILE
} from '@/shared/graphql/home';
import { useTranslation } from 'react-i18next';
import { Button, Form, message, Popconfirm, Space, Table } from 'antd';
import React, { useState } from 'react';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import ExoteachLayout from '@/shared/components/ExoteachLayout';
import { showGqlErrorsInMessagePopupFromException, tryParseJSONObject } from '@/shared/utils/utils';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { ModalType } from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal';
import { ErrorResult } from '@/shared/components/ErrorResult';

export const PaymentAction = ({ refetch, record, loading }) => {
  const { t } = useTranslation();
  const [editVisible, setEditVisible] = useState(false);
  const [MutationDeleteConfig] = useMutation(MUTATION_DELETE_CONFIG_BY_ID);
  const payment = tryParseJSONObject(record.value);

  const handleDelete = async (id) => {
    try {
      await MutationDeleteConfig({ variables: { id } });
      message.success(t('DeletedWithSuccess'));
      refetch();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  return (
    <span>
      <Button
        onClick={() => {
          setEditVisible(true);
        }}
        style={{ marginRight: 16 }}
        type="primary"
        shape="circle"
        icon={<EditOutlined />}
      />
      <CreateEditPaymentModal
        payment={payment}
        isVisible={editVisible}
        modalType={ModalType.UPDATE}
        closeModalHandler={() => {
          setEditVisible(false);
          refetch(); // Load new modifications
        }}
        companyDomain={record?.domain}
        id={record?.id}
      />

      <Popconfirm title={t('SureOfDeletion')} onConfirm={() => handleDelete(record.id)}>
        <Button shape="circle" danger style={{ marginRight: 16 }} icon={<DeleteOutlined />} />
      </Popconfirm>
    </span>
  );
};

export default function (props) {
  const { t } = useTranslation();

  const { paymentConfig, loading, error, refetch } = useExoteachPaymentMethodsQuery();

  const [Mutation] = useMutation(MUTATION_UPDATE_CONFIG);
  const [uploadFile] = useMutation(MUTATION_UPLOAD_FILE);
  const [form] = Form.useForm();
  const [createVisible, setCreateVisible] = useState(false);

  const closeModalHandler = () => {
    refetch(); // Load new modifications
    setCreateVisible(false);
  };
  const openModalHandler = () => {
    setCreateVisible(true);
  };

  const columns = [
    {
      title: t('Name'),
      // dataIndex: 'name',
      key: 'name',
      render: (_, record) => {
        const value = tryParseJSONObject(record?.value);
        const name = value?.name;
        const type = value?.type;
        return (
          <Space>
            {type === 'stripe' && (
              <img
                src="https://avatars.githubusercontent.com/u/856813?s=200&v=4"
                style={{ width: 32, height: 32, marginRight: 10 }}
                alt={'stripeLogo'}
              />
            )}
            {type === 'monetico' && (
              <img
                src="https://www.scaledev.fr/wp-content/uploads/elementor/thumbs/monetico-pm29vc9uzr2nqx8ckw328cq6pig5es0nl4psda7454.png"
                style={{ width: 32, height: 32, marginRight: 10 }}
                alt={'moneticoLogo'}
              />
            )}
            {name}
          </Space>
        );
      }
    },
    /*
    {
      title: t('Domain'),
      dataIndex: 'domain',
      key: 'domain',
      render: (domain, record) => {
        return <>{domain || <Tag>Default</Tag>}</>;
      },
    },
    */
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => <PaymentAction record={record} refetch={refetch} key={record.id} />
    }
  ];

  return (
    <>
      <FullMediParticlesBreadCrumb title={t('general.Payment')} />
      <ExoteachLayout>
        <br />
        <Button type="primary" icon={<PlusOutlined />} onClick={openModalHandler}>
          {t('general.add')}
        </Button>
        <br />

        <CreateEditPaymentModal
          closeModalHandler={closeModalHandler}
          isVisible={createVisible}
          modalType={ModalType.CREATE}
          refetch={refetch}
          loading={loading}
        />

        {!error && (
          <Table
            loading={loading}
            columns={columns}
            dataSource={paymentConfig}
            scroll={{ x: true }}
            pagination={false}
          />
        )}
        {error && !loading && <ErrorResult refetch={refetch} error={error} />}
      </ExoteachLayout>
    </>
  );
}
