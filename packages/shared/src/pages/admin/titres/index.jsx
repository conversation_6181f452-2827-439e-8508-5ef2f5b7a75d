import Title from '@/shared/components/Formation/Title.jsx';
import { QUERY_ALL_TITLES } from '@/shared/graphql/formations.js';
import { CreateEditTitleModal } from '@/shared/pages/formations/components/modal/CreateEditTitleModal.jsx';
import React, { useState } from 'react';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { Button, Card, Table } from 'antd';
import { ErrorResult } from '@/shared/components/ErrorResult';
import { ModalType } from '@/shared/pages/admin/groupes/CreateEditGroupeModal';
import { EditOutlined } from '@ant-design/icons';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';

const TitresActions = ({ refetch, record, loading }) => {
  const [editVisible, setEditVisible] = useState(false);
  const { t } = useTranslation();
  return (
    <span>
      <Button
        // size="large"
        onClick={() => {
          setEditVisible(true);
        }}
        style={{ marginRight: 16 }}
        type="primary"
        shape="circle"
        icon={<EditOutlined />}
      />

      <CreateEditTitleModal
        id={record.id}
        name={record.name}
        title={record}
        isModalVisible={editVisible}
        modalType={ModalType.UPDATE}
        closeModalHandler={() => {
          setEditVisible(false);
          refetch(); // Load new modifications
        }}
      />
    </span>
  );
};

export default function (props) {
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_ALL_TITLES, {
    fetchPolicy: 'cache-and-network'
  });
  const [createVisible, setCreateVisible] = useState(false);
  const allTitles = data?.allTitles;

  const closeModalHandler = () => {
    setCreateVisible(false);
    refetch(); // Load new modifications
  };
  const openModalHandler = () => {
    setCreateVisible(true);
  };

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name',
      render: (name, obj) => <Title element={{ name }} title={obj} showDescription={false} />
    },
    {
      title: 'Taille',
      dataIndex: 'size',
      key: 'size'
    },
    {
      title: 'Niveau',
      dataIndex: 'level',
      key: 'level'
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => <TitresActions record={record} refetch={refetch} key={record.id} />
    }
  ];

  return (
    <>
      <FullMediParticlesBreadCrumb title={t('general.Title')} />
      <Card style={{ margin: 20 }}>
        <Button onClick={openModalHandler}>{t('general.add')}</Button>
      </Card>
      <Table loading={loading} columns={columns} dataSource={allTitles} scroll={{ x: true }} />

      <CreateEditTitleModal
        closeModalHandler={closeModalHandler}
        isModalVisible={createVisible}
        modalType={ModalType.CREATE}
        refetch={refetch}
        loading={loading}
      />
      {error && !loading && <ErrorResult refetch={refetch} error={error} />}
    </>
  );
}
