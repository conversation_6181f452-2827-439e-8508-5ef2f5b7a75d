import { QuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal';
import { GroupRender } from '@/shared/pages/admin/schemas/components/Layers/GroupRender';
import { IS_DEV } from '@/shared/utils/utils';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { Group } from 'react-konva';

const LegendsRender = ({
  legends,
  selectedTool,
  setSelectedLegendId,
  opacity,
  handleClickOnLegend,
  selectedLegendId,
  exerciseSettings,
  mode,
  correction = false // Affichage correction exercice
}) => {
  const [highlightLegendId, setHighlighLegendId] = useState(null);
  const [renderCount, setRenderCount] = useState(0); // State to track changes

  if (IS_DEV) {
    console.log('LegendsRender');
  }

  const legendsToShow = useMemo(() => {
    // A la correction on affiche seulement la légende sélectionnée
    if (mode === QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS && correction) {
      return legends.filter((legend) => legend.id === selectedLegendId);
    }

    // On ne charge que les légendes visibles en mode admin bibliothèque
    if (mode === null) {
      return legends.filter((legend) => legend.isVisible);
    }
    // mode exercice: on charge toutes les légendes et on masquera celles qui ne sont pas dans les settings
    if (mode === QuestionAnswerType.SCHEMA_POINT_AND_CLICK) {
      if (correction && selectedLegendId) {
        return legends.filter((legend) => legend.id === selectedLegendId);
      } else {
        return legends;
      }
    }
    // Mode exercise edition pour profs, affiche seulement les legendes dont les ID sont dans les settings (visibleLegends)
    return legends.filter((legend) => exerciseSettings.visibleLegends?.includes(legend.id));
  }, [legends, exerciseSettings, correction, mode, selectedLegendId]);

  useEffect(() => {
    setRenderCount((prevCount) => prevCount + 1); // Update renderCount to force re-render
  }, [legendsToShow]);

  const handleMouseEnter = (e, legendId) => {
    if (
      mode === QuestionAnswerType.SCHEMA_POINT_AND_CLICK ||
      ['eraser'].includes(selectedTool) ||
      selectedTool !== null
    ) {
      return;
    }
    setHighlighLegendId(legendId);
  };
  const handleMouseLeave = (e, legendId) => {
    if (mode === QuestionAnswerType.SCHEMA_POINT_AND_CLICK) {
      return;
    }
    setHighlighLegendId(null);
  };

  return (
    <Group key={renderCount}>
      {legendsToShow?.map((legend, legendIndex) => {
        const highlight = legend.id === highlightLegendId;
        let opacityToShow = opacity;
        if (mode === QuestionAnswerType.SCHEMA_POINT_AND_CLICK) {
          // On met opacité à 0 pour les légendes qui ne sont pas dans visibleLegends. Il faut les charger pour que le point & click marche
          if (!correction && !exerciseSettings?.visibleLegends?.includes(legend.id)) {
            opacityToShow = 0;
          }
        }
        return (
          <GroupRender
            key={legend.id}
            legendIndex={legendIndex}
            legend={legend}
            highlight={highlight}
            opacity={opacityToShow}
            handleMouseEnter={handleMouseEnter}
            handleMouseLeave={handleMouseLeave}
            setSelectedLegendId={setSelectedLegendId}
            handleClickOnLegend={handleClickOnLegend}
          />
        );
      })}
    </Group>
  );
};

export default memo(LegendsRender, (prevProps, nextProps) => {
  //console.log('LegendsRender memo');
  return (
    prevProps.legends === nextProps.legends &&
    prevProps.selectedTool === nextProps.selectedTool &&
    prevProps.opacity === nextProps.opacity &&
    prevProps.exerciseSettings === nextProps.exerciseSettings &&
    prevProps.mode === nextProps.mode &&
    prevProps.selectedLegendId === nextProps.selectedLegendId
  );
});
