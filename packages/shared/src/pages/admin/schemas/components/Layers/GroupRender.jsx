import { LegendShapeRender } from '@/shared/pages/admin/schemas/components/Layers/LegendShapeRender';
import { IS_DEV } from '@/shared/utils/utils';
import React, { useEffect, useRef } from 'react';
import { Group } from 'react-konva';

/**
 * Chaque légende dans un groupe pour gérer transparence
 * @param legend
 * @param opacity
 * @param legendIndex
 * @param highlight
 * @param handleMouseEnter
 * @param handleMouseLeave
 * @param setSelectedLegendId
 * @param handleClickOnLegend
 * @returns {JSX.Element}
 * @constructor
 */
export const GroupRender = ({
  legend,
  opacity,
  legendIndex,
  highlight,
  handleMouseEnter,
  handleMouseLeave,
  setSelectedLegendId,
  handleClickOnLegend
}) => {
  const groupRef = useRef(null);

  if (IS_DEV) {
    console.log('GroupRender');
  }

  useEffect(() => {
    if (groupRef.current) {
      groupRef.current.cache();
      groupRef.current.opacity(opacity);
    }
  }, [legend, opacity, highlight]);

  const handleGroupMouseOver = (e) => {};
  const handleGroupMouseOut = (e) => {};

  return (
    <Group ref={groupRef} opacity={opacity}>
      <LegendShapeRender
        key={legendIndex}
        legend={legend}
        highlight={highlight}
        opacity={opacity}
        handleMouseEnter={handleMouseEnter}
        handleMouseLeave={handleMouseLeave}
        setSelectedLegendId={setSelectedLegendId}
        handleClickOnLegend={handleClickOnLegend}
      />
    </Group>
  );
};
