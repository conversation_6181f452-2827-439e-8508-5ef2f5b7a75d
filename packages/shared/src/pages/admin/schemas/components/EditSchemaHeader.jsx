import { HierarchySelecter, validesTypes } from '@/shared/components/HierarchySelecter';
import FillInLegendsAutoExercice from '@/shared/pages/admin/schemas/components/AutoExercises/FillInLegendsAutoExercice';
import QuickEditAutoSchemaExercise from '@/shared/pages/admin/schemas/components/AutoExercises/QuickEditAutoSchemaExercise';
import { SchemaTypeQcmManager } from '@/shared/pages/admin/schemas/components/SchemaType/SchemaTypeQcmManager';
import { SchemaEditionContext } from '@/shared/pages/admin/schemas/context/SchemaEditionContext';
import { EditOutlined, QuestionCircleTwoTone } from '@ant-design/icons';
import { Radio, Space, Tooltip, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const EditSchemaHeader = ({ mode = null, hasSelectedAType, setHasSelectedAType }) => {
  const { t } = useTranslation();

  const {
    schema,
    // Cours liés
    selectedCoursId,
    setSelectedCoursId,
    name,
    description,
    setName,
    setDescription,
    handleSave,
    handleSaveHeader,
    view,
    setView,
    coursSelectorVisible,
    refetch
  } = React.useContext(SchemaEditionContext);

  const editingSchemaFromLibrary = mode === null;

  useEffect(() => {
    setKey((prev) => prev + 1); // Pour faire fonctionner le HierarchySelecter quand on change de schema
  }, [schema]);
  const [key, setKey] = useState(0);
  return (
    <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
      <div style={{ marginBottom: 12 }}>
        <div>
          <Typography.Title
            level={4}
            editable={
              editingSchemaFromLibrary
                ? {
                    onChange: (v) => {
                      setName(v);
                      handleSaveHeader({ name: v });
                    }
                  }
                : false
            }
          >
            {name || 'Cliquez pour éditer le nom...'}
          </Typography.Title>
        </div>

        <div>
          <Typography.Text
            editable={
              editingSchemaFromLibrary
                ? {
                    onChange: (v) => {
                      setDescription(v);
                      handleSaveHeader({ description: v });
                    }
                  }
                : false
            }
            style={{ color: 'grey' }}
          >
            {description || 'Cliquez pour éditer la description...'}
          </Typography.Text>
        </div>

        {mode === null && coursSelectorVisible && (
          <div style={{ marginTop: 8, marginBottom: 8 }}>
            <div style={{}}>
              <HierarchySelecter
                setterHookSelection={(value) => {
                  setSelectedCoursId(value);
                }}
                initialisationVariable={
                  selectedCoursId === null ? null : { [validesTypes.CTYPE_COURS]: selectedCoursId }
                }
                simplificationFeature={validesTypes.CTYPE_COURS}
                rankToRemoveIfLeaf={[
                  validesTypes.CTYPE_FOLDER,
                  validesTypes.CTYPE_UE,
                  validesTypes.CTYPE_PAGE,
                  validesTypes.CTYPE_UNKNOWN,
                  validesTypes.CTYPE_CATEGORY
                ]}
                disabledTypes={[
                  validesTypes.CTYPE_FOLDER,
                  validesTypes.CTYPE_UE,
                  validesTypes.CTYPE_PAGE,
                  validesTypes.CTYPE_UNKNOWN,
                  validesTypes.CTYPE_CATEGORY
                ]}
                useTreeSelect
                additionalTreeProps={{
                  //disabled: isCoursSelectionDisabled,
                  placeholder: t('LinkCoursesToExerciseSelecterPlaceholder'),
                  style: {
                    width: 'auto',
                    marginRight: '5px',
                    minWidth: '500px'
                  }
                }}
                key={key}
              />
            </div>
          </div>
        )}

        {/* Publié / non publié et type de schéma, afficher seulement sur schemaLibrary */}
        {schema && editingSchemaFromLibrary && (
          <div>
            <h3>
              <Space>
                Exercice automatique
                <Tooltip
                  title={
                    'ExoTeach peut générer automatiquement des exercices sur les schémas de votre bibliothèque pour qu’ils apparaissent dans le générateur ou les modules de cours ! Configurez ci-dessous leur disponibilité'
                  }
                  placement="right"
                >
                  <QuestionCircleTwoTone />
                </Tooltip>
              </Space>
            </h3>

            {/* Point and click */}
            <Space direction={'horizontal'} wrap style={{ marginBottom: 12 }}>
              <div>
                <b>Point & click</b>{' '}
                <QuickEditAutoSchemaExercise exerciseId={schema?.autoExercisePointAndClickId} />
              </div>
              <div>
                <Radio.Group
                  disabled={!editingSchemaFromLibrary}
                  buttonStyle="solid"
                  defaultValue={schema?.isPublished}
                  onChange={(e) => {
                    handleSaveHeader({ isPublished: e.target.value });
                  }}
                >
                  <Radio.Button value={true}>{t('general.Published')}</Radio.Button>
                  <Radio.Button value={false}>{t('FilterExercices.Unpublished')}</Radio.Button>
                </Radio.Group>
              </div>
              <SchemaTypeQcmManager
                disabled={!editingSchemaFromLibrary}
                schemaLibraryId={schema?.id}
                typesQcm={schema?.types}
                hasSelectedAType={hasSelectedAType}
                setHasSelectedAType={setHasSelectedAType}
              />
            </Space>

            {/* Fill in legends */}
            <FillInLegendsAutoExercice
              schema={schema}
              editingSchemaFromLibrary={editingSchemaFromLibrary}
              handleSaveHeader={handleSaveHeader}
              refetch={refetch}
            />
          </div>
        )}
      </div>

      <div>
        <div style={{ margin: 'auto', textAlign: 'center', marginTop: 42, marginBottom: 12 }}>
          <Radio.Group value={view} onChange={(e) => setView(e.target.value)}>
            <Radio.Button value={'schema'}>Schéma vierge</Radio.Button>
            <Radio.Button value={'correction'}>Schéma corrigé</Radio.Button>
          </Radio.Group>
        </div>
      </div>
    </div>
  );
};
