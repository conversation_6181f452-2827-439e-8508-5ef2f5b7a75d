import { SchemaExerciseModes } from '@/shared/pages/admin/qcm/components/modal/components/Schemas/EditSchemaPointAndClick';
import EditSchema from '@/shared/pages/admin/schemas/components/EditSchema.jsx';
import { EditSchemaHeader } from '@/shared/pages/admin/schemas/components/EditSchemaHeader';
import { SchemaEditionContextProvider } from '@/shared/pages/admin/schemas/context/SchemaEditionContext.jsx';
import { Alert } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function EditFullSchema({
  schemaId,
  setBreadcrumbTitle,
  mode = null,
  settings,
  setSettings,
  canEditCorrection = true
}) {
  const { t } = useTranslation();
  const editingSchemaFromLibrary = mode === null;

  const [hasSelectedAType, setHasSelectedAType] = useState(!editingSchemaFromLibrary); // Si on édite depuis mode exercice, on affiche pas le choix de type

  return (
    <SchemaEditionContextProvider
      mode={mode}
      schemaId={schemaId}
      setBreadcrumbTitle={setBreadcrumbTitle}
      exerciseSettings={settings}
      setExerciseSettings={setSettings}
    >
      {mode === SchemaExerciseModes.PointAndClick && (
        <Alert type={'info'} message={t('Schemas.InstructionsCheckLegends')} showIcon />
      )}
      {mode === SchemaExerciseModes.FillInLegends && (
        <Alert type={'info'} message={t('Schemas.InstructionsPlaceLegendsToComplete')} showIcon />
      )}
      <EditSchemaHeader
        mode={mode}
        hasSelectedAType={hasSelectedAType}
        setHasSelectedAType={setHasSelectedAType}
      />
      <div
        style={{
          width: 'auto',
          //border: '0.5px solid #a8a8a8',
          borderRadius: 5,
          paddingLeft: 10
        }}
      >
        {hasSelectedAType ? (
          <EditSchema mode={mode} canEditCorrection={canEditCorrection} />
        ) : (
          <Alert type={'info'} message={'Sélectionnez au moins un type pour continuer'} showIcon />
        )}
      </div>
    </SchemaEditionContextProvider>
  );
}
