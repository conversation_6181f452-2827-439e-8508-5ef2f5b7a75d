import { QUERY_QUESTION_DETAILS_FROM_DO_SINGLE_QUESTION } from '@/shared/graphql/qcm';
import { TypeQcmToQuestionManager } from '@/shared/pages/admin/qcm/components/modal/components/TypeQcmToQuestionManager';
import { MUTATION_UPDATE_QUESTION } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal';
import QuickEditAutoSchemaExercise from '@/shared/pages/admin/schemas/components/AutoExercises/QuickEditAutoSchemaExercise';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { PlusOutlined } from '@ant-design/icons';
import { useMutation, useQuery } from '@apollo/client';
import { Button, notification, Radio, Space, Tag } from 'antd';
import gql from 'graphql-tag';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

const MUTATION_GENERATE_AUTO_FILL_IN_LEGENDS_EXERCISE = gql`
  mutation generateAutoFillInLegendsExercise($schemaLibraryId: ID!) {
    generateAutoFillInLegendsExercise(schemaLibraryId: $schemaLibraryId) {
      id_question
    }
  }
`;

export default function FillInLegendsAutoExercice({
  schema,
  editingSchemaFromLibrary,
  handleSaveHeader,
  refetch
}) {
  const { t } = useTranslation();

  const [generateAutoFillInLegendsExercise, { loading }] = useMutation(
    MUTATION_GENERATE_AUTO_FILL_IN_LEGENDS_EXERCISE
  );

  const [updateQuestionMutation] = useMutation(MUTATION_UPDATE_QUESTION);
  const [exerciseId, setExerciseId] = useState(schema?.autoExerciseFillInLegendId);
  const [editingQuestion, setEditingQuestion] = useState(false);

  const { data: dataQuestion, refetch: refetchQuestionDetails } = useQuery(
    QUERY_QUESTION_DETAILS_FROM_DO_SINGLE_QUESTION,
    {
      variables: {
        id: exerciseId
      }
    }
  );
  const exercise = dataQuestion?.question;

  useEffect(() => {
    setExerciseId(schema?.autoExerciseFillInLegendId);
  }, [schema?.autoExerciseFillInLegendId]);

  // Need mutation to create auto exercise
  const handleCreateAutoExercise = async () => {
    try {
      const result = await generateAutoFillInLegendsExercise({
        variables: {
          schemaLibraryId: schema.id
        }
      });
      const exerciseId = result.data.generateAutoFillInLegendsExercise.id_question;
      await refetch(); // recharger schema lib pour voir le nouvel exercice

      setExerciseId(exerciseId);
      // Auto open modal after creation for admin to check
      setEditingQuestion(true);

      notification.info({
        message: 'Exercice créé automatiquement',
        description: 'Vérifiez le placement des points de légendes et sauvegardez',
        duration: 5
      });
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  return (
    <div>
      <Space direction={'horizontal'} wrap style={{ marginBottom: 12 }}>
        <div>
          <b>Légendes à remplir</b>{' '}
          {exerciseId && (
            <QuickEditAutoSchemaExercise
              editingQuestion={editingQuestion}
              exerciseId={exerciseId}
              refetch={refetchQuestionDetails}
            />
          )}
        </div>
        {exerciseId ? (
          <>
            <div>
              <Radio.Group
                disabled={!editingSchemaFromLibrary}
                buttonStyle="solid"
                value={exercise?.isPublished}
                onChange={async (e) => {
                  //handleSaveHeader({ isPublished: e.target.value });
                  await updateQuestionMutation({
                    variables: {
                      id: exercise.id_question,
                      question: {
                        isPublished: e.target.value
                      }
                    }
                  });
                  await refetchQuestionDetails();
                }}
              >
                <Radio.Button value={true}>{t('general.Published')}</Radio.Button>
                <Radio.Button value={false}>{t('FilterExercices.Unpublished')}</Radio.Button>
              </Radio.Group>
            </div>

            {exercise && (
              <TypeQcmToQuestionManager
                typesQcm={exercise?.types}
                questionId={exercise?.id_question}
              />
            )}

            {/*
            <SchemaTypeQcmManager
              disabled={!editingSchemaFromLibrary}
              schemaLibraryId={schema?.id}
              typesQcm={schema?.types}
              //hasSelectedAType={hasSelectedAType}
              //setHasSelectedAType={setHasSelectedAType}
            />
            */}
          </>
        ) : (
          <>
            <Tag color="warning">Aucun</Tag>
            &nbsp;
            <Button
              //loading={loadingMutation}
              onClick={handleCreateAutoExercise}
              icon={<PlusOutlined />}
            >
              Créer
            </Button>
          </>
        )}
      </Space>
    </div>
  );
}
