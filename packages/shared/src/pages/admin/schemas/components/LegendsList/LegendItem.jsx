import EditableByDoubleClick from '@/shared/components/ContentEdition/EditableByDoubleClick';
import ModalSelectLegends from '@/shared/pages/admin/schemas/components/LegendsList/ModalSelectLegends.jsx';
import {
  DownOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  MinusOutlined,
  PlusOutlined,
  RightOutlined
} from '@ant-design/icons';
import { Badge, Button, Checkbox, Space } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

export const LegendItem = ({
  legend,
  index,
  legends,
  setLegends,
  selectedLegendId,
  setSelectedLegendId,
  canHaveChildren = true,
  parentLegend,
  toggleCollapse,
  isCollapsed,
  mode,
  exerciseSettings,
  setExerciseSettings
}) => {
  const { t } = useTranslation();

  const [modalInsertVisible, setModalInsertVisible] = useState(false);

  const editingSchemaFromLibrary = mode === null;

  const modalProps = {
    modalInsertVisible,
    setModalInsertVisible,
    legend,
    legends,
    setLegends,
    index
  };

  const handleClickOnLegend = () => {
    if (editingSchemaFromLibrary) {
      setSelectedLegendId(legend?.id);
    }
  };

  const handleClickCollapse = (e) => {
    e.stopPropagation();
    toggleCollapse(legend.id);
  };

  const handleClickPlusButton = (e) => {
    e.stopPropagation();
    setModalInsertVisible(true);
  };

  const handleClickMinusButton = (e) => {
    e.stopPropagation();
    const newLegends = [...legends];
    const parentIndex = legends.findIndex((l) => l.id === parentLegend?.id);
    newLegends[parentIndex].childrenLegendsIds = newLegends[parentIndex].childrenLegendsIds.filter(
      (id) => id !== legend?.id
    );
    setLegends(newLegends);
  };

  const handleClickShowHideButton = (e) => {
    e.stopPropagation();
    if (editingSchemaFromLibrary) {
      // Mode edition schema
      const newLegends = [...legends];
      // Find index of the legend to update
      const lIndex = newLegends.findIndex((l) => l.id === legend?.id);
      // Update legends state
      newLegends[lIndex].isVisible = !newLegends[lIndex].isVisible;
      setLegends(newLegends);
    } else {
      // mode exercice
      const newSettings = { ...exerciseSettings };
      if (newSettings.visibleLegends?.includes(legend.id)) {
        newSettings.visibleLegends = newSettings.visibleLegends.filter((id) => id !== legend.id);
      } else {
        if (Array.isArray(newSettings.visibleLegends) && newSettings.visibleLegends.length > 0) {
          newSettings.visibleLegends = [...newSettings.visibleLegends, legend.id];
        } else {
          newSettings.visibleLegends = [legend.id];
        }
      }
      setExerciseSettings(newSettings);
    }
  };

  // Mode exercise: click on checkbox
  const handleClickCheckbox = (e) => {
    e.stopPropagation();
    const newSettings = { ...exerciseSettings };
    if (newSettings.checkedLegends?.includes(legend.id)) {
      newSettings.checkedLegends = newSettings.checkedLegends.filter((id) => id !== legend.id);
    } else {
      if (Array.isArray(newSettings.checkedLegends) && newSettings.checkedLegends.length > 0) {
        newSettings.checkedLegends = [...newSettings.checkedLegends, legend.id];
      } else {
        newSettings.checkedLegends = [legend.id];
      }
    }
    setExerciseSettings(newSettings);
  };

  const getEyeIcon = () => {
    if (editingSchemaFromLibrary) {
      return legend?.isVisible ? <EyeOutlined /> : <EyeInvisibleOutlined />;
    } else {
      // Si la légende est cochée d'après settings.visibleLegends, on affiche l'oeil ouvert
      // Sinon on affiche l'oeil fermé
      return exerciseSettings?.visibleLegends?.includes(legend.id) ? (
        <EyeOutlined />
      ) : (
        <EyeInvisibleOutlined />
      );
    }
  };

  return (
    <div
      style={{
        height: 40,
        backgroundColor: selectedLegendId === legend?.id ? 'rgba(24,144,255,0.4)' : 'transparent',
        cursor: 'pointer',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        border: '0.5px solid #f0f0f0'
      }}
      key={index}
      onClick={handleClickOnLegend}
    >
      {modalInsertVisible && <ModalSelectLegends {...modalProps} />}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis'
        }}
      >
        {mode !== null && (
          <>
            <Checkbox
              checked={exerciseSettings?.checkedLegends?.includes(legend.id)}
              onChange={handleClickCheckbox}
            />
            &nbsp;&nbsp;
          </>
        )}
        <Badge color={legend?.color} /> &nbsp;&nbsp;
        {editingSchemaFromLibrary ? (
          <EditableByDoubleClick
            value={legend?.name}
            onChange={(text) => {
              const newLegends = [...legends];
              const lIndex = newLegends.findIndex((l) => l.id === legend?.id);
              newLegends[lIndex].name = text;
              setLegends(newLegends);
            }}
            type="text"
          />
        ) : (
          <span>{legend?.name}</span>
        )}
      </div>
      <Space>
        {/* Bouton collapse > */}
        {canHaveChildren && (
          <div>
            {legend?.childrenLegendsIds?.length > 0 && (
              <Button
                onClick={handleClickCollapse}
                type={'text'}
                icon={isCollapsed ? <RightOutlined /> : <DownOutlined />}
                size={'small'}
              />
            )}
          </div>
        )}

        {editingSchemaFromLibrary && (
          <>
            {/* Boutons + ou - */}
            {canHaveChildren ? (
              <div>
                <Button
                  onClick={handleClickPlusButton}
                  type={'text'}
                  icon={<PlusOutlined />}
                  size={'small'}
                />
              </div>
            ) : (
              <div>
                <Button
                  onClick={handleClickMinusButton}
                  type={'text'}
                  icon={<MinusOutlined />}
                  size={'small'}
                />
              </div>
            )}
          </>
        )}

        {/* Bouton oeil afficher/masquer */}
        <div>
          <Button
            onClick={handleClickShowHideButton}
            type={'text'}
            icon={getEyeIcon()}
            size={'small'}
          />
        </div>
      </Space>
    </div>
  );
};
