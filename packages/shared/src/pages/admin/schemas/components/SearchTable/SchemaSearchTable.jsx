import {
  MUTATION_CREATE_SCHEMA,
  MUTATION_DELETE_SCHEMA,
  QUERY_SCHEMAS
} from '@/shared/graphql/schemas';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { getLanguageName, missingTranslation, tr } from '@/shared/services/translate';
import { isAdmin } from '@/shared/utils/authority';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  RollbackOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Card, Checkbox, Image, Input, Popconfirm, Space, Table, Tabs, Tag } from 'antd';
import i18n from 'i18next';
import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, router } from 'umi';

const SchemaActions = ({
  record,
  refetch,
  edit,
  linkToExercise,
  onLinkToExercise = null,
  canDelete
}) => {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [deleteMutation, { loading, error }] = useMutation(MUTATION_DELETE_SCHEMA);

  return (
    <Space>
      {edit && (
        <Button
          icon={<EditOutlined />}
          type="primary"
          shape="circle"
          onClick={() => {
            //setVisible(true);
            router.push(`/admin-schemas/edit/${record.id}`);
          }}
        />
      )}
      {linkToExercise && (
        <Button
          icon={<PlusOutlined />}
          type="primary"
          shape="circle"
          onClick={() => {
            if (onLinkToExercise) {
              onLinkToExercise(record);
            }
          }}
        />
      )}

      {canDelete && isAdmin() && (
        <Popconfirm
          title={t('AreYouSure')}
          onConfirm={async () => {
            await deleteMutation({
              variables: {
                id: record.id
              }
            });
            refetch();
          }}
          // onCancel={() => setVisible(false)}
        >
          <Button
            shape="circle"
            icon={record.deletedAt === null ? <DeleteOutlined /> : <RollbackOutlined />}
            danger
          />
        </Popconfirm>
      )}
    </Space>
  );
};

export const SchemaSearchTable = ({
  onLinkToExercise,
  edit = true,
  canDelete = true,
  linkToExercise = false
}) => {
  const { t } = useTranslation();
  const { enabledLanguages } = useContext(GlobalContext);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);

  const [searchText, setSearchText] = useState('');
  const [limit, setLimit] = useState(10); // Default page size
  const [offset, setOffset] = useState(0);
  const [deleted, setDeleted] = useState(false);
  const [currentPage, setCurrentPage] = React.useState(1);
  const { data, loading, error, refetch } = useQuery(QUERY_SCHEMAS, {
    variables: {
      filter: {
        name: searchText,
        offset,
        limit,
        deleted
      }
    },
    fetchPolicy: 'no-cache'
  });
  const schemas = data?.schemas?.schemas || [];
  const totalResults = data?.schemas?.count || 0;
  const [createSchema, { loading: loadingCreate }] = useMutation(MUTATION_CREATE_SCHEMA);

  const handleCreate = async () => {
    const returnedData = await createSchema({
      variables: {
        input: {
          name: 'Nouveau schema'
        }
      }
    });
    const newSchema = returnedData.data.createSchema;
    router.push(`/admin-schemas/edit/${newSchema.id}`);
  };

  const columns = [
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <div
          style={{ cursor: 'pointer' }}
          onClick={() => {
            //setVisible(true);
            router.push(`/admin-schemas/edit/${record.id}`);
          }}
        >
          <b>{record?.[tr('name', selectedLanguage)] || ''}</b>
          <div style={{ overflow: 'hidden', height: '50px', width: '100%', fontSize: '10px' }}>
            {record?.[tr('description', selectedLanguage)] || ''}
          </div>
        </div>
      )
    },
    {
      title: t('Image'),
      dataIndex: 'image',
      key: 'image',
      render: (image, record) => (
        <Image
          src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + image)}
          style={{ height: '90px', width: 'auto' }}
          fallback="data:image/png;base64,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"
        />
      )
    },
    // Cours liés linkedCourses
    {
      title: t('CoursesLinked'),
      dataIndex: 'linkedCourses',
      key: 'linkedCourses',
      render: (linkedCourses, record) => (
        <>
          {linkedCourses?.map((cours, index) => {
            const coursToShow = cours?.targetCours || cours;
            return (
              <Link to={`/cours/${cours.id}`} key={cours.id}>
                <Tag color="blue">
                  {index >= 1 && ' '}{' '}
                  <b>{coursToShow?.[tr('name', selectedLanguage)] || missingTranslation}</b>{' '}
                  {coursToShow?.[tr('text', selectedLanguage)]}
                </Tag>
              </Link>
            );
          })}
        </>
      )
    },
    // Add "Statut": Publié / Non publié
    {
      title: t('Status'),
      dataIndex: 'isPublished',
      key: 'isPublished',
      render: (isPublished) => (
        <Tag color={isPublished ? 'green' : 'red'}>{isPublished ? 'Publié' : 'Non publié'}</Tag>
      )
    },
    //updatedAt createdAt
    {
      title: t('Actions'),
      dataIndex: 'actions',
      key: 'actions',
      render: (text, record) => (
        <>
          <SchemaActions
            record={record}
            refetch={refetch}
            edit={edit}
            linkToExercise={linkToExercise}
            canDelete={canDelete}
            onLinkToExercise={onLinkToExercise}
          />
        </>
      )
    }
  ];

  return (
    <>
      <div style={{ margin: 24 }}>
        <Card style={{ width: 600 }}>
          <div style={{ marginBottom: 24 }}>
            <Button icon={<PlusOutlined />} type="primary" onClick={handleCreate}>
              {t('Create')}
            </Button>
          </div>

          {/* Add input search bar for search text */}
          <Input
            style={{ maxWidth: 300 }}
            placeholder={t('general.search')}
            prefix={<SearchOutlined />}
            onChange={(e) => setSearchText(e.target.value)}
          />
          <br />
          <br />
          <Checkbox
            onChange={(e) => {
              setDeleted(e.target.checked);
              //refetch();
            }}
            checked={deleted}
          >
            Supprimés
          </Checkbox>
        </Card>
        {/*
            <Playground />
          */}
        <Tabs
          defaultActiveKey={i18n.language}
          destroyInactiveTabPane
          onChange={(activeKey) => setSelectedLanguage(activeKey)}
          style={{ marginLeft: '10px' }}
        >
          {enabledLanguages &&
            enabledLanguages?.map((lang) => (
              <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                <Table
                  dataSource={schemas}
                  columns={columns}
                  loading={loading}
                  rowKey="id"
                  pagination={{
                    current: currentPage,
                    pageSize: limit,
                    showTotal: (total) => `${total} résultats`,
                    onChange: (page, pageSize) => {
                      setLimit(pageSize);
                      setCurrentPage(page);
                      setOffset((page - 1) * pageSize);
                    },
                    total: totalResults,
                    size: 'large',
                    responsive: true
                  }}
                />
              </Tabs.TabPane>
            ))}
        </Tabs>
      </div>
    </>
  );
};
