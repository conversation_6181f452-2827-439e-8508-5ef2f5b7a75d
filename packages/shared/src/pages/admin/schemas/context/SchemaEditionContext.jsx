import {
  MUTATION_UPDATE_LINKED_COURSES_SCHEMA,
  MUTATION_UPDATE_SCHEMA,
  QUERY_SCHEMA_BY_ID_WITH_AUTO_EXERCISES
} from '@/shared/graphql/schemas.js';
import { IS_DEV, showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { useMutation, useQuery } from '@apollo/client';
import { Form, notification } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';

const DEFAULT_STROKE_WIDTH = 15;

export const SchemaEditionContext = React.createContext(undefined);
export const SchemaEditionContextProvider = (props) => {
  const [form] = Form.useForm();
  const mode = props.mode;

  const { data, loading, error, refetch } = useQuery(QUERY_SCHEMA_BY_ID_WITH_AUTO_EXERCISES, {
    variables: {
      id: props.schemaId
    },
    fetchPolicy: 'no-cache'
  });
  const schema = data?.schema;

  const [name, setName] = React.useState(schema?.name);
  const [description, setDescription] = React.useState(schema?.description);
  const [image, setImage] = useState(null);
  const [legends, setLegends] = useState([]);
  const [undoStack, setUndoStack] = useState([]);
  const [redoStack, setRedoStack] = useState([]);
  const [coursSelectorVisible, setCoursSelectorVisible] = useState(true);

  const [resizingVisible, setResizingVisible] = useState(false);

  // Dessins additionnels
  const [texts, setTexts] = useState([]);
  const [selectedTextIndex, setSelectedTextIndex] = useState(null);
  const [lines, setLines] = useState([]);

  const [legendBlockAttachedToTopBar, setLegendBlockAttachedToTopBar] = useState(false); // Légendes attachées à la barre du haut

  const onDeleteImage = async () => {
    await updateSchema({
      variables: {
        id: schema.id,
        input: {
          image: null
        }
      }
    });
    await refetch();
  };

  // Functions to handle undo and redo actions
  const undo = useCallback(() => {
    if (undoStack.length > 1) {
      const previousState = undoStack[undoStack.length - 2];
      setRedoStack((prevRedoStack) => [
        ...prevRedoStack,
        { legends: JSON.parse(JSON.stringify(legends)), lines: JSON.parse(JSON.stringify(lines)) }
      ]);
      setLegends(JSON.parse(JSON.stringify(previousState.legends)));
      setLines(JSON.parse(JSON.stringify(previousState.lines)));
      setUndoStack((prevUndoStack) => prevUndoStack.slice(0, -1));
    }
  }, [undoStack, legends, lines]);

  const redo = useCallback(() => {
    if (redoStack.length > 0) {
      const nextState = redoStack.pop();
      setUndoStack((prev) => [
        ...prev,
        { legends: JSON.parse(JSON.stringify(legends)), lines: JSON.parse(JSON.stringify(lines)) }
      ]);
      setLegends(nextState.legends);
      setLines(nextState.lines);
      setRedoStack(redoStack);
    }
  }, [redoStack, legends, lines]);

  // Function to update legends and manage the undo/redo stacks
  const updateLegends = useCallback(
    (newLegends) => {
      setUndoStack((prev) => [
        ...prev,
        { legends: JSON.parse(JSON.stringify(legends)), lines: JSON.parse(JSON.stringify(lines)) }
      ]); // Deep copy of legends and lines to avoid mutation issues
      setRedoStack([]); // Clear the redo stack when making a new action
      setLegends(newLegends);
    },
    [legends, lines, undoStack, redoStack]
  );

  const updateLines = useCallback(
    (newLines) => {
      setUndoStack((prev) => [
        ...prev,
        { legends: JSON.parse(JSON.stringify(legends)), lines: JSON.parse(JSON.stringify(lines)) }
      ]);
      setRedoStack([]); // Clear the redo stack when making a new action
      setLines(newLines);
    },
    [legends, lines, undoStack, redoStack]
  );

  const [settings, setSettings] = useState({
    stageWidth: 800,
    stageHeight: 600,
    scaleX: 1,
    scaleY: 1
  });

  // Légende sélectionnée
  const [selectedLegendId, setSelectedLegendId] = useState(null);
  // Outil sélectionné
  const [selectedTool, setSelectedTool] = useState(null);

  const [strokeColor, setStrokeColor] = useState('#000000'); // Couleur initiale
  const [strokeWidth, setStrokeWidth] = useState(DEFAULT_STROKE_WIDTH); // Épaisseur initiale

  const [updateSchema, { loading: loadingUpdate }] = useMutation(MUTATION_UPDATE_SCHEMA);
  const [updateLinkedCoursesSchema, { loading: loadingUpdateCours }] = useMutation(
    MUTATION_UPDATE_LINKED_COURSES_SCHEMA
  );

  const [view, setView] = useState('schema'); // schema, correction

  // Cours ids selectionnés, liés au schéma
  const [selectedCoursId, setSelectedCoursId] = useState(null);

  useEffect(() => {
    if (IS_DEV) {
      console.log('useEffect data legends');
    }
    const allLegends = schema?.legends || [];
    const allLines = schema?.lines || [];

    setLegends(allLegends); // légendes
    setUndoStack([
      {
        legends: JSON.parse(JSON.stringify(allLegends)),
        lines: JSON.parse(JSON.stringify(allLines))
      }
    ]); // Initialiser la pile undo avec l'état initial

    setTexts(schema?.text || []); // textes additionnels
    setLines(schema?.lines || []); // dessins additionnels

    if (schema?.settings?.stageWidth && schema?.settings?.stageHeight) {
      setSettings(schema?.settings || {}); // settings canvas
    }

    setImage(schema?.image || null);

    if (props?.setBreadcrumbTitle) {
      props.setBreadcrumbTitle(schema?.name);
    }

    /* Cours liés schéma - Ne réinitialiser que si pas de sélection en cours */
    if (schema?.linkedCourses && selectedCoursId === null) {
      const data = schema?.linkedCourses?.map((node) => node.id);
      setSelectedCoursId(data);
    }

    setName(schema?.name);
    setDescription(schema?.description);
  }, [data]);

  // Effect to handle undo/redo keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      const key = e.key.toLowerCase(); // Convert to lowercase
      if ((e.ctrlKey || e.metaKey) && key === 'z') {
        if (e.shiftKey) {
          redo();
        } else {
          undo();
        }
        e.preventDefault();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [undo, redo]);

  if (IS_DEV) {
    console.log('re-render SchemaEditionContext');
  }

  const handleSaveHeader = async (input) => {
    await updateSchema({
      variables: {
        id: schema.id,
        input
      }
    });
  };

  const beforeUploadImage = async (file) => {
    try {
      await updateSchema({
        variables: {
          id: schema.id,
          input: {
            image: file
          }
        }
      });
      await refetch();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  const handleSave = async () => {
    try {
      // Ne pas enregistrer les valeurs de scaleX et scaleY (zoom)
      const settingsCopy = JSON.parse(JSON.stringify(settings));
      delete settingsCopy.scaleX;
      delete settingsCopy.scaleY;
      await updateSchema({
        variables: {
          id: schema.id,
          input: {
            legends: legends,
            settings: settingsCopy,
            lines: lines,
            text: texts,
            name,
            description
          }
        }
      });
      await updateLinkedCoursesSchema({
        variables: {
          schemaId: schema.id,
          coursIdArray: selectedCoursId
        }
      });
      // Pour démonter/remonter composant HierarchySelecter qui ne s'update pas tout seul
      //await refetch();
      setCoursSelectorVisible(false);
      setCoursSelectorVisible(true);
      notification.success({
        message: 'Sauvegardé',
        key: 'saved'
      });
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  return (
    <SchemaEditionContext.Provider
      value={{
        image,
        setImage,
        form,
        legends,
        setLegends,
        view,
        setView,
        lines,
        setLines,

        updateSchema,
        updateLinkedCoursesSchema,
        loadingUpdate: loadingUpdate || loadingUpdateCours,

        refetch,
        loading,
        schema,

        selectedLegendId,
        setSelectedLegendId,

        selectedTool,
        texts,
        setTexts,
        setSelectedTool,

        selectedTextIndex,
        setSelectedTextIndex,

        settings,
        setSettings,

        exerciseSettings: props.exerciseSettings, // Exercise mode
        setExerciseSettings: props.setExerciseSettings, // Exercise mode

        strokeColor,
        setStrokeColor,
        strokeWidth,
        setStrokeWidth,

        selectedCoursId,
        setSelectedCoursId,

        name,
        setName,
        description,
        setDescription,
        handleSave,
        handleSaveHeader,

        undo,
        redo,
        setUndoStack,
        setRedoStack,

        updateLegends, // Update with undo/redo management
        updateLines, // Update with undo/redo management free drawing

        undoStack,
        redoStack,

        beforeUploadImage,
        onDeleteImage,
        legendBlockAttachedToTopBar,
        setLegendBlockAttachedToTopBar,
        coursSelectorVisible,
        resizingVisible,
        setResizingVisible
      }}
    >
      {props.children}
    </SchemaEditionContext.Provider>
  );
};
