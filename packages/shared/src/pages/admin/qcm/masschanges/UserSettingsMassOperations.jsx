import { Card, Radio, Tag, TreeSelect, Select, Button, message, Modal, Checkbox } from 'antd';
import { useTranslation } from 'react-i18next';
import { useMutation, gql } from '@apollo/client';
import React, { useEffect, useState } from 'react';
import { MultiplesUserSelectRework } from '@/shared/pages/admin/qcm/components/MultiplesUserSelectRework';
import { useExoteachCompaniesQuery } from '@/shared/hooks/config/useConfigHooks';
import ReactDOM from 'react-dom';
import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager.jsx';

const MASS_MUTATION_GROUPS = gql`
  mutation AddOrRemoveOrReplaceGroupsToGroupsOrUsers(
    $type: String!
    $groupsIds: [ID]!
    $usersIds: [ID]!
    $targetGroupsIds: [ID]!
  ) {
    addOrRemoveOrReplaceGroupsToGroupsOrUsers(
      type: $type
      groupsIds: $groupsIds
      usersIds: $usersIds
      targetGroupsIdsString: $targetGroupsIds
    )
  }
`;

const MASS_MUTATION_COMPANIES = gql`
  mutation AddOrDeleteOrReplaceUsersAndGroupsToCompaniesAssociations(
    $type: String!
    $groupsIds: [ID]!
    $usersIds: [ID]!
    $targetCompaniesIds: [ID]!
  ) {
    addOrDeleteOrReplaceUsersAndGroupsToCompaniesAssociations(
      type: $type
      groupsIds: $groupsIds
      usersIds: $usersIds
      targetCompaniesIds: $targetCompaniesIds
    )
  }
`;

const updateType = {
  updateType_Add: 'add',
  updateType_Remove: 'remove',
  updateType_Replace: 'replace'
};
const targetType = {
  targetType_Groupe: 'GROUPS',
  targetType_Company: 'COMPANIES'
};

const ConfirmationModal = ({ isOpen, onConfirm, onCancel }) => {
  const { t } = useTranslation();

  /* modal de confirmation à checkbox */
  const [isCheckboxChecked, setIsCheckboxChecked] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      setIsCheckboxChecked(false); // Réinitialiser lors de la fermeture du modal
    }
  }, [isOpen]);

  return ReactDOM.createPortal(
    <Modal
      title={t('UserMassChange.AdminReplaceConfirmation')}
      open={isOpen}
      onOk={() => isCheckboxChecked && onConfirm()}
      onCancel={onCancel}
      okButtonProps={{ disabled: !isCheckboxChecked }}
    >
      <div>{t('UserMassChange.ReplaceExplanation')}</div>
      <br />
      <Checkbox
        checked={isCheckboxChecked}
        onChange={(e) => setIsCheckboxChecked(e.target.checked)}
      >
        {t('UserMassChange.ConfirmCheckBox')}
      </Checkbox>
    </Modal>,
    document.body
  );
};
const confirmWithCheckbox = (onConfirm) => {
  /* fonction qui crée dynamiquement le modal de confirmation dans le DOM */
  const div = document.createElement('div');
  document.body.appendChild(div);

  const handleConfirm = () => {
    onConfirm();
    ReactDOM.unmountComponentAtNode(div);
    document.body.removeChild(div);
  };

  const handleCancel = () => {
    ReactDOM.unmountComponentAtNode(div);
    document.body.removeChild(div);
  };

  ReactDOM.render(
    <ConfirmationModal isOpen={true} onConfirm={handleConfirm} onCancel={handleCancel} />,
    div
  );
};

export default function UserSettingsMassOperations(props) {
  const { t } = useTranslation();

  const initType = updateType.updateType_Add;
  const initTarget = targetType.targetType_Groupe;

  const [groupIdArraySelection, setGroupIdArraySelection] = useState([]);
  const [groupTarget, setGroupTarget] = useState([]);
  const [companyTarget, setCompanyTarget] = useState([]);
  const [userIdArray, setUserIdArray] = useState([]);
  const [modifType, setModifType] = useState(initType);
  const [targetSelecter, setTargetSelecter] = useState(initTarget);

  const { companyInfos } = useExoteachCompaniesQuery();

  const [mutateGroups, { data: dataGroups, loading: loadingGroups, error: errorGroups }] =
    useMutation(MASS_MUTATION_GROUPS);
  const [
    mutateCompanies,
    { data: dataCompanies, loading: loadingCompanies, error: errorCompanies }
  ] = useMutation(MASS_MUTATION_COMPANIES);

  const [flexDirection, setFlexDirection] = useState(window.innerWidth <= 768 ? 'column' : 'row');
  useEffect(() => {
    const handleResize = () => {
      setFlexDirection(window.innerWidth <= 768 ? 'column' : 'row');
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  async function executeMutation() {
    /* fonction qui execute la mutation sélectionnée, lorsque le bouton est cliqué */
    let allIsGood = true;

    /* vérif des arguments */
    if (!Object.values(targetType).includes(targetSelecter)) {
      allIsGood = false;
      message.error(t('UserMassChange.TargetTypeInvalid'));
    } // Le type est bon
    const dynamicMutationCaller =
      targetSelecter === targetType.targetType_Groupe ? mutateGroups : mutateCompanies;

    /* vérfid de la cible */
    if (!Object.values(updateType).includes(modifType)) {
      allIsGood = false;
      message.error(t('UserMassChange.ModifTypeInvalid'));
    } // le type de mutation est bon

    /* vérif que les éléments à modifier ne sont pas vide*/
    if (groupIdArraySelection.length === 0 && userIdArray.length === 0) {
      allIsGood = false;
      message.error(t('UserMassChange.SelectIsEmpty'));
    } // La sélection est vide

    // Si on réalise la mutation
    if (allIsGood) {
      const data = await dynamicMutationCaller({
        variables: {
          type: modifType,
          groupsIds: groupIdArraySelection,
          usersIds: userIdArray,
          targetGroupsIds: groupTarget,
          targetCompaniesIds: companyTarget
        }
      });

      const isTransactionGood = Object.values(data.data)[0] === true;

      if (isTransactionGood) {
        message.success(t('Updated'));
      } else {
        message.error(t('Failure'));
      }
    }
  }

  const paddingStyle = { padding: '10px 10px 20px 0px' };

  return (
    <Card>
      <span>{t('UserMassChange.ChooseElementsToModify')}</span>
      <div
        style={{
          display: 'flex',
          ...paddingStyle,
          flexDirection
        }}
      >
        <div
          style={{
            padding: '5px',
            minWidth: '50%'
          }}
        >
          <AbstractGroupsManager
            onChange={(toAdd, groupId) => {
              if (toAdd) {
                const temp = [...new Set([...groupIdArraySelection, groupId])];
                setGroupIdArraySelection(temp);
              } else if (toAdd === false) {
                setGroupIdArraySelection(
                  groupIdArraySelection.filter(
                    (item) => !(groupId === item || groupId === parseInt(item))
                  )
                );
              }
            }}
            onChangeArray={
              (groupArray, toAdd) => {
                if (toAdd) {
                  const temp = [...new Set([...groupIdArraySelection, ...groupArray])];
                  setGroupIdArraySelection(temp);
                } else if (toAdd === false) {
                  setGroupIdArraySelection(
                    groupIdArraySelection.filter(
                      (item) => !(groupArray.includes(item) || groupArray.includes(parseInt(item)))
                    )
                  );
                } else {
                  throw new Error(`toAdd ni true ni false, : ${JSON.stringify(toAdd)}`);
                }
              } // Si on ne veut pas utiliser de mutation, on peut utiliser cette fonction pour gérer les changements (envoi ids groupes en array)
            }
          />
        </div>
        <div
          style={{
            padding: '5px',
            minWidth: '50%'
          }}
        >
          <MultiplesUserSelectRework
            setUserIds={(value) => setUserIdArray(value)}
            initialValues={[]}
            withUsername={true}
            placeholder={t('UserMassChange.SelectUsersToModify')}
            flexDirection={flexDirection}
          />
        </div>
      </div>

      <span>{t('UserMassChange.ModificationType')}</span>
      <div style={{ display: 'flex', ...paddingStyle }}>
        <Radio.Group
          defaultValue={initType}
          onChange={(value) => {
            setModifType(value.target.value);
          }}
        >
          <Radio.Button value={updateType.updateType_Add}>
            {t('UserMassChange.ButtonUpdateTypeAdd')}
          </Radio.Button>
          <Radio.Button value={updateType.updateType_Remove}>
            {t('UserMassChange.ButtonUpdateTypeRemove')}
          </Radio.Button>
          <Radio.Button value={updateType.updateType_Replace}>
            {t('UserMassChange.ButtonUpdateTypeReplace')}
          </Radio.Button>
        </Radio.Group>
      </div>

      <span>{t('UserMassChange.ElementTarget')}</span>
      <div style={{ ...paddingStyle }}>
        <Radio.Group
          defaultValue={initTarget}
          onChange={(value) => setTargetSelecter(value.target.value)}
        >
          <Radio.Button value={targetType.targetType_Groupe}>
            {t('UserMassChange.Groups')}
          </Radio.Button>
          <Radio.Button value={targetType.targetType_Company}>
            {t('UserMassChange.Companies')}
          </Radio.Button>
        </Radio.Group>

        <div
          style={{
            display: 'flex',
            ...paddingStyle,
            flexDirection
          }}
        >
          <div
            style={{
              padding: '5px',
              minWidth: '50%'
            }}
          >
            <AbstractGroupsManager
              onChange={(toAdd, groupId) => {
                if (toAdd) {
                  const temp = [...new Set([...groupTarget, groupId])];
                  setGroupTarget(temp);
                } else if (toAdd === false) {
                  setGroupTarget(
                    groupTarget.filter((item) => !(groupId === item || groupId === parseInt(item)))
                  );
                }
              }}
              onChangeArray={(groupArray, toAdd) => {
                if (toAdd) {
                  const temp = [...new Set([...groupTarget, ...groupArray])];
                  setGroupTarget(temp);
                } else if (toAdd === false) {
                  setGroupTarget(
                    groupTarget.filter(
                      (item) => !(groupArray.includes(item) || groupArray.includes(parseInt(item)))
                    )
                  );
                } else {
                  throw new Error(`toAdd ni true ni false, : ${JSON.stringify(toAdd)}`);
                }
              }} // Si on ne veut pas utiliser de mutation, on peut utiliser cette fonction pour gérer les changements (envoi ids groupes en array)
              disabled={!(targetSelecter === targetType.targetType_Groupe)}
            />
          </div>
          <div
            style={{
              padding: '5px',
              minWidth: '50%'
            }}
          >
            <Select
              mode="multiple"
              style={{ width: '100%' }}
              onChange={setCompanyTarget}
              disabled={!(targetSelecter === targetType.targetType_Company)}
              placeholder={t('UserMassChange.SelectCompaniesToApply')}
              defaultValue={companyTarget}
            >
              {companyInfos?.map((companyInfo) => (
                <Select.Option key={companyInfo?.id} value={companyInfo?.id}>
                  {companyInfo?.commercialName}
                </Select.Option>
              ))}
            </Select>
          </div>
        </div>
      </div>

      <Button
        onClick={
          modifType === updateType.updateType_Replace
            ? () => {
                confirmWithCheckbox(executeMutation);
              }
            : executeMutation
        }
      >
        {' '}
        {t('Update')}
      </Button>
    </Card>
  );
}
