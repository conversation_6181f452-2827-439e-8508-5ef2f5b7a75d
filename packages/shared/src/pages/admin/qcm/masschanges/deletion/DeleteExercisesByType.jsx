import { QUERY_ALL_QCM_TYPE } from '@/shared/graphql/qcm';
import { CONTENT_TYPE_VALUES } from '@/shared/pages/admin/qcm/types-qcm/index';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { useMutation, useQuery } from '@apollo/client';
import { Button, message, notification, Popconfirm, Select, Tag } from 'antd';
import gql from 'graphql-tag';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

const MUTATION_MASS_DELETE_EXERCISES_BY_TYPE = gql`
  mutation massDeleteExercisesByType($typeQcmIds: [ID]) {
    massDeleteExercisesByType(typeQcmIds: $typeQcmIds)
  }
`;

export default function DeleteExercisesByType() {
  const { t } = useTranslation();
  const [deleteMutation, { loading, error, data }] = useMutation(
    MUTATION_MASS_DELETE_EXERCISES_BY_TYPE
  );

  const [selectedTypes, setSelectedTypes] = useState([]);
  const dataTypeQcm = useQuery(QUERY_ALL_QCM_TYPE, { fetchPolicy: 'no-cache' });
  const exercisesSerieTypes = dataTypeQcm?.data?.allTypeQcm?.filter(
    (typeQcm) => typeQcm?.contentType === CONTENT_TYPE_VALUES.EXERCISE
  );

  const tagRender = ({ label, value, closable, onClose, key }) => (
    <Tag
      value={value}
      key={key}
      color="red"
      closable={closable}
      onClose={onClose}
      style={{ marginRight: 3 }}
    >
      {label}
    </Tag>
  );

  const handleSelect = async (_, option) => {
    try {
      setSelectedTypes([...selectedTypes, { id: option.key, name: option.value }]);
    } catch (e) {
      message.error(`Type ${option.value} n'a pas été ajouté`);
      console.error(e);
    }
  };
  const handleDeselect = async (_, option) => {
    try {
      setSelectedTypes(selectedTypes.filter((type) => type.id !== option.key));
    } catch (e) {
      message.error(`Type ${option.value} n'a pas été enlevé`);
      console.error(e);
    }
  };

  const handleDeleteExercises = async () => {
    const notifKey = 'delete-exercises';
    notification.info({
      key: notifKey,
      message: t('DeleteMassExerciseByType.deleting'),
      description: t('DeleteMassExerciseByType.deletingInProgress'),
      duration: 0
    });
    try {
      const typeQcmIds = selectedTypes.map((type) => type.id);
      const { data } = await deleteMutation({ variables: { typeQcmIds: typeQcmIds } });
      const nbOfDeleted = data?.massDeleteExercisesByType || 0;
      notification.success({
        key: notifKey,
        message: t('DeleteMassExerciseByType.deletingSuccess'),
        description: `${nbOfDeleted} ${t('DeleteMassExerciseByType.exercisesDeleted')}`
      });
      setSelectedTypes([]);
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      notification.error({
        key: notifKey,
        title: 'Error',
        description: e.message
      });
    }
  };

  return (
    <>
      <h1>{t('DeleteMassExerciseByType.title')}</h1>

      <Select
        mode="multiple"
        style={{ maxWidth: '380px', minWidth: '380px' }}
        tagRender={tagRender}
        placeholder={t('ChooseQuestionType')}
        value={selectedTypes?.map((typeQcm) => typeQcm.name)}
        loading={dataTypeQcm.loading}
        options={exercisesSerieTypes?.map((typeQcmId) => ({
          value: typeQcmId.name,
          key: typeQcmId.id
        }))}
        // onChange={handleGroupsChange}
        onDeselect={handleDeselect}
        onSelect={handleSelect}
      />

      <br />
      <br />
      <Popconfirm
        title={t('DeleteMassExerciseByType.deleteConfirmation')}
        onConfirm={handleDeleteExercises}
        okText={t('yes')}
        cancelText={t('no')}
        disabled={selectedTypes.length === 0}
      >
        <Button
          type="primary"
          danger
          loading={loading}
          disabled={selectedTypes.length === 0}
          style={{ marginTop: 24 }}
        >
          {t('DeleteMassExerciseByType.buttonText')}
        </Button>
      </Popconfirm>
    </>
  );
}
