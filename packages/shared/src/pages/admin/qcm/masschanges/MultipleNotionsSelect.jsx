
import { SEARCH_NOTIONS } from '@/shared/graphql/notions.js'
import { useQuery } from '@apollo/client'
import { Select } from 'antd'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next';

export const MultipleNotionsSelect = ({ onChange }) => {
  const {t} = useTranslation();
  const { loading, error, data, refetch } = useQuery(SEARCH_NOTIONS, {
    fetchPolicy: 'no-cache',
    variables: { filter: {} },
  })

  const [selectedItems, setSelectedItems] = useState([])
  const getNotions = () => data && data.searchNotions?.notions
  const filteredOptions = getNotions() && getNotions().filter(o => !selectedItems.includes(o.id))

  return (
    <Select
      loading={loading}
      mode="multiple"
      placeholder={t('SelectNotions')}
      value={selectedItems}
      onChange={(selected, items) => {
        setSelectedItems(selected)
        onChange(items)
      }}
      style={{ maxWidth: '377px', minWidth: '300px', marginTop: 10 }}
    >
      {filteredOptions && filteredOptions.map(item => {
        const fullValue = `${item.name}`
        return (
          <Select.Option key={item.id} value={fullValue}>
            {fullValue}
          </Select.Option>
        )
      })}
    </Select>
  )
}