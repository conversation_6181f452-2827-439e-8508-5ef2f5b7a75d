import {
  PedagogicCascaderSelector,
  PedagogicCascaderSelectorTarget
} from '@/shared/components/Cours/PedagogicCascaderSelector.jsx';
import { MUTATION_MASSCHANGEQUESTIONCOURS } from '@/shared/graphql/qcm.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { UeCategorySelector } from '@/shared/pages/admin/components/UeCategorySelector.jsx';
import ChangeCoursesModulesAndTypes from '@/shared/pages/admin/qcm/masschanges/ChangeCoursesModulesAndTypes.jsx';
import ChangeQuestionsCours from '@/shared/pages/admin/qcm/masschanges/ChangeQuestionsCours.jsx';
import ContentTypesMassOperations from '@/shared/pages/admin/qcm/masschanges/ContentTypesMassOperations.jsx';
import DeleteExercisesByType from '@/shared/pages/admin/qcm/masschanges/deletion/DeleteExercisesByType';
import DeleteExerciseSeriesByType from '@/shared/pages/admin/qcm/masschanges/deletion/DeleteExerciseSeriesByType';
import { DeleteUsersByGroups } from '@/shared/pages/admin/qcm/masschanges/deletion/DeleteUsersByGroups';
import { NotionsQuestionsAttribution } from '@/shared/pages/admin/qcm/masschanges/NotionsQuestionsAttribution.jsx';
import QuestionSettingsMassOperations from '@/shared/pages/admin/qcm/masschanges/QuestionSettingsMassOperations.jsx';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { useMutation } from '@apollo/client';
import { Alert, Button, Card, Form, notification, Select, Tag } from 'antd';
import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import UserSettingsMassOperations from '@/shared/pages/admin/qcm/masschanges/UserSettingsMassOperations';

export default function MassChangesPage(props) {
  const { t } = useTranslation();
  const [associerToutesQuestionsMutation, { loading, error, data }] = useMutation(
    MUTATION_MASSCHANGEQUESTIONCOURS
  );
  const [selectedCategoryId, setSelectedCategoryId] = useState(null);
  const [selectedCoursId, setSelectedCoursId] = useState(null);
  const [form] = Form.useForm();
  const { me } = useContext(GlobalContext);

  const handleFinish = async (formData) => {
    try {
      const result = await associerToutesQuestionsMutation({
        variables: {
          categoryId: selectedCategoryId,
          coursId: selectedCoursId
        }
      });

      if (result && result.data) {
        const questionsTraitees = result.data.massChangesQuestionsCategories;
        notification.success({
          message: `Opération terminée`,
          description: `${questionsTraitees} questions traitées`,
          placement: 'topLeft'
        });
      }
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const defaultSelectedKey = '1';
  const [selectedKey, setSelectedKey] = useState(defaultSelectedKey);

  return (
    <>
      {/* <AdminMenu selected="qcm/masschanges"> */}
      <FullMediParticlesBreadCrumb title={t('operationEnMasse')} />
      <div style={{ margin: '20px' }}>
        <div style={{ margin: '20px' }}>
          <Select
            style={{ minWidth: '400px' }}
            defaultValue={defaultSelectedKey}
            onChange={(value) => setSelectedKey(value)}
          >
            <Select.Option value="1">{t('changeModuleLessons')}</Select.Option>
            <Select.Option value="2">Opérations en masse sur les types de contenu</Select.Option>
            <Select.Option value="3">Attribuer notions en masse aux questions</Select.Option>
            <Select.Option value="4">Attribuer cours aux questions</Select.Option>
            <Select.Option value="5">Changer le barême ou l'affichage des questions</Select.Option>
            <Select.Option value="6">
              {t('UserMassChange.UserMassChangeSelectername')}
            </Select.Option>
            {me?.exostaff && (
              <>
                <Select.Option value="7">
                  <Tag>Exostaff</Tag> {t('UserMassChange.deleteUsersByGroupsTitle')}
                </Select.Option>
                <Select.Option value="8">
                  <Tag>Exostaff</Tag> {t('ExercisesMassChange.DeleteExercisesByType')}
                </Select.Option>
                <Select.Option value="9">
                  <Tag>Exostaff</Tag> {t('ExercisesMassChange.DeleteSeriesByType')}
                </Select.Option>
              </>
            )}
          </Select>
        </div>

        {selectedKey === '1' && <ChangeCoursesModulesAndTypes />}

        {selectedKey === '2' && <ContentTypesMassOperations />}

        {selectedKey === '3' && (
          <>
            <Alert
              message="Attribuer (ou retirer) 1 à N notions pour tous les items qui match un mot clé"
              type="info"
              showIcon
            />
            <br />
            <NotionsQuestionsAttribution />
          </>
        )}

        {selectedKey === '4' && (
          <>
            {t('SelectAllQuestionsToAttribute')}
            <Card>
              <h3>{t('SelectAllQuestionWithCategory')}</h3>
              <br />
              <Form layout="vertical" onFinish={handleFinish} form={form}>
                <p>{t('ChooseCategoryToAttribute')}</p>
                <Form.Item label={t('CategoryToSelect')}>
                  <UeCategorySelector
                    withNombreQuestions
                    onChangeCategory={(categId) => {
                      setSelectedCategoryId(categId);
                    }}
                  />
                </Form.Item>
                <Form.Item label={t('CourseThatWillBeAssociated')}>
                  <PedagogicCascaderSelector
                    onSelectTarget={async ({ label, value, type }) => {
                      setSelectedCoursId(value);
                    }}
                    acceptedTypes={[PedagogicCascaderSelectorTarget.Cours]}
                    placeholder="Sélectionner le cours"
                  />
                </Form.Item>
                <br />
                <Form.Item>
                  <Button htmlType="submit" type="danger" loading={loading}>
                    {t('LaunchRequest')}
                  </Button>
                </Form.Item>
              </Form>
            </Card>
            <br />
            <br />
            <ChangeQuestionsCours />
          </>
        )}

        {selectedKey === '5' && <QuestionSettingsMassOperations />}
        {selectedKey === '6' && <UserSettingsMassOperations />}
        {/* Deletions (exostaff only) */}
        {selectedKey === '7' && <DeleteUsersByGroups />}
        {selectedKey === '8' && <DeleteExercisesByType />}
        {selectedKey === '9' && <DeleteExerciseSeriesByType />}
      </div>
    </>
  );
}
