import { ErrorResult } from '@/shared/components/ErrorResult.jsx';
import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import { FileImage } from '@/shared/components/FileImage.jsx';
import { SEARCH_POST_TYPE } from '@/shared/graphql/posts.js';
import { ModalType } from '@/shared/pages/admin/groupes/CreateEditGroupeModal.jsx';
import { CreateEditPostTypeModal } from '@/shared/pages/admin/qcm/posttype/modal/CreateEditPostTypeModal.jsx';
import { getCommentaireTypeName } from '@/shared/services/commentaires.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { DeleteOutlined } from '@ant-design/icons';
import EditOutlined from '@ant-design/icons/lib/icons/EditOutlined.js';
import UserAddOutlined from '@ant-design/icons/lib/icons/UserAddOutlined.js';
import { gql, useMutation, useQuery } from '@apollo/client';
import { But<PERSON>, Card, Popconfirm, Table, Tag, Tooltip } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';

const DELETE_POST_TYPE = gql`
  mutation deletePostType($id: ID!) {
    deletePostType(id: $id)
  }
`;
const PostTypeActions = ({ record, key, refetch }) => {
  const { t } = useTranslation();
  const [editVisible, setEditVisible] = useState(false);
  const [deletePostType] = useMutation(DELETE_POST_TYPE);
  return (
    <>
      <Tooltip title={t('general.Edit')}>
        <Button
          onClick={(e) => {
            setEditVisible(true);
          }}
          style={{ marginRight: 16 }}
          type="primary"
          shape="circle"
          icon={<EditOutlined />}
        />
      </Tooltip>
      <Tooltip title={t('Delete')}>
        <Popconfirm
          title={t('SureOfDeletion')}
          onConfirm={async (e) => {
            try {
              await deletePostType({ variables: { id: record.id } });
              refetch();
            } catch (e) {
              console.error(e);
            }
          }}
          okText={t('general.yes')}
          cancelText={t('general.no')}
        >
          <Button style={{ marginRight: 16 }} danger shape="circle" icon={<DeleteOutlined />} />
        </Popconfirm>
      </Tooltip>

      {editVisible && (
        <CreateEditPostTypeModal
          id={record.id}
          postType={record}
          isVisible={editVisible}
          modalType={ModalType.UPDATE}
          refetch={refetch}
          closeModalHandler={() => {
            setEditVisible(false);
            refetch(); // Load new modifications
          }}
        />
      )}
    </>
  );
};

export default function (props) {
  const { t } = useTranslation();
  useEffectScrollTop();
  const [filter, setFilter] = useState({});
  const { loading, error, data, refetch } = useQuery(SEARCH_POST_TYPE, {
    fetchPolicy: 'cache-and-network'
  });
  const [createVisible, setCreateVisible] = useState(false);
  const columns = [
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => <Tag color={'geekblue'}>{getCommentaireTypeName(type)}</Tag>
    },
    {
      title: 'Image',
      dataIndex: 'image',
      key: 'image',
      hideInSearch: true,
      render: (image, record) => (
        <FileImage key={record.id} image={image} style={{ maxHeight: '36px' }} />
      )
    },
    {
      title: 'Actions',
      key: 'action',
      hideInSearch: true,
      render: (text, record) => (
        <PostTypeActions record={record} refetch={refetch} key={record.id} />
      )
    }
  ];

  const getTableDataSource = () => {
    return data?.postTypes;
  };

  return (
    <>
      <ExoteachLayout>
        <FullMediParticlesBreadCrumb title={t('AllDiscussionsType')} />

        <Card style={{ margin: 20 }}>
          <Button
            type="primary"
            icon={<UserAddOutlined />}
            onClick={() => {
              setCreateVisible(true);
            }}
          >
            {t('AddAPostType')}
          </Button>
        </Card>

        <br />
        {!error && (
          <>
            <Table
              loading={loading}
              columns={columns}
              dataSource={getTableDataSource()}
              pagination={{
                defaultPageSize: 100,
                pageSizeOptions: [50, 100, 1000]
              }}
              onSubmit={(params) => {
                setFilter(params);
              }}
              search={{
                labelWidth: 'auto'
              }}
              size="small"
              showHeader
              scroll={{ x: true }}
            />
          </>
        )}

        {error && !loading && <ErrorResult refetch={refetch} error={error} />}

        <CreateEditPostTypeModal
          isVisible={createVisible}
          modalType={ModalType.CREATE}
          refetch={refetch}
          closeModalHandler={() => {
            setCreateVisible(false);
            refetch(); // Load new modifications
          }}
        />
      </ExoteachLayout>
    </>
  );
}
