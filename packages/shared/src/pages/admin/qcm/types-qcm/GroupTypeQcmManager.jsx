import {
  MUTATION_ADD_GROUP_TYPEQCM,
  MUTATION_REMOVE_GROUP_TYPEQCM
} from '@/shared/graphql/groupes.js';
import { QUERY_ALL_QCM_TYPE } from '@/shared/graphql/qcm.js';
import { useMutation, useQuery } from '@apollo/client';
import { Select, Tag, message } from 'antd';
import React from 'react';

/* One group => several types */

export const GroupTypeQcmManager = ({ groupe, typesQcm, contentType }) => {
  const { data: dataTypesQcm, loading: loadingTypes } = useQuery(QUERY_ALL_QCM_TYPE, {
    fetchPolicy: 'cache-and-network'
  });
  const allTypesQcm = dataTypesQcm?.allTypeQcm;

  const typesToShow =
    (contentType && allTypesQcm?.filter((t) => t.contentType === contentType)) || allTypesQcm;

  const [addTypeQcm, addGroupData] = useMutation(MUTATION_ADD_GROUP_TYPEQCM);
  const [removeTypeQcm, removeGroupData] = useMutation(MUTATION_REMOVE_GROUP_TYPEQCM);

  const groupeTagRender = ({ label, value, closable, onClose, key }) => (
    <Tag
      value={value}
      key={key}
      color="geekblue"
      closable={closable}
      onClose={onClose}
      style={{ marginRight: 3 }}
    >
      {label}
    </Tag>
  );

  const handleSelect = async (_, option) => {
    try {
      await addTypeQcm({ variables: { typeQcmId: option.key, groupId: groupe.id } });
      if (groupe?.isIndividual) {
        message.success(`L'utilisateur a accès au type ${option.value}`);
      } else {
        message.success(`Groupe ${groupe.name} a accès au type ${option.value}`);
      }
    } catch (e) {
      message.error(`Erreur: Groupe ${groupe.name} n'a pas été ajouté à ${option.value} `);
      console.error(e);
    }
  };
  const handleDeselect = async (_, option) => {
    try {
      await removeTypeQcm({ variables: { typeQcmId: option.key, groupId: groupe.id } });
      if (groupe?.isIndividual) {
        message.success(`L'utilisateur n'a plus accès au type ${option.value}`);
      } else {
        message.success(`Groupe ${groupe.name} n'a plus accès au type ${option.value}`);
      }
    } catch (e) {
      message.error(`Erreur: Groupe ${groupe.name} n'a pas été supprimé de ${option.value} `);
      console.error(e);
    }
  };

  return (
    <>
      {typesQcm && (
        <Select
          onDeselect={handleDeselect}
          onSelect={handleSelect}
          showArrow
          mode="multiple"
          placeholder="Choisir types de contenu..."
          loading={loadingTypes}
          options={typesToShow?.map((type) => ({
            value: type.name,
            key: type.id
          }))}
          defaultValue={typesQcm?.map((t) => ({ key: t.id, value: t?.name }))}
          style={{ width: '100%' }}
          tagRender={typesQcm && groupeTagRender}
        />
      )}
    </>
  );
};
