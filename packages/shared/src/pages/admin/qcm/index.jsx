import AvailableOnlyOnWeb from '@/shared/components/AvailableOnlyOnWeb.jsx';
import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import serieLogo from '@/shared/assets/serieExercice.png';
import { AddYearModal } from '@/shared/pages/admin/qcm/components/modal/AddYearModal.jsx';
import { QcmListInUE } from '@/shared/pages/admin/qcm/components/QcmListInUE.jsx';
import { formatAnnee, initQcmSearchFilter, resetQcmSearchFilter } from '@/shared/services/qcm.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { isMobile } from '@/shared/utils/utils.js';
import { SettingOutlined, UndoOutlined } from '@ant-design/icons';
import { gql, useQuery } from '@apollo/client';
import {
  Button,
  Card,
  Checkbox,
  Collapse,
  DatePicker,
  Form,
  Modal,
  Popover,
  Spin,
  Tag
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import Search from 'antd/es/input/Search';
import { GET_ANNEES } from '@/shared/graphql/home';
import { MultiplesUserSelect } from '@/shared/pages/admin/qcm/components/MultiplesUserSelect';
import { CoursIdTreeSelect } from '@/shared/pages/admin/qcm/components/CoursIdTreeSelect';
import dayjs from 'dayjs';
import { CascaderMultiSelect } from '@/shared/components/CascaderMultiSelect.jsx';
import {
  hierarchyComponantActions,
  HierarchySelecter,
  validesTypes
} from '@/shared/components/HierarchySelecter';

export const QUERY_MES_UES_FOR_MCQ = gql`
  query mesUEs {
    mesUEs {
      id
      name
      name_en
      name_es
      name_it
      name_de
      description
      description_en
      description_es
      description_it
      description_de
      image
      color
      color2
      order
      parentId
      isFolder
    }
  }
`;

const QUERY_ALL_QCM_TYPE = gql`
  query allTypeQcm {
    allTypeQcm {
      contentType
      name
      id
    }
  }
`;

function ModalSelectionUe({
  setIsModalOpen,
  isModalOpen,
  onChange,
  defaultIds,
  setStringSelected
}) {
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      open={isModalOpen}
      onOk={handleOk}
      onCancel={() => handleCancel(false)}
      forceRender // permet de lancer le load des données avant même d'ouvrir le modal
      cancelButtonProps={{ style: { display: 'none' } }}
      okButtonProps={{ style: { display: 'none' } }}
    >
      <CoursIdTreeSelect
        setUeIds={onChange}
        defaultIds={defaultIds}
        setStringSelected={setStringSelected}
        setIsModalOpen={setIsModalOpen}
      />
    </Modal>
  );
}

function DatePickerCreation({
  initialValueStart = null,
  initialValueEnd = null,
  setSelection,
  field
}) {
  const [selectedDateStart, setSelectedDateStart] = useState(
    initialValueStart ? dayjs(initialValueStart) : null
  );
  const [selectedDateEnd, setSelectedDatesEnd] = useState(
    initialValueEnd ? dayjs(initialValueEnd) : null
  );

  const onChange = (dates, dateStrings) => {
    if (!dates) {
      resetValues();
    } else {
      setSelectedDateStart(dayjs(dateStrings[0]));
      setSelectedDatesEnd(dayjs(dateStrings[1]));
      setSelection((prevSelection) => ({
        ...prevSelection,
        [`${field}Start`]: dateStrings[0],
        [`${field}End`]: dateStrings[1]
      }));
    }
  };

  const resetValues = () => {
    setSelectedDateStart(null);
    setSelectedDatesEnd(null);
    setSelection((prevSelection) => ({
      ...prevSelection,
      [`${field}Start`]: null,
      [`${field}End`]: null
    }));
  };

  const { t } = useTranslation();

  return (
    <>
      {/* Ici, remplace "values" par "value" */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <DatePicker.RangePicker onChange={onChange} value={[selectedDateStart, selectedDateEnd]} />
        <Button
          type={'text'}
          icon={<UndoOutlined />}
          onClick={() => {
            resetValues();
          }}
        >
          {' '}
        </Button>
      </div>
    </>
  );
}

export default function (props) {
  // GET MES UEs
  const { t } = useTranslation();
  useEffectScrollTop();

  // Ligne afin de récupérer le qcm filter depuis le session storage
  const [qcmSearchFilter, setQcmSearchFilter] = initQcmSearchFilter();

  // Permet de récupérer les UE de l'user
  const { loading, error, data, refetch } = useQuery(QUERY_MES_UES_FOR_MCQ, {
    fetchPolicy: 'cache-and-network'
  });

  // Recupère les données pour la table année
  const { data: { annees: anneesTemp = null } = {}, loading: anneesLoading } = useQuery(
    GET_ANNEES,
    { fetchPolicy: 'cache-and-network' }
  );
  const annees = anneesTemp?.map((value) => {
    return { ...value, name: formatAnnee(value.annee) };
  });
  const anneesArray = annees
    ?.map((value) => value.annee)
    .sort((a, b) => b - a)
    .map((value) => formatAnnee(value));
  const lastAnnee = anneesArray?.[0]; // Definition de la dernière année en base. C'est important car si on file pas une année valide à QcmListInUe ça bug

  // Récupère les données pour les types de QCM
  const { data: { allTypeQcm = null } = {}, loading: typeLoading } = useQuery(QUERY_ALL_QCM_TYPE, {
    fetchPolicy: 'cache-and-network'
  });
  const typeArray = allTypeQcm
    ?.filter((value) => value.contentType === 'EXERCISE_SERIES')
    ?.map((value) => {
      return { ...value, name: `${value.name}` };
    });

  // Variables
  const mesUEs = data?.mesUEs?.filter((ue) => !ue?.isFolder); // Récupération des UE, pour avoir des champs par default pour la création de série down the line
  const [key, setKey] = useState(0);

  const [addAnneeModalVisible, setAddAnneeModalVisible] = useState(false);
  const [modifyUeModalVisible, setModifyUeModalVisible] = useState(false);

  const formStyleBottom = { marginBottom: '8px' }; // style pour modifier le style des Form.Item

  const [externalAction, setExternalAction] = useState(null);

  //
  useEffect(() => {
    if (qcmSearchFilter?.ueIds && mesUEs) {
      const subArray = qcmSearchFilter.ueIds;
      if (subArray.length === 0) {
        setStringSelected(t('FilterQcm.NoSubjectSelected'));
      } else if (subArray.length === mesUEs.length) {
        setStringSelected(t('FilterQcm.AllSubjectSelected'));
      } else {
        setStringSelected(
          t('FilterQcm.VariableSubjectSelected', { numberMatieres: subArray.length.toString() })
        );
      }
    } else {
      setStringSelected('');
    }
  }, [qcmSearchFilter.ueIds, mesUEs]);

  const [stringSelected, setStringSelected] = useState(null); // Hook custom qui contiendra la string indiquant le nombre de matières sélectionnées
  const { Panel } = Collapse;
  const globalSelection = (
    <>
      <FullMediParticlesBreadCrumb title={t('ManageExercicesSeries')} />
      <Card style={{ margin: '10px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Form style={{ width: '100%' }} labelCol={{ span: 3 }} className="testMyFormQcm">
            <h1 style={{ fontSize: '26px', ...formStyleBottom }}>{t('FilterExercicesSeries')}</h1>
            <Collapse>
              <Panel
                forceRender
                header={
                  <>
                    <>{t('FilterQcm.SeeMore')}</>
                    &nbsp;
                    {stringSelected ? (
                      <Tag color={'blue'}>{stringSelected}</Tag>
                    ) : (
                      <Spin size={'small'} />
                    )}
                    <>
                      {qcmSearchFilter?.anneesSelected?.length === annees?.length ? (
                        <Tag>Toutes les années</Tag>
                      ) : (
                        qcmSearchFilter?.anneesSelected?.map((anneeId) => {
                          const finded = annees?.find((obj) => obj.id === anneeId);
                          return <Tag key={anneeId}>{formatAnnee(finded?.annee)}</Tag>;
                        })
                      )}
                    </>
                  </>
                }
              >
                <Form.Item label={t('FilterQcm.PerSubject')} style={{ ...formStyleBottom }}>
                  <div>
                    <HierarchySelecter
                      multiple
                      setterHookSelection={(value) =>
                        setQcmSearchFilter({ ...qcmSearchFilter, ueIds: value })
                      }
                      useTreeSelect
                      rankToRemoveIfLeaf={[
                        validesTypes.CTYPE_UNKNOWN,
                        validesTypes.CTYPE_FOLDER,
                        validesTypes.CTYPE_CATEGORY,
                        validesTypes.CTYPE_PAGE,
                        validesTypes.CTYPE_COURS
                      ]}
                      simplificationFeature={validesTypes.CTYPE_UE}
                      initialisationVariable={
                        qcmSearchFilter.ueIds
                          ? { [validesTypes.CTYPE_UE]: [...qcmSearchFilter.ueIds] }
                          : null
                      }
                      initAll={qcmSearchFilter.ueIds ? null : [validesTypes.CTYPE_UE]}
                      additionalTreeProps={{
                        placement: 'topLeft',
                        listHeight: 500,
                        style: {
                          width: 'auto',
                          marginRight: '5px',
                          minWidth: '500px'
                        },
                        popupMatchSelectWidth: false,
                        treeLine: true,
                        placeholder: t('FilterQcm.SelectUePlaceholder')
                      }}
                      externalAction={externalAction}
                      resetExternalAction={() => {
                        setExternalAction(null);
                      }}
                      isTreeSelectCheckable={true}
                      key={key}
                    />
                    <div>
                      <Button
                        onClick={() => {
                          setExternalAction(hierarchyComponantActions.CHECK_ALL);
                        }}
                      >
                        {t('FilterQuestions.CheckAllCourses')}
                      </Button>
                      <Button
                        onClick={() => {
                          setExternalAction(hierarchyComponantActions.UNCHECK_ALL);
                        }}
                      >
                        {t('FilterQuestions.UncheckAllCourses')}
                      </Button>
                    </div>
                  </div>
                </Form.Item>

                <Form.Item label={t('FilterQcm.PerYears')} style={{ ...formStyleBottom }}>
                  {annees && lastAnnee ? (
                    <CascaderMultiSelect
                      initData={
                        qcmSearchFilter && qcmSearchFilter.anneesSelected
                          ? qcmSearchFilter.anneesSelected
                          : null
                      }
                      initStyle={'all'}
                      setSelection={(value) => {
                        setQcmSearchFilter((prev) => ({ ...prev, anneesSelected: value }));
                      }}
                      objectData={annees}
                      rootName={'FilterQcm.Allyears'}
                      style={{ width: '80%' }}
                      key={key}
                    />
                  ) : (
                    <Spin />
                  )}
                  <Popover content={t('FilterQcm.AddYear')}>
                    <Button
                      onClick={() => setAddAnneeModalVisible(true)}
                      icon={<SettingOutlined />}
                      size="small"
                    />
                  </Popover>
                  <AddYearModal
                    isVisible={addAnneeModalVisible}
                    closeModalHandler={() => {
                      setAddAnneeModalVisible(false);
                      window.location.reload();
                    }}
                  />
                </Form.Item>

                <Form.Item label={t('FilterQcm.PerType')} style={{ ...formStyleBottom }}>
                  {typeArray ? (
                    <CascaderMultiSelect
                      initData={
                        qcmSearchFilter && qcmSearchFilter.typeQcm ? qcmSearchFilter.typeQcm : null
                      }
                      initStyle={'all'}
                      setSelection={(value) => {
                        setQcmSearchFilter((prev) => ({ ...prev, typeQcm: value }));
                      }}
                      objectData={typeArray}
                      rootName={'FilterQcm.AllTypes'}
                      style={{ width: '80%' }}
                      key={key}
                    />
                  ) : (
                    <Spin />
                  )}
                </Form.Item>

                <Form.Item label={t('FilterQcm.PerCreator')} style={{ ...formStyleBottom }}>
                  <MultiplesUserSelect
                    initialValues={qcmSearchFilter.userIds}
                    setUserIds={(value) => {
                      setQcmSearchFilter({ ...qcmSearchFilter, userIds: value });
                    }}
                    key={key}
                  />
                </Form.Item>

                <Form.Item
                  label={t('FilterQcm.DeletedExercicesSeries')}
                  style={{ ...formStyleBottom }}
                >
                  <Checkbox
                    checked={qcmSearchFilter?.deleted}
                    onClick={() =>
                      setQcmSearchFilter({ ...qcmSearchFilter, deleted: !qcmSearchFilter.deleted })
                    }
                  />
                </Form.Item>

                <Form.Item label={t('FilterQcm.PerCreationDate')} style={{ ...formStyleBottom }}>
                  <DatePickerCreation
                    initialValueStart={qcmSearchFilter.dateCreationStart}
                    initialValueEnd={qcmSearchFilter.dateCreationEnd}
                    setSelection={setQcmSearchFilter}
                    field={'dateCreation'}
                    key={key}
                  />
                </Form.Item>

                <Form.Item label={t('FilterQcm.PerLastModifDate')} style={{ ...formStyleBottom }}>
                  <DatePickerCreation
                    initialValueStart={qcmSearchFilter.dateLastModifStart}
                    initialValueEnd={qcmSearchFilter.dateLastModifEnd}
                    setSelection={setQcmSearchFilter}
                    field={'dateLastModif'}
                    key={key}
                  />
                </Form.Item>
              </Panel>
            </Collapse>

            <h1 style={{ fontSize: '26px', ...formStyleBottom }}>{t('general.search')}</h1>
            <Form.Item
              label={t('FilterQcm.PerExercicesSeriesTitle')}
              style={{ ...formStyleBottom }}
            >
              <Search
                enterButton
                allowClear
                onSearch={(text) => {
                  setQcmSearchFilter((sel) => ({ ...sel, titre: text }));
                }}
                onChange={(e) => {
                  const text = e.target.value;
                  setQcmSearchFilter((sel) => ({ ...sel, titre: text }));
                }}
                size="medium"
                placeholder={t('FilterQcm.SearchSerieExercice')}
                key={key}
              />
            </Form.Item>

            <Form.Item style={{ ...formStyleBottom }}>
              <Button
                onClick={() => {
                  setQcmSearchFilter(resetQcmSearchFilter());
                  setKey(key + 1);
                }}
              >
                {t('FilterQcm.ResetFilter')}
              </Button>
            </Form.Item>
          </Form>
          <img src={serieLogo} style={{ height: '87px' }} alt="" />
        </div>
      </Card>
    </>
  );

  return (
    <>
      {isMobile ? (
        <ExoteachLayout>
          <AvailableOnlyOnWeb />
        </ExoteachLayout>
      ) : (
        <ExoteachLayout>
          <>
            {globalSelection}
            {loading && <SpinnerCentered />}
            <br />
            {mesUEs?.length > 0 && qcmSearchFilter && lastAnnee && (
              <QcmListInUE
                // ↓ Ce sont les valeures passées par default quand on créé une nouvelle série d'exercice.
                initSerieQuestionValues={{
                  //initId:null,
                  //initId:mesUEs?.[0]?.id,
                  //initDescription:mesUEs?.[0]?.description,
                  //initName:mesUEs?.[0]?.name,
                  initAnnee: lastAnnee
                  //initImage:mesUEs?.[0]?.image,
                }} // /!\ Il faut mettre une année existante sinon ça bug /!\ (dans le modal de création on a pas le 2020-2021 et juste l'année, et ça créé quand même )
                selection={qcmSearchFilter}
              />
            )}
          </>
        </ExoteachLayout>
      )}
    </>
  );
}
