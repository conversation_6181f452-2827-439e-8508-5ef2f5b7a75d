import NotificationForm from '@/shared/components/Notification/NotificationForm.jsx';
import { SubjectTreeSelect } from '@/shared/components/UE/SubjectTreeSelect.jsx';
import { QcmAuthorEditor } from '@/shared/pages/admin/qcm/components/modal/components/QcmAuthorEditor.jsx';
import UserLogs from '@/shared/components/User/UserLogs.jsx';
import { QUERY_MES_UES_LIGHT } from '@/shared/graphql/cours.js';
import { GET_ANNEES } from '@/shared/graphql/home.js';
import { NOTIFY_QCM_UPDATE } from '@/shared/graphql/notifications.js';
import { QUERY_ALL_QCM_TYPE } from '@/shared/graphql/qcm.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { EditMcqHeaderElements } from '@/shared/pages/admin/qcm/components/modal/components/EditMcqHeaderElements.jsx';
import { MUTATION_IMPORT_QUESTIONS_CSV } from '@/shared/pages/admin/qcm/components/modal/ImportQuestionModal.jsx';
import { CreateEditTypeQcmModal } from '@/shared/pages/admin/qcm/components/TypeQcm/CreateEditTypeQcmModal.jsx';
import { TypeQcmManager } from '@/shared/pages/admin/qcm/components/TypeQcm/TypeQcmManager.jsx';
import { EditDatesDiffusion } from '@/shared/pages/cours/details/components/EditInformationsModal.jsx';
import { convertSecondsToHoursMinutesSeconds, formatAnnee } from '@/shared/services/qcm.js';
import { getLanguageName, tr } from '@/shared/services/translate.js';
import { isAdmin } from '@/shared/utils/authority.js';
import {
  getUrlProtectedRessource,
  GlobalConfig,
  isAptoria,
  showGqlErrorsInMessagePopupFromException
} from '@/shared/utils/utils.js';
import { GET_ME } from '@/shared/models/user';
import {
  CheckCircleTwoTone,
  EditOutlined,
  LoadingOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  QuestionCircleTwoTone,
  CheckCircleOutlined
} from '@ant-design/icons';
import {
  Space,
  Checkbox,
  Button,
  Form,
  Input,
  message,
  Modal,
  Select,
  Col,
  Card,
  Tabs,
  Radio,
  Upload,
  Tooltip,
  Segmented,
  Row,
  Popover,
  Alert,
  InputNumber,
  Image,
  Tag
} from 'antd';
import { useMutation, useQuery, gql } from '@apollo/client';
import React, { useContext, useEffect, useState } from 'react';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult.jsx';
import { router } from 'umi';
import { useTranslation } from 'react-i18next';
import { HierarchySelecter, validesTypes } from '@/shared/components/HierarchySelecter';

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE'
};

const MUTATION_CREATE_QCM = gql`
  mutation createQcm($qcm: QcmInput!) {
    createQcm(qcm: $qcm) {
      id_qcm
    }
  }
`;

export const MUTATION_UPDATE_QCM = gql`
  mutation updateQcm($id: ID!, $qcm: QcmInput!) {
    updateQcm(id: $id, qcm: $qcm)
  }
`;
const MUTATION_IMPORT_QCM = gql`
  mutation ImportFullMcqFromJson(
    $file: Upload
    $ueId: ID
    $typesIdsForImport: [ID]
    $annee: String
  ) {
    importFullMcqFromJson(
      file: $file
      ueId: $ueId
      typesIdsForImport: $typesIdsForImport
      annee: $annee
    ) {
      id_qcm
    }
  }
`;
const MUTATION_IMPORT_QCM_XLS = gql`
  mutation importFullMcqFromXls($file: Upload, $id_qcm: ID!) {
    importFullMcqFromXls(file: $file, id_qcm: $id_qcm)
  }
`;
const getMutationFromModalType = (modalType) => {
  switch (modalType) {
    case ModalType.CREATE:
      return MUTATION_CREATE_QCM;
    case ModalType.UPDATE:
      return MUTATION_UPDATE_QCM;
    default:
      return MUTATION_CREATE_QCM;
  }
};

const DEFAULT_TIMER_DELAY = 90;

export const CreateEditQcmModal = ({
  closeModalHandler,
  modalType,
  isVisible,
  ueName,
  ueImage,
  ueDescription,
  currentYear,
  qcm: {
    id_qcm,
    titre,
    titre_en,
    titre_es,
    titre_it,
    titre_de,
    description,
    description_en,
    description_es,
    description_it,
    description_de,
    url_image,
    deleted,
    chronometre,
    timer_delay,
    goToNextQuestionWhenTimesUp,
    shouldResumeTime,
    chronoByQuestionOrGlobal,
    annee,
    pseudo_createur,
    id_createur,
    isAuthorChanged,
    annale,
    difficulty,
    isPublished,
    isFullscreen,
    timesItCanBeDone,
    hasCheckboxes,
    UEId,
    randomizeQuestions,
    randomizeQuestionsAnswers,
    showCorrectionAtEachStep,
    groupQuestionsByTheme,
    type,
    defaultQuestionsType,

    hasExternalQuestions,
    questionPickingStrategy,
    enableAiAnalysis,

    correctionConfig = {
      // Default values
      showCorrection: true,
      showNote: true,
      showMoyenne: true,
      showAnalysis: true,
      showStrengthsWeaknesses: true,
      enableAiAnalysis: false
    }
  } = {}
}) => {
  const { t, i18n } = useTranslation();
  const { enabledLanguages } = useContext(GlobalContext);
  const [form] = Form.useForm();
  const [Mutation, { loading, data, error }] = useMutation(getMutationFromModalType(modalType));
  const [fileImage, setFileImage] = useState(null);
  // For new MCQ only
  const [typeQcmIDS, setQcmTypeIds] = useState([]);
  const [defaultQuestionTypeQcmIDS, setDefaultQcmTypeIds] = useState([]);
  const [actualCorrectionConfig, setCorrectionConfig] = useState(correctionConfig);
  const [questionPickingStrategyValue, setQuestionPickingStrategyValue] =
    useState(questionPickingStrategy);
  const [isChronoByQuestionOrGlobal, setchronoByQuestionOrGlobal] = useState(
    chronoByQuestionOrGlobal || 'timeByQuestion'
  );
  const [timerValues, setTimerValues] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  });
  const [selectedUEId, setSelectedUEId] = useState(UEId || null);
  //const [selectedUEName, setSelectedUEName] = useState(null);

  // Variables pour la sélection de l'autheur dans le modal
  const { data: { me } = {} } = useQuery(GET_ME, { fetchPolicy: 'cache-and-network' });
  const [selectedUsernameCreateur, setSelectedUsernameCreateur] = useState(
    pseudo_createur || me?.username
  );
  const [stateIsAuthorChanged, setStateIsAuthorChanged] = useState(isAuthorChanged);
  const [selectedIdCreateur, setSelectedIdCreateur] = useState(id_createur || me?.id);

  const dataTypeQcm = useQuery(QUERY_ALL_QCM_TYPE, { fetchPolicy: 'cache-and-network' });
  const { data: { annees = null } = {} } = useQuery(GET_ANNEES, {
    fetchPolicy: 'cache-and-network'
  });
  const { data: { mesUEs = null } = {} } = useQuery(QUERY_MES_UES_LIGHT, {
    fetchPolicy: 'cache-and-network'
  });

  // UseEffect qui lorsque l'on change le selectedUsernameCreateur set le 'isAuthorChanged' à 1 si il est différent de me.username. Si c'est identique, la reset à la valeur par default
  useEffect(() => {
    switch (true) {
      // pseudo_createur est à undefined lorsque l'on créé une serie donc ce test permet d'exclure les fois où l'on créé des séries
      // Cas où on édite une série pour remettre son auteur original
      case pseudo_createur !== undefined && selectedUsernameCreateur === pseudo_createur:
        setStateIsAuthorChanged(isAuthorChanged); // on reset à 0
        break;

      // Cas où l'on édite une série pour changer son autheur original
      case pseudo_createur !== undefined && selectedUsernameCreateur !== pseudo_createur:
        setStateIsAuthorChanged(true);
        break;

      // Cas où potentiellement un admin crée une serie en utilisant le nom de qqn d'autre => sera override dans tous les cas par les fonctions bas level (pour ça que désactivé)
      case pseudo_createur === undefined && selectedUsernameCreateur !== me.username:
        setStateIsAuthorChanged(true);
        break;

      // Cas où pseduo_createur === undefined => On créé la série, alors on set à False
      case pseudo_createur === undefined:
        setStateIsAuthorChanged(false);
        break;

      default:
        throw new Error("Comportement innatendu dans l'assigniation de l'user au qcm");
    }
  }, [selectedUsernameCreateur]);

  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  useEffect(() => {
    setQuestionPickingStrategyValue(questionPickingStrategy);
  }, [questionPickingStrategy]);

  /**/
  function convertDurationtoSeconds() {
    const { hours, minutes, seconds } = timerValues;
    let hoursInSecond = Number(hours) * 60 * 60;
    if (isChronoByQuestionOrGlobal === 'timeByQuestion') {
      hoursInSecond = 0;
    }
    return hoursInSecond + Number(minutes) * 60 + Number(seconds);
  }

  useEffect(() => {
    if (timer_delay > 0) {
      const { hours, minutes, seconds } = convertSecondsToHoursMinutesSeconds(timer_delay);
      setTimerValues({
        hours,
        minutes,
        seconds
      });
    }
  }, [timer_delay]);

  const handleFinish = async (data) => {
    try {
      let newQcm;
      if (fileImage) {
        newQcm = { ...data, url_image: fileImage };
      } else {
        newQcm = { ...data };
      }
      if (selectedUEId === null || selectedUEId === undefined) {
        message.error('Sélectionnez une matière');
        return;
      }
      newQcm.UEId = parseInt(selectedUEId); // Parent subject
      newQcm.ue = parseInt(selectedUEId); // Legacy only UEId is used, will be removed later
      newQcm.annee = parseInt(data.annee);
      newQcm.timer_delay = convertDurationtoSeconds() || DEFAULT_TIMER_DELAY;
      newQcm.timesItCanBeDone = parseInt(data.timesItCanBeDone || 0);
      newQcm.chronoByQuestionOrGlobal = isChronoByQuestionOrGlobal;

      newQcm.correctionConfig = {
        showCorrection: newQcm.showCorrection,
        showNote: newQcm.showNote,
        showMoyenne: newQcm.showMoyenne,

        showAnalysis: newQcm.showAnalysis,
        showStrengthsWeaknesses: newQcm.showStrengthsWeaknesses
      };

      // Modif du createur
      newQcm.pseudo_createur = selectedUsernameCreateur;
      newQcm.id_createur = selectedIdCreateur;
      newQcm.isAuthorChanged = stateIsAuthorChanged;

      // Remove fields that are not part of the QCM model, but are used for the form
      delete newQcm.showCorrection;
      delete newQcm.showNote;
      delete newQcm.showMoyenne;
      delete newQcm.showAnalysis;
      delete newQcm.showStrengthsWeaknesses;

      if (modalType === ModalType.UPDATE) {
        await Mutation({ variables: { id: id_qcm, qcm: newQcm } });
        message.success(t('Updated'));
      } else {
        // Create
        if (!typeQcmIDS || typeQcmIDS?.length === 0) {
          message.error("Veuillez choisir au moins un type de série d'exercice");
          return;
        }
        if (!defaultQuestionTypeQcmIDS || defaultQuestionTypeQcmIDS?.length === 0) {
          message.error('Veuillez choisir au moins un type par défaut pour les questions créées');
          return;
        }

        newQcm.typeQcmIDS = typeQcmIDS;
        newQcm.defaultQuestionsTypeQcmIDS = defaultQuestionTypeQcmIDS;
        const result = await Mutation({ variables: { qcm: newQcm } });
        message.success(t('Created'));
        // redirect to question creation
        if (result && result.data) {
          router.push(`/admin-series/edit/${result.data.createQcm.id_qcm}`);
        }
      }
      await closeModalHandler();
    } catch (e) {
      if (e.message.includes('notNull Violation: qcm.ue cannot be null')) {
        // Gérer l'erreur de contrainte NOT NULL
        message.error('Veuillez sélectionner une matière pour la série.');
      } else {
        // Gérer d'autres erreurs
        showGqlErrorsInMessagePopupFromException(e);
      }
      // eslint-disable-next-line no-console
      console.error(e);
    }
  };

  let notificationIfAdmin;
  if (isAdmin() && id_qcm) {
    notificationIfAdmin = (
      <Col xl={24} lg={24} md={24} sm={24} xs={24}>
        <Card>
          <NotificationForm
            GQLFunc={NOTIFY_QCM_UPDATE}
            message={{
              qcmId: id_qcm,
              route: `/qcm/${id_qcm}`
            }}
            title={t('AvailableExercice')}
            body={titre}
          />
        </Card>
      </Col>
    );
  } else {
    notificationIfAdmin = (
      <>
        {/* Vous n'avez pas le droit */}
        <Alert
          message={"Vos droits actuels ne vous permettent pas d'envoyer de notifications"}
          type="error"
          showIcon
        />
      </>
    );
  }

  const [ImportMutation, { loading: loadingImport }] = useMutation(MUTATION_IMPORT_QCM);
  const [ImportXLSMutation, { loading: loadingImportXls }] = useMutation(MUTATION_IMPORT_QCM_XLS);
  const [ImportCSVMutation, { loading: loadingImportCSV }] = useMutation(
    MUTATION_IMPORT_QUESTIONS_CSV
  );
  const [fileToImport, setFileToImport] = useState(null);

  const [typesIdsForImport, setTypeIdsToImport] = useState([]);

  /* ExoQCM Import */
  const importMcqFromFile = (
    <React.Fragment>
      <Form
        onFinish={async () => {
          try {
            message.loading({ key: 'isLoading', content: 'Importation en cours....' });
            await ImportMutation({
              variables: { file: fileToImport, typesIdsForImport, ueId: UEId, annee }
            });
            message.success({ key: 'isLoading', content: 'Terminé!' });
            closeModalHandler();
          } catch (e) {
            console.error(e);
          }
        }}
      >
        <Form.Item label={t('ContentType')}>
          <TypeQcmManager
            typesQcm={typesIdsForImport}
            qcmId={id_qcm}
            dataTypeQcm={dataTypeQcm}
            // onChange overrides component inner mutations
            onChange={(_, options) => {
              const ids = options?.map((o) => o.key);
              setTypeIdsToImport(ids);
            }}
          />
        </Form.Item>
        <Form.Item label={t('FileToImportExoqcm')}>
          <Upload.Dragger
            name="file"
            listType="file"
            showUploadList
            // accept=".exoqcm"
            beforeUpload={(file, fileList) => {
              setFileToImport(file);
              return false;
            }}
            fileList={fileToImport ? [fileToImport] : []}
            onRemove={(file) => setFileToImport('')}
          >
            <div>
              {loading ? <LoadingOutlined /> : <PlusOutlined />}
              <div className="ant-upload-text">{t('general.upload')}</div>
            </div>
          </Upload.Dragger>
        </Form.Item>

        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loadingImport}>
            {t('general.import')}
          </Button>
        </Form.Item>
      </Form>
    </React.Fragment>
  );
  const importMcqFromXLS = (
    <React.Fragment>
      <Form
        onFinish={async () => {
          try {
            message.loading({ key: 'isLoading', content: 'Importation en cours....' });
            await ImportXLSMutation({ variables: { file: fileToImport, id_qcm } });
            message.success({ key: 'isLoading', content: 'Terminé!' });
            closeModalHandler();
          } catch (e) {
            console.error(e);
          }
        }}
      >
        <Form.Item label={t('FileToImportXLS')}>
          <Upload.Dragger
            name="file"
            listType="file"
            showUploadList
            beforeUpload={(file, fileList) => {
              setFileToImport(file);
              return false;
            }}
            fileList={fileToImport ? [fileToImport] : []}
            onRemove={(file) => setFileToImport('')}
          >
            <div>
              {loading ? <LoadingOutlined /> : <PlusOutlined />}
              <div className="ant-upload-text">{t('general.upload')}</div>
            </div>
          </Upload.Dragger>
        </Form.Item>

        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loadingImport}>
            {t('general.import')}
          </Button>
        </Form.Item>
      </Form>
    </React.Fragment>
  );

  const importParentsChildsFromCSV = (
    <React.Fragment>
      <Form
        onFinish={async (data) => {
          try {
            message.loading({ key: 'isLoading', content: t('Importing...') });
            await ImportCSVMutation({
              variables: {
                file: fileToImport,
                qcmId: id_qcm,
                type: data?.type
              }
            });
            message.success({ key: 'isLoading', content: t('general.done') });
            setFileToImport(null);
            closeModalHandler();
          } catch (e) {
            console.error(e);
          }
        }}
      >
        <Form.Item label={t('FileType')} name="type">
          <Select>
            <Select.Option key="parentsChilds" value="parentsChilds">
              {t('Parents/Childs')}
            </Select.Option>
            <Select.Option key="doubleParentsChilds" value="doubleParentsChilds">
              {t('DoubleParentChildren')}
            </Select.Option>

            <Select.Option key="errorParentsChilds" value="errorParentsChilds">
              {t('ErrorParentChildren')}
            </Select.Option>
            <Select.Option key="errorDoubleParentsChilds" value="errorDoubleParentsChilds">
              {t('ErrorDoubleParentChildren')}
            </Select.Option>
          </Select>
        </Form.Item>

        <Form.Item label={t('FileToImportCSV')}>
          <Upload.Dragger
            name="file"
            listType="file"
            showUploadList
            beforeUpload={(file, fileList) => {
              setFileToImport(file);
              return false;
            }}
            fileList={fileToImport ? [fileToImport] : []}
            onRemove={(file) => setFileToImport('')}
          >
            <div>
              {loading ? <LoadingOutlined /> : <PlusOutlined />}
              <div className="ant-upload-text">{t('general.upload')}</div>
            </div>
          </Upload.Dragger>
        </Form.Item>

        <br />
        <Alert
          type="info"
          message="Format du fichier CSV"
          description={'La premiere colonne de la premiere ligne doit être "question_id"'}
        />
        <br />

        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loadingImport}>
            {t('general.import')}
          </Button>
        </Form.Item>
      </Form>
    </React.Fragment>
  );

  const ChangeQuestionOrderFromCSV = (
    <React.Fragment>
      <Form
        onFinish={async (data) => {
          try {
            message.loading({ key: 'isLoading', content: 'Importation en cours....' });
            await ImportCSVMutation({
              variables: {
                file: fileToImport,
                qcmId: id_qcm,
                type: 'changeQuestionOrder'
              }
            });
            message.success({ key: 'isLoading', content: 'Terminé!' });
            setFileToImport(null);
            closeModalHandler();
          } catch (e) {
            console.error(e);
          }
        }}
      >
        <Form.Item label={t('FileToImportCSV')}>
          <Upload.Dragger
            name="file"
            listType="file"
            showUploadList
            beforeUpload={(file, fileList) => {
              setFileToImport(file);
              return false;
            }}
            fileList={fileToImport ? [fileToImport] : []}
            onRemove={(file) => setFileToImport('')}
          >
            <div>
              {loading ? <LoadingOutlined /> : <PlusOutlined />}
              <div className="ant-upload-text">{t('general.upload')}</div>
            </div>
          </Upload.Dragger>
        </Form.Item>

        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loadingImport}>
            {t('ImportNewOrder')}
          </Button>
        </Form.Item>
      </Form>
    </React.Fragment>
  );

  const qcmInitialValues = {
    titre,
    titre_en,
    titre_es,
    titre_it,
    titre_de,
    description,
    description_en,
    description_es,
    description_it,
    description_de,
    isPublished,
    chronometre: chronometre || false,
    difficulty,
    annee: `${annee}`,
    UEId: `${UEId}`,
    timer_delay,
    goToNextQuestionWhenTimesUp,
    shouldResumeTime,
    chronoByQuestionOrGlobal,
    annale,
    isFullscreen,
    timesItCanBeDone,
    hasCheckboxes,
    randomizeQuestions,
    randomizeQuestionsAnswers,
    showCorrectionAtEachStep,
    groupQuestionsByTheme,
    type,
    defaultQuestionsType,

    hasExternalQuestions,
    questionPickingStrategy,

    pseudo_createur,
    isAuthorChanged: isAuthorChanged || false,

    showCorrection: correctionConfig?.showCorrection,
    showNote: correctionConfig?.showNote,
    showMoyenne: correctionConfig?.showMoyenne,

    enableAiAnalysis: enableAiAnalysis,
    showAnalysis:
      correctionConfig?.showAnalysis === undefined ? true : correctionConfig?.showAnalysis,
    showStrengthsWeaknesses:
      correctionConfig?.showStrengthsWeaknesses === undefined
        ? true
        : correctionConfig?.showStrengthsWeaknesses
  };
  const [formValues, setFormValues] = useState(qcmInitialValues);

  /* TYPE QCMS */
  const [createTypeVisible, setCreateTypeVisible] = useState(false);

  const editTypeQcm = (
    <Form.Item
      label={
        <>
          <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
            <div style={{ fontSize: '20px', fontWeight: 700 }}>{t('MCQType')}</div>
            <div>
              {isAdmin() && (
                <span>
                  {' '}
                  {/* Vous pouvez utiliser <div> si vous préférez */}
                  <Tooltip title={t('AddAType')} style={{ width: '20%' }}>
                    <PlusCircleOutlined
                      className="dynamic-delete-button"
                      onClick={() => setCreateTypeVisible(true)}
                    />
                  </Tooltip>
                  {createTypeVisible && (
                    <CreateEditTypeQcmModal
                      isVisible={createTypeVisible}
                      modalType={ModalType.CREATE}
                      closeModalHandler={() => {
                        setCreateTypeVisible(false);
                        dataTypeQcm.refetch(); // Load new modifications
                      }}
                    />
                  )}
                </span>
              )}
            </div>
          </div>
        </>
      }
    >
      <div>
        <div>
          {qcmInitialValues && (
            <TypeQcmManager
              typesQcm={type}
              qcmId={id_qcm}
              dataTypeQcm={dataTypeQcm}
              // onChange overrides component inner mutations
              onChange={
                modalType === ModalType.CREATE
                  ? (_, options) => {
                      const ids = options?.map((o) => o.key);
                      setQcmTypeIds(ids);
                    }
                  : null
              }
            />
          )}
        </div>
      </div>
    </Form.Item>
  );

  const editDefaultExerciceType = (
    <Form.Item
      label={
        <>
          <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
            <div style={{ fontSize: '20px', fontWeight: 700 }}>
              {t('DefaultCreatedExerciseTypes')}
            </div>
            <div>
              <Popover
                content={<p>{t('ExplanationDefaultExercice')}</p>}
                title={t('general.Help')}
                trigger="hover"
              >
                <QuestionCircleTwoTone />
              </Popover>
            </div>
            <div>
              {isAdmin() && (
                <span>
                  {' '}
                  {/* Vous pouvez utiliser <div> si vous préférez */}
                  <Tooltip title="Ajouter un type" style={{ width: '20%' }}>
                    <PlusCircleOutlined
                      className="dynamic-delete-button"
                      onClick={() => setCreateTypeVisible(true)}
                    />
                  </Tooltip>
                  {createTypeVisible && (
                    <CreateEditTypeQcmModal
                      isVisible={createTypeVisible}
                      modalType={ModalType.CREATE}
                      closeModalHandler={() => {
                        setCreateTypeVisible(false);
                        dataTypeQcm.refetch(); // Load new modifications
                      }}
                    />
                  )}
                </span>
              )}
            </div>
          </div>
        </>
      }
    >
      <div>
        <div>
          {qcmInitialValues && (
            <TypeQcmManager
              typesQcm={defaultQuestionsType}
              qcmId={id_qcm}
              dataTypeQcm={dataTypeQcm}
              type="AddRemoveQcmTypeDefaultQuestionType"
              // onChange overrides component inner mutations
              onChange={
                modalType === ModalType.CREATE
                  ? (_, options) => {
                      const ids = options?.map((o) => o.key);
                      setDefaultQcmTypeIds(ids);
                    }
                  : null
              }
            />
          )}
        </div>
      </div>
    </Form.Item>
  );

  const timerProps = {
    min: 0,
    style: { maxWidth: '140px' },
    type: 'number'
  };

  /*
  const getSelectedSubjectText = () => {
    if (!selectedUEName) {
      return '';
    }
    return (
      <>
        {' '}
        : &nbsp;
        <u>
          {selectedUEName} <CheckCircleTwoTone />
        </u>
      </>
    );
  };

   */

  return (
    <Modal
      title={
        modalType === ModalType.UPDATE
          ? `${t('Edit')} ${titre}`
          : //: `${t('CreateNewSerieIn')} ${ueName}`
            `${t('CreateNewSerie')}`
      }
      open={isVisible}
      onCancel={closeModalHandler}
      footer={null}
      closable
      width={'90%'}
      confirmLoading={false}
      bodyStyle={{ paddingTop: '0' /* Because of the tabs */ }}
    >
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab={t('tab.General')} key={1}>
          <React.Fragment>
            {/* Show small error(s) if needed */}
            <SmallErrorsAlert error={error} loading={loading} />
            <Form
              // layout="vertical"
              layout="vertical"
              onFinish={handleFinish}
              form={form}
              onValuesChange={async (values) => {
                setFormValues({ ...formValues, ...values }); // real form values
              }}
              initialValues={qcmInitialValues}
            >
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  gap: '8px'
                }}
              >
                <div style={{ display: 'flex', flex: '2' }}>
                  <Card
                    style={{ width: '100%' }}
                    size="small"
                    headStyle={{ backgroundColor: primaryColor, textAlign: 'center' }}
                    title={<span style={{ color: 'white' }}> {t('SORTING')}</span>}
                  >
                    <Form.Item
                      label={
                        <div style={{ fontSize: '20px', fontWeight: 700 }}>
                          {t('general.Subject')}
                        </div>
                      }
                    >
                      <>
                        <HierarchySelecter
                          multiple={false}
                          useTreeSelect
                          setterHookSelection={(value) => {
                            setSelectedUEId(value[0]);
                          }}
                          initialisationVariable={
                            selectedUEId ? { [validesTypes.CTYPE_UE]: [selectedUEId] } : null
                          }
                          simplificationFeature={validesTypes.CTYPE_UE}
                          disabledTypes={[
                            validesTypes.CTYPE_FOLDER,
                            validesTypes.CTYPE_COURS,
                            validesTypes.CTYPE_CATEGORY,
                            validesTypes.CTYPE_UNKNOWN,
                            validesTypes.CTYPE_PAGE
                          ]}
                          rankToRemoveIfLeaf={[
                            validesTypes.CTYPE_FOLDER,
                            validesTypes.CTYPE_COURS,
                            validesTypes.CTYPE_CATEGORY,
                            validesTypes.CTYPE_UNKNOWN,
                            validesTypes.CTYPE_PAGE
                          ]}
                          additionalTreeProps={{
                            placement: 'topLeft',
                            listHeight: 500,
                            style: { width: '500px', marginRight: '5px' },
                            popupMatchSelectWidth: false,
                            treeLine: true,
                            size: 'large',
                            placeholder: t('general.SelectUeForSeriePlaceholder')
                          }}
                        />
                        {/*
                        <div>UE ID : {selectedUEId}</div>
                        {<Button onClick={() => {
                          setSelectedUEId(null)
                        }}> remove UEID</Button>}
                        */}
                        {/*


                        <ShowUeBadge UEId={selectedUEId}/>
                        {!editingUE && <Button shape={'circle'} icon={<EditOutlined />} type="primary" onClick={() => setEditingUe(true)}/>}
                        {editingUE &&  <Button shape={'circle'} icon={<CheckCircleOutlined />} type="primary" onClick={() => setEditingUe(false)}/>}
                        {
                          editingUE &&
                          <HierarchySelecter
                            //initialisationVariable = {[validesTypes.CTYPE_CATEGORY]:["4"]},
                            multiple={false}
                            useTreeSelect
                            setterHookSelection={(value)=>{setSelectedUEId(value[0])}}
                            initialisationVariable={{[validesTypes.CTYPE_UE]:[selectedUEId]}}
                            simplificationFeature={validesTypes.CTYPE_UE}
                            disabledTypes={[validesTypes.CTYPE_FOLDER,validesTypes.CTYPE_COURS,validesTypes.CTYPE_CATEGORY,validesTypes.CTYPE_UNKNOWN,validesTypes.CTYPE_PAGE]}
                            rankToRemoveIfLeaf={[validesTypes.CTYPE_FOLDER,validesTypes.CTYPE_COURS,validesTypes.CTYPE_CATEGORY,validesTypes.CTYPE_UNKNOWN,validesTypes.CTYPE_PAGE]}
                          />
                        }
                        {<Button onClick={()=>{setSelectedUEId(null)}}> remove UEID</Button>}
                        */}
                        {/*
                        <Button
                          shape={'circle'}
                          icon={<EditOutlined />}
                          type="primary"
                          onClick={() => setEditingUe(!editingUE)}
                        />
                        <Button
                          shape={'circle'}
                          icon={<CheckCircleOutlined />}
                          type="primary"
                          onClick={() => setEditingUe(!editingUE)}
                        />


                        <HierarchySelecter
                          //initialisationVariable = {[validesTypes.CTYPE_CATEGORY]:["4"]},
                          multiple={false}
                          useTreeSelect
                          setterHookSelection={(value)=>{setSelectedUEId(value[0])}}
                          initialisationVariable={{[validesTypes.CTYPE_UE]:[selectedUEId]}}
                          simplificationFeature={validesTypes.CTYPE_UE}
                          disabledTypes={[validesTypes.CTYPE_FOLDER,validesTypes.CTYPE_COURS,validesTypes.CTYPE_CATEGORY,validesTypes.CTYPE_UNKNOWN,validesTypes.CTYPE_PAGE]}
                          rankToRemoveIfLeaf={[validesTypes.CTYPE_FOLDER,validesTypes.CTYPE_COURS,validesTypes.CTYPE_CATEGORY,validesTypes.CTYPE_UNKNOWN,validesTypes.CTYPE_PAGE]}
                        />
                        <ShowUeBadge UEId={selectedUEId}/>
                      </>

                      {!editingUE ? (
                        <div
                          style={{
                            fontWeight: 600,
                            display: 'flex',
                            alignItems: 'center',
                            gap: '5px',
                          }}
                        >
                          <div>
                            {selectedUEImage && <Image src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL+selectedUEImage)} style={{height:'40px',width:'auto'}}/>}
                          </div>
                          <div style={{ fontSize: '16px' }}>{selectedUEName}</div>
                          <div>
                            <Button
                              shape={'circle'}
                              icon={<EditOutlined />}
                              type="primary"
                              onClick={() => setEditingUe(!editingUE)}
                            ></Button>
                          </div>
                        </div>
                      ) : (
                        <>
                            <SubjectTreeSelect
                              defaultIds={selectedUEId ? [selectedUEId] : []}
                              multiple={false}
                              setUeIds={(ids) => {
                                const id = ids[ids.length - 1]; // Récupère le dernier élément de ids
                                setSelectedUEId(id);
                                const ueNameTmp=mesUEs?.find(ue => String(ue.id) === String(id))?.name
                                const ueImageTmp=mesUEs?.find(ue => String(ue.id) === String(id))?.image
                                setSelectedUEName(ueNameTmp);
                                setHasInvalidUE(false);
                                setEditingUe(!editingUE);
                                setSelectedUEImage(ueImageTmp);
                              }}
                            />

*/}
                        {/*
                            OLD component
                            <PedagogicCascaderSelector
                              onSelectTarget={({ label, value, type }) => {
                                if (type === 'ue') {
                                  //message.success(`Matière sélectionnée: ${label}`);
                                  setSelectedUEName(label);
                                  setSelectedUEId(value);
                                  setHasInvalidUE(false);
                                }
                              }}
                              onSelectInvalidTarget={() => {
                                setHasInvalidUE(true);
                              }}
                              acceptedTypes={[PedagogicCascaderSelectorTarget.UE]}
                              hideCategories={true}
                              placeholder="Choisir une matière..."
                              selectorType={SelectorType.Cascader}
                            />
                            */}

                        {/*
                      </>
                      {hasInvalidUE && (
                        <Alert
                          message="Attention"
                          description="Vous devez sélectionner une matière."
                          type="warning"
                          showIcon
                          style={{ marginTop: '8px' }}
                        />

                      )}*/}
                      </>
                    </Form.Item>

                    {/*
                    <Form.Item name="UEId" label={t('general.Subject')}>
                      <Select placeholder={t('ChooseASubject')}>
                        {(mesUEs && mesUEs?.map(ue => (
                          <Select.Option key={ue.id} value={ue.id}>{ue.name}: {ue.description}</Select.Option>
                        )))}
                      </Select>
                    </Form.Item>
                    */}

                    {/* Année */}
                    <Form.Item
                      name="annee"
                      label={
                        <>
                          <div
                            style={{
                              fontSize: '20px',
                              fontWeight: 700
                            }}
                          >
                            {t('Year')}
                          </div>
                        </>
                      }
                    >
                      <Select placeholder={t('ChooseYear')}>
                        {annees?.map((annee) => (
                          <Select.Option key={annee.annee} value={annee.annee}>
                            {formatAnnee(annee.annee)}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>

                    {editTypeQcm}

                    {editDefaultExerciceType}

                    <Tabs defaultActiveKey={i18n.language}>
                      {enabledLanguages &&
                        enabledLanguages?.map((lang) => (
                          <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                            <Form.Item
                              name={tr('titre', lang)}
                              label={
                                <>
                                  <div
                                    style={{
                                      fontSize: '20px',
                                      fontWeight: 700
                                    }}
                                  >
                                    {t('SerieTitle')}
                                  </div>
                                </>
                              }
                              rules={[
                                {
                                  required: true,
                                  min: 1,
                                  message: t('PleaseEnterMcqName')
                                }
                              ]}
                            >
                              <Input type="text" placeholder={t('Title')} />
                            </Form.Item>
                            <Form.Item
                              name={tr('description', lang)}
                              label={
                                <>
                                  <div
                                    style={{
                                      fontSize: '20px',
                                      fontWeight: 700
                                    }}
                                  >
                                    {t('SerieDescription')}
                                  </div>
                                </>
                              }
                              rules={[{ required: true }]}
                            >
                              <Input.TextArea rows={4} type="text" placeholder="" />
                            </Form.Item>
                          </Tabs.TabPane>
                        ))}
                    </Tabs>
                  </Card>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', flex: '1' }}>
                  <div>
                    <Card
                      size="small"
                      headStyle={{ backgroundColor: primaryColor, textAlign: 'center' }}
                      title={<span style={{ color: 'white' }}>AFFICHAGE</span>}
                    >
                      <Form.Item
                        label={
                          <>
                            <div
                              style={{
                                fontSize: '20px',
                                fontWeight: 700
                              }}
                            >
                              {t('State')}
                            </div>
                          </>
                        }
                        name="isPublished"
                      >
                        <Radio.Group defaultValue={false} buttonStyle="solid">
                          <Radio.Button value={false}>
                            <span role="img">🚫</span>
                            &nbsp;<b>{t('isNotPublishedVisibleByStudents')}</b>
                          </Radio.Button>
                          <Radio.Button value={true}>
                            <span role="img">✅️</span>
                            &nbsp;<b>{t('isPublishedVisibleByStudents')}</b>
                          </Radio.Button>
                        </Radio.Group>
                      </Form.Item>

                      <Form.Item
                        name="isFullscreen"
                        label={
                          <div
                            style={{
                              fontSize: '20px',
                              fontWeight: 700
                            }}
                          >
                            {t('general.Display')}
                            &nbsp;
                            <Popover
                              content={<p>{t('ExplanationDisplayExercice')}</p>}
                              title={t('general.Help')}
                              trigger="hover"
                            >
                              <QuestionCircleTwoTone />
                            </Popover>
                          </div>
                        }
                      >
                        <Radio.Group defaultValue={false} buttonStyle="solid">
                          <Radio.Button value={false}>{t('general.classic')}</Radio.Button>
                          <Radio.Button value>{t('1by1')}</Radio.Button>
                        </Radio.Group>
                      </Form.Item>

                      <Form.Item
                        label={
                          <>
                            <div
                              style={{
                                fontSize: '20px',
                                fontWeight: 700
                              }}
                            >
                              {t('Creator')}
                            </div>
                          </>
                        }
                      >
                        <QcmAuthorEditor
                          setterUsername={setSelectedUsernameCreateur}
                          username={selectedUsernameCreateur}
                          setterId={setSelectedIdCreateur}
                          id={selectedIdCreateur}
                          isAuthorChanged={stateIsAuthorChanged}
                          disabled={modalType === ModalType.CREATE}
                        />
                      </Form.Item>
                    </Card>
                  </div>
                  <div>
                    <Card
                      size="small"
                      headStyle={{ backgroundColor: primaryColor, textAlign: 'center' }}
                      title={<span style={{ color: 'white' }}>OPTIONS DU CHRONOMÈTRE</span>}
                    >
                      {/* Chronometer */}
                      <Form.Item
                        label={
                          <div
                            style={{
                              fontSize: '20px',
                              fontWeight: 700
                            }}
                          >
                            {' '}
                            {t('MandatoryTimed')}&nbsp;{' '}
                            <Popover
                              content={<p>{t('MandatoryTimedExplanation')}</p>}
                              title={t('general.Help')}
                              trigger="hover"
                            >
                              <QuestionCircleTwoTone />
                            </Popover>
                          </div>
                        }
                        name="chronometre"
                      >
                        <Radio.Group buttonStyle="solid">
                          <Radio.Button
                            style={
                              formValues.chronometre
                                ? {
                                    backgroundColor: '#73d13d',
                                    borderColor: '#73d13d'
                                  }
                                : {}
                            }
                            value
                          >
                            {t('general.yes')}
                          </Radio.Button>
                          <Radio.Button
                            style={
                              formValues.chronometre === false
                                ? {
                                    backgroundColor: '#ff4d4f',
                                    borderColor: '#ff4d4f'
                                  }
                                : {}
                            }
                            value={false}
                          >
                            {t('general.no')}
                          </Radio.Button>
                        </Radio.Group>
                      </Form.Item>

                      <>
                        <div style={{ fontSize: '20px', fontWeight: 700 }}>Calcul de la durée</div>
                        <div style={{ textAlign: 'left', margin: 'auto', marginBottom: '10px' }}>
                          <Segmented
                            options={[
                              { label: t('TimeByQuestion'), value: 'timeByQuestion' },
                              { label: t('GlobalTime'), value: 'globalTime' }
                            ]}
                            value={isChronoByQuestionOrGlobal}
                            onChange={setchronoByQuestionOrGlobal}
                          />
                        </div>

                        {isChronoByQuestionOrGlobal === 'timeByQuestion' && (
                          <Form.Item
                            label={t('TimeByQuestion')}
                            help={t('inSeconds(90secByDefault)')}
                            // name="timer_delay"
                          >
                            <InputNumber
                              addonAfter="minutes"
                              {...timerProps}
                              value={timerValues.minutes}
                              onChange={(m) =>
                                setTimerValues((p) => ({
                                  ...p,
                                  minutes: m
                                }))
                              }
                            />
                            <InputNumber
                              addonAfter="secondes"
                              {...timerProps}
                              value={timerValues.seconds}
                              onChange={(s) =>
                                setTimerValues((p) => ({
                                  ...p,
                                  seconds: s
                                }))
                              }
                            />
                          </Form.Item>
                        )}
                        {isChronoByQuestionOrGlobal === 'globalTime' && (
                          <>
                            <Form.Item
                              label={t('GlobalTime')}
                              // name="timer_delay"
                            >
                              {/*
                        <InputNumber addonAfter="secondes" type="number" placeholder="450" defaultValue={DEFAULT_TIMER_DELAY}/>
                      */}
                              <InputNumber
                                addonAfter="heures"
                                {...timerProps}
                                value={timerValues.hours}
                                onChange={(h) =>
                                  setTimerValues((p) => ({
                                    ...p,
                                    hours: h
                                  }))
                                }
                              />
                              <InputNumber
                                addonAfter="minutes"
                                {...timerProps}
                                value={timerValues.minutes}
                                onChange={(m) =>
                                  setTimerValues((p) => ({
                                    ...p,
                                    minutes: m
                                  }))
                                }
                              />
                              <InputNumber
                                addonAfter="secondes"
                                {...timerProps}
                                value={timerValues.seconds}
                                onChange={(s) =>
                                  setTimerValues((p) => ({
                                    ...p,
                                    seconds: s
                                  }))
                                }
                              />
                            </Form.Item>
                          </>
                        )}

                        <Form.Item name="shouldResumeTime" valuePropName="checked">
                          <Checkbox>{t('ConserveTimePassedOnSerie')}</Checkbox>
                        </Form.Item>

                        {formValues.isFullscreen === true && (
                          <>
                            <Form.Item name="goToNextQuestionWhenTimesUp" valuePropName="checked">
                              <Checkbox>{t('GoToNextQuestionWhenTimesUp')}</Checkbox>
                            </Form.Item>
                          </>
                        )}
                      </>

                      {/* End chronometer settings */}
                    </Card>
                  </div>
                </div>
              </div>

              {/*
              <Form.Item name="difficulty" label="Difficulté" help="Difficulté entre 0 et 5">
                <Slider tipFormatter={(value) => `${value}/5`} min={0} max={5} step={0.1}/>
              </Form.Item>
              */}

              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  marginTop: '15px',
                  flexWrap: 'wrap',
                  gap: '20px'
                }}
              >
                <Card
                  size="small"
                  headStyle={{ backgroundColor: primaryColor, textAlign: 'center' }}
                  title={<span style={{ color: 'white' }}>OPTIONS DE TIRAGE</span>}
                >
                  <Form.Item name="randomizeQuestions" valuePropName="checked">
                    <Checkbox>{t('RandomlyDrawnQuestions')}</Checkbox>
                  </Form.Item>

                  <Form.Item name="randomizeQuestionsAnswers" valuePropName="checked">
                    <Checkbox>{t('RandomlyDrownitems')}</Checkbox>
                  </Form.Item>

                  {formValues.isFullscreen === true && (
                    <>
                      <Form.Item name="showCorrectionAtEachStep" valuePropName="checked">
                        <Checkbox>{t('showCorrectionAfterEachQuestion')}</Checkbox>
                      </Form.Item>
                      <Form.Item name="groupQuestionsByTheme" valuePropName="checked">
                        <Checkbox>{t('groupQuestionsSameCourseWithSeparators')}</Checkbox>
                      </Form.Item>

                      {isAptoria && (
                        <Form.Item name="questionPickingStrategy" label={t('NextQuestionStrategy')}>
                          <Radio.Group defaultValue="normal" buttonStyle="solid">
                            <Radio.Button value="normal">{t('Normal')}</Radio.Button>
                            <Radio.Button value="smart">{t('Smart')}</Radio.Button>
                          </Radio.Group>
                        </Form.Item>
                      )}
                    </>
                  )}

                  {formValues.isFullscreen === true && (
                    <Form.Item
                      label={t('ThisExerciseCanBeDone')}
                      help={t('ByDefaultCanBeDoneInfinitelyButFirstResultIsSaved')}
                      name="timesItCanBeDone"
                    >
                      <Input type="number" placeholder="0" defaultValue={0} suffix="fois" />
                    </Form.Item>
                  )}
                </Card>

                <Card
                  size="small"
                  headStyle={{ backgroundColor: primaryColor, textAlign: 'center' }}
                  title={<span style={{ color: 'white' }}>PARAMÈTRES DE CORRECTION</span>}
                >
                  <Form.Item name="showNote" valuePropName="checked">
                    <Checkbox>{t('DisplayGrade')}</Checkbox>
                  </Form.Item>
                  <Form.Item name="showMoyenne" valuePropName="checked">
                    <Checkbox>{t('DisplayAverage')}</Checkbox>
                  </Form.Item>
                  <Form.Item name="enableAiAnalysis" valuePropName="checked">
                    <Checkbox>
                      🤖 <Tooltip title={t('AIAnalysisTooltip')}>{t('AIAnalysis')}</Tooltip>
                    </Checkbox>
                  </Form.Item>
                  <Form.Item name="showCorrection" valuePropName="checked">
                    <Checkbox>{t('DisplayCorrection')}</Checkbox>
                  </Form.Item>
                  {/*
                  <Form.Item name="showAnalysis" valuePropName="checked">
                    <Checkbox>{t('DisplayResultAnalysis')}</Checkbox>
                  </Form.Item>
                  <Form.Item name="showStrengthsWeaknesses" valuePropName="checked">
                    <Checkbox>{t('DisplayWeakStrongPoints')}</Checkbox>
                  </Form.Item>
                  */}
                </Card>
              </div>

              <div
                style={{ width: '100%', margin: 'auto', textAlign: 'center', marginTop: '20px' }}
              >
                <Form.Item>
                  {modalType === ModalType.UPDATE && (
                    <>
                      <Button htmlType="submit" type="primary" size={'large'} loading={loading}>
                        {t('Update')}
                      </Button>
                    </>
                  )}
                  {modalType === ModalType.CREATE && (
                    <Space>
                      <Button htmlType="submit" type="primary" size={'large'} loading={loading}>
                        {t('CreateAndAddQuestions')}
                      </Button>
                    </Space>
                  )}
                </Form.Item>
              </div>
            </Form>
          </React.Fragment>
        </Tabs.TabPane>

        {modalType === ModalType.UPDATE && (
          <Tabs.TabPane tab={t('tab.Annexe')} key={2}>
            <EditMcqHeaderElements qcmId={id_qcm} />
          </Tabs.TabPane>
        )}
        {id_qcm && (
          <Tabs.TabPane tab={t('tab.Dates')} key={3}>
            <EditDatesDiffusion qcmId={id_qcm} />
          </Tabs.TabPane>
        )}
        {modalType === ModalType.UPDATE && (
          <Tabs.TabPane tab={t('tab.Notifications')} key={4}>
            {notificationIfAdmin}
          </Tabs.TabPane>
        )}
        {modalType === ModalType.UPDATE && id_qcm && (
          <Tabs.TabPane tab={t('tab.Activity')} key={5}>
            <UserLogs qcmId={id_qcm} withTypeSelector={false} showUser canShowAll />
          </Tabs.TabPane>
        )}

        {/* APTORIA ONLY */}
        {isAptoria && modalType === ModalType.UPDATE && (
          <Tabs.TabPane tab={t('tab.ImportQuestionsXLS')} key={6}>
            {importMcqFromXLS}
          </Tabs.TabPane>
        )}
        {isAptoria && modalType === ModalType.CREATE && (
          <Tabs.TabPane tab={t('tab.ImportQuestionsFromFile')} key={7}>
            {importMcqFromFile}
          </Tabs.TabPane>
        )}
        {isAptoria && modalType === ModalType.UPDATE && (
          <Tabs.TabPane tab={t('tab.ImportQuestionsRelationsParentChildrenCSV')} key={8}>
            {importParentsChildsFromCSV}
          </Tabs.TabPane>
        )}
        {isAptoria && modalType === ModalType.UPDATE && (
          <Tabs.TabPane tab={t('tab.ChangeQuestionsOrderCSV')} key={9}>
            {ChangeQuestionOrderFromCSV}
          </Tabs.TabPane>
        )}
      </Tabs>
    </Modal>
  );
};
