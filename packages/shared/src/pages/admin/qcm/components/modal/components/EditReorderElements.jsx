import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Space, message, Popconfirm, Upload, Tooltip } from 'antd';
import { PlusOutlined, DeleteOutlined, HolderOutlined, PictureOutlined, CloseOutlined } from '@ant-design/icons';
import ExoQuillEditor from '@/shared/components/ExoQuill/ExoQuillEditor';
import { ExoQuillToolbarPresets } from '@/shared/components/ExoQuill/utils';
import ExoFormImage from '@/shared/components/Forms/ExoFormImage';
import { useMutation } from '@apollo/client';
import { MUTATION_UPDATE_IMAGE_QCM } from '@/shared/graphql/qcm';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useTranslation } from 'react-i18next';



// Individual sortable element for admin editing
const SortableElementItem = ({ element, index, onUpdateText, onUpdateImage, onRemove, canRemove }) => {
  const { t } = useTranslation();
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: element.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const getElementClasses = () => {
    let classes = ['edit-reorder-elements__element-item'];
    if (isDragging) {
      classes.push('edit-reorder-elements__element-item--dragging');
    }

    // Add class for adaptive layout based on content type
    const hasText = element.content?.replace(/<[^>]*>/g, '').trim();
    const hasImage = element.imageFileName;

    if (hasImage && !hasText) {
      classes.push('edit-reorder-elements__element-item--image-only');
    } else if (hasText && hasImage) {
      classes.push('edit-reorder-elements__element-item--text-and-image');
    } else if (hasText && !hasImage) {
      classes.push('edit-reorder-elements__element-item--text-only');
    }

    return classes.join(' ');
  };

  const getDeleteButtonClasses = () => {
    let classes = ['edit-reorder-elements__delete-button'];
    if (!canRemove) {
      classes.push('edit-reorder-elements__delete-button--disabled');
    }
    return classes.join(' ');
  };

  return (
    <Card
      ref={setNodeRef}
      className={getElementClasses()}
      style={{ ...style, position: 'relative' }}
      size="small"
    >
      <div className="edit-reorder-elements__element-content">
        <div
          {...attributes}
          {...listeners}
          className="edit-reorder-elements__drag-handle"
        >
          <HolderOutlined />
        </div>

        <div className="edit-reorder-elements__position-indicator">
          {index + 1}
        </div>

        <div className="edit-reorder-elements__content-container">
          <div className="edit-reorder-elements__editor-container">
            <ExoQuillEditor
              defaultValue={element.content || ''}
              onChange={(content) => onUpdateText(element.id, content)}
              modules={ExoQuillToolbarPresets.exerciseEdition}
              placeholder={t('ReorderElements.ElementPlaceholder', { index: index + 1 })}
            />
          </div>

          <div className="edit-reorder-elements__image-container">
            <ExoFormImage
              name={null}
              beforeUpload={(file) => onUpdateImage(element.id, file, 'upload')}
              onDelete={() => onUpdateImage(element.id, null, 'delete')}
              label={t('ReorderElements.ElementImage', { index: index + 1 })}
              defaultValue={element.imageFileName}
              size={180}
              fileType="QCM"
              showDeleteButton={true}
              buttonStyle={{
                marginTop: 8,
                fontSize: '12px',
                height: '24px',
                padding: '0 8px'
              }}
            />
          </div>
        </div>

        <Tooltip title={t('ReorderElements.DeleteElementTooltip')} placement="top">
          <Popconfirm
            title={t('ReorderElements.DeleteConfirmTitle')}
            description={t('ReorderElements.DeleteConfirmDescription')}
            onConfirm={() => onRemove(element.id)}
            okText={t('ReorderElements.DeleteConfirmYes')}
            cancelText={t('ReorderElements.DeleteConfirmNo')}
            okButtonProps={{ danger: true }}
          >
            <Button
              type="text"
              danger
              icon={<CloseOutlined />}
              disabled={!canRemove}
              className={getDeleteButtonClasses()}
              size="small"
              style={{
                position: 'absolute',
                top: '8px',
                right: '8px',
                zIndex: 10,
                backgroundColor: '#fff',
                border: '1px solid #ff4d4f',
                borderRadius: '50%',
                width: '24px',
                height: '24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '12px'
              }}
            />
          </Popconfirm>
        </Tooltip>
      </div>
    </Card>
  );
};

export const EditReorderElements = ({question, setSettings }) => {
  const { t } = useTranslation();
  const [elements, setElements] = useState([]);
  const [activeId, setActiveId] = useState(null);

  // GraphQL mutation for image upload
  const [updateImageQcmMutation] = useMutation(MUTATION_UPDATE_IMAGE_QCM);


  // Configure sensors for better accessibility and touch support
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require 8px movement before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Initialize elements from question settings
  useEffect(() => {

    if (question?.settings?.correctOrder && Array.isArray(question.settings.correctOrder) && question.settings.correctOrder.length > 0) {
      // Handle both old format (array of strings) and new format (array of objects)
      const correctOrder = question.settings.correctOrder;
      if (typeof correctOrder[0] === 'string') {
        // Old format: convert strings to rich content objects
        setElements(correctOrder.map((text, index) => ({
          id: `element-${index}`,
          content: `<p>${text}</p>`, // Convert plain text to HTML
          order: index,
          imageFileName: null
        })));
      } else {
        // New format: already objects with content
        setElements(correctOrder.map((item, index) => ({
          id: item.id || `element-${index}`,
          content: item.content || '',
          order: index,
          imageFileName: item.imageFileName || null
        })));
      }
    } else {
      // Default with 2 empty elements (for new questions or empty correctOrder)
      setElements([
        { id: 'element-0', content: '', order: 0, imageFileName: null },
        { id: 'element-1', content: '', order: 1, imageFileName: null }
      ]);
    }
  }, [question]);

  // Auto-update parent settings when elements change
  useEffect(() => {
    if (elements.length > 0 && setSettings) {
      const correctOrder = elements
        .sort((a, b) => a.order - b.order)
        .map(el => ({
          id: el.id,
          content: el.content,
          order: el.order,
          imageFileName: el.imageFileName
        }));

      setSettings(prevSettings => ({
        ...prevSettings,
        correctOrder: correctOrder
      }));
    }
  }, [elements, setSettings]);

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    if (active.id !== over.id) {
      setElements((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);

        const newElements = arrayMove(items, oldIndex, newIndex);

        // Update order property
        return newElements.map((element, index) => ({
          ...element,
          order: index
        }));
      });
    }
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  const addElement = () => {
    const newElement = {
      id: `element-${Date.now()}`,
      content: '',
      order: elements.length,
      imageFileName: null
    };
    setElements([...elements, newElement]);
  };

  const removeElement = (elementId) => {
    if (elements.length <= 2) {
      message.warning(t('ReorderElements.MinElementsWarning'));
      return;
    }
    
    const newElements = elements
      .filter(el => el.id !== elementId)
      .map((element, index) => ({
        ...element,
        order: index
      }));
    setElements(newElements);
  };

  const updateElementText = (elementId, newContent) => {
    setElements(elements.map(el =>
      el.id === elementId ? { ...el, content: newContent } : el
    ));
  };

  const updateElementImage = async (elementId, file, action) => {
    try {

      if (action === 'upload' && file) {
        // Upload new image
        const result = await updateImageQcmMutation({
          variables: {
            id: `reorder-element-${elementId}`, // Unique identifier for this element
            file,
            action: 'upload',
            type: 'reorder_element'
          }
        });

        // Update element with new image filename
        setElements(prevElements =>
          prevElements.map(el =>
            el.id === elementId ? { ...el, imageFileName: result.data.updateImageQcm } : el
          )
        );

        message.success(t('ReorderElements.ImageUploadSuccess'));
      } else if (action === 'delete') {
        // For reorder elements, we just remove the filename from the element
        // The actual file cleanup will happen when the question is saved
        // (we could implement a cleanup service later if needed)

        // Remove image filename from element
        setElements(prevElements =>
          prevElements.map(el =>
            el.id === elementId ? { ...el, imageFileName: null } : el
          )
        );

        message.success(t('ReorderElements.ImageDeleteSuccess'));
      }
    } catch (error) {
      console.error('Error updating image:', error);
      message.error(t('ReorderElements.ImageUpdateError'));
    }
  };



  // Get the currently dragged element for the overlay
  const activeElement = activeId ? elements.find(element => element.id === activeId) : null;
  const activeIndex = activeElement ? elements.indexOf(activeElement) : -1;

  return (
    <div className="edit-reorder-elements__container">
      <div className="edit-reorder-elements__header">
        <div className="edit-reorder-elements__header-content">
          <h3>{t('ReorderElements.ConfigurationTitle')}</h3>
          <p>{t('ReorderElements.ConfigurationSubtitle')}</p>
        </div>
        <Space className="edit-reorder-elements__header-actions">
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={addElement}
          >
            {t('ReorderElements.AddElement')}
          </Button>
        </Space>
      </div>

      <div className="edit-reorder-elements__instructions">
        <p>
          💡 <strong>{t('ReorderElements.InstructionsTitle')}</strong> {t('ReorderElements.InstructionsText')}
        </p>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
        accessibility={{
          announcements: {
            onDragStart: ({ active }) => t('ReorderElements.DragStart', { id: active.id }),
            onDragOver: ({ active, over }) =>
              over ? t('ReorderElements.DragOver', { activeId: active.id, overId: over.id }) : t('ReorderElements.DragOverNone', { activeId: active.id }),
            onDragEnd: ({ active, over }) =>
              over ? t('ReorderElements.DragEnd', { activeId: active.id, overId: over.id }) : t('ReorderElements.DragEndNone', { activeId: active.id }),
          },
        }}
      >
        <SortableContext
          items={elements.map(el => el.id)}
          strategy={verticalListSortingStrategy}
        >
          <div className="edit-reorder-elements__sortable-container">
            {elements.map((element, index) => (
              <SortableElementItem
                key={element.id}
                element={element}
                index={index}
                onUpdateText={updateElementText}
                onUpdateImage={updateElementImage}
                onRemove={removeElement}
                canRemove={elements.length > 2}
              />
            ))}
          </div>
        </SortableContext>

        <DragOverlay>
          {activeElement ? (
            <SortableElementItem
              element={activeElement}
              index={activeIndex}
              onUpdateText={() => {}}
              onUpdateImage={() => {}}
              onRemove={() => {}}
              canRemove={false}
            />
          ) : null}
        </DragOverlay>
      </DndContext>

      {elements.length < 2 && (
        <div className="edit-reorder-elements__warning-message">
          <p>
            ⚠️ {t('ReorderElements.MinElementsWarning')}
          </p>
        </div>
      )}

      {elements.length >= 2 && elements.every(el => {
        const textContent = el.content?.replace(/<[^>]*>/g, '').trim() || '';
        const hasImage = el.imageFileName && el.imageFileName.trim() !== '';
        return textContent || hasImage;
      }) && (
        <div className="edit-reorder-elements__success-message">
          <p>
            ✅ {t('ReorderElements.ConfigurationValid', { count: elements.length })}
          </p>
        </div>
      )}
    </div>
  );
};
