import { Tag } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  DATA_INPUT_TYPE,
  useAiCreateAndImportContext
} from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';

const WarningPdfCrypted = ({ uploadStructure, pdfStructure, type }) => {
  const { t } = useTranslation();
  const { dataType, selectedCoursPdf, uploadPdf } = useAiCreateAndImportContext();

  return (
    <>
      {((dataType === DATA_INPUT_TYPE.COURS && selectedCoursPdf?.isCrypted === true) ||
        (dataType === DATA_INPUT_TYPE.PDF && uploadPdf?.isCrypted === true)) && (
        <Tag style={{ width: 'fit-content' }} color={'red'}>
          {t('AiQuestionCreationModal.PdfIsCryptedWarning')}
        </Tag>
      )}
    </>
  );
};

export default WarningPdfCrypted;
