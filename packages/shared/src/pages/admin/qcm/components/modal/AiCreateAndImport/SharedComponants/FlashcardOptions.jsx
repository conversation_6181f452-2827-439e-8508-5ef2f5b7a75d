import { Image, Popover, Radio } from 'antd';
import imageTrueFalse from '@/shared/assets/imageTrueFalse.png';
import imageScaleNotation from '@/shared/assets/imageScaleNotation.png';
import InfoCircleOutlined from '@ant-design/icons/lib/icons/InfoCircleOutlined';
import { FLASHCARD_RESPONSE_TYPE } from '@/shared/pages/admin/qcm/bareme/modal/CreateEditBaremeModal';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAiCreateAndImportContext } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';
import { McqScaleQuestionType } from '@/shared/pages/admin/bareme/componants/subBareme';

const FlashcardOptions = () => {
  const { t } = useTranslation();
  const { exerciseFormat, flashcardResponseType, setFlashcardResponseType } =
    useAiCreateAndImportContext();

  return (
    <>
      {exerciseFormat === McqScaleQuestionType.FLASHCARD && (
        <div style={{ display: 'flex' }}>
          <span style={{ minWidth: '200px', textAlign: 'end', alignContent: 'center' }}>
            {t('Flashcard.SelectFlashcardBaremTypeLabel')}
            &nbsp;
            <Popover
              content={
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '10px'
                  }}
                >
                  <span>{t('Flashcard.SelectFlashcardBaremTypeExplanation')}</span>
                  <p>
                    <span style={{ display: 'inline-block', minWidth: '130px' }}>
                      {t('Flashcard.SelectFlashcardBaremTypeExplanationTrueFalse')}
                    </span>
                    &nbsp;:&nbsp;
                    <Image src={imageTrueFalse} height={40} />
                  </p>
                  <p>
                    <span style={{ display: 'inline-block', minWidth: '130px' }}>
                      {t('Flashcard.SelectFlashcardBaremTypeExplanationGradation')}
                    </span>
                    &nbsp;:&nbsp;
                    <Image src={imageScaleNotation} height={40} />
                  </p>
                </div>
              }
              trigger="hover"
            >
              <InfoCircleOutlined
                style={{ fontSize: '12px', cursor: 'pointer', color: '#1890ff' }}
              />
            </Popover>
            &nbsp; :
          </span>

          <Radio.Group
            onChange={(e) => {
              setFlashcardResponseType(e.target.value);
            }}
            value={flashcardResponseType}
            style={{ marginLeft: '5px' }}
            //size={'small'}
          >
            <Radio.Button value={FLASHCARD_RESPONSE_TYPE.BINARY}>
              {t('Flashcard.FlashcardBaremRadioSelecterBinary')}
            </Radio.Button>
            <Radio.Button value={FLASHCARD_RESPONSE_TYPE.CONTINUOUS}>
              {t('Flashcard.FlashcardBaremRadioSelecterContinuous')}
            </Radio.Button>
          </Radio.Group>
        </div>
      )}
    </>
  );
};

export default FlashcardOptions;
