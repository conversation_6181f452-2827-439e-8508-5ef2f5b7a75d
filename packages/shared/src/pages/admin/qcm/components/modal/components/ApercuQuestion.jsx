import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { CreateEditQuestionModal } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal.jsx';
import { QuestionContextProvider } from '@/shared/pages/admin/qcm/context/QuestionContext.jsx';
import { CorrectionQuestionWithAnswers } from '@/shared/pages/qcm/components/QcmQuestion.jsx';
import { isAdmin, isTuteur } from '@/shared/utils/authority.js';
import { useMathJaxScript } from '@/shared/utils/hooks/useScript.js';
import { useQuery, gql } from '@apollo/client';

import { Button } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import { Link } from 'umi';
import { useTranslation } from 'react-i18next';

const QUERY_APERCU_QUESTION = gql`
  query questionDetails($id: ID!) {
    question(id: $id) {
      id_question
      id_qcm
      question
      question_en
      question_es
      question_it
      question_de
      url_image_q
      url_image_explication
      isCheckbox
      isAnswerFreeText
      isAnswerUniqueChoiceInList
      isAnswerMultipleChoiceInList
      type
      isLinkedToCourses
      answers {
        id
        isTrue
        text
        text_en
        text_es
        text_it
        text_de
        explanation
        explanation_en
        explanation_es
        explanation_it
        explanation_de
        url_image_explanation
        url_image
        autoAddNotions
        createdAt
        updatedAt
        posts {
          id
          isResolved
          parentId
          type {
            id
            name
            image
          }
        }
      }
      posts {
        id
        isResolved
        parentId
        type {
          id
          name
          image
        }
      }
      explications
      linkedCours {
        id
        name
        name_en
        name_es
        name_it
        name_de
        text
        text_en
        text_es
        text_it
        text_de
        ueCategory {
          id
          name
        }
      }
      sousCategorie {
        id
        name
        name_en
        name_es
        name_it
        name_de
      }
      successPercent
      questionNumberInMcq
      settings
      schemaLibraryId
      parentQcm {
        id_qcm
      }
    }
  }
`;

const QUERY_ONE_QUESTION_ADMIN = gql`
  query questionDetails($id: ID!) {
    question(id: $id) {
      id_question
      id_sous_categorie
      order
      id_qcm
      question
      isCheckbox
      url_image_q
      url_image_explication
      successPercent
      isAnswerFreeText
      isAnswerUniqueChoiceInList
      isAnswerMultipleChoiceInList
      isLinkedToCourses
      settings
      schemaLibraryId
      notions {
        id
        name
      }
      autoAddNotions
      answers {
        id
        isTrue
        text
        explanation
        autoAddNotions
        notions {
          id
          name
        }
        url_image_explanation
        url_image
        createdAt
        updatedAt
      }
      explications
      linkedCours {
        id
        name
        text
        ueCategory {
          id
          name
        }
      }
      sousCategorie {
        id
        name
        name_en
        name_es
        name_it
        name_de
      }
      mcqScale {
        id
        name
      }

      parentQcm {
        UE {
          id
          name
          description
        }
      }
    }
  }
`;

const EditOneQuestionModal = ({ questionId, closeModalHandler, isVisible, index }) => {
  const { t } = useTranslation();
  const { loading, data, error, refetch } = useQuery(QUERY_ONE_QUESTION_ADMIN, {
    fetchPolicy: 'no-cache',
    variables: { id: questionId },
    skip: !questionId
  });
  const question = data?.question;
  return (
    <QuestionContextProvider>
      {question && (
        <CreateEditQuestionModal
          closeModalHandler={async () => {
            closeModalHandler();
          }}
          questionNumber={question?.id_question}
          isVisible={isVisible}
          modalType="UPDATE"
          refetch={refetch}
          loading={false}
          question={question}
          UE={question?.parentQcm?.UE}
        />
      )}
    </QuestionContextProvider>
  );
};

export const ApercuQuestionCorrection = memo(
  ({
    questionNumber = null,
    question,
    shouldRefetch,
    highlightAnswerId = null,
    closeModalHandler = null,
    showCorrectionLink = false
  }) => {
    const { t } = useTranslation();
    const { loading, error, data, refetch } = useQuery(QUERY_APERCU_QUESTION, {
      fetchPolicy: 'no-cache',
      variables: { id: question.id_question },
      skip: !question
    });

    const [editQuestionModalVisible, setEditQuestionModalVisible] = useState(false);

    const questionData = data && data.question;
    const launchMathJax = useMathJaxScript();
    useEffect(() => {
      if (questionData) {
        launchMathJax(); // Load Mathjax seulement quand les questions sont loaded
      }
    }, [questionData]);

    useEffect(() => {
      if (shouldRefetch) {
        refetch();
      }
    }, [shouldRefetch]);

    const questionIndex =
      questionData &&
      ((questionNumber && questionNumber - 1) ||
        (questionData.questionNumberInMcq && questionData.questionNumberInMcq - 1));
    const canEditQuestion = isAdmin() || isTuteur();

    if (loading) return <SpinnerCentered />;
    return (
      <React.Fragment>
        <h3>
          {t('general.Question')} {questionIndex + 1}
        </h3>

        <div style={{ display: 'flex', gap: '5px', flexWrap: 'wrap' }}>
          {canEditQuestion && (
            <Button
              size="small"
              style={{ marginBottom: 10 }}
              onClick={() => {
                setEditQuestionModalVisible(true);
              }}
            >
              {t('EditQuestion')}
            </Button>
          )}

          {question?.parentQcm?.id_qcm && (
            <Link to={`/qcm/correction/${question?.parentQcm?.id_qcm}`}>
              <Button style={{ height: 'auto' }} size="small">
                {t('ExerciseSerieCorrection')}
              </Button>
            </Link>
          )}
        </div>

        {canEditQuestion && (
          <EditOneQuestionModal
            index={questionIndex}
            isVisible={editQuestionModalVisible}
            questionId={questionData?.id_question}
            closeModalHandler={() => {
              setEditQuestionModalVisible(false);
              refetch();
            }}
          />
        )}

        {questionData && (
          <CorrectionQuestionWithAnswers
            index={questionIndex}
            question={questionData}
            hideComments={false}
            highlightAnswerId={highlightAnswerId}
          />
        )}
      </React.Fragment>
    );
  }
);
