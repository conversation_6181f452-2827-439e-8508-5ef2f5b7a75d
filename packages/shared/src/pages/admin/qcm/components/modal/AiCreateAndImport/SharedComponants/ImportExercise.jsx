import { useTranslation } from 'react-i18next';
import { useAiCreateAndImportContext } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';
import { useMutation } from '@apollo/client';
import { MUTATION_LAUNCH_AI_EXERCISE_IMPORT } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/AiExercisePrompts';
import { onErrorShowErrorsFunction, uuidv4 } from '@/shared/utils/utils';
import { Button, message } from 'antd';
import AiConfigSelecter from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/SharedComponants/AiConfigSelecter';
import PixSelecter from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/SharedComponants/PixSelecter';
import React from 'react';
import ImportCoursesSelecter from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/ImportExercisesComponants/ImportCoursesSelecter';
import DataInput from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/SharedComponants/DataInput';
import { DATA_INPUT_TYPE } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';
import UploadPdf from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/SharedComponants/UploadPdf';
import InputText from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/SharedComponants/InputText';
import UploadImages from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/SharedComponants/UploadImages';
import WarningPdfCrypted from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/SharedComponants/WarningPdfCrypted';
import CustomPromptComponant from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/SharedComponants/CustomPromptComponant';
import ExerciseTagSelection from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/SharedComponants/ExerciseTagSelection';
import EnableAmelioration from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/ImportExercisesComponants/EnableAmelioration';
import NumberOfQuestions from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/ImportExercisesComponants/NumberOfQuestions';

const ImportExercise = ({
  qcmDefaultQuestionType,
  frontendTokenForSubscription,
  setFrontendTokenForSubscription,
  wrapperStopGptIfLoading,
  createBlankStatesRework,
  setShouldSubscribe,
  setIsSubscriptionLoading,
  setStep
}) => {
  const { t } = useTranslation();
  const {
    dataType,
    processData,
    modelId,
    modelConfig,
    pix,
    questionsToGenerate,
    customPrompt,
    coursIdArray,
    exerciceTag,
    verifyPdfInput,
    verifyTextInput,
    verifyImageInput,
    exempleExerciseId,
    correctionImprovement
  } = useAiCreateAndImportContext();

  const [launchExerciseImportation] = useMutation(MUTATION_LAUNCH_AI_EXERCISE_IMPORT, {
    onError: (errors) => {
      setIsSubscriptionLoading(false);
      onErrorShowErrorsFunction(errors, 0);
    }
  });

  const wrapperLaunchImportation = async () => {
    // Fonction qui wrap le lancement de la création de question

    /////// Avant la vérification de la querie
    if (frontendTokenForSubscription) {
      await wrapperStopGptIfLoading();
    }

    /////// Block de Vérification des inputs
    // Data
    if (verifyImageInput() === false) {
      message.error(`${t('MathpixIntegration.ErrorIfMultiplesDataWeNeedAllPicturesType')}`);
      return null;
    }

    if (verifyTextInput() === false) {
      message.error(`${t('AiQuestionCreationModal.ErrorInputTextIsEmpty')}`);
      return null;
    }

    //
    if (verifyPdfInput() === false) {
      message.error(`${t('AiQuestionCreationModal.ErrorCannotReadPdf')}`);
      return null;
    }

    // models
    if (!pix) {
      message.error(`${t('MathpixIntegration.ErrorMessageNoOcrConfigId')}`);
      return null;
    }

    // Configuration de modèle (notamment les authorisations)
    if (!modelConfig) {
      message.error(`${t('AiQuestionCreationModal.ErrorMessageNeedConfigForGpt')}`);
      return null;
    }

    // Modèle : notamment si o1, 4o, etc...
    if (!modelId) {
      message.error(`${t('AiQuestionCreationModal.ErrorMessageNeedModelForGpt')}`);
      return null;
    }

    // Cours associés :
    if (coursIdArray.length === 0) {
      message.error(`${t('AiQuestionCreationModal.ErrorNeedLinkWithCourses')}`);
      return null;
    }

    // Créé dans le parent le nombre de blankStates
    createBlankStatesRework(1);

    //
    const frontendToken = uuidv4();
    setFrontendTokenForSubscription(frontendToken);
    setShouldSubscribe(true);

    const input = {
      ...processData(),
      qcmConfigModelId: modelId,
      qcmConfigConfigId: modelConfig,

      pixId: pix,
      numberOfQuestions: questionsToGenerate,
      customPrompt,
      //exerciseFormat, pas pour l'import

      coursIdArray,
      exerciceTag,

      exempleExerciseId,
      correctionImprovement
    };

    // Changement de step
    setStep((prevState) => prevState + 1);
    setIsSubscriptionLoading(true);

    await launchExerciseImportation({ variables: { frontendToken, input } });
  };

  return (
    <>
      <div style={{ display: 'flex', justifyContent: 'space-around', marginBottom: '10px' }}>
        <AiConfigSelecter />
        <PixSelecter />
      </div>

      <ImportCoursesSelecter />

      <DataInput showCours={false} />

      <br />

      {dataType === DATA_INPUT_TYPE.PDF && <UploadPdf />}

      {dataType === DATA_INPUT_TYPE.TEXT && <InputText />}

      {dataType === DATA_INPUT_TYPE.PICTURE && <UploadImages />}

      <WarningPdfCrypted />
      <CustomPromptComponant />
      <br />

      <span
        style={{
          fontSize: '18px',
          fontWeight: 'bold'
        }}
      >
        {t('AiQuestionCreationModal.QuestionParameterSelectionTitle')}
      </span>
      <ExerciseTagSelection qcmDefaultQuestionType={qcmDefaultQuestionType} />
      <EnableAmelioration />
      <div style={{ marginTop: '30px', display: 'flex', flexDirection: 'column', gap: '5px' }}>
        <span
          style={{
            fontSize: '18px',
            fontWeight: 'bold'
          }}
        >
          {t('AiQuestionCreationModal.NumberOfQuestionToGenerateTitle')}
        </span>
        <NumberOfQuestions />
      </div>
      <div style={{ display: 'flex', justifyContent: 'center', marginTop: '10px' }}>
        <Button
          type={'primary'}
          onClick={async () => {
            await wrapperLaunchImportation();
          }}
        >
          {t('AiQuestionCreationModal.LaunchQuestionImportationButtonLabel')}
        </Button>
      </div>
    </>
  );
};

export default ImportExercise;
