import { MUTATION_CREATE_QUESTION_TYPE, MUTATION_UPDATE_TYPE_QUESTION } from '@/shared/graphql/qcm.js'
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js'
import { gql } from '@apollo/client/core'
import { Button, Form, Input, message, Modal, Space } from 'antd'
import { useMutation, useQuery } from '@apollo/client'
import React, { useState } from 'react'
import { SmallErrorsAlert } from '@/shared/components/ErrorResult'
import { useTranslation } from 'react-i18next';

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
}

const GET_TYPE_QUESTION_BY_ID = gql`
    query typeQuestion($id: ID!) {
        typeQuestion(id: $id) {
            id
            name
            image
            keywords {
                id
                name
            }
            cours {
                id
                name
                text
            }
            questions {
                id_question
                question
                isCheckbox
            }
        }
    }
`

const getMutationFromModalType = modalType => {
  switch (modalType) {
    case ModalType.CREATE:
      return MUTATION_CREATE_QUESTION_TYPE
    case ModalType.UPDATE:
      return MUTATION_UPDATE_TYPE_QUESTION
    default:
      return MUTATION_CREATE_QUESTION_TYPE
  }
}

export const CreateEditTypeQuestionModal = ({ closeModalHandler, modalType, isVisible, id }) => {
  const {t} = useTranslation();
  const { data: dataNotion, refetch } = useQuery(GET_TYPE_QUESTION_BY_ID, {
    fetchPolicy: 'no-cache',
    variables: { id },
    skip: !id,
  })
  const typeQuestion = dataNotion && dataNotion.typeQuestion

  const [form] = Form.useForm()
  const [Mutation, { loading, data, error }] = useMutation(getMutationFromModalType(modalType))
  const [shouldCloseModalAfterSubmit, setShouldCloseModalAfterSubmit] = useState(true)
  const [fileImageContent, setFileImageContent] = useState(null) // null = indéfini, 'delete' si supprimer

  const isLoading = loading

  const handleFinish = async data => {
    try {
      let typeQuestion
      if (fileImageContent) {
        typeQuestion = { image: fileImageContent }
      }
      if (modalType === ModalType.UPDATE) {
        typeQuestion = { ...typeQuestion, name: data.name }
        await Mutation({ variables: { id, typeQuestion } })
        message.success(t('Updated'));
        refetch()
      } else { // Create
        typeQuestion = { ...typeQuestion, name: data.name }
        await Mutation({ variables: { typeQuestion } })
        message.success(t('Created'));
      }
      form.resetFields()
      setFileImageContent(null)
      if (shouldCloseModalAfterSubmit) {
        await closeModalHandler()
      }
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e)
      console.error(e)
    }
  }

  React.useEffect(() => {
    if (typeQuestion) {
      form.setFieldsValue({
        name: typeQuestion.name,
      })
    }
  }, [form, typeQuestion])

  const canShowModal = typeQuestion || modalType === ModalType.CREATE
  return (
    <>
      {canShowModal && (
        <Modal
          title={modalType === ModalType.UPDATE ? `${t('Edit')} ${typeQuestion.name}` : t('Create')}
          open={isVisible}
          onCancel={closeModalHandler}
          footer={null}
          closable
          confirmLoading={false}
          destroyOnClose
        >
          {/* Show small error(s) if needed */}
          <SmallErrorsAlert error={error} loading={loading}/>
          <Form
            layout="vertical"
            onFinish={handleFinish}
            form={form}
            initialValues={{
              name: typeQuestion?.name,
            }}
          >
            <Form.Item
              name="name"
              label="Nom du tag de question"
              rules={[
                { required: true, message: `` },
              ]}
            >
              <Input defaultValue={typeQuestion?.name} type="text" placeholder="Nom"/>
            </Form.Item>

            {/*
            <ExoFormImage
              name="image"
              beforeUpload={(file) => setFileImageContent(file)}
              onDelete={() => setFileImageContent({ shouldDelete: true })}
              label="Image de la typeQuestion"
              defaultValue={typeQuestion?.image}
              loading={loading}
            />
            */}


            <Form.Item>
              {modalType === ModalType.UPDATE &&
              <>
                <Button onClick={() => form.submit()} type="primary" loading={isLoading}>
                  {t('Update')}
                </Button>
              </>
              }
              {modalType === ModalType.CREATE &&
              <Space>
                <Button onClick={() => {
                  setShouldCloseModalAfterSubmit(true)
                  form.submit()
                }} type="primary" loading={loading}>
                  {t('CreateAndClose')}
                </Button>
                <Button onClick={() => {
                  setShouldCloseModalAfterSubmit(false)
                  form.submit()
                }} loading={loading}>
                  {t('CreateAndAddAnother')}
                </Button>
              </Space>
              }
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  )
}
