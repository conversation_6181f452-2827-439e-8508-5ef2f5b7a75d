import ExoQuillEditor from '@/shared/components/ExoQuill/ExoQuillEditor';
import { ExoQuillToolbarPresets } from '@/shared/components/ExoQuill/utils';
import ExoFormImage from '@/shared/components/Forms/ExoFormImage.jsx';
import { NotionTag } from '@/shared/components/Notion/NotionTag.jsx';
import { MUT_AUTO_SET_NOTIONS_TO_QUESTION_ANSWER_FROM_INPUT } from '@/shared/graphql/notions.js';
import { MUTATION_UPDATE_IMAGE_QCM } from '@/shared/graphql/qcm.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import {
  EditNotionsLink,
  NotionTarget
} from '@/shared/pages/admin/notions/components/EditNotions.jsx';
import { equationEditorLink } from '@/shared/pages/admin/qcm/components/modal/components/EditAnswersQuestion.jsx';
import { QuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal.jsx';
import { ExerciseEditionContext } from '@/shared/pages/admin/qcm/context/ExerciseEditionContext.jsx';
import { QuestionContext } from '@/shared/pages/admin/qcm/context/QuestionContext.jsx';
import { getLanguageName, tr } from '@/shared/services/translate.js';
import {
  alphabetAndNumbersArray,
  alphabetArray,
  numericalArrayForCombinations,
  showGqlErrorsInMessagePopupFromException,
  tryParseJSONObject
} from '@/shared/utils/utils.js';
import {
  CalculatorOutlined,
  DeleteOutlined,
  EditTwoTone,
  FunctionOutlined,
  QuestionCircleTwoTone
} from '@ant-design/icons';
import { useMutation } from '@apollo/client';
import {
  Button,
  Checkbox,
  Collapse,
  ConfigProvider,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Popover,
  Radio,
  Select,
  Space,
  Tabs,
  Tooltip
} from 'antd';
import { debounce } from 'lodash';
import React, {
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useState
} from 'react';
import { AiEnhancementMenu } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/AiEnhancementMenu';
import { enhancementType } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/sharedConstantesFiles';

import { useTranslation } from 'react-i18next';
import {
  overrideDataFonctionOnePropositionFormater,
  OverrideTitleSetter
} from '@/shared/services/AiEnhancementMenu';
import ImageToLatex from '@/shared/pages/admin/qcm/components/modal/components/ImageToLatex/ImageToLatex';

const { Panel } = Collapse;

export const EditQuestionAnswer = forwardRef(
  (
    {
      answer,
      n,
      onDelete,
      onEdit,
      refetch,
      isPreviewActivated = true,
      autoUpdateAnswer = false,
      questionId,
      aptoriaAnswerType,
      answersCollapsed = false,
      enabledAiEnhancementMenu = false
    },
    ref
  ) => {
    const { t, i18n } = useTranslation();
    const { enabledLanguages, appearance } = useContext(GlobalContext);
    const primaryColor = appearance?.primaryColor;

    const { setHasUnsavedChanges } = useContext(QuestionContext);
    const { answerValidationArray, setAnswerValidationArray } = useContext(ExerciseEditionContext);
    const answerLetter = alphabetArray[n];
    const [form] = Form.useForm();

    // Fonction qui permet de reload le form, notament quand je l'update depuis le AiEnhancementMenu
    function reloadForm() {
      form.resetFields();
    }

    const [isNotionModalVisible, setNotionModalVisible] = useState(false);
    const [formValuesForPreview, setFormValuesForPreview] = useState(answer);
    const [explanationWarning, setExplanationWarning] = useState(null);
    const [editorKey, setEditorKey] = useState(0);
    const [isAllPointsOrNothing, setIsAllPointsOrNothing] = useState(
      answer?.isAllPointsOrNothing || false
    );
    const [autoSetNotionsToQuestionAnswerFromInput] = useMutation(
      MUT_AUTO_SET_NOTIONS_TO_QUESTION_ANSWER_FROM_INPUT
    );
    const [updateImageQcmMutation, { loading: loadingUpload }] =
      useMutation(MUTATION_UPDATE_IMAGE_QCM);

    const [selectedAlphanumOptions, setSelectedAlphanumOptions] = useState(
      ([QuestionAnswerType.ALPHANUMERICAL, QuestionAnswerType.NUMERICAL].includes(
        aptoriaAnswerType
      ) &&
        tryParseJSONObject(answer?.text)) ||
        []
    );

    const isChoiceInList =
      aptoriaAnswerType === QuestionAnswerType.UNIQUE_CHOICE_IN_LIST ||
      aptoriaAnswerType === QuestionAnswerType.MULTIPLE_CHOICE_IN_LIST;

    useEffect(() => {
      if (autoUpdateAnswer) {
        form.submit();
      }
    }, [autoUpdateAnswer]);

    useImperativeHandle(ref, () => ({
      async sendForm() {
        form.submit();
      }
    }));

    const notionButtonModal = (
      <>
        <Button size="small" icon={<EditTwoTone />} onClick={() => setNotionModalVisible(true)} />
        {isNotionModalVisible && (
          <Modal
            title={`Éditer les notions de la réponse ${answerLetter}`}
            open={isNotionModalVisible}
            destroyOnClose
            onCancel={() => {
              setNotionModalVisible(false);
              refetch();
            }}
            footer={null}
            closable
            width={700}
          >
            <EditNotionsLink type={NotionTarget.ANSWER} typeId={answer.id} />
          </Modal>
        )}
      </>
    );

    const extractNotionsEffect = () => {
      const extractNotions = async () => {
        // Remplacer les notions de cet item par celles détectées
        try {
          await autoSetNotionsToQuestionAnswerFromInput({
            variables: {
              input: formValuesForPreview.text,
              answerId: answer.id
            }
          });
          refetch();
        } catch (e) {
          console.error(e);
          showGqlErrorsInMessagePopupFromException(e);
        }
        // Soit ajouter la (les) notions
      };
      if (answer?.autoAddNotions) {
        extractNotions();
      }
    };

    useEffect(extractNotionsEffect, []); // Extract notions on load
    useEffect(extractNotionsEffect, [formValuesForPreview.text]); // Extract notions when text form value change

    const debouncedSave = useCallback(
      debounce((nextValue) => setFormValuesForPreview(nextValue), 120),
      [] // will be created only once initially
    );

    const onFinish = async (answer) => {
      try {
        if (
          [QuestionAnswerType.ALPHANUMERICAL, QuestionAnswerType.NUMERICAL].includes(
            aptoriaAnswerType
          )
        ) {
          // eslint-disable-next-line no-param-reassign
          answer.text = JSON.stringify(selectedAlphanumOptions);
        }
        // eslint-disable-next-line no-param-reassign
        answer.isAllPointsOrNothing = isAllPointsOrNothing;
        await onEdit(answer, answerLetter);
        setHasUnsavedChanges(false);
      } catch (e) {
        console.error(e);
      }
    };

    const deleteButton = (
      <Popconfirm
        title={t('SureOfDeletion')}
        onConfirm={() => onDelete(answer)}
        okText={t('general.yes')}
        cancelText={t('general.no')}
      >
        <Button size="small" type="text" icon={<DeleteOutlined />} danger>
          Supprimer cette réponse{' '}
        </Button>
      </Popconfirm>
    );

    const notionsAssociees = (
      <Form.Item noStyle>
        {answer.notions && answer.notions.map((n) => <NotionTag key={n.id} notion={n} />)}
        {!answer?.autoAddNotions && notionButtonModal}
      </Form.Item>
    );

    const getCollapsedInitialState = () => {
      if ([QuestionAnswerType.FREE_TEXT].includes(aptoriaAnswerType)) {
        return false;
      }
      return answersCollapsed;
    };
    const [isCollapsed, setIsCollapsed] = useState(getCollapsedInitialState);
    useEffect(() => {
      setIsCollapsed(getCollapsedInitialState());
    }, [answersCollapsed]);

    const localOverrideDataFonctionOnePorpositionFormater = useCallback(
      (lang = null, triggerUpdate = null) => {
        let language;
        if (lang === null) {
          language = enabledLanguages?.[0];
        } else {
          language = lang;
        }
        const value = overrideDataFonctionOnePropositionFormater(
          form,
          setFormValuesForPreview,
          'text',
          'explanation',
          language,
          answer?.id,
          formValuesForPreview?.isTrue,
          triggerUpdate
        );
        return value;
      },
      [form, formValuesForPreview, enabledLanguages, answer]
    );

    if (!answer) return null;

    return (
      <ConfigProvider
        theme={{
          components: {
            Collapse: {
              colorFillAlter: primaryColor,
              colorBorder: primaryColor,
              colorTextHeading: '#ffffff'
            }
          }
        }}
      >
        <Collapse
          accordion
          activeKey={isCollapsed ? null : answer.id}
          onChange={(key) => setIsCollapsed(!isCollapsed)}
          collapsible={'icon'}
        >
          <Panel
            //style={{ background: primaryColor, color: '#ffffff' }}
            key={answer?.id}
            header={
              <div
                style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                  size: 'small'
                }}
                onClick={(key) => setIsCollapsed(!isCollapsed)}
              >
                <span style={{ fontWeight: 'bold' }}>Réponse {answerLetter}</span>
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                ></div>
              </div>
            }
          >
            <Form
              requiredMark={false}
              form={form}
              initialValues={answer}
              onFieldsChange={(changedFields, allFields) => {
                setHasUnsavedChanges(true);
                console.log('on fields change');
                if (changedFields?.[0]?.name?.[0] === 'isTrue') {
                  // Sélectionne vrai ou faux, donc on valide pour answer actuelle
                  let currentValidation = answerValidationArray.find((o) => o.id === answer.id);
                  currentValidation.valid = true;
                  // Update
                  setAnswerValidationArray([
                    ...answerValidationArray.filter((o) => o.id !== answer.id),
                    currentValidation
                  ]);
                }
              }}
              onValuesChange={async (changedValues, allValues) => {
                //setFormValues({ ...formValues, ...values }); // real form values
                console.log({ changedValues, allValues });
                debouncedSave(allValues);
              }}
              onFinishFailed={({ values, errorFields, outOfDate }) => {
                errorFields.map((e) => {
                  return e.errors.map((msg) => {
                    return message.error(`Réponse ${answerLetter}: ${msg}`);
                  });
                });
              }}
              scrollToFirstError
              onFinish={onFinish}
              labelAlign="left"
              className="form-edit-answers-question"
            >
              <Form.Item hidden name="id" />

              {/* ALPHANUMERICAL / NUMERICAL INPUT */}
              {[QuestionAnswerType.ALPHANUMERICAL, QuestionAnswerType.NUMERICAL].includes(
                aptoriaAnswerType
              ) && (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    gap: '5px',
                    width: '100%'
                  }}
                >
                  <p>
                    <b>Veuillez cocher la bonne combinaison de réponse(s) : </b>
                  </p>
                  <Select
                    showArrow
                    mode="multiple"
                    style={{ minWidth: '350px', width: '100%' }}
                    placeholder="Sélectionnez combinaison"
                    defaultValue={selectedAlphanumOptions}
                    onChange={(_, options) => {
                      const ids = options?.map((o) => o.key);
                      setSelectedAlphanumOptions(ids);
                    }}
                    options={(aptoriaAnswerType === QuestionAnswerType.ALPHANUMERICAL
                      ? alphabetAndNumbersArray
                      : numericalArrayForCombinations
                    )?.map((character) => ({
                      value: character,
                      key: character
                    }))}
                  />
                </div>
              )}

              {![
                QuestionAnswerType.FREE_TEXT,
                QuestionAnswerType.ALPHANUMERICAL,
                QuestionAnswerType.NUMERICAL
              ].includes(aptoriaAnswerType) && (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    gap: '5px',
                    width: '100%',
                    marginBottom: '30px'
                  }}
                >
                  <div
                    style={{
                      flexBasis: '80%',
                      flexGrow: 1
                    }}
                  >
                    <Tabs defaultActiveKey={i18n.language}>
                      {enabledLanguages &&
                        enabledLanguages?.map((lang) => (
                          <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                            <div
                              style={{
                                width: '100%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'flex-end'
                              }}
                            >
                              {/* La condition est bonne : on est forcément dans un QCU/QCM car pour rentrer ici, on exclu les 'freeText','AlphaNumeric' et 'numeric' */}
                              <AiEnhancementMenu
                                style={{ marginTop: -56, marginBottom: 24 }}
                                secondary
                                componantType={enhancementType.ONE_PROPOSITION}
                                answerId={answer?.id} // Important pour récupérer le context => pour l'amélioration de correction
                                refetch={() => {}} //reloadForm();}}
                                onModalClose={() => {
                                  setEditorKey((ek) => ek + 1);
                                }}
                                getTextFct={() =>
                                  localOverrideDataFonctionOnePorpositionFormater(
                                    lang,
                                    () => {} // Permet d'update éditeur non controllé par sa defaultValue)
                                  )
                                }
                              />
                            </div>
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'flex-start',
                                flexDirection: 'column',
                                gap: 10
                              }}
                            >
                              <div
                                style={{
                                  display: 'flex',
                                  gap: 8,
                                  alignItems: 'center',
                                  justifyContent: 'space-between',
                                  width: '100%',
                                  flexWrap: 'wrap'
                                }}
                              >
                                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                  <div> Énoncé</div>
                                </div>

                                <div
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 8,
                                    flexWrap: 'wrap'
                                  }}
                                >
                                  {notionsAssociees}
                                </div>
                              </div>

                              <div
                                style={{
                                  marginBottom: 24,
                                  alignItems: 'center',
                                  width: '100%',
                                  display: 'flex',
                                  flexDirection: 'row',
                                  gap: '10px',
                                  flexWrap: 'wrap'
                                }}
                              >
                                <div style={{ flexGrow: 4, minWidth: 225 }}>
                                  {/*
                                  <Form.Item
                                    name={tr('text', lang)}
                                    style={{ minHeight: '100px', margin: 0 }}
                                    // rules={[{ required: true, min: 1, message: `Veuillez entrer la réponse` }]}
                                  >
                                    <Input.TextArea
                                      type="textarea"
                                      placeholder=""
                                      rows={4}
                                      value={answer.text}
                                    />
                                  </Form.Item>
                                  */}
                                  <Form.Item
                                    name={tr('text', lang)}
                                    style={{ minHeight: '100px', margin: 0 }}
                                  >
                                    <ExoQuillEditor
                                      key={`answer-text-${editorKey}`}
                                      defaultValue={
                                        form.getFieldValue(tr('text', lang)) ||
                                        answer?.[tr('text', lang)]
                                      }
                                      onChange={(editorData) => {
                                        form.setFieldsValue({
                                          [tr('text', lang)]: editorData
                                        });
                                        // Déclencher éventuellement la validation
                                        form.validateFields([tr('text', lang)]);
                                      }}
                                      modules={ExoQuillToolbarPresets.exerciseEdition}
                                    />
                                  </Form.Item>
                                </div>

                                {/* Image */}
                                <div style={{ flex: 1, minWidth: 80, height: 80 }}>
                                  <ExoFormImage
                                    name={null}
                                    beforeUpload={async (file) => {
                                      try {
                                        await updateImageQcmMutation({
                                          variables: {
                                            id: answer.id,
                                            file,
                                            action: 'upload',
                                            type: 'answer'
                                          }
                                        });
                                        refetch();
                                      } catch (e) {
                                        console.error(e);
                                      }
                                    }}
                                    onDelete={async (file) => {
                                      try {
                                        await updateImageQcmMutation({
                                          variables: {
                                            id: answer.id,
                                            file: null,
                                            action: 'delete',
                                            type: 'answer'
                                          }
                                        });
                                        refetch();
                                      } catch (e) {
                                        console.error(e);
                                      }
                                    }}
                                    label={`Image item ${answerLetter}`}
                                    defaultValue={answer?.url_image}
                                    loading={loadingUpload}
                                    fileType="QCM"
                                  />
                                </div>
                              </div>
                            </div>

                            {/* Correction */}
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'flex-start',
                                flexDirection: 'column',
                                gap: 10
                              }}
                            >
                              <div>Correction</div>
                              <div
                                style={{
                                  marginBottom: 24,
                                  alignItems: 'center',
                                  width: '100%',
                                  display: 'flex',
                                  flexDirection: 'row',
                                  gap: '10px',
                                  flexWrap: 'wrap'
                                }}
                              >
                                {/*
                                <Popover
                                  content={
                                    isPreviewActivated ? (
                                      <ContentWithMathJax
                                        value={formValuesForPreview[tr('explanation', lang)]}
                                      />
                                    ) : (
                                      ''
                                    )
                                  }
                                >
                                  <div style={{ flexGrow: 4, minWidth: 225 }}>
                                    <Form.Item
                                      name={tr('explanation', lang)}
                                      extra={explanationWarning}
                                      style={{ minHeight: '100px', margin: 0 }}
                                    >
                                      <Input.TextArea
                                        type="text"
                                        //style={{ height: '80px' }}
                                        rows={4}
                                        placeholder={t('ExplainWhyTrueFalse')}
                                      />
                                    </Form.Item>
                                  </div>
                                </Popover>
                                */}

                                <div style={{ flexGrow: 4, minWidth: 225 }}>
                                  <Form.Item
                                    name={tr('explanation', lang)}
                                    extra={explanationWarning}
                                  >
                                    <ExoQuillEditor
                                      key={`explanation-${editorKey}`}
                                      defaultValue={
                                        form.getFieldValue(tr('explanation', lang)) ||
                                        answer?.[tr('explanation', lang)]
                                      }
                                      onChange={(editorData) => {
                                        // updat form
                                        form.setFieldsValue({
                                          [tr('explanation', lang)]: editorData
                                        });
                                      }}
                                      modules={ExoQuillToolbarPresets.exerciseEdition}
                                      placeholder={t('ExplainWhyTrueFalse')}
                                    />
                                  </Form.Item>
                                </div>

                                {/* Image correction */}
                                <div style={{ flex: 1, minWidth: 80, height: 80 }}>
                                  <ExoFormImage
                                    name={null}
                                    beforeUpload={async (file) => {
                                      try {
                                        await updateImageQcmMutation({
                                          variables: {
                                            id: answer.id,
                                            file,
                                            action: 'upload',
                                            type: 'answer_explanation'
                                          }
                                        });
                                        refetch();
                                      } catch (e) {
                                        console.error(e);
                                      }
                                    }}
                                    onDelete={async (file) => {
                                      try {
                                        await updateImageQcmMutation({
                                          variables: {
                                            id: answer.id,
                                            file: null,
                                            action: 'delete',
                                            type: 'answer_explanation'
                                          }
                                        });
                                        refetch();
                                      } catch (e) {
                                        console.error(e);
                                      }
                                    }}
                                    label={`Correction ${answerLetter}`}
                                    defaultValue={answer?.url_image_explanation}
                                    loading={loadingUpload}
                                    fileType="QCM"
                                  />
                                </div>
                              </div>
                            </div>

                            {/* End language tab */}
                            {!isChoiceInList &&
                              ![
                                QuestionAnswerType.FREE_TEXT,
                                QuestionAnswerType.ALPHANUMERICAL,
                                QuestionAnswerType.NUMERICAL
                              ].includes(aptoriaAnswerType) && (
                                <div
                                  style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    textAlign: 'center',
                                    gap: '35px',
                                    flexWrap: 'wrap'
                                  }}
                                >
                                  <div>
                                    <Form.Item
                                      noStyle
                                      name="isTrue"
                                      rules={[
                                        {
                                          required: true,
                                          message: 'Vous devez cocher Vrai ou Faux'
                                        }
                                      ]}
                                    >
                                      <Radio.Group defaultValue={answer.isTrue} buttonStyle="solid">
                                        <Radio.Button
                                          style={
                                            formValuesForPreview.isTrue
                                              ? {
                                                  backgroundColor: '#73d13d',
                                                  borderColor: '#73d13d'
                                                }
                                              : {}
                                          }
                                          value
                                        >
                                          {t('general.TRUE')}
                                        </Radio.Button>
                                        <Radio.Button
                                          style={
                                            formValuesForPreview.isTrue === false
                                              ? {
                                                  backgroundColor: '#ff4d4f',
                                                  borderColor: '#ff4d4f'
                                                }
                                              : {}
                                          }
                                          value={false}
                                        >
                                          {t('general.FALSE')}
                                        </Radio.Button>
                                      </Radio.Group>
                                    </Form.Item>
                                  </div>

                                  {!isChoiceInList && (
                                    <div>
                                      <Form.Item
                                        noStyle
                                        name="isHorsConcours"
                                        rules={[{ required: true, message: 'Vous devez cocher' }]}
                                      >
                                        <Radio.Group
                                          defaultValue={answer.isHorsConcours}
                                          buttonStyle="solid"
                                        >
                                          <Radio.Button
                                            style={
                                              formValuesForPreview.isHorsConcours === false
                                                ? {
                                                    backgroundColor: '#73d13d',
                                                    borderColor: '#73d13d'
                                                  }
                                                : {}
                                            }
                                            value={false}
                                          >
                                            AU PROGRAMME
                                          </Radio.Button>
                                          <Radio.Button
                                            style={
                                              formValuesForPreview.isHorsConcours
                                                ? {
                                                    backgroundColor: '#ff4d4f',
                                                    borderColor: '#ff4d4f'
                                                  }
                                                : {}
                                            }
                                            value
                                          >
                                            HORS CONCOURS
                                          </Radio.Button>
                                        </Radio.Group>
                                      </Form.Item>
                                    </div>
                                  )}

                                  <div style={{ fontSize: 18 }}>
                                    {aptoriaAnswerType === QuestionAnswerType.FREE_TEXT ? (
                                      <Space direction="horizontal">
                                        <div>Paramètres de la réponse&nbsp;</div>
                                      </Space>
                                    ) : (
                                      <Space direction="horizontal">
                                        <Checkbox
                                          size="small"
                                          checked={isAllPointsOrNothing}
                                          onClick={() =>
                                            setIsAllPointsOrNothing(!isAllPointsOrNothing)
                                          }
                                          style={{ fontWeight: 400 }}
                                        >
                                          {t('AllOrNothing')}{' '}
                                          <Popover
                                            content="Un item tout ou rien donnera tous les points si coché et vrai, ou passera la note de la question à la note minimale si coché et faux"
                                            title={t('AllOrNothingItem')}
                                            trigger="hover"
                                          >
                                            <QuestionCircleTwoTone />
                                          </Popover>
                                        </Checkbox>
                                      </Space>
                                    )}
                                  </div>
                                </div>
                              )}
                          </Tabs.TabPane>
                        ))}
                    </Tabs>
                  </div>
                </div>
              )}

              {deleteButton}
            </Form>
          </Panel>
        </Collapse>
      </ConfigProvider>
    );
  }
);

EditQuestionAnswer.displayName = 'EditQuestionAnswer';
