import { gql } from '@apollo/client';
import { QUESTION_FRAGMENT } from '@/shared/graphql/qcm';

export const QUERY_GET_PDF_DESCRIPTION_FOR_COURS = gql`
  query GetPdfDescriptionForCours($getPdfDescriptionForCoursId: [ID]!) {
    getPdfDescriptionForCours(id: $getPdfDescriptionForCoursId) {
      fileName
      numberOfPages
      cryptedInReading
      pdfId
    }
  }
`;

export const QUERY_GET_PDF_DESCRIPTION_FOR_UPLOAD = gql`
  query GetPdfDescriptionForUpload($pdfFile: Upload) {
    getPdfDescriptionForUpload(pdfFile: $pdfFile) {
      fileName
      numberOfPages
      cryptedInReading
    }
  }
`;

export const MUTATION_ADD_LINK_QUESTIONS_IDS_WITH_QCM_ID = gql`
  mutation AddLinkQuestionIdWithQcmId($questionId: ID!, $qcmId: ID!) {
    addLinkQuestionIdWithQcmId(questionId: $questionId, qcmId: $qcmId)
  }
`;

export const MUTATION_REMOVE_LINK_QUESTIONS_IDS_WITH_QCM_ID = gql`
  mutation RemoveLinkQuestionIdWithQcmId($questionId: ID!, $qcmId: ID!) {
    removeLinkQuestionIdWithQcmId(questionId: $questionId, qcmId: $qcmId)
  }
`;

export const SUBSCRIPTION_FETCH_QUESTION_ID_FROM_GPT_BACK = gql`
  subscription GenerateAiQuestions($frontendToken: String!) {
    generateAiQuestions(frontendToken: $frontendToken) {
      arrayIndex
      questionId
      eventType
      errorMessage
    }
  }
`;

export const MUTATION_STOP_GPT_GENERATION = gql`
  mutation RemoveGptJob($frontendToken: String!) {
    removeGptJob(frontendToken: $frontendToken)
  }
`;

export const MUTATION_LAUNCH_AI_EXERCISE_CREATION = gql`
  mutation CreateExerciseWithAiV2($frontendToken: String, $input: CreateOrImport) {
    createExerciseWithAiV2(frontendToken: $frontendToken, input: $input)
  }
`;

export const MUTATION_LAUNCH_AI_EXERCISE_IMPORT = gql`
  mutation ImportExerciseWithAiV2($frontendToken: String, $input: CreateOrImport) {
    importExerciseWithAiV2(frontendToken: $frontendToken, input: $input)
  }
`;

export const SEARCH_MULTIPLE_QUESTIONS = gql`
  ${QUESTION_FRAGMENT}
  query queryMultiplesQuestions($id: [ID]!) {
    queryMultiplesQuestions(id: $id) {
      ...CoreQuestionFragment
    }
  }
`;

export const MUTATION_ACCEPT_QUESTION = gql`
  mutation ValidateAiGeneration($id: ID!) {
    validateAiGeneration(id: $id)
  }
`;

export const MUTATION_REJECT_QUESTION = gql`
  mutation RejectAiValidation($id: ID!) {
    rejectAiGeneration(id: $id)
  }
`;
