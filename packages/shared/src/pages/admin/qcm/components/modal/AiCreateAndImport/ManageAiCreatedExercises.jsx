import React, { useMemo, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import {
  ModalType,
  ExerciseEdition
} from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal';
import { ExerciseEditionContextProvider } from '@/shared/pages/admin/qcm/context/ExerciseEditionContext.jsx';
import { Button } from 'antd';
import { useMutation } from '@apollo/client';
import {
  MUTATION_ADD_LINK_QUESTIONS_IDS_WITH_QCM_ID,
  MUTATION_REMOVE_LINK_QUESTIONS_IDS_WITH_QCM_ID
} from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/AiExercisePrompts';
import { QUESTION_AI_CREATION_MODAL_TYPE } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateOrImportExercisesWithAiModal';
import { useTranslation } from 'react-i18next';

const ManageAiCreatedExercises = forwardRef(
  (
    {
      stepNode,
      switchState,
      qcmId,
      setStep,
      modalType,
      setAcceptationState,
      refetchQcm,
      isLastStep,
      closeModalHandler,
      isSubscriptionLoading
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [addLinkQuestionsFunction, { data: addData, loading: addLoading, error: addError }] =
      useMutation(MUTATION_ADD_LINK_QUESTIONS_IDS_WITH_QCM_ID);
    const [
      removeLinkQuestionsFunction,
      { data: removeData, loading: removeLoading, error: removeError }
    ] = useMutation(MUTATION_REMOVE_LINK_QUESTIONS_IDS_WITH_QCM_ID);

    const [componantIsLoading, setComponantIsLoading] = useState(false);

    // Marqueur de si on ne connait pas à l'avance le nombre de questions
    const isTypeUnknownNumberOfQuestions =
      modalType === QUESTION_AI_CREATION_MODAL_TYPE.IMPORT_EXERCICES_FROM_PDF_WITH_AI_IN_SERIE ||
      modalType === QUESTION_AI_CREATION_MODAL_TYPE.IMPORT_EXERCICES_FROM_PDF_WITH_AI_STANDALONE;

    // Marqueur de si l'on a besoin d'ajouter l'exerice à la série
    const needToImportInSerie =
      modalType === QUESTION_AI_CREATION_MODAL_TYPE.IMPORT_EXERCICES_FROM_PDF_WITH_AI_IN_SERIE ||
      modalType === QUESTION_AI_CREATION_MODAL_TYPE.EXERCISE_SERIE;

    // Petit memo qui met à jour le bool 'isLastStepAndLoading' qui permet de savoir si, quand on ne connait pas le nombre total de question, si on est le dernier affiché
    const isLastStepAndLoading = useMemo(
      () => isLastStep && isTypeUnknownNumberOfQuestions && isSubscriptionLoading,
      [isLastStep, isSubscriptionLoading]
    );

    const staticRef = useRef();
    const questionId = stepNode?.id;
    const step = stepNode?.step;

    const saveQuestion = async () => {
      /* Fonction qui save la question. Returne true si la fonction s'est bien déroulée, false sinon */
      try {
        const doesQuestionNeedSave = staticRef?.current?.getSaveState();
        setComponantIsLoading(true);

        // Si la question a besoin d'être save. On récupère le résultat. Si true on save. Si not false => exception , si false alors on continue la fonction
        if (doesQuestionNeedSave) {
          // On save et on récup la réponse. Si false ou not true, alors on lève une erreur. Si true, on continue la fonction
          const questionSave = await handleMakeSave();
          if (questionSave === false) {
            throw new Error('question need saving but saving failed');
          } else if (questionSave !== true) {
            throw new Error('question need saving, but handled returned invalid value');
          }
        } else if (doesQuestionNeedSave !== false) {
          throw new Error('doesQuestionNeedSave is not bool');
        }

        setComponantIsLoading(false);
        return true;
      } catch (e) {
        setComponantIsLoading(false);
        console.error("Question couldn't be saved : ", e);
        return false;
      }
    };

    const handleNextAccept = async () => {
      try {
        // Fonction qui s'assure en fonction du modalType d'ajouter la question à la série + de la valider
        setComponantIsLoading(true);

        // Save de la question :
        const saveQuestionResult = await saveQuestion();
        if (saveQuestionResult === false) {
          setComponantIsLoading(false);
          return null;
        }

        // Création des promise de modif en BDD
        const promiseArray = [];

        // Acceptation de la question en BDD
        const promiseAccept = staticRef?.current?.handleModifyState('ACCEPTATION');
        promiseArray.push(promiseAccept);

        // Lier la question à la série
        if (needToImportInSerie) {
          const promiseAddToSerie = addLinkQuestionsFunction({ variables: { qcmId, questionId } });
          promiseArray.push(promiseAddToSerie);
        }

        // Ensuite on attends les promises
        const results = await Promise.all(promiseArray);

        // on change l'état du step
        const hasStateUpdated = results[0]?.data?.validateAiGeneration; // Si l'update s'est bien passé
        if (hasStateUpdated) {
          setAcceptationState(step, true);
        }

        // fin de loading
        setComponantIsLoading(false);

        // Si tout est validé, alors on change
        if (isLastStep === true) {
          closeModalHandler();
        } else if (isLastStep === false) {
          setStep((prevState) => prevState + 1);
        }

        // refetch du qcm si besoin
        if (needToImportInSerie) {
          refetchQcm();
        }
      } catch (e) {
        setComponantIsLoading(false);
      }
    };

    const handleNextReject = async () => {
      try {
        // Fonction qui s'assure en fonction du modalType d'ajouter la question à la série + de la valider
        setComponantIsLoading(true);

        const saveQuestionResult = await saveQuestion();
        if (saveQuestionResult === false) {
          setComponantIsLoading(false);
          return null;
        }

        // Création des promise de modif en BDD
        const promiseArray = [];

        // Acceptation de la question en BDD
        const promiseAccept = staticRef?.current?.handleModifyState('REJECTION');
        promiseArray.push(promiseAccept);

        if (needToImportInSerie) {
          const promiseAddToSerie = removeLinkQuestionsFunction({
            variables: { qcmId, questionId }
          });
          promiseArray.push(promiseAddToSerie);
        }

        // Ensuite on attends les promises
        const results = await Promise.all(promiseArray);

        // on change l'état du step
        const hasStateUpdated = results[0]?.data?.rejectAiGeneration;
        if (hasStateUpdated) {
          setAcceptationState(step, false);
        }

        // fin de loading
        setComponantIsLoading(false);

        // Si tout est validé, alors on change
        if (isLastStep === true) {
          closeModalHandler();
        } else if (isLastStep === false) {
          setStep((prevState) => prevState + 1);
        }

        // refetch du qcm si besoin
        if (needToImportInSerie) {
          refetchQcm();
        }
      } catch (e) {
        setComponantIsLoading(false);
      }
    };

    const previous = async () => {
      try {
        // Fonction qui s'assure en fonction du modalType d'ajouter la question à la série + de la valider
        setComponantIsLoading(true);

        ///// Validation de la question
        // Check si la question a besoin d'être sauvegardée
        const doesQuestionNeedSave = staticRef?.current?.getSaveState();

        // si oui, alors on save
        if (doesQuestionNeedSave) {
          const questionSave = await handleMakeSave();
          if (!questionSave) {
            setComponantIsLoading(false);
            return null;
          }
        }

        setComponantIsLoading(false);
        setStep((prevState) => prevState - 1);
      } catch (e) {
        setComponantIsLoading(false);
      }
    };

    const handleMakeSave = async () => {
      if (staticRef.current) {
        return await staticRef?.current?.saveExercise();
      }
    };

    // Block d'imperativeHandle afin d'avoir un contrôle sur ce componant depuis un componant parent
    useImperativeHandle(ref, () => ({
      // Fonction qui permet de savoir si il reste des éléments non sauvegardés ou pas.
      saveQuestion: async () => saveQuestion()
    }));

    const navigationBar = (
      <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
        <div style={{ width: '80%', display: 'flex' }}>
          <div style={{ width: '70%' }}>
            <Button
              loading={componantIsLoading}
              disabled={componantIsLoading}
              onClick={previous}
              type={'primary'}
            >
              {t('Previous')}
            </Button>
          </div>
          <div style={{ textAlign: 'center', width: '15%', minWidth: '150px' }}>
            <Button
              loading={componantIsLoading || isLastStepAndLoading}
              disabled={componantIsLoading || isLastStepAndLoading}
              onClick={handleNextReject}
              type={'primary'}
              style={
                isLastStepAndLoading
                  ? { backgroundColor: '#a9a9a9', borderColor: '#a9a9a9', color: 'white' }
                  : { backgroundColor: 'red', borderColor: 'red', color: 'white' }
              }
            >
              {isLastStepAndLoading && t('MathpixIntegration.ButtonLabelNextButLoadingSoDisabled')}
              {isLastStep &&
                !isSubscriptionLoading &&
                t('MathpixIntegration.ButtonLabelFinishAndRejectExercise')}
              {!isLastStep &&
                !isLastStepAndLoading &&
                t('AiQuestionCreationModal.NextAndRejectQuestion')}
            </Button>
          </div>
          <div style={{ textAlign: 'center', width: '15%', minWidth: '150px' }}>
            <Button
              loading={componantIsLoading || isLastStepAndLoading}
              disabled={componantIsLoading || isLastStepAndLoading}
              onClick={handleNextAccept}
              type="primary"
              style={
                isLastStepAndLoading
                  ? { backgroundColor: '#a9a9a9', borderColor: '#a9a9a9', color: 'white' }
                  : { backgroundColor: '#87D143', borderColor: '#87D143', color: 'white' }
              }
            >
              {isLastStepAndLoading && t('MathpixIntegration.ButtonLabelNextButLoadingSoDisabled')}
              {isLastStep &&
                !isSubscriptionLoading &&
                t('MathpixIntegration.ButtonLabelFinishAndAcceptExercise')}
              {!isLastStep &&
                !isLastStepAndLoading &&
                t('AiQuestionCreationModal.NextAndAcceptQuestion')}
            </Button>
          </div>
        </div>
      </div>
    );

    return (
      <>
        {navigationBar}
        <span
          style={{
            display: 'flex',
            fontSize: '20px',
            fontWeight: 'bold',
            width: '100%',
            justifyContent: 'center'
          }}
        >
          {stepNode?.title}
        </span>
        {questionId && (
          <ExerciseEditionContextProvider>
            <ExerciseEdition
              closeModalHandler={() => {}}
              modalType={ModalType.UPDATE}
              isVisible={true}
              qcmId={qcmId}
              question={{ id_question: questionId }}
              key={`key_question_id_${questionId}`}
              withoutDrawer={true}
              ref={staticRef}
              componantType={'questionEditionAiCreationComponant'}
            />
          </ExerciseEditionContextProvider>
        )}
        {/* On affiche la seconde navigation bar juste si on a  */}
        {questionId && navigationBar}
      </>
    );
  }
);

export { ManageAiCreatedExercises };
