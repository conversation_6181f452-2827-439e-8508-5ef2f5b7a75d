import { useTranslation } from 'react-i18next';
import React, { useEffect, useState } from 'react';
import { Dropdown, Button, Modal } from 'antd';
import { AiEnhancementGptPrompt } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/AiEnhancementMenuGptPrompt';
import { WandFilled } from '@/shared/assets/customIcons/WandFilled';
import { motion } from 'framer-motion/dist/framer-motion';
import { WrapperAmeliorationComponant } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/WrapperAmeliorationComponant';
import { ConfigSelecter } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/ConfigSelecter';
import {
  enhancementType,
  whatAuthorized,
  whatContextAuthorized
} from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/sharedConstantesFiles';

export const AiEnhancementMenu = ({
  componantType,
  questionId,
  formationElementTextId,
  answerId,
  refetch,
  secondary = false,
  style,
  getTextFct,
  onModalClose = null
}) => {
  /* Ce componant est lié à :
  - une questionId
  - formationElementTextId
  - answerId
  - enhancementType
  - getDataFct => Fonction qui permet de récupérer les données à envoyer à chatGPT. Comme il s'agit une wrapper d'une implémentation modulaire, doit suivre le format suivant :
     -> Si Answer, doit être séparé en l'énoncé et l'explication et avec le format suivant :
        [{
          text:String
          id:String
          uniqueId:`${string}_énonce`
          mutationType:whatAuthorized.ONE_ENONCE,
          additionalInputJson:{isTrue:Boolea}
        },{
          text:String
          id:String
          uniqueId:`${string}_justification`
          mutationType:whatAuthorized.ONE_EXPLANATION
          additionalInputJson:{isTrue:Boolean}
        }]

    -> Si Title, doit être du format suivant :
      [{
        text:String
        id:String
        uniqueId:String
      }]

  L'idée est de en fonction du type (enhancement type), definir une key 'enhancementType' dans dropdownComposition qui permet de
  customiser de façon modulaire le menu déroulant.

  */
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);

  ///////////////////////////////////////
  // DEBUT ORGANISATION DU MENU
  ///////////////////////////////////////

  const [callWrapper, setCallWrapper] = useState(null);

  // les options du menu contextuel et défini l'action qui va s'effectuer
  const dropdownKeys = {
    FIX_SPELLING_AND_GRAMMAR_TITLE: {
      label: t('AiEnhancementModal.FixSpellingAndGrammar'),
      key: 1,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.EXERCISE_TITLE,
          chatGptPromptObject: AiEnhancementGptPrompt.CORRECT_MULTIPLES_TEXTS,
          id: questionId,
          additionalArgs: {
            initWhatContext:
              whatContextAuthorized.QUESTION_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION
          }
        });
      }
    },
    FIX_SPELLING_AND_GRAMMAR_FORMATION_ELEMENT: {
      label: t('AiEnhancementModal.FixSpellingAndGrammar'),
      key: 2,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.FE_TEXT,
          chatGptPromptObject: AiEnhancementGptPrompt.CORRECT_MULTIPLES_TEXTS,
          id: formationElementTextId,
          additionalArgs: {
            isRichText: true
          }
        });
      }
    },
    FIX_SPELLING_AND_GRAMMAR_ALL_ANSWERS: {
      label: t('AiEnhancementModal.FixSpellingAndGrammar'),
      key: 3,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.ALL_ANSWERS,
          id: questionId,
          chatGptPromptObject: AiEnhancementGptPrompt.CORRECT_MULTIPLES_TEXTS,
          additionalArgs: {
            initWhatContext:
              whatContextAuthorized.QUESTION_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION
          }
        });
      }
    },
    FIX_SPELLING_AND_GRAMMAR_ONE_ANSWER: {
      label: t('AiEnhancementModal.FixSpellingAndGrammar'),
      key: 4,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.ONE_ANSWER,
          chatGptPromptObject: AiEnhancementGptPrompt.CORRECT_MULTIPLES_TEXTS,
          id: answerId,
          additionalArgs: {
            initWhatContext:
              whatContextAuthorized.ANSWER_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION
          }
        });
      }
    },
    REFORMULATE_TITLE: {
      label: t('AiEnhancementModal.ReformulateText'),
      key: 5,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.EXERCISE_TITLE,
          chatGptPromptObject: AiEnhancementGptPrompt.REFORMULATE_MULTIPLES_TEXTS,
          id: questionId,
          additionalArgs: {
            initWhatContext:
              whatContextAuthorized.QUESTION_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION
          }
        });
      }
    },
    REFORMULATE_FORMATION_ELEMENT: {
      label: t('AiEnhancementModal.ReformulateText'),
      key: 6,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.FE_TEXT,
          chatGptPromptObject: AiEnhancementGptPrompt.REFORMULATE_MULTIPLES_TEXTS,
          id: formationElementTextId,
          additionalArgs: {
            isRichText: true
          }
        });
      }
    },
    REFORMULATE_ALL_ANSWERS: {
      label: t('AiEnhancementModal.ReformulateText'),
      key: 7,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.ALL_ANSWERS,
          id: questionId,
          chatGptPromptObject: AiEnhancementGptPrompt.REFORMULATE_MULTIPLES_TEXTS,
          additionnalEnonceOrJustificationData: true,
          additionalArgs: {
            initWhatContext:
              whatContextAuthorized.QUESTION_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION
          }
        });
      }
    },
    REFORMULATE_ONE_ANSWER: {
      label: t('AiEnhancementModal.ReformulateText'),
      key: 8,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.ONE_ANSWER,
          chatGptPromptObject: AiEnhancementGptPrompt.REFORMULATE_MULTIPLES_TEXTS,
          additionnalEnonceOrJustificationData: true,
          id: answerId,
          additionalArgs: {
            initWhatContext:
              whatContextAuthorized.ANSWER_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION
          }
        });
      }
    },
    TRADUCT_TO_TITLE: {
      label: t('AiEnhancementModal.TradInto'),
      key: 9,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.EXERCISE_TITLE,
          chatGptPromptObject: AiEnhancementGptPrompt.TRAD_MULTIPLE_TEXTS_TO,
          id: questionId,
          additionalArgs: {
            enableTraductionComponant: true,
            initWhatContext:
              whatContextAuthorized.QUESTION_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION
          }
        });
      }
    },
    TRADUCT_TO_FORMATION_ELEMENT: {
      label: t('AiEnhancementModal.TradInto'),
      key: 10,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.FE_TEXT,
          chatGptPromptObject: AiEnhancementGptPrompt.TRAD_MULTIPLE_TEXTS_TO,
          id: formationElementTextId,
          additionalArgs: {
            isRichText: true,
            enableTraductionComponant: true
          }
        });
      }
    },
    TRADUCT_TO_ALL_ANSWERS: {
      label: t('AiEnhancementModal.TradInto'),
      key: 11,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.ALL_ANSWERS,
          chatGptPromptObject: AiEnhancementGptPrompt.TRAD_MULTIPLE_TEXTS_TO,
          id: questionId,
          additionalArgs: {
            enableTraductionComponant: true,
            initWhatContext:
              whatContextAuthorized.QUESTION_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION
          }
        });
      }
    },
    TRADUCT_TO_ONE_ANSWER: {
      label: t('AiEnhancementModal.TradInto'),
      key: 12,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.ONE_ANSWER,
          chatGptPromptObject: AiEnhancementGptPrompt.TRAD_MULTIPLE_TEXTS_TO,
          id: answerId,
          additionalArgs: {
            enableTraductionComponant: true,
            initWhatContext:
              whatContextAuthorized.ANSWER_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION
          }
        });
      }
    },
    CUSTOM_PROMPT_TITLE: {
      label: t('AiEnhancementModal.CustomGptQuery'),
      key: 13,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.EXERCISE_TITLE,
          chatGptPromptObject: AiEnhancementGptPrompt.CUSTOM_PROMPT,
          id: questionId,
          additionalArgs: {
            enableCustomComponant: true,
            initWhatContext:
              whatContextAuthorized.QUESTION_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION
          }
        });
      }
    },
    CUSTOM_PROMPT_FORMATION_ELEMENT: {
      label: t('AiEnhancementModal.CustomGptQuery'),
      key: 16,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.FE_TEXT,
          chatGptPromptObject: AiEnhancementGptPrompt.CUSTOM_PROMPT,
          id: formationElementTextId,
          additionalArgs: {
            isRichText: true,
            enableCustomComponant: true
          }
        });
      }
    },
    CUSTOM_PROMPT_ALL_CORRECTIONS: {
      label: t('AiEnhancementModal.CustomGptQuery'),
      key: 17,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.ALL_ANSWERS,
          chatGptPromptObject: AiEnhancementGptPrompt.CUSTOM_PROMPT,
          id: questionId,
          additionalArgs: {
            enableCustomComponant: true,
            initWhatContext:
              whatContextAuthorized.QUESTION_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION,
            additionnalEnonceOrJustificationData: true
          }
        });
      }
    },

    CUSTOM_PROMPT_ONE_CORRECTION: {
      label: t('AiEnhancementModal.CustomGptQuery'),
      key: 18,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.ONE_ANSWER,
          //chatGptPromptObject: AiEnhancementGptPrompt.CUSTOM_PROMPT,
          chatGptPromptObject: AiEnhancementGptPrompt.CUSTOM_CORRECTION_PROMPT,
          id: answerId,
          additionalArgs: {
            enableCustomComponant: true,
            initWhatContext:
              whatContextAuthorized.ANSWER_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION,
            additionnalEnonceOrJustificationData: true
          }
        });
      }
    },

    ENHANCE_ALL_CORRECTIONS: {
      label: t('AiEnhancementModal.EnhanceCorrection'),
      key: 14,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.ALL_ANSWERS,
          chatGptPromptObject: AiEnhancementGptPrompt.AMELIORATION_CORRECTION_EXERCISE,
          id: questionId,
          additionalArgs: {
            initWhatContext:
              whatContextAuthorized.QUESTION_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION,
            enableAmeliorationCorrectionComponant: true,
            additionnalEnonceOrJustificationData: true
          }
        });
      }
    },
    ENHANCE_ONE_CORRECTION: {
      label: t('AiEnhancementModal.EnhanceCorrection'),
      key: 15,
      icon: null,
      onClick: () => {
        setCallWrapper({
          initWhat: whatAuthorized.ONE_ANSWER,
          chatGptPromptObject: AiEnhancementGptPrompt.AMELIORATION_CORRECTION_EXERCISE,
          id: answerId,
          additionalArgs: {
            initWhatContext:
              whatContextAuthorized.ANSWER_ID_TO_QUESTION_TITLE_AND_LINKED_COURSES_DESCRIPTION,
            enableAmeliorationCorrectionComponant: true,
            additionnalEnonceOrJustificationData: true
          }
        });
      }
    }
  };

  // L'organisation du menu contextuel
  const dropdownComposition = {
    [enhancementType.TITLE]: [
      dropdownKeys.FIX_SPELLING_AND_GRAMMAR_TITLE,
      dropdownKeys.REFORMULATE_TITLE,
      dropdownKeys.TRADUCT_TO_TITLE,
      dropdownKeys.CUSTOM_PROMPT_TITLE
    ],
    [enhancementType.FE_TEXT]: [
      dropdownKeys.FIX_SPELLING_AND_GRAMMAR_FORMATION_ELEMENT,
      dropdownKeys.REFORMULATE_FORMATION_ELEMENT,
      dropdownKeys.TRADUCT_TO_FORMATION_ELEMENT,
      dropdownKeys.CUSTOM_PROMPT_FORMATION_ELEMENT
    ],
    [enhancementType.ALL_ANSWERS]: [
      dropdownKeys.FIX_SPELLING_AND_GRAMMAR_ALL_ANSWERS,
      dropdownKeys.REFORMULATE_ALL_ANSWERS,
      dropdownKeys.TRADUCT_TO_ALL_ANSWERS,
      dropdownKeys.CUSTOM_PROMPT_ALL_CORRECTIONS,
      dropdownKeys.ENHANCE_ALL_CORRECTIONS
    ],
    [enhancementType.ONE_PROPOSITION]: [
      dropdownKeys.FIX_SPELLING_AND_GRAMMAR_ONE_ANSWER,
      dropdownKeys.REFORMULATE_ONE_ANSWER,
      dropdownKeys.TRADUCT_TO_ONE_ANSWER,
      dropdownKeys.CUSTOM_PROMPT_ONE_CORRECTION,
      dropdownKeys.ENHANCE_ONE_CORRECTION
    ]
  };

  ///////////////////////////////////////
  // FIN ORGANISATION DU MENU
  ///////////////////////////////////////

  useEffect(() => {
    if (callWrapper !== null) {
      setIsModalOpen(true);
    }
  }, [callWrapper]);

  const enableTraductionComponant = !!callWrapper?.additionalArgs?.enableTraductionComponant;
  const enableCustomComponant = !!callWrapper?.additionalArgs?.enableCustomComponant;
  const [stateChatGptPromptObject, setStateChatGptPromptObject] = useState({
    ...callWrapper?.chatGptPromptObject
  });

  useEffect(() => {
    callWrapper && setStateChatGptPromptObject(callWrapper?.chatGptPromptObject);
  }, [callWrapper, callWrapper?.chatGptPromptObject]);

  return (
    <>
      <Dropdown
        menu={{ items: dropdownComposition[componantType] }}
        trigger={['click']}
        onClick={(e) => e.stopPropagation()}
      >
        <Button
          type={!secondary && 'primary'}
          shape="circle"
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', ...style }}
          icon={<WandFilled />}
        ></Button>
      </Dropdown>
      <Modal
        open={isModalOpen}
        onCancel={() => {
          setIsModalOpen(false);
          onModalClose && onModalClose();
        }}
        width={'90%'}
        onClick={(event) => {
          event.stopPropagation();
        }}
        destroyOnClose={true}
        key={`modal-${questionId}-${formationElementTextId}-${answerId}`}
        footer={null}
      >
        <motion.div layout>
          <ConfigSelecter
            enableTraductionComponant={enableTraductionComponant}
            enableCustomComponant={enableCustomComponant}
            setStateChatGptPromptObject={setStateChatGptPromptObject}
          />
          <WrapperAmeliorationComponant
            enableTraductionComponant={enableTraductionComponant}
            enableCustomComponant={enableCustomComponant}
            stateChatGptPromptObject={stateChatGptPromptObject}
            setStateChatGptPromptObject={setStateChatGptPromptObject}
            callArgument={callWrapper ? callWrapper : {}}
            refetch={refetch}
            onClick={(event) => {
              event.stopPropagation();
            }}
            closeModalFunction={() => {
              setIsModalOpen(false);
              onModalClose && onModalClose();
            }}
            key={`NewWrapper-${questionId}-${formationElementTextId}-${answerId}`}
            getTextFct={getTextFct}
            componantType={componantType}
          />
        </motion.div>
      </Modal>
    </>
  );
};
