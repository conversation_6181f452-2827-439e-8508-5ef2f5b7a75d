import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import { useTranslation } from 'react-i18next';

const PageRangeSelecter = ({ numberOfPages, setter, disabled }) => {
  const { t } = useTranslation();

  const [selectedPageStart, setSelectedPageStart] = useState(null);
  const [selectedPageEnd, setSelectedPageEnd] = useState(null);
  const [selectedPlage, setSelectedPlage] = useState(null);

  const handleStartPageChange = (value) => {
    setSelectedPageStart(value);
    // Réinitialiser la page de fin si la page de départ est supérieure ou égale à la page de fin
    if (selectedPageEnd !== null && value >= selectedPageEnd) {
      setSelectedPageEnd(null);
      setSelectedPlage(null);

      // Si on ré-initialise pas : alors on fait la plage
    } else {
      checkIfSelectionIsFullAndSetupPlage({ start: value, end: selectedPageEnd });
    }
  };

  const handleEndPageChange = (value) => {
    setSelectedPageEnd(value);
    checkIfSelectionIsFullAndSetupPlage({ start: selectedPageStart, end: value });
  };

  const checkIfSelectionIsFullAndSetupPlage = ({ start, end }) => {
    // Fonction qui check si la fin et le début sont setup et file la plage au setter
    if (start && end) {
      setSelectedPlage(Array.from({ length: end - start + 1 }, (_, index) => start + index));
    }
  };

  useEffect(() => {
    if (selectedPlage !== null) {
      setter(selectedPlage);
    }
  }, [selectedPlage, setter]);

  const firstSelecterOption = Array.from({ length: numberOfPages }, (_, index) => ({
    value: index + 1,
    label: index + 1,
    key: index + 1
  }));
  const lastSelecterOption = Array.from({ length: numberOfPages }, (_, index) => ({
    value: index + 1,
    label: index + 1,
    key: index + 1
  })).filter((option) => option.value >= selectedPageStart);

  return (
    <div style={{ display: 'flex', gap: '50px' }}>
      <div>
        <span style={{ alignContent: 'center' }}>
          {t('AiQuestionCreationModal.LabelRadioButtonLabelSelectIntervalStart')}
        </span>

        <Select
          style={{ width: '150px' }}
          value={selectedPageStart}
          onChange={handleStartPageChange}
          placeholder={t('AiQuestionCreationModal.PlaceholderRadioButtonLabelSelectIntervalStart')}
          options={firstSelecterOption}
          disabled={disabled}
        />
      </div>

      <div>
        <span style={{ alignContent: 'center' }}>
          {t('AiQuestionCreationModal.LabelRadioButtonLabelSelectIntervalEnd')}
        </span>

        {lastSelecterOption && (
          <Select
            style={{ width: '150px' }}
            value={selectedPageEnd}
            onChange={handleEndPageChange}
            placeholder={t('AiQuestionCreationModal.PlaceholderRadioButtonLabelSelectIntervalEnd')}
            options={lastSelecterOption}
            disabled={disabled || !selectedPageStart} // Désactiver tant qu'une page de départ n'est pas sélectionnée
          />
        )}
      </div>
    </div>
  );
};

export { PageRangeSelecter };
