import { CreateAndImportAiContextProvider } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';
import CreateExercise from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateExercise';

const CreateExerciseHoC = (props) => {
  return (
    <>
      <CreateAndImportAiContextProvider>
        <CreateExercise {...props} />
      </CreateAndImportAiContextProvider>
    </>
  );
};

export default CreateExerciseHoC;
