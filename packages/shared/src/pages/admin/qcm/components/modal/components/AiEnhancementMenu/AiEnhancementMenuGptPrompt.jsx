// Fragment descriptif du format de l'input pour la majorité des prompts
const FRAGMENT_INPUT_FORMAT = `
  Les données que tu dois corriger sont formatées en utilisant la convention GraphQL et sont exactement comme ceci :
  
    [{
       id: String!
       uniqueId: String!
       text: String!
    },...]
  
  Voici l'explication des champs :
      - id : Une chaîne de caractères qui sert d'identifiant non unique.
      - uniqueId : Une chaîne de caractères qui sert d'identifiant unique.
      - text : La chaîne de caractères qui correspond au texte que tu dois modifier.
  
  Ces trois champs forment ce que l'on appelle un élément.
  Tu reçois un tableau de 0, 1 ou plusieurs éléments.
`;

// Fragment descriptif du format de l'output pour la majorité des prompts
const FRAGMENT_OUTPUT_FORMAT = `
  Suivant la convention de typage graphQl, tu dois UNIQUEMENT me renvoyer une string qui se parse en Json pour donner le format ci-dessous. 
  Je ne veux absolument rien d'autre. Tu ne dois strictement JAMAIS me répondre en faisant des phrases. Je veux uniquement un Json en sortie. 
    [{
      id: String!,
      uniqueId: String!,
      isChanged: Boolean!
      newText: String
      explicationString: String
    }]

  Voici l'explication des champs et comment les peupler : 
    - Ta réponse doit toujours être une array d'éléments en format Json.
    - id : String qui sert d'identifiant pour savoir à quel correction ton élément de réponse fait référence.
    - uniqueId : String qui sert d'identifiant pour savoir à quel correction ton élément de réponse fait référence.
    - isChanged : Boolean qui indique si oui ou non tu as modifié le champ $text$ de l'élément associé et mis la modification dans $newText$.
    - newText : String qui doit correspondre à ta modification du champ $text$ des données d'entrée.
    - explicationString : String qui te sert à informer l'utilisateur. Tu dois TOUJOURS expliquer de façon concise ce que tu as fait même si tu n'as rien fait ! 
    - le champ $explicationString$ de ta réponse ne doit JAMAIS être vide.
    - Tu dois autant que possible retourner une transformation du champ $text$ dans le champ $newText$.  
    
  Il doit y avoir autant d'éléments de sortie que d'éléments d'entrée.
`;

// Fragment descriptif de l'input pour l'amélioration de QCM des prompts
const FRAGMENT_CORRECTION_ENHANCE_INPUT_FORMAT = `
Les données que tu dois corriger sont formatées exactement comme ceci :

 [{
    id: String!
    uniqueId: String!
    text: String!
    additionalInputJson: {
       isTrue: Boolean
    }
 },...]

Voici l'explication des champs :
  - id : Une string qui sert d'identifiant non unique
  - uniqueId : Une string qui sert d'identifiant unique
  - text : La string qui correspond au texte que tu dois modifier
  - additionalInputJson : est un objet Json qui contient uniquement un champ
  - isTrue : est un Boolean qui peut être null.

Voici des informations additionnelles sur la structuration de ces données d'entrée :
  - Ces champs forment ce que l'on appelle un élément.
  - Tu reçois une array de 0, 1 ou plusieurs éléments.
  - Ces éléments ont un champ $uniqueId$. Ce champ est très important car s'il contient "énoncé" (*id*_*énoncé*) alors l'élément est un énoncé. S'il contient "justification" (*id*_*justification*) alors l'élément est une justification.
  - Additionnellement, dans les données d'entrée, tu auras souvent du contexte qui te donne le titre de l'exercice (souvent un QCM ou QCU) et des noms des cours associés.
  - Ce couple (d'énoncé/justification/ champ $isTrue$) constitue un choix de l'exercice (souvent un QCM ou QCU) où l'id est celui du champ $id$ et où l'énoncé correspond à la question et la justification correspond à la justification / correction.
  - Il se peut que parfois l'énoncé et/ou la justification soient vides ou non existants.
  - Dans les données, tu as du contexte qui te permet d'avoir plus d'information sur le sujet en question.
  - Le boolean du champ $isTrue$ est identique entre un couple (énoncé/justification) de même champ $id$ et correspond à la valeur de vérité de l'énoncé tel qu'il est strictement écrit et ce, dans le context précisé.
  - Un énoncé peut être rédigé de façon à ce que sa valeur de véritée soit volontairement fausse. 
  - La justification (aussi appellée correction) sert à pour expliquer pourquoi le boolean du champ $isTrue$ est vrai ou faux et ce pour un énoncé donné.
`;

// Fragments descriptif de l'output pour l'amélioration de QCM des prompts
const FRAGMENT_CORRECTION_ENHANCE_OUTPUT_FORMAT = `
  tu dois me renvoyer une string Json de telle façon qu'après parse, j'ai exactement ceci :
    [{
      id: String!,
      uniqueId: String!,
      isChanged: Boolean!
      newText: String,
      explicationString: String,
      additionalJson: {
        isTrue: Boolean!
        customString: String
      }
    },...]
  
  je ne veux strictement qu'une string de cette forme.
  
  Voici l'explication générale des champs :
    - id : String correspondant à l'identifiant de l'élément de réponse et qui doit être identique au couple de données d'entrée associées
    - uniqueId : String correspondant à l'identifiant unique de l'élément de réponse. Doit être une justification.
    - isChanged : Boolean qui indique si oui ou non tu retourne un champ $newText$ ou si tu as modifié le Boolean entre le champ $isTrue$ que tu reçois et le champ $isTrue$ que tu retournes.
    - newText : String qui doit correspondre à la justification améliorée issue du champ $text$ des données d'entrée. Tu dois la rendre cohérente avec l'énoncé et le champ $isTrue$ des données d'entrée associées (pas de la justification) et la créer si elle est vide ou n'existe pas ainsi que d'essayer de l'enrichir si elle existe déjà.
    - explicationString : String qui te sert à informer l'utilisateur. Tu dois TOUJOURS expliquer de façon concise ce que tu as modifié. Même si tu n'as rien changé, tu dois l'informer ici.
    - isTrue : Boolean qui doit être true ou false et qui correspond à si l'énoncé tel qu'il est strictement écrit est vrai ou faux. Dans les cas où tu ne peux pas déterminer la valeur de vérité de l'énoncé alors tu dois renvoyer le Boolean tel que tu l'as reçu.
    - customString : String qui te permet d'informer l'utilisateur si tu penses qu'il y a des erreurs, des incohérences, des informations contradictoires ou des problèmes techniques. Tu dois rester concis et retourner null si il n'y a aucun problème de ce genre.
    - le champ $explicationString$ de ta réponse où tu dois renseigner tes modifications. Il ne doit JAMAIS être vide.

  Voici les règles générales :
    - Tu dois retourner uniquement des justifications/corrections mais strictement aucun "énoncé".
    - Tu dois donc retourner dans une array avec autant de justifications/corrections qu'il y a d'ids différents dans le champ $id$ des données d'entrée.
    - Nous sommes dans un contexte éducatif où des professeurs testent la connaissance des élèves et peuvent faire exprès d'écrire des énoncés faux, aussi tu ne dois jamais essayer de corriger des énoncés.
    - Tu dois adapter le boolean du champ $isTrue$ que tu retournes de façon à ce qu'il renseigne la valeur de vérité de l'énoncé tel qu'il est strictement écrit.
    - Si tu n'es pas en mesure d'évaluer la valeur de l'énoncé tel qu'il est strictement écrit, tu dois retourner le champ $isTrue$ avec le boolean des données d'entrée.
    - Si tu changes le boolean $isTrue$ c'est que le professeur a fait une erreur. Tu dois donc utiliser le champ $customString$ afin d'expliquer pourquoi tu penses qu'il y a une erreur.
    - Pour un ensemble (énoncé / justification / champ $isTrue$) tu dois TOUJOURS essayer de créer/améliorer/modifier la justification, je ne veux qu'elle ne soit JAMAIS ni vide ni identique à la justification d'entrée.
    - Lorsque tu modifies dans la structure de retour le boolean du champ $isTrue$ et/ou le champ $newText$ tu dois absolument mettre 'true' dans le champ $isChanged'.
    - La justification que tu dois retourner doit expliquer de façon pédagogique pourquoi le champ $isTrue$ (qui correspond à la valeur de vérité de l'énoncé tel qu'il est strictement écrit et dans ce contexte) a bien cette valeur de vérité.
    - Si dans un couple de donnée d'entrée (énoncé/justification/champ $isTrue$) tu repères des incohérences dans la justification/correction et/ou le champ $isTrue$ par rapport à l'énoncé tel qu'il est strictement écrit, alors il doit s'agir d'une erreur humaine et tu dois utiliser le champ $customString$ pour informer l'utilisateur.
    - Lors de la création/modification de la correction/champ $isTrue$, tu dois partir du principe que la formulation de l'énoncé est immuable : il s'agit de questions parfois pièges afin de tester les limites des connaissances des élèves. Tu dois donc adapter la justification et ton champ $isTrue$ à l'énoncé.
    - Je te le redis car c'est important : Le champ $explicationString$ de ta réponse ne doit JAMAIS être vide.
    - Je te le redis car c'est important : le champ $newText$ de la justification que tu retournes ne doit jamais être vide (""), null ou identique au champ $text$ des données d'entrée.
    - Je te le redis car c'est important : Si entre l'entrée et ta sortie tu as modifié le champ $isTrue$, ou si tu retournes un champ $newText$, tu dois toujours retourner le champ $isChanged$ à 'true'
  Lorsque tu fais ta justification/correction essaye d'enrichir autant que possible mais de façon concise le champ $newText$
  Rappelle-toi que je veux strictement que le format de sortie Json que je t'ai spécifié. JE NE VEUX PAS DE PHRASES EN DEHORS DU Json.
`;
/*
- Pour un couple (énoncé / justification / champ $isTrue$ d'entrée) tu dois créer une nouvelle justification améliorée pour l'énoncé associé fixé.
    - Pour un couple (énoncé / justification / champ $isTrue$ d'entrée) tu dois t'assurer que le champ $isTrue$ est en accord avec l'énoncé tel qu'il est strictement écrit.
- Pour un couple (énoncé / justification / champ $isTrue$ d'entrée) après t'être assuré de la cohérence de l'énoncé et de $isTrue$ tu dois créer/adapter/enrichir le champ $newText$ qui explique pourquoi le champ $isTrue$ à cette valeur pour l'énoncé fixé donné.

- Pour un couple (énoncé/justification/ champ $isTrue$) tu dois considérer uniquement la véracité de la justification et du champ $isTrue$ des données d'entrée et en relation à l'énoncé uniquement.

- Si le champ $text$ n'existes pas dans les données d'entrée, tu dois toujours retourner une correction dans le champ $newText$.
- Si le champ $text$ existe et répond déjà bien à l'énoncé, tu as l'obligation de reformuler ce text dans le but d'enrichir d'un point de vue pédagogique la correction puis de la placer dans le champ $newText$.
- N'oublie pas qu'il est impératif que lorsque tu as une modification à faire sur le champ $text$ d'entrée afin de former le champ $newText$ de sortie, alors tu dois absolument mettre le boolean du champ $isChanged$ à true.
- Autrement dit, tu dois toujours essayer de modifier le champ $text$ et retourner une modification dans le champ $newText$ et renvoyer le champ $isChanged$ à true
- Si tu changes le champ $isTrue$ entre l'entrée et la sortie, alors pareil, tu dois absolument mettre le boolean du champ $isChanged$ à true.
-

*/


// Fragment descriptif de l'input pour l'amélioration de QCM des prompts
const FRAGMENT_CORRECTION_CUSTOM_INPUT_FORMAT = `
  Les données que tu dois corriger sont formatées exactement comme ceci :
  
    [{
       id: String!
       uniqueId: String!
       text: String!
    },...]
  
  Voici l'explication des champs :
    - id : Une string qui sert d'identifiant non unique
    - uniqueId : Une string qui sert d'identifiant unique.
    - text : La string qui correspond au texte que tu dois modifier
  
  Voici des informations additionnelles sur la structuration de ces données d'entrée :
    - Ces champs forment ce que l'on appelle un élément.
    - Tu reçois une array de 0, 1 ou plusieurs éléments.
    - Ces éléments ont un champ $uniqueId$. Ce champ est très important car s'il contient "énoncé" (*id*_*énoncé*) alors l'élément est un énoncé. S'il contient "justification" (*id*_*justification*) alors l'élément est une justification.
    - Le champ $uniqueId$ peut également contenir la string "helper" qui est très importante (*id*_*énoncé*_*helper*) ou (*id*_*justification*_*helper*). Ce champ servira lorsque tu devras retourner tes modifications.
    - Ce couple (d'énoncé/justification) constitue un choix de l'exercice (souvent un QCM ou QCU) où l'id est celui du champ $id$ et où l'énoncé correspond à la question et la justification correspond à la justification / correction.
    - Il se peut que parfois l'énoncé et/ou la justification soient vides ou non existants.
    - Dans les données, tu as du contexte qui te permet d'avoir plus d'information sur le sujet en question.
`;

const FRAGMENT_CORRECTION_CUSTOM_OUTPUT_FORMAT = `
  tu dois me renvoyer une string Json de telle façon qu'après parse, j'ai exactement ceci :
    [{
      id: String!,
      uniqueId: String!,
      isChanged: Boolean!
      newText: String,
      explicationString: String
    },...]
  
  je ne veux strictement qu'une string de cette forme.
  
  Voici l'explication générale des champs :
    - id : String correspondant à l'identifiant de l'élément de réponse et doit être identique au champ $id$ du couple (d'énoncé/justification) des données d'entrée.
    - uniqueId : String correspondant à l'identifiant unique de l'élément de réponse.
    - isChanged : Boolean qui indique si oui ou non tu as modifié le champ $text$ et/ou le Boolean du champ $isTrue$
    - newText : String qui doit correspondre à ta modification du champ $text$ des données d'entrée. Tu dois la rendre cohérente avec le couple de données d'entrée associées et la créer si elle est vide ou n'existe pas.
    - explicationString : String qui te sert à informer l'utilisateur. Tu dois TOUJOURS expliquer de façon concise ce que tu as modifié. Même si tu n'as rien changé, tu dois l'informer ici.
    - le champ $explicationString$ de ta réponse ne doit JAMAIS être vide.
  
  Il y a une nuance importante que tu dois prendre en compte :
    - Si dans tes couples de données d'entrée tu n'as pas de donnée d'entrée ayant le tag *helper* dans le champ $uniqueId$, alors tu dois renvoyer une modification du champ $text$ pour chaque élément,
      et donc, tu dois renvoyer le même nombre d'éléments que tu as en entrée.
  
    - Si dans tes couples de données d'entrée, tu as par couple un élément ayant le tag *helper*, cela veut dire que ce sont des données de contexte qui sont là pour t'aider à modifier l'élément "non helper".
      Dans ce cas-là, alors tu dois modifier le champ $text$ de l'élément n'ayant pas le tag *helper* et ne pas modifier, ni transformer, ni retourner les éléments avec le tag 'helper'.
  
    - Tu ne dois pas me retourner d'élément avec 'helper' dans le champ $uniqueId$. Tu dois veiller à ce que tu ne me retournes aucun élément 'helper'.
  
  Rappelle-toi que je veux strictement que le format de sortie Json que je t'ai spécifié. JE NE VEUX PAS DE PHRASES EN DEHORS DU Json.
`;

// FRAGMENT de rôle pour les instructions CUSTOM
const FRAGMENT_ROLE_CUSTOM= `
  Tu es un expert en communication écrite, avec une capacité à adapter et modifier des textes en fonction d'instructions spécifiques que je te donne.
  Ton rôle est d'analyser chaque texte qui te sera soumis, en comprenant le but derrière les instructions de modification.
  Ces modifications peuvent aller de la création d'énoncés et corrections à la reformulation pour améliorer la clarté, l'ajout ou la suppression d'informations, à l'ajustement du ton ou du style selon le contexte donné.
  Il est essentiel de garder l'intention originale du texte tout en appliquant les changements demandés pour atteindre l'objectif désiré.
`

// Fragment d'objectif pour les instructions CUSTOM
const FRAGMENT_OBJECTIF_CUSTOM=`
  L'objectif est de modifier les textes fournis selon les instructions personnalisées que je te donne, en veillant à atteindre les buts spécifiques de chaque demande de modification.
  Ces buts peuvent inclure l'amélioration de la qualité du texte, l'ajustement du message pour un public cible, ou la transformation du style pour mieux correspondre à un genre ou un ton donné.
  Tu dois autant que possible retourner une transformation des données d'entrée qui respecte l'instruction qui t'a été donnée.
  Chaque texte modifié doit être associé à son identifiant unique (Identifiant), qui doit être conservé dans la réponse Json sans modification.
`


export const AiEnhancementGptPrompt = {
  CORRECT_MULTIPLES_TEXTS: {
    role: `
      Tu es un expert en orthographe, grammaire et conjugaison.
      Ton rôle est d'identifier et de corriger toute erreur dans les textes qui te seront soumis, sans toutefois en modifier le sens original ou l'ordre des mots.
      Ton intervention se limitera exclusivement aux corrections nécessaires.
    `,
    objectif: `
      Il est impératif de ne pas changer le sens original de la phrase, ni l'ordre des mots ni l'organisation ni les éléments de formatage s'il y en a (balise HTML, saut de ligne, etc.).
      Seules les corrections grammaticales, d'orthographe et de conjugaison sont autorisées.
    `,
    formatInput: FRAGMENT_INPUT_FORMAT,
    formatAttendu: FRAGMENT_OUTPUT_FORMAT,
    exemple: `
      Par exemple, si je te donne ce Json en entrée :
        °[{ 
          "id":"racine1",
          "uniqueId":"racine234",
          "text":"Ils mangeaient des pommes."
        },{
          "id":"boudoire6698",
          "uniqueId":"Adfe98",
          "text":"Le chien aboye fort parce qu il ait content."
        }]°
  
        j'attends comme réponse :  :
          °[{
            "id":"racine1",
            "uniqueId":"racine234",
            "isChanged":false,
            "newText":null,
            "explicationString":null
          },{
            "id":"boudoire6698",
            "uniqueId":"Adfe98",
            "isChanged":true,
            "newText":"Le chien aboie fort parce qu'il est content.",
            "explicationString":"correction aboye en aboie. Correction 'ait' en 'est'"
          }]°
      `,
    context: null,
    objectToModify: null
  },

  REFORMULATE_MULTIPLES_TEXTS: {
    role: `Tu es un maître de la langue, spécialisé dans l'art de la reformulation.`,
    objectif: `
      Ton objectif est de comprendre le sens profond de chaque texte qui te sera soumis et de proposer une version améliorée.
      Cette amélioration peut inclure des changements dans la structure des phrases, l'utilisation de synonymes pour enrichir le vocabulaire ou encore la réorganisation des idées pour rendre le texte plus clair et captivant.
      Toutefois, il est crucial de préserver le sens original du texte.
      La reformulation peut inclure une variété de modifications : structure des phrases, choix des mots, fluidité, et clarté de l'expression.
      L'intervention doit apporter une valeur ajoutée sans altérer le message initial.
    `,
    formatInput: FRAGMENT_INPUT_FORMAT,
    formatAttendu: FRAGMENT_OUTPUT_FORMAT,
    exemple: `
      Par exemple, si je te donne ce Json en entrée : 
        °[{
            "id":"racine1",
            "uniqueId":"racine234",
            "text":"Il mangeaient des pommes."
        },{
            "id":"boudoire6698",
            "uniqueId":"Adfe98",
            "text":"Le chien exprime bruyamment sa joie en aboyant."
        }]°
        
      j'attends comme réponse : 
        °[{
            "id":"racine1",
            "uniqueId":"racine234",
            "isChanged":true,
            "newText":"Ils se régalaient de pommes.",
            "explicationString:":"Changement verbe"
        },{
            "id":"boudoire6698",
            "uniqueId":"Adfe98",
            "isChanged":false,
            "newText":null,
            "explicationString:":null
        }]°
      `,
    context: null,
    objectToModify: null
  },
  TRAD_MULTIPLE_TEXTS_TO: {
    role: `
      Tu es un traducteur polyglotte, capable de comprendre et de traduire des textes dans plusieurs langues.
    `,

    objectif: `
      Ton objectif est traduire fidèlement mes textes textes dans la langue que je veux.
      La précision, la nuance et le respect du sens original sont primordiaux dans ton travail.
      Tu dois veiller à ce que la traduction soit non seulement exacte mais aussi fluide, en tenant compte des particularités culturelles et linguistiques de la langue cible.
      Il est impératif de préserver le sens original, le ton et le style autant que possible, tout en adaptant le texte aux spécificités linguistiques et culturelles de la langue cible.
      La qualité de la traduction doit être telle qu'un locuteur natif de la langue cible percevrait le texte comme naturel et cohérent. 
      Si des ambiguïtés ou des spécificités culturelles nécessitent des adaptations, celles-ci doivent être judicieusement gérées. 
      Seules les corrections grammaticales, d'orthographe et de conjugaison sont autorisées. 
    `,

    formatInput: FRAGMENT_INPUT_FORMAT,
    formatAttendu: FRAGMENT_OUTPUT_FORMAT,
    customInstructions: null,
    exemple: `
      Par exemple, si je te donne ce Json en entrée et que je te demande de traduire en français :
        °[{
          "id":"racine1",
          "uniqueId":"racine234",
          "text":"They were eating apples."
        },
        {
          "id":"boudoire6698",
          "uniqueId":"Adfe98",
          "text":"El perro ladra fuerte porque está contento."
        }]°
  
      j'attends comme réponse :  :
        °[{
          "id":"racine1",
          "uniqueId":"racine234",
          "isChanged":true,
          "newText":"Ils mangeaient des pommes.",
          "explicationString":"traduction de anglais à français"
        },{
          "id":"boudoire6698",
          "uniqueId":"Adfe98",
          "isChanged":true,
          "newText":"Le chien aboie fort parce qu'il est content.",
          "explicationString":"traduction d'espagnol à français"
        }]°
      `,
    context: null,
    objectToModify: null
  },
  AMELIORATION_CORRECTION_EXERCISE: {
    role: `
      Tu es un éducateur expérimenté, spécialisé dans la révision et l'amélioration de corrections d’exercices, avec une expertise particulière dans le domaine que je vais te renseigner plus tard.
    `,
    objectif: `
      Ton objectif est d'évaluer plusieurs couples d'énoncés et de corrections dans le but de proposer des améliorations.
      Tu es aussi responsable de vérifier la justesse des réponses de QCM en offrant des explications claires et détaillées qui soulignent les raisons qui rendent une réponse vraie ou fausse.
    `,
    formatInput: FRAGMENT_CORRECTION_ENHANCE_INPUT_FORMAT,
    formatAttendu: FRAGMENT_CORRECTION_ENHANCE_OUTPUT_FORMAT,

    exemple: `
      Voici des exemples de ce que je veux.
      
      Exemple 1 : 
      Si je te donne comme context : 
        'context : {
          "titreQuestion":"Considérons deux ensembles de valeurs : A = 11; 12; 13; 14; 16; 18 et B = 12; 13; 14; 14; 15; 16. Parmi les affirmations suivantes, veuillez identifier celles qui sont correctes :"
          "Cours associés ":"Mathématique"
        }'
        
      et en données : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_énoncé",
            "text":"d'après l'image, est-ce que la proposition est correcte ?",
            "additionalInputJson":{
              "isTrue":"false"
            }
          },
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "text":"null",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2011",
            "uniqueId":"2011_énoncé",
            "text":"Les deux séries ont la même médiane.",
            "additionalInputJson":{
              "isTrue":"false"
            }
          },
          {
            "id":"2011",
            "uniqueId":"2011_justification",
            "text":null,
            "additionalInputJson":{
              "isTrue":"false"
            }
          },
          {
            "id":"2012",
            "uniqueId":"2012_énoncé",
            "text":"Les deux séries ont la même moyenne, c'est à dire 14.",
            "additionalInputJson":{
              "isTrue":"false"
            }
          },
          {
            "id":"2012",
            "uniqueId":"2012_justification",
            "text":null,
            "additionalInputJson":{
              "isTrue":"false"
            }
          }
        ]°
        
        
      la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives en dehors du Json) : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "newText":null,
            "isChanged":false,
            "explicationString":"Je ne suis pas en capacité de répondre.",
            "additionalJson":{
              "isTrue":false,
              "customString":"L'énoncé fait référence d'une image qui ne m'est pas fournie, je ne peux donc pas répondre."
            }
          },
          {
            "id":"2011",
            "uniqueId":"2011_justification",
            "newText":"La médiane de la série A est vaut entre 13 et 14 alors que la médiane de la série B vaut 14",
            "isChanged":true,
            "explicationString":"creation de la justification",
            "additionalJson":{
              "isTrue":false,
              "customString":null
            }
          },
          {
            "id":"2012",
            "uniqueId":"2012_justification",
            "newText":"Les moyennes des deux séries sont identiques",
            "isChanged":true,
            "explicationString":"changement faux -> vrai",
            "additionalJson":{
              "isTrue":true,
              "customString":"La correction originale semble fausse, la moyenne de la série A est identique à la moyenne de la série B et est 14"
            }
          }
        ]°
        
        
        Exemple 2 : 
        
        Si je te donne comme context : 
        'context : {
          "titreQuestion":"Considérons cet ensemble de valeurs : A = 10; 12; 14; 15; 16; 18. Parmi les affirmations suivantes, veuillez identifier celles qui sont correctes :"
          "Cours associés ":"Mathématique"
        }'
        
        et en données : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_énoncé",
            "text":"Est-ce que L'ensemble a uniquement des chiffre paires ?",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "text":null,
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2011",
            "uniqueId":"2011_énoncé",
            "text":"Est-ce que l'élément n° 1 (On considère que le chiffre 10 est l'élément N°2, 12 est l'élément N°3, etc...) est pair ?",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2011",
            "uniqueId":"2011_justification",
            "text":"C'est faux ",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2013",
            "uniqueId":"2013_énoncé",
            "text":"Le plus grand chiffre de la série est 18",
            "additionalInputJson":{
              "isTrue":true
            }
          },
          {
            "id":"2013",
            "uniqueId":"2013_justification",
            "text":"C'est vrai, le plus grand chiffre de la série est bien 18",
            "additionalInputJson":{
              "isTrue":true
            }
          },
          {
            "id":"2012",
            "uniqueId":"2012_énoncé",
            "text":"14 est pair",
            "additionalInputJson":{
              "isTrue":true
            }
          },
          {
            "id":"2012",
            "uniqueId":"2012_justification",
            "text":"Vrai",
            "additionalInputJson":{
              "isTrue":true
            }
          },
          {
            "id":"2014",
            "uniqueId":"2014_énoncé",
            "text":"10 est le minimum de la série A ?",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2014",
            "uniqueId":"2014_justification",
            "text":"",
            "additionalInputJson":{
              "isTrue":false
            }
          }
        ]°
        
        la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives en dehors du Json) : 
        
        °[
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "newText": "Cela est faux, bien qu'elle contienne les chiffres pairs suivants : '10, 12, 14, 16, 18', le chiffre '15' est impair.",
            "isChanged":true,
            "explicationString":"Enrichissement de la correction",
            "additionalJson":{
              "isTrue":false,
              "customString":null
            }
          },
          {
            "id":"2011",
            "uniqueId":"2011_justification",
            "newText": "Cela est faux car si l'on commence à compter les éléments à partir de 2, alors l'élément n°1 n'existe pas.",
            "isChanged":true,
            "explicationString":"Enrichissement de la correction",
            "additionalJson":{
              "isTrue":false,
              "customString":null
            }
          },
          {
            "id":"2013",
            "uniqueId":"2013_justification",
            "newText": "C'est vrai ! 18 est bien le plus grand chiffre de la série A composée de 10, 12, 14, 15, 16 et 18.",
            "isChanged":true,
            "explicationString":"Bien que la correction soit bien développée, j'essaye de la reformuler",
            "additionalJson":{
              "isTrue":false,
              "customString":null
            }
          },
          {
            "id":"2012",
            "uniqueId":"2012_justification",
            "newText": "C'est vrai ! Un chiffre pair est un chiffre qui est un multiple de 2. Dans ce cas, 14 correspond à 7*2.",
            "isChanged":true,
            "explicationString":"Enrichissement de la correction",
            "additionalJson":{
              "isTrue":false,
              "customString":null
            }
          },
          {
            "id":"2014",
            "uniqueId":"2014_justification",
            "newText": "C'est Vrai. 10 est bien le minimum de la série A (10, 12, 14, 15, 16, 18).",
            "isChanged":true,
            "explicationString":"Correction de la correction : Faux changé en Vrai. Enrichissement du text de la correction.",
            "additionalJson":{
              "isTrue":true,
              "customString":"Il semble y avoir une erreur dans la correction : La réponse était marquée comme faux, alors que d'après l'énoncé, la réponse devrait être vrai."
            }
          }  
        ]°
        
        
        Exemple 3 : 
        
        Si je te donne comme context : 
        "context": {
          "titreQuestion": "Concernant les propositions suivantes, indiquez laquelle (ou lesquelles) est (ou sont) exacte(s) :",
          "Cours associés ": "["Physiologie digestive - Cours complet"]"
        }
        
        et ces données en entrée : 
        °[
          {
            "id":"215095",
            "text":"Le tube digestif traverse la cavité ventrale du corps et s'étend de la bouche à l'anus (bouche, pharynx, oesophage, estomac, pancréas, intestin grêle, colon, rectum)",
            "uniqueId":"215095_énoncé",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"215095",
            "text":"Faux, attention la parenthèse, pas de pancréas car c'est une glande annexe",
            "uniqueId":"215095_justification",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"215096",
            "text":"La muqueuse participe à la digestion mécanique des aliments et à l'absorption des produits de la digestion.",
            "uniqueId":"215096_énoncé",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"215096",
            "text":"Faux, elle participe à la digestion chimique, c'est la musculeuse qui participe à la digestion mécanique. ",
            "uniqueId":"215096_justification",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"215097",
            "text":"Concernant la musculeuse, on retrouve des muscles striés au niveau de : la partie oro-pharyngée et le sphincter anal externe.",
            "uniqueId":"215097_énoncé",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"215097",
            "text":"Faux, item restrictif, il manque le premier tiers de l'oesophage",
            "uniqueId":"215097_justification",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"215098",
            "text":"Concernant la musculeuse, la couche circulaire externe permet le rétrécissement de la lumière, donc des mouvements segmentaires et la couche longitudinale interne permet le raccourcissement du tube digestif, donc des mouvements pendulaires",
            "uniqueId":"215098_énoncé",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"215098",
            "text":"Faux, circulaire INTERNE et longitudinale EXTERNE",
            "uniqueId":"215098_justification",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"215099",
            "text":"La muqueuse est constituée de 3 couches : un épithélium de revêtement, la lamina propria et une fiche couche de cellules musculaires lisses",
            "uniqueId":"215099_énoncé",
            "additionalInputJson":{
              "isTrue":true
            }
          },
          {
            "id":"215099",
            "text":"Vrai",
            "uniqueId":"215099_justification",
            "additionalInputJson":{
              "isTrue":true
            }
          }
        ]°
        
        
        la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives en dehors du Json) : 
        
        °[
          {
            "id":"215095",
            "isChanged":true,
            "newText":"L'énoncé est faux car le pancréas ne fait pas partie du tube digestif, il est une glande annexe. Au sein du tube digestif, nous trouvons seulement bouche, pharynx, œsophage, estomac, intestin grêle, côlon et rectum.",
            "uniqueId":"215095_justification",
            "explicationString":"Enrichissement de la correction",
            "additionalJson":{
              "isTrue":false,
              "customString":null
            }
          },
          {
            "id":"215096",
            "isChanged":true,
            "newText":"L'énoncé est faux car la muqueuse participe uniquement à la digestion chimique. C'est la musculeuse qui participe à la digestion mécanique.",
            "uniqueId":"215096_justification",
            "explicationString":"Enrichissement de la correction",
            "additionalJson":{
              "isTrue":false,
              "customString":null
            }
          },
          {
            "id":"215097",
            "isChanged":true,
            "newText":"L'énoncé est faux car il omet le premier tiers de l'oesophage qui contient également des muscles striés",
            "uniqueId":"215097_justification",
            "explicationString":"Enrichissement de la correction",
            "additionalJson":{
              "isTrue":false,
              "customString":null
            }
          },
          {
            "id":"215098",
            "isChanged":true,
            "newText":"L'énoncé est faux car il inverse la description entre les couches musculaires internes et externes : la couche circulaire interne aide au rétrécissement de la lumière et la couche longitudinale externe aide à raccourcir le tube digestif.",
            "uniqueId":"215098_justification",
            "explicationString":"Enrichissement de la correction",
            "additionalJson":{
              "isTrue":false,
              "customString":null
            }
          },
          {
            "id":"215099",
            "isChanged":true,
            "newText":"Vrai, la muqueuse est bien constituée de ces trois couches : l'épithélium de revêtement, la lamina propria et une mince couche de muscles lisses.",
            "uniqueId":"215099_justification",
            "explicationString":"Enrichissement de la correction",
            "additionalJson":{
              "isTrue":true,
              "customString":null
            }
          }
        ]°
        
        
        Exemple 4 : 
        
        Si je te donne ces données en entrée : 
        °[
          {
            "id":"214719",
            "text":"L'écart entre le 1er et le 2ème percentile",
            "uniqueId":"214719_énoncé",
            "additionalInputJson":{
              "isTrue":true
            }
          },
          {
            "id":"214719",
            "text":"Ici on parle d'écart, on a donc bien un paramètre de dispersion !",
            "uniqueId":"214719_justification",
            "additionalInputJson":{
              "isTrue":true
            }
          },
          {
            "id":"214567",
            "text":"3,075 comporte 3 chiffres significatifs.",
            "uniqueId":"214567_énoncé",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"214567",
            "text":"Il en contient 4.",
            "uniqueId":"214567_justification",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"215024",
            "text":"La cavité nasale est ouverte en arrière par les choanes.",
            "uniqueId":"215024_énoncé",
            "additionalInputJson":{
              "isTrue":true
            }
          },
          {
            "id":"215024",
            "text":"et ouverte en avant par les narines",
            "uniqueId":"215024_justification",
            "additionalInputJson":{
              "isTrue":true
            }
          }
        ]°
        
        
        la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives en dehors du Json) : 
        °[
          {
            "id":"214719",
            "isChanged":true,
            "newText":"Dans l'énoncé nous parlons d'écart, Nous avons donc bien un paramètre de dispersion !",
            "uniqueId":"214719_justification",
            "explicationString":"Reformulation de la correction",
            "additionalJson":{
              "isTrue":true,
              "customString":null
            }
          },
          {
            "id":"214567",
            "isChanged":true,
            "newText":"L'énoncé est faux car le chiffre '3,075' comporte 4 chiffres significatif et non 3 comme l'indique l'énoncé.",
            "uniqueId":"214567_justification",
            "explicationString":"Enrichissement de la correction",
            "additionalJson":{
              "isTrue":false,
              "customString":null
            }
          },
          {
            "id":"215024",
            "isChanged":true,
            "newText":"La cavité nasale est bien ouverte en arrière par les choanes, conformément à l'énoncé.",
            "uniqueId":"215024_justification",
            "explicationString":"Reformulation de la justification",
            "additionalJson":{
              "isTrue":true,
              "customString":null
            }
          }
        ]°

        
      `,
    context: null,
    objectToModify: null,
    customInstructions: null // Ajouter dynamiquement le cours/ matière
  },
  CUSTOM_PROMPT: {
    role:FRAGMENT_ROLE_CUSTOM,
    objectif:FRAGMENT_OBJECTIF_CUSTOM,
    exemple: `
      Exemple 1 : 
      Si je te donne cette instruction "Mon instruction à laquelle tu dois t'adapter est la suivante : rend le texte plus formel"et cette array en entrée : 
      °[{
        "id":"racine1",
        "uniqueId":"racine234",
        "text":"Salut, tu veux venir à la réunion demain ?"
      }]°
      
      la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives) : °[{
        "id":"racine1",
        "uniqueId":"racine234",
        "isChanged":true,
        "newText":"Bonjour, seriez-vous disponible pour assister à la réunion de demain ?"
      }]°
      
      Exemple 2 : 
      Si je te donne cette instruction : "Ajouter une note d'optimisme." et cette array en entrée : 
      °[{
        "id":"boudoire6698",
        "uniqueId":"Adfe98",
        "text":"Les résultats n'ont pas été à la hauteur des attentes."
      }],
      la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives) : [{
        "id":"boudoire6698",
        "uniqueId":"Adfe98",
        "isChanged":true,
        "newText":"Bien que les résultats n'aient pas totalement répondu aux attentes, ils ouvrent la voie à de nouvelles opportunités d'amélioration."
      }]°
      
      Exemple 3 : 
      Si je te donne l'instruction suivante : "change chaque lettre par des 'e'" et cette array en entrée : 
      °[{
        "id":"nouvelExemple69",
        "uniqueId":"zinzolin",
        "text":"J'aime les pommes"
      },{
        "id":"jeSuisUnePhrase",
        "uniqueId":"prismaradite",
        "text":"e'eee ee eeee"
      }]°
      
      la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives) 
      °[{
        "id":"nouvelExemple69",
        "uniqueId":"zinzolin",
        "isChanged":true,
        "newText":"e'eeee eee eeeeee"
        },{
        "id":"jeSuisUnePhrase",
        "uniqueId":"prismaradite",
        "isChanged":false,
        "newText":null
      }]°
      `,
    formatInput: FRAGMENT_INPUT_FORMAT,
    formatAttendu: FRAGMENT_OUTPUT_FORMAT,
    objectToModify: null,
    customInstructions: null
  },
  CUSTOM_CORRECTION_PROMPT:{
    role:FRAGMENT_ROLE_CUSTOM,
    objectif:FRAGMENT_OBJECTIF_CUSTOM,
    formatInput:FRAGMENT_CORRECTION_CUSTOM_INPUT_FORMAT,
    formatAttendu:FRAGMENT_CORRECTION_CUSTOM_OUTPUT_FORMAT,
    objectToModify:null,
    customInstructions:null,
    exemple: `
      Voici des exemples de ce que je veux.
      
      Exemple 1 : 
      Si je te donne comme context : 
        'context : {
          "titreQuestion":"Considérons cet ensemble de valeurs : A = 10; 12; 14; 15; 16; 18. Parmi les affirmations suivantes, veuillez identifier celles qui sont correctes :"
          "Cours associés ":"Mathématique"
        }'
        
      comme instruction : "Mon instruction à laquelle tu dois t'adapter est la suivante : enrichi l'énoncé et la correction"

      et cette array en entrée : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_énoncé",
            "text":"Est-ce que L'ensemble a uniquement des chiffre paires ?"
          },
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "text":null
          },
          {
            "id":"2011",
            "uniqueId":"2011_énoncé",
            "text":"Est-ce que L'ensemble a uniquement des chiffre Impaires ?"
          },
          {
            "id":"2011",
            "uniqueId":"2011_justification",
            "text":"C'est faux"
          },
          {
            "id":"2012",
            "uniqueId":"2012_énoncé",
            "text":"La moyenne de la série est 15."
          },
          {
            "id":"2012",
            "uniqueId":"2012_justification",
            "text":"c'est vrai !"
          },
          {
            "id":"2012",
            "uniqueId":"2012_énoncé",
            "text":"Le plus grand chiffre de la série est 18"
          },
          {
            "id":"2012",
            "uniqueId":"2012_justification",
            "text":"c'est vrai"
          }
        ]°
        
      la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives en dehors du Json) : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_énoncé",
            "newText":"Soit la suite A, est-ce que cette suite contient uniquement des chiffres paires ?",
            "isChanged":true,
            "explicationString":"Enrichissement de l'énoncé"
          },
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "newText":"Cela est faux, bien qu'elle contienne les chiffres pairs suivant : '10,12,14,16,18', le chiffre '15' est impaire",
            "isChanged":true,
            "explicationString":"Création de la correction"
          },
          {
            "id":"2011",
            "uniqueId":"2011_énoncé",
            "newText":"Soit la suite A, est-ce que cette suite contient uniquement des chiffres impaires ?",
            "isChanged":true,
            "explicationString":"Enrichissement de l'énoncé"
          },
          {
            "id":"2011",
            "uniqueId":"2011_justification",
            "newText":"Cela est faux, bien qu'elle contienne le chiffre impaire suivant : '15' tous les autres chiffres de la série ('10,12,14,16,18') sont pairs",
            "isChanged":true,
            "explicationString":"Enrichissement de la correction"
          },
          {
            "id":"2012",
            "uniqueId":"2012_énoncé",
            "newText":"Soit la suite A, est-ce que la moyenne de la série vaut 15 ?",
            "isChanged":true,
            "explicationString":"Enrichissement de l'énoncé"
          },
          {
            "id":"2012",
            "uniqueId":"2012_justification",
            "newText":"C'est faux, lorsque l'on calcule la moyenne de la série, nous trouvons 14.16666. Pour trouver ce chiffre, il faut faire (10+12+14+15+16+18) / 6.",
            "isChanged":true,
            "explicationString":"Correction de la correction (la correction expliquait à tord que la moyenne de la série A vallait 15) et enrichissement"
          },
          {
            "id":"2013",
            "uniqueId":"2013_énoncé",
            "newText":"Soit la série A, est-ce que le plus grand chiffre de cette série est 18 ?",
            "isChanged":true,
            "explicationString":"Enrichissement de l'énoncé"
          },
          {
            "id":"2013",
            "uniqueId":"2013_justification",
            "newText":"C'est vrai, le plus grand chiffre de la série est bien 18",
            "isChanged":true,
            "explicationString":"Enrichissement de la correction"
          }
        ]°
        
        
      Exemple 2 : 
        
      Si je te donne comme context : 
        'context : {
          "titreQuestion":"Considérons cet ensemble de valeurs : A = 10; 12; 14; 15; 16; 18. Parmi les affirmations suivantes, veuillez identifier celles qui sont correctes :"
          "Cours associés ":"Mathématique"
        }'
        
      comme instruction : "Mon instruction à laquelle tu dois t'adapter est la suivante : enrichi uniquement l'énoncé"

      et cette array en entrée : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_énoncé",
            "text":"Est-ce que L'ensemble a uniquement des chiffre paires ?"
          },
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "text":""
          }
        ]°
        
        
        la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives en dehors du Json) : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_énoncé",
            "newText":"Soit la suite A, est-ce que cette suite contient uniquement des chiffres paires ?",
            "isChanged":true,
            "explicationString":"Enrichissement de l'énoncé"
          },
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "newText":"",
            "isChanged":false,
            "explicationString":"Pas de modification de correction comme demandé"
          }
        ]°
        
        
        
      Exemple 3 : 
        
      Si je te donne comme context : 
        'context : {
          "titreQuestion":"Considérons cet ensemble de valeurs : A = 10; 12; 14; 15; 16; 18. Parmi les affirmations suivantes, veuillez identifier celles qui sont correctes :"
          "Cours associés ":"Mathématique"
        }'
        
      comme instruction : "Mon instruction à laquelle tu dois t'adapter est la suivante : Améliore"

      et cette array en entrée : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_énoncé_helper",
            "text":"Est-ce que L'ensemble a uniquement des chiffre paires ?"
          },
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "text":""
          }
        ]°
        
        la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives en dehors du Json) : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "newText":"Cela est faux, bien qu'elle contienne les chiffres pairs suivant : '10,12,14,16,18', le chiffre '15' est impaire",
            "isChanged":false,
            "explicationString":"Enrichissement de la correction"
          }
        ]°
        
      
      
      Exemple 4 : 
        
      Si je te donne comme context : 
        'context : {
          "titreQuestion":"Considérons cet ensemble de valeurs : A = 10; 12; 14; 15; 16; 18. Parmi les affirmations suivantes, veuillez identifier celles qui sont correctes :"
          "Cours associés ":"Mathématique"
        }'
        
      comme instruction : "Mon instruction à laquelle tu dois t'adapter est la suivante : Améliore le texte"

      et cette array en entrée : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_énoncé",
            "text":"Est-ce que L'ensemble a uniquement des chiffre paires ?"
          },
          {
            "id":"2010",
            "uniqueId":"2010_justification_helper",
            "text":""
          }
        ]°
        
        la réponse attendu est UNIQUEMENT (je ne veux pas de phrase explicatives en dehors du Json) : 
        °[
          {
            "id":"2010",
            "uniqueId":"2010_énoncé",
            "newText":"Soit la suite A, est-ce que cette suite contient uniquement des chiffres paires ?",
            "isChanged":true,
            "explicationString":"Enrichissement de l'énoncé"
          },
        ]°
        
        
      Je te rapelle que tu ne dois JAMAIS retourner des phrases en dehors de la structure que je t'ai défini. Sinon cela casse la string que je parse pour faire un Json.
      `,
  }
};

/* Big exemple
        'context : {
          "titreQuestion":"Considérons cet ensemble de valeurs : A = 10; 12; 14; 15; 16; 18. Parmi les affirmations suivantes, veuillez identifier celles qui sont correctes :"
          "Cours associés ":"Mathématique"
        }'


        [
          {
            "id":"2010",
            "uniqueId":"2010_énoncé",
            "text":"Est-ce que L'ensemble a uniquement des chiffre paires ?",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "text":null,
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2011",
            "uniqueId":"2011_énoncé",
            "text":"Est-ce que L'ensemble a uniquement des chiffre Impaires ?",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2011",
            "uniqueId":"2011_justification",
            "text":"C'est faux ",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2012",
            "uniqueId":"2012_énoncé",
            "text":"La moyenne de la série est 15.",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2012",
            "uniqueId":"2012_justification",
            "text":"c'est vrai !",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2013",
            "uniqueId":"2013_énoncé",
            "text":"Le plus grand chiffre de la série est 18",
            "additionalInputJson":{
              "isTrue":false
            }
          },
          {
            "id":"2013",
            "uniqueId":"2013_justification",
            "text":"c'est vrai",
            "additionalInputJson":{
              "isTrue":false
            }
          }
        ]


        [
          {
            "id":"2010",
            "uniqueId":"2010_énoncé",
            "newText":"Soit la suite A, est-ce que cette suite contient uniquement des chiffres paires ?",
            "isChanged":true,
            "explicationString":"Enrichissement de l'énoncé"
          },
          {
            "id":"2010",
            "uniqueId":"2010_justification",
            "newText":"Cela est faux, bien qu'elle contienne les chiffres pairs suivant : '10,12,14,16,18', le chiffre '15' est impaire",
            "isChanged":true,
            "explicationString":"Création de la correction"
          },
          {
            "id":"2011",
            "uniqueId":"2011_énoncé",
            "newText":"Soit la suite A, est-ce que cette suite contient uniquement des chiffres impaires ?",
            "isChanged":true,
            "explicationString":"Enrichissement de l'énoncé"
          },
          {
            "id":"2011",
            "uniqueId":"2011_justification",
            "newText":"Cela est faux, bien qu'elle contienne le chiffre impaire suivant : '15' tous les autres chiffres de la série ('10,12,14,16,18') sont pairs",
            "isChanged":true,
            "explicationString":"Enrichissement de la correction"
          },
          {
            "id":"2012",
            "uniqueId":"2012_énoncé",
            "newText":"Soit la suite A, est-ce que la moyenne de la série vaut 15 ?",
            "isChanged":true,
            "explicationString":"Enrichissement de l'énoncé"
          },
          {
            "id":"2012",
            "uniqueId":"2012_justification",
            "newText":"C'est faux, lorsque l'on calcule la moyenne de la série, nous trouvons 14.16666. Pour trouver ce chiffre, il faut faire (10+12+14+15+16+18) / 6.",
            "isChanged":true,
            "explicationString":"Correction de la correction (la correction expliquait à tord que la moyenne de la série A vallait 15) et enrichissement"
          },
          {
            "id":"2013",
            "uniqueId":"2013_énoncé",
            "newText":"Soit la série A, est-ce que le plus grand chiffre de cette série est 18 ?",
            "isChanged":true,
            "explicationString":"Enrichissement de l'énoncé"
          },
          {
            "id":"2013",
            "uniqueId":"2013_justification",
            "newText":"C'est vrai, le plus grand chiffre de la série est bien 18",
            "isChanged":true,
            "explicationString":"Enrichissement de la correction"
          }
        ]


 */
