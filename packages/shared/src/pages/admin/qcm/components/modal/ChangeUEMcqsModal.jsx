import { QUERY_MES_UES_LIGHT } from '@/shared/graphql/cours.js'
import { MUTATION_UPDATE_QCM } from '@/shared/pages/admin/qcm/components/modal/CreateEditQcmModal.jsx'
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js'
import {
  Button,
  Form,
  message,
  Modal,
  Select,
} from 'antd'
import { useMutation, useQuery } from '@apollo/client'
import React from 'react'
import { SmallErrorsAlert } from '@/shared/components/ErrorResult.jsx'
import { useTranslation } from 'react-i18next';

export const ChangeUEMcqsModal = ({
    closeModalHandler,
    isVisible,
    mcqIds,
  },
) => {
  const {t} = useTranslation();
  const [form] = Form.useForm()
  const [Mutation, { loading, data, error }] = useMutation(MUTATION_UPDATE_QCM);
  const handleFinish = async data => {
    try {
      const newQcm = {
        UEId: parseInt(data.UEId),
        ue: parseInt(data.UEId)
      }
      const msgKey = { key: 'isLoading' }
      message.loading({ content: `Changement de l'UE des QCMs en cours...`, ...msgKey })
      await Promise.all(mcqIds.map(async m => {
        await Mutation({ variables: { id: m, qcm: newQcm } })
      }))
      message.success({ content: `Changement d'UE terminé`, ...msgKey, duration: 3 })
      await closeModalHandler()
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e)
      console.error(e)
    }
  }

  const { data: { mesUEs = null } = {} } = useQuery(QUERY_MES_UES_LIGHT, { fetchPolicy: 'cache-and-network' })

  return (
    <Modal
      title={`Changer les QCMs d'UE`}
      open={isVisible}
      onCancel={closeModalHandler}
      footer={null}
      closable
      confirmLoading={false}
    >
      <React.Fragment>
        {/* Show small error(s) if needed */}
        <SmallErrorsAlert error={error} loading={loading}/>
        <Form
          layout="horizontal"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 18 }}
          onFinish={handleFinish}
          form={form}
          initialValues={{}}
        >
          <Form.Item name="UEId" label={t('general.Subject')}>
            <Select placeholder={t('ChooseASubject')}>
              {(mesUEs && mesUEs.map(ue => (
                <Select.Option key={ue.id} value={ue.id}>{ue.name}: {ue.description}</Select.Option>
              )))}
            </Select>
          </Form.Item>

          <Form.Item>
            <Button htmlType="submit" type="primary" loading={loading}>
              {t('ChangeSubjectExercices')}
            </Button>
          </Form.Item>
        </Form>
      </React.Fragment>

    </Modal>
  )
}
