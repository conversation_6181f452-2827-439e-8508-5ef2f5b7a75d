import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import {
  MUTATION_UPDATE_QUESTION,
  QuestionAnswerType
} from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal.jsx';
import {
  BgColorsOutlined,
  CheckCircleOutlined,
  CheckSquareOutlined,
  FormOutlined,
  IdcardOutlined,
  NumberOutlined,
  OrderedListOutlined
} from '@ant-design/icons';
import FileTextOutlined from '@ant-design/icons/lib/icons/FileTextOutlined';
import { useMutation } from '@apollo/client';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { QuestionType } from '@/shared/pages/admin/qcm/components/modal/components/QuestionType';
import { Button, Image } from 'antd';
import checkboxImage from '@/shared/assets/GIFs/CHECKBOX.gif';
import radioImage from '@/shared/assets/GIFs/RADIO.gif';
import freeTextImage from '@/shared/assets/GIFs/FREE_TEXT.gif';
import alphanumericalOrNumericalImage from '@/shared/assets/GIFs/ALPHANUMERICAL_OR_NUMERICAL.gif';
import schemaPointAndClickImage from '@/shared/assets/GIFs/SCHEMA_POINT_AND_CLICK.gif';
import schemaToCompleteImage from '@/shared/assets/GIFs/SCHEMA_FILL_IN_LEGENDS.gif';
import fillInTheBlanksImage from '@/shared/assets/GIFs/FILL_IN_THE_BLANKS.gif';
import flashcardImage from '@/shared/assets/GIFs/FLASHCARD.gif';
import useMediaQuery from '@/shared/hooks/useMediaQuery';

export function ChooseQuestionAnswerType({ question, setHasAnswerType, refetch }) {
  const { t } = useTranslation();
  const [updateQuestionMutation, { loading }] = useMutation(MUTATION_UPDATE_QUESTION);
  const [selectedType, setSelectedType] = React.useState(QuestionAnswerType.CHECKBOX);
  const wrapSelector = useMediaQuery('(max-width: 729px)');

  const handleChange = async (answerType) => {
    /* Add if it's freetext or checkbox or radio */
    const isCheckbox = answerType === QuestionAnswerType.CHECKBOX; // if false => it's radio
    const isAnswerFreeText = answerType === QuestionAnswerType.FREE_TEXT;
    const isAnswerUniqueChoiceInList = answerType === QuestionAnswerType.UNIQUE_CHOICE_IN_LIST;
    const isAnswerMultipleChoiceInList = answerType === QuestionAnswerType.MULTIPLE_CHOICE_IN_LIST;
    let type =
      answerType === QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL
        ? QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL
        : null;

    if (answerType === QuestionAnswerType.SCHEMA_POINT_AND_CLICK) {
      type = QuestionAnswerType.SCHEMA_POINT_AND_CLICK;
    }
    if (answerType === QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS) {
      type = QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS;
    }
    if (answerType === QuestionAnswerType.FILL_IN_THE_BLANKS) {
      type = QuestionAnswerType.FILL_IN_THE_BLANKS;
    }
    if (answerType === QuestionAnswerType.FLASHCARD) {
      type = QuestionAnswerType.FLASHCARD;
    }
    if (answerType === QuestionAnswerType.REORDER_ELEMENTS) {
      type = QuestionAnswerType.REORDER_ELEMENTS;
    }

    // Ici, on a la modif, peut bloquer le back mais c'est contrôlé => Componant utilisé une unique fois, à la création d'une question, donc l'attribution de la scale est gérée dans le back à l'init
    const newQuestion = {
      isCheckbox,
      isAnswerFreeText,
      isAnswerUniqueChoiceInList,
      isAnswerMultipleChoiceInList,
      type
    };
    await updateQuestionMutation({
      variables: { id: question.id_question, question: newQuestion }
    });

    refetch();
  };

  const typesShown = [
    {
      type: QuestionAnswerType.CHECKBOX,
      icon: <CheckCircleOutlined />,
      name: 'QCM',
      description: '- Plusieurs réponses possible',
      disabled: false,
      image: checkboxImage
    },
    {
      type: QuestionAnswerType.RADIO,
      icon: <CheckSquareOutlined />,
      name: 'QCU',
      description: '- Une seule réponse possible',
      disabled: false,
      image: radioImage
    },
    {
      type: QuestionAnswerType.SCHEMA_POINT_AND_CLICK,
      icon: <BgColorsOutlined />,
      name: 'Schéma P&C',
      description: (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
          <span>- Une ou plusieurs légendes à positionner</span>
        </div>
      ),
      disabled: false,
      image: schemaPointAndClickImage
    },
    {
      type: QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS,
      icon: <BgColorsOutlined />,
      name: 'Schéma à compléter',
      description: (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
          <span>- Une ou plusieurs légendes à compléter</span>
        </div>
      ),
      disabled: false,
      image: schemaToCompleteImage
    },
    {
      type: QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL,
      icon: <NumberOutlined />,
      name: 'Alphanumérique',
      description: '- Table de réponse prédéfinies (Lettre + chiffres)',
      disabled: false,
      image: alphanumericalOrNumericalImage
    },
    {
      type: QuestionAnswerType.FILL_IN_THE_BLANKS,
      icon: <FileTextOutlined />,
      name: 'Texte à trous',
      description: (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
          <span>- Un ou plusieurs trous à remplir</span>
          <span>- Indices possibles</span>
          <span>- Fautes d&apos;orthographe tolérables</span>
        </div>
      ),
      disabled: false,
      image: fillInTheBlanksImage
    },
    {
      type: QuestionAnswerType.FLASHCARD,
      icon: <IdcardOutlined />,
      name: 'Flashcard',
      disabled: false,
      description: (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
          <span>- Une flashcard qui se retourne avec énoncé et réponse.</span>
        </div>
      ),
      image: flashcardImage
    },
    {
      type: QuestionAnswerType.REORDER_ELEMENTS,
      icon: <OrderedListOutlined />,
      name: t('ReorderElements.Name'),
      disabled: false,
      description: (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
          <span>- {t('ReorderElements.Description1')}</span>
          <span>- {t('ReorderElements.Description2')}</span>
        </div>
      ),
      image: flashcardImage // TODO: Add specific image for reorder
    },
    {
      type: QuestionAnswerType.FREE_TEXT,
      icon: <FormOutlined />,
      name: 'Texte libre',
      description: (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
          <span>Réponse Courte</span>
          <span>Réponse longue</span>
        </div>
      ),
      disabled: false,
      image: freeTextImage
    }
  ];

  const exercicePreview = useMemo(
    () => (
      <div
        style={{
          maxHeight: !wrapSelector && 350,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          paddingLeft: 24,
          minWidth: wrapSelector ? 1 : 350
        }}
      >
        <Image
          width={'100%'}
          height={'100%'}
          src={typesShown.find((type) => type.type === selectedType)?.image}
        />
      </div>
    ),
    [selectedType, typesShown, wrapSelector]
  );

  return (
    <div style={{ textAlign: 'center', margin: 'auto' }}>
      <h2 style={{ marginTop: 0 }}>{t('ExerciceFormat')} </h2>
      {loading ? (
        <SpinnerCentered />
      ) : (
        <>
          {wrapSelector && exercicePreview}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', padding: '0 24px' }}>
            <div
              style={{
                gridColumn: wrapSelector ? '1 / 3' : '1 / 2',
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: 12,
                margin: '24px 0'
              }}
            >
              {typesShown?.map((answerType) => (
                <QuestionType
                  key={`type-${answerType.type}`}
                  disabled={answerType.disabled}
                  onClick={() => setSelectedType(answerType.type)}
                  isSelected={selectedType === answerType.type}
                  title={answerType.name}
                  description={answerType.description}
                />
              ))}
            </div>
            {!wrapSelector && exercicePreview}
          </div>
          <div style={{ display: 'flex', marginTop: 48, padding: '0 24px' }}>
            <Button type={'primary'} onClick={async () => await handleChange(selectedType)}>
              {t('CreateExercice')}
            </Button>
          </div>
        </>
      )}
    </div>
  );
}
