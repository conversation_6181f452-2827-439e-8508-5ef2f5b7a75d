import { useQuery } from '@apollo/client';
import { QUERY_PROCESS_IMAGE_MATHPIX } from '@/shared/graphql/mathpix';
import { message, Upload, Divider, Typography, Badge, Spin, Popover } from 'antd';
import { onErrorShowErrorsFunction } from '@/shared/utils/utils';
import { DeleteTwoTone, EditTwoTone, FunctionOutlined, PlusCircleTwoTone } from '@ant-design/icons';
import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { EQUATION_OBJECT_KEYS, ImageToLatexContext } from './ImageToLatexContextProvider';
import katex from 'katex';
import '@/shared/utils/katex/katex.min.css';

const { Paragraph } = Typography;

const KatexEquation = ({ equation }) => {
  const html = katex.renderToString(equation, {
    throwOnError: false // Pour éviter de casser l'affichage si l'équation est incorrecte
  });

  return <div dangerouslySetInnerHTML={{ __html: html }} />;
};

export const ImageToLatex = ({
  minWidth = '300px',
  maxWidth = '300px',
  maxHeight = '300px',
  handleSaveNewEquationString = null,
  handleEditEquationString = null
}) => {
  /*
    minWidth défini la largeur minimale du componant
    maxWidth défini défini la largeur max du componant => Lorsque des equations vont être "longues", le componant va s'élargir jusqu'à cette valeur, ensuite, overflow-x sur les equations
    maxHeight defini la largeur max du componant
  */

  const { t } = useTranslation();
  const {
    equationObject, // L'objet de type {1:{equation,index},2:{}} qui contient les équations en mémoire du componant
    addEquation, // Hook qui permet d'ajouter une équation en mémoire. => addEquation(string)
    deleteEquation, // Hook qui permet de supprimer une équation de la mémoire, prend l'index en argument => deleteEquation(index)
    mathpixConfigId, // ConfigMathpix => liaison avec l'intégration
    mathpixConfigLoading // Si on est en train de loader la config mathpix
  } = useContext(ImageToLatexContext);

  const isComponantReady = mathpixConfigLoading === false && mathpixConfigId;
  const componantWithoutConfig = mathpixConfigLoading === false && !mathpixConfigId;

  const AUTHORIZED_FILE_EXTENSIONS = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.bmp',
    '.tiff',
    '.tif',
    '.svg',
    '.webp',
    '.heif',
    '.heic'
  ];

  const [selectedUpload, setSelectedUpload] = useState();

  const { refetch, loading } = useQuery(QUERY_PROCESS_IMAGE_MATHPIX, {
    variables: { file: selectedUpload, mathpixConfigId },
    skip: !selectedUpload || !mathpixConfigId,
    onError: (errors) => {
      onErrorShowErrorsFunction(errors);
    },
    onCompleted: (data) => {
      const imagePixie = data?.processImage;
      const latex = imagePixie?.latex;
      addEquation(`${latex}`);
    },
    fetchPolicy: 'cache-and-network',
    notifyOnNetworkStatusChange: 'true'
  });

  const uploadImageComponant = (
    <Upload.Dragger
      name="file"
      showUploadList={false}
      disabled={!isComponantReady}
      style={{
        height: '10px',
        maxHeight: '100px',
        maxWidth: '200px',
        margin: '5px'
      }}
      beforeUpload={(file, fileList) => {
        // Recup de l'extension
        const extension = file?.name?.split('.')?.pop().toLowerCase();

        if (AUTHORIZED_FILE_EXTENSIONS.includes(`.${extension}`)) {
          setSelectedUpload(file);
          return false;
        } else {
          message.error(
            t('ImageToLatex.UploadExensionErrorText', {
              extension,
              validesExtension: AUTHORIZED_FILE_EXTENSIONS.join(', ')
            })
          );
          return Upload.LIST_IGNORE;
        }
      }}
    >
      <>
        <FunctionOutlined /> {t('ImageToLatex.UploadDraggerLabel')} {loading && <Spin />}
      </>
    </Upload.Dragger>
  );

  const showEquation = (node, index) => {
    const key = node?.[EQUATION_OBJECT_KEYS?.INDEX];

    return (
      <div
        style={{
          display: 'flex',
          gap: '5px',
          margin: '5px',
          whiteSpace: 'nowrap',
          alignItems: 'center'
        }}
        key={key}
      >
        <DeleteTwoTone
          twoToneColor={'red'}
          onClick={() => {
            deleteEquation(node?.[EQUATION_OBJECT_KEYS?.INDEX]);
          }}
        />
        <Badge count={index + 1} size={'default'} color="blue" />
        <div style={{ display: 'flex', flexGrow: '1', overflowX: 'auto' }}>
          <Paragraph
            style={{
              display: 'flex',
              alignItems: 'center',
              margin: 0,
              flexGrow: 1,
              whiteSpace: 'nowrap'
            }} // Ajout des styles ici
          >
            <KatexEquation key={key} equation={node?.[EQUATION_OBJECT_KEYS?.EQUATION]} />
          </Paragraph>
        </div>

        <Paragraph
          style={{ alignItems: 'center', margin: 0 }}
          copyable={{ text: node?.[EQUATION_OBJECT_KEYS?.EQUATION] }}
        />

        {handleEditEquationString !== null && (
          <Popover content={t('ImageToLatex.InsertInQuillEditor')} trigger="hover">
            <PlusCircleTwoTone
              style={{ cursor: 'pointer' }}
              onClick={() => handleSaveNewEquationString(node?.[EQUATION_OBJECT_KEYS?.EQUATION])}
            />
          </Popover>
        )}

        {handleEditEquationString !== null && (
          <Popover content={t('ImageToLatex.EditInQuillEditor')} trigger="hover">
            <EditTwoTone
              style={{ cursor: 'pointer' }}
              onClick={() => handleEditEquationString(node?.[EQUATION_OBJECT_KEYS.EQUATION])}
            />
          </Popover>
        )}
      </div>
    );
  };

  return (
    <div
      style={{
        minWidth: minWidth,
        width: 'min-content',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        backgroundColor: 'white'
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '5px',
          minWidth,
          maxWidth,
          maxHeight,
          overflowY: 'auto'
        }}
      >
        {Object.values(equationObject)?.map((equationNode, index) =>
          showEquation(equationNode, index)
        )}
      </div>
      {Object.keys(equationObject).length > 0 && (
        <Divider style={{ marginTop: '5px', marginBottom: '0px' }} />
      )}
      {uploadImageComponant}
      {componantWithoutConfig && <span>{t('ImageToLatex.ErrorOcrConfig')}</span>}
    </div>
  );
};

export default ImageToLatex;
