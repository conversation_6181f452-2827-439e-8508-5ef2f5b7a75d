import { InputNumber } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAiCreateAndImportContext } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';

const NumberOfQuestion = () => {
  const { t } = useTranslation();
  const { questionsToGenerate, setQuestionsToGenerate, MAX_QUESTIONS_NUMBER_TO_GENERATE } =
    useAiCreateAndImportContext();

  return (
    <InputNumber
      style={{ width: '500px' }}
      value={questionsToGenerate}
      onChange={setQuestionsToGenerate}
      placeholder={t('AiQuestionCreationModal.PlaceholderNumberOfQuestionToGenerate', {
        count: MAX_QUESTIONS_NUMBER_TO_GENERATE
      })}
      min={0}
      max={MAX_QUESTIONS_NUMBER_TO_GENERATE}
    />
  );
};

export default NumberOfQuestion;
