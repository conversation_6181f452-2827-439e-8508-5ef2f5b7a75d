import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { MUTATION_UPDATE_QUESTION } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal';
import EditFullSchema from '@/shared/pages/admin/schemas/components/EditFullSchema';
import { SchemaSearchTable } from '@/shared/pages/admin/schemas/components/SearchTable/SchemaSearchTable';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { CloseOutlined } from '@ant-design/icons';
import { useMutation } from '@apollo/client';
import { Button, Card, Popconfirm } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const SchemaExerciseModes = {
  PointAndClick: 'point-and-click',
  FillInLegends: 'fill-in-legends'
};

// TODO rename car utilisé pour point and click et fill in legends
export const EditSchemaPointAndClick = ({ question, answerType, settings, setSettings, mode }) => {
  const { t } = useTranslation();

  const [updateQuestion, { loadingUpdate }] = useMutation(MUTATION_UPDATE_QUESTION);

  const [selectedSchemaId, setSelectedSchemaId] = useState(null);
  useEffect(() => {
    if (question?.schemaLibraryId) {
      setSelectedSchemaId(question?.schemaLibraryId);
    }
  }, [question]);

  // Link schema to exercise
  const onLinkSchemaToExercise = async (schema) => {
    // Update bdd
    try {
      await updateQuestion({
        variables: {
          id: question.id_question,
          question: {
            schemaLibraryId: schema.id
          }
        }
      });
      setSelectedSchemaId(schema.id);
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  return (
    <>
      {selectedSchemaId === null ? (
        <Card
          style={{ width: '100%' }}
          title={<span style={{ color: 'white' }}> SÉLECTIONNEZ LE SCHÉMA </span>}
          headStyle={{ background: primaryColor }}
          bodyStyle={{ padding: 0 }}
        >
          <SchemaSearchTable
            edit={false}
            canDelete={false}
            linkToExercise={true}
            onLinkToExercise={onLinkSchemaToExercise}
          />
        </Card>
      ) : (
        <>
          <Popconfirm
            title={t('Voulez-vous vraiment changer le schéma ?')}
            onConfirm={() => setSelectedSchemaId(null)}
          >
            <Button size={'small'} icon={<CloseOutlined />} style={{ marginBottom: 10 }}>
              Changer le schéma de cet exercice
            </Button>
          </Popconfirm>

          <EditFullSchema
            settings={settings}
            setSettings={setSettings}
            mode={mode}
            schemaId={selectedSchemaId}
            canEditCorrection={false}
            setBreadcrumbTitle={() => {
              // Do nothing
            }}
          />
        </>
      )}
    </>
  );
};
