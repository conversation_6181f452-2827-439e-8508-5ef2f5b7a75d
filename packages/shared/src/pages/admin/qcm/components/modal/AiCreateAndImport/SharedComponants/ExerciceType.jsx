import { Select } from 'antd';
import React from 'react';
import { McqScaleQuestionType } from '@/shared/pages/admin/bareme/componants/subBareme';
import { useTranslation } from 'react-i18next';
import { useAiCreateAndImportContext } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';

const ExerciceType = () => {
  const { t } = useTranslation();
  const { exerciseFormat, setExerciseFormat } = useAiCreateAndImportContext();

  const EXERCISE_TYPE_SUPPORTED = [
    // On se sert de ça, pour filter les barems, il faut que McqScaleQuestionType === le type du barem
    { value: McqScaleQuestionType.MultipleChoice, label: t('AiQuestionCreationModal.LabelQcm') },
    { value: McqScaleQuestionType.UniqueChoice, label: t('AiQuestionCreationModal.LabelQcu') },
    {
      value: McqScaleQuestionType.FillInTheBlanks,
      label: t('AiQuestionCreationModal.LabelFillInTheBlanks')
    },
    {
      value: McqScaleQuestionType.FLASHCARD,
      label: t('AiQuestionCreationModal.LabelFlashcard')
    }
  ];

  return (
    <div style={{ display: 'flex', margin: '5px' }}>
      <span style={{ minWidth: '200px', textAlign: 'end', alignContent: 'center' }}>
        {t('AiQuestionCreationModal.QuestionTypeSelecterLabel')}
      </span>
      <Select
        size={'large'}
        value={exerciseFormat}
        options={EXERCISE_TYPE_SUPPORTED}
        onChange={setExerciseFormat}
        style={{ width: '500px', marginLeft: '5px' }}
      />
    </div>
  );
};

export default ExerciceType;
