import ExoQuillEditor, { QUILL_PRESET } from '@/shared/components/ExoQuill/ExoQuillEditor';
import { ExoQuillToolbarPresets } from '@/shared/components/ExoQuill/utils';
import { NotionTag } from '@/shared/components/Notion/NotionTag.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import UserLogs from '@/shared/components/User/UserLogs.jsx';
import {
  QUERY_ELEMENTS_IN_QUESTION,
  QUERY_ELEMENTS_IN_QUESTION_FOOTER,
  QUERY_MES_UES_LIGHT
} from '@/shared/graphql/cours.js';
import { MUT_AUTO_SET_NOTIONS_TO_QUESTION_FROM_INPUT } from '@/shared/graphql/notions.js';
import { UPDATE_COUR_FROM_QUESTION } from '@/shared/graphql/questions.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import {
  EditNotionsLink,
  NotionTarget
} from '@/shared/pages/admin/notions/components/EditNotions.jsx';
import { ChooseQuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/ChooseQuestionAnswerType.jsx';
import { ApercuQuestionCorrection } from '@/shared/pages/admin/qcm/components/modal/components/ApercuQuestion.jsx';
import { EditAnswersQuestion } from '@/shared/pages/admin/qcm/components/modal/components/EditAnswersQuestion.jsx';
import EditFillInTheBlanks from '@/shared/pages/admin/qcm/components/modal/components/FillInTheBlanks/EditFillInTheBlanks';
import { EditReorderElements } from '@/shared/pages/admin/qcm/components/modal/components/EditReorderElements.jsx';
import QuestionHierarchy from '@/shared/pages/admin/qcm/components/modal/components/QuestionHierarchy.jsx';
import { EditSchemaPointAndClick } from '@/shared/pages/admin/qcm/components/modal/components/Schemas/EditSchemaPointAndClick';
import { TypeQcmToQuestionManager } from '@/shared/pages/admin/qcm/components/modal/components/TypeQcmToQuestionManager.jsx';
import { QuestionSearchActionsTypes } from '@/shared/pages/admin/qcm/components/modal/QuestionSearchTable.jsx';
import {
  ExerciseEditionContext,
  ExerciseEditionContextProvider
} from '@/shared/pages/admin/qcm/context/ExerciseEditionContext.jsx';
import { QuestionContext } from '@/shared/pages/admin/qcm/context/QuestionContext.jsx';
import FormationEditableElement from '@/shared/pages/formations/components/FormationEditableElement.jsx';
import { CreateEditFormationElementModal } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal.jsx';
import { FormationContextProvider } from '@/shared/pages/formations/context/FormationContext.jsx';
import { ELEMENTS_TYPE } from '@/shared/services/formations.js';
import { getLanguageName, missingTranslation, tr } from '@/shared/services/translate.js';
import {
  IS_DEV,
  isAptoria,
  isMedisupPPS,
  showGqlErrorsInMessagePopupFromException
} from '@/shared/utils/utils.js';
import {
  CloseOutlined,
  ControlOutlined,
  DownloadOutlined,
  EditTwoTone,
  ExclamationCircleTwoTone,
  PlusCircleTwoTone,
  QuestionCircleTwoTone
} from '@ant-design/icons';
import {
  Button,
  Card,
  Checkbox,
  Drawer,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  notification,
  Popover,
  Radio,
  Select,
  Slider,
  Space,
  Tabs,
  Tag,
  Tooltip,
  Image
} from 'antd';
import { gql, useMutation, useQuery } from '@apollo/client';
import { debounce } from 'lodash';
import React, {
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from 'react';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult.jsx';
import { AnimatePresence } from 'framer-motion/dist/framer-motion';
import { SimpleMoveTransition } from '@/shared/assets/transitions/SimpleMoveTransition.jsx';
import imageScaleNotation from '@/shared/assets/imageScaleNotation.png';
import imageTrueFalse from '@/shared//assets/imageTrueFalse.png';
import { useTranslation } from 'react-i18next';
import { HierarchySelecter, validesTypes } from '@/shared/components/HierarchySelecter';
import { AiEnhancementMenu } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/AiEnhancementMenu';
import useMediaQuery from '@/shared/hooks/useMediaQuery';
import Link from 'umi/link';
import { enhancementType } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/sharedConstantesFiles';
import { AiEnhancementContextProvider } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/AiEnhancementContextProvider';
import { overrideDataFonctionTitleFormater } from '@/shared/services/AiEnhancementMenu';
import ImageToLatexContextProvider from '@/shared/pages/admin/qcm/components/modal/components/ImageToLatex/ImageToLatexContextProvider';
import {
  MUTATION_ACCEPT_QUESTION,
  MUTATION_REJECT_QUESTION
} from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/AiExercisePrompts';

import { downloadFile, FILE_TYPE } from '@/shared/services/file';
import InfoCircleOutlined from '@ant-design/icons/lib/icons/InfoCircleOutlined';
import { FLASHCARD_RESPONSE_TYPE } from '@/shared/pages/admin/qcm/bareme/modal/CreateEditBaremeModal';
import { McqScaleQuestionType } from '@/shared/pages/admin/bareme/componants/subBareme';
import { EditBaremeWithMcqScaleType } from '@/shared/pages/admin/qcm/components/EditBaremeWithMcqScaleType';

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE'
};
export const QuestionAnswerType = {
  RADIO: 'RADIO',
  CHECKBOX: 'CHECKBOX',
  FREE_TEXT: 'FREE_TEXT',

  SCHEMA_POINT_AND_CLICK: 'SCHEMA_POINT_AND_CLICK',
  SCHEMA_FILL_IN_LEGENDS: 'SCHEMA_FILL_IN_LEGENDS',

  FILL_IN_THE_BLANKS: 'FILL_IN_THE_BLANKS',

  ALPHANUMERICAL: 'ALPHANUMERICAL',
  NUMERICAL: 'NUMERICAL',
  ALPHANUMERICAL_OR_NUMERICAL: 'ALPHANUMERICAL_OR_NUMERICAL',

  FLASHCARD: 'FLASHCARD',

  REORDER_ELEMENTS: 'REORDER_ELEMENTS',

  /* Not used yet */
  UNIQUE_CHOICE_IN_LIST: 'UNIQUE_CHOICE_IN_LIST',
  MULTIPLE_CHOICE_IN_LIST: 'MULTIPLE_CHOICE_IN_LIST',

  TRUE_OR_FALSE_OR_UNDEFINED: 'TRUE_OR_FALSE_OR_UNDEFINED'
};
export const CREATION_TYPE = {
  GPT_CREATION: 'gptCreation',
  GPT_IMPORTATION: 'gptImportation',
  GPT_IMPORTATION_PDF: 'gptImportationPdf',
  GPT_IMPORTATION_PICTURE: 'gptImportationPicture',
  GPT_IMPORTATION_TEXT: 'gptImportationText',
  GPT_IMPORTATION_COURS: 'gptImportationCourse',
  HUMAN: 'human'
};

export const MUTATION_CREATE_QUESTION = gql`
  mutation CreateQuestion($question: QuestionInput!) {
    createQuestion(question: $question) {
      id_question
    }
  }
`;
export const MUTATION_UPDATE_QUESTION = gql`
  mutation UpdateQuestion($id: ID!, $question: QuestionInput!) {
    updateQuestion(id: $id, question: $question)
  }
`;

export const MUTATION_UPDATE_QUESTION_ORDER = gql`
  mutation changeQuestionOrder($id: ID!, $qcmId: ID!, $order: Int!) {
    changeQuestionOrder(id: $id, qcmId: $qcmId, order: $order)
  }
`;

export const MUTATION_UPDATE_QUESTION_ORDER_IMPORTED = gql`
  mutation changeOrderImportedQcm($qcmId: ID, $questionId: ID, $targetQuestionId: ID) {
    changeOrderImportedQcm(
      qcmId: $qcmId
      questionId: $questionId
      targetQuestionId: $targetQuestionId
    )
  }
`;

export const defaultQuestionText =
  'Concernant les propositions suivantes, indiquez laquelle (ou lesquelles) est (ou sont) exacte(s) :';

export const defaultSchemaText = 'Consigne : Sur ce schéma, cliquez sur les légendes indiquées';

const getMutationFromModalType = (modalType) => {
  switch (modalType) {
    case ModalType.CREATE:
      return MUTATION_CREATE_QUESTION;
    case ModalType.UPDATE:
      return MUTATION_UPDATE_QUESTION;
    default:
      return MUTATION_UPDATE_QUESTION;
  }
};

const QUESTION_DETAIL_LINKED_COURS_ONLY = gql`
  query questionDetails($id: ID!) {
    question(id: $id) {
      id_question
      isLinkedToCourses
      linkedCours {
        id
        name
        name_en
        name_es
        name_it
        name_de
        text
        text_en
        text_es
        text_it
        text_de
        ueCategory {
          id
          name
          name_en
          name_es
          name_it
          name_de
          ue {
            id
            name
            name_en
            name_es
            name_it
            name_de
            description
            description_en
            description_es
            description_it
            description_de
            type
          }
        }
      }
    }
  }
`;

export const QUESTION_DETAIL_FRAGMENT = gql`
  fragment QuestionEditionDetailFragment on Question {
    id_question
    id_sous_categorie
    order
    id_qcm
    question
    question_en
    question_es
    question_it
    question_de
    url_image_q
    url_image_explication
    definedDifficulty
    calculatedDifficulty
    evaluateCertainty
    allowComments
    isCheckbox
    isPublished
    isAnswerFreeText
    isAnswerUniqueChoiceInList
    isAnswerMultipleChoiceInList
    autoAddNotions
    explications
    maxPoints
    type
    isLinkedToCourses
    schemaLibraryId
    settings
    creationType
    mathpixieId
    types {
      id
      name
    }
    typeQuestion {
      id
      name
    }
    linkedCours {
      id
      name
      name_en
      name_es
      name_it
      name_de
      text
      text_en
      text_es
      text_it
      text_de
      ueCategory {
        id
        name
        name_en
        name_es
        name_it
        name_de
        ue {
          id
          name
          name_en
          name_es
          name_it
          name_de
          description
          description_en
          description_es
          description_it
          description_de
          type
        }
      }
    }
    sousCategorie {
      id
      name
      name_en
      name_es
      name_it
      name_de
    }
    mcqScale {
      id
      name
    }
    parentQcms {
      id_qcm
      titre
      titre_en
      titre_es
      titre_it
      titre_de
      annee
      UE {
        id
        name
        name_en
        name_es
        name_de
        name_it
      }
    }
    isAiGenerated
    isAiGenerationError
    aiGenerationHasBeenValidated
  }
`;
const QUERY_QUESTION_DETAILS = gql`
  ${QUESTION_DETAIL_FRAGMENT}
  query questionDetails($id: ID!) {
    question(id: $id) {
      ...QuestionEditionDetailFragment
      parentsQuestions {
        ...QuestionEditionDetailFragment
      }
      childrensQuestions {
        ...QuestionEditionDetailFragment
      }
    }
  }
`;

const QUERY_QUESTION_NOTIONS = gql`
  query questionNotions($id: ID!) {
    question(id: $id) {
      id_question
      notions {
        id
        name
        image
      }
      autoAddNotions
    }
  }
`;

/**
 * Modal edition exercice
 *
 * @param props
 * @returns {JSX.Element}
 * @constructor
 */
export const CreateEditQuestionModal = (props) => {
  return (
    <ExerciseEditionContextProvider>
      <ExerciseEdition {...props} />
    </ExerciseEditionContextProvider>
  );
};

// Petit dico qui ressence les types d'affichage disponible pour ce componant
export const EDIT_EXERCISE_COMPONANT_TYPE = {
  EXERCISE_EDITION_STANDALONE: 'questionEditionStandalone', // Modal normal d'edition "standalone" d'un exercice
  EXERCISE_EDITION_AI_CREATION_COMPONANT: 'questionEditionAiCreationComponant' // Componant dans le steper de création de question par AI => besoin du componant + custom close
};

export const ExerciseEdition = forwardRef(
  (
    {
      closeModalHandler,
      modalType,
      isVisible,
      questionNumber,
      question: qFromParent,
      UE = {},
      hasExternalQuestions = false,
      qcmId,
      componantType = EDIT_EXERCISE_COMPONANT_TYPE.EXERCISE_EDITION_STANDALONE
    },
    ref
  ) => {
    const { t, i18n } = useTranslation();
    const { enabledLanguages, appearance } = useContext(GlobalContext);
    const primaryColor = appearance?.primaryColor;

    const [form] = Form.useForm();
    const { hasUnsavedChanges, setHasUnsavedChanges } = useContext(QuestionContext);

    const { answerValidationArray } = useContext(ExerciseEditionContext);

    const questionId = qFromParent?.id_question;

    /*
     * Queries
     */
    const {
      data,
      loading: loadingQuestion,
      error,
      refetch
    } = useQuery(QUERY_QUESTION_DETAILS, {
      fetchPolicy: 'no-cache',
      variables: { id: questionId },
      skip: modalType === ModalType.CREATE
    });

    // Cours linked only
    const {
      data: dataLinkedCourses,
      loading: loadingLinkedCourses,
      error: errorLinkedCourses,
      refetch: refetchLinkedCourses
    } = useQuery(QUESTION_DETAIL_LINKED_COURS_ONLY, {
      fetchPolicy: 'no-cache',
      variables: { id: questionId },
      skip: modalType === ModalType.CREATE
    });

    // Notion only query
    const {
      data: dataNotions,
      error: errorNotions,
      refetch: refetchNotions
    } = useQuery(QUERY_QUESTION_NOTIONS, {
      fetchPolicy: 'no-cache',
      variables: { id: questionId },
      skip: modalType === ModalType.CREATE
    });

    // Query elements header
    const {
      data: dataElements,
      error: errorElements,
      refetch: refetchElements
    } = useQuery(QUERY_ELEMENTS_IN_QUESTION, {
      fetchPolicy: 'no-cache',
      variables: { id: questionId },
      skip: modalType === ModalType.CREATE
    });

    // Query elements footer
    const {
      data: dataElementsFooter,
      error: errorElementsFooter,
      refetch: refetchElementsFooter
    } = useQuery(QUERY_ELEMENTS_IN_QUESTION_FOOTER, {
      fetchPolicy: 'no-cache',
      variables: { id: questionId },
      skip: modalType === ModalType.CREATE
    });

    const { data: { mesUEs = null } = {} } = useQuery(QUERY_MES_UES_LIGHT, {
      fetchPolicy: 'cache-and-network'
    });

    const question = data?.question;

    const linkedCourses = dataLinkedCourses?.question?.linkedCours;

    const notions = dataNotions?.question?.notions;
    const elements = dataElements?.elementsInQuestion; // header elements question
    const elementsFooter = dataElementsFooter?.elementsInQuestionFooter; // Correction only elements question
    const flashcardDefaultResponseType = FLASHCARD_RESPONSE_TYPE.BINARY;

    const [dicoRef, setDicoRef] = useState({});

    const questionCreationElementRef = useRef(null); // Ref par default du component de création de FE question
    const correctionCreationElementRef = useRef(null); // Ref par default du component de creation de FE correction

    useEffect(() => {
      const mergedElements = [...(elements || []), ...(elementsFooter || [])];

      // IDs présents dans mergedElements (convertis en strings)
      const mergedIds = new Set(mergedElements.map((el) => String(el.id)));

      // IDs présents dans dicoRef (convertis en strings)
      const dicoRefIds = new Set(Object.keys(dicoRef));

      // Intersection des deux ensembles
      const intersection = [...mergedIds].filter((id) => dicoRefIds.has(id));

      // Éléments uniquement dans mergedElements
      const uniqueToMerged = [...mergedIds].filter((id) => !dicoRefIds.has(id));

      // Éléments uniquement dans dicoRef
      const uniqueToDicoRef = [...dicoRefIds].filter((id) => !mergedIds.has(id));

      // Mise à jour de dicoRef
      setDicoRef((prevDicoRef) => {
        const updatedDicoRef = { ...prevDicoRef };

        // Ajouter les nouvelles refs
        uniqueToMerged.forEach((id) => {
          updatedDicoRef[id] = React.createRef();
        });

        // Supprimer les refs obsolètes
        uniqueToDicoRef.forEach((id) => {
          delete updatedDicoRef[id];
        });

        return updatedDicoRef;
      });
    }, [elements, elementsFooter]);

    const saveAllFormationElements = async () => {
      // Fonction qui utilise les ref des Formations Elements, pour appeler la fonction de save.
      // dicoRef tiens compte des FE de façon dynamique dans CreateEditQuestionModal, et on y rajoute les deux FE statiques

      const array = [
        ...Object.values(dicoRef),
        questionCreationElementRef,
        correctionCreationElementRef
      ];

      try {
        // Création d'un tableau de promesses
        const savePromises = array
          .filter((ref) => ref?.current?.saveComponant) // Filtre uniquement les refs valides
          .map((ref) => ref.current.saveComponant()); // Map vers des promesses de sauvegarde

        // Exécution concurrentielle avec Promise.all
        await Promise.all(savePromises);
      } catch (error) {
        console.error('Error saving components:', error);
      }
    };

    const [Mutation, { loading }] = useMutation(getMutationFromModalType(modalType));

    const [updateCoursFromQuestion, { loading: loadingUpdateCoursQ }] =
      useMutation(UPDATE_COUR_FROM_QUESTION);

    const [fileImageQuestion, setFileImageQuestion] = useState(null);
    const [fileImageExplication, setFileImageExplication] = useState(null);
    const [shouldRefetch, setShouldRefetch] = useState(false);

    const [isNotionModalVisible, setNotionModalVisible] = useState(false);
    const [isQuitWithoutSavingVisible, setQuitWithoutSavingVisible] = useState(false);
    const [isLoadingALot, setIsLoadingALot] = useState(false);
    const [autoUpdateAllAnswer, setAutoUpdateAllAnswer] = useState(false);
    const [selectedUEId, setSelectedUEId] = useState(UE?.id);
    const [createTypeVisible, setCreateTypeVisible] = useState(false);
    const [shouldCloseModalWhenReady, setShouldCloseModalWhenReady] = useState(false);
    const [answerType, setAnswerType] = useState();

    const [hasAnswerType, setHasAnswerType] = useState(false);
    const [enableEnhancement, setEnableEnhancement] = useState(false);
    const [editorKey, setEditorKey] = useState(0);
    // Question settings (schema exercise settings)
    const [settings, setSettings] = useState(question?.settings || {});

    useEffect(() => {
      if (!loadingQuestion && question) {
        const questionType = getQuestionType(question);
        setEnableEnhancement(questionType === 'QCM' || questionType === 'QCU');
        setSettings(question.settings || {});

        // Init du settings pour la flashcard
        if (questionType === QuestionAnswerType.FLASHCARD) {
          setSettings((prev) => {
            if (!('flashcardResponseType' in prev)) {
              return { ...prev, flashcardResponseType: flashcardDefaultResponseType };
            }
            return prev;
          });
        }
      }
    }, [question]);

    //////////////////// Verification
    // Block de variable et useEffect qui permet de sync l'état de 'isLinkedToCourses' au disabled de la selection de cours

    const [isCoursSelectionDisabled, setIsCoursSelectionDisabled] = useState(null); // Peut avoir 2 stats => true / false
    useEffect(() => {
      // L'idée est que quand une question vient d'être crée, alors isLinkedToCourses est à null (indéterminé)
      // La première fois que l'on update la question, il faut déterminer cet état. Sinon on le récupère
      if (question) {
        if (question?.isLinkedToCourses === null) {
          // Si isLinkedToCourses de la question n'est pas déterminé, alors comportement par default => l'user a le choix => Soit cocher soit remplir un cours
          setIsCoursSelectionDisabled(false);
        } else {
          // Si isLinkedToCourses est déterminé, alors le bool du checkbox est l'inverse de isLinkedToCourse
          setIsCoursSelectionDisabled(!question.isLinkedToCourses);
        }
      }
    }, [question]);

    // Il faut notre hook pour avoir en mémoire la sélection des cours sous forme d'id, ici on initialise l'array
    const [selectedCoursId, setSelectedCoursId] = useState(null);
    useEffect(() => {
      if (loadingLinkedCourses === false && dataLinkedCourses?.question?.linkedCours) {
        const data = dataLinkedCourses?.question?.linkedCours.map((node) => {
          return node.id;
        });
        setSelectedCoursId(data);
      }
    }, [dataLinkedCourses, loadingLinkedCourses]);

    const [key, setKey] = useState(0);

    const isQuestionCreated = !!question?.id_question;
    const createQuestion = async (newQuestion) => {
      return Mutation({ variables: { question: newQuestion } });
    };

    const getQuestionType = () => {
      const { questionType } = subGetQuestionType(question) || {};
      return questionType;
    };

    const subGetQuestionType = ({ type, isAnswerFreeText, isCheckbox } = {}) => {
      // Sub fonction getQuestionType, prend les bool descriptif et retourne un objet avec le descriptif et le McqScaleQuestionType

      if (isAnswerFreeText === true) {
        return { questionType: 'Texte libre', scaleType: McqScaleQuestionType.FreeText };
      }

      if (type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK) {
        return { questionType: 'Schema - Point and click', scaleType: McqScaleQuestionType.Schema };
      }
      if (type === QuestionAnswerType.FILL_IN_THE_BLANKS) {
        return { questionType: 'Texte à trous', scaleType: McqScaleQuestionType.FillInTheBlanks };
      }
      if (type === QuestionAnswerType.FLASHCARD) {
        return {
          questionType: QuestionAnswerType.FLASHCARD,
          scaleType: McqScaleQuestionType.FLASHCARD
        };
      }
      if (type === QuestionAnswerType.REORDER_ELEMENTS) {
        return {
          questionType: 'Remettre dans l\'ordre',
          scaleType: McqScaleQuestionType.ReorderElements
        };
      }
      if (
        [
          QuestionAnswerType.NUMERICAL,
          QuestionAnswerType.ALPHANUMERICAL,
          QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL
        ].includes(type)
      ) {
        return { questionType: 'QA', scaleType: McqScaleQuestionType.Alphanumerical };
      }
      if (isCheckbox === true) {
        return { questionType: 'QCM', scaleType: McqScaleQuestionType.MultipleChoice };
      }
      if (isCheckbox === false) {
        return { questionType: 'QCU', scaleType: McqScaleQuestionType.UniqueChoice };
      }
      return { questionType: '', scaleType: '' };
    };

    const sleep = (delay) => new Promise((resolve) => setTimeout(resolve, delay));
    const handleSubmit = async (data) => {
      try {
        setIsLoadingALot(true);
        setShouldRefetch(false);

        // Validate REORDER_ELEMENTS configuration
        if (answerType === QuestionAnswerType.REORDER_ELEMENTS) {
          const correctOrder = settings?.correctOrder || [];

          // Check that there are at least 2 elements
          if (correctOrder.length < 2) {
            message.error('Il faut au moins 2 éléments pour créer un exercice de remise en ordre');
            setIsLoadingALot(false);
            return;
          }

          // Check that all elements have either content OR image
          const emptyElements = correctOrder.filter(el => {
            const textContent = el.content?.replace(/<[^>]*>/g, '').trim() || '';
            const hasImage = el.imageFileName && el.imageFileName.trim() !== '';
            return !textContent && !hasImage;
          });
          if (emptyElements.length > 0) {
            message.error('Tous les éléments doivent avoir du contenu ou une image');
            setIsLoadingALot(false);
            return;
          }
        }

        if (!isCoursSelectionDisabled && selectedCoursId && selectedCoursId.length === 0) {
          console.log('UpdateExerciseItNeedsCoursesOrCheckbox');
          message.warning(t('UpdateExerciseItNeedsCoursesOrCheckbox'));
          notification.warning({
            message: t('UpdateExerciseItNeedsCoursesOrCheckbox'),
            placement: 'bottom'
          });
          setIsLoadingALot(false);
          return false; // J'ai changé ça car j'avais besoin que la fonction me dise si ça c'était bien passé ou pas (c'était à null), à priori à change rien
        }

        // Vérification si toutes les réponses sont valides (coché vrai/faux pour chaque par exemple)
        let validateAll = answerValidationArray.every((answer) => answer.valid === true);

        // Désac temporaire
        /*
      if (!validateAll) {
        console.log('PleaseFillAllAnswers');
        message.warning(t('PleaseFillAllAnswers'));
        notification.warning({
          message: t('PleaseFillAllAnswers'),
          placement: 'bottom'
        });
        setIsLoadingALot(false);
        return false; // J'ai changé ça car j'avais besoin que la fonction me dise si ça c'était bien passé ou pas (c'était à null), à priori à change rien
      }
       */

        // Update de tous les Formation Elements
        await saveAllFormationElements();

        delete data?.coursUeId;
        delete data?.isNotLinkedToCourses;

        IS_DEV && console.log({ settings });

        let questionType = question?.type;
        // Options (types) qui peuvent être changées lors de l'édition de l'exercice.
        if (
          [
            QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL,
            QuestionAnswerType.ALPHANUMERICAL,
            QuestionAnswerType.NUMERICAL,
            QuestionAnswerType.TRUE_OR_FALSE_OR_UNDEFINED,
            QuestionAnswerType.REORDER_ELEMENTS
          ].includes(answerType)
        ) {
          questionType = answerType;
        }
        let newQuestion = {
          ...data,
          id_qcm: question.id_qcm,
          isLinkedToCourses: !isCoursSelectionDisabled,
          settings: settings,
          type: questionType
        };
        /* Add images */
        if (fileImageQuestion) {
          newQuestion = { ...newQuestion, imageQuestion: fileImageQuestion };
        }
        if (fileImageExplication) {
          newQuestion = { ...newQuestion, imageExplication: fileImageExplication };
        }
        if (newQuestion?.maxPoints) {
          newQuestion.maxPoints = parseFloat(newQuestion.maxPoints, 10);
        }

        newQuestion = {
          ...newQuestion
        };

        if (modalType === ModalType.UPDATE) {
          if (isCoursSelectionDisabled) {
            await updateCoursFromQuestion({
              variables: { questionId: question.id_question, coursIdArray: [] }
            });
          } else {
            await updateCoursFromQuestion({
              variables: { questionId: question.id_question, coursIdArray: selectedCoursId }
            });
          }

          await Mutation({ variables: { id: question.id_question, question: newQuestion } });
          //await refetch(); // Refetch détails questions inutile si on ferme la modale
          message.success(`Exercice ${questionNumber} ${t('Updated')}`);
          if (shouldCloseModalWhenReady) {
            setShouldCloseModalWhenReady(false);
            //closeModalHandler();
          }
        } else {
          // Create
          await createQuestion(newQuestion);
          message.success(t('Created'));
          form.resetFields();
          //await closeModalHandler();
        }
        setShouldRefetch(true); // for preview
        setAutoUpdateAllAnswer(true);
        setAutoUpdateAllAnswer(false);
        setIsLoadingALot(false);

        // Pour validation TODO à supprimer

        await sleep(500);
        closeModalHandler();
        return true; // J'ai changé ça car j'avais besoin que la fonction me dise si ça c'était bien passé ou pas (y avait pas de return), à priori à change rien
      } catch (e) {
        showGqlErrorsInMessagePopupFromException(e);
        console.error(e);
      }
    };

    const handleFinish = async (formData) => {
      await handleSubmit(formData);
    };

    const showQuitWithoutSavingModal = () => {
      setQuitWithoutSavingVisible(true);
    };

    const notionButtonModal = (
      <React.Fragment>
        <Button
          size="small"
          icon={<EditTwoTone />}
          onClick={() => setNotionModalVisible(true)}
        ></Button>
        {isNotionModalVisible && (
          <Modal
            destroyOnClose
            title={`${t('Edit')} les notions de question ${questionNumber}`}
            open={isNotionModalVisible}
            onCancel={() => {
              setNotionModalVisible(false);
            }}
            footer={null}
            closable
            width={700}
          >
            <EditNotionsLink type={NotionTarget.QUESTION} typeId={question.id_question} />
          </Modal>
        )}
      </React.Fragment>
    );

    const autoSaveEverything = async () => {
      await form.submit(); // update header
      await setAutoUpdateAllAnswer(true);
      await setAutoUpdateAllAnswer(false);
    };

    const quitWithoutSavingModal = (
      <React.Fragment>
        <Modal
          destroyOnClose
          title={t('UnsavedModificaitons')}
          open={isQuitWithoutSavingVisible}
          onCancel={() => {
            setQuitWithoutSavingVisible(false);
          }}
          footer={null}
          closable
        >
          <div style={{ marginBottom: 24 }}>
            <ExclamationCircleTwoTone size={8} /> {t('SureToQuitUnsavedChange')}
          </div>
          <Space>
            <Button onClick={() => setQuitWithoutSavingVisible(false)}>{t('Cancel')}</Button>
            <Button
              onClick={() => {
                form.resetFields();
                closeModalHandler();
              }}
            >
              {t('CloseWithoutSaving')}
            </Button>
            <Button
              onClick={async () => {
                form.submit();
                setQuitWithoutSavingVisible(false);
                //await autoSaveEverything();
                // TODO Si y'a des erreurs ça ferme quand même
                // closeModalHandler()
                //setShouldCloseModalWhenReady(true);
              }}
              type="primary"
            >
              {t('SaveAndClose')}
            </Button>
          </Space>
        </Modal>
      </React.Fragment>
    );
    const [autoAddNotionFromKeywords, setAutoAddNotionFromKeywords] = useState(
      question?.autoAddNotions
    );
    const [autoSetNotionsToQuestionFromInput] = useMutation(
      MUT_AUTO_SET_NOTIONS_TO_QUESTION_FROM_INPUT
    );
    const [formValues, setFormValues] = useState(question);
    const [debouncedFormValues, setDebouncedFormValues] = useState(question);
    const [editBaremeVisible, setEditBaremeVisible] = useState(false);

    useEffect(() => {
      if (question) {
        setDebouncedFormValues(question);
      }
    }, [question]);

    const debouncedSave = useCallback(
      debounce((nextValue) => setDebouncedFormValues(nextValue), 700),
      [] // will be created only once initially
    );

    const setInitialAnswerTypeFromQuestion = () => {
      if (question?.isCheckbox === null) {
        setHasAnswerType(false); // Allows user to choose answer type
      } else {
        if (question?.isCheckbox) {
          setAnswerType(QuestionAnswerType.CHECKBOX);
        } else if (question?.isAnswerFreeText) {
          setAnswerType(QuestionAnswerType.FREE_TEXT);
        } else if (question?.isAnswerMultipleChoiceInList) {
          setAnswerType(QuestionAnswerType.MULTIPLE_CHOICE_IN_LIST);
        } else if (question?.isAnswerUniqueChoiceInList) {
          setAnswerType(QuestionAnswerType.UNIQUE_CHOICE_IN_LIST);
        } else {
          if (question?.type === QuestionAnswerType.ALPHANUMERICAL) {
            setAnswerType(QuestionAnswerType.ALPHANUMERICAL);
          } else if (question?.type === QuestionAnswerType.NUMERICAL) {
            setAnswerType(QuestionAnswerType.NUMERICAL);
          } else if (question?.type === QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL) {
            setAnswerType(QuestionAnswerType.ALPHANUMERICAL); // Pour nouvelle exercice alphanum par défaut
          } else if (question?.type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK) {
            setAnswerType(QuestionAnswerType.SCHEMA_POINT_AND_CLICK);
          } else if (question?.type === QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS) {
            setAnswerType(QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS);
          } else if (question?.type === QuestionAnswerType.FILL_IN_THE_BLANKS) {
            setAnswerType(QuestionAnswerType.FILL_IN_THE_BLANKS);
          } else if (question?.type === QuestionAnswerType.FLASHCARD) {
            setAnswerType(QuestionAnswerType.FLASHCARD);
          } else if (question?.type === QuestionAnswerType.REORDER_ELEMENTS) {
            setAnswerType(QuestionAnswerType.REORDER_ELEMENTS);
          } else {
            setAnswerType(QuestionAnswerType.RADIO);
          }
        }
        setHasAnswerType(true);
      }
    };

    const questionEffect = () => {
      if (question) {
        setAutoAddNotionFromKeywords(question?.autoAddNotions);
        setInitialAnswerTypeFromQuestion();
        form.setFieldsValue({
          question:
            question?.question ||
            (question?.type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK
              ? defaultSchemaText
              : defaultQuestionText),
          definedDifficulty: question?.definedDifficulty,
          coursUeId: `${selectedUEId}`,
          evaluateCertainty: question?.evaluateCertainty,
          explications: question?.explications
        });
      }
    };
    useEffect(questionEffect, [question]);

    const extractNotionsEffect = () => {
      const extractNotions = async () => {
        // Remplacer les notions de cet item par celles détectées
        try {
          await autoSetNotionsToQuestionFromInput({
            variables: {
              input: debouncedFormValues[tr('question')],
              questionId: question.id_question
            }
          });
          // refetch notions only
          refetchNotions();
        } catch (e) {
          console.error(e);
          showGqlErrorsInMessagePopupFromException(e);
        }
        // Soit ajouter la (les) notions
      };
      if (autoAddNotionFromKeywords) {
        extractNotions();
      }
    };

    useEffect(extractNotionsEffect, []);
    useEffect(extractNotionsEffect, [debouncedFormValues?.question]);

    /*
    const ConfirmCheckbox = ( {value, setter}) => {

    const showConfirm = () => {
      Modal.confirm({
        title: t("A?"),
        onOk() {
          setter(true);
        },
        onCancel() {},
      });
    };

    const handleChange = (e) => {
      if (e.target.checked) {
        // Si l'utilisateur essaie de cocher la case, afficher la confirmation
        showConfirm();
      } else {
        // Si l'utilisateur décoche la case, simplement mettre à jour l'état
        setter(false)
      }
    };

    return (
      <Checkbox checked={value} onChange={handleChange} defaultChecked={defaultNoCoursesSelection}>
        {t('CheckBoxDoenstWantCoursesLinkedToExercise')}
        no cours selection :{JSON.stringify(noCoursesSelection)}
        <br/>
        coursIdsArray : {JSON.stringify(selectedCoursId)}
        <br/>
        default selection {JSON.stringify(defaultNoCoursesSelection)}
      </Checkbox>
    );
  };

  //const [modalAddCourseVisible, setModalAddCourseVisible] = useState(false);

  const handleCheckboxChange = (e) => {




    if (e.target.checked) {
      Modal.confirm({
        title: t("ConfirmDoesntWantCoursesLinkedToExerciseTitle"),
        content: 'ConfirmDoesntWantCoursesLinkedToExerciseExplanation',
        onOk() {
          form.setFieldsValue({ isLinkedToCourses: true });
          setIsCoursSelectionDisabled(true)
        },
        onCancel() {
          // Annuler le changement d'état
          form.setFieldsValue({ isLinkedToCourses: false });
          setIsCoursSelectionDisabled(false)
        }
      });
    } else {
      // Si l'utilisateur décoche la case, pas besoin de confirmation
      form.setFieldsValue({ isLinkedToCourses: false });
      setIsCoursSelectionDisabled(false)
    }


  };
  */

    ////////////////////// Logique de modif du barème lorsque l'on change le type V2
    const [forceScaleChangeOpen, setForceScaleChangeOpen] = useState(false);

    const wrapperSetterIsCheckbox = (value) => {
      form.setFieldsValue({ isCheckbox: value });
    };

    //////////////////// Logique de modification du barème lorsque l'on passe de QCM à QCU et inversement
    const [shouldUpdateScale, setShouldUpdateScale] = useState(false);

    //// Structure de référence de la question pour inférer la scale => doit pouvoir être modifiée si on modifie la scale
    const [initQuestion, setInitQuestion] = useState();
    const [newIsCheckbox, setNewIsCheckbox] = useState();

    useEffect(() => {
      setInitQuestion(question);
      setNewIsCheckbox(question?.isCheckbox);
    }, [question]);

    const { questionType: initQuestionType, scaleType: initScaleType } = useMemo(() => {
      return subGetQuestionType(initQuestion);
    }, [initQuestion]);

    const { questionType: newQuestionType, scaleType: newScaleType } = useMemo(() => {
      return subGetQuestionType({ ...initQuestion, isCheckbox: newIsCheckbox });
    }, [initQuestion, newIsCheckbox]);

    useEffect(() => {
      if (newScaleType !== '' && initScaleType !== '') {
        setShouldUpdateScale(newScaleType !== initScaleType);
        setForceScaleChangeOpen(newScaleType !== initScaleType);
      }
    }, [newScaleType, initScaleType]);

    const switchBaremeAndQuestionType = (newScale) => {
      const { value, children } = newScale;
      setInitQuestion((prev) => ({
        ...prev,
        isCheckbox: newIsCheckbox, // Assurez-vous que newIsCheckbox est bien passé en paramètre
        mcqScale: {
          id: value,
          name: children
        }
      }));
    };
    const customHandleChange = async (e) => {
      /* Fonction custom de changement du checkbox de isLinkedToCourses */
      form.setFieldsValue({ isLinkedToCourses: false });

      if (e.target.checked) {
        Modal.confirm({
          title: t('ConfirmDoesntWantCoursesLinkedToExerciseTitle'),
          content: t('ConfirmDoesntWantCoursesLinkedToExerciseExplanation'),
          onOk() {
            form.setFieldsValue({ isNotLinkedToCourses: true });
            setIsCoursSelectionDisabled(true);
            setSelectedCoursId([]);
            setKey(key + 1);
          },
          onCancel() {
            form.setFieldsValue({ isNotLinkedToCourses: false });
            setIsCoursSelectionDisabled(false);
          }
        });
      } else {
        form.setFieldsValue({ isNotLinkedToCourses: false });
        setIsCoursSelectionDisabled(false);
      }
    };

    const coursAssocie = (
      <div style={{ display: 'flex', minWidth: 225, flex: 1 }}>
        <Card
          style={{ width: '100%' }}
          title={
            <span style={{ color: 'white' }}>
              {' '}
              COURS ASSOCIÉ(S) À L'EXERCICE (
              {isCoursSelectionDisabled ? 0 : selectedCoursId?.length})
            </span>
          }
          headStyle={{ background: primaryColor }}
        >
          <div style={{ display: 'flex', alignItems: 'center', columnGap: '10px' }}>
            <div style={{ flexGrow: 1 }}>
              <Form.Item style={{ margin: 0, width: '100%' }}>
                {selectedCoursId && (
                  <HierarchySelecter
                    setterHookSelection={(value) => setSelectedCoursId(value)}
                    initialisationVariable={{ [validesTypes.CTYPE_COURS]: selectedCoursId }}
                    simplificationFeature={validesTypes.CTYPE_COURS}
                    rankToRemoveIfLeaf={[
                      validesTypes.CTYPE_FOLDER,
                      validesTypes.CTYPE_UE,
                      validesTypes.CTYPE_PAGE,
                      validesTypes.CTYPE_UNKNOWN,
                      validesTypes.CTYPE_CATEGORY
                    ]}
                    disabledTypes={[
                      validesTypes.CTYPE_FOLDER,
                      validesTypes.CTYPE_UE,
                      validesTypes.CTYPE_PAGE,
                      validesTypes.CTYPE_UNKNOWN,
                      validesTypes.CTYPE_CATEGORY
                    ]}
                    useTreeSelect
                    additionalTreeProps={{
                      disabled: isCoursSelectionDisabled,
                      placeholder: t('LinkCoursesToExerciseSelecterPlaceholder')
                    }}
                    key={key}
                  />
                )}
              </Form.Item>
            </div>

            <Form.Item valuePropName="checked" name="isNotLinkedToCourses" style={{ margin: 0 }}>
              <Checkbox onChange={customHandleChange}>
                {t('CheckBoxDoesntWantCoursesLinkedToMyExercise')}
              </Checkbox>
            </Form.Item>
          </div>

          {/*
        <div>isLinkedtoCourses : {JSON.stringify(form.getFieldValue("isLinkedToCourses"))}</div>
        <div>isNotLinkedToCourses : {JSON.stringify(form.getFieldValue("isNotLinkedToCourses"))} </div>
        <div>isCoursSelectionDisabled : {JSON.stringify(isCoursSelectionDisabled)}</div>
        */}

          {/*


            <Modal
              open={modalAddCourseVisible}
              onCancel={() => setModalAddCourseVisible(false)}
              footer={null}
              closable
              destroyOnClose
              title={t('AddCourses')}
            >
              <PedagogicCascaderSelector
                onSelectTarget={async ({ label, value, type }) => {
                  await addCoursToQuestion({ variables: { coursId: value, questionId: question.id_question } });
                  await refetchLinkedCourses();
                  message.success('Cours associé avec succès');
                }}
                acceptedTypes={[PedagogicCascaderSelectorTarget.Cours]}
                placeholder="Sélectionner le cours"
                selectorType={SelectorType.TreeSelect}
              />
            </Modal>

            <Button icon={<BookOutlined /> } onClick={() => setModalAddCourseVisible(true)} type="primary" style={{ marginBottom: 24 }}>
              {t('AddCourses')}
            </Button>

            <h3>Cours associés :</h3>
            {linkedCourses?.length > 0 ? (
              <LinkedCoursList
                cours={linkedCourses}
                closable
                newLineBetweenEach
                onClose={async (coursId) => {
                  await removeCoursFromQuestion({
                    variables: {
                      questionId: question.id_question,
                      coursId,
                    },
                  });
                  refetchLinkedCourses();
                }}
              />
            ) : (
              <>
                <div style={{ color: 'red', fontWeight: 'bold' }}>
                  <Tag color="warning" icon={<ExclamationCircleOutlined/>}
                       style={{ whiteSpace: 'pre-line', wordBreak: 'normal' }}>
                    {t('NOLINKEDCOURSE')}
                  </Tag>️
                </div>
              </>
            )}
            */}
        </Card>
      </div>
    );

    const editTypeQuestion = (
      <Form.Item>
        <div>
          {/* <div>
          <h2>{t('QuestionTags')}
            <Popover
              content={<p>{t('ExplanationTags')}</p>}
              title={t('general.Help')}
              trigger="hover"
            >
              &nbsp;<QuestionCircleTwoTone/>
            </Popover>
          </h2>
          {question && (
            <TypeQuestionManager typesQuestion={question?.typeQuestion} questionId={questionId}/>
          )}
        </div>

        <div >
          <Tooltip title={t('AddAType')} style={{ width: '20%' }}>
            <PlusCircleOutlined
              className="dynamic-delete-button"
              onClick={() => setCreateTypeVisible(true)}
            />
          </Tooltip>

          {createTypeVisible && (
            <CreateEditTypeQuestionModal
              isVisible={createTypeVisible}
              modalType={ModalType.CREATE}
              closeModalHandler={() => {
                setCreateTypeVisible(false);
                refetch(); // Load new modifications
              }}
            />
          )}
        </div>
          */}

          <div>
            <h2>
              {t('QuestionType')}
              <Popover
                content={<p>{t('ExplanationQuestionType')}</p>}
                title={t('general.Help')}
                trigger="hover"
              >
                &nbsp;
                <QuestionCircleTwoTone />
              </Popover>
            </h2>
            {question && (
              <TypeQcmToQuestionManager
                typesQcm={question?.types}
                questionId={question?.id_question}
              />
            )}
          </div>
        </div>
      </Form.Item>
    );

    /* Elements header */
    const [createVisible, setCreateVisible] = useState(false);
    /* Elements footer */
    const [createElementsFooterVisible, setCreateElementsFooterVisible] = useState(false);

    /* Elements header position */
    const [position, setPosition] = useState(null);
    /* Elements footer position */
    const [positionFooter, setPositionFooter] = useState(null);

    const renderElement = (
      element,
      previousElement,
      nextElement,
      key,
      columnPosition,
      footer = false,
      isOpenByDefault = false,
      showButtons = true,
      ref = null
    ) => (
      <FormationEditableElement
        style={{ transition: 'transform .35s ease-in-out' }}
        key={key}
        element={element}
        nextElement={nextElement}
        previousElement={previousElement}
        columnPosition={columnPosition}
        questionId={footer ? null : questionId}
        footerQuestionId={footer ? questionId : null}
        canEdit
        refetchAll={() => {
          refetchElements();
          refetchElementsFooter();
        }}
        enableEnhancement={enableEnhancement}
        isOpenByDefault={isOpenByDefault}
        showButtons={showButtons}
        enableParameterTab={false}
        enablePlanificationTab={false}
        enableStylingTab={false}
        ref={ref}
      />
    );

    const renderFormationElementCreation = (elementPosition = null, footer = false, ref) => {
      return (
        <>
          <>
            {(footer ? createElementsFooterVisible : createVisible) && (
              <div
                style={{
                  border: '1px dashed #b5b5b5',
                  borderRadius: '11px',
                  margin: 5,
                  marginBottom: '15px'
                }}
              >
                <div style={{ margin: '15px' }}>
                  <CreateEditFormationElementModal
                    isModalVisible={footer ? createElementsFooterVisible : createVisible}
                    modalType="CREATE"
                    questionId={footer ? null : questionId}
                    footerQuestionId={footer ? questionId : null}
                    position={footer ? positionFooter : position}
                    closeModalHandler={() => {
                      if (footer) {
                        setCreateElementsFooterVisible(false);
                        refetchElementsFooter();
                      } else {
                        setCreateVisible(false);
                        refetchElements();
                      }
                    }}
                    enableEnhancement={enableEnhancement}
                    elementsTypesToShow={{
                      [ELEMENTS_TYPE.TITLE]: true,
                      [ELEMENTS_TYPE.IMAGE]: true,
                      [ELEMENTS_TYPE.MCQ]: false,
                      [ELEMENTS_TYPE.LINK]: true,
                      [ELEMENTS_TYPE.HTML]: true,
                      [ELEMENTS_TYPE.COURS]: false,
                      [ELEMENTS_TYPE.COURSE_SHORTCUT]: true,
                      [ELEMENTS_TYPE.FILE]: true,
                      [ELEMENTS_TYPE.RICH_TEXT]: true,
                      [ELEMENTS_TYPE.VIDEO]: false, // obsolète
                      [ELEMENTS_TYPE.VIDSTACK_VIDEO]: true,
                      [ELEMENTS_TYPE.VIDSTACK_AUDIO]: true,
                      [ELEMENTS_TYPE.DIAPO]:true,
                      [ELEMENTS_TYPE.CALLOUT]: true,
                      [ELEMENTS_TYPE.SCORM]:true,
                    }}
                    enableParameterTab={false}
                    enablePlanificationTab={false}
                    enableStylingTab={false}
                    quillEditorPreset={
                      QuestionAnswerType.FLASHCARD === answerType
                        ? QUILL_PRESET.FLASHCARD
                        : QUILL_PRESET.DEFAULT
                    }
                    ref={ref}
                  />
                </div>
              </div>
            )}
          </>

          {!(footer ? createElementsFooterVisible : createVisible) && (
            <div
              style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}
            >
              <div style={{ width: '100%' }}>
                <Button
                  style={{ marginTop: 10, minHeight: '50px' }}
                  type="dashed"
                  block
                  icon={<PlusCircleTwoTone />}
                  onClick={() => {
                    if (footer) {
                      setPositionFooter(elementPosition);
                      setCreateElementsFooterVisible(true);
                    } else {
                      setPosition(elementPosition);
                      setCreateVisible(true);
                    }
                  }}
                >
                  {t('AddElementHere')}
                </Button>
              </div>
            </div>
          )}
        </>
      );
    };

    const onCancelModal = () => {
      if (hasUnsavedChanges) {
        showQuitWithoutSavingModal();
      } else {
        closeModalHandler();
      }
    };
    const onFormFieldsChange = (changedFields, allFields) => {
      setHasUnsavedChanges(true);
    };
    const onFormValuesChange = async (values) => {
      // setFormValues({ ...formValues, ...values }); // real form values
      debouncedSave(values);
    };

    const isSmallScreen = useMediaQuery('(max-width: 450px)');

    const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);
    const drawerContent = (
      <>
        <ImageToLatexContextProvider>
          {!hasAnswerType ? (
            <>
              {loadingQuestion ? (
                <SpinnerCentered />
              ) : (
                <ChooseQuestionAnswerType
                  refetch={async () => {
                    await refetch();
                    await refetchElementsFooter();
                    await refetchElements();
                  }}
                  question={question}
                  setHasAnswerType={setHasAnswerType}
                />
              )}
            </>
          ) : (
            <>
              {quitWithoutSavingModal}
              <Tabs defaultActiveKey="1">
                {/* Main tab: edition */}
                <Tabs.TabPane tab={t('tab.Edit')} key={1}>
                  <React.Fragment>
                    {/* Show small error(s) if needed */}
                    <SmallErrorsAlert error={error} loading={loading} />
                    <Form
                      layout="vertical"
                      id={'questionEdition'}
                      onFinish={handleFinish}
                      onFieldsChange={onFormFieldsChange}
                      onValuesChange={onFormValuesChange} // can cause lag
                      form={form}
                      initialValues={{
                        question:
                          question?.question ||
                          (question?.type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK
                            ? defaultSchemaText
                            : defaultQuestionText),
                        question_en: question?.question_en,
                        question_es: question?.question_es,
                        question_it: question?.question_it,
                        question_de: question?.question_de,
                        maxPoints: question?.maxPoints,
                        definedDifficulty: question?.definedDifficulty,
                        coursUeId: `${selectedUEId}`,
                        evaluateCertainty: question?.evaluateCertainty,
                        explications: question?.explications,
                        isPublished: question?.isPublished,
                        type: question?.type,
                        isNotLinkedToCourses:
                          question?.isLinkedToCourses === null
                            ? false
                            : !question?.isLinkedToCourses, // init => cas où on vient de créer la quesiton
                        isLinkedToCourses: question?.isLinkedToCourses
                      }}
                    >
                      {coursAssocie}

                      {/* ÉNONCÉ */}
                      <Card
                        style={{ width: '100%', marginTop: '15px' }}
                        headStyle={{ background: primaryColor }}
                        title={
                          <span style={{ color: 'white' }}>
                            {QuestionAnswerType.FLASHCARD === answerType
                              ? t('Flashcard.EditQuestionRectoBoxTitle')
                              : 'ÉNONCÉ'}
                          </span>
                        }
                      >
                        <Tabs defaultActiveKey={i18n.language}>
                          {enabledLanguages &&
                            QuestionAnswerType.FLASHCARD !== answerType &&
                            enabledLanguages?.map((lang, index) => (
                              <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                                <div style={{ display: 'flex', flexDirection: 'column' }}>
                                  {enableEnhancement && (
                                    <AiEnhancementMenu
                                      secondary
                                      style={{
                                        alignSelf: 'end',
                                        marginTop: -56,
                                        marginBottom: 24
                                      }}
                                      componantType={enhancementType.TITLE}
                                      questionId={question?.id_question}
                                      refetch={refetch}
                                      onModalClose={() => {
                                        setEditorKey((ek) => ek + 1);
                                      }}
                                      getTextFct={() =>
                                        overrideDataFonctionTitleFormater(
                                          form,
                                          'question',
                                          lang,
                                          question?.id_question,
                                          () => setEditorKey((ek) => ek + 1) // Permet d'update éditeur non controllé par sa defaultValue
                                        )
                                      }
                                    />
                                  )}
                                  <div style={{ marginBottom: '5px' }}>
                                    <Form.Item name={tr('question', lang)}>
                                      <ExoQuillEditor
                                        key={`enonce-${editorKey}`}
                                        defaultValue={form.getFieldValue(tr('question', lang))} // Utilise la valeur actuelle du formulaire
                                        onChange={(editorData) => {
                                          // update form
                                          form.setFieldsValue({
                                            [tr('question', lang)]: editorData
                                          });
                                        }}
                                        modules={ExoQuillToolbarPresets.exerciseEdition}
                                      />
                                    </Form.Item>
                                  </div>

                                  <br />
                                </div>
                              </Tabs.TabPane>
                            ))}
                        </Tabs>

                        {/* On veut pas les notions pour les flashcard parce que c'est pas supporté */}
                        {!answerType === QuestionAnswerType.FLASHCARD
                          ? (modalType === ModalType.UPDATE && (
                              <Form.Item label="Notions associée(s) à ce chapeau :">
                                {/*help={
                                        <>
                                          Éditer manuellement
                                          &nbsp;<Switch
                                          size="small"
                                          checked={!autoAddNotionFromKeywords}
                                          onChange={async () => {
                                            await Mutation({
                                              variables: {
                                                question: { autoAddNotions: !autoAddNotionFromKeywords },
                                                id: question.id_question,
                                              },
                                            });
                                            setAutoAddNotionFromKeywords(!autoAddNotionFromKeywords);
                                          }}/>
                                        </>
                                      }*/}
                                <div
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'flex-start',
                                    flexWrap: 'wrap',
                                    gap: 8
                                  }}
                                >
                                  <span>Notions: </span>
                                  {notions?.map((n) => (
                                    <NotionTag key={n.id} notion={n} />
                                  ))}
                                  {/* {question?.notions?.length === 0 && (<span>(Aucune)</span>)} */}
                                  {!autoAddNotionFromKeywords && notionButtonModal}
                                </div>
                              </Form.Item>
                            )) || (
                              <p>
                                Associez des notions à cette question une fois la question créé.
                              </p>
                            )
                          : null}
                        {/* type de barem pour les flashcard */}
                        {answerType === QuestionAnswerType.FLASHCARD && (
                          <>
                            <>
                              {t('Flashcard.SelectFlashcardBaremTypeLabel')}
                              &nbsp;
                              <Popover
                                content={
                                  <div
                                    style={{
                                      display: 'flex',
                                      flexDirection: 'column',
                                      gap: '10px'
                                    }}
                                  >
                                    <span>
                                      {t('Flashcard.SelectFlashcardBaremTypeExplanation')}
                                    </span>
                                    <p>
                                      <span style={{ display: 'inline-block', minWidth: '130px' }}>
                                        {t(
                                          'Flashcard.SelectFlashcardBaremTypeExplanationTrueFalse'
                                        )}
                                      </span>
                                      &nbsp;:&nbsp;
                                      <Image src={imageTrueFalse} height={40} />
                                    </p>
                                    <p>
                                      <span style={{ display: 'inline-block', minWidth: '130px' }}>
                                        {t(
                                          'Flashcard.SelectFlashcardBaremTypeExplanationGradation'
                                        )}
                                      </span>
                                      &nbsp;:&nbsp;
                                      <Image src={imageScaleNotation} height={40} />
                                    </p>
                                  </div>
                                }
                                trigger="hover"
                              >
                                <InfoCircleOutlined
                                  style={{ fontSize: '12px', cursor: 'pointer', color: '#1890ff' }}
                                />
                              </Popover>
                              &nbsp; : &nbsp;
                            </>
                            &nbsp;
                            <Radio.Group
                              onChange={(e) => {
                                const newValue = e.target.value;
                                setSettings((prev) => {
                                  return { ...prev, flashcardResponseType: newValue };
                                });
                              }}
                              value={
                                settings?.flashcardResponseType ?? flashcardDefaultResponseType
                              }
                              size={'small'}
                            >
                              <Radio.Button value={FLASHCARD_RESPONSE_TYPE.BINARY}>
                                {t('Flashcard.FlashcardBaremRadioSelecterBinary')}
                              </Radio.Button>
                              <Radio.Button value={FLASHCARD_RESPONSE_TYPE.CONTINUOUS}>
                                {t('Flashcard.FlashcardBaremRadioSelecterContinuous')}
                              </Radio.Button>
                            </Radio.Group>
                          </>
                        )}

                        <FormationContextProvider>
                          <AnimatePresence mode="popLayout">
                            {elements?.map((elem, k) => (
                              <SimpleMoveTransition id={elem.id} key={elem.id}>
                                <div
                                  key={elem?.id}
                                  style={{ display: 'flex', alignItems: 'flex-start' }}
                                >
                                  <div style={{ width: '100%' }}>
                                    {renderElement(
                                      elem,
                                      elements[k - 1],
                                      elements[k + 1],
                                      elem?.id,
                                      null,
                                      false,
                                      answerType === QuestionAnswerType.FLASHCARD,
                                      //&& elem?.type === ELEMENTS_TYPE.RICH_TEXT,
                                      answerType !== QuestionAnswerType.FLASHCARD,
                                      dicoRef[elem?.id]
                                    )}
                                  </div>
                                </div>
                              </SimpleMoveTransition>
                            ))}

                            {renderFormationElementCreation(
                              null,
                              false,
                              questionCreationElementRef
                            )}
                          </AnimatePresence>
                        </FormationContextProvider>
                      </Card>

                      {question?.isAnswerFreeText && (
                        <Form.Item
                          name="maxPoints"
                          label={<h2>Nombre de points</h2>}
                          rules={[
                            { required: true, message: `Veuillez entrer le nombre de points` }
                          ]}
                        >
                          <InputNumber type="number" min="0" placeholder="10" />
                        </Form.Item>
                      )}

                      {[
                        QuestionAnswerType.NUMERICAL,
                        QuestionAnswerType.ALPHANUMERICAL,
                        QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL
                      ].includes(question?.type) && (
                        <>
                          <h3>Sélectionnez le type à afficher</h3>
                          <Select
                            placeholder="Type de grille"
                            value={answerType}
                            onSelect={(v) => setAnswerType(v)}
                          >
                            <Select.Option
                              key={QuestionAnswerType.ALPHANUMERICAL}
                              value={QuestionAnswerType.ALPHANUMERICAL}
                            >
                              Alphanumérique
                            </Select.Option>
                            <Select.Option
                              key={QuestionAnswerType.NUMERICAL}
                              value={QuestionAnswerType.NUMERICAL}
                            >
                              Numérique
                            </Select.Option>
                            {/*
                            <Select.Option
                              key={QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL}
                              value={QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL}
                            >
                              Non-défini
                            </Select.Option>
                            */}
                          </Select>
                        </>
                      )}
                    </Form>

                    <br />
                    {/* QUESTION ANSWERS (QCM/QCU) EDITION */}
                    {isQuestionCreated &&
                      ![
                        QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL,
                        QuestionAnswerType.SCHEMA_POINT_AND_CLICK,
                        QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS,
                        QuestionAnswerType.FILL_IN_THE_BLANKS,
                        QuestionAnswerType.FLASHCARD,
                        QuestionAnswerType.REORDER_ELEMENTS
                      ].includes(answerType) && (
                        <EditAnswersQuestion
                          questionId={question?.id_question}
                          autoUpdateAllAnswer={autoUpdateAllAnswer}
                          aptoriaAnswerType={answerType}
                          onUpdate={() => {
                            setShouldRefetch(false);
                            setShouldRefetch(true);
                            // refetch() // refetch ici fermerait la modale
                          }}
                          enabledAiEnhancementMenu={enableEnhancement}
                        />
                      )}

                    {/* SCHEMA EDITION */}
                    {isQuestionCreated &&
                      answerType === QuestionAnswerType.SCHEMA_POINT_AND_CLICK && (
                        <EditSchemaPointAndClick
                          question={question}
                          settings={settings}
                          setSettings={setSettings}
                          aptoriaAnswerType={answerType}
                          mode={'point-and-click'}
                          onUpdate={() => {
                            setShouldRefetch(false);
                            setShouldRefetch(true);
                            // refetch() // refetch ici fermerait la modale
                          }}
                          enabledAiEnhancementMenu={enableEnhancement}
                        />
                      )}
                    {isQuestionCreated &&
                      answerType === QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS && (
                        <EditSchemaPointAndClick
                          question={question}
                          settings={settings}
                          setSettings={setSettings}
                          aptoriaAnswerType={answerType}
                          mode={'fill-in-legends'}
                          onUpdate={() => {
                            setShouldRefetch(false);
                            setShouldRefetch(true);
                            // refetch() // refetch ici fermerait la modale
                          }}
                          enabledAiEnhancementMenu={enableEnhancement}
                        />
                      )}

                    {/* FILL IN THE BLANKS EDITION */}
                    {isQuestionCreated && answerType === QuestionAnswerType.FILL_IN_THE_BLANKS && (
                      <EditFillInTheBlanks
                        question={question}
                        primaryColor={primaryColor}
                        settings={settings}
                        setSettings={setSettings}
                        aptoriaAnswerType={answerType}
                        onUpdate={() => {
                          setShouldRefetch(false);
                          setShouldRefetch(true);
                          // refetch() // refetch ici fermerait la modale
                        }}
                        enabledAiEnhancementMenu={enableEnhancement}
                      />
                    )}

                    {/* REORDER ELEMENTS EDITION */}
                    {isQuestionCreated && answerType === QuestionAnswerType.REORDER_ELEMENTS && (
                      <EditReorderElements
                        questionId={question?.id_question}
                        question={question}
                        settings={settings}
                        setSettings={setSettings}
                        onUpdate={() => {
                          setShouldRefetch(false);
                          setShouldRefetch(true);
                          // refetch() // refetch ici fermerait la modale
                        }}
                      />
                    )}

                    <Card
                      style={{ width: '100%', marginTop: '15px' }}
                      headStyle={{ background: primaryColor }}
                      title={
                        !(QuestionAnswerType.FLASHCARD === answerType) ? (
                          <span style={{ color: 'white' }}>
                            {t('AdditionnalElementsCorrection')}
                            <Popover
                              content="Sera affiché en dessous de la question, seulement au moment de la correction."
                              title={t('CorrectionElements')}
                              trigger="hover"
                            >
                              <QuestionCircleTwoTone />
                            </Popover>
                          </span>
                        ) : (
                          <span style={{ color: 'white' }}>
                            {t('Flashcard.EditQuestionVersoBoxTitle')}
                          </span>
                        )
                      }
                    >
                      {/* Question Elements */}
                      <FormationContextProvider>
                        <AnimatePresence mode="popLayout">
                          {elementsFooter?.map((elem, k) => (
                            <SimpleMoveTransition id={elem.id} key={elem.id}>
                              {renderElement(
                                elem,
                                elementsFooter[k - 1],
                                elementsFooter[k + 1],
                                elem?.id,
                                null,
                                true,
                                answerType === QuestionAnswerType.FLASHCARD,
                                //&& elem?.type === ELEMENTS_TYPE.RICH_TEXT,
                                answerType !== QuestionAnswerType.FLASHCARD,
                                dicoRef[elem?.id]
                              )}
                            </SimpleMoveTransition>
                          ))}
                          {renderFormationElementCreation(null, true, correctionCreationElementRef)}
                        </AnimatePresence>
                      </FormationContextProvider>
                    </Card>

                    <Form
                      layout="vertical"
                      id={'questionEdition'}
                      onFinish={handleFinish}
                      onFieldsChange={onFormFieldsChange}
                      onValuesChange={onFormValuesChange} // can cause lag
                      form={form}
                      initialValues={{
                        question:
                          question?.question ||
                          (question?.type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK
                            ? defaultSchemaText
                            : defaultQuestionText),
                        question_en: question?.question_en,
                        question_es: question?.question_es,
                        question_it: question?.question_it,
                        question_de: question?.question_de,
                        maxPoints: question?.maxPoints,
                        definedDifficulty: question?.definedDifficulty,
                        coursUeId: `${selectedUEId}`,
                        evaluateCertainty: question?.evaluateCertainty,
                        explications: question?.explications,
                        isPublished: question?.isPublished,
                        type: question?.type
                      }}
                    >
                      <br />
                      <Form.Item name="isPublished" label={t('IsPublishedVisible')}>
                        <Radio.Group buttonStyle="solid">
                          <Radio.Button
                            value={true}
                            // style={formValues?.isPublished ? { backgroundColor: '#73d13d', borderColor: '#73d13d' } : {}}
                          >
                            {t('general.yes')}
                          </Radio.Button>
                          <Radio.Button
                            value={false}
                            // style={formValues?.isPublished === false ? { backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' } : {}}
                          >
                            {t('general.no')}
                          </Radio.Button>
                        </Radio.Group>
                      </Form.Item>

                      {componantType !==
                        EDIT_EXERCISE_COMPONANT_TYPE.EXERCISE_EDITION_AI_CREATION_COMPONANT && (
                        <Form.Item>
                          <Button
                            block
                            htmlType="submit"
                            type="primary"
                            loading={loading || isLoadingALot}
                          >
                            {modalType === ModalType.UPDATE
                              ? 'Enregistrer les modifications'
                              : 'Créer la question'}
                          </Button>
                        </Form.Item>
                      )}
                    </Form>
                  </React.Fragment>
                </Tabs.TabPane>

                <Tabs.TabPane tab={t('tab.Options')} key={3}>
                  <SmallErrorsAlert error={error} loading={loading} />
                  <Form
                    layout="vertical"
                    id={'questionOptions'}
                    onFinish={handleFinish}
                    onFieldsChange={(changedFields, allFields) => {
                      setHasUnsavedChanges(true);
                    }}
                    onValuesChange={async (values) => {
                      setFormValues({ ...formValues, ...values }); // real form values
                      debouncedSave(values);
                    }}
                    form={form}
                    initialValues={{
                      //question: question?.question || defaultQuestionText,
                      definedDifficulty: question?.definedDifficulty,
                      coursUeId: `${selectedUEId}`,
                      evaluateCertainty: question?.evaluateCertainty,
                      isCheckbox: question?.isCheckbox,
                      allowComments: question?.allowComments
                      //explications: question?.explications,
                    }}
                  >
                    <h2>{t('QuestionOptions')}</h2>

                    {/* Pour diapo synthèses possibilité donner nom et description de l'exercice */}
                    {isMedisupPPS && (
                      <>
                        <br />
                        {/* input settings exerciseName */}
                        <Form.Item
                          label={"Nom de l'exercice"}
                          help={'Utilisé pour les diapos de synthèse'}
                        >
                          <Input
                            onChange={(e) => {
                              setSettings({ ...settings, exerciseName: e.target.value });
                            }}
                            value={settings.exerciseName}
                          />
                        </Form.Item>
                        {/* input settings exerciseDescription */}
                        <Form.Item label={"Description de l'exercice"}>
                          <Input
                            onChange={(e) => {
                              setSettings({ ...settings, exerciseDescription: e.target.value });
                            }}
                            value={settings.exerciseDescription}
                          />
                        </Form.Item>
                        <br />
                      </>
                    )}

                    {/* Barême sélectionné */}
                    {!question?.isAnswerFreeText && (
                      <Form.Item
                        label={t('CurrentQuestionScale')}
                        style={{ marginTop: '20px', display: 'inline-flex' }}
                      >
                        <Tooltip
                          title={`Barême de la question : ${initQuestion?.mcqScale?.name || 'Barême par défaut'} `}
                        >
                          <Popover
                            content={
                              <div style={{ margin: '20px' }}>
                                <EditBaremeWithMcqScaleType
                                  question={initQuestion}
                                  mcqScaleType={newScaleType}
                                  hookAfterChange={switchBaremeAndQuestionType}
                                  syncQcuAndQcmWithScaleWhenSwitched={true}
                                />
                              </div>
                            }
                            title={`Modifier le barême`}
                            trigger="click"
                            isOpen={editBaremeVisible}
                            onVisibleChange={() => setEditBaremeVisible(!editBaremeVisible)}
                          >
                            {!shouldUpdateScale && (
                              <Tag color={'geekblue'}>
                                {initQuestion?.mcqScale?.name || 'Barême par défaut'}
                              </Tag>
                            )}
                            {shouldUpdateScale && (
                              <Tag color={'error'}>{t('Scale.ErrorNeedToChangeScale')}</Tag>
                            )}
                            <Button type="primary" icon={<ControlOutlined />}>
                              {t('Edit')}
                            </Button>
                          </Popover>
                        </Tooltip>
                      </Form.Item>
                    )}

                    <Modal
                      open={forceScaleChangeOpen}
                      onCancel={() => {
                        setNewIsCheckbox((prev) => {
                          wrapperSetterIsCheckbox(!prev);
                          return !prev;
                        });
                        setForceScaleChangeOpen(false);
                      }}
                      footer={null}
                      closeIcon={false}
                    >
                      <div style={{ margin: '20px' }}>
                        <EditBaremeWithMcqScaleType
                          question={initQuestion}
                          mcqScaleType={newScaleType}
                          hookAfterChange={(value) => {
                            switchBaremeAndQuestionType(value);
                            setForceScaleChangeOpen(false);
                          }}
                          syncQcuAndQcmWithScaleWhenSwitched={true}
                        />
                      </div>
                    </Modal>

                    {/* On affiche que pour QCM et QCU */}
                    {!question?.type && !question?.isAnswerFreeText && (
                      <>
                        <Form.Item label={t('exerciceFormat')} name="isCheckbox">
                          <Radio.Group
                            buttonStyle="solid"
                            onChange={(e) => {
                              setNewIsCheckbox(e.target.value);
                            }}
                          >
                            <Radio.Button value={true}>{t('MCQ')}</Radio.Button>
                            <Radio.Button value={false}>{t('QCU')}</Radio.Button>
                          </Radio.Group>
                        </Form.Item>

                        {shouldUpdateScale && (
                          <Tag color={'warning'}>
                            {t('Scale.WarningToUpdateScaleWhenSwitchingQuestionQCUandQCM')}
                          </Tag>
                        )}
                        {question?.isCheckbox === true &&
                          ![
                            QuestionAnswerType.NUMERICAL,
                            QuestionAnswerType.ALPHANUMERICAL,
                            QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL,
                            QuestionAnswerType.FREE_TEXT
                          ].includes(question?.type) && (
                            <Form.Item name="type" label="Type d'affichage vrai/faux">
                              <Select
                                placeholder="Type d'affichage vrai/faux"
                                onSelect={(v) => setAnswerType(v)}
                              >
                                <Select.Option key={null} value={null}>
                                  Affichage classique
                                </Select.Option>
                                <Select.Option
                                  key={QuestionAnswerType.TRUE_OR_FALSE_OR_UNDEFINED}
                                  value={QuestionAnswerType.TRUE_OR_FALSE_OR_UNDEFINED}
                                >
                                  Affichage avec sélection Vrai ou Faux
                                </Select.Option>
                              </Select>
                            </Form.Item>
                          )}
                      </>
                    )}

                    {
                      <>
                        <Form.Item
                          label={t(
                            'MathpixIntegration.CreationMethodParagraphLabelInExerciceOptions'
                          )}
                        >
                          <div>
                            {question?.creationType === CREATION_TYPE.HUMAN && (
                              <Tag color="blue">{t('MathpixIntegration.CreationHumanLabel')}</Tag>
                            )}
                            {question?.creationType === CREATION_TYPE.GPT_CREATION && (
                              <Tag color="blue">{t('MathpixIntegration.CreationGPTLabel')}</Tag>
                            )}
                            {question?.creationType === CREATION_TYPE.GPT_IMPORTATION && (
                              <Tag color="blue">{t('MathpixIntegration.ImportGptLabel')}</Tag>
                            )}
                            {question?.creationType === CREATION_TYPE.GPT_IMPORTATION_PDF && (
                              <Tag color="blue">{t('MathpixIntegration.ImportGptPdfLabel')}</Tag>
                            )}
                            {question?.creationType === CREATION_TYPE.GPT_IMPORTATION_TEXT && (
                              <Tag color="blue">
                                {t('MathpixIntegration.ImportGptRawTextLabel')}
                              </Tag>
                            )}
                            {question?.creationType === CREATION_TYPE.GPT_IMPORTATION_COURS && (
                              <Tag color={'blue'}>
                                {t('MathpixIntegration.ImportGptCourseLabel')}
                              </Tag>
                            )}
                            {question?.creationType === CREATION_TYPE.GPT_IMPORTATION_PICTURE && (
                              <Tag color="blue">
                                {t('MathpixIntegration.ImportGptPicturesLabel')}
                              </Tag>
                            )}
                            &nbsp;
                            {(question?.creationType === CREATION_TYPE.GPT_IMPORTATION ||
                              question?.creationType === CREATION_TYPE.GPT_IMPORTATION_PDF ||
                              question?.creationType === CREATION_TYPE.GPT_IMPORTATION_TEXT ||
                              question?.creationType === CREATION_TYPE.GPT_IMPORTATION_PICTURE ||
                              question?.creationType === CREATION_TYPE.GPT_IMPORTATION_COURS) && (
                              <Button
                                icon={<DownloadOutlined />}
                                disabled={!question?.mathpixieId}
                                onClick={async () => {
                                  await downloadFile(FILE_TYPE.PIXIES, `/${question?.mathpixieId}`);
                                }}
                              >
                                {t('MathpixIntegration.LabelDownloadPdfForImportedExerciceButton')}
                              </Button>
                            )}
                          </div>
                        </Form.Item>
                      </>
                    }

                    {editTypeQuestion}

                    <Form.Item
                      name="definedDifficulty"
                      label={<h2>{t('EstimatedDifficultyOfQuestion')}</h2>}
                      help={t('Difficulty')}
                    >
                      <div style={{ maxWidth: '300px' }}>
                        <Slider
                          min={0}
                          max={5}
                          step={0.1}
                          tooltip={{
                            formatter: (value) => `${value}/5`
                          }}
                        />
                      </div>
                    </Form.Item>
                    <br />
                    <Form.Item
                      name="evaluateCertainty"
                      help="Demander à l'utilisateur son niveau de certitude (Uniquement en affichage 1 par 1)"
                      valuePropName="checked"
                    >
                      <Checkbox>{t('EvaluateCertaintyLevel')}</Checkbox>
                    </Form.Item>
                    <Form.Item
                      name="allowComments"
                      help="Donnera la possibilité aux élèves de poser des questions"
                      valuePropName="checked"
                    >
                      <Checkbox>{t('EnableQuestionComments')}</Checkbox>
                    </Form.Item>
                    <br />
                    <br />
                    <Form.Item>
                      <Button
                        block
                        htmlType="submit"
                        type="primary"
                        loading={loading || isLoadingALot}
                      >
                        {modalType === ModalType.UPDATE
                          ? 'Enregistrer les modifications'
                          : 'Créer la question'}
                      </Button>
                    </Form.Item>
                  </Form>
                </Tabs.TabPane>

                {isQuestionCreated && (
                  <Tabs.TabPane tab={t('tab.Preview')} key={4}>
                    <ApercuQuestionCorrection
                      questionNumber={questionNumber}
                      question={question}
                      shouldRefetch={shouldRefetch}
                    />
                  </Tabs.TabPane>
                )}

                {/* Smart question selection classification Aptoria only */}
                {isAptoria && hasExternalQuestions && (
                  <>
                    <Tabs.TabPane tab="Questions parents" key={5}>
                      <QuestionHierarchy
                        type="parents"
                        mode={QuestionSearchActionsTypes.addParent}
                        question={question}
                        fromQuestionId={question?.id_question}
                        questions={question?.parentsQuestions}
                        disabledQuestionIds={question?.parentsQuestions?.map((n) => n.id_question)}
                        qcmId={qcmId}
                        hasExternalQuestions
                        UE={UE}
                        refetch={refetch}
                      />
                    </Tabs.TabPane>
                    <Tabs.TabPane tab="Questions enfants" key={6}>
                      <QuestionHierarchy
                        type="childs"
                        mode={QuestionSearchActionsTypes.addChild}
                        question={question}
                        fromQuestionId={question?.id_question}
                        questions={question?.childrensQuestions}
                        disabledQuestionIds={question?.childrensQuestions?.map(
                          (n) => n.id_question
                        )}
                        qcmId={qcmId}
                        hasExternalQuestions
                        UE={UE}
                        refetch={refetch}
                      />
                    </Tabs.TabPane>
                  </>
                )}

                <Tabs.TabPane tab={t('tab.Activity')} key={7}>
                  <UserLogs questionId={questionId} withTypeSelector={false} showUser canShowAll />
                </Tabs.TabPane>
              </Tabs>
            </>
          )}
        </ImageToLatexContextProvider>
      </>
    );

    const [
      acceptQuestionHook,
      { data: acceptationData, loading: acceptationLoading, error: acceptationError }
    ] = useMutation(MUTATION_ACCEPT_QUESTION);
    const [
      rejectQuestionHook,
      { data: rejectionData, loading: rejectionLoading, error: rejectionError }
    ] = useMutation(MUTATION_REJECT_QUESTION);

    const modifyAiAcceptationState = async (arg) => {
      if (arg === 'ACCEPTATION') {
        return acceptQuestionHook({ variables: { id: questionId } });
      } else if (arg === 'REJECTION') {
        return rejectQuestionHook({ variables: { id: questionId } });
      }
    };

    // Block d'imperativeHandle afin d'avoir un contrôle sur ce componant depuis un componant parent
    useImperativeHandle(ref, () => ({
      // Fonction qui permet de savoir si il reste des éléments non sauvegardés ou pas.
      getSaveState: () => {
        return hasUnsavedChanges;
      },

      // Save Componant => retourne "true" si ça s'est bien uploadé sinon false
      saveExercise: async () => {
        const values = form.getFieldsValue();
        const result = await handleSubmit(values);
        return result;
      },

      // Modification du state de la question
      handleModifyState: (arg) => {
        return modifyAiAcceptationState(arg);
      }
    }));

    return (
      <AiEnhancementContextProvider>
        {componantType === EDIT_EXERCISE_COMPONANT_TYPE.EXERCISE_EDITION_STANDALONE && (
          <Drawer
            placement={'bottom'}
            onClose={onCancelModal}
            closeIcon={null}
            extra={<CloseOutlined onClick={onCancelModal} />}
            key={`${modalType}-question-drawer-${questionNumber}`}
            title={
              modalType === ModalType.UPDATE ? (
                <>
                  {t('EditQuestion')} : {questionNumber} - {getQuestionType()}
                  {' - '}
                  <Popover
                    content={
                      <>
                        {question?.parentQcms?.map((parentQcm, k) => (
                          <p key={k}>
                            <Link to={`/admin-series/edit/${parentQcm?.id_qcm}`} key={k}>
                              {parentQcm?.[tr('titre')] || missingTranslation} (
                              {parentQcm?.UE?.[tr('name', selectedLanguage)] || missingTranslation})
                            </Link>
                          </p>
                        ))}
                      </>
                    }
                    title="Séries utilisant l'exercice"
                    trigger="click"
                  >
                    <Button size={'small'}>{question?.parentQcms?.length} série(s)</Button>
                  </Popover>
                </>
              ) : (
                t('CreateNewQuestion')
              )
            }
            open={isVisible}
            footer={null}
            destroyOnClose
            height={'calc(100% - 64px)'}
          >
            {drawerContent}
          </Drawer>
        )}

        {componantType === EDIT_EXERCISE_COMPONANT_TYPE.EXERCISE_EDITION_AI_CREATION_COMPONANT && (
          <>
            <>{drawerContent}</>
          </>
        )}
      </AiEnhancementContextProvider>
    );
  }
);
