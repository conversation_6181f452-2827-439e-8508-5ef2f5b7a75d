import { CreateAndImportAiContextProvider } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';
import ImportExercise from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/SharedComponants/ImportExercise';
import React from 'react'

const ImportExerciseHoC = (props) => {
  return (
    <>
      <CreateAndImportAiContextProvider>
        <ImportExercise {...props} />
      </CreateAndImportAiContextProvider>
    </>
  );
};

export default ImportExerciseHoC;
