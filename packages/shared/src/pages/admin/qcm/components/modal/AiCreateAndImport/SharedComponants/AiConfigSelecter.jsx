import { useAiCreateAndImportContext } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import useLocalStorage from '@/shared/hooks/useLocalStorage';
import { QUERY_FETCH_GPT_QCM_CONFIG } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/AiEnhancementMenuQueries';
import { useQuery } from '@apollo/client';
import { message, Typography, TreeSelect } from 'antd';

const AiConfigSelecter = ({ title = '', hideTitle = false }) => {
  const { t } = useTranslation();
  const { setModelId, setModelConfig } = useAiCreateAndImportContext();

  // récupération ou init des configIdQuestionGeneration // modelIdQuestionGeneration depuis le local storage si existe. Sinon, null
  const [currentGptQcmConfigId, setCurrentGptQcmConfigId] = useLocalStorage(
    'configIdQuestionGeneration',
    null
  );
  const [currentGptQcmModelId, setCurrentGptQcmModelId] = useLocalStorage(
    'modelIdQuestionGeneration',
    null
  );

  // Use Effect pour mettre les éléments récupérés de localStorage dans les setters à l'initialisation
  useEffect(() => {
    setModelId(currentGptQcmModelId);
    setModelConfig(currentGptQcmConfigId);
  }, []);

  const {
    data: { getMyAiQcmConfigs = null } = {},
    error,
    loading
  } = useQuery(QUERY_FETCH_GPT_QCM_CONFIG, {
    onError: (error) => {
      message.error(`Erreur lors de la récupération des configurations d'AI : ${error}`);
    }
  });

  useEffect(() => {
    let idExists;

    if (getMyAiQcmConfigs && currentGptQcmConfigId) {
      idExists = getMyAiQcmConfigs.some((config) => config.id === currentGptQcmConfigId);

      if (!idExists) {
        setCurrentGptQcmConfigId(null);
        setModelConfig(null);
      }
    }

    if (
      getMyAiQcmConfigs &&
      (currentGptQcmConfigId === undefined || currentGptQcmConfigId === null || !idExists) &&
      getMyAiQcmConfigs.length === 1
    ) {
      setCurrentGptQcmConfigId(getMyAiQcmConfigs[0]?.id);
      setModelConfig(getMyAiQcmConfigs[0]?.id);
    }
  }, [currentGptQcmConfigId, getMyAiQcmConfigs]);

  useEffect(() => {
    let currentConfig = getMyAiQcmConfigs?.find((config) => config.id === currentGptQcmConfigId);

    if (currentConfig) {
      let modelExists;

      if (currentGptQcmModelId) {
        modelExists = currentConfig?.models.some((model) => model.id === currentGptQcmModelId);
        if (!modelExists) {
          setCurrentGptQcmModelId(null);
          setModelId(null);
        }
      }

      if (!modelExists && currentConfig?.models?.length === 1) {
        setCurrentGptQcmModelId(currentConfig?.models?.[0]?.id);
        setModelId(currentConfig?.models?.[0]?.id);
      }
    }
  }, [currentGptQcmConfigId, currentGptQcmModelId, getMyAiQcmConfigs, setCurrentGptQcmModelId]);

  const treeData = getMyAiQcmConfigs?.map((config) => ({
    title: config.name,
    value: config.id,
    key: config.id,
    disabled: true,
    children: config.models.map((model) => ({
      title: model.name,
      value: model.id,
      key: model.id
    }))
  }));

  const handleSelect = (selectedValue) => {
    setCurrentGptQcmModelId(selectedValue);
    setModelId(selectedValue);

    // find du parent
    const parentNode = treeData?.find((parent) =>
      parent.children.some((child) => child?.value === selectedValue)
    );
    const parentValue = parentNode?.value;

    setCurrentGptQcmConfigId(parentValue);
    setModelConfig(parentValue);
  };

  return (
    <>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-evenly',
          flexWrap: 'wrap'
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
          {!hideTitle ? (
            title.length === 0 ? (
              <Typography.Title level={4}>
                {t('AiQuestionCreationModal.ChooseMyAiIntegration')}
              </Typography.Title>
            ) : (
              { title }
            )
          ) : null}
          <TreeSelect
            style={{ minWidth: '200px' }}
            treeData={treeData}
            value={currentGptQcmModelId}
            placeholder={t('AiQuestionCreationModal.ChooseMyAiIntegrationPlaceholder')}
            onChange={handleSelect}
            treeDefaultExpandAll
            disabled={loading || !getMyAiQcmConfigs}
          />
        </div>
      </div>
    </>
  );
};

export default AiConfigSelecter;
