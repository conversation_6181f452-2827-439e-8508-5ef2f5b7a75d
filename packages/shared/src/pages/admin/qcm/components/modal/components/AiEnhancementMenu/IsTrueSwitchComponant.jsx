import { useTranslation } from 'react-i18next';
import { Radio } from 'antd';
import React from 'react';

const IsTrueSwitchComponant = ({ value, onChange }) => {
  /* Petit componant de Vrai/Faux */
  const { t } = useTranslation();
  return (
    <>
      <Radio.Group
        value={value}
        buttonStyle="solid"
        onChange={(e) => {
          onChange(e.target.value);
        }}
      >
        <Radio.Button
          style={
            value
              ? {
                  backgroundColor: '#73d13d',
                  borderColor: '#73d13d'
                }
              : {}
          }
          value={true}
        >
          {t('general.TRUE')}
        </Radio.Button>
        <Radio.Button
          style={
            value === false
              ? {
                  backgroundColor: '#ff4d4f',
                  borderColor: '#ff4d4f'
                }
              : {}
          }
          value={false}
        >
          {t('general.FALSE')}
        </Radio.Button>
      </Radio.Group>
    </>
  );
};

export { IsTrueSwitchComponant };
