import {ExoUserLight} from "@/shared/components/User/ExoUserLight";
import {Tooltip, Collapse, Table,Panel} from "antd";
import {MinusCircleOutlined} from "@ant-design/icons";
import React,{useState} from "react";
import SearchUser from "@/shared/components/User/SearchUser";
import {useTranslation} from "react-i18next";



export const MultiplesUserSelectRework=({setUserIds,initialValues,withUsername=false,placeholder=null,flexDirection='row'})=> {
    const { t } = useTranslation();
    const [arrayUser,setArrayUser]=useState(initialValues ? initialValues : [] ) // permet d'initialiser le componant

    function onRemoveUser(userId){
        const tmpArray= arrayUser.filter(item => item !== userId)
        setArrayUser(tmpArray);
        setUserIds(tmpArray)
    }

    function onAddUser(userId) {
        const tmpArray=[...new Set([...arrayUser,userId])]
        setArrayUser(tmpArray);
        setUserIds(tmpArray)
    }

    const { Panel } = Collapse;

    return (
        <>

            <div style={{width:"100%"}}>
                <div style={{alignSelf:'center',width:'100%',minWidth:'200px'}}>
                    <SearchUser
                      onSelectUser={async (value, option) => {
                          onAddUser(option?.key);
                      }}
                      placeholder={placeholder}
                    />
                </div>

                {/*showText && "Utilisateurs ayant accès (optionnel) :"*/}
                {/* Affichage des participants dans collapse */}


                <Collapse
                  style={{marginTop: '12px'}}
                  size={"small"}
                >
                    <Panel
                      style={{ fontWeight: '700' }}
                      header={`Utilisateurs : (${arrayUser?.length || 0})`}
                      key="1"
                    >
                        <Table
                          dataSource={arrayUser.map((value)=>{return {id:value}})}
                          pagination
                        >

                            <Table.Column
                              title={t('general.Username')}
                              key="userId"
                              render={(record) => (
                                <>
                                    <ExoUserLight key={record?.id} id={record?.id}/>
                                </>
                              )}
                            />
                            <Table.Column
                              title={t('Action')}
                              key="action"
                              render={(record) => (
                                <Tooltip title={t('general.Remove')}>
                                    <MinusCircleOutlined
                                      className="dynamic-delete-button"
                                      onClick={() => onRemoveUser(record?.id)}
                                    />
                                </Tooltip>
                              )}
                            />
                        </Table>
                    </Panel>
                </Collapse>
            </div>
        </>
    )
}