import { useMutation, gql } from '@apollo/client';
import React, { useState } from 'react';

export const MUTATION_CREATE_QUESTION_ANSWER = gql`
  mutation createAnswer($answer: AnswerInput!) {
    createAnswer(answer: $answer) {
      id
    }
  }
`;
export const MUTATION_CREATE_MULTIPLE_QUESTION_ANSWER = gql`
  mutation createMultipleAnswer($answers: [AnswerInput]!) {
    createMultipleAnswer(answers: $answers)
  }
`;
const MUTATION_EDIT_QUESTION_ANSWER = gql`
  mutation updateAnswer($id: ID!, $answer: AnswerInput!) {
    updateAnswer(id: $id, answer: $answer)
  }
`;
const MUTATION_REMOVE_QUESTION_ANSWER = gql`
  mutation deleteAnswer($id: ID!) {
    deleteAnswer(id: $id)
  }
`;
const MUTATION_UPDATE_ALL_GRADES_MCQ = gql`
  mutation updateAllGradesForMcq($id: ID!) {
    updateAllGradesForMcq(id: $id)
  }
`;
export const QuestionContext = React.createContext({
  hasUnsavedChanges: false,
  setHasUnsavedChanges: () => {},
  questionIdToOpenByModal: undefined,
  setQuestionIdToOpenByModal: () => {},
  CreateAnswer: () => {},
  EditAnswer: () => {},
  RemoveAnswer: () => {},
  createDefaultNumberOfAnswers: () => {},
  showImportQuestionModal: false,
  setShowImportQuestionModal: () => {},
  setHasValidAnswers: () => {}
});
// Create a provider for components to consume and subscribe to changes
export const QuestionContextProvider = (props) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showImportQuestionModal, setShowImportQuestionModal] = useState(false);
  const [questionIdToOpenByModal, setQuestionIdToOpenByModal] = useState(undefined);
  const [autoLoadModalDone, setAutoLoadModalDone] = useState(false);

  const [CreateAnswer] = useMutation(MUTATION_CREATE_QUESTION_ANSWER);
  const [CreateMultipleAnswers] = useMutation(MUTATION_CREATE_MULTIPLE_QUESTION_ANSWER);
  const [EditAnswer] = useMutation(MUTATION_EDIT_QUESTION_ANSWER);
  const [RemoveAnswer] = useMutation(MUTATION_REMOVE_QUESTION_ANSWER);
  const [UpdateGradesMcq] = useMutation(MUTATION_UPDATE_ALL_GRADES_MCQ);

  const createDefaultNumberOfAnswers = async (questionId) => {
    const numberOfAnswers = 5;
    const answers = [];
    for (let i = 0; i < numberOfAnswers; i++) {
      answers.push({ questionId });
    }
    await CreateMultipleAnswers({ variables: { answers } });
  };
  return (
    <QuestionContext.Provider
      value={{
        hasUnsavedChanges,
        setHasUnsavedChanges,
        CreateAnswer,
        EditAnswer,
        RemoveAnswer,
        UpdateGradesMcq,
        createDefaultNumberOfAnswers,
        questionIdToOpenByModal,
        setQuestionIdToOpenByModal,
        autoLoadModalDone,
        setAutoLoadModalDone,
        showImportQuestionModal,
        setShowImportQuestionModal
      }}
    >
      {props.children}
    </QuestionContext.Provider>
  );
};
