import AvailableOnlyOnWeb from '@/shared/components/AvailableOnlyOnWeb.jsx';
import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import QuestionSearchTable from '@/shared/pages/admin/qcm/components/modal/QuestionSearchTable.jsx';
import { QuestionContextProvider } from '@/shared/pages/admin/qcm/context/QuestionContext.jsx';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { isMobile } from '@/shared/utils/utils.js';
import React from 'react';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';

export default function (props) {
  const { t } = useTranslation();
  useEffectScrollTop();
  return (
    <>
      <ExoteachLayout>
        {isMobile ? (
          <AvailableOnlyOnWeb />
        ) : (
          <>
            <FullMediParticlesBreadCrumb title={t('AllQuestions')} />
            <QuestionContextProvider>
              <QuestionSearchTable />
            </QuestionContextProvider>
          </>
        )}
      </ExoteachLayout>
    </>
  );
}
