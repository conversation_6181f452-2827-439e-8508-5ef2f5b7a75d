import {
  MUTATION_DELETE_FORM,
  MUTATION_DUPLICATE_FORM,
  QUERY_ALL_FORMS,
  QUERY_FORM_BY_ID_WITH_USER_COMPLETION
} from '@/shared/graphql/forms.js';
import { ModalType } from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal.jsx';
import { CreateEditFormModal } from '@/shared/pages/admin/forms/components/CreateEditFormModal.jsx';
import { downloadFile, FILE_TYPE } from '@/shared/services/file';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import {
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  FileExcelOutlined,
  LinkOutlined,
  PlusOutlined
} from '@ant-design/icons';
import React, { useState } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import { But<PERSON>, Card, message, notification, Popconfirm, Popover, Table, Typography } from 'antd';
import { ErrorResult } from '@/shared/components/ErrorResult';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import { Link } from 'umi/index.js';

export const FormActions = ({ refetch, record, loading }) => {
  const { t } = useTranslation();
  const [editVisible, setEditVisible] = useState(false);
  const [deleteForm] = useMutation(MUTATION_DELETE_FORM);
  const [duplicateForm] = useMutation(MUTATION_DUPLICATE_FORM);

  const handleExportToXls = async () => {
    //export-form-xls/:formId
    notification.info({ message: 'Exportation du formulaire...' });
    downloadFile(FILE_TYPE.FILE, `export-form-xls/${record?.id}`).then((r) => {});
  };

  const handleDuplicate = async (id) => {
    try {
      await duplicateForm({ variables: { id } });
      message.success(t('DuplicatedWithSuccess'));
      refetch();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };
  const handleDelete = async (id) => {
    try {
      await deleteForm({ variables: { id } });
      message.success(t('DeletedWithSuccess'));
      refetch();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  return (
    <span>
      <Button
        onClick={() => {
          setEditVisible(true);
        }}
        style={{ marginRight: 16 }}
        type="primary"
        shape="circle"
        icon={<EditOutlined />}
      />
      {editVisible && (
        <CreateEditFormModal
          formId={record.id}
          isVisible={editVisible}
          modalType={ModalType.UPDATE}
          closeModalHandler={() => {
            setEditVisible(false);
            refetch(); // Load new modifications
          }}
        />
      )}

      <Popover
        trigger="click"
        title={'Lien du formulaire'}
        content={
          <>
            <Typography.Paragraph copyable>
              {`${window.location.href.split('#')[0]}#/form/${record.uuid}`}
            </Typography.Paragraph>
          </>
        }
      >
        <Button shape="circle" style={{ marginRight: 16 }} icon={<LinkOutlined />} />
      </Popover>

      <Popconfirm title={t('SureOfDuplication')} onConfirm={() => handleDuplicate(record.id)}>
        <Button shape="circle" style={{ marginRight: 16 }} icon={<CopyOutlined />} />
      </Popconfirm>

      <Popconfirm title={t('SureOfDeletion')} onConfirm={() => handleDelete(record.id)}>
        <Button shape="circle" style={{ marginRight: 16 }} danger icon={<DeleteOutlined />} />
      </Popconfirm>

      <Button
        shape="circle"
        style={{ marginRight: 16 }}
        icon={<FileExcelOutlined />}
        onClick={handleExportToXls}
      />
    </span>
  );
};

const CountFormUserResults = ({ record }) => {
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_FORM_BY_ID_WITH_USER_COMPLETION, {
    fetchPolicy: 'cache-and-network',
    variables: {
      id: record.id
    }
  });
  const form = data?.form;
  const completedBy = form?.completedBy;
  const count = completedBy?.length || 0;

  return <div>{count}</div>;
};

export const AdminForms = (props) => {
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_ALL_FORMS, {
    fetchPolicy: 'no-cache'
  });
  const allForms = data?.allForms || [];

  const [createVisible, setCreateVisible] = useState(false);

  const closeModalHandler = () => {
    refetch(); // Load new modifications
    setCreateVisible(false);
  };
  const openModalHandler = () => {
    setCreateVisible(true);
  };

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name',
      render: (name, f) => <Link to={`/admin/forms/results/${f.id}`}>{name}</Link>
    },
    {
      title: t('Elements'),
      dataIndex: 'elements',
      key: 'elements',
      render: (elements) => elements?.length
    },
    {
      title: t('Results'),
      key: 'results',
      render: (_, record) => <CountFormUserResults record={record} />
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => <FormActions record={record} refetch={refetch} key={record.id} />
    }
  ];

  return (
    <>
      <Card>
        <Button onClick={openModalHandler} type={'primary'} icon={<PlusOutlined />}>
          {t('general.add')}
        </Button>
      </Card>

      <CreateEditFormModal
        closeModalHandler={closeModalHandler}
        isVisible={createVisible}
        modalType={ModalType.CREATE}
        refetch={refetch}
        loading={loading}
      />

      {!error && data && (
        <Table
          loading={loading}
          columns={columns}
          dataSource={allForms}
          scroll={{ x: true }}
          pagination={{
            defaultPageSize: 50
          }}
        />
      )}
      {error && !loading && <ErrorResult refetch={refetch} error={error} />}
    </>
  );
};

export default function (props) {
  const { t } = useTranslation();
  return (
    <>
      <FullMediParticlesBreadCrumb title={t('Forms')} />

      <AdminForms />
    </>
  );
}
