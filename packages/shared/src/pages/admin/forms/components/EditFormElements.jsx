import {
  MUTATION_ADD_ELEMENT_TO_FORM,
  MUTATION_REMOVE_ELEMENT_FROM_FORM
} from '@/shared/graphql/forms.js';
import FormationEditableElement from '@/shared/pages/formations/components/FormationEditableElement.jsx';
import { CreateEditFormationElementModal } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal.jsx';
import { ELEMENTS_TYPE } from '@/shared/services/formations.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { PlusCircleTwoTone } from '@ant-design/icons';
import { useMutation } from '@apollo/client';
import { Button } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AnimatePresence } from 'framer-motion/dist/framer-motion';
import { SimpleMoveTransition } from '@/shared/assets/transitions/SimpleMoveTransition.jsx';

export const EditFormElements = ({ elements, refetchElements, formId }) => {
  const { t, i18n } = useTranslation();
  // Mutations
  const [addToForm, { loading: loadingAddElem }] = useMutation(MUTATION_ADD_ELEMENT_TO_FORM);
  const [removeFromForm, { loading: loadingRemoveElem }] = useMutation(
    MUTATION_REMOVE_ELEMENT_FROM_FORM
  );

  /* Elements related */
  const [createVisible, setCreateVisible] = useState(false);
  const [position, setPosition] = useState(null);
  const renderElement = (element, previousElement, nextElement, key, columnPosition) => (
    <FormationEditableElement
      style={{ transition: 'transform .35s ease-in-out' }}
      key={key}
      element={element}
      nextElement={nextElement}
      previousElement={previousElement}
      columnPosition={columnPosition}
      canEdit
      refetchAll={refetchElements}
    />
  );

  const addElementToForm = async (elementId) => {
    try {
      await addToForm({
        variables: {
          formId,
          elementId
        }
      });
      return true;
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  const removeElementFromForfait = async (elementId) => {
    try {
      await removeFromForm({
        variables: {
          formId,
          elementId
        }
      });
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  const elementCreationComponents = (
    <>
      {createVisible && (
        <div
          style={{
            border: '1px dashed #b5b5b5',
            borderRadius: '11px',
            margin: 5,
            marginBottom: '15px'
          }}
        >
          <div style={{ margin: '15px' }}>
            <CreateEditFormationElementModal
              isModalVisible={createVisible}
              modalType="CREATE"
              position={position}
              closeModalHandler={async (newId) => {
                setCreateVisible(false);
                if (newId) {
                  // Ajoute au formulaire le new element
                  await addElementToForm(newId);
                }
                refetchElements();
              }}
              elementsTypesToShow={{
                // Affichage
                [ELEMENTS_TYPE.TITLE]: true,
                [ELEMENTS_TYPE.IMAGE]: true,
                [ELEMENTS_TYPE.MCQ]: false,
                [ELEMENTS_TYPE.LINK]: true,
                [ELEMENTS_TYPE.HTML]: true,
                [ELEMENTS_TYPE.COURS]: false,
                [ELEMENTS_TYPE.COURSE_SHORTCUT]: true,
                [ELEMENTS_TYPE.FILE]: true,
                [ELEMENTS_TYPE.RICH_TEXT]: true,
                [ELEMENTS_TYPE.VIDEO]: false,
                [ELEMENTS_TYPE.VIDSTACK_VIDEO]: true,
                [ELEMENTS_TYPE.VIDSTACK_AUDIO]: true,
                [ELEMENTS_TYPE.DIAPO]:true,
                [ELEMENTS_TYPE.CALLOUT]: true,
                [ELEMENTS_TYPE.SCORM]:true,
                // Début de "step"
                [ELEMENTS_TYPE.SECTION]: true,
                //-------

                // INPUTS
                [ELEMENTS_TYPE.SHORT_ANSWER]: true,
                [ELEMENTS_TYPE.LONG_ANSWER]: true,
                [ELEMENTS_TYPE.SINGLE_SELECT]: true,
                [ELEMENTS_TYPE.MULTIPLE_SELECT]: true,
                [ELEMENTS_TYPE.INTEGER_NUMBER]: true,
                [ELEMENTS_TYPE.DATE_PICKER]: true,
                [ELEMENTS_TYPE.DATE_AND_TIME_PICKER]: true,
                [ELEMENTS_TYPE.FILE_IMPORT]: true,
                [ELEMENTS_TYPE.AVATAR_SELECT]: true,

                [ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT]: true
              }}
            />
          </div>
        </div>
      )}
    </>
  );

  const renderFormationElementCreation = (elementPosition = null) => (
    <>
      {elementPosition === position && elementCreationComponents}
      {!createVisible && (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
          <div style={{ width: '90%' }}>
            <Button
              style={{ marginTop: 10, minHeight: '50px' }}
              type="dashed"
              block
              icon={<PlusCircleTwoTone />}
              onClick={() => {
                setPosition(elementPosition);
                setCreateVisible(true);
              }}
            >
              {t('AddElementHere')}
            </Button>
          </div>
        </div>
      )}
    </>
  );

  return (
    <AnimatePresence mode="popLayout">
      {elements.map((elem, k) => (
        <SimpleMoveTransition id={elem.id} key={elem.id}>
          {renderElement(elem, elements[k - 1], elements[k + 1], elem?.id)}
        </SimpleMoveTransition>
      ))}
      {renderFormationElementCreation()}
    </AnimatePresence>
  );
};
