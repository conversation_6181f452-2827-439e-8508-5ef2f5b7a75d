import GroupsIdSelecter from "@/shared/components/GroupsIdSelecter";
import {<PERSON>lap<PERSON>, Select,Button,message} from 'antd';
import React, {useState} from "react";
import {useExoteachCompaniesQuery} from "@/shared/hooks/config/useConfigHooks";
import {useTranslation} from "react-i18next";

export const GroupIdAndCompaniesIdHelperForXLSImport = ({}) => {
  /* Componant that allow to select groups and companies, and return id formated as it should for excel import.
  * Allow to copy in clipboard for easy manipulation
  * */
  const { t } = useTranslation();

  const [groupIdArraySelection,setGroupIdArraySelection]=useState([])
  const [companyTarget,setCompanyTarget]=useState([])
  const { companyInfos } = useExoteachCompaniesQuery();

  function ButtonCopieToClipBoard({array}) {
    const handleButtonClick = () => {
      const dataToClipboard = array.join(" ");
      navigator.clipboard.writeText(dataToClipboard)
        .then(() => {
          message.success(t('GetGroupIdAndCompanyIdHelper.SuccessfullyCopiedIntoTheClipBoard'));
        })
        .catch(err => {
          message.error(t('GetGroupIdAndCompanyIdHelper.FailCopyClipboard'));
        });
    };

    return (
      <Button style={{width:"300px"}} onClick={handleButtonClick} type={"primary"} ghost>{t('GetGroupIdAndCompanyIdHelper.CopyToClipBoard')}</Button>
    );
  }

  return (
    <div>
      <p >
        <b>{t('GetGroupIdAndCompanyIdHelper.GroupsToAttribute')}</b>
        <div style={{marginTop:'10px',marginBottom:"10px"}}>
          <GroupsIdSelecter
            groupes={[]}
            setterId={(value)=>setGroupIdArraySelection(value)}
            style={{width:'100%',marginTop:'2px',marginBottom:"2px"}}
            placeholder={t('GetGroupIdAndCompanyIdHelper.GroupsMultiSelectPlaceholder')}
          />
          <div style={{display:'flex',flexDirection:"column",marginTop:'2px',marginBottom:"2px"}}>
            {t('GetGroupIdAndCompanyIdHelper.IdsOfYourGroups')} {groupIdArraySelection.length > 0 ? groupIdArraySelection.join(" ") : t("GetGroupIdAndCompanyIdHelper.SelectionIsEmpty")}
            <ButtonCopieToClipBoard array={groupIdArraySelection}/>
          </div>
        </div>
      </p>
      <p>
        <b>{t('GetGroupIdAndCompanyIdHelper.CompaniesToAttribute')}</b>
        <div style={{marginTop:'10px'}}>
          <Select
            //mode="multiple"
            style={{width:"100%",marginTop:'2px',marginBottom:"2px"}}
            onChange={(value)=>setCompanyTarget([value])}
            placeholder={t('GetGroupIdAndCompanyIdHelper.CompaniesMultiSelectPlaceholder')}
            defaultValue={companyTarget}
          >
            {companyInfos?.map(companyInfo => (
              <Select.Option key={companyInfo?.id} value={companyInfo?.id}>
                {companyInfo?.commercialName}
              </Select.Option>
            ))}
          </Select>
          <div style={{display:'flex',flexDirection:"column",marginTop:'2px',marginBottom:"2px"}}>
            {t('GetGroupIdAndCompanyIdHelper.IdOfYourCompany')} {companyTarget.length > 0 ? companyTarget.join(" ") : t("GetGroupIdAndCompanyIdHelper.SelectionIsEmpty")}
            <ButtonCopieToClipBoard array={companyTarget}/>
          </div>
        </div>
      </p>
    </div>
  )
}