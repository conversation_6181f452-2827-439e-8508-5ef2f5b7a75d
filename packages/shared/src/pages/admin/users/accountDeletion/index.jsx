import { ErrorResult } from '@/shared/components/ErrorResult.jsx';
import ExoAvatar from '@/shared/components/User/ExoAvatar.jsx';
import { MUTATION_DELETE_USER, UPDATE_USER } from '@/shared/graphql/user/index.js';
import { mapRoleToShortRoleName } from '@/shared/utils/authority.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { DeleteOutlined, RollbackOutlined } from '@ant-design/icons';
import { gql, useMutation, useQuery } from '@apollo/client';
import { Button, message, Popconfirm, Table, Tag, Tooltip } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import { UserProfileCard } from '@/shared/components/User/UserProfileCard/UserProfileCard';

const QUERY_USERS_ASKING_ACCOUNT_DELETION = gql`
  query usersAskingForDeletion {
    usersAskingForDeletion {
      id
      username
      userCodeName
      isActive
      email
      avatar
      role
      firstName
      name
      banned
      credits
      likesReceived
      credits
      warningReceived
      addressline1
      addressline2
      postcode
      city
      phone
      groups {
        id
        name
        folderId
      }
    }
  }
`;

const UserAccountDeletionActions = ({ refetch, record, loading, key }) => {
  const { t } = useTranslation();
  // Here mutation for real deletion and mutation for cancelling account deletion

  const [userMutation, { loading: loadingMutation, data: dataMutation, error: errorMutation }] =
    useMutation(UPDATE_USER);

  const [deleteMutation, { loading: loadingDelete, data: dataDelete, error: errorDelete }] =
    useMutation(MUTATION_DELETE_USER);

  const handleCancelDeletion = async () => {
    try {
      await userMutation({
        variables: {
          id: record?.id,
          user: { asksForDeletion: false }
        }
      });
      message.success(t('Updated'));
      refetch();
    } catch (e) {
      console.error(e);
      // message.error('Erreur serveur, veuillez réessayer')
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  return (
    <>
      <Tooltip title={t('CancelDeletion')}>
        <Button
          onClick={(e) => {
            //alert('Fonctionnalité non implémentée');
            handleCancelDeletion();
          }}
          style={{ marginRight: 16 }}
          type="primary"
          shape="circle"
          icon={<RollbackOutlined />}
        />
      </Tooltip>

      <Tooltip title={t('ConfirmDeletion')}>
        <Popconfirm
          title={t('SureOfDeletion')}
          onConfirm={async (e) => {
            try {
              await deleteMutation({ variables: { id: record.id } });
              message.success(t('DeletedWithSuccess'));
              refetch();
            } catch (e) {
              console.error(e);
            }
          }}
          okText={t('general.yes')}
          cancelText={t('general.no')}
        >
          <Button
            style={{ marginRight: 16 }}
            type="danger"
            shape="circle"
            icon={<DeleteOutlined />}
          />
        </Popconfirm>
      </Tooltip>
    </>
  );
};

export default function (props) {
  const { t } = useTranslation();
  const { data, loading, error, refetch } = useQuery(QUERY_USERS_ASKING_ACCOUNT_DELETION, {
    fetchPolicy: 'cache-and-network'
  });
  const users = data?.usersAskingForDeletion;

  const columns = [
    {
      title: 'Pseudo',
      dataIndex: 'username',
      key: 'username',
      render: (username, user) => (
        <span key={user.id}>
          <UserProfileCard userId={user?.id} username={user?.username}>
            <ExoAvatar isActive={user.isActive} avatar={user.avatar} size="small" /> &nbsp;{' '}
            {username}
          </UserProfileCard>
        </span>
      )
    },
    {
      title: 'Prénom',
      dataIndex: 'firstName',
      key: 'firstName'
    },
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      copyable: true
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      filters: [
        { text: 'Super Admin', value: 'ADMIN' },
        { text: 'Admin', value: 'SUB_ADMIN' },
        { text: 'Tuteur', value: 'TUTEUR' },
        { text: 'Utilisateur', value: 'USER' }
      ],
      render: (role, user) => (
        <Tag
          key={user.id}
          color={role === 'ADMIN' || role === 'SUB_ADMIN' ? 'geekblue' : 'purple'}
          style={{ height: 'auto' }}
        >
          {mapRoleToShortRoleName(role)}
        </Tag>
      )
    },
    {
      title: 'Groupes',
      dataIndex: 'groups',
      key: 'groups',
      render: (groups) => groups?.map((group) => <Tag key={group.id}>{group.name}</Tag>)
    },
    {
      title: 'Actions',
      key: 'action',
      hideInSearch: true,
      render: (text, record) => (
        <UserAccountDeletionActions record={record} refetch={refetch} key={record.id} />
      )
    }
  ];

  return (
    <div>
      <FullMediParticlesBreadCrumb title={t('DeletionRequests')} />

      {error && !loading && <ErrorResult refetch={refetch} error={error} />}
      <Table
        loading={loading}
        dataSource={users}
        columns={columns}
        size="small"
        scroll={{ x: true }}
      />
    </div>
  );
}
