import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import {
  <PERSON>ert,
  Button,
  Collapse,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Popconfirm,
  Radio,
  Select,
  Tabs
} from 'antd';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult';
import dayjs from 'dayjs';
import { CrownOutlined, PoweroffOutlined } from '@ant-design/icons';
import { isSuperAdmin } from '@/shared/utils/authority';
import ExoQuillEditor from '@/shared/components/ExoQuill/ExoQuillEditor';
import { ExoQuillToolbarPresets } from '@/shared/components/ExoQuill/utils';
import UserParentsChildsManager from '@/shared/pages/admin/users/components/UserParentsChildsManager';
import UserProperties from '@/shared/components/User/UserProperties/UserProperties';
import { ChangeUserPassword } from '@/shared/pages/admin/users/components/ChangeUserPassword';
import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager';
import {
  ADD_USER_TO_GROUP,
  MUTATION_DISCONNECT_USER_FROM_ALL_DEVICES,
  REMOVE_USER_FROM_GROUP,
  UPDATE_USER
} from '@/shared/graphql/user';
import {
  MUTATION_ADD_GROUP_RESPONSIBILITY,
  MUTATION_REMOVE_GROUP_RESPONSIBILITY
} from '@/shared/graphql/groupes';
import IndividualPermissionsManager from '@/shared/pages/admin/permissions/IndividualPermissionsManager';
import EditGroupPermissionsTree from '@/shared/pages/admin/permissions/EditGroupPermissionsTree';
import PlanificationTab from '@/shared/pages/admin/groupes/planification/PlanificationTab';
import { UserBillsTable } from '@/shared/components/User/UserBillsTable';
import { PreferencesAndNotificationTabPane } from '@/shared/pages/admin/users/components/PreferencesAndNotificationTabPane';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from '@apollo/client';
import { useExoteachCompaniesQuery } from '@/shared/hooks/config/useConfigHooks';

export const EditUserCard = ({
  loading,
  error,
  data,
  refetch,
  editUserAvatarView,
  userId,
  user
}) => {
  const { TabPane } = Tabs;
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { companyInfos } = useExoteachCompaniesQuery();

  const [addGroupResponsibility] = useMutation(MUTATION_ADD_GROUP_RESPONSIBILITY);
  const [removeGroupResponsibility] = useMutation(MUTATION_REMOVE_GROUP_RESPONSIBILITY);
  const [disconnectUserMutation] = useMutation(MUTATION_DISCONNECT_USER_FROM_ALL_DEVICES);
  const [userMutation, { loading: loadingMutation, data: dataMutation, error: errorMutation }] =
    useMutation(UPDATE_USER);

  const [userType, setUserType] = useState(null);
  const [editorContent, setEditorContent] = useState(data?.user?.bio);
  const [responsibiltySwitcher, setResponsibilitySwitcher] = useState('responsibleFor');
  const individualGroup = user?.individualGroup;

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 14 }
  };

  const handleFinish = async (formData) => {
    formData.bio = editorContent;
    let user = { ...formData, credits: parseInt(formData.credits, 10) };
    try {
      user = { ...user, role: userType };
      await userMutation({ variables: { id: userId, user } });
      message.success(t('Updated'));
      refetch();
    } catch (e) {
      console.error(e);
      // message.error('Erreur serveur, veuillez réessayer')
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  useEffect(() => {
    if (data && data.user && data.user.role) {
      setUserType(data.user.role);
      setEditorContent(data.user.bio);
    }
  }, [data]);

  const handleDisconnectUser = async () => {
    await disconnectUserMutation({ variables: { userId: userId } });
    message.success(t('UserDisconnectedFromAllDevices'));
  };
  return (
    <Tabs defaultActiveKey="1">
      <TabPane tab={t('general.Informations')} key="1">
        <SmallErrorsAlert error={error} loading={loading} />
        {!loading && data?.user && (
          <>
            <div style={{ display: 'flex', gap: '15px', padding: '0 24px' }}>
              <div>{editUserAvatarView}</div>
              <div>
                <div>
                  <h3>
                    {user?.firstName} {user?.name} ({user?.username})
                  </h3>
                </div>
                <div>
                  <h5>
                    <a href={`mailto:${user?.email}`}>{user?.email}</a>
                  </h5>
                </div>
                <div
                  style={{
                    marginLeft: -18 // pour que ça soit mieux aligner en gardant le padding interne du Button
                  }}
                >
                  <Popconfirm
                    title={t('DisconnectFromAllDevicesConfirmation')}
                    onConfirm={handleDisconnectUser}
                  >
                    <Button icon={<PoweroffOutlined />} type={'text'}>
                      {t('DisconnectFromAllDevices')}
                    </Button>
                  </Popconfirm>
                </div>
              </div>
            </div>

            <br />

            <div
              style={{
                margin: '18px'
                // textAlign: 'center'
              }}
            >
              <Collapse defaultActiveKey={['1']} size="small">
                <Collapse.Panel header={<b>{t('GeneralDetails')}</b>} key={1}>
                  <Form
                    {...layout}
                    onFinish={handleFinish}
                    form={form}
                    initialValues={{
                      ...data.user,
                      birthdate: data.user?.birthdate ? dayjs(data.user?.birthdate) : undefined,
                      companyId: data?.user?.companiesDescriptions.map(
                        (value) => value.companyConfigId
                      )
                    }}
                  >
                    <Form.Item
                      label={
                        <span
                          style={{
                            fontWeight: 'bold',
                            fontSize: '16px'
                          }}
                        >
                          <CrownOutlined /> {t('Rank')}
                        </span>
                      }
                      name="role"
                    >
                      <Select
                        defaultValue="USER"
                        style={{ width: '300px' }} // Vous pouvez ajuster le style selon vos besoins
                        onChange={(value) => setUserType(value)}
                      >
                        <Select.Option value="USER">{t('standardUser')}</Select.Option>
                        <Select.Option value="TUTEUR">{t('tutor')}</Select.Option>
                        <Select.Option value="PARENT">{t('general.Parent')}</Select.Option>
                        {isSuperAdmin() && (
                          <>
                            <Select.Option value="COMMERCIAL">
                              {t('general.Commercial')}
                            </Select.Option>
                            <Select.Option value="SUB_ADMIN">{t('Admin')}</Select.Option>
                            <Select.Option value="ADMIN">{t('general.SuperAdmin')}</Select.Option>
                          </>
                        )}
                      </Select>
                    </Form.Item>

                    <Form.Item name="title" label="Role (optionnel)">
                      <Input type="text" placeholder="Professeur de Maths" />
                    </Form.Item>

                    <Form.Item
                      name="username"
                      label="Pseudo (nom utilisateur)"
                      rules={[
                        {
                          required: true,
                          message: `Veuillez entrer le pseudo`
                        }
                      ]}
                    >
                      <Input type="text" placeholder="jeanjacques34" />
                    </Form.Item>

                    {user?.bot && (
                      <>
                        <Form.Item name="bot_personality" label="Personnalité">
                          <Input.TextArea
                            rows={4}
                            type="text"
                            placeholder="Exemple : Professeur de médecine, tu réponds aux élèves de manière sympathique en les tutoyant. Tu cherches toujours à leur faire comprendre le maximum et tu images tes réponses si besoins"
                          />
                        </Form.Item>
                      </>
                    )}
                    <Form.Item name="userCodeName" label="Code utilisateur (optionnel)">
                      <Input type="text" placeholder="U_1321_G" />
                    </Form.Item>

                    <Form.Item
                      name="email"
                      label="Email"
                      rules={[
                        {
                          required: true,
                          message: 'Email valide requis',
                          type: 'email'
                        }
                      ]}
                    >
                      <Input type="email" />
                    </Form.Item>

                    {user?.role !== 'PARENT' && (
                      <Form.Item name="parentsEmail" label="Email des parents (optionnel)">
                        <Input type="email" />
                      </Form.Item>
                    )}

                    <Form.Item
                      name="firstName"
                      label="Prénom"
                      rules={[{ required: true, message: 'Requis' }]}
                    >
                      <Input />
                    </Form.Item>

                    <Form.Item
                      name="name"
                      label="Nom"
                      rules={[{ required: true, message: 'Requis' }]}
                    >
                      <Input />
                    </Form.Item>

                    <Form.Item
                      name="addressline1"
                      label="Adresse"
                      rules={[{ required: false, message: 'Requis' }]}
                    >
                      <Input />
                    </Form.Item>
                    <Form.Item
                      name="addressline2"
                      label={t('general.moreAddress')}
                      rules={[{ required: false }]}
                    >
                      <Input />
                    </Form.Item>

                    <Form.Item
                      name="postcode"
                      label="Code postal"
                      rules={[{ required: false, message: 'Requis' }]}
                    >
                      <Input />
                    </Form.Item>

                    <Form.Item
                      name="city"
                      label="Ville"
                      rules={[{ required: false, message: 'Requis' }]}
                    >
                      <Input />
                    </Form.Item>

                    <Form.Item
                      name="country"
                      label="Pays"
                      rules={[{ required: false, message: 'Requis' }]}
                    >
                      <Input />
                    </Form.Item>

                    <Form.Item name="nationality" label="Nationalité">
                      <Input />
                    </Form.Item>

                    <Form.Item name="birthdate" label="Date de naissance">
                      <DatePicker format="DD/MM/YYYY" />
                    </Form.Item>

                    <Form.Item name="gender" label={t('general.Gender')}>
                      <Select>
                        <Select.Option value={'Male'}>{t('Male')}</Select.Option>
                        <Select.Option value={'Female'}>{t('Female')}</Select.Option>
                        <Select.Option value={'Other'}>{t('Other')}</Select.Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="credits"
                      label="Crédits (non utilisés)"
                      rules={[{ required: false }]}
                    >
                      <Input type="number" />
                    </Form.Item>

                    <Form.Item
                      name="phone"
                      label="Numéro de téléphone"
                      rules={[{ required: false }]}
                    >
                      <Input />
                    </Form.Item>

                    <Form.Item
                      label={t('general.Company')}
                      name="companyId"
                      rules={[
                        {
                          required: true,
                          message: t('EnterCompanyNameForfaitForm')
                        }
                      ]}
                    >
                      <Select mode="multiple">
                        {companyInfos?.map((companyInfo) => (
                          <Select.Option key={companyInfo?.id} value={companyInfo?.id}>
                            {companyInfo?.commercialName}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item label="Bio" rules={[{ required: false }]}>
                      <ExoQuillEditor
                        defaultValue={data?.user?.bio}
                        onChange={(editorData) => {
                          setEditorContent(editorData);
                        }}
                        modules={ExoQuillToolbarPresets.userBio}
                      />
                    </Form.Item>

                    <Form.Item
                      label={t('IsUserExtraTime')}
                      name="isExtraTime"
                      rules={[{ required: false }]}
                    >
                      <Radio.Group buttonStyle="solid" defaultValue={false}>
                        <Radio.Button value={false}>{t('general.no')}</Radio.Button>
                        <Radio.Button value={true}>{t('general.yes')}</Radio.Button>
                      </Radio.Group>
                    </Form.Item>

                    {/*
                              <Form.Item
                                name="isExtraTime"
                                valuePropName="checked"
                                style={layout}
                                rules={[{ required: false }]}
                                help={t('InUserEditExtraTimeExplanation')}
                              >
                                <Checkbox>
                                  {t('IsUserExtraTime')}
                                </Checkbox>
                              </Form.Item>
                              */}

                    {/*
                                // Déplacé dans PreferencesAndNotificationTabPane
                                <Form.Item
                                  name="appearsInTeam"
                                  valuePropName="checked"
                                  help="Cochez cette case si vous souhaitez que votre profil apparaisse dans la liste des membres de l'équipe. Uniquement pour tuteurs / admins"
                                  rules={[{ required: false }]}
                                  style={{ marginBottom: '20px' }}
                                >
                                  <Checkbox>{t('AppearInTeamTab')}</Checkbox>
                                </Form.Item>

                                {user?.role !== 'USER' && (
                                  <>
                                    <Form.Item
                                      name="appearsInSubjects"
                                      valuePropName="checked"
                                      help="Cochez cette case si vous souhaitez que votre profil apparaisse dans la liste des responsables de matières. Uniquement pour tuteurs / admins"
                                      rules={[{ required: false }]}
                                      style={{ marginBottom: '20px' }}
                                    >
                                      <Checkbox>{t('AppearsAsSubjectResponsible')}</Checkbox>
                                    </Form.Item>
                                    <br />
                                    <h3>{t('SubjectsForWhichYoureResponsible')}</h3>
                                    <UETuteursManager
                                      refetch={refetch}
                                      userId={user.id}
                                      ueIds={user?.responsibleForUeIds}
                                    />
                                    <br />
                                  </>
                                )}
                                */}

                    <br />
                    <Form.Item>
                      <Button htmlType="submit" type="primary" loading={loadingMutation}>
                        {t('EditThisUser')}
                      </Button>
                    </Form.Item>
                  </Form>
                </Collapse.Panel>

                <Collapse.Panel
                  header={
                    <b>
                      {t('Parents/Childs')} ({user?.childs?.length} / {user?.parents?.length})
                    </b>
                  }
                  key={2}
                >
                  <UserParentsChildsManager user={user} refetch={refetch} />
                </Collapse.Panel>
              </Collapse>

              <Divider orientation={'left'} orientationMargin={0}>
                {t('CustomFields')}
              </Divider>

              <UserProperties userId={user.id} />
            </div>
          </>
        )}
      </TabPane>
      <TabPane tab={t('password')} key="2">
        <ChangeUserPassword userId={userId} />
      </TabPane>
      <TabPane tab={t('general.Groups')} key="3">
        {user && (
          <>
            <AbstractGroupsManager
              entityName={user.username}
              entityId={user.id}
              groupes={user?.groups}
              addGroupMutation={ADD_USER_TO_GROUP}
              removeGroupMutation={REMOVE_USER_FROM_GROUP}
              groupParameterName="groupId"
              entityParameterName="userId"
            />
          </>
        )}
      </TabPane>
      <TabPane tab={t('IndividualPermissions')} key="4">
        {individualGroup && (
          <>
            <h1>{t('Responsability')}</h1>
            <Form.Item>
              <Alert
                message={t('responsabilityExplanation')}
                type="info"
                showIcon
                style={{ marginBottom: '10px' }}
              />

              <Radio.Group
                value={responsibiltySwitcher}
                onChange={(e) => setResponsibilitySwitcher(e.target.value)}
              >
                <Radio.Button value={'responsibleFor'}>{t('ResponsibleFor...')}</Radio.Button>
                <Radio.Button value={'supervisedBy'}>{t('SupervisedBy...')}</Radio.Button>
              </Radio.Group>

              {responsibiltySwitcher === 'responsibleFor' && (
                <>
                  <h2>{t('ResponsibilityGroups')}</h2>
                  <AbstractGroupsManager
                    entityId={individualGroup.id}
                    groupes={individualGroup.responsibleFor?.filter((g) => !g?.isIndividual)}
                    addGroupMutation={MUTATION_ADD_GROUP_RESPONSIBILITY}
                    removeGroupMutation={MUTATION_REMOVE_GROUP_RESPONSIBILITY}
                    groupParameterName="groupId"
                    entityParameterName="responsibleOfGroupId"
                    key={1}
                  />
                  <br />
                  <h2>{t('ResponsibilityUsers')}</h2>
                  {/* L'utilisateur est responsable de ces users à travers leur groupes individuels */}
                  <div style={{ marginBottom: '20px' }}>
                    <IndividualPermissionsManager
                      showText={false}
                      individualGroups={individualGroup.responsibleFor?.filter(
                        (g) => g?.isIndividual
                      )}
                      onAdd={async (individualGroupId) => {
                        await addGroupResponsibility({
                          variables: {
                            responsibleOfGroupId: individualGroup.id,
                            groupId: individualGroupId
                          }
                        });
                        await refetch();
                      }}
                      onRemove={async (groupId) => {
                        await removeGroupResponsibility({
                          variables: {
                            responsibleOfGroupId: individualGroup.id,
                            groupId: groupId
                          }
                        });
                        await refetch();
                      }}
                    />
                  </div>
                </>
              )}

              {responsibiltySwitcher === 'supervisedBy' && (
                <>
                  <h2>{t('ResponsibilityGroups')}</h2>
                  <AbstractGroupsManager
                    entityId={individualGroup.id}
                    groupes={individualGroup.supervisedBy?.filter((g) => !g?.isIndividual)}
                    addGroupMutation={MUTATION_ADD_GROUP_RESPONSIBILITY}
                    removeGroupMutation={MUTATION_REMOVE_GROUP_RESPONSIBILITY}
                    groupParameterName="responsibleOfGroupId"
                    entityParameterName="groupId"
                    key={2}
                  />
                  <br />
                  <h2>{t('ResponsibilityUsers')}</h2>
                  {/* L'utilisateur est responsable de ces users à travers leur groupes individuels */}
                  <div style={{ marginBottom: '20px' }}>
                    <IndividualPermissionsManager
                      showText={false}
                      individualGroups={individualGroup.supervisedBy?.filter(
                        (g) => g?.isIndividual
                      )}
                      onAdd={async (individualGroupId) => {
                        await addGroupResponsibility({
                          variables: {
                            responsibleOfGroupId: individualGroupId,
                            groupId: individualGroup.id
                          }
                        });
                        await refetch();
                      }}
                      onRemove={async (groupId) => {
                        await removeGroupResponsibility({
                          variables: {
                            responsibleOfGroupId: groupId,
                            groupId: individualGroup.id
                          }
                        });
                        await refetch();
                      }}
                    />
                  </div>
                </>
              )}
            </Form.Item>

            {/* Cours accessibles permissions individuelles user */}
            <br />
            <h1>{t('accessibleCourses')}</h1>
            <EditGroupPermissionsTree
              groupeType={null}
              forGroupId={individualGroup?.id}
              isIndividualGroup
            />
          </>
        )}
      </TabPane>

      <TabPane tab={t('Planification.Planification')} key="6">
        <PlanificationTab groupe={individualGroup} />
      </TabPane>

      <TabPane tab={t('Invoices')} key="7">
        <UserBillsTable bills={user?.bills} loading={loading} refetch={refetch} />
      </TabPane>

      <TabPane tab={t('LimitationQuestionLayer.Preferences')} key="8">
        <PreferencesAndNotificationTabPane user={user} userId={userId} refetchUser={refetch} />
      </TabPane>
    </Tabs>
  );
};
