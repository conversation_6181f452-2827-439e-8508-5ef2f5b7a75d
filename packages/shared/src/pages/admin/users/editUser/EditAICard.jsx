import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';
import { QUERY_GET_CONFIG_FROM_AI_USER_ID } from '@/shared/graphql/config';
import { UPDATE_USER } from '@/shared/graphql/user/index';
import {
  MUTATION_DELETE_AI_CONFIG,
  MUTATION_UPDATE_AI_CONFIG
} from '@/shared/pages/admin/ai/AiUsers';
import { allGPTModels } from '@/shared/pages/admin/ai/index';
import { CONFIG_KEYS } from '@/shared/services/config';
import { showGqlErrorsInMessagePopupFromException, tryParseJSONObject } from '@/shared/utils/utils';
import React, { useEffect, useState } from 'react';
import { Alert, Button, Form, Input, message, Popconfirm, Select, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager';
import {
  MUTATION_ADD_GROUP_RESPONSIBILITY,
  MUTATION_REMOVE_GROUP_RESPONSIBILITY
} from '@/shared/graphql/groupes';
import IndividualPermissionsManager from '@/shared/pages/admin/permissions/IndividualPermissionsManager';
import { useMutation, useQuery } from '@apollo/client';
import EditGroupPermissionsTree from '@/shared/pages/admin/permissions/EditGroupPermissionsTree';
import { AITemplateSelector } from '@/shared/pages/admin/users/components/AITemplateSelector';
import {
  useExoteachCompaniesQuery,
  useExoteachIntegrationsQuery
} from '@/shared/hooks/config/useConfigHooks';
import { FadeTransition } from '@/shared/assets/transitions/FadeTransition';
import { Trash2 } from 'lucide-react';
import { router } from 'umi';

export const EditAICard = ({ user, editUserAvatarView, refetch }) => {
  const [form] = Form.useForm();
  const { companyInfos } = useExoteachCompaniesQuery();
  const defaultUsername = user?.username;
  const individualGroup = user?.individualGroup;

  const { t } = useTranslation();

  // QUERIES
  const { data: configData, loading: loadingConfig } = useQuery(QUERY_GET_CONFIG_FROM_AI_USER_ID, {
    variables: {
      aiUserId: user?.id
    },
    skip: !user?.id,
    fetchPolicy: 'no-cache'
  });
  const aiConfig = configData?.getConfigFromAiUserId;
  const configValues = aiConfig?.value ? JSON.parse(aiConfig.value) : {};

  const { integrationsConfig } = useExoteachIntegrationsQuery();
  const [chatGptIntegrationId, setChatGptIntegrationId] = useState(null);
  //////////////////

  useEffect(() => {
    setChatGptIntegrationId(configValues?.integrationId);
  }, [configData]);

  // STATE
  const [userName, setUserName] = React.useState(user.username);
  const [showSavePrompt, setShowSavePrompt] = React.useState(false);
  const [modelId, setModelId] = useState(null);
  const [modelConfig, setModelConfig] = useState(null);

  // MUTATIONS
  const [addGroupResponsibility] = useMutation(MUTATION_ADD_GROUP_RESPONSIBILITY);
  const [removeGroupResponsibility] = useMutation(MUTATION_REMOVE_GROUP_RESPONSIBILITY);
  const [mutationUpdateUser, { loading: loadingUpdateUser }] = useMutation(UPDATE_USER);
  const [updateAIConfig, { loading: loadingMutation }] = useMutation(MUTATION_UPDATE_AI_CONFIG);
  const [deleteAIConfig, { loading: loadingMutationDelete }] =
    useMutation(MUTATION_DELETE_AI_CONFIG);

  const loadingMutations = loadingUpdateUser || loadingMutation || loadingMutationDelete;
  ///////////////////

  useEffect(() => {
    setUserName(user.username);
  }, [defaultUsername]);

  const submitForm = async () => {
    try {
      const formValues = form.getFieldsValue();
      const trueModelName=formValues?.model


      // model, templates
      const input = {
        model: trueModelName,
        user: formValues.user,
        aiUserId: user?.id,
        companyId: formValues.companyId,
        integrationId: chatGptIntegrationId
      };
      await updateAIConfig({
        variables: {
          id: aiConfig.id,
          input
        }
      });
      // update user
      await mutationUpdateUser({
        variables: {
          id: user.id,
          user: {
            username: formValues.username,
            bot_personality: formValues.bot_personality
          }
        }
      });
      message.success(t('general.Updated!'));
      setShowSavePrompt(false);
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const deleteAI = async () => {
    try {
      await deleteAIConfig({
        variables: {
          id: aiConfig.id
        }
      });
      message.success(t('general.SuccessfullyDeleted'));
      router.push(`/admin/ai-settings`);
    } catch (e) {
      console.error(e);
    }
  };

  if (loadingConfig) {
    return <SpinnerCentered />;
  }
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 15, padding: '0 24px' }}>
      <div style={{ display: 'flex', gap: '15px' }}>
        <div>{editUserAvatarView}</div>
        <div>
          <h3>
            {user?.username}
            <Popconfirm
              title={t('editAI.sureToDeleteAiTitle')}
              description={t('editAI.sureToDeleteAiDescription')}
              onConfirm={() => deleteAI()}
            >
              <Button ghost danger shape="circle" style={{ marginLeft: 12 }}>
                <Trash2 />
              </Button>
            </Popconfirm>
          </h3>

          {/*
            TODO : Implement this
            <AITemplateSelector style={{ paddingTop: 8 }} />
          */}
        </div>
      </div>
      <Form
        form={form}
        initialValues={{
          // User relateed
          username: user?.username,
          bot_personality: user?.bot_personality,
          // Config related
          model: configValues?.model,
          companyId: configValues?.companyId || null
          // Integration Id is in state
        }}
        onValuesChange={() => {
          setShowSavePrompt(true);
        }}
        layout="vertical"
      >
        <div style={{ display: 'flex', gap: 15, flexWrap: 'wrap', alignItems: 'flex-end' }}>
          <Form.Item
            style={{ flex: 1, minWidth: 200 }}
            name="username"
            label={
              <Typography.Title level={5} style={{ marginTop: 0 }}>
                {t('editAI.UserName')}
              </Typography.Title>
            }
            rules={[
              {
                required: true,
                message: t('editAI.RequiredUserName')
              }
            ]}
          >
            <Input
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              type="text"
              placeholder={t('editAI.UserName')}
            />
          </Form.Item>

          <Form.Item
            style={{ minWidth: 222, flex: 1 }}
            label={
              <Typography.Title level={5} style={{ marginTop: 0 }}>
                {t('general.Company')}
              </Typography.Title>
            }
            name="companyId"
            rules={[
              {
                required: true,
                message: t('EnterCompanyNameForfaitForm')
              }
            ]}
          >
            <Select mode="multiple">
              {companyInfos?.map((companyInfo) => (
                <Select.Option key={companyInfo?.id} value={companyInfo?.id}>
                  {companyInfo?.commercialName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            style={{ flex: 1 }}
            rules={[
              {
                required: true,
                message: t('OpenAIKey')
              }
            ]}
            label={
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <Typography.Title level={5} style={{ marginTop: 0 }}>
                  {t('OpenAIKey')}
                </Typography.Title>
              </div>
            }
          >
            <Select
              value={chatGptIntegrationId}
              style={{ minWidth: '300px' }}
              placeholder={t('OpenAIKey')}
              onChange={(value, option) => setChatGptIntegrationId(value)}
            >
              {integrationsConfig &&
                integrationsConfig
                  ?.filter((c) => c.key === CONFIG_KEYS.CHAT_GPT_INTEGRATION)
                  ?.map((integration, index) => {
                    const value = tryParseJSONObject(integration?.value);
                    return (
                      <Select.Option value={integration?.id} key={integration?.id}>
                        {value?.name}
                      </Select.Option>
                    );
                  })}
            </Select>
          </Form.Item>
          {!chatGptIntegrationId && (
            <Alert message={t('YouMustChooseOpenAIKey')} type="error" showIcon />
          )}

          <Form.Item
            style={{ flex: 1 }}
            name={'model'}
            label={
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <Typography.Title level={5} style={{ marginTop: 0 }}>
                  {t('editAI.Model')}
                </Typography.Title>
                <Typography.Text type="secondary">{t('editAI.ModelExplanation')}</Typography.Text>
              </div>
            }
            rules={[
              {
                required: true,
                message: t('editAI.RequiredModel')
              }
            ]}
          >
            <Select
              placeholder="Choisir le modèle"
              dropdownMatchSelectWidth={false}
            >
              {allGPTModels?.map((n) => (
                <Select.Option key={n.key} value={n.key}>{n.name} </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        <Form.Item
          name="bot_personality"
          label={
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <Typography.Title level={5} style={{ marginTop: 0 }}>
                {t('editAI.Personality')}
              </Typography.Title>
              <Typography.Text type="secondary">
                {t('editAI.PersonalityExplanation')}
              </Typography.Text>
            </div>
          }
        >
          <Input.TextArea rows={4} type="text" placeholder={t('editAI.PersonalityPlaceholder')} />
        </Form.Item>

        {individualGroup && (
          <>
            <h1>{t('editAI.Responsibility')}</h1>
            <Form.Item>
              <h2>{t('editAI.ResponsibilityGroups')}</h2>
              <AbstractGroupsManager
                entityId={individualGroup.id}
                groupes={individualGroup.responsibleFor?.filter((g) => !g?.isIndividual)}
                addGroupMutation={MUTATION_ADD_GROUP_RESPONSIBILITY}
                removeGroupMutation={MUTATION_REMOVE_GROUP_RESPONSIBILITY}
                groupParameterName="groupId"
                entityParameterName="responsibleOfGroupId"
                key={1}
              />
              <br />
              <h2>{t('editAI.ResponsibilityUsers')}</h2>
              {/* L'utilisateur est responsable de ces users à travers leur groupes individuels */}
              <div style={{ marginBottom: '20px' }}>
                <IndividualPermissionsManager
                  showText={false}
                  individualGroups={individualGroup.responsibleFor?.filter((g) => g?.isIndividual)}
                  onAdd={async (individualGroupId) => {
                    await addGroupResponsibility({
                      variables: {
                        responsibleOfGroupId: individualGroup.id,
                        groupId: individualGroupId
                      }
                    });
                    await refetch();
                  }}
                  onRemove={async (groupId) => {
                    await removeGroupResponsibility({
                      variables: {
                        responsibleOfGroupId: individualGroup.id,
                        groupId: groupId
                      }
                    });
                    await refetch();
                  }}
                />
              </div>
            </Form.Item>

            <h1>{t('editAI.accessibleCourses')}</h1>
            <EditGroupPermissionsTree
              groupeType={null}
              forGroupId={individualGroup?.id}
              isIndividualGroup
              hideContentType
              hideTreeTitle
            />
          </>
        )}
      </Form>

      <FadeTransition displayCondition={showSavePrompt} style={{ position: 'sticky', bottom: 24 }}>
        <Alert
          style={{ background: '#E7E7E7', borderColor: '#AAAAAA', borderRadius: 100 }}
          description={t('editAI.modificationsNotSaved')}
          type="info"
          action={
            <Button
              loading={loadingMutations}
              size="small"
              type="primary"
              onClick={() => submitForm()}
            >
              {t('general.save')}
            </Button>
          }
        />
      </FadeTransition>
    </div>
  );
};
