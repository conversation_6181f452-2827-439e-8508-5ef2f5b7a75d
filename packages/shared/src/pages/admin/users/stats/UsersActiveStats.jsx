import { ErrorResult } from '@/shared/components/ErrorResult.jsx';
import ExoAvatar from '@/shared/components/User/ExoAvatar.jsx';
import { QUERY_USERS_ACTIVE } from '@/shared/graphql/user/index.js';
import SessionsDetails from '@/shared/pages/admin/users/stats/SessionsDetails.jsx';
import { AUTHORITIES, mapRoleToShortRoleName } from '@/shared/utils/authority.js';
import {
  BackwardOutlined,
  ForwardOutlined,
  TrophyOutlined,
  UserOutlined,
  BarChartOutlined,
  DownloadOutlined,
  MailOutlined,
  HistoryOutlined,
  ReadOutlined,
  EllipsisOutlined,
  BulbTwoTone
} from '@ant-design/icons';
import BookOutlined from '@ant-design/icons/lib/icons/BookOutlined';
import CheckSquareOutlined from '@ant-design/icons/lib/icons/CheckSquareOutlined';
import { useQuery } from '@apollo/client';
import { But<PERSON>, Card, DatePicker, Space, Table, Tag, Select, Input, Statistic, Badge } from 'antd';
import React, { useState } from 'react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import ExportAndDownloadStatsAsXLS from '@/shared/pages/admin/users/components/ExportAndDownloadStatsAsXLS.jsx';
import { useExoteachCompaniesQuery } from '@/shared/hooks/config/useConfigHooks';
import PlotGraph from '@/shared/pages/admin/users/components/PlotGraph';
import {
  hierarchyComponantActions,
  HierarchySelecter,
  validesTypes
} from '@/shared/components/HierarchySelecter';
import { UserGroupsManager } from '@/shared/pages/admin/users/components/UserGroupsManager';
import { UserProfileCard } from '@/shared/components/User/UserProfileCard/UserProfileCard';
import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager';

export const prettyPrintMinutes = (minutesTotal) => {
  const hours = Math.floor(minutesTotal / 60);
  const minutes = minutesTotal % 60;
  return `${hours}h${minutes?.toFixed(0).toString().padStart(2, '0')}min`;
};

// Selection du type de graph à afficher en fonction des cards
export const dataSupported = {
  // les types valides
  UniqueSeenClasses: 'uniqueSeenClasses',
  ExercisesDone: 'exercisesDone',
  ActiveUsers: 'activeUsers',
  DownloadedFiles: 'downloadedFiles',
  AllSeenClasses: 'allSeenClasses',
  PostsSent: 'postsSent'
};

export const chartColor = '#2a8bbb';

export default function (props) {
  /* State */
  const DEFAULT_FILTER = {
    // je dois mettre un filter de default. Lorsque les éléments seront reset, c'est ici que l'on viendra chercher les valeures
    groupIdArray: [], // en initial state sur le groupIdArray, on a aucune filtration
    pseudoFilter: '', // en initial state pour la filtration, on a aucune filtration
    startDate: dayjs().startOf('month').toDate(),
    //startDate:dayjs("2023-09-30T22:00:00.000Z"),
    //endDate:dayjs("2023-10-31T22:59:59.999Z"),
    endDate: dayjs().endOf('month').toDate(),
    roleFilter: [],
    companyFilter: [],
    coursIdsFilter: []
  };

  const { t } = useTranslation();
  const [filter, setFilter] = useState(DEFAULT_FILTER); // Le buffer filter, on va mettre les modif du filtre ici, et on remplacement fetchFilter par celui-ci lorsqu'on appuie sur le boutton
  const [fetchFilter, setFetchFilter] = useState(DEFAULT_FILTER); // Le filter qui va servir aux fetch des data
  const [showResult, setShowResult] = useState(true); // Boolean qui permet de cacher / show les résultats lorsque l'on modifie les filters => Fluidifie la mécanique pour l'user

  // Utilisation du filter pour filtrer les données
  const { loading, error, data, refetch } = useQuery(QUERY_USERS_ACTIVE, {
    fetchPolicy: 'no-cache',
    variables: {
      activeUsersFilter: fetchFilter,
      startDate: fetchFilter?.startDate,
      endDate: fetchFilter?.endDate
    }
  });

  const defaultDataSelecter = 'ActiveUsers';
  const [dataShowed, setDataShowed] = useState(dataSupported[defaultDataSelecter]);

  const users = data?.findUsersActiveForDates?.users;
  const stats = data?.findUsersActiveForDates?.stats;
  const { companyInfos } = useExoteachCompaniesQuery();

  const showCoursSelecter =
    dataShowed === dataSupported.AllSeenClasses ||
    dataShowed === dataSupported.UniqueSeenClasses ||
    filter.coursIdsFilter.length > 0;

  const [externalAction, setExternalAction] = useState(null);

  const StatCard = ({
    prefix,
    title,
    value,
    style = { width: '200px', height: '130px' },
    bodyStyle = { fontSize: '18px' },
    loading,
    ribbon = false,
    isSelected = false,
    onClick
  }) => {
    return ribbon ? (
      <Badge.Ribbon color={chartColor} text={<BarChartOutlined />}>
        {' '}
        {/* Ajoutez 'text={ribbon}' si vous souhaitez afficher du texte sur le ruban */}
        <Card
          style={{ ...style, border: isSelected && `2px solid ${chartColor}` }}
          //loading={loading}
          bodyStyle={bodyStyle}
          onClick={onClick}
          hoverable
        >
          <Statistic
            prefix={prefix}
            title={title}
            value={value}
            style={{ textAlign: 'center' }}
            loading={loading}
            formatter={(value) =>
              showResult ? value : <EllipsisOutlined style={{ fontSize: '16px' }} />
            }
          />
        </Card>
      </Badge.Ribbon>
    ) : (
      <Card
        style={style}
        //loading={loading}
        bodyStyle={bodyStyle}
      >
        <Statistic
          prefix={prefix}
          title={title}
          value={value}
          style={{ textAlign: 'center' }}
          loading={loading}
          formatter={(value) =>
            showResult ? value : <EllipsisOutlined style={{ fontSize: '16px' }} />
          }
        />
      </Card>
    );
  };

  /*
  const userTotalAndAverageStats = useMemo(() => {
    if (!users) {
      return;
    }
    if (users.length === 0) {
      return;
    }
    const totalStats = users?.reduce(
      (acc, user) => {
        // Assurez-vous que stats existe et est un objet avant de continuer.
        const stats = user.stats ?? {
          sessionCount: 0,
          sessionMinutes: 0,
          seenClasses: 0,
          postsSent: 0,
          mcqDone: 0,
          downloadedFiles: 0,
          totalUniqueSeenClasses: 0,
        };

        return {
          sessionCount: acc.sessionCount + stats?.sessionCount,
          sessionMinutes: acc.sessionMinutes + stats?.sessionMinutes,
          seenClasses: acc.seenClasses + stats?.seenClasses,
          postsSent: acc.postsSent + stats?.postsSent,
          mcqDone: acc.mcqDone + stats?.mcqDone,
          downloadedFiles: acc.downloadedFiles + stats?.downloadedFiles,
          totalUniqueSeenClasses:
            acc.totalUniqueSeenClasses +
            stats?.totalUniqueSeenClasses / (stats?.totalSeeableClasses || 1),
        };
      },
      {
        sessionCount: 0,
        sessionMinutes: 0,
        seenClasses: 0,
        postsSent: 0,
        mcqDone: 0,
        downloadedFiles: 0,
        totalUniqueSeenClasses: 0,
      },
    );

    const userCount = users?.length || 1; // Éviter la division par 0

    return {
      averageSessionCount: totalStats.sessionCount / userCount,
      averageSessionMinutes: totalStats.sessionMinutes / userCount, // Temps moyen passé

      averageSeriesDone: totalStats.mcqDone / userCount, // Temps moyen passé
      averageExercisesDone: 0, //TODO à ajouter backend //globalStats.exercisesDone / userCount, // Temps moyen passé

      // Durée moyenne session:  durée totale de toutes les sessions divisé par nombre de session total tous les utilisateurs
      averageTotalUniqueSeenClasses: totalStats.totalUniqueSeenClasses / userCount,
      ...totalStats,
    };
  }, [users]); // Dépendance [users].
   */

  const GlobalStatsCards = ({ stats, users, loading }) => {
    const { t } = useTranslation();

    // +%
    //const globalProgress = stats?.globalProgress * 100 //(globalStats?.uniqueSeenClasses / (globalStats?.totalSeeableClasses || 1)) * 100;

    //const test = (globalStats?.uniqueSeenClasses / (globalStats?.totalSeeableClasses || 1)) * 100;

    // % total
    //const globalProgressMedian=stats?.progressMedian*100 //const globalProgressAverage = userTotalAndAverageStats?.averageTotalUniqueSeenClasses * 100;

    /*
    //let dureeMoyenneDeSession =
    //  (userTotalAndAverageStats?.sessionMinutes / userTotalAndAverageStats?.sessionCount)?.toFixed(
    //    2,
    //  ) || 0;
    //if (dureeMoyenneDeSession === 'NaN' || dureeMoyenneDeSession === Infinity) {
    //  dureeMoyenneDeSession = 0;
    //}
   */

    return (
      <Space direction="horizontal" wrap>
        <StatCard
          prefix={<UserOutlined />}
          title={t('ActiveUsers')}
          value={users?.length}
          loading={loading}
          ribbon={true}
          onClick={() => {
            setDataShowed(dataSupported.ActiveUsers);
          }}
          isSelected={dataShowed === dataSupported.ActiveUsers}
        />

        <StatCard
          prefix={<HistoryOutlined />}
          title="Temps moyen passé"
          value={prettyPrintMinutes((stats?.sessionMinutes / users?.length).toFixed(2) || 0)}
          loading={loading}
        />

        {/*
          Durée totale de toutes les sessions divisé par le nombre de session
          sessionMinutes / sessionCount
        */}
        <StatCard
          prefix={<HistoryOutlined />}
          title="Durée moy. de session"
          value={prettyPrintMinutes(stats?.averageSessionMinutes?.toFixed(2))}
          loading={loading}
        />

        <StatCard
          prefix={<ReadOutlined />}
          title={'Nombre moy. de sessions'}
          value={stats?.averageSessionCount?.toFixed(2)}
          loading={loading}
        />

        <StatCard
          prefix={<TrophyOutlined />}
          title={t('StatsGraphTraductions.ProgressGeneral')}
          value={`${(stats?.allTimeBatchProgress * 100)?.toFixed(2)}%`}
          loading={loading}
        />

        <StatCard
          prefix={<TrophyOutlined />}
          title={t('StatsGraphTraductions.PeriodeBatchProgress')}
          value={`+${(stats?.periodeBatchProgress * 100)?.toFixed(2)}%`}
          loading={loading}
        />

        {/* Total séries terminées */}
        <StatCard
          prefix={<CheckSquareOutlined />}
          title={t('SeriesDone')}
          value={stats?.mcqDone}
          loading={loading}
        />
        {/* Nombre de séries moy. faites */}
        <StatCard
          prefix={<CheckSquareOutlined />}
          title={'Séries moy. faites'}
          value={stats?.averageMcqDone?.toFixed(2) || 0}
          loading={loading}
        />

        {/* Total exercices faits */}
        <StatCard
          prefix={<CheckSquareOutlined />}
          title={t('ExercicesDone')}
          value={stats?.questionsDone}
          loading={loading}
          ribbon={true}
          onClick={() => {
            setDataShowed(dataSupported.ExercisesDone);
          }}
          isSelected={dataShowed === dataSupported.ExercisesDone}
        />

        {/* Nombre exercices moyen faits */}
        {/*
      <StatCard
        prefix={<CheckSquareOutlined />}
        title={"Exercices moy. faits"}
        value={globalStats?.averageExercisesDone?.toFixed(2) || 0}
        loading={loading}
      />
      */}

        <StatCard
          prefix={<BookOutlined />}
          title={t('StatsGraphTraductions.AllSeenCourses')}
          value={stats?.seenClasses}
          loading={loading}
          ribbon={true}
          onClick={() => {
            setDataShowed(dataSupported.AllSeenClasses);
          }}
          isSelected={dataShowed === dataSupported.AllSeenClasses}
        />

        <StatCard
          prefix={<BookOutlined />}
          title={t('StatsGraphTraductions.uniqueSeenClasses')}
          value={stats?.batchUniqueSeenClassesPeriode}
          loading={loading}
          ribbon={true}
          onClick={() => {
            setDataShowed(dataSupported.UniqueSeenClasses);
          }}
          isSelected={dataShowed === dataSupported.UniqueSeenClasses}
        />
        {
          <StatCard
            prefix={<BookOutlined />}
            title={t('StatsGraphTraductions.UniqueClassesForBatch')}
            value={stats?.allTimeBatchSeeableClassesIds}
            loading={loading}
          />
        }

        <StatCard
          prefix={<MailOutlined />}
          title={t('StatsGraphTraductions.PostsSent')}
          value={stats?.postsSent}
          loading={loading}
          ribbon={true}
          onClick={() => {
            setDataShowed(dataSupported.PostsSent);
          }}
          isSelected={dataShowed === dataSupported.PostsSent}
        />

        <StatCard
          prefix={<DownloadOutlined />}
          title={t('StatsGraphTraductions.AllDownloadedFiles')}
          value={stats?.downloadedFiles}
          loading={loading}
          ribbon={true}
          onClick={() => {
            setDataShowed(dataSupported.DownloadedFiles);
          }}
          isSelected={dataShowed === dataSupported.DownloadedFiles}
        />

        <StatCard
          prefix={<DownloadOutlined />}
          title={t('StatsGraphTraductions.DownloadedFileMedian')}
          value={stats?.medianDownloadedFiles}
          loading={loading}
        />
      </Space>
    );
  };

  // Definition des colonnes
  const columns = [
    {
      title: 'Pseudo',
      dataIndex: 'username',
      key: 'username',
      render: (username, user) => (
        <span style={{ display: 'flex', whiteSpace: 'nowrap' }} key={user.id}>
          <UserProfileCard userId={user?.id} username={user?.username}>
            <ExoAvatar isActive={user.isActive} avatar={user.avatar} size="small" /> &nbsp;{' '}
            {username}
          </UserProfileCard>
        </span>
      )
    },
    {
      title: t('general.firstname'),
      dataIndex: 'firstName',
      key: 'firstName',
      render: (firstName, user) => {
        return firstName;
      }
    },
    {
      title: t('general.lastname'),
      dataIndex: 'name',
      key: 'name',
      render: (lastName, user) => {
        return lastName;
      }
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      filters: [
        { text: 'SuperAdmin', value: 'ADMIN' },
        { text: 'Admin', value: 'SUB_ADMIN' },
        { text: 'Tuteur', value: 'TUTEUR' },
        { text: 'Utilisateur', value: 'USER' }
      ],
      render: (role, user) => (
        <Tag
          key={user.id}
          color={role === 'ADMIN' ? 'geekblue' : 'purple'}
          style={{ height: 'auto' }}
        >
          {mapRoleToShortRoleName(role)}
        </Tag>
      )
    },

    {
      title: 'Groupes',
      dataIndex: 'groups',
      key: 'groups',
      hideInSearch: true,
      width: '300',
      render: (groups, user) => (
        <UserGroupsManager
          key={user.id}
          groupes={groups?.filter((g) => !g?.isIndividual)}
          user={user}
        />
      )
    },
    /*
    {
      title: 'Groupe',
      dataIndex: 'groups',
      key: 'groups',
      render: (groups, user) => {
        return (
          <>
            {groups.map(value => {
              return <Tag>{value.name}</Tag>;
            })}
          </>
        );
      },
    },

     */
    {
      title: 'Sessions',
      dataIndex: 'stats',
      key: 'sessionCount',
      render: (s, user) => {
        return <SessionsDetails stats={s} user={user} filter={filter} />;
      }
    },
    {
      title: 'Durée',
      dataIndex: 'stats',
      key: 'sessionMinutes',
      render: (stats, user) => {
        return prettyPrintMinutes(stats?.sessionMinutes);
      }
    },
    {
      title: 'Progression totale (profile sur la période)',
      dataIndex: 'stats',
      key: 'globalProgress',
      render: (stats, user) => {
        let periodProgress = (stats?.uniqueSeenClasses / (stats?.totalSeeableClasses || 1)) * 100;
        let allProgress = (stats?.totalUniqueSeenClasses / stats?.totalSeeableClasses || 1) * 100;
        if (periodProgress === Infinity || periodProgress === 'NaN') {
          periodProgress = 0;
        }
        if (allProgress === Infinity || allProgress === 'NaN') {
          allProgress = 0;
        }
        return `${allProgress?.toFixed(2)}% (+${periodProgress?.toFixed(2)}%)`;
      }
    },
    {
      title: t('StatsGraphTraductions.AllSeenCourses'),
      dataIndex: 'stats',
      key: 'seenClasses',
      sorter: (a, b) => a.stats && b.stats && a.stats?.seenClasses - b.stats?.seenClasses,
      render: (stats) => stats?.seenClasses
    },
    {
      title: t('StatsGraphTraductions.uniqueSeenClasses'),
      dataIndex: 'stats',
      key: 'uniqueSeenClasses',
      sorter: (a, b) =>
        a.stats && b.stats && a.stats?.uniqueSeenClasses - b.stats?.uniqueSeenClasses,
      render: (stats) => stats?.uniqueSeenClasses
    },
    {
      title: t('StatsGraphTraductions.TotalSeeableClasses'),
      dataIndex: 'stats',
      key: 'totalSeeableClasses',
      sorter: (a, b) =>
        a.stats && b.stats && a.stats?.totalSeeableClasses - b.stats?.totalSeeableClasses,
      render: (stats) => stats?.totalSeeableClasses
    },
    {
      title: 'Posts envoyés',
      dataIndex: 'stats',
      key: 'postsSent',
      sorter: (a, b) => a.stats && b.stats && a.stats?.postsSent - b.stats?.postsSent,
      render: (stats) => stats?.postsSent
    },
    {
      title: "Séries d'exercice terminées",
      dataIndex: 'stats',
      key: 'mcqDone',
      sorter: (a, b) => a.stats && b.stats && a.stats?.mcqDone - b.stats?.mcqDone,
      render: (stats) => stats && stats?.mcqDone
    },
    {
      title: 'Cours téléchargés',
      dataIndex: 'stats',
      key: 'downloadedFiles',
      sorter: (a, b) => a.stats && b.stats && a.stats?.downloadedFiles - b.stats?.downloadedFiles,
      render: (stats) => stats && stats?.downloadedFiles
    },
    {
      title: t('Company'),
      dataIndex: 'companiesDescriptions',
      key: 'companiesDescriptions',
      render: (companiesDescriptions) => {
        return (
          <div>
            {companiesDescriptions.map((value) => {
              return <Tag>{value.companyName}</Tag>;
            })}
          </div>
        );
      }
    }
  ];

  const filters = (
    <div style={{ textAlign: 'center' }}>
      <Space>
        <b>{t('period')}</b>
        <DatePicker
          value={dayjs(filter?.startDate)}
          format="DD/MM/YYYY"
          style={{ display: 'inline-block' }}
          size="middle"
          placeholder=""
          onChange={async (value) => {
            setFilter({ ...filter, startDate: value.toDate() });
            setShowResult(false);
          }}
        />
        ➡️
        <DatePicker
          value={dayjs(filter?.endDate)}
          format="DD/MM/YYYY"
          style={{ display: 'inline-block' }}
          size="middle"
          placeholder=""
          onChange={async (value) => {
            setFilter({ ...filter, endDate: value.toDate() });
            setShowResult(false);
          }}
        />
      </Space>
      <br />
      <br />
      <Space>
        <Button
          icon={<BackwardOutlined />}
          onClick={() => {
            setFilter({
              ...filter, // je dois rajouter ça pour relancer le fetch
              startDate: dayjs(filter?.startDate).subtract(1, 'month').startOf('month').toDate(),
              endDate: dayjs(filter?.endDate).subtract(1, 'month').endOf('month').toDate()
            });
            setShowResult(false);
          }}
        >
          {t('previousMonth')}
        </Button>
        <Button
          onClick={() => {
            setFilter({
              ...filter, // je dois rajouter ça pour relancer le fetch
              startDate: dayjs().startOf('month').toDate(),
              endDate: dayjs().endOf('month').toDate()
            });
            setShowResult(false);
          }}
        >
          {t('currentMonth')}
        </Button>
        <Button
          icon={<ForwardOutlined />}
          onClick={() => {
            setFilter({
              ...filter, // je dois rajouter ça pour relancer le fetch
              startDate: dayjs(filter?.startDate).add(1, 'month').startOf('month').toDate(),
              endDate: dayjs(filter?.endDate).add(1, 'month').endOf('month').toDate()
            });
            setShowResult(false);
          }}
        >
          {t('nextMonth')}
        </Button>
      </Space>
      <br />
      <br />

      <AbstractGroupsManager
        onChange={(toAdd, groupId) => {
          if (toAdd) {
            const temp = [...new Set([...filter?.groupIdArray, groupId])];
            setFilter({ ...filter, groupIdArray: temp });
          } else if (toAdd === false) {
            const temp = filter?.groupIdArray.filter(
              (item) => !(groupId === item || groupId === parseInt(item))
            );
            setFilter({ ...filter, groupIdArray: temp });
          }
        }}
        onChangeArray={(groupArray, toAdd) => {
          if (toAdd) {
            const temp = [...new Set([...filter?.groupIdArray, ...groupArray])];
            setFilter({ ...filter, groupIdArray: temp });
          } else if (toAdd === false) {
            const temp = filter?.groupIdArray.filter(
              (item) => !(groupArray.includes(item) || groupArray.includes(parseInt(item)))
            );
            setFilter({ ...filter, groupIdArray: temp });
          } else {
            throw new Error(`toAdd ni true ni false, : ${JSON.stringify(toAdd)}`);
          }
        }} // Si on ne veut pas utiliser de mutation, on peut utiliser cette fonction pour gérer les changements (envoi ids groupes en array)
        style={{ width: '80%' }}
      />

      <br />
      <br />
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        <Input
          placeholder={t('AdminStatPage.SearchByPseudo')}
          allowClear
          onChange={(e) => {
            setFilter({ ...filter, pseudoFilter: e.target.value });
            setShowResult(false);
          }}
          onClear={() => {
            setFilter({ ...filter, pseudoFilter: DEFAULT_FILTER.pseudoFilter });
          }}
          style={{ width: '400px' }}
        />
        &nbsp;&nbsp;
        <Select
          mode="multiple"
          allowClear
          style={{ width: '400px' }}
          placeholder={t('AdminStatPage.SearchByRole')}
          onChange={(value) => {
            setFilter({ ...filter, roleFilter: value });
            setShowResult(false);
          }}
        >
          {Object.keys(AUTHORITIES).map((role) => (
            <Select.Option key={role} value={role}>
              {mapRoleToShortRoleName(role)}
            </Select.Option>
          ))}
        </Select>
        &nbsp;&nbsp;
        <Select
          mode="multiple"
          allowClear
          style={{ width: '400px' }}
          onChange={(value) => {
            setFilter({ ...filter, companyFilter: value });
            setShowResult(false);
          }}
          placeholder={t('AdminStatPage.SearchByCompany')}
        >
          {companyInfos?.map((companyInfo) => (
            <Select.Option key={companyInfo?.id} value={companyInfo?.id}>
              {companyInfo?.commercialName}
            </Select.Option>
          ))}
        </Select>
      </div>
      <br />
    </div>
  );

  return (
    <>
      {filters}

      <div style={{ display: 'flex', flexGrow: 1, justifyContent: 'center' }}>
        <Button
          type={'primary'}
          loading={loading}
          ghost
          onClick={() => {
            setFetchFilter(filter);
            setShowResult(true);
          }}
        >
          {t('StatsGraphTraductions.LaunchFetch')}
          {!showResult && <BulbTwoTone twoToneColor={'#FFBF00'} />}
        </Button>
      </div>
      <div style={{ display: 'flex', flexGrow: 1, justifyContent: 'right', marginRight: '50px' }}>
        <Button type={'primary'} loading={loading}>
          {t('StatsGraphTraductions.ResetFilter')}
        </Button>
      </div>

      <>
        <div
          style={{
            marginTop: '24px',
            marginBottom: '32px',
            display: 'flex',
            flexDirection: 'row'
          }}
        >
          {showResult ? (
            <div style={{ width: '50%' }}>
              <GlobalStatsCards
                //userTotalAndAverageStats={userTotalAndAverageStats}
                users={users}
                stats={stats}
                loading={loading}
              />
            </div>
          ) : (
            <div style={{ width: '50%' }}>
              <GlobalStatsCards
                //userTotalAndAverageStats={userTotalAndAverageStats}
                users={users}
                stats={stats}
                loading={false}
              />
            </div>
          )}
          <div style={{ width: '50%', display: 'flex', flexDirection: 'column' }}>
            {showCoursSelecter && (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'column'
                }}
              >
                <HierarchySelecter
                  multiple
                  setterHookSelection={(value) => {
                    setFilter({ ...filter, coursIdsFilter: value });
                    setShowResult(false);
                  }}
                  useTreeSelect
                  rankToRemoveIfLeaf={[
                    validesTypes.CTYPE_UNKNOWN,
                    validesTypes.CTYPE_FOLDER,
                    validesTypes.CTYPE_CATEGORY,
                    validesTypes.CTYPE_PAGE,
                    validesTypes.CTYPE_UE
                  ]}
                  simplificationFeature={validesTypes.CTYPE_COURS}
                  additionalTreeProps={{
                    placement: 'topLeft',
                    listHeight: 500,
                    popupMatchSelectWidth: false,
                    treeLine: true,
                    placeholder: t('general.SelectCoursesPlaceholder'),
                    style: { width: '80%' }
                  }}
                  isTreeSelectCheckable={true}
                  externalAction={externalAction}
                  resetExternalAction={() => {
                    setExternalAction(null);
                  }}
                />
                <div>
                  <Button
                    onClick={() => {
                      setExternalAction(hierarchyComponantActions.CHECK_ALL);
                    }}
                  >
                    {t('FilterQuestions.CheckAllCourses')}
                  </Button>
                  <Button
                    onClick={() => {
                      setExternalAction(hierarchyComponantActions.UNCHECK_ALL);
                    }}
                  >
                    {t('FilterQuestions.UncheckAllCourses')}
                  </Button>
                </div>
              </div>
            )}
            {showResult && (
              <PlotGraph
                activeUsersFilter={fetchFilter}
                dataShowed={dataShowed || defaultDataSelecter}
                setActiverFilterCoursId={(value) => {
                  setFilter({ ...filter, coursIdsFilter: value });
                  setShowResult(false);
                }}
              />
            )}
          </div>
        </div>
        {!error && showResult && (
          <>
            <Space style={{ display: 'flex', justifyContent: 'flex-start' }}>
              <ExportAndDownloadStatsAsXLS filter={fetchFilter} />
            </Space>
            <Table
              loading={loading}
              columns={columns}
              dataSource={users}
              pagination={{
                defaultPageSize: 10,
                pageSizeOptions: [10, 50, 100, 1000, 999999]
              }}
              onSubmit={(params) => {
                setFilter(params);
              }}
              showHeader
              scroll={{ x: true }}
            />
          </>
        )}
      </>
      {error && !loading && <ErrorResult refetch={refetch} error={error} />}
    </>
  );
}
