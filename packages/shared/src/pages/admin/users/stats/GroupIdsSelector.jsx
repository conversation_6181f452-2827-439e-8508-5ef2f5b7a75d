import { QUERY_ALL_GROUPS } from '@/shared/graphql/cours.js';
import { QUERY_ALL_FOLDERS } from '@/shared/graphql/folders.js';
import { mapGroupsForTreeSelection } from '@/shared/services/groupes.js';
import { useQuery } from '@apollo/client';
import { Tag, TreeSelect } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

export const GroupsIdSelecter = ({ groupes, setterId ,style={}}) => {
    const { t } = useTranslation();
    const dataGroupes = useQuery(QUERY_ALL_GROUPS, { fetchPolicy: 'cache-and-network' });

    // All Folders query
    const {
        loading: loadingFolders,
        error: errorFolders,
        data: dataFolders,
        refetch: refetchFolders,
    } = useQuery(QUERY_ALL_FOLDERS, { fetchPolicy: 'cache-and-network' });
    const folders = dataFolders?.folders;

    const allGroupes = dataGroupes?.data?.allGroupes;

    const foldersIds = allGroupes?.map(gr => gr?.folderId);
    const foldersToShow = folders?.filter(f => foldersIds?.includes(f.id));

    const [localState,setLocalState]=useState(groupes)

    const groupeTagRender = ({ label, value, closable, onClose, key }) => (
        <Tag value={value} key={key} color="geekblue" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
            {label}
        </Tag>
    );

    const handleSelect = async (_, option,groupIdList) => {
        if (!groupIdList.includes(option.key)) {
            groupIdList.push(option.key);
        }
    };

    const handleDeselect = async (_, option,groupIdList) => {
        groupIdList.splice(groupIdList.indexOf(option.key),1)
    };

    const handleChange = async (groupIdList,shouldAdd, groupId) => {
        const option = { key: groupId, value: allGroupes.find(g => g.id === groupId)?.name };
        if (shouldAdd) {
            await handleSelect(null, option,groupIdList);
        } else {
            await handleDeselect(null, option,groupIdList);
        }
    };

    return (
        <>
            {allGroupes && foldersToShow && groupes && (
                <TreeSelect
                    treeCheckable
                    placeholder={t('AdminStatPage.FilterByGroups')}
                    treeNodeFilterProp="title"
                    treeData={mapGroupsForTreeSelection(foldersToShow, allGroupes)}
                    defaultValue={groupes?.map(groupe => ({ key: groupe.id, title: groupe.name, value: groupe?.id }))}
                    style={{ ...style}}
                    onChange={async (newValue, label, extra) => {
                        const groupId = extra.triggerValue;
                        const shouldAdd = extra.checked;
                        let groupIdList=[...localState] // Récup du local state et important de spread [...], sinon filter pas réactualisé

                        if (groupId?.startsWith('folder')) {
                            // Find groups in folder
                            const folder = mapGroupsForTreeSelection(foldersToShow, allGroupes)?.find(g => g?.key === groupId);
                            // Multiple group
                            for (const group of folder.children) {
                                handleChange(groupIdList,shouldAdd, group?.key);
                            }
                        } else {
                            // Single group
                            handleChange(groupIdList,shouldAdd, groupId);
                        }
                        setterId(groupIdList)
                        setLocalState(groupIdList)
                    }}
                    tagRender={allGroupes && groupeTagRender}
                />
            )}
        </>
    );
};

