import { CREATE_FORFAIT } from '@/shared/graphql/forfaits.js';
import { useExoteachCompaniesQuery } from '@/shared/hooks/config/useConfigHooks.js';
import { useMutation } from '@apollo/client';
import { Button, Form, Input, Modal, Select } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function(
  {
    visible,
    onCancel,
    onOk,
    folderId,
    createMinimalForfaitType,
  }) {

  const [createForfait, { loading: loadingCreation }] = useMutation(CREATE_FORFAIT);
  const {t} = useTranslation();
  const [form] = Form.useForm();
  // Facturé par (get companyInfos)
  const { companyInfos } = useExoteachCompaniesQuery();

  const handleFinish = async inputData => {
    const data = { ...inputData };
    data.paymentSettings = {
      companyId: data?.company
    }
    delete data?.company;
    data.type = createMinimalForfaitType;
    data.folderId = folderId;
    const result = await createForfait({
      variables: {
        forfait: data,
      },
    });
    const forfaitCreated = result?.data?.createForfait;
    if (forfaitCreated) {
      form.resetFields();
      onOk(forfaitCreated);
    }
  }

  return (
    <Modal
      title={t('Create')}
      open={visible}
      onCancel={onCancel}
      footer={null}
      closable
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
      >
        <Form.Item
          name="name"
          label={t('SubscriptionName')}
          rules={[
            { required: true, min: 1, message: `Veuillez entrer le nom du forfait` },
          ]}
        >
          <Input type="text" placeholder={t('ExempleName')} />
        </Form.Item>
        <Form.Item
          label={t('BilledBy')}
          name="company"
          rules={[{ required: true, message: t('EnterCompanyNameForfaitForm') }]}
        >
          <Select>
            {companyInfos?.map(companyInfo => (
              <Select.Option value={companyInfo?.id}>{companyInfo?.commercialName}</Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loadingCreation}>
            {t('Create')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  )
}