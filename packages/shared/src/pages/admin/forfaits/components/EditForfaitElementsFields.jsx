import { SimpleMoveTransition } from '@/shared/assets/transitions/SimpleMoveTransition.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { GET_ELEMENTS_IN_FORFAIT } from '@/shared/graphql/user/user-properties.js';
import FormationEditableElement from '@/shared/pages/formations/components/FormationEditableElement.jsx';
import { CreateEditFormationElementModal } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal.jsx';
import { FormationContextProvider } from '@/shared/pages/formations/context/FormationContext.jsx';
import { ELEMENTS_TYPE } from '@/shared/services/formations.js';
import { PlusCircleTwoTone } from '@ant-design/icons';
import { Button } from 'antd';
import React, { useState } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import { AnimatePresence } from 'framer-motion/dist/framer-motion';
import { useTranslation } from 'react-i18next';
import { ADD_ELEMENT_TO_FORFAIT, REMOVE_ELEMENT_FROM_FORFAIT } from '@/shared/graphql/forfaits';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';

/**
 * Création / Édition éléments d'un forfait, avec éléments de type "input"
 *
 * @param elements
 * @param refetchElements
 * @param forfaitId
 * @returns {JSX.Element}
 * @constructor
 */
const EditElementsInForfait = ({ elements, refetchElements, forfaitId }) => {
  const { t, i18n } = useTranslation();

  // Mutations addToForfait / removeFromForfait
  const [addToForfait, { loading: loadingAddElem }] = useMutation(ADD_ELEMENT_TO_FORFAIT);
  const [removeFromForfait, { loading: loadingRemoveElem }] = useMutation(
    REMOVE_ELEMENT_FROM_FORFAIT
  );

  /* Elements related */
  const [createVisible, setCreateVisible] = useState(false);
  const [position, setPosition] = useState(null);

  const addElementToForfait = async (elementId) => {
    try {
      await addToForfait({
        variables: {
          forfaitId,
          elementId
        }
      });
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  const removeElementFromForfait = async (elementId) => {
    try {
      await removeFromForfait({
        variables: {
          forfaitId,
          elementId
        }
      });
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  const renderElement = (element, previousElement, nextElement, key, columnPosition) => (
    <FormationEditableElement
      style={{ transition: 'transform .35s ease-in-out' }}
      key={key}
      element={element}
      nextElement={nextElement}
      previousElement={previousElement}
      columnPosition={columnPosition}
      userPropertyFolderId={element?.userPropertyFolderId}
      canEdit
      refetchAll={refetchElements}
    />
  );

  const elementCreationComponents = (
    <>
      {createVisible && (
        <div
          style={{
            border: '1px dashed #b5b5b5',
            borderRadius: '11px',
            margin: 5,
            marginBottom: '15px'
          }}
        >
          <div style={{ margin: '15px' }}>
            <CreateEditFormationElementModal
              isModalVisible={createVisible}
              modalType="CREATE"
              position={position}
              closeModalHandler={async (newId) => {
                setCreateVisible(false);
                if (newId) {
                  // Ajoute au formulaire le new element
                  await addElementToForfait(newId);
                }
                refetchElements();
              }}
              elementsTypesToShow={{
                // Affichage
                [ELEMENTS_TYPE.TITLE]: true,
                [ELEMENTS_TYPE.IMAGE]: true,
                [ELEMENTS_TYPE.MCQ]: false,
                [ELEMENTS_TYPE.LINK]: true,
                [ELEMENTS_TYPE.HTML]: true,
                [ELEMENTS_TYPE.COURS]: false,
                [ELEMENTS_TYPE.COURSE_SHORTCUT]: false,
                [ELEMENTS_TYPE.FILE]: false,
                [ELEMENTS_TYPE.RICH_TEXT]: true,
                [ELEMENTS_TYPE.VIDEO]: false,
                [ELEMENTS_TYPE.VIDSTACK_VIDEO]: true,
                [ELEMENTS_TYPE.VIDSTACK_AUDIO]: true,
                [ELEMENTS_TYPE.DIAPO]:true,
                [ELEMENTS_TYPE.CALLOUT]: true,
                [ELEMENTS_TYPE.SCORM]:false,
                // Début de "step"
                [ELEMENTS_TYPE.SECTION]: true,
                //-------

                // INPUTS
                [ELEMENTS_TYPE.SHORT_ANSWER]: true,
                [ELEMENTS_TYPE.LONG_ANSWER]: true,
                [ELEMENTS_TYPE.SINGLE_SELECT]: true,
                [ELEMENTS_TYPE.MULTIPLE_SELECT]: true,
                [ELEMENTS_TYPE.INTEGER_NUMBER]: true,
                [ELEMENTS_TYPE.DATE_PICKER]: true,
                [ELEMENTS_TYPE.DATE_AND_TIME_PICKER]: true,
                [ELEMENTS_TYPE.FILE_IMPORT]: true, // Import de fichier

                [ELEMENTS_TYPE.AVATAR_SELECT]: false, // pas à l'inscription

                [ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT]: true, // Champs prédéfinis globaux (seront importés à travers création d'élément proxy)
                [ELEMENTS_TYPE.REGISTER_FIELD]: true // Champs d'inscription prédéfinis
              }}
            />
          </div>
        </div>
      )}
    </>
  );

  const renderFormationElementCreation = (elementPosition = null) => (
    <>
      {elementPosition === position && elementCreationComponents}
      {!createVisible && (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
          <div style={{ width: '90%' }}>
            <Button
              style={{ marginTop: 10, minHeight: '50px' }}
              type="dashed"
              block
              icon={<PlusCircleTwoTone />}
              onClick={() => {
                setPosition(elementPosition);
                setCreateVisible(true);
              }}
            >
              {t('AddElementHere')}
            </Button>
          </div>
        </div>
      )}
    </>
  );

  return (
    <AnimatePresence mode="popLayout">
      {elements?.map((element, k) => (
        <SimpleMoveTransition id={element.id} key={element.id}>
          {renderElement(element, elements[k - 1], elements[k + 1], element?.id)}
        </SimpleMoveTransition>
      ))}
      {renderFormationElementCreation()}
    </AnimatePresence>
  );
};

/**
 * EditForfaitElementsFields
 *
 * Champs additionnels (globaux) pour un forfait
 *
 * @param forfaitId
 * @returns {JSX.Element}
 * @constructor
 */
export const EditForfaitElementsFields = ({ forfaitId }) => {
  const { t } = useTranslation();

  const {
    data: dataElements,
    loading: loadingElementsInForfait,
    refetch
  } = useQuery(GET_ELEMENTS_IN_FORFAIT, {
    fetchPolicy: 'no-cache',
    variables: {
      id: forfaitId
    }
  });
  const elements = dataElements?.elementsInForfait;

  return (
    <React.Fragment>
      {loadingElementsInForfait ? (
        <SpinnerCentered />
      ) : (
        <FormationContextProvider>
          <EditElementsInForfait
            elements={elements}
            refetchElements={refetch}
            forfaitId={forfaitId}
          />
        </FormationContextProvider>
      )}
    </React.Fragment>
  );
};
