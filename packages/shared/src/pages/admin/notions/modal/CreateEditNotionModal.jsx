import { ContentWithMathJax } from '@/shared/components/ContentWithMathJax.jsx';
import ExoFormImage from '@/shared/components/Forms/ExoFormImage.jsx';
import {
  ADD_RemoveNotionQuestionsFromKeyword,
  CREATE_NOTION,
  CREATE_NOTION_KEYWORD,
  DELETE_NOTION,
  EDIT_NOTION,
  GET_NOTION_BY_ID_WITH_PARENTS_AND_CHILDREN,
  REMOVE_CHILDREN_NOTION,
  REMOVE_NOTION_KEYWORD,
  REMOVE_PARENT_NOTION,
  UPDATE_NOTION_KEYWORD
} from '@/shared/graphql/notions.js';
import {
  NotionActionsTypes,
  NotionTable
} from '@/shared/pages/admin/notions/components/NotionsTable.jsx';
import { equationEditorLink } from '@/shared/pages/admin/qcm/components/modal/components/EditAnswersQuestion.jsx';
import { CONFIG_KEYS } from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import { mediboxEditorFullSettingsProps } from '@/shared/utils/editor.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  notification,
  Popconfirm,
  Popover,
  Row,
  Space,
  Tabs,
  Tag,
  Tooltip
} from 'antd';
import { useMutation, useQuery } from '@apollo/client';
import { debounce } from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult';
import { useTranslation } from 'react-i18next';
import ReactQuill from 'react-quill-new';
import router from 'umi/router.js';

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE'
};

const getMutationFromModalType = (modalType) => {
  switch (modalType) {
    case ModalType.CREATE:
      return CREATE_NOTION;
    case ModalType.UPDATE:
      return EDIT_NOTION;
    default:
      return CREATE_NOTION;
  }
};

export const CreateEditNotionModal = ({ closeModalHandler, modalType, isVisible, id }) => {
  const { t } = useTranslation();
  const { data: dataNotion, refetch } = useQuery(GET_NOTION_BY_ID_WITH_PARENTS_AND_CHILDREN, {
    fetchPolicy: 'cache-and-network',
    variables: { id },
    skip: !id
  });
  const notion = dataNotion && dataNotion.notion;

  const [form] = Form.useForm();
  const [Mutation, { loading, data, error }] = useMutation(getMutationFromModalType(modalType));
  const [MutationCreateKeyword, { loading: loadingCreate }] = useMutation(CREATE_NOTION_KEYWORD);
  const [MutationUpdateKeyword, { loading: loadingUpdate }] = useMutation(UPDATE_NOTION_KEYWORD);
  const [MutationRemoveKeyword, { loading: loadingRemove }] = useMutation(REMOVE_NOTION_KEYWORD);
  const [deleteNotion] = useMutation(DELETE_NOTION);

  const [shouldCloseModalAfterSubmit, setShouldCloseModalAfterSubmit] = useState(true);
  const [fileImageContent, setFileImageContent] = useState(null); // null = indéfini, 'delete' si supprimer

  const [searchNotionsModalVisible, setSearchNotionsModalVisible] = useState(false);

  const [selectedNotionActionType, setSelectedNotionActionType] = useState('default');
  const isLoading = loading || loadingCreate || loadingUpdate || loadingRemove;

  const [removeParentNotion] = useMutation(REMOVE_PARENT_NOTION);
  const [removeChildrenNotion] = useMutation(REMOVE_CHILDREN_NOTION);

  const [MutationAddRemoveNotionQuestionFromKeyword, { loading: loadingAddRemoveNotion }] =
    useMutation(ADD_RemoveNotionQuestionsFromKeyword);

  //const [formula, setFormula] = useState(false)

  // Quill
  const [editorContent, setEditorContent] = useState(notion?.description || '');

  useEffect(() => {
    setEditorContent(notion?.description);
  }, [dataNotion]);

  const handleMassAttributionFromKeyword = async (keywords, action) => {
    try {
      const keywordText = ` (${keywords?.map((k) => `${k} `)})`;
      const messageInfos =
        action === 'remove'
          ? `Dé-attribution en cours... ${keywordText}`
          : `Attribution en cours... ${keywordText}`;
      notification.info({
        message: messageInfos,
        description: 'Cela peut prendre du temps, veuillez garder la page ouverte'
      });
      const result = await MutationAddRemoveNotionQuestionFromKeyword({
        variables: {
          keywords,
          notionId: notion?.id,
          action
        }
      });
      const { totalAnswers, totalQuestions } = result?.data?.addRemoveNotionQuestionsFromKeyword;
      notification.success({
        message: 'Terminé',
        description: `${totalAnswers} réponses et ${totalQuestions} questions (chapeaux) ${action === 'add' ? 'attribués' : 'dé-attribués'}`
      });
      return true;
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
      return false;
    }
  };

  const handleFinish = async (data) => {
    try {
      let notion;
      if (fileImageContent) {
        notion = { image: fileImageContent };
      }
      notion = { ...notion, description: editorContent };
      if (modalType === ModalType.UPDATE) {
        notion = { ...notion, name: data.name, formula: data?.formula };
        await Mutation({ variables: { id, notion } });
        await Promise.all(
          data.keywords.map(async (keyword) => {
            const notionKeyword = { name: keyword.name, notionId: id };
            if (keyword.id) {
              await MutationUpdateKeyword({ variables: { id: keyword.id, notionKeyword } });
            } else {
              await MutationCreateKeyword({ variables: { notionKeyword } });
            }
          })
        );

        // mass attribute new keywords
        const newKeywords = data?.keywords?.filter((k) => !k.hasOwnProperty('id'));
        if (newKeywords && newKeywords?.length > 0) {
          //console.log({newKeywords})
          await handleMassAttributionFromKeyword(
            newKeywords?.map((k) => k.name),
            'add'
          );
        }

        await refetch();

        message.success(t('Updated'));
      } else {
        // Create
        notion = { ...notion, name: data.name, keywords: data.keywords };
        await Mutation({ variables: { notion } });
        // Attribution
        // mass attribute new keywords
        const newKeywords = data?.keywords?.filter((k) => !k.hasOwnProperty('id'));
        if (newKeywords && newKeywords?.length > 0) {
          await handleMassAttributionFromKeyword(
            newKeywords?.map((k) => k.name),
            'add'
          );
        }
        message.success(t('Created'));
      }
      form.resetFields();
      setFileImageContent(null);
      if (shouldCloseModalAfterSubmit) {
        await closeModalHandler();
      }
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  const onRemoveKeyword = async (field) => {
    try {
      let iddd = form.getFieldValue('keywords')[field.name]?.id;
      if (!iddd && form.getFieldValue('keywords')[0]) {
        iddd = form.getFieldValue('keywords')[0]?.id;
      }
      if (iddd && isKeywordSaved(field)) {
        // Dé-attribue
        await handleMassAttributionFromKeyword(
          [form.getFieldValue('keywords')[field.name]?.name],
          'remove'
        );
        // Supprime
        await MutationRemoveKeyword({ variables: { notionKeywordId: iddd } });
        message.success(t('DeletedWithSuccess'));
      }
      return true;
    } catch (e) {
      console.error(e);
    }
  };
  const onCreateKeyword = async () => {
    try {
      const notionKeyword = { name: '', notionId: id };
      return await MutationCreateKeyword({ variables: { notionKeyword } });
    } catch (e) {
      console.error(e);
    }
  };

  React.useEffect(() => {
    if (notion) {
      form.setFieldsValue({
        name: notion.name,
        keywords: notion.keywords
      });
    }
  }, [form, notion]);

  const canShowModal = notion || modalType === ModalType.CREATE;

  /* Preview LaTeX formula */
  const [formValuesForPreview, setFormValuesForPreview] = useState(notion);
  const debouncedSave = useCallback(
    debounce((nextValue) => setFormValuesForPreview(nextValue), 100),
    [] // will be created only once initially
  );

  const isKeywordSaved = (field) => {
    return form.getFieldValue('keywords')[field.name]?.hasOwnProperty('id');
  };

  return (
    <>
      {canShowModal && (
        <Modal
          title={modalType === ModalType.UPDATE ? `${t('Edit')} ${notion.name}` : t('Create')}
          open={isVisible}
          onCancel={closeModalHandler}
          footer={null}
          closable
          confirmLoading={false}
          destroyOnClose
          bodyStyle={{ paddingTop: 0 }}
          width={1000}
        >
          <Tabs defaultActiveKey="1">
            <Tabs.TabPane tab={t('tab.General')} key={1}>
              {/* Show small error(s) if needed */}
              <SmallErrorsAlert error={error} loading={loading} />
              <Form
                layout="vertical"
                onFinish={handleFinish}
                form={form}
                initialValues={{
                  name: notion?.name,
                  keywords: notion?.keywords,
                  formula: notion?.formula
                }}
                onValuesChange={async (values) => {
                  debouncedSave(values);
                }}
              >
                <Form.Item
                  name="name"
                  label="Nom de la notion"
                  rules={[{ required: true, message: `Veuillez entrer le nom de la notion` }]}
                >
                  <Input defaultValue={notion?.name} type="text" placeholder="Nom" />
                </Form.Item>

                <ExoFormImage
                  name="image"
                  beforeUpload={(file) => setFileImageContent(file)}
                  onDelete={() => setFileImageContent({ shouldDelete: true })}
                  label="Image de la notion"
                  defaultValue={notion?.image}
                  loading={loading}
                />

                <Form.Item label="Description">
                  <ReactQuill
                    className="notion-description-editor"
                    bounds=".notion-description-editor"
                    key="quillEditor"
                    defaultValue={notion?.description || ''}
                    onChange={(editorData) => {
                      setEditorContent(editorData);
                    }}
                    {...mediboxEditorFullSettingsProps}
                    preserveWhitespace
                  />
                </Form.Item>

                <Popover content={<ContentWithMathJax value={formValuesForPreview?.formula} />}>
                  <Form.Item
                    name="formula"
                    label="Formule"
                    style={{ width: '100%' }}
                    extra={
                      <Tooltip
                        title={"Astuce: utilisez $ autour de l'équation LaTeX pour l'afficher"}
                      >
                        <a href={equationEditorLink} rel="noopener noreferrer" target="_blank">
                          <Button type="link">Éditeur d'équations</Button>
                        </a>
                      </Tooltip>
                    }
                  >
                    <Input.TextArea type="textarea" placeholder="" />
                  </Form.Item>
                </Popover>

                <h5>Mots clés associés à la notion</h5>
                <Form.List name="keywords">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map((field, index) => (
                        <Row gutter={[4, 4]} key={field.key}>
                          <Col span={12}>
                            <Form.Item
                              label={index === 0 ? 'Mot clé' : ''}
                              required={false}
                              noStyle
                            >
                              <Form.Item
                                hidden
                                name={[field.id, 'id']}
                                //fieldKey={[field.fieldKey, 'id']}
                              />
                              <Form.Item
                                {...field}
                                name={[field.name, 'name']}
                                fieldKey={[field.fieldKey, 'name']}
                                validateTrigger={['onChange', 'onBlur']}
                                //noStyle
                                rules={[
                                  {
                                    required: 'true',
                                    whitespace: true,
                                    message: `Veuillez entrer un mot clé ou le supprimer`
                                  }
                                ]}
                              >
                                <Input disabled={isKeywordSaved(field)} placeholder="" />
                              </Form.Item>
                            </Form.Item>
                          </Col>

                          {isKeywordSaved(field) &&
                            form.getFieldValue('keywords')[field.name]?.name?.length > 0 && (
                              <>
                                <Col span={3}>
                                  <Button
                                    onClick={() =>
                                      handleMassAttributionFromKeyword(
                                        [form.getFieldValue('keywords')[field.name]?.name],
                                        'add'
                                      )
                                    }
                                  >
                                    Attribuer
                                  </Button>
                                </Col>
                                <Col span={3}>
                                  <Button
                                    onClick={() =>
                                      handleMassAttributionFromKeyword(
                                        [form.getFieldValue('keywords')[field.name]?.name],
                                        'remove'
                                      )
                                    }
                                  >
                                    Dé-attribuer
                                  </Button>
                                </Col>
                              </>
                            )}

                          {/*
                        <Col span={3}>
                          {!isKeywordSaved(field) && (
                            <Button onClick={async () => {
                              setShouldCloseModalAfterSubmit(false)
                              await handleMassAttributionFromKeyword([form.getFieldValue('keywords')[field.name]?.name], 'add')
                              form.submit()
                            }}
                            >
                              Valider
                            </Button>
                          )}
                        </Col>
                        */}

                          <Col span={2}>
                            {fields.length > 0 ? (
                              <>
                                <Tooltip title="Supprimer ce mot clé" style={{ width: '20%' }}>
                                  <MinusCircleOutlined
                                    className="dynamic-delete-button"
                                    onClick={() => {
                                      onRemoveKeyword(field).then(() => {
                                        remove(field.name);
                                      });
                                    }}
                                  />
                                </Tooltip>
                              </>
                            ) : null}
                          </Col>
                        </Row>
                      ))}
                      <Form.Item>
                        <Button
                          type="dashed"
                          block
                          onClick={async () => {
                            await add();
                          }}
                          icon={<PlusOutlined />}
                        >
                          Ajouter un mot clé
                        </Button>
                      </Form.Item>
                    </>
                  )}
                </Form.List>

                <Form.Item>
                  <Space>
                    {modalType === ModalType.UPDATE && (
                      <>
                        <Button htmlType="submit" type="primary" loading={isLoading}>
                          {t('Update')}
                        </Button>
                      </>
                    )}
                    {modalType === ModalType.CREATE && (
                      <Space>
                        <Button
                          onClick={() => {
                            setShouldCloseModalAfterSubmit(true);
                            form.submit();
                          }}
                          type="primary"
                          loading={loading}
                        >
                          {t('CreateAndClose')}
                        </Button>
                        <Button
                          onClick={() => {
                            setShouldCloseModalAfterSubmit(false);
                            form.submit();
                          }}
                          loading={loading}
                        >
                          {t('CreateAndAddAnother')}
                        </Button>
                      </Space>
                    )}
                    <Button
                      onClick={async () => {
                        const keywords = form.getFieldValue('keywords')?.map((k) => k.name);
                        await handleMassAttributionFromKeyword(keywords, 'add');
                      }}
                    >
                      Attribuer tous
                    </Button>
                    <Button
                      onClick={async () => {
                        const keywords = form.getFieldValue('keywords')?.map((k) => k.name);
                        await handleMassAttributionFromKeyword(keywords, 'remove');
                      }}
                    >
                      Dé-attribuer tous
                    </Button>

                    <Popconfirm
                      title="Dé-attribuer tout puis supprimer la notion et les mots clés ?"
                      onConfirm={async (e) => {
                        //console.log(form.getFieldValue('keywords').map(n => n.name))
                        // Dé-attribue tout
                        await handleMassAttributionFromKeyword(
                          form.getFieldValue('keywords').map((n) => n.name),
                          'remove'
                        );

                        // Supprime tout les keywords
                        /*
                                            await form.getFieldValue('keywords').map(async k => {
                                              await MutationRemoveKeyword({ variables: { notionKeywordId: k.id } })
                                            })
                                            */
                        try {
                          // Delete keywords, parents et enfants et notion
                          const resultDeletion = await deleteNotion({
                            variables: { id: notion?.id }
                          });
                          notification.success({ message: 'Suppression terminée !' });
                          refetch();
                          router.push('/admin-notions');
                          closeModalHandler();
                        } catch (e) {
                          showGqlErrorsInMessagePopupFromException(e);
                          console.error(e);
                        }
                      }}
                      okText={t('general.yes')}
                      cancelText={t('general.no')}
                    >
                      <Button danger>Supprimer la notion</Button>
                    </Popconfirm>
                  </Space>
                </Form.Item>
              </Form>
            </Tabs.TabPane>

            {/* NOTION CHILDS */}
            <Tabs.TabPane tab="Enfants" key={2}>
              <h3>Enfants de cette notion</h3>
              {notion?.childrens?.map((n) => (
                <Tag
                  onClose={async () => {
                    await removeChildrenNotion({
                      variables: {
                        notionId: notion.id,
                        childrenNotionId: n.id
                      }
                    });
                  }}
                  closable
                >
                  {n.name}
                </Tag>
              ))}
              {/* query les enfants de la notion */}
              <br />
              <br />
              <NotionTable
                onFinish={() => refetch()}
                mode={NotionActionsTypes.addChild}
                fromNotionId={notion?.id}
                disabledNotionIds={notion?.childrens?.map((n) => n.id)}
              />
            </Tabs.TabPane>

            <Tabs.TabPane tab="Parents" key={3}>
              <h3>Parents de cette notion</h3>
              {notion?.parents?.map((n) => (
                <Tag
                  onClose={async () => {
                    await removeParentNotion({
                      variables: {
                        notionId: notion.id,
                        parentNotionId: n.id
                      }
                    });
                  }}
                  closable
                >
                  {n.name}
                </Tag>
              ))}
              {/* query les parents de la notion */}
              <br />
              <br />
              <NotionTable
                onFinish={() => refetch()}
                mode={NotionActionsTypes.addParent}
                fromNotionId={notion?.id}
              />
            </Tabs.TabPane>
          </Tabs>

          <CreateEditNotionModal
            isVisible={false}
            modalType={ModalType.CREATE}
            closeModalHandler={() => {
              // setCreateVisible(false)
              refetch(); // Load new modifications
            }}
          />
        </Modal>
      )}
    </>
  );
};
