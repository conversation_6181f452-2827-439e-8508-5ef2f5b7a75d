import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx'
import {
  ADD_NOTION_TO_COURS, ADD_NOTION_TO_QUESTION, ADD_NOTION_TO_QUESTION_ANSWER,
  QUERY_COURS_NOTIONS_MANUALLY_ADDED, QUERY_COURS_NOTIONS_AUTO_ADDED, QUERY_QUESTION_ANSWER_NOTIONS,
  QUERY_QUESTION_NOTIONS,
  REMOVE_NOTION_FROM_COURS, REMOVE_NOTION_FROM_QUESTION, REMOVE_NOTION_FROM_QUESTION_ANSWER, SEARCH_NOTIONS,
} from '@/shared/graphql/notions.js'
import { ModalType } from '@/shared/pages/admin/groupes/CreateEditGroupeModal.jsx'
import { CreateEditNotionModal } from '@/shared/pages/admin/notions/modal/CreateEditNotionModal.jsx'
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js'
import { PlusCircleTwoTone } from '@ant-design/icons'
import { useMutation, useQuery } from '@apollo/client'
import { Button, message, Space, Transfer } from 'antd'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next';

export const NotionTarget = {
  COURS: 'cours',
  QUESTION: 'question',
  ANSWER: 'answer',
}

/* Types : cours, question, answer, typeId: coursId, questionId, answerId */
export const EditNotionsLink = ({ type, typeId, autoAdded = false }) => {
  const {t} = useTranslation();
  const getQueryFromType = () => {
    switch (type) {
      case NotionTarget.COURS:
        if (autoAdded) {
          return QUERY_COURS_NOTIONS_AUTO_ADDED
        }
        return QUERY_COURS_NOTIONS_MANUALLY_ADDED
      case NotionTarget.QUESTION:
        return QUERY_QUESTION_NOTIONS
      case NotionTarget.ANSWER:
        return QUERY_QUESTION_ANSWER_NOTIONS
      default:
        return undefined
    }
  }
  const { loading, error, data, refetch } = useQuery(SEARCH_NOTIONS, {
    fetchPolicy: 'no-cache',
    variables: { filter: {} },
  })

  const {
    data: typeNotionData,
    refetch: refetchTypeNotionData,
    loading: typeNotionLoading,
  } = useQuery(getQueryFromType(), {
    fetchPolicy: 'no-cache',
    variables: { id: typeId },
    skip: !typeId,
  })

  const [addNotionToCours] = useMutation(ADD_NOTION_TO_COURS)
  const [removeNotionFromCours] = useMutation(REMOVE_NOTION_FROM_COURS)

  const [addNotionToQuestion] = useMutation(ADD_NOTION_TO_QUESTION)
  const [removeNotionFromQuestion] = useMutation(REMOVE_NOTION_FROM_QUESTION)

  const [addNotionToAnswer] = useMutation(ADD_NOTION_TO_QUESTION_ANSWER)
  const [removeNotionFromAnswer] = useMutation(REMOVE_NOTION_FROM_QUESTION_ANSWER)

  const [targetKeys, setTargetKeys] = useState([]) // Notion IDs inside
  const [selectedKeys, setSelectedKeys] = useState([])
  const [createModalVisible, setCreateModalVisible] = useState(false) // modal

  const allNotions = data && data.searchNotions?.notions

  const getNotionsTargetData = () => {
    switch (type) {
      case NotionTarget.COURS:
        if(autoAdded === true) {
          return typeNotionData?.cour?.notionsAutoAdded
        }
        else if(autoAdded === false) {
          return typeNotionData?.cour?.notionsManuallyAdded
        }
        return typeNotionData?.cour?.notions
      case NotionTarget.QUESTION:
        return typeNotionData && typeNotionData.question && typeNotionData.question.notions
      case NotionTarget.ANSWER:
        return typeNotionData && typeNotionData.answer && typeNotionData.answer.notions
    }
  }
  const notionsTargetData = getNotionsTargetData()

  const isLoading = loading || typeNotionLoading

  const setNotionTargetData = () => {
    if (allNotions && notionsTargetData) {
      const notionsCoursRemaining = notionsTargetData.filter(nc => allNotions.map(n => n.id).includes(nc.id))
      if (notionsCoursRemaining) {
        setTargetKeys(notionsCoursRemaining.map(n => n.id))
      }
    }
  }

  useEffect(setNotionTargetData, [allNotions, notionsTargetData])

  const onChange = async (nextTargetKeys, direction, moveKeys) => {
    try {
      if (direction === 'right') {
        for (const notionId of moveKeys) {
          switch (type) {
            case NotionTarget.COURS:
              await addNotionToCours({
                variables: {
                  notionId,
                  coursId: typeId,
                  autoAdded
                },
              })
              break
            case NotionTarget.ANSWER:
              await addNotionToAnswer(
                {
                  variables: {
                    notionId,
                    answerId: typeId,
                  },
                },
              )
              break
            case NotionTarget.QUESTION:
              await addNotionToQuestion(
                {
                  variables: {
                    notionId,
                    questionId: typeId,
                  },
                },
              )
              break
          }
        }
        message.success(`${moveKeys.length} notion(s) ajoutées`)
      } else if (direction === 'left') {
        for (const notionId of moveKeys) {
          switch (type) {
            case NotionTarget.COURS:
              await removeNotionFromCours({
                variables: {
                  notionId,
                  coursId: typeId,
                  autoAdded
                },
              })
              break
            case NotionTarget.ANSWER:
              await removeNotionFromAnswer({
                variables: {
                  notionId,
                  answerId: typeId,
                },
              })
              break
            case NotionTarget.QUESTION:
              await removeNotionFromQuestion({
                variables: {
                  notionId,
                  questionId: typeId,
                },
              })
              break
          }
        }
        message.success(`${moveKeys.length} notion(s) enlevées`)
      }
      await refetchTypeNotionData()
      setTargetKeys(nextTargetKeys)
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e)
      console.error(e)
    }
  }

  const onSelectChange = (sourceSelectedKeys, targetSelectedKeys) => {
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys])
  }

  const getTargetTitle = () => {
    switch (type) {
      case NotionTarget.COURS:
        return 'Notions dans ce cours'
      case NotionTarget.QUESTION:
        return 'Notions dans cette question'
      case NotionTarget.ANSWER:
        return 'Notions de cette réponse'
    }
  }
  const transferListStyle = {
    width: 300,
    height: 400,
  }
  if (isLoading) {
    return <SpinnerCentered tip="Chargement des notions..."/>
  }
  const createNotionButton = (
    <Button icon={<PlusCircleTwoTone/>} size="small" type="text" onClick={() => setCreateModalVisible(true)}>{t('CreateNotion')}</Button>
  )
  return (
    <Space direction="vertical">
      {createNotionButton}
      <CreateEditNotionModal
        isVisible={createModalVisible}
        modalType={ModalType.CREATE}
        closeModalHandler={() => {
          setCreateModalVisible(false)
          refetch() // Load new modifications
        }}
      />
      <Transfer
        dataSource={allNotions}
        titles={['Notions disponibles', getTargetTitle()]}
        targetKeys={targetKeys}
        selectedKeys={selectedKeys} // qui ne sont pas déjà dans le cours/qcm
        onChange={onChange}
        onSelectChange={onSelectChange}
        render={item => item.name}
        listStyle={transferListStyle}
        showSearch
        rowKey={r => r.id}
        // operations={['Ajouter', 'Enlever']}
      />
    </Space>
  )
}