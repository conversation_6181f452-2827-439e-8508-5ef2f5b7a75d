import React, { useState } from 'react';
import dayjs from 'dayjs';
import { Button, DatePicker } from 'antd';
import { UndoOutlined } from '@ant-design/icons';

export function DatePickerCreation({
  initialValueStart = null,
  initialValueEnd = null,
  setSelection
}) {
  const [selectedDateStart, setSelectedDateStart] = useState(
    initialValueStart ? dayjs(initialValueStart) : null
  );
  const [selectedDateEnd, setSelectedDatesEnd] = useState(
    initialValueEnd ? dayjs(initialValueEnd) : null
  );

  const onChange = (dates, dateStrings) => {
    if (!dates) {
      resetValues();
    } else {
      setSelectedDateStart(dayjs(dateStrings[0]));
      setSelectedDatesEnd(dayjs(dateStrings[1]));
      setSelection(dateStrings[0], dateStrings[1]);
    }
  };

  const resetValues = () => {
    setSelectedDateStart(null);
    setSelectedDatesEnd(null);
    setSelection(null, null);
  };

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <DatePicker.RangePicker onChange={onChange} value={[selectedDateStart, selectedDateEnd]} />
        <Button
          type={'text'}
          icon={<UndoOutlined />}
          onClick={() => {
            resetValues();
          }}
        >
          {' '}
        </Button>
      </div>
    </>
  );
}
