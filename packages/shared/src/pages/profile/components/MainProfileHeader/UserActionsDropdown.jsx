import { useMutation } from '@apollo/client';
import { BLOCK_UNBLOCK_USER } from '@/shared/graphql/user';
import React, { useContext, useState } from 'react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { Button, Dropdown, Menu, notification } from 'antd';
import { downloadFile, FILE_TYPE } from '@/shared/services/file';
import { isAdmin } from '@/shared/utils/authority';
import { ExportOutlined, FlagOutlined, UserOutlined } from '@ant-design/icons';
import { ReportPostModal } from '@/shared/components/Commentaires/ReportPostModal';
import { useTranslation } from 'react-i18next';

export function UserActionsDropdown({ userId, isBlocked, refetch, username, isMyProfile }) {
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;
  const { t } = useTranslation();
  const [BlockUnBlockMutation] = useMutation(BLOCK_UNBLOCK_USER);
  const [reportPostModalVisible, setReportPostModalVisible] = useState(false);
  const onBlockUnblockUser = async () => {
    let action = 'block';
    if (isBlocked) {
      action = 'unblock';
    }
    await BlockUnBlockMutation({
      variables: {
        userId: userId,
        action
      }
    });
    await refetch();
  };

  const actionsMenu = (
    <>
      <Menu
        onClick={(e) => {
          switch (e.key) {
            case 'ExportThisUsersResults':
              notification.info({ message: 'Exportation XLS en cours...', key: 'export' });
              downloadFile(FILE_TYPE.FILE, `export-user-xls-resultsonly/${userId}`).then(() => {
                notification.success({ message: 'Exportation terminée', key: 'export' });
              });
              break;
            case 'ExportThisUsersPlanning':
              notification.info({ message: 'Exportation XLS en cours...', key: 'export' });
              downloadFile(FILE_TYPE.FILE, `export-user-planning/${userId}`).then(() => {
                notification.success({ message: 'Exportation terminée', key: 'export' });
              });
              break;
            case 'blockUnblockUser':
              onBlockUnblockUser();
              break;
            case 'report':
              setReportPostModalVisible(true);
              break;
            default:
              break;
          }
        }}
      >
        {isAdmin() && (
          <Menu.Item key="ExportThisUsersResults" icon={<ExportOutlined />}>
            {t('ExportThisUsersResults')}
          </Menu.Item>
        )}
        {isAdmin() && (
          <Menu.Item key="ExportThisUsersPlanning" icon={<ExportOutlined />}>
            {t('ExportThisUsersPlanning')}
          </Menu.Item>
        )}
        {!isMyProfile && (
          <Menu.Item key="blockUnblockUser" icon={<UserOutlined />}>
            {isBlocked ? t('UnblockAction') : t('BlockAction')} {username}
          </Menu.Item>
        )}
        {!isMyProfile && (
          <Menu.Item key="report" icon={<FlagOutlined />}>
            {t('Report')} {username}
          </Menu.Item>
        )}
      </Menu>
    </>
  );

  return (
    <>
      {(!isMyProfile || isAdmin()) && (
        <>
          <Dropdown overlay={actionsMenu} trigger={'click'}>
            <Button
              shape="circle"
              type="link"
              size="large"
              style={{ rotate: '90deg', color: primaryColor, minWidth: 40, minHeight: 40 }}
            >
              <span style={{ fontSize: 30, marginTop: -8 }}>...</span>
            </Button>
          </Dropdown>
          {reportPostModalVisible && (
            <ReportPostModal
              closeModalHandler={() => setReportPostModalVisible(false)}
              isVisible={reportPostModalVisible}
              userId={userId}
            />
          )}
        </>
      )}
    </>
  );
}
