import {
  QUERY_USER_TIME_SPENT_EXERCISING_BY_UE,
} from '@/shared/graphql/user/index.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { isMobile } from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { Alert, Card } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Chart } from '@antv/g2';

const UserTimeSpentExercising = ({ userId }) => {
  const { t } = useTranslation();
  const { colorsHeader = {} } = useContext(GlobalContext);

  const { data, loading, error, refetch } = useQuery(QUERY_USER_TIME_SPENT_EXERCISING_BY_UE, {
    skip: !userId,
    fetchPolicy: 'no-cache',
    variables: {
      userId,
    },
  });

  const mesUEs = data?.mesUEs?.filter(ue => !ue?.isFolder);

  const [hasInit, setHasInit] = useState(false);

  let chart = null;

  const allTimes = mesUEs?.map((ue) => ue?.totalTimeSpentExercising?.time);
  const uesWithTimeSpent = allTimes?.filter(time => time && time !== 0)
  const hasNoData = uesWithTimeSpent?.length === 0;

  console.log({uesWithTimeSpent, hasNoData});

  useEffect(() => {
    if (!hasInit && mesUEs && !hasNoData) {
      const formattedData = mesUEs?.map((ue) => (
        {
          ...ue,
          time: Math.round((ue?.totalTimeSpentExercising?.time) / 60), // convert to minutes
        }
      ));
      //console.log({formattedData});

      chart = new Chart({
        container: 'graphTimeSpentByUE',
        autoFit: true,
        height: isMobile ? 200 : 400,
      });
      chart.data(formattedData);
      chart.scale('time', { nice: true });
      chart.scale('name', { nice: true });
      chart.tooltip({
        showMarkers: false,
        position: 'top',
        title: (title, datum) => {
          return datum?.time + ' min';
        },
      });

      chart.legend('name', false);
      chart.legend('time', false);
      chart.interaction('active-region');
      // chart.interaction('element-active')

      chart.axis('time', {
        nice: true,
        label: {
          formatter: (val) => {
            return `${val} min`;
          },
        },
      });
      /*
      chart.axis('name', {
        nice: true,
      });
      */

      chart
        .interval()
        .position('name*time')
        .style({ radius: [20, 20, 0, 0] });
      chart.render(true);
      setHasInit(true);

    }
  }, [data]);


  return (
    <Card size="small" bordered={false} loading={loading} >
      {!loading && hasNoData && (
        <Alert
          type="info"
          message="Aucune donnée disponible"
          showIcon
          size="small"
        />
      )}
      <div id="graphTimeSpentByUE"/>
    </Card>
  );
};

export default UserTimeSpentExercising;