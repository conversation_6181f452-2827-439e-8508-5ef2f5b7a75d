import React, { useEffect, useState } from 'react';
import DataSet from '@antv/data-set';
import { Chart } from '@antv/g2';
import { useTranslation } from 'react-i18next';
import { Radio, Checkbox } from 'antd';
import { tr } from '@/shared/services/translate';
import { renderToString } from 'react-dom/server';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils';
import CategoryPlaceholder from '@/shared/assets/CategoryPlaceholder.png';
import CoursPlaceholder from '@/shared/assets/CoursPlaceholder.png';
import SubjectPlaceholder from '@/shared/assets/SubjectPlaceholder.png';
import { RoundImageForGraphAndLabels } from '@/shared/pages/profile/components/RoundImageForGraphAndLabels';

const NewGraphLine = ({ data, bin, onChangeBin }) => {
  /// Constantes
  const { t } = useTranslation();
  const graphId = 'testGrap322';
  const STRUCTURE = t('ProgressionRadarGraph.AverageGrade');
  const USER = t('ProgressionRadarGraph.YourGrade');

  /// Hook
  const [graph, setGraph] = useState(null);
  const [reworkedData, setReworkedData] = useState(null);
  const [showUserData, setShowUserData] = useState(true);
  const [showStructureData, setShowStructureData] = useState(false);

  function stringToColor(str, shouldTweak = false) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = '#';
    for (let i = 0; i < 3; i++) {
      let value = (hash >> (i * 8)) & 0xff;

      if (shouldTweak) {
        value = value === 255 ? 254 : value + 1;
      }

      color += ('00' + value.toString(16)).substr(-2);
    }

    return color;
  }

  function getUrlFromImageAndNode(node) {
    /* permet de récupérer les images entre les placeHolders et images originales */
    let securedImageLink;
    if (node?.image) {
      securedImageLink = getUrlProtectedRessource(GlobalConfig.get().FILES_URL + node?.image);
    } else if (node?.isFolder) {
      securedImageLink = CategoryPlaceholder;
    } else if (node?.type === 'cours') {
      securedImageLink = CoursPlaceholder;
    } else if (node?.type === 'ue' || node?.type === 'category') {
      securedImageLink = SubjectPlaceholder;
    }

    return securedImageLink;
  }

  const initLines = (data) => {
    const { DataView } = DataSet;
    const dv = new DataView().source(data);

    const chart = new Chart({
      container: graphId,
      autoFit: true
    });

    chart.data(dv.rows);

    chart.scale({
      modifiedDate: {
        type: 'time'
      },
      moyenne: {
        min: 0,
        max: 1
      }
    });

    chart.axis('modifiedDate', {
      title: { text: t('ProgressionRadarGraph.Date') },
      label: {
        formatter: (val) => {
          return val;
        }
      }
    });

    chart.axis('moyenne', {
      title: { text: t('ProgressionRadarGraph.Note') },
      label: {
        formatter: (val) => `${val * 20}`
      }
    });

    chart.tooltip({
      shared: true,
      crosshairs: true,
      customContent: (title, items) => {
        /// Craft du content => Création de la div de toolbox
        let content = `<div style='margin: 10px'>`;

        const obj = {};

        if (items && items.length > 0) {
          items.forEach((item) => {
            const data = item?.data;
            const identifier = data?.uniqueId;

            let securedImageLink = getUrlFromImageAndNode(data);
            const IconBalise = renderToString(
              <RoundImageForGraphAndLabels src={securedImageLink} size={24} />
            );

            const subObj = {};

            subObj.IconBalise = IconBalise;

            if (data?.category === STRUCTURE) {
              subObj.structureMoyenne = (data.moyenne * 20).toFixed(2);
              subObj.structureNumber = data.nbResults;
            }
            if (data?.category === USER) {
              subObj.userMoyenne = (data.moyenne * 20).toFixed(2);
              subObj.userNumber = data.nbResults;
            }
            if (data?.fullName) {
              subObj.fullName = data.fullName;
            }

            // On check si l'objet a la key, si non, on la créé avec l'objet, si déjà présent, alors on spread le nouvel objet avec l'ancien
            if (!obj.hasOwnProperty(identifier)) {
              obj[identifier] = subObj;
            } else {
              obj[identifier] = { ...obj[identifier], ...subObj };
            }
          });

          content += `<div style='text-align: center;font-size: 18px;'>${t('ProgressionRadarGraph.Date')} : ${title}</div>`;
          Object.keys(obj).forEach((key) => {
            content += '<br>';
            const color = stringToColor(key, obj[key]?.category === USER);
            //content += `<div style='display: flex; align-items: center;'>`<div style='flex-grow: 1; font-size: 14px;font-weight:bold'>${obj[key].fullName}</div>&nbsp;${IconBalise}&nbsp;<div style='width: 12px; height: 12px; background-color: ${color};'></div>`</div>`;
            content += `<div style='display: flex; align-items: center;justify-content: center'>
              <div style='flex-grow: 1 ; background-color: yellow'> </div>
              <div style='font-size: 14px; font-weight:bold'>${obj[key].fullName}</div>
              &nbsp;${obj[key]?.IconBalise}&nbsp;
              <div style='display:flex ; flex-grow: 1;justify-content: end'>
                <div style='width: 12px; height: 12px; background-color: ${color};'></div>
              </div>
             
            </div>`;
            content += obj[key]?.userMoyenne
              ? `<div>${t('ProgressionRadarGraph.UserMoyenne')} : ${obj[key].userMoyenne}</div>`
              : '';
            content += obj[key]?.userNumber
              ? `<div>${t('ProgressionRadarGraph.UserNumber')} : ${obj[key].userNumber}</div>`
              : '';
            content += obj[key]?.structureMoyenne
              ? `<div>${t('ProgressionRadarGraph.StructureMoyenne')} : ${obj[key].structureMoyenne}</div>`
              : '';
            content += obj[key]?.structureNumber
              ? `<div>${t('ProgressionRadarGraph.StructureNumber')} : ${obj[key].structureNumber}</div>`
              : '';
          });
        }

        // Fermeture de la div englobante
        content += `</div>`;
        return content;
      }
    });

    chart
      .line()
      .position('modifiedDate*moyenne')
      .color('uniqueId*category', (uniqueId, category) =>
        stringToColor(uniqueId, category === USER)
      )
      .shape('smooth')
      .style('category', (category) => {
        if (category === STRUCTURE) {
          return { lineDash: [6, 6] };
        }
      });

    chart
      .point()
      .position('modifiedDate*moyenne')
      .color('uniqueId*category', (uniqueId, category) =>
        stringToColor(uniqueId, category === USER)
      )
      .shape('category', (category) => {
        if (category === USER) {
          return 'cross';
        }
      });

    chart.render();
    return chart;
  };

  /// formatage des données lorsque on les changes
  useEffect(() => {
    if (data) {
      setReworkedData(extractData(data));
    } else setReworkedData(null);
  }, [data, showUserData, showStructureData]);

  useEffect(() => {
    if (reworkedData && graph) {
      graph?.destroy();
    }
    if (reworkedData) {
      setGraph(initLines(reworkedData));
    }
  }, [reworkedData]);

  function extractData(data) {
    /* fonction d'extraction de données */

    function transformProgressNode(
      progressNode,
      category,
      name,
      id,
      type,
      description,
      image,
      isFolder
    ) {
      /* transforme les progressNode temporelle en objet facilement traitable par le graph */

      const dataPoint = {
        type: type,
        id: id,
        description: description,
        uniqueId: name + description, //type+id,
        category,
        moyenne: progressNode.moyenne,
        modifiedDate: progressNode.modifiedDate,
        nbResults: progressNode.nbResults,
        fullName: name + description,
        image: image,
        isFolder: isFolder
      };
      flattenedData.push(dataPoint);
    }

    function transformStructureAndUserNode(node) {
      /* transforme les node (structure, user) en objet facilement traitable par le graph */
      const id = node?.id;
      const name = node?.[tr('name')] || node?.name || '';
      const type = node?.type;
      const description = node?.[tr('description')] || node?.description || '';
      const image = node?.image;
      const isFolder = node?.isFolder;

      if (showStructureData && node?.structureProgress) {
        node.structureProgress.forEach((progressNode) => {
          transformProgressNode(
            progressNode,
            STRUCTURE,
            name,
            id,
            type,
            description,
            image,
            isFolder
          );
        });
      }
      if (showUserData && node?.userProgress) {
        node.userProgress.forEach((progressNode) => {
          transformProgressNode(progressNode, USER, name, id, type, description, image, isFolder);
        });
      }
    }

    // placeholder des données
    const flattenedData = [];

    // Traitement des deux nodes : structureProgress et userProgress
    data.forEach((node) => {
      transformStructureAndUserNode(node);
    });

    return flattenedData;
  }

  const BinSelector = () => {
    const handleChange = (e) => {
      const value = e.target.value;
      onChangeBin(value);
    };

    return (
      <Radio.Group onChange={handleChange} defaultValue={bin}>
        <Radio.Button value="annee">{t('ProgressionRadarGraph.SelectBinYear')}</Radio.Button>
        <Radio.Button value="mois">{t('ProgressionRadarGraph.SelectBinMonth')}</Radio.Button>
        <Radio.Button value="semaine">{t('ProgressionRadarGraph.SelectBinWeek')}</Radio.Button>
        <Radio.Button value="jour">{t('ProgressionRadarGraph.SelectBinDay')}</Radio.Button>
      </Radio.Group>
    );
  };

  return (
    <div
      style={{
        width: '100%',
        height: '500px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        //flexWrap:'wrap'
        flexDirection: 'column',
        marginTip: '20px'
      }}
    >
      <div style={{ width: '100%', height: '500px', marginBottom: '5px' }} id={graphId} />
      <BinSelector />
      <div style={{ display: 'flex', gap: '50px', marginTop: '5px' }}>
        <Checkbox
          checked={showUserData}
          onChange={(e) => {
            setShowUserData(e.target.checked);
          }}
        >
          {' '}
          {t('ProgressionRadarGraph.ShowMyResults')}{' '}
        </Checkbox>
        <Checkbox
          checked={showStructureData}
          onChange={(e) => {
            setShowStructureData(e.target.checked);
          }}
        >
          {' '}
          {t('ProgressionRadarGraph.ShowStructureResults')}{' '}
        </Checkbox>
      </div>
    </div>
  );
};

export default React.memo(NewGraphLine);
