import React from 'react';
import { motion } from 'framer-motion/dist/framer-motion';

export function BiographyIndicator() {
  const dotStyle = {
    width: 20,
    height: 20,
    opacity: 1,
    margin: -3,
    borderRadius: '100%',
    display: 'inline-block',
    background: '#898989'
  };

  return (
    <div>
      <motion.div
        style={{ background: '#FFFFFF', padding: '18px 24px', borderRadius: '100%' }}
        animate={{
          scale: [1, 1.2, 1, 1, 1]
        }}
        transition={{
          duration: 5,
          ease: 'anticipate',
          times: [0, 0.3, 0.5, 0.9, 1],
          repeat: Infinity,
          delay: 0
        }}
      />
      <div style={{ marginTop: -25, marginLeft: 3 }}>
        <motion.div
          style={dotStyle}
          animate={{
            scale: [0.2, 0.6, 0.2]
          }}
          transition={{
            duration: 2.5,
            ease: 'anticipate',
            times: [0, 0.25, 0.5],
            repeat: Infinity,
            delay: 0
          }}
        />
        <motion.div
          style={dotStyle}
          animate={{
            scale: [0.2, 0.6, 0.2]
          }}
          transition={{
            duration: 2.5,
            ease: 'anticipate',
            times: [0, 0.25, 0.5],
            repeat: Infinity,
            delay: 0.2
          }}
        />
        <motion.div
          style={dotStyle}
          animate={{
            scale: [0.2, 0.6, 0.2]
          }}
          transition={{
            duration: 2.5,
            ease: 'anticipate',
            times: [0, 0.25, 0.5],
            repeat: Infinity,
            delay: 0.4
          }}
        />
      </div>
    </div>
  );
}
