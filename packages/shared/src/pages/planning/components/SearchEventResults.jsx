import React, { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Eventcalendar } from '@mobiscroll/react';
import EventAgendaItem from '@/shared/pages/planning/components/EventAgendaItem';
import { getMobiscrollPlanningFromData } from '@/shared/pages/planning/getMobiscrollPlanningFromData';
import { TimeZoneManager } from '@/shared/utils/utils';
import useLocalStorage from '@/shared/hooks/useLocalStorage';
import { Divider, Empty, Spin, Typography } from 'antd';
import dayjs from 'dayjs';

export function SearchEventResults({ results, loading, useMobiscroll = false, navigateToDate }) {
  const { t } = useTranslation();

  const [resultsList, setResultsList] = React.useState([]);

  const listView = useMemo(() => ({ agenda: { type: 'year', size: 1 } }), []);

  const [currentTimeZone, setCurrentTimezone] = useLocalStorage(
    TimeZoneManager.getLocalStorageTimeZoneKey(),
    TimeZoneManager.getDefaultTimeZone()
  );

  const renderEvent = useCallback((e) => {
    return <EventAgendaItem key={e?.id} currentEvent={e} />;
  }, []);
  const renderEventProp = useMemo(
    () => ({
      renderEvent
    }),
    [renderEvent]
  );

  // Utilisation de useMemo pour éviter de recalculer si results n'a pas changé
  useEffect(() => {
    // Débounce ou throttle possible ici pour les appels fréquents
    const processPlanningData = () => {
      const { dataPlanning, datesToAdd } = getMobiscrollPlanningFromData(
        t,
        listView,
        results,
        [],
        currentTimeZone
      );
      setResultsList([...dataPlanning, ...datesToAdd]);
    };

    processPlanningData();
  }, [results, t, listView, currentTimeZone]);

  return (
    <div style={{ overflow: 'auto', height: '100%' }}>
      {loading ? (
        <Spin tip={t('general.Loading...')} size="large">
          <div
            style={{
              marginTop: 100,
              padding: 50,
              borderRadius: 4
            }}
          />
        </Spin>
      ) : (
        <>
          {useMobiscroll && resultsList?.length > 0 ? (
            <Eventcalendar
              {...renderEventProp}
              data={resultsList}
              showControls={false}
              view={listView}
              onEventClick={(e) => {
                console.log(e.event); // @TODO handle click
              }}
            />
          ) : (
            <>
              {results?.length > 0 ? (
                results?.map((e, i) => (
                  <div key={e?.id} style={{ margin: 5 }}>
                    {(i === 0 || e.date !== results[i - 1].date) && (
                      <>
                        <Typography.Title level={5} style={{ margin: 0 }}>
                          {dayjs(e.date).format('ddd DD MMMM YYYY')}
                        </Typography.Title>
                        <Divider style={{ marginTop: 5, marginBottom: 12 }} />
                      </>
                    )}
                    <EventAgendaItem
                      key={e?.id}
                      currentEvent={e}
                      useMobiscroll={false}
                      manualHover
                      navigateToDate={navigateToDate}
                      navigateOnClick
                    />
                    {i < results.length - 1 && (
                      <Divider style={{ marginTop: 12, marginBottom: 0 }} />
                    )}
                  </div>
                ))
              ) : (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100%'
                  }}
                >
                  <Empty description={t('NoResult')} />
                </div>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
}
