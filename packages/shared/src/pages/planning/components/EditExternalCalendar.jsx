import { CreateEditCalendarModal } from '@/shared/pages/admin/calendars/components/CreateEditCalendarModal';
import { ModalType } from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal';
import { SettingOutlined } from '@ant-design/icons';
import React from 'react';

export default function EditExternalCalendar({ cal, refetch }) {
  const [editVisible, setEditVisible] = React.useState(false);
  return (
    <div>
      <SettingOutlined onClick={() => setEditVisible(!editVisible)} />

      <CreateEditCalendarModal
        calendar={cal}
        handleCloseModal={() => setEditVisible(false)}
        isVisible={editVisible}
        modalType={ModalType.UPDATE}
        refetch={refetch}
      />
    </div>
  );
}
