import { MUTATION_MODIFY_SINGLE_OCCURENCE_DATE } from '@/shared/graphql/edt';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { useMutation } from '@apollo/client';
import React, { useState } from 'react';
import { Alert, Button as AntButton, Form, Modal, Space, TimePicker, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';

export default function OccurrenceEditor({
  currentEvent,
  editOccurenceModalVisible,
  setEditOccurenceModalVisible,
  refetch
}) {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const [modifySingleOccurenceData, { loading: loadingModify }] = useMutation(
    MUTATION_MODIFY_SINGLE_OCCURENCE_DATE
  );

  const handleSave = async () => {
    try {
      const startTime = form.getFieldValue('startDate'); // Contient uniquement l'heure
      const endTime = form.getFieldValue('endDate'); // Contient uniquement l'heure
      const eventDate = dayjs(currentEvent.start).startOf('day'); // Récupère uniquement la date

      // Fusion de la date de currentEvent.start avec l'heure choisie
      const fullStartDate = eventDate.hour(startTime.hour()).minute(startTime.minute()).toDate();
      const fullEndDate = eventDate.hour(endTime.hour()).minute(endTime.minute()).toDate();

      await modifySingleOccurenceData({
        variables: {
          dateDiffusionId: currentEvent.id,
          date: fullStartDate,
          dateEnd: fullEndDate
        }
      });

      refetch();
      setEditOccurenceModalVisible(false);
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const [title, setTitle] = useState(currentEvent.tooltip);

  return (
    <Modal
      title={t('EditThisOccurence')}
      open={editOccurenceModalVisible}
      onCancel={() => setEditOccurenceModalVisible(false)}
      footer={null}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          title: currentEvent.title,
          startDate: dayjs(currentEvent.start),
          endDate: dayjs(currentEvent.end)
        }}
      >
        <Form.Item name="title" valuePropName={title} rules={[{ required: true }]}>
          <Typography.Paragraph editable={{ onChange: setTitle }}>{title}</Typography.Paragraph>
        </Form.Item>

        <Form.Item label={t('StartTime')} name="startDate" rules={[{ required: true }]}>
          <TimePicker style={{ width: '100%' }} format="HH:mm" />
        </Form.Item>

        <Form.Item label={t('EndTime')} name="endDate" rules={[{ required: true }]}>
          <TimePicker style={{ width: '100%' }} format="HH:mm" />
        </Form.Item>

        <Alert style={{ marginBottom: 10 }} message={t('OccurrenceEditorInfo')} showIcon />

        <Form.Item>
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <AntButton type="primary" onClick={handleSave} loading={loadingModify}>
              {t('general.save')}
            </AntButton>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
}
