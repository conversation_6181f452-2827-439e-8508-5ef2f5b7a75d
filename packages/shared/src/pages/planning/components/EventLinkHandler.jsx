import React from 'react'; // Assurez-vous que React est importé
import { Avatar, Button, Input, Typography } from 'antd'; // Supposons que vous utilisez Ant Design
import { ExternalLink, PenLine } from 'lucide-react'; // Supposons que vous utilisez Lucide pour les icônes
import MeetIcon from '@/shared/assets/meet.svg';
import TeamsIcon from '@/shared/assets/teams.svg';
import ZoomIcon from '@/shared/assets/zoom.svg';
import { useTranslation } from 'react-i18next';

export const EventLinkHandler = ({
  canEdit,
  editingPlatformLink,
  setEditingPlatformLink,
  platformLinkvalue, // Valeur utilisée pour l'input defaultValue
  setPlatformLinkValue,
  setLink,
  handleEditField,
  currentEvent, // Contient le lien 'final' ou 'sauvegardé'
  linkButton // Fonction pour rendre un bouton externe/action
}) => {
  const { t } = useTranslation();

  const isPlatformLink = (url) => {
    const videoCallPlatforms = ['meet.google.com', 'teams.microsoft.com', 'zoom.us'];
    return !!videoCallPlatforms.some((platform) => url?.includes(platform));
  };

  const getShortenedPlatformLink = (url) => {
    const videoCallPlatforms = ['meet.google.com', 'teams.microsoft.com', 'zoom.us'];
    return videoCallPlatforms.find((platform) => url?.includes(platform)) || '';
  };

  /**
   * Configuration des plateformes supportées.
   * Centralise les informations spécifiques à chaque plateforme (couleur, icône, clé de traduction).
   */
  const platformConfig = {
    MEET: {
      color: '#E94235',
      icon: MeetIcon,
      nameKey: 'EventGoogleMeet',
      check: (url) => url?.includes('meet.google.com')
    },
    TEAMS: {
      color: '#6264A7',
      icon: TeamsIcon,
      nameKey: 'EventTeams',
      check: (url) => url?.includes('teams.microsoft.com')
    },
    ZOOM: {
      color: '#0B5CFF', // Couleur par défaut si aucune autre ne correspond (originellement Zoom)
      icon: ZoomIcon,
      nameKey: 'EventZoom',
      check: (url) => url?.includes('zoom.us') // Assurez-vous que c'est le bon domaine pour Zoom
    }
  };

  /**
   * Détermine les détails de la plateforme en fonction de l'URL.
   * @param {string | undefined} url - L'URL à vérifier.
   * @returns {{ type: PlatformType, config: platformConfig[PlatformType] } | null} - Le type et la configuration de la plateforme, ou null si non reconnue.
   */
  const getPlatformDetails = (url) => {
    if (!url) return null;
    for (const [type, config] of Object.entries(platformConfig)) {
      if (config.check(url)) {
        return { type: /** @type {PlatformType} */ (type), config };
      }
    }

    if (isPlatformLink(url)) {
      return { type: 'ZOOM', config: platformConfig.ZOOM };
    }

    return null;
  };

  const commonPlatformLinkStyle = {
    marginTop: 8,
    padding: '6px 12px',
    borderRadius: 8,
    color: 'white',
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    width: 'fit-content',
    cursor: 'pointer'
  };

  const readOnlyContainerStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: 8
  };

  const genericLinkContainerStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%'
  };

  /**
   * Composant React pour afficher et potentiellement éditer un lien d'événement.
   * Gère spécifiquement les liens de plateformes (Meet, Teams, Zoom) et les liens génériques.
   *
   * @param {object} props - Les props du composant.
   * @param {string | undefined} props.url - L'URL actuelle à afficher/éditer.
   * @param {boolean} props.canEdit - L'utilisateur a-t-il la permission d'éditer ?
   * @param {boolean} props.editingPlatformLink - L'état d'édition pour les liens de plateforme est-il actif ?
   * @param {function} props.setEditingPlatformLink - Fonction pour changer l'état d'édition des liens de plateforme.
   * @param {string} props.platformLinkvalue - La valeur actuelle (potentiellement en cours d'édition) du lien de plateforme.
   * @param {function} props.setPlatformLinkValue - Fonction pour mettre à jour la valeur du lien de plateforme (pour l'input).
   * @param {function} props.setLink - Fonction pour mettre à jour l'état global du lien.
   * @param {function} props.handleEditField - Fonction pour sauvegarder le champ 'link' (par exemple, via API).
   * @param {object} props.currentEvent - L'objet événement contenant le lien (utilisé pour l'affichage initial et l'ouverture).
   * @param {function} props.linkButton - Composant ou fonction pour rendre un bouton d'action sur le lien (utilisé en mode lecture seule pour plateforme).
   * @param {function} props.t - Fonction de traduction (i18n).
   * @returns {JSX.Element | null} Le rendu JSX du gestionnaire de lien.
   */
  const platformDetails = getPlatformDetails(currentEvent.link); // Détermine la plateforme une seule fois

  /**
   * Gère la mise à jour du lien depuis l'input (onPressEnter ou onBlur).
   * @param {string} newValue - La nouvelle valeur de l'URL.
   */
  const handlePlatformLinkUpdate = (newValue) => {
    // Normalisation ou validation simple (optionnel)
    const trimmedValue = newValue.trim();
    if (trimmedValue === platformLinkvalue) {
      // Évite les mises à jour inutiles si la valeur n'a pas changé
      setEditingPlatformLink(false);
      return;
    }
    setLink(trimmedValue);
    setPlatformLinkValue(trimmedValue); // Met à jour la valeur affichée dans l'input pour la prochaine fois
    handleEditField('link', trimmedValue); // Sauvegarde la modification
    setEditingPlatformLink(false); // Quitte le mode édition
  };

  // --- Rendu pour les liens de plateforme ---
  if (platformDetails) {
    const { config } = platformDetails;

    // CAS 1: L'utilisateur PEUT éditer
    if (canEdit) {
      // CAS 1a: En cours d'édition
      if (editingPlatformLink) {
        return (
          <Input
            style={{ marginTop: 8 }}
            defaultValue={platformLinkvalue} // Utilise la valeur d'état pour l'input
            onPressEnter={(e) => handlePlatformLinkUpdate(e.target.value)}
            onBlur={(e) => handlePlatformLinkUpdate(e.target.value)}
            autoFocus
            aria-label={t('EditLinkLabel') || 'Modifier le lien de la plateforme'} // Pour l'accessibilité
          />
        );
      }
      // CAS 1b: Affichage éditable (cliquer pour éditer)
      else {
        return (
          <>
            <div>
              <span
                style={{
                  ...commonPlatformLinkStyle,
                  backgroundColor: config.color // Utilise la couleur de la config
                }}
                onClick={() => setEditingPlatformLink(true)}
                role="button" // Pour l'accessibilité
                tabIndex={0} // Pour la navigation clavier
                onKeyPress={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') setEditingPlatformLink(true);
                }} // Accessibilité clavier
                aria-label={t('EditPlatformLinkAria') || `Modifier le lien ${config.nameKey}`}
              >
                {/* Utilise currentEvent.link pour afficher la valeur sauvegardée, ou platformLinkvalue si on veut afficher la valeur potentiellement non sauvegardée */}
                {getShortenedPlatformLink(currentEvent?.link)}
                <span style={{ fontSize: 16, display: 'inline-flex' }}>
                  {' '}
                  {/* Ajustement taille/affichage icône */}
                  <PenLine size={16} /> {/* Préciser la taille peut être une bonne pratique */}
                </span>
              </span>
            </div>
            {/* Affiche le bouton d'action (ex: rejoindre la réunion) si la fonction linkButton est fournie */}
            {linkButton && currentEvent?.link && linkButton(currentEvent.link)}
          </>
        );
      }
    } else {
      return (
        <>
          <div style={readOnlyContainerStyle}>
            <Avatar
              style={{ backgroundColor: 'white' }}
              shape="square"
              size="large"
              src={config.icon}
              alt={`${config.nameKey} icon`}
            />
            {/* Utilise currentEvent.link pour le texte affiché ici? Ou une traduction? */}
            {/* Le code original utilisait t() basé sur l'URL, ce qui est bien */}
            <Typography.Paragraph style={{ margin: 0 }}>{t(config.nameKey)}</Typography.Paragraph>
          </div>
          {/* Affiche le bouton d'action (ex: rejoindre la réunion) si la fonction linkButton est fournie */}
          {linkButton && currentEvent?.link && linkButton(currentEvent.link)}
        </>
      );
    }
  }
  // --- Rendu pour les liens génériques (non-plateforme) ---
  else {
    // Pour les liens génériques, on utilise directement Typography.Link éditable si canEdit est vrai.
    // Note: Le code original ne conditionnait pas l'éditabilité de Typography.Link par `canEdit`.
    // Je l'ajoute ici car c'est logique. Si ce n'est pas le comportement souhaité, retirez la condition `canEdit &&`.
    const isEditable = canEdit && {
      onChange: (value) => {
        const trimmedValue = value.trim();
        setLink(trimmedValue);
        handleEditField('link', trimmedValue);
      },
      triggerType: ['text', 'icon'],
      tooltip: t('ClickToEditLink') || 'Cliquez pour modifier le lien' // Info-bulle pour l'édition
    };

    return (
      <div style={genericLinkContainerStyle}>
        <Typography.Link
          style={{
            margin: 0,
            flexGrow: 1,
            marginRight: 8
          }} // Permet au lien de prendre l'espace et évite qu'il touche le bouton
          // Applique une apparence 'secondaire' si le lien est vide
          type={!currentEvent?.link?.length ? 'secondary' : undefined}
          // Rend le composant éditable seulement si canEdit est vrai
          editable={isEditable || false} // Passe `false` explicitement si non éditable
          ellipsis={{ rows: 1, expandable: false }} // Garde l'ellipsis
          // Utilise currentEvent.link pour afficher le lien sauvegardé
          // Si le lien est vide et non éditable, affiche un placeholder ou rien
          href={currentEvent?.link || undefined} // Rend le lien cliquable s'il existe
          target="_blank" // Ouvre dans un nouvel onglet
          rel="noopener noreferrer" // Sécurité pour target="_blank"
        >
          {currentEvent?.link || (canEdit ? t('AddEventLink') : t('NoLinkAvailable'))}
        </Typography.Link>
        {/* Affiche le bouton pour ouvrir le lien dans un nouvel onglet seulement si le lien existe */}
        {currentEvent?.link && (
          <Button
            type="text" // type="link" peut avoir des paddings/styles non désirés ici, "text" est plus neutre
            icon={<ExternalLink size={16} />} // Préciser la taille
            onClick={(e) => {
              e.stopPropagation(); // Empêche le déclenchement d'autres événements (ex: édition si sur le même conteneur)
              window.open(currentEvent.link, '_blank', 'noopener,noreferrer');
            }}
            aria-label={t('OpenLinkInNewTab') || 'Ouvrir le lien dans un nouvel onglet'}
          />
        )}
      </div>
    );
  }
};
