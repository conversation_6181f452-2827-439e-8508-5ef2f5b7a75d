import { useTranslation } from 'react-i18next';
import React from 'react';
import { Tag, Tree, TreeSelect } from 'antd';
import { useQuery } from '@apollo/client';
import { QUERY_ALL_BUILDINGS } from '@/shared/graphql/events';
import { isMobile } from '@/shared/utils/utils.js';
export const mapBuildingsForTreeSelection = (allBuildings) => {
  return allBuildings?.map((building) => ({
    title: building.name,
    value: `building-${building?.id}`,
    key: `building-${building?.id}`,
    children: building?.rooms?.map((r) => ({
      title: r?.name,
      value: r.id,
      key: r?.id
    }))
  }));
};

export default function BuildingsRoomsTreeSelect({ onChange, useTreeSelect = true }) {
  const { t } = useTranslation();
  const { data, loading, error, refetch } = useQuery(QUERY_ALL_BUILDINGS, {
    fetchPolicy: 'cache-and-network'
  });
  const buildings = data?.allBuildings || [];
  const treeData = mapBuildingsForTreeSelection(buildings);

  const tagRender = ({ label, value, closable, onClose, key }) => (
    <Tag
      value={value}
      key={key}
      color="geekblue"
      closable={closable}
      onClose={onClose}
      style={{ marginRight: 3 }}
    >
      {label}
    </Tag>
  );

  if (useTreeSelect) {
    return (
      <TreeSelect
        treeCheckable
        placeholder={t('ChooseRooms')}
        treeNodeFilterProp="title"
        treeData={treeData}
        style={{ width: isMobile ? 'auto' : '100%', minWidth: '300px' }}
        onChange={async (newValue, label, extra) => {
          const selectedRoomIds = newValue.filter((value) => !value.startsWith('building-'));
          onChange(selectedRoomIds);
        }}
        tagRender={buildings && tagRender}
      />
    );
  }

  return (
    <Tree
      checkable
      selectable={false}
      treeData={treeData}
      onCheck={(checkedKeys, { checkedNodes }) => {
        const selectedRoomIds = checkedNodes
          .filter((node) => !node.key.startsWith('building-'))
          .map((node) => node.key);
        onChange(selectedRoomIds);
      }}
      style={{ width: '100%', minWidth: '300px', maxHeight: '400px', overflow: 'auto' }}
    />
  );
}
