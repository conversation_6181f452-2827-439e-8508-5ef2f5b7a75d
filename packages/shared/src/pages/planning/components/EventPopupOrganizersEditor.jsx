import { Button, Space, Tooltip } from 'antd';
import { ExoUserLight } from '@/shared/components/User/ExoUserLight';
import { MinusCircleOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import SearchUser from '@/shared/components/User/SearchUser';
import { CirclePlus } from 'lucide-react';
import { useMutation } from '@apollo/client';
import {
  MUTATION_ADD_ORGANIZER_DATE_DIFFUSION,
  MUTATION_REMOVE_ORGANIZER_DATE_DIFFUSION
} from '@/shared/graphql/edt';
import { useTranslation } from 'react-i18next';

export const EventPopupOrganizersEditor = ({ event, refetch }) => {
  const { t } = useTranslation();
  const [showOrganizerSearch, setShowOrganizerSearch] = useState(false);

  const [addOrganizer] = useMutation(MUTATION_ADD_ORGANIZER_DATE_DIFFUSION);
  const [removeOrganizer] = useMutation(MUTATION_REMOVE_ORGANIZER_DATE_DIFFUSION);

  const onAddOrganizer = async (userId) => {
    await addOrganizer({ variables: { dateDiffusionId: event.id, userId } });
    await refetch();
  };
  const onRemoveOrganizer = async (userId) => {
    await removeOrganizer({ variables: { dateDiffusionId: event.id, userId } });
    await refetch();
  };

  return (
    <div>
      <Space>
        <Button
          style={{ height: 'auto' }}
          onClick={() => setShowOrganizerSearch((v) => !v)}
          icon={showOrganizerSearch ? null : <CirclePlus />}
        >
          {showOrganizerSearch ? 'Annuler' : 'Ajouter un organisateur'}
        </Button>
      </Space>

      {showOrganizerSearch && (
        <div style={{ marginBottom: '20px', textAlign: 'center' }}>
          <SearchUser
            onSelectUser={async (value, option) => {
              await onAddOrganizer(option?.key);
            }}
          />
        </div>
      )}

      {event?.organizersIds?.map((userId) => (
        <div key={userId} style={{ margin: 20, textAlign: 'left', fontWeight: '700' }}>
          <ExoUserLight id={userId} />
          <Tooltip title={t('general.Remove')}>
            <MinusCircleOutlined
              className="dynamic-delete-button"
              onClick={() => onRemoveOrganizer(userId)}
            />
          </Tooltip>
        </div>
      ))}
    </div>
  );
};
