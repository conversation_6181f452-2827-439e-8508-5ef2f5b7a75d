import React from 'react';
import { Avatar } from 'antd';
import { Check } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';

export const CalendarCheckbox = ({ onChange, checked = false, children, size, color }) => {
  return (
    <div
      onClick={onChange}
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        cursor: 'pointer',
        userSelect: 'none'
      }}
    >
      <Avatar
        size={size}
        shape="square"
        style={{
          border: checked ? `2px solid ${color}` : '2px solid transparent',
          borderRadius: '5px',
          transition: 'all 0.2s ease-in-out',
          backgroundColor: checked && color,
          verticalAlign: 'middle'
        }}
        icon={
          <AnimatePresence>
            {checked && (
              <motion.div
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.5 }}
                transition={{ duration: 0.2 }}
                style={{ position: 'absolute', marginBottom: -6, fontSize: size - 4 }}
              >
                <Check />
              </motion.div>
            )}
          </AnimatePresence>
        }
      />

      {children && <span style={{ marginLeft: size / 2 }}>{children}</span>}
    </div>
  );
};
