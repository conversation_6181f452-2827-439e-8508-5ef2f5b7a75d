import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';
import { QUERY_GROUPS_FOR_PLANNING } from '@/shared/graphql/cours.js';
import {
  MUTATION_ADD_GROUPE_DATE_DIFFUSION,
  MUTATION_ADD_ORGANIZER_DATE_DIFFUSION,
  MUTATION_CREATE_DATE_DIFFUSION,
  MUTATION_EDIT_DATE_DIFFUSION,
  MUTATION_MODIFY_SINGLE_OCCURRENCE_DATE_WITH_DUPLICATION,
  QUERY_GET_CALENDARS_TO_SHOW_ON_THE_SIDE,
  QUERY_MON_PLANNING_BETWEEN,
  QUERY_REVISIONS_BETWEEN,
  QUERY_SEARCH_DATES_DIFF
} from '@/shared/graphql/edt.js';

import useLocalStorage from '@/shared/hooks/useLocalStorage.js';
import EventAgendaItem from '@/shared/pages/planning/components/EventAgendaItem.jsx';
import EventCreation from '@/shared/pages/planning/components/EventCreation';
import { LeftFilters } from '@/shared/pages/planning/components/LeftFilters';
import OccurrenceEditor from '@/shared/pages/planning/components/OccurrenceEditor';
import { getMobiscrollPlanningFromData } from '@/shared/pages/planning/getMobiscrollPlanningFromData.js';
import { getMobiscrollRevisionPlanning } from '@/shared/pages/planning/getMobiscrollRevisionPlanning';
import { isAdmin, isTuteur } from '@/shared/utils/authority.js';
import {
  getUrlProtectedRessource,
  GlobalConfig,
  isMobile,
  showGqlErrorsInMessagePopupFromException,
  TimeZoneManager
} from '@/shared/utils/utils.js';
import { useLazyQuery, useMutation, useQuery } from '@apollo/client';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import {
  Button as AntdButton,
  Divider,
  Drawer,
  Form,
  Modal,
  Radio,
  Select,
  Tag,
  Typography
} from 'antd';
import dayjs from 'dayjs';
import moment from 'moment-timezone';

import {
  Button,
  CalendarNav,
  CalendarToday,
  Eventcalendar,
  localeDe,
  localeEn,
  localeEs,
  localeFr,
  localeIt,
  momentTimezone,
  Popup
} from '@mobiscroll/react';
import '@mobiscroll/react/dist/css/mobiscroll.min.css';
import { useTranslation } from 'react-i18next';
import { ExoUserLight } from '@/shared/components/User/ExoUserLight';
import EventPopupContent from '@/shared/pages/planning/components/EventPopupContent';
import {
  CalendarDays,
  CalendarSearch,
  ChartNoAxesGantt,
  Clock10,
  Globe,
  List,
  Search,
  User,
  X
} from 'lucide-react';
import { router } from 'umi';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import useMediaQuery from '@/shared/hooks/useMediaQuery';
import SearchAll from '@/shared/pages/planning/components/SearchAll';
import { MUTATION_CREATE_EVENT, MUTATION_UPDATE_EVENT } from '@/shared/graphql/events';
import { SearchEventResults } from '@/shared/pages/planning/components/SearchEventResults';
import { motion } from 'framer-motion/dist/framer-motion';
import debounce from 'lodash.debounce';

momentTimezone.moment = moment;

const calculateBeginning = (date) => dayjs(date).subtract('42', 'days');
const calculateEnd = (date) => dayjs(date).add(1, 'month').add('42', 'days');

export const getMobiScrollLanguage = (lng) => {
  switch (lng) {
    case 'fr':
      return localeFr;
    case 'en':
      return localeEn;
    case 'es':
      return localeEs;
    case 'de':
      return localeDe;
    case 'it':
      return localeIt;
    default:
      return localeFr;
  }
};

// https://demo.mobiscroll.com/react/scheduler/custom-resource-header-template#

export const getPlanningLink = (item) => {
  if (item?.cour) {
    return `/cours/${item.cour.id}`;
  }
  if (item?.cours?.id) {
    return `/cours/${item.cours.id}`;
  }
  if (item?.qcm) {
    const { qcm } = item;
    if (qcm?.annale) {
      return `/annales/${qcm.id_qcm}`;
    }
    return `/qcm/${qcm?.id_qcm}`;
  }
  if (item?.exam_session) {
    return `/exam/${item?.exam_session?.examId}`;
  }
  if (item?.examQuestionSerie) {
    return `/exam/${item?.examQuestionSerie?.examId}`;
  }
  if (item?.event) {
    return `/event/${item?.event?.id}`;
  }
  return '';
};

export default function PlanningPage(props) {
  const [CreateDateDiffusion] = useMutation(MUTATION_CREATE_DATE_DIFFUSION);
  const [createEventMutation] = useMutation(MUTATION_CREATE_EVENT);
  const [updateEventMutation] = useMutation(MUTATION_UPDATE_EVENT);
  const [addOrganizerToDateDiffusion] = useMutation(MUTATION_ADD_ORGANIZER_DATE_DIFFUSION);
  const [addGroupToDateDiffusion] = useMutation(MUTATION_ADD_GROUPE_DATE_DIFFUSION);
  const [EditDateDiffusion, { loading: loadingEditDateDiff }] = useMutation(
    MUTATION_EDIT_DATE_DIFFUSION
  );
  const [modifySingleOccurenceWithDuplication, { loading: loadingSingleOccurence }] = useMutation(
    MUTATION_MODIFY_SINGLE_OCCURRENCE_DATE_WITH_DUPLICATION
  );

  const [openViewSelect, setOpenViewSelect] = useState(false);
  const [openTimezoneSelect, setOpenTimezoneSelect] = useState(false);
  const [openRecurringEventModal, setOpenRecurringEventModal] = useState(false);
  const [displaySearchSider, setDisplaySearchSider] = useState(false);
  const [recurringEventArgs, setRecurringEventArgs] = useState(null);

  const isSmallScreen = useMediaQuery('(max-width: 600px)');
  const canEdit = (isAdmin() || isTuteur()) && !currentEvent?.ical_data;

  const handleEventCreation = async (args) => {
    if (args.action === 'drag') {
      return;
    }
    const argsCpy = { ...args };
    const eventId = await createEvent(t('Calendar.NewEvent'));
    argsCpy.event.customTitle = t('EventCustomTitle');
    argsCpy.event.date = argsCpy.event.start;
    argsCpy.event.dateEnd = argsCpy.event.end;
    argsCpy.event.organizersIds = [`${me.id}`];
    argsCpy.event.event = { id: eventId, name: t('Calendar.NewEvent') };
    const dateDiffId = await createDateDiff({
      eventId,
      allDay: argsCpy.event.allDay,
      date: argsCpy.event.start,
      dateEnd: argsCpy.event.end
    });
    argsCpy.event.id = dateDiffId;

    if (groupsView) {
      argsCpy.event.groupes = [
        {
          id: argsCpy.resourceObj.id,
          name: argsCpy.resourceObj.name
        }
      ];
      await addGroupToDateDiffusion({
        variables: {
          dateDiffusionId: dateDiffId,
          groupId: argsCpy.resourceObj.id
        }
      });
    }

    await organizeDateDiff({ dateDiffusionId: dateDiffId, userId: me.id });

    await refetch();

    // argsCpy.event = mobiscrollDataPlanning.find((e) => e.id === dateDiffId);

    handleEventClick(argsCpy);
  };

  const handleEventDragAndDrop = async (args) => {
    await EditDateDiffusion({
      variables: {
        dateDiffusionId: args.event.id,
        dateDiffusion: {
          date: dayjs(args.event.start).toDate(),
          dateEnd: dayjs(args.event.end).toDate()
        }
      }
    });
    await refetch();
  };

  const handleModifySingleOccurenceWithDuplication = async (args) => {
    const { data } = await modifySingleOccurenceWithDuplication({
      variables: {
        dateDiffusionId: args.event.id,
        exceptionDate: dayjs(args.event.start).toDate()
      }
    });
    const duplicatedDateDiff = data?.modifySingleOccurrenceDateWithDuplication;
    await EditDateDiffusion({
      variables: {
        dateDiffusionId: duplicatedDateDiff.id,
        dateDiffusion: {
          date: dayjs(args?.newEvent?.start).toDate(),
          dateEnd: dayjs(args?.newEvent?.end).toDate()
        }
      }
    });
    await refetch();
    setOpenRecurringEventModal(false);
  };

  const moveAllOccurences = async (args) => {
    await EditDateDiffusion({
      variables: {
        dateDiffusionId: args.oldEvent.id,
        dateDiffusion: {
          date: dayjs(args?.newEvent?.start).toDate(),
          dateEnd: dayjs(args?.newEvent?.end).toDate()
        }
      }
    });
    await refetch();
    setOpenRecurringEventModal(false);
  };

  const createEvent = async (name) => {
    const result = await createEventMutation({
      variables: {
        input: { name }
      }
    });
    return result?.data?.createEvent.id;
  };

  const updateEventName = async (name) => {
    const result = await updateEventMutation({
      variables: {
        input: { name }
      }
    });
    return result?.data?.createEvent.id;
  };

  const createDateDiff = async (dateDiff) => {
    try {
      const result = await CreateDateDiffusion({
        variables: {
          dateDiffusion: dateDiff
        }
      });
      const createdDateDiff = result?.data?.createDateDiffusion;
      if (createdDateDiff) {
        return createdDateDiff.id;
      }
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const organizeDateDiff = async ({ dateDiffusionId, userId }) => {
    try {
      const result = await addOrganizerToDateDiffusion({
        variables: {
          dateDiffusionId,
          userId
        }
      });
      await refetch();
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const displayMobileMode = useMemo(() => {
    return isSmallScreen || isMobile;
  }, [isSmallScreen]);

  // useEffectScrollTop();

  /* CONSTANTES (quasi) */
  const CALENDAR_VIEW_SCHEDULE_WEEK = displayMobileMode
    ? {
        calendar: { type: 'week' }, // WEEK VIEW
        agenda: { type: 'week' }
      }
    : {
        schedule: {
          type: displayMobileMode ? 'day' : 'week',
          startTime: '06:00'
        }
      };

  const CALENDAR_VIEW_MONTH_MOBILE = {
    calendar: { type: 'month' },
    agenda: { type: 'month' }
  };

  const CALENDAR_VIEW_TIMELINE = {
    timeline: {
      type: displayMobileMode ? 'day' : 'month',
      startTime: '06:00',
      eventList: true
      //resourceWidth: 120 // Contrôle la largeur des ressources
    }
  };
  const CALENDAR_VIEW_SCHEDULE_WEEK_GROUPS = {
    schedule: {
      type: 'week',
      startTime: '06:00'
      //resourceWidth: 120 // Contrôle la largeur des ressources
    }
  };

  useEffect(() => {
    setCalView(CALENDAR_VIEW_SCHEDULE_WEEK);
  }, [displayMobileMode]);

  const { t, i18n } = useTranslation();
  const { language } = i18n;

  const { appearance } = useContext(GlobalContext);
  const { me } = useContext(GlobalContext);

  const primaryColor = appearance?.primaryColor;

  const [begin, setBegin] = useState(calculateBeginning(dayjs()));
  const [end, setEnd] = useState(calculateEnd(dayjs()));
  const [mobiscrollDataPlanning, setMobiscrollDataPlanning] = useState([]);
  const [editOccurenceModalVisible, setEditOccurenceModalVisible] = useState(false);
  const [myPlanningChecked, setMyPlanningChecked] = useLocalStorage('p-myplanningchecked', true);
  const [forSpecificGroups, setForSpecificGroups] = useLocalStorage(
    'planning-forSpecificGroups',
    false
  ); // My groups by default
  const [userIdsSelected, setUserIdsSelected] = useState([]);
  const [userIdsDisplayed, setUserIdsDisplayed] = useState([]);
  /* Ici, le useLocalStorage n'est pas aussi réactif que le useState et ça pose probleme quand on l'utilise directemnet pour save les ids
   * lors de la selection d'un dossier complet dans le AbstractGroupsManager. Pour contrer ça, on utilise un state classique ids qui met à jour le
   * idsSaved et du coup ça résout le problème */
  const [idsSaved, setIdsSaved] = useLocalStorage('planningv2-groups', []);
  //const [idsSaved, setIdsSaved] = useState([]);
  const [ids, setIds] = useState(idsSaved); // Groupes sélectionnés (ids)
  const [hasStarted, setStarted] = useState(false);
  const [planningOrTimelineMode, setPlanningOrTimeLineMode] = useState('planning');
  const [view, setView] = useState('schedule'); //  calendar, scheduleByGroups, schedule
  const [currentDate, setCurrentDate] = useState(new Date());
  const [groupsView, setGroupsView] = useState(false);
  const [calView, setCalView] = useState(CALENDAR_VIEW_SCHEDULE_WEEK);
  const [showTimeZoneSelect, setShowTimeZoneSelect] = useState(false);
  const [currentTimeZone, setCurrentTimezone] = useLocalStorage(
    TimeZoneManager.getLocalStorageTimeZoneKey(),
    TimeZoneManager.getDefaultTimeZone()
  );
  /* Tooltip popup */
  const [anchor, setAnchor] = useState(null);
  const [currentEvent, setCurrentEvent] = useState(null);
  const [info, setInfo] = useState('');
  const [time, setTime] = useState('');
  const timerRef = React.useRef(null);
  const [isTooltipOpen, setTooltipOpen] = useState(false);
  const [eventDrawerOpen, setEventDrawerOpen] = useState(false);
  const [searchDrawerOpen, setSearchDrawerOpen] = useState(false);
  const [mobileCreationButtonsVisible, setMobileCreationButtonsVisible] = useState(false);
  const isTuteurOrAdmin = isAdmin() || isTuteur();
  const [selectedMenu, setSelectedMenu] = useState('generalPlanning');
  const [selectedRooms, setSelectedRooms] = useState([]);
  const [flexDirection, setFlexDirection] = useState(window.innerWidth <= 768 ? 'column' : 'row');

  const [mobileModalFilterByVisible, setMobileModalFilterByVisible] = useState(false);
  const [isMobileDrawerOpen, setIsMobileDrawerOpen] = useState(false);

  useEffect(() => {
    // Ferme tooltip si clic sur edit occurence
    if (editOccurenceModalVisible) {
      setTooltipOpen(false);
    }
  }, [editOccurenceModalVisible]);

  // When ids change, save them
  useEffect(() => {
    setIdsSaved(ids);
  }, [ids]);

  // Protection for ids suite à potentiel bug introduit dans localStorage
  useEffect(() => {
    let hasInvalid = false;
    const newIds = idsSaved?.filter((id) => {
      hasInvalid = [true, false].includes(id);
      return ![true, false].includes(id);
    });
    if (hasInvalid) {
      setIdsSaved(newIds);
    }
  }, [idsSaved]);

  const [disabledExternalCalendars, setDisabledExternalCalendars] = useState([]);

  const [showPlanningRevision, setShowPlanningRevision] = useLocalStorage(
    'showPlanningRevisionToggle',
    false
  );
  const calendarRef = useRef(null);

  const navigateToDate = (event) => {
    onSelectedDateChange({ date: event.date });
    setTimeout(() => {
      const eventRect = document.querySelector(`[data-id="${event.id}"]`);
      handleEventClick({ event, domEvent: { target: eventRect } });
    }, 500);
  };

  /* QUERIES ---------------------------------*/
  const [queryPlanning, { loading, error, data, refetch }] = useLazyQuery(
    QUERY_MON_PLANNING_BETWEEN,
    {
      variables: {
        begin: begin.toDate(),
        end: end.toDate(),
        myPlanning: myPlanningChecked,
        groupIds: ids,
        additionalUserIds: userIdsSelected,
        disabledExternalCalendars: disabledExternalCalendars
      },
      skip: !begin && !end,
      fetchPolicy: 'cache-and-network'
    }
  );
  const dataGroupes = useQuery(QUERY_GROUPS_FOR_PLANNING, { fetchPolicy: 'no-cache' }); // groupes (non individuels) que le user a le droit de voir

  const { data: dataExternalCalendars, refetch: refetchExternalCalendars } = useQuery(
    QUERY_GET_CALENDARS_TO_SHOW_ON_THE_SIDE,
    {
      fetchPolicy: 'cache-and-network'
    }
  );
  const externalCalendarsToShow = dataExternalCalendars?.getCalendarsToShowOnTheSide || [];

  useEffect(() => {
    if (isTooltipOpen && currentEvent) {
      // update currentEvent with the new data, if we refetched and popup is opened
      const newEvent = data?.getPlanning.find((e) => e.id === currentEvent.id);
      setCurrentEvent(newEvent);
    }
  }, [data]);

  // Planning revisions
  const [
    queryPlanningRevision,
    {
      loading: loadingRevision,
      error: errorRevisions,
      data: dataRevisions,
      refetch: refetchRevisions
    }
  ] = useLazyQuery(QUERY_REVISIONS_BETWEEN, {
    variables: {
      begin: begin.toDate(),
      end: end.toDate()
    },
    skip: (!begin && !end) || !showPlanningRevision,
    fetchPolicy: 'no-cache'
  });
  /* ------------------------------------------ */

  /* Load fresh data */
  const onPageLoading = (event, inst) => {
    const firstDay = event?.firstDay; // Période du calendrier
    const lastDay = event?.lastDay;
    // Pour fetch les évènements 30 jours avant, 30 jours après, pour prendre en compte les évènements longs (ex: vacances, révisions)
    setBegin(dayjs(firstDay).subtract(30, 'days'));
    setEnd(dayjs(lastDay).add(30, 'days'));
    queryPlanning();
    queryPlanningRevision();
  };

  useEffect(() => {
    if (!hasStarted) {
      queryPlanning();
      queryPlanningRevision();
      setStarted(true);
    }
  }, []);

  const getViewIcon = () => {
    if (openViewSelect) return <X />;
    switch (view) {
      case 'calendar':
        return <CalendarDays />;
      case 'schedule':
        return <List />;
      case 'scheduleByGroups':
        return <User />;
      case 'timeline':
        return <ChartNoAxesGantt />;
      default:
        return null;
    }
  };

  const changeView = React.useCallback(
    (event) => {
      let calendarView;
      switch (event.target.value) {
        case 'planning':
        case 'calendar':
          // MOIS
          calendarView = displayMobileMode
            ? CALENDAR_VIEW_MONTH_MOBILE
            : {
                calendar: {
                  labels: 'all' // Show all events
                }
              };
          setGroupsView(false);
          break;
        case 'schedule':
          // SEMAINE
          calendarView = CALENDAR_VIEW_SCHEDULE_WEEK;
          setGroupsView(false);
          break;
        case 'scheduleByGroups':
          // GROUPES
          calendarView = CALENDAR_VIEW_SCHEDULE_WEEK_GROUPS;
          setGroupsView(true);
          break;
        case 'timeline':
          // TIMELINE
          calendarView = CALENDAR_VIEW_TIMELINE;
          setGroupsView(true);
          break;
        default:
          break;
      }
      setOpenViewSelect(false);
      setView(event.target.value);
      setCalView(calendarView);
    },
    [setView, setCalView, setGroupsView]
  );

  const onSelectedDateChange = React.useCallback(
    (event) => {
      const value = event?.date;
      setCurrentDate(value);
    },
    [setCurrentDate]
  );

  const navigatePage = React.useCallback(
    (prev) => {
      let nextDate;
      if (view === 'calendar') {
        // Mois précédent / suivant
        nextDate = dayjs(currentDate)
          .add(prev ? -1 : 1, 'month')
          .toDate();
      } else {
        // Mode semaine : navigation précise
        nextDate = dayjs(currentDate)
          .add(prev ? -7 : 7, 'day')
          .toDate();
      }
      setCurrentDate(nextDate);
    },
    [view, currentDate]
  );

  useEffect(() => {
    const handleResize = () => {
      setFlexDirection(window.innerWidth <= 768 ? 'column' : 'row');
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const [mobileFuseauModalVisible, setMobileFuseauModalVisible] = useState(false);

  const handleEventClick = React.useCallback(
    (args) => {
      const { domEvent } = args;
      const { event } = args;
      if ((domEvent?.metaKey || domEvent?.ctrlKey) && event.ical_data === null) {
        router.push(getPlanningLink(event));
      } else {
        displayMobileMode ? eventDrawer(args) : eventPopin(args);
      }
    },
    [displayMobileMode, eventDrawer, eventPopin, isTooltipOpen, currentEvent]
  );

  const [searchText, setSearchText] = useState('');

  const [debouncedSearchText, setDebouncedSearchText] = useState('');

  // Effet pour débouncer la recherche
  useEffect(() => {
    const handler = debounce(() => {
      // Minimum 3 caractères pour lancer la recherche
      if (searchText.length >= 3) {
        setDebouncedSearchText(searchText);
      } else if (searchText.length === 0) {
        setResultsSearchEvent([]);
        setDisplaySearchSider(false);
      }
    }, 300); // Attendre 300ms après la dernière frappe

    handler();
    return () => {
      handler.cancel();
    };
  }, [searchText]);

  // Lancer la recherche uniquement quand le texte débounced change
  useEffect(() => {
    async function fetchData() {
      if (debouncedSearchText) {
        setDisplaySearchSider(true);
        const res = await searchDatesDiff();
        setResultsSearchEvent(res.data.searchDatesDiff);
      }
    }

    fetchData();
  }, [debouncedSearchText, searchDatesDiff]);

  const [resultsSearchEvent, setResultsSearchEvent] = useState([]);
  const [searchDatesDiff, { loading: loadingSearchEvent }] = useLazyQuery(QUERY_SEARCH_DATES_DIFF, {
    variables: { filter: { text: debouncedSearchText } },
    fetchPolicy: 'cache-and-network'
  });

  const timeZoneSelect = (
    <>
      <Select
        style={{ minWidth: '300px' }}
        showSearch
        placeholder="Select a timezone"
        value={currentTimeZone}
        onSelect={(value) => {
          setCurrentTimezone(value);
          moment.tz.setDefault(value);
          window.location.reload();
        }}
      >
        {moment.tz.names().map((tz) => (
          <Select.Option value={tz} key={tz}>
            {tz === 'CET' ? 'CET : Europe/Paris' : tz}
          </Select.Option>
        ))}
      </Select>
    </>
  );
  const currentTimeZoneString = currentTimeZone === 'CET' ? 'Paris' : currentTimeZone;
  const timeZoneControls = (
    <>
      {showTimeZoneSelect ? (
        timeZoneSelect
      ) : (
        <AntdButton type="link" onClick={() => setShowTimeZoneSelect(true)}>
          {currentTimeZoneString}
        </AntdButton>
      )}
    </>
  );

  const mobileViewControls = (
    <>
      <Button
        onClick={() => setOpenViewSelect(!openViewSelect)}
        style={{ fontSize: 22, margin: 0 }}
      >
        {getViewIcon()}
      </Button>
      {openViewSelect && (
        <div
          style={{
            position: 'absolute',
            bottom: 40,
            right: 30,
            zIndex: 100,
            background: 'white',
            border: '1px solid lightgrey',
            borderRadius: 12,
            padding: 12,
            display: 'flex',
            flexDirection: 'column',
            boxShadow:
              'rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px'
          }}
        >
          <div
            style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', gap: 8 }}
            onClick={() => changeView({ target: { value: 'schedule' } })}
          >
            <List /> {t('general.Week')}
          </div>
          <Divider style={{ margin: 8 }} />
          <div
            style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', gap: 8 }}
            onClick={() => changeView({ target: { value: 'calendar' } })}
          >
            <CalendarDays /> {t('general.Months')}
          </div>
          <Divider style={{ margin: 8 }} />
          <div
            style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', gap: 8 }}
            onClick={() => changeView({ target: { value: 'scheduleByGroups' } })}
          >
            <User /> {t('general.Groups')}
          </div>
          <Divider style={{ margin: 8 }} />
          <div
            style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', gap: 8 }}
            onClick={() => changeView({ target: { value: 'timeline' } })}
          >
            <ChartNoAxesGantt /> {t('Timeline')}
          </div>
        </div>
      )}
    </>
  );

  const mobileTimeZoneControls = (
    <>
      <Button
        onClick={() => setOpenTimezoneSelect(!openTimezoneSelect)}
        style={{ fontSize: 22, margin: 0 }}
      >
        {openTimezoneSelect ? <X /> : <Globe />}
      </Button>
      {openTimezoneSelect && (
        <div
          style={{
            position: 'absolute',
            bottom: 40,
            left: 30,
            zIndex: 100,
            maxHeight: 250,
            overflow: 'scroll',
            background: 'white',
            border: '1px solid lightgrey',
            borderRadius: 12,
            padding: 12,
            display: 'flex',
            flexDirection: 'column',
            boxShadow:
              'rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px'
          }}
        >
          {moment.tz.names().map((tz) => (
            <>
              <div
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  setCurrentTimezone(tz);
                  moment.tz.setDefault(tz);
                  window.location.reload();
                }}
              >
                {tz === 'CET' ? 'CET : Europe/Paris' : tz}
              </div>
              <Divider style={{ margin: 8 }} />
            </>
          ))}
        </div>
      )}
    </>
  );

  const customWithNavButtons = () => {
    if (displayMobileMode) {
      return (
        <div
          style={{
            display: 'flex',
            width: '100%',
            justifyContent: 'space-between',
            alignItems: 'center',
            height: 40
          }}
        >
          <div>
            <CalendarNav className="md-custom-header-nav" />
          </div>
          <div>
            <Button
              onClick={() => navigatePage(true)}
              icon="material-arrow-back"
              variant="flat"
              className="md-custom-header-button"
            ></Button>
            <CalendarToday className="md-custom-header-today" />
            <Button
              onClick={() => navigatePage(false)}
              icon="material-arrow-forward"
              variant="flat"
              className="md-custom-header-button"
            ></Button>
          </div>
          <div>
            <div style={{ position: 'relative', display: 'flex' }}>
              <Button onClick={() => setSearchDrawerOpen(true)}>
                <Search />
              </Button>
            </div>
          </div>
          <div>
            <div style={{ position: 'relative', display: 'flex' }}>
              {isTuteurOrAdmin && (
                <EventCreation
                  refetch={refetch}
                  refetchExternalCalendars={refetchExternalCalendars}
                  buttonType="link"
                  style={{ fontSize: 22 }}
                  mobileMode
                />
              )}
            </div>
          </div>
        </div>
      );
    }

    return (
      <div
        style={{
          display: 'flex',
          width: '100%',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: 40
        }}
      >
        <CalendarNav className="md-custom-header-nav" />
        {loading && <SpinnerCentered />}
        <div className="md-custom-header-controls">
          <Button
            onClick={() => navigatePage(true)}
            icon="material-arrow-back"
            variant="flat"
            className="md-custom-header-button"
          />
          <CalendarToday className="md-custom-header-today" />
          <Button
            onClick={() => navigatePage(false)}
            icon="material-arrow-forward"
            variant="flat"
            className="md-custom-header-button"
          />
        </div>
      </div>
    );
  };

  /* Load mobiscroll planning from data */
  useEffect(() => {
    // General planning
    const { dataPlanning, datesToAdd } = getMobiscrollPlanningFromData(
      t,
      view,
      data?.getPlanning,
      userIdsSelected,
      currentTimeZone,
      selectedRooms,
      me
    );
    // Revision planning

    let dataPlanningRevision = [];
    if (showPlanningRevision) {
      dataPlanningRevision = getMobiscrollRevisionPlanning(
        t,
        view,
        dataRevisions?.monPlanningRevision,
        currentTimeZone
      );
    }

    setMobiscrollDataPlanning([...dataPlanning, ...datesToAdd, ...dataPlanningRevision]);
  }, [data, dataRevisions, userIdsSelected, selectedRooms, view, showPlanningRevision]);

  /* Groupe render */
  const renderCustomResource = (groupe) => {
    const isGroup = groupe?.__typename === 'Groupe';

    const timeLineStyle = {
      height: '100%'
    };
    const defaultStyle = {
      height: '64px'
    };
    const divStyle = planningOrTimelineMode === 'timeline' ? timeLineStyle : defaultStyle;

    if (isGroup) {
      return (
        <div className="resource-template-content">
          {groupe?.image && (
            <div style={divStyle}>
              <img
                alt="groupeImage"
                style={{ maxWidth: '50px', maxHeight: '50px', margin: 'auto' }}
                src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + groupe?.image)}
                onError={(e) => {
                  e.target.style = 'display: none';
                }}
              />
            </div>
          )}
          <div>
            {groupe?.isIndividual ? (
              <>
                <ExoUserLight id={groupe?.name} />
              </>
            ) : (
              <>{groupe?.name}</>
            )}
          </div>
        </div>
      );
    }
    return (
      <div className="resource-template-content">
        <div className="resource-name">
          <ExoUserLight id={groupe?.id} />
        </div>
      </div>
    );
  };

  /* Selectable groups */
  const allGroups = dataGroupes?.data?.groupsForPlanning;

  const getResourcesToShow = () => {
    if (forSpecificGroups) {
      return (
        (ids &&
          allGroups
            ?.filter((g) => ids?.includes(g?.id) && !g.isIndividual)
            ?.map((g) => ({ ...g }))) ||
        []
      );
    }

    // Il faut retourner un tableau d'objets, avec pour id le id de l'utilisateur
    if (userIdsSelected?.length > 0) {
      return (
        userIdsSelected
          ?.map((id) => ({ id }))
          ?.filter((value, index, self) => index === self.findIndex((t) => t.id === value.id))
          ?.map((g) => ({ ...g })) || []
      );
    }

    // Default
    return (
      data?.getPlanning
        ?.map((c) => c?.groupes?.filter((g) => !g.isIndividual) || [])
        ?.flat()
        ?.filter((value, index, self) => index === self.findIndex((t) => t.id === value.id))
        ?.map((g) => ({ ...g })) || []
    );
  };
  /* Groups to show in planning */
  // const groupsSelected = ids && allGroups?.filter(g => ids?.includes(g?.id)) || [];
  // All groups from planning data
  // const allGroupes = data?.getPlanning?.map(c => c?.groupes || {})?.flat().filter((value, index, self) => index === self.findIndex((t) => (t.id === value.id))) || [];

  const groupsRessourcesProps = groupsView
    ? {
        resources: getResourcesToShow(), // Groups in the current calendar period
        renderResource: renderCustomResource
      }
    : {};

  const eventPopin = React.useCallback(
    (args) => {
      const { event } = args;
      // Close current event if we click again
      if (isTooltipOpen && currentEvent?.id === event.id) {
        setTooltipOpen(false);
        return;
      }
      // Else, set current event
      // TODO voir si time, info, sont vraiment nécessaires et les virer
      const time = `${dayjs(event.start).format('HH:mm')} - ${dayjs(event.end).format('HH:mm')} `;
      setCurrentEvent(event);
      setInfo(event.tooltip); //
      setTime(time);
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      setAnchor(args.domEvent?.target);
      setTooltipOpen(true);
    },
    [currentEvent, info, time, isTooltipOpen]
  );

  const eventDrawer = React.useCallback((args) => {
    const { event } = args;
    const time = `${dayjs(event.start).format('HH:mm')} - ${dayjs(event.end).format('HH:mm')} `;
    setCurrentEvent(event);
    setInfo(event.tooltip); //
    setTime(time);

    setEventDrawerOpen(true);
  }, []);

  const renderEventMobile = React.useCallback((e) => {
    return <EventAgendaItem currentEvent={e} />;
  }, []);

  // Custom event render for mobile
  const renderEventProp = displayMobileMode
    ? {
        renderEvent: renderEventMobile
      }
    : {};

  const calendarView = (
    <div style={{ flex: 1 }}>
      <>
        <div
          style={{
            height: 'calc(100vh - 250px)',
            //width: displayMobileMode ? '100vw' : 'calc(100vw - 270px)',
            overflow: 'auto'
          }}
        >
          <Eventcalendar
            ref={calendarRef}
            locale={getMobiScrollLanguage(language)}
            dataTimezone="utc"
            displayTimezone={currentTimeZone}
            timezonePlugin={momentTimezone}
            {...groupsRessourcesProps}
            {...renderEventProp}
            renderHeader={customWithNavButtons}
            theme="ios"
            themeVariant="light"
            data={mobiscrollDataPlanning}
            onSelectedDateChange={onSelectedDateChange}
            selectedDate={currentDate}
            view={calView}
            onEventClick={handleEventClick}
            cssClass="md-custom-header"
            onPageLoading={onPageLoading}
            showEventTooltip
            style={{ height: '100vh' }}
            clickToCreate={!!canEdit}
            onEventCreate={handleEventCreation}
            dragToMove={true}
            dragToResize={true}
            onEventUpdate={(args) => {
              if (args.event.recurring) {
                setOpenRecurringEventModal(true);
                setRecurringEventArgs(args);
              } else {
                handleEventDragAndDrop(args);
              }
            }}
          />
        </div>

        <Modal
          onCancel={async () => {
            setOpenRecurringEventModal(false);
            await refetch();
          }}
          open={openRecurringEventModal}
          footer={null}
        >
          {loadingEditDateDiff || loadingSingleOccurence || loading ? (
            <SpinnerCentered />
          ) : (
            <>
              <Typography.Title level={5}>
                {t('Calendar.RecurringEventModalTitle')}
              </Typography.Title>
              <Typography.Paragraph>
                {t('Calendar.RecurringEventModalDescription', {
                  dateStart: dayjs(recurringEventArgs?.newEvent?.start).format('DD/MM/YYYY HH:mm')
                })}
              </Typography.Paragraph>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 12,
                  width: 'fit-content',
                  margin: 'auto',
                  marginTop: 44
                }}
              >
                <AntdButton
                  type={'primary'}
                  onClick={() => {
                    handleModifySingleOccurenceWithDuplication(recurringEventArgs);
                    // recurringEventArgs.newEvent.start
                  }}
                >
                  {t('Calendar.RecurringEventModalModifySingle')}
                </AntdButton>
                <AntdButton
                  onClick={() => {
                    moveAllOccurences(recurringEventArgs);
                  }}
                >
                  {t('Calendar.RecurringEventModalModifyAll')}
                </AntdButton>
              </div>
            </>
          )}
        </Modal>

        <Popup
          display="anchored"
          focusOnClose={false}
          isOpen={isTooltipOpen}
          anchor={anchor}
          touchUi={true}
          theme="ios"
          themeVariant="light"
          showOverlay={false}
          scrollLock={false}
          contentPadding={false}
          closeOnOverlayClick={true}
          width={'auto'}
        >
          <div style={{ padding: '10px' }}>
            <EventPopupContent
              style={{ backgroundColor: '#ffffff' }}
              currentEvent={currentEvent}
              info={info}
              setEditOccurenceModalVisible={setEditOccurenceModalVisible}
              refetch={refetch}
              setOpen={setTooltipOpen}
              isPopup
            />
          </div>
        </Popup>

        <Drawer
          open={searchDrawerOpen}
          placement="bottom"
          onClose={() => setSearchDrawerOpen(false)}
          height={'calc(100% - 109px)'}
          styles={{ body: { padding: '0' } }}
        >
          <div
            style={{
              padding: '24px 0',
              zIndex: 9,
              position: 'sticky',
              top: 0,
              background: 'white',
              display: 'flex',
              justifyContent: 'center'
            }}
          >
            <SearchAll text={searchText} setText={setSearchText} loading={loadingSearchEvent} />
          </div>

          <SearchEventResults
            results={resultsSearchEvent}
            loading={loadingSearchEvent}
            navigateToDate={navigateToDate}
          />
        </Drawer>
        <Drawer
          open={eventDrawerOpen}
          placement="bottom"
          onClose={() => setEventDrawerOpen(false)}
          height={'calc(100% - 109px)'}
          styles={{ body: { padding: '0 24px' } }}
          title={
            currentEvent?.ical_data && (
              <div
                style={{
                  display: 'flex',
                  width: '100%',
                  alignItems: 'center',
                  flexWrap: 'wrap',
                  justifyContent: 'end'
                }}
              >
                {currentEvent.ical_data.summary}
                <Tag style={{ margin: 8, cursor: 'default' }}>{t('Calendar.ExternalCalendar')}</Tag>
              </div>
            )
          }
          extra={
            !currentEvent?.ical_data && (
              <Typography.Link
                editable={canEdit && { onChange: (e) => updateEventName(e), triggerType: ['icon'] }}
                href={`${window.location.origin}/#${getPlanningLink(currentEvent)}`}
                target="_blank"
              >
                {currentEvent?.event?.name}
              </Typography.Link>
            )
          }
        >
          <EventPopupContent
            style={{ backgroundColor: '#ffffff' }}
            currentEvent={currentEvent}
            info={info}
            setEditOccurenceModalVisible={setEditOccurenceModalVisible}
            refetch={refetch}
            hideEventLink={true}
            setOpen={setEventDrawerOpen}
          />
        </Drawer>

        {/* Uniquement pour events récurrents, pouvoir éditer*/}
        {currentEvent &&
          (isTuteur() || isAdmin()) &&
          currentEvent?.hasRecurrence &&
          editOccurenceModalVisible && (
            <OccurrenceEditor
              currentEvent={currentEvent}
              editOccurenceModalVisible={editOccurenceModalVisible}
              setEditOccurenceModalVisible={setEditOccurenceModalVisible}
              refetch={refetch}
            />
          )}
      </>
    </div>
  );

  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(true);

  const toggleFilterPanel = () => {
    isFilterPanelOpen ? setIsFilterPanelOpen(false) : setIsFilterPanelOpen(true);
  };

  const filtersComponent = (
    <LeftFilters
      userIdsSelected={userIdsSelected}
      setUserIdsSelected={setUserIdsSelected}
      userIdsDisplayed={userIdsDisplayed}
      setUserIdsDisplayed={setUserIdsDisplayed}
      forSpecificGroups={forSpecificGroups}
      setForSpecificGroups={setForSpecificGroups}
      allGroups={allGroups}
      ids={ids}
      setIds={setIds}
      setMyPlanningChecked={setMyPlanningChecked}
      myPlanningChecked={myPlanningChecked}
      selectedMenu={selectedMenu}
      selectedRooms={selectedRooms}
      setSelectedRooms={setSelectedRooms}
      disabledExternalCalendars={disabledExternalCalendars}
      setDisabledExternalCalendars={setDisabledExternalCalendars}
      showPlanningRevision={showPlanningRevision}
      setShowPlanningRevision={setShowPlanningRevision}
      refetchRevisions={refetchRevisions}
      refetch={refetch}
      externalCalendarsToShow={externalCalendarsToShow}
      displayMobileMode={displayMobileMode}
    />
  );

  return (
    <>
      <FullMediParticlesBreadCrumb title={t('Planning')} />
      <ExoteachLayout
        style={
          {
            //height: '86%'
            //display: 'flex', flexDirection
          }
        }
      >
        {/* Header elements */}
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Form
            style={{
              display: 'flex',
              flexDirection: 'column',
              padding: displayMobileMode ? 0 : 24,
              width: '100%'
            }}
          >
            {/* Bouton creation web */}
            {!displayMobileMode && (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  flexWrap: 'wrap',
                  gap: 8
                }}
              >
                <AntdButton
                  onClick={() => {
                    toggleFilterPanel();
                  }}
                >
                  <CalendarSearch />
                </AntdButton>

                <EventCreation
                  refetch={refetch}
                  refetchExternalCalendars={refetchExternalCalendars}
                />

                <Radio.Group value={view} onChange={changeView} style={{ display: 'flex' }}>
                  <Radio.Button value="schedule">
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <List /> {t('general.Week')}
                    </div>
                  </Radio.Button>
                  <Radio.Button value="calendar">
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <CalendarDays /> {t('general.Months')}
                    </div>
                  </Radio.Button>
                  <Radio.Button value="scheduleByGroups">
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <User /> {t('general.Groups')}
                    </div>
                  </Radio.Button>
                  <Radio.Button key="timeline" value="timeline">
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <ChartNoAxesGantt /> {t('Timeline')}
                    </div>
                  </Radio.Button>
                </Radio.Group>

                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Clock10 />
                  {timeZoneControls}
                </div>
                <SearchAll text={searchText} setText={setSearchText} loading={loadingSearchEvent} />
              </div>
            )}
          </Form>
        </div>
        {displayMobileMode ? (
          <>
            {calendarView}
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginTop: '10px',
                marginLeft: '5px',
                marginRight: '5px',
                position: 'relative'
              }}
            >
              {mobileTimeZoneControls}

              <Button
                size="large"
                style={{ fontSize: 20 }}
                onClick={() => setIsMobileDrawerOpen(true)}
              >
                {t('Calendars')}
              </Button>

              {mobileViewControls}

              <Drawer
                open={isMobileDrawerOpen}
                placement="bottom"
                onClose={() => setIsMobileDrawerOpen(false)}
                height={displayMobileMode ? 'calc(100% - 109px)' : 'calc(100% - 64px)'}
                styles={{ body: { padding: '0 24px', fontSize: '1.5em' } }}
              >
                {filtersComponent}
              </Drawer>
            </div>
          </>
        ) : (
          <motion.div
            style={{
              display: 'grid',
              maxWidth: '100vw',
              gridTemplateColumns: `${isFilterPanelOpen ? '260px' : ''} 1fr ${displaySearchSider ? '350px' : ''}`
            }}
          >
            {isFilterPanelOpen && <motion.div layout>{filtersComponent}</motion.div>}
            <motion.div layout style={{ overflow: 'auto' }}>
              {calendarView}
            </motion.div>

            {displaySearchSider && (
              <motion.div
                layout
                style={{
                  height: 'calc(100vh - 292px)',
                  overflow: 'auto'
                }}
              >
                <SearchEventResults
                  results={resultsSearchEvent}
                  loading={loadingSearchEvent}
                  navigateToDate={navigateToDate}
                />
              </motion.div>
            )}
          </motion.div>
        )}
      </ExoteachLayout>
    </>
  );
}
