import { DATE_FORMATS, getUrlProtectedRessource, GlobalConfig, TimeZoneManager } from "@/shared/utils/utils.js";
import { Avatar, Table } from 'antd'
import dayjs from 'dayjs';
import React, { useState } from "react";
import Link from 'umi/link.js'

export const PlanningRevisionsListView = ({ isLoading = false, monPlanningRevision, currentTimeZone }) => {
  const columns = [
    {
      title: '📅 Cours à réviser',
      dataIndex: 'cours',
      key: 'cours',
      render: (_, event) => {
        return (
          <Link to={`/cours/${event?.cours?.id}`}>
            <b>
              {event?.cours?.ueCategory?.image && (
                <Avatar.Group>
                  <Avatar
                    style={{
                      width: '16px',
                      height: '16px',
                      lineHeight: '16px',
                    }}
                    src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + event?.cours?.ueCategory?.image)}/>
                </Avatar.Group>
              )}
              {event?.cours?.name} {event?.cours?.text && (`: ${event?.cours?.text}`)}
            </b>
          </Link>
        )
      },
    },
    {
      title: 'Occurence',
      dataIndex: 'occurence',
      key: 'occurence',
      render: (_, { occurence, totalOccurences }) => {
        return `${occurence}/${totalOccurences}${totalOccurences === occurence ? (' 👑') : ''}`
      },
    },
    {
      title: 'Date de première révision',
      dataIndex: 'premiereRevision',
      key: 'premiereRevision',
      render: (premiereRevision, event) => {
        return dayjs(premiereRevision).format('DD/MM/YYYY')
      },
    },
    {
      title: 'Date de dernière révision',
      dataIndex: 'derniereRevision',
      key: 'derniereRevision',
      render: (derniereRevision, event) => {
        return dayjs(derniereRevision).format('DD/MM/YYYY')
      },
    },
    {
      title: 'J0',
      dataIndex: 'date_diffusion',
      key: 'date_diffusion',
      render: (date_diffusion, event) => {
        return dayjs(date_diffusion.date).format('HH:mm DD/MM/YYYY')
      },
    },
    // premiere revision
    // derniere revisino
    // date diffusion en amphu
  ]


  return (
    <React.Fragment>
      {!isLoading && monPlanningRevision && monPlanningRevision.length > 0 && monPlanningRevision.map(e => (
        <React.Fragment>
          <h2>
            {dayjs(e.date).locale('fr').tz(currentTimeZone).format('DD MMMM YYYY')}
          </h2>
          <Table pagination={false} columns={columns} dataSource={e?.events} scroll={{ x: true }}/>
        </React.Fragment>
      ))}
    </React.Fragment>
  )
}
