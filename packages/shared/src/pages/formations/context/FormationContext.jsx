import { MUTATION_UPDATE_FORMATION_STEP_PROGRESSION } from '@/shared/graphql/formations.js';
import { useMutation } from '@apollo/client';
import { Form } from 'antd';
import React, { useEffect, useState } from 'react';

export const FormationContext = React.createContext(undefined);
export const FormationContextProvider = (props) => {
  const [currentSelection, setCurrentSelection] = useState([]);
  const [isInFormation, setIsInFormation] = useState(false);

  const [summary, setSummary] = useState([]);

  const [currentStepId, setCurrentStepId] = useState(undefined);
  const [currentSectionId, setCurrentSectionId] = useState(undefined);

  const [currentStep, setCurrentStep] = useState(undefined);
  const [currentSection, setCurrentSection] = useState(undefined);

  const [isEditing, setIsEditing] = useState(props?.isEditing || false);

  useEffect(() => {
    setIsEditing(props?.isEditing);
  }, [props.isEditing]);

  const [currentProgress, setCurrentProgress] = useState(undefined);

  const [steps, setSteps] = useState(undefined);
  const [form] = Form.useForm();

  const [updateFormationProgression, { loading: loadingMutUpdate }] = useMutation(
    MUTATION_UPDATE_FORMATION_STEP_PROGRESSION
  );

  return (
    <FormationContext.Provider
      value={{
        isEditing,
        setIsEditing,

        currentSelection,
        setCurrentSelection,
        form,

        steps,
        setSteps,

        currentStepId,
        setCurrentStepId,
        currentStep,
        setCurrentStep,

        currentSectionId,
        setCurrentSectionId,

        currentSection,
        setCurrentSection,

        updateFormationProgression,
        loadingMutUpdate,

        isInFormation,
        setIsInFormation,

        currentProgress,
        setCurrentProgress,

        summary,
        setSummary
      }}
    >
      {props.children}
    </FormationContext.Provider>
  );
};
