import { GET_ME } from '@/shared/models/user.js';
import { AvatarView } from '@/shared/pages/account/settings/components/profil.jsx';
import { getAvatarSrc } from '@/shared/services/user.js';
import { useQuery } from '@apollo/client';
import React from 'react';

export default function({ element }) {
  const { data: dataMe, refetch } = useQuery(GET_ME, { fetchPolicy: 'cache-and-network' });
  return (
    <div
      style={{
        textAlign: element?.settings?.textAlign || 'auto',
      }}
    >
      <AvatarView refetch={refetch} showTitle={false} avatar={getAvatarSrc(dataMe?.me?.avatar)} />
    </div>
  );
}
