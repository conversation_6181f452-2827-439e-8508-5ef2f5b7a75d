import { ContentWithMathJax } from '@/shared/components/ContentWithMathJax.jsx';
import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import Title from '@/shared/components/Formation/Title.jsx';
import EmbeddedVideo from '@/shared/components/Video/EmbeddedVideo.jsx';
import CourseShortcut from '@/shared/pages/cours/components/CourseShortcut.jsx';
import { CoursPage } from '@/shared/pages/cours/details/$cours.jsx';
import { EventPage } from '@/shared/pages/event/event';
import AvatarInputElementWrapper from '@/shared/pages/formations/components/AvatarInputElementWrapper.jsx';
import ElementDiapoSynthese from '@/shared/pages/formations/components/DiapoSynthese/ElementDiapoSynthese';
import DoExerciseElement from '@/shared/pages/formations/components/DoExerciseElement/DoExerciseElement';
import InputElementsInFormElement from '@/shared/pages/formations/components/InputElementsInFormElement.jsx';
import RegisterFieldElement from '@/shared/pages/formations/components/RegisterFieldElement.jsx';
import TagForInputElementPreview from '@/shared/pages/formations/components/TagForInputElementPreview.jsx';
import { TargetPredefinedInput } from '@/shared/pages/formations/components/TargetPredefinedInput.jsx';
import { DoMcq } from '@/shared/pages/qcm/$faireqcm.jsx';
import linkIcon from '@/shared/assets/link.svg';
import ErrorBoundaryElement from '@/shared/pages/qcm/components/error/ErrorBoundaryElement';
import { downloadFile, FILE_TYPE } from '@/shared/services/file.js';
import {
  AuthorizedDynamicUrlFieldsArray,
  ELEMENTS_TYPE,
  getElementOrBlockStyle
} from '@/shared/services/formations.js';
import {
  renderElementDescriptionWithFallback,
  renderElementNameWithFallback,
  tr
} from '@/shared/services/translate.js';
import { useMathJaxScript } from '@/shared/utils/hooks/useScript.js';
import {
  displayDirectHtml,
  getUrlProtectedRessource,
  GlobalConfig,
  isMobile
} from '@/shared/utils/utils.js';
import {
  Alert,
  Button,
  Card,
  DatePicker,
  Divider,
  Form,
  Image,
  Input,
  InputNumber,
  Select,
  Spin,
  Upload
} from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { DownloadOutlined, FileTwoTone, LinkOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';
import { GET_ME } from '@/shared/models/user';
import { FORMATION_ELEMENT_DYNAMIC_REGEX } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal';
import { GET_USER_PROPERTIES_FOLDER } from '@/shared/graphql/user/user-properties.js';
import { openInBrowser } from '@/services/downloadFiles.js';
import { useQuery } from '@apollo/client';
import { VidstackContextProvider } from '@/shared/components/Vidstack/VidstackContextProvider';
import VidstackFormationElementDisplay from '@/shared/pages/formations/components/modal/components/VidstackFormationElementDisplay';
import DiapoViewerFormationElementDisplay from '@/shared/pages/formations/components/modal/components/DiapoViewerFormationElementDisplay';
import {
  BACKEND_SETTINGS_KEY,
  DiapoViewerContextProvider
} from '@/shared/components/diapoViewer/DiapoViewerContextProvider';
import {ScormContextProvider} from "@/shared/components/SCORM/ScormContextProvider";
import ScormFormationElementDisplay
  from "@/shared/pages/formations/components/modal/components/ScormFormationElementDisplay";

/*
 * Renders an Formation Element. Unlocked is false in unfinished challenge
 * */
export default function ({
  // The element to show
  element,
  unlocked = true,

  // For input type
  input = false, // If it's an input element type or not
  customFiles, // Current file upload list
  setCustomFiles, // Function to set the file upload list

  isEditing = false, // If it's in edit mode or not
  usePublicFolder = false,

  disabledInputTypes = [] // List of input types to disable
}) {
  const { t } = useTranslation();

  const launchMathJax = useMathJaxScript();
  useEffect(() => {
    launchMathJax();
  }, []);

  const [videoElementKey, setVideoElementKey] = useState(0);

  useEffect(() => {
    setVideoElementKey(videoElementKey + 1);
  }, [element]);

  useEffect(() => {
    launchMathJax();
  }, [element]);

  // For preview, when not in form, we use a Form wrapper
  // Comment l'élément va être affiché => Si input => Alors entouré de <></> // Si pas input, alors Form.
  // Input correspond à si l'élément est d'un type input.
  const Wrapper = input ? React.Fragment : Form;

  const getHeight = () => {
    if (element.type === ELEMENTS_TYPE.LONG_ANSWER) {
      return '130px';
    }
    return '75px';
  };
  const [dynamicUrlValues, setDynamicUrlValues] = useState({});

  const {
    loading: loadingMe,
    refetch: refetchMe,
    error: errorMe
  } = useQuery(GET_ME, {
    notifyOnNetworkStatusChange: true,
    skip: element?.type !== ELEMENTS_TYPE.LINK,
    onCompleted: ({ me } = {}) => {
      const meDict = {};
      for (const authorizedKey of AuthorizedDynamicUrlFieldsArray) {
        meDict[authorizedKey] = me?.[authorizedKey] ?? 'null';
      }
      setDynamicUrlValues((prev) => {
        return { ...prev, ...meDict };
      });
    }
  });
  const {
    loading: loadingPropertyFolder,
    refetch: refetchPropertyFolder,
    error: errorPropertyFolder
  } = useQuery(GET_USER_PROPERTIES_FOLDER, {
    notifyOnNetworkStatusChange: true,
    skip: element?.type !== ELEMENTS_TYPE.LINK,
    onCompleted: ({ userPropertiesFolders } = {}) => {
      const propertiesDict = {};

      for (const folderArray of Object.values(userPropertiesFolders)) {
        for (const element of folderArray?.elements) {
          const label = `c-${element?.id}`;
          const userPropertyValue = element?.userPropertyValue;
          const value = userPropertyValue?.value ?? 'null';

          propertiesDict[label] = value;
        }
      }
      setDynamicUrlValues((prev) => {
        return { ...prev, ...propertiesDict };
      });
    }
  });

  const replaceMarkedStringWithData = (str) => {
    /* Fonction pour Dynamic URL, qui prend l'url, trouve les balises, et les remplaces avec les champs associés */
    const regex = FORMATION_ELEMENT_DYNAMIC_REGEX ?? null; // Regex pour capturer les variables entre $% et %$

    const result =
      regex &&
      str?.replace(regex, (match, p1) => {
        return dynamicUrlValues[p1] || 'null';
      });

    return result;
  };

  const dynamicUrl = useMemo(() => {
    if (!loadingMe && !loadingPropertyFolder) {
      return replaceMarkedStringWithData(element?.text ?? '');
    }
  }, [dynamicUrlValues, loadingMe, loadingPropertyFolder, element?.text]);

  // minHeight nécessaire pour que la preview dépasse pas
  // récupération de la largeur si on est un form =>
  const wrapperProps = input ? {} : { style: { minHeight: getHeight() } }; // For input, we don't need to set height

  /* Special input type elements  */
  const inputTypeElements = (
    <>
      {/* wrapper for target user property form element */}
      {element?.type === ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT && (
        <TargetPredefinedInput
          element={element}
          input={input}
          customFiles={customFiles}
          setCustomFiles={setCustomFiles}
          isEditing={isEditing}
        />
      )}
      {/* Special element for register fields only */}
      {element?.type === ELEMENTS_TYPE.REGISTER_FIELD && (
        <RegisterFieldElement
          element={element}
          input={input}
          disabled={disabledInputTypes.includes(ELEMENTS_TYPE.REGISTER_FIELD)}
          isEditing={isEditing}
        />
      )}

      {element?.type === ELEMENTS_TYPE.AVATAR_SELECT && (
        <>
          <span style={{ fontWeight: '600' }}>{t('ChooseYourAvatar')}</span>
          <AvatarInputElementWrapper element={element} />
        </>
      )}
      {element?.type === ELEMENTS_TYPE.BUTTON && (
        <div>
          <Button
            size={element?.settings?.size}
            type={element?.settings?.type}
            block={element?.settings?.block}
            style={{
              marginTop: '10px',
              backgroundColor: element?.settings?.backgroundColor || 'auto'
            }}
            onClick={() => {
              if (element?.settings?.target === 'platform') {
                router.push(`/user/platform-select/${element?.settings?.platformId}`);
              } else if (element?.settings?.target === 'inscription') {
                router.push(`/user/register`);
              } else {
                if (isMobile) {
                  openInBrowser(element?.settings?.link);
                } else {
                  window.open(element?.settings?.link, '_blank');
                }
              }
            }}
          >
            {element?.[tr('name')]}
          </Button>
        </div>
      )}
      {element?.type === ELEMENTS_TYPE.SHORT_ANSWER && (
        <div>
          <TagForInputElementPreview {...{ element, input }} />
          <Wrapper {...wrapperProps}>
            <Form.Item
              label={element?.name}
              name={['customFields', element?.id]}
              rules={[{ required: element?.settings?.isMandatory }]}
            >
              <Input type="text" />
            </Form.Item>
          </Wrapper>
        </div>
      )}
      {element?.type === ELEMENTS_TYPE.LONG_ANSWER && (
        <div>
          <TagForInputElementPreview {...{ element, input }} />
          <Wrapper {...wrapperProps}>
            <Form.Item
              label={element?.name}
              name={['customFields', element?.id]}
              rules={[{ required: element?.settings?.isMandatory }]}
            >
              <Input.TextArea rows={4} />
            </Form.Item>
          </Wrapper>
        </div>
      )}

      {(element?.type === ELEMENTS_TYPE.VIDSTACK_VIDEO ||
        element?.type === ELEMENTS_TYPE.VIDSTACK_AUDIO) && (
        <Wrapper {...wrapperProps}>
          <VidstackContextProvider
            key={videoElementKey}
            initValues={{
              initUrl: element?.settings?.url,
              initS3FileName: element?.s3FileName
            }}
            autoPlay={element?.settings?.vidStackAutoPlay}
            authorizeSpeedModification={element?.settings?.vidStackAuthorizeSpeedModification}
            authorizeFullScreen={element?.settings?.vidStackAuthorizeFullScreen}
            authorizePictureInPicture={element?.settings?.vidStackAuthorizePictureInPicture}
            authorizeDownload={element?.settings?.vidStackAuthorizeDownload}
            videoWidth={element?.settings?.vidStackVideoWidth}
            enableStatsTracking={element?.settings?.vidstackEnableStatsTracking}
            title={element?.settings?.vidstackTitle}
            imageSrc={element?.image}
            tracksStringObject={element?.settings?.vidstackTrackObject}
            initDescription={element?.settings?.vidstackQuillDescription}
            formationElementId={element?.id}
            layoutType={
              element?.type === ELEMENTS_TYPE.VIDSTACK_VIDEO
                ? 'video'
                : element?.type === ELEMENTS_TYPE.VIDSTACK_AUDIO
                  ? 'audio'
                  : null
            }
          >
            <VidstackFormationElementDisplay hideTitle />
          </VidstackContextProvider>
        </Wrapper>
      )}

      {element?.type === ELEMENTS_TYPE.FILE_IMPORT && (
        <div>
          <TagForInputElementPreview {...{ element, input }} />
          <Wrapper {...wrapperProps}>
            <Form.Item label={element?.name}>
              <Upload.Dragger
                name="file"
                listType="file"
                showUploadList
                beforeUpload={(file, fileList) => {
                  // find custom file by id in customFiles
                  // if exists, replace it (add file attribute)
                  // else add it
                  const existingCustomFile = customFiles?.find(
                    (customFile) => customFile?.elementId === element?.id
                  );
                  if (existingCustomFile) {
                    const newCustomFiles = customFiles?.map((customFile) => {
                      if (customFile?.elementId === element?.id) {
                        return {
                          ...customFile,
                          file
                        };
                      }
                      return customFile;
                    });
                    setCustomFiles(newCustomFiles);
                  } else {
                    setCustomFiles([
                      ...customFiles,
                      {
                        elementId: element?.id,
                        file
                      }
                    ]);
                  }
                  return false;
                }}
                fileList={
                  customFiles &&
                  customFiles?.find(
                    (customFile) => customFile?.elementId === element?.id && customFile.file
                  )
                    ? [
                        customFiles?.find((customFile) => customFile?.elementId === element?.id)
                          ?.file
                      ]
                    : []
                }
                onRemove={(file) => {
                  const newCustomFiles = customFiles?.filter(
                    (customFile) => customFile?.elementId !== element?.id
                  );
                  setCustomFiles(newCustomFiles);
                }}
              >
                <div>
                  <div className="ant-upload-text">{t('general.upload')}</div>
                </div>
              </Upload.Dragger>
            </Form.Item>
          </Wrapper>
        </div>
      )}

      {element?.type === ELEMENTS_TYPE.SINGLE_SELECT && (
        <div>
          <TagForInputElementPreview {...{ element, input }} />
          <Wrapper {...wrapperProps}>
            <Form.Item
              label={element?.name}
              name={['customFields', element?.id]}
              rules={[{ required: element?.settings?.isMandatory }]}
            >
              <Select>
                {element?.settings?.answers?.map((answer, index) => (
                  <Select.Option key={index} value={answer?.text}>
                    {answer?.text}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Wrapper>
        </div>
      )}
      {element?.type === ELEMENTS_TYPE.MULTIPLE_SELECT && (
        <div>
          <TagForInputElementPreview {...{ element, input }} />
          <Wrapper {...wrapperProps}>
            <Form.Item
              label={element?.name}
              name={['customFields', element?.id]}
              rules={[{ required: element?.settings?.isMandatory }]}
            >
              <Select mode="multiple">
                {element?.settings?.answers?.map((answer, index) => (
                  <Select.Option key={index} value={answer?.text}>
                    {answer?.text}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Wrapper>
        </div>
      )}
      {element?.type === ELEMENTS_TYPE.INTEGER_NUMBER && (
        <div>
          <TagForInputElementPreview {...{ element, input }} />
          <Wrapper {...wrapperProps}>
            <Form.Item
              label={element?.name}
              name={['customFields', element?.id]}
              rules={[{ required: element?.settings?.isMandatory }]}
            >
              <InputNumber />
            </Form.Item>
          </Wrapper>
        </div>
      )}
      {element?.type === ELEMENTS_TYPE.FLOAT_NUMBER && (
        <div>
          <TagForInputElementPreview {...{ element, input }} />
          <Wrapper {...wrapperProps}>
            <Form.Item
              label={element?.name}
              name={['customFields', element?.id]}
              rules={[{ required: element?.settings?.isMandatory }]}
            >
              <InputNumber step="0.01" />
            </Form.Item>
          </Wrapper>
        </div>
      )}
      {element?.type === ELEMENTS_TYPE.DATE_PICKER && (
        <div>
          <TagForInputElementPreview {...{ element, input }} />
          <Wrapper {...wrapperProps}>
            <Form.Item
              label={element?.name}
              name={['customFields', element?.id]}
              rules={[{ required: element?.settings?.isMandatory }]}
            >
              <DatePicker format="DD/MM/YYYY" style={{ width: '100%' }} />
            </Form.Item>
          </Wrapper>
        </div>
      )}
      {element?.type === ELEMENTS_TYPE.DATE_AND_TIME_PICKER && (
        <div>
          <TagForInputElementPreview {...{ element, input }} />
          <Wrapper {...wrapperProps}>
            <Form.Item
              label={element?.name}
              name={['customFields', element?.id]}
              rules={[{ required: element?.settings?.isMandatory }]}
            >
              <DatePicker format="DD/MM/YYYY HH:mm" showTime style={{ width: '100%' }} />
            </Form.Item>
          </Wrapper>
        </div>
      )}
    </>
  );

  return (
    <div>
      {element?.type === ELEMENTS_TYPE.TITLE && (
        <div id={`fcTitle-${element.id}`}>
          <Title element={element} title={element?.title} />
        </div>
      )}

      {element?.type === ELEMENTS_TYPE.IMAGE && (
        <div style={{ textAlign: 'center' }}>
          {element?.settings?.notClickable ? (
            <img
              onError={(e) => {
                e.target.style = 'display: none';
              }}
              style={{
                maxWidth: element?.settings?.imageWidth || 'auto', // 350px
                height: 'auto'
              }}
              src={
                GlobalConfig.get()?.FILES_URL && !usePublicFolder
                  ? getUrlProtectedRessource(GlobalConfig.get().FILES_URL + element?.image)
                  : CONFIGS?.serverUrl + element?.image
              }
              alt="image"
            />
          ) : (
            <Image
              onError={(e) => {
                e.target.style = 'display: none';
              }}
              style={{
                maxWidth: element?.settings?.imageWidth || 'auto', // 350px
                height: 'auto'
              }}
              src={
                GlobalConfig.get()?.FILES_URL && !usePublicFolder
                  ? getUrlProtectedRessource(GlobalConfig.get().FILES_URL + element?.image)
                  : CONFIGS?.serverUrl + element?.image
              }
              alt="image"
            />
          )}

          {renderElementDescriptionWithFallback(element) && (
            <div style={{ color: '#666666', marginTop: '5px' }}>
              {renderElementDescriptionWithFallback(element)}
            </div>
          )}
        </div>
      )}

      {element?.type === ELEMENTS_TYPE.RICH_TEXT && (
        <div style={getElementOrBlockStyle(element)} className="exoteach-rich-text">
          <RenderQuillHtml>{element?.[tr('text')] || element?.text}</RenderQuillHtml>
        </div>
      )}

      {element?.type === ELEMENTS_TYPE.HTML && displayDirectHtml(element?.text)}

      {element?.type === ELEMENTS_TYPE.COURS && (
        <ErrorBoundaryElement>
          <CoursPage
            coursId={element?.coursId}
            withLateralMenu={false}
            hideBanner
            hideBreadCrumb
            hideDiscussion
            showSettings={element?.settings}
            useSettingsFromParent
          />
        </ErrorBoundaryElement>
      )}

      {/* Target event element */}
      {element?.type === ELEMENTS_TYPE.EVENT && (
        <>
          <EventPage eventId={element?.targetEventId} showBreadcrumb={false} />
        </>
      )}

      {element?.type === ELEMENTS_TYPE.COURSE_SHORTCUT && (
        <ErrorBoundaryElement>
          <CourseShortcut coursId={element?.coursId} element={element} />
        </ErrorBoundaryElement>
      )}

      {/* Target Form */}
      {element?.type === ELEMENTS_TYPE.FORM && (
        <div style={{ marginTop: 64 }}>
          <InputElementsInFormElement formId={element?.formId} parentElementId={element?.id} />
        </div>
      )}

      {element?.type === ELEMENTS_TYPE.LINK && (
        <Card
          style={{
            boxShadow:
              'rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px'
          }}
        >
          {!loadingMe && !loadingPropertyFolder ? (
            <div
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '10px' }}
            >
              <div>
                <img src={linkIcon} style={{ width: '60px', height: '60px' }} />
              </div>

              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'start',
                  justifyContent: 'left'
                }}
              >
                <div style={{ textAlign: 'left' }}>
                  <a
                    style={{ fontSize: '20px', lineHeight: '20px' }}
                    href={dynamicUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {renderElementNameWithFallback(element)} <LinkOutlined />
                  </a>
                </div>

                <div style={{}}>
                  {renderElementDescriptionWithFallback(element) && (
                    <div>{renderElementDescriptionWithFallback(element)}</div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <Spin />
          )}
        </Card>
      )}

      {element?.type === ELEMENTS_TYPE.FILE && (
        <Card>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {!element?.image ? (
                <FileTwoTone
                  onClick={async () => {
                    await downloadFile(FILE_TYPE.FILE, element.text);
                  }}
                  style={{ fontSize: '50px' }}
                />
              ) : (
                <img
                  onClick={async () => {
                    await downloadFile(FILE_TYPE.FILE, element.text);
                  }}
                  onError={(e) => {
                    e.target.style = 'display: none';
                  }}
                  src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + element?.image)}
                  style={{
                    height: '100px',
                    width: 'auto',
                    cursor: 'pointer',
                    border: 'solid 2px #8080808f',
                    borderRadius: '5px'
                  }}
                  alt="file icon preview"
                />
              )}
              <div style={{ flexDirection: 'column' }}>
                <div style={{ fontSize: '13pt', fontWeight: 'bold', marginLeft: '10px' }}>
                  {renderElementNameWithFallback(element)}
                </div>
                <div style={{ fontSize: '10pt', color: 'grey', marginLeft: '10px' }}>
                  {renderElementDescriptionWithFallback(element)}
                </div>
              </div>
            </div>

            <div>
              <Button
                key="download"
                type="default"
                size="large"
                onClick={async () => {
                  await downloadFile(FILE_TYPE.FILE, element.text);
                }}
                icon={<DownloadOutlined />}
              />
            </div>
          </div>
        </Card>
      )}

      {element?.type === ELEMENTS_TYPE.VIDEO && (
        <Card style={{ textAlign: 'center' }}>
          {renderElementNameWithFallback(element) && (
            <h2>{renderElementNameWithFallback(element)}</h2>
          )}
          {renderElementDescriptionWithFallback(element) && (
            <h4>{renderElementDescriptionWithFallback(element)}</h4>
          )}
          <EmbeddedVideo video={element.text} />
        </Card>
      )}

      {element?.type === ELEMENTS_TYPE.CALLOUT && (
        <div
          style={{
            whiteSpace: 'pre-line',
            wordBreak: 'normal'
          }}
        >
          <ContentWithMathJax
            value={
              <Alert
                message={renderElementNameWithFallback(element)}
                description={renderElementDescriptionWithFallback(element)}
                type={
                  ['success', 'info', 'warning', 'error'].includes(element?.text)
                    ? element?.text
                    : 'info'
                }
                showIcon
              />
            }
          />
        </div>
      )}

      {element?.type === ELEMENTS_TYPE.MCQ && (
        <div>
          {element?.mcqId && (
            <DoMcq
              formationElement={element}
              qcmId={element?.mcqId}
              doType="fullscreen"
              isFormation
              showBanner={false}
              useExoteachLayout={false}
              correctionInSamePage
            />
          )}
        </div>
      )}

      {element?.type === ELEMENTS_TYPE.DIAPO_SYNTHESE && <ElementDiapoSynthese element={element} />}

      {element?.type === ELEMENTS_TYPE.DIAPO && (
        <DiapoViewerContextProvider
          initValues={{
            initUrl: element?.settings?.url,
            initS3FileName: element?.s3FileName
          }}
          tracks={element?.settings?.[BACKEND_SETTINGS_KEY.TRACK]}
          title={element?.settings?.[BACKEND_SETTINGS_KEY.TITLE]}
          initDescription={element?.settings?.[BACKEND_SETTINGS_KEY.DESCRIPTION]}
          authorizeDownload={element?.settings?.[BACKEND_SETTINGS_KEY.AUTHORIZE_DOWNLOAD]}
          authorizeFullScreen={element?.settings?.[BACKEND_SETTINGS_KEY.AUTHORIZE_FULL_SCREEN]}
          width={element?.settings?.[BACKEND_SETTINGS_KEY.WIDTH]}
        >
          <DiapoViewerFormationElementDisplay hideTitle />
        </DiapoViewerContextProvider>
      )}

      {element?.type === ELEMENTS_TYPE.DO_EXERCISE && <DoExerciseElement element={element} />}

      {/* Special separator */}
      {element?.type === ELEMENTS_TYPE.SECTION && isEditing && <Divider>{element?.name}</Divider>}

      {element?.type === ELEMENTS_TYPE.SCORM && (
        <ScormContextProvider
          formationElementId={element?.id}
          scormVersion={element?.scormVersion}
          scormEntryPoint={element?.scormEntryPoint}
          scormFilename={element?.scormFilename}
          scormFilePath={element?.scormFilePath}
          scormParsing={element?.scormParsing}
        >
          <ScormFormationElementDisplay />
        </ScormContextProvider>
      )}

      {/* INPUT TYPE ELEMENTS */}
      {inputTypeElements}
    </div>
  );
}
