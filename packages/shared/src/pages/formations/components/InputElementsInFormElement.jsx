import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { QUERY_ELEMENTS_IN_FORM_WITH_SAVED_VALUES } from '@/shared/graphql/forms.js';
import {
  SEND_INPUT_ELEMENTS_FORM,
  SET_FORM_COMPLETED
} from '@/shared/graphql/user/user-properties.js';
import FormationElement from '@/shared/pages/formations/components/FormationElement.jsx';
import {
  ELEMENTS_TYPE,
  extractElementsInSteps,
  extractInitialValuesFromElementsUserProperties,
  mapFormFieldsToCustomFields
} from '@/shared/services/formations.js';
import { IS_DEV, showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Form, Result, Space, Steps } from 'antd';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import router from 'umi/router';

export default function InputElementsInFormElement({
  formId,
  showBackToHome = false,
  parentElementId = null // The parent element ID, if any
}) {
  const { t } = useTranslation();

  const [currentStep, setCurrentStep] = useState(0);
  const [hasAnswered, setHasAnswered] = useState(false);
  const [form] = Form.useForm();
  const [customFiles, setCustomFiles] = useState([]);
  const [elementsSplittedInSteps, setElementsSplittedInSteps] = useState([]);

  const { data: dataElementsInForm, loading: loadingElementsInForm } = useQuery(
    QUERY_ELEMENTS_IN_FORM_WITH_SAVED_VALUES, // With user property values
    {
      fetchPolicy: 'no-cache',
      variables: { formId },
      skip: !formId
    }
  );

  // TODO si il a déjà répondu à tout, on affiche un message comme quoi il a déjà répondu à tout (vérification déjà faite dans le parent normalement, à vérifier lors de l'import de form)
  // Et ajouter bouton précédent pour revenir sur les réponses déjà faites
  // Et ça doit createOrUpdate mais pas recréer à chaque fois

  const elements = dataElementsInForm?.elementsInForm;
  const [sendFormInputElements, { loading: loadingMut }] = useMutation(SEND_INPUT_ELEMENTS_FORM);
  const [setFormCompleted, { loading: loadingCompleteFormMut }] = useMutation(SET_FORM_COMPLETED);

  const mutationLoading = loadingMut || loadingCompleteFormMut;

  // Découpage en steps automatique
  useEffect(() => {
    extractElementsInSteps(elements, setElementsSplittedInSteps);
  }, [dataElementsInForm]);

  // Set form fully completed after last step
  const setFormCompletedBackend = async () => {
    try {
      return await setFormCompleted({
        variables: { formId, finished: true, parentElementId }
      });
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };
  const setFormUncompleteBackend = async () => {
    try {
      await setFormCompleted({
        variables: { formId, finished: false }
      });
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  /* Semi-automatic fields validations, same in CustomFormItems.jsx */
  const validateFields = async (stepIndex) => {
    const stepFields = elementsSplittedInSteps[stepIndex].map((element) => {
      // Elements registerField: le nom est le type
      if (element.type === ELEMENTS_TYPE.REGISTER_FIELD) {
        return element.settings.type;
      }
      // éléments prédéfinis importés (proxy)
      if (element?.type === ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT) {
        return ['customFields', element.objectId];
      }
      // Élements standard: le nom est customFields+id
      return ['customFields', element.id];
    });
    // customFields+id
    // Ou juste attribut name pour register field

    await form.validateFields(stepFields);
    ////////
  };

  // Envoie une partie ou totalité du formulaire
  const handleFinish = async (formInput) => {
    try {
      const formItems = JSON.parse(JSON.stringify(formInput));
      if (IS_DEV) {
        console.log(formItems?.customFields);
      }
      const customFields = mapFormFieldsToCustomFields(formItems.customFields, customFiles);
      if (IS_DEV) {
        console.log({ customFields });
      }
      delete formItems.customFields;
      let customFieldsWithFormId = [];
      if (customFields?.length > 0) {
        customFieldsWithFormId = customFields.map((customField) => ({
          ...customField,
          formId
        }));
      }
      await sendFormInputElements({
        variables: {
          input: customFieldsWithFormId
        }
      });
      return true;
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  const getInitialValues = (elementsInStep) => {
    return extractInitialValuesFromElementsUserProperties(elementsInStep);
  };

  return (
    <>
      {hasAnswered ? (
        <>
          {loadingMut ? (
            <SpinnerCentered />
          ) : (
            <>
              <Result status="success" title="Vos réponses ont bien été enregistrées." />
              {showBackToHome && (
                <>
                  <br />
                  <Button type="primary" onClick={() => router.push('/')}>
                    {t('backToHome')}
                  </Button>
                </>
              )}
            </>
          )}
        </>
      ) : (
        <>
          {/* Si découpage en steps, on les affiche */}
          {elementsSplittedInSteps.length > 1 && (
            <Steps current={currentStep}>
              {elementsSplittedInSteps.map((step, index) => {
                const sectionElement = step.find((e) => e.type === 'section');
                return <Steps.Step key={index} title={sectionElement ? sectionElement.name : ``} />;
              })}
            </Steps>
          )}

          {/* Affichage de la partie correspondante. Si qu'une seule affiche tout */}
          {elementsSplittedInSteps.map((step, stepIndex) => (
            <Form
              layout="vertical"
              initialValues={getInitialValues(step)}
              onFinish={handleFinish}
              form={form}
              size="large"
            >
              <div
                key={stepIndex}
                style={{ display: stepIndex === currentStep ? 'block' : 'none' }}
              >
                {step.map((element) => (
                  <FormationElement
                    key={element.id}
                    element={element}
                    input
                    customFiles={customFiles}
                    setCustomFiles={setCustomFiles}
                  />
                ))}

                {/* Si on est sur n'importe quelle étape sauf la dernière */}
                {stepIndex !== elementsSplittedInSteps.length - 1 ? (
                  <Space>
                    {stepIndex !== 0 && (
                      <Button
                        type="primary"
                        onClick={async () => {
                          setCurrentStep(stepIndex - 1);
                        }}
                      >
                        {t('back')}
                      </Button>
                    )}
                    <Button
                      type="primary"
                      loading={mutationLoading}
                      onClick={async () => {
                        await validateFields(stepIndex);
                        await form.submit(); // Enregistre step actuelle
                        await setFormUncompleteBackend(); // Set form uncompleted for backend
                        setCurrentStep(stepIndex + 1);
                      }}
                    >
                      {t('Next')}
                    </Button>
                  </Space>
                ) : (
                  <Space>
                    {stepIndex !== 0 && (
                      <Button
                        type="primary"
                        onClick={async () => {
                          setCurrentStep(stepIndex - 1);
                        }}
                      >
                        {t('back')}
                      </Button>
                    )}
                    <Button
                      loading={mutationLoading}
                      type="primary"
                      onClick={async () => {
                        await validateFields(stepIndex);
                        await form.submit(); // Enregistre step finale
                        await setFormCompletedBackend(); // Set form completed for backend
                        setHasAnswered(true);
                      }}
                    >
                      {t('send')}
                    </Button>
                  </Space>
                )}
              </div>
            </Form>
          ))}
        </>
      )}
    </>
  );
}
