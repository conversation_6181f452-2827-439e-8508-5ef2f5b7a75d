import React from 'react';
import { Button } from 'antd';

export const ElementTypeButton = ({ icon, label, selected = false, onClick }) => {
  return (
    // eslint-disable-next-line react/react-in-jsx-scope
    <Button
      type="default"
      onClick={onClick}
      style={{
        height: 100,
        width: 100,
        padding: 0,
        border: selected ? '2px solid #1677ff' : '1px solid #d9d9d9',
        borderRadius: 8,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 8,
        fontWeight: 500
      }}
    >
      <div style={{ fontSize: 38, lineHeight: 1 }}>{icon}</div>
      <div style={{ lineHeight: 1 }}>{label}</div>
    </Button>
  );
};
