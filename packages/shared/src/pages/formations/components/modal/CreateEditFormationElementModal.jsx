import {
  PedagogicCascaderSelector,
  PedagogicCascaderSelectorTarget,
  SelectorType
} from '@/shared/components/Cours/PedagogicCascaderSelector.jsx';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult';
import ExoQuillEditor from '@/shared/components/ExoQuill/ExoQuillEditor';
import { ExoQuillToolbarPresets } from '@/shared/components/ExoQuill/utils';
import Title from '@/shared/components/Formation/Title.jsx';
import EmbeddedVideo from '@/shared/components/Video/EmbeddedVideo.jsx';
import { AgnosticWatermarkModalModule } from '@/shared/components/WatermarkComponants/AgnosticWatermarkModalModule';
import { QUERY_ALL_GROUPS } from '@/shared/graphql/cours';
import {
  MUTATION_CREATE_FORMATION_ELEMENT,
  MUTATION_DELETE_FORMATION_ELEMENT,
  MUTATION_DIAPO_TRACK_FILE_PREPROCESSING,
  MUTATION_UPDATE_FORMATION_ELEMENT,
  MUTATION_VIDSTACK_TRACK_IMAGE_PREPROCESSING,
  QUERY_ALL_TITLES
} from '@/shared/graphql/formations.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import FormSelector from '@/shared/pages/admin/forms/components/FormSelector.jsx';
import PlanificationTab from '@/shared/pages/admin/groupes/planification/PlanificationTab.jsx';
import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager.jsx';
import IndividualPermissionsManager from '@/shared/pages/admin/permissions/IndividualPermissionsManager';
import { AiEnhancementMenu } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/AiEnhancementMenu';
import { enhancementType } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/sharedConstantesFiles';
import { AdminEventsTable } from '@/shared/pages/event/admin/allEvents';
import FormationElement from '@/shared/pages/formations/components/FormationElement.jsx';
import AdminQuestionSerieElementPreview from '@/shared/pages/formations/components/modal/components/AdminQuestionSerieElementPreview';
import ElementGroupsAccessManager from '@/shared/pages/formations/components/modal/components/ElementGroupsAccessManager.jsx';
import QcmDetails from '@/shared/pages/formations/components/modal/components/QcmDetails.jsx';
import QCMSelector from '@/shared/pages/formations/components/modal/components/QCMSelector.jsx';
import QuizzParameters from '@/shared/pages/formations/components/modal/components/QuizzParameters.jsx';
import SelectExerciseToDoInElement from '@/shared/pages/formations/components/modal/components/SelectExerciseToDoInElement';
import SingleEventSelector from '@/shared/pages/formations/components/modal/components/SingleEventSelector';
import SingleExamSelector from '@/shared/pages/formations/components/modal/components/SingleExamSelector';
import UserPropertySelector from '@/shared/pages/formations/components/modal/components/UserPropertySelector.jsx';
import EditDiapoSynthese from '@/shared/pages/formations/components/modal/DiapoSynthese/EditDiapoSynthese';
import { McqRecapHeader } from '@/shared/pages/qcm/components/McqRecapHeader/McqRecapHeader';
import { overrideDataFonctionFormationElementFormater } from '@/shared/services/AiEnhancementMenu';
import { ELEMENTS_TYPE, RegisterFieldsNames } from '@/shared/services/formations.js';
import { getLanguageName, tr } from '@/shared/services/translate.js';
import {
  supportedWatermarkPdfTypes,
  verifyWatermarkModuleForm,
  watermarkFormDataToObject
} from '@/shared/services/watermark';
import {
  displayDirectHtml,
  getUrlProtectedRessource,
  GlobalConfig,
  isMedisupPPS,
  showGqlErrorsInMessagePopupFromException
} from '@/shared/utils/utils.js';
import {
  AlignCenterOutlined,
  AlignLeftOutlined,
  AlignRightOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  LoadingOutlined,
  PlusOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { useMutation, useQuery } from '@apollo/client';
import {
  Alert,
  Button,
  Checkbox,
  Col,
  Divider,
  Form,
  Image,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Progress,
  Radio,
  Row,
  Select,
  Slider,
  Space,
  Switch,
  Tabs,
  Tooltip,
  Upload
} from 'antd';
import { debounce } from 'lodash';
import React, {
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from 'react';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';
import WrapperIsOnlyTextTabBig from '@/shared/pages/formations/components/modal/components/WrapperIsOnlyTextTabBig';
import { VidstackContextProvider } from '@/shared/components/Vidstack/VidstackContextProvider';
import VidstackParameterFormationElementTab from '@/shared/pages/formations/components/modal/components/VidstackParameterFormationElementTab';
import VidstackFormationElementGeneralEdit from '@/shared/pages/formations/components/modal/components/VidstackFormationElementGeneralEdit';
import { useS3Upload } from '@/shared/hooks/useS3Upload';
import { ONE_TRACK_KEY } from '@/shared/components/Vidstack/VidstackContextProvider';
import { ONE_TRACK_KEY as DIAPO_KEY } from '@/shared/components/diapoViewer/DiapoViewerTableManager';
import cloneDeep from 'lodash/cloneDeep';
import {
  BACKEND_SETTINGS_KEY,
  DiapoViewerContextProvider
} from '@/shared/components/diapoViewer/DiapoViewerContextProvider';
import DiapoViewerFormationElementGeneralEdit from '@/shared/pages/formations/components/modal/components/DiapoViewerFormationElementGeneralEdit';
import DiapoViewerFormationElementParameters from '@/shared/pages/formations/components/modal/components/DiapoViewerFormationElementParameters';
import { ElementTypeButton } from '@/shared/pages/formations/components/modal/ElementTypeButton';
import {
  ArrowDownFromLine,
  BookMarked,
  CalendarClock,
  CalendarDays,
  CandlestickChart,
  ChartNetwork,
  CircleArrowOutUpRight,
  CircleDot,
  CircleUserRound,
  Clapperboard,
  ClipboardList,
  CodeXml,
  CopyCheck,
  File,
  FileImage,
  FileUp,
  LetterText,
  LinkIcon,
  ListChecks,
  ListMinus,
  NotebookPen,
  Projector,
  SquareKanban,
  TextCursorInput,
  Type,
  Volume2,
  PackageOpen
} from 'lucide-react';
import {SCORM_BACKEND_SETTINGS_KEY, ScormContextProvider} from "@/shared/components/SCORM/ScormContextProvider";
import ScormFormationElementGeneral
  from "@/shared/pages/formations/components/modal/components/ScormFormationElementGeneral";

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE'
};

/// Constant pour les liens dynamiques
// Les flag de début et de fin de label pour dynamic remplacement
const FORMATION_ELEMENT_DYNAMIC_FLAG_START = '[[';
const FORMATION_ELEMENT_DYNAMIC_FLAG_END = ']]';
// La regex qui permet d'extraire tous les label ( ex -> inputString : 'je mange $%USERNAME%$' , doit retourner 'USERNAME')
export const FORMATION_ELEMENT_DYNAMIC_REGEX = /\[\[([^\]]+)\]\]/g;

const getMutationFromModalType = (modalType) => {
  switch (modalType) {
    case ModalType.CREATE:
      return MUTATION_CREATE_FORMATION_ELEMENT;
    case ModalType.UPDATE:
      return MUTATION_UPDATE_FORMATION_ELEMENT;
    default:
      return MUTATION_CREATE_FORMATION_ELEMENT;
  }
};

/**
 * CRUD modal for FormationElement
 */
export const CreateEditFormationElementModal = forwardRef(
  (
    {
      closeModalHandler,
      modalType,
      isModalVisible,
      formationStep = null,
      refetch,
      element,
      position = null,
      block,
      coursSupportId = null,
      eventId = null,
      challengeId = null,
      questionId = null,
      footerQuestionId = null,
      correctionSchemaLibraryId = null,
      headerMcqId = null,
      forfaitId = null,
      userPropertyFolderId = null,
      globalAnnounceType = null,
      configId = null,
      diapoSyntheseElementBeforeId = null,
      diapoSyntheseElementAfterId = null,
      enableEnhancement = false,

      disableDiapoSynthese = false,

      showContentTitle = true, // Afficher titre séparation contenu

      uploadInPublicFolder = false,

      // Display/input elements types, attention ne pas modifier les valeurs par défaut
      elementsTypesToShow = {
        // Default display elements
        [ELEMENTS_TYPE.TITLE]: true,
        [ELEMENTS_TYPE.IMAGE]: true,
        [ELEMENTS_TYPE.MCQ]: true,
        [ELEMENTS_TYPE.DO_EXERCISE]: true,
        [ELEMENTS_TYPE.LINK]: true,
        [ELEMENTS_TYPE.HTML]: true,
        [ELEMENTS_TYPE.COURS]: false, // Old full course element, disabled
        [ELEMENTS_TYPE.COURSE_SHORTCUT]: true, // Raccourci vers un cours
        [ELEMENTS_TYPE.FILE]: true,
        [ELEMENTS_TYPE.RICH_TEXT]: true,
        [ELEMENTS_TYPE.VIDEO]: false, // obsolète
        [ELEMENTS_TYPE.DIAPO]: true,
        [ELEMENTS_TYPE.CALLOUT]: true,
        // Display target form and its childs elements (input elements)
        [ELEMENTS_TYPE.FORM]: true,

        // Special user input elements for forms
        [ELEMENTS_TYPE.SHORT_ANSWER]: false,
        [ELEMENTS_TYPE.LONG_ANSWER]: false,
        [ELEMENTS_TYPE.RADIO_CHOICE_ANSWER]: false,
        [ELEMENTS_TYPE.CHECKBOXES_ANSWER]: false,
        [ELEMENTS_TYPE.FILE_IMPORT]: false,
        [ELEMENTS_TYPE.AVATAR_SELECT]: true, // Available everywhere by default

        [ELEMENTS_TYPE.SECTION]: false, // Special separator for form steps

        [ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT]: false,
        [ELEMENTS_TYPE.REGISTER_FIELD]: false, // Pour inscription uniquement

        [ELEMENTS_TYPE.BUTTON]: false, // Bouton

        [ELEMENTS_TYPE.EVENT]: false,
        [ELEMENTS_TYPE.EXAM]: false,

        [ELEMENTS_TYPE.VIDSTACK_VIDEO]: true,
        [ELEMENTS_TYPE.VIDSTACK_AUDIO]: true,

        [ELEMENTS_TYPE.DIAPO]: true,
        [ELEMENTS_TYPE.SCORM]: true,
      },

      // Controle l'affichage des bouton de update/cancel
      showButtons = true,

      // Contrôle laffichage des différents tab de parameter, styling , planification
      enableParameterTab = true,
      enableStylingTab = true,
      enablePlanificationTab = true,

      // Afficher choix entre les différents types d'éléments
      showElementChoice = true,
      preselectedElementType = null, // Si défini (ELEMENTS_TYPE) préselectionne le type d'élément

      showCancelButton = true,
      inFormation = false, // true if in formation context
      //
      quillEditorPreset
    },
    ref
  ) => {
    const { t, i18n } = useTranslation();
    const { enabledLanguages } = useContext(GlobalContext);

    const dataGroupes = useQuery(QUERY_ALL_GROUPS, { fetchPolicy: 'cache-and-network' });
    const allGroupes = dataGroupes?.data?.allGroupes;

    const editor = useRef(null);
    const [form] = Form.useForm();
    const [ElementMutation, { loading, data, error }] = useMutation(
      getMutationFromModalType(modalType)
    );
    const [DeleteElement] = useMutation(MUTATION_DELETE_FORMATION_ELEMENT);
    const [uploadTracksImage] = useMutation(MUTATION_VIDSTACK_TRACK_IMAGE_PREPROCESSING);
    const [uploadTrackFileDiapo] = useMutation(MUTATION_DIAPO_TRACK_FILE_PREPROCESSING);
    const [vidstackData, setVidstackData] = useState();
    const [diapoData, setDiapoData] = useState();
    const { uploadS3File, progress, isUploading } = useS3Upload();
    const [scormData,setScormData]=useState()

    // Check de si on est dans le cas où on affiche uniquement le text Tab. Si oui, on retire divers éléments pour améliorer la présentation
    const isOnlyTextTab = useMemo(() => {
      return !enableParameterTab && !enableStylingTab && !enablePlanificationTab;
    }, [enableParameterTab, enableStylingTab, enablePlanificationTab]);

    const {
      loading: loadingTitles,
      error: errorTitles,
      data: dataTitles,
      refetch: refetchTitles
    } = useQuery(QUERY_ALL_TITLES, { fetchPolicy: 'cache-and-network' });
    const allTitles = dataTitles?.allTitles;

    const editorRef = useRef(null);

    const [fileImage, setFileImage] = useState(null);
    const [file, setFile] = useState(null);
    const [tmpVidPreview, setTmpVidPreview] = useState(element?.text || null);
    const [isMandatory, setIsMandatory] = useState(false);

    const [selectedElementType, setSelectedElementType] = useState(element?.type || null);
    // Target object id if any
    const [selectedElementObjectId, setSelectedElementObjectId] = useState(
      element?.objectId || null
    );
    const [selectedCoursId, setSelectedCoursId] = useState(element?.coursId || null);
    const [selectedCoursName, setSelectedCoursName] = useState(null);
    const [selectedTargetFormId, setSelectedTargetFormId] = useState(element?.formId || null);
    const [selectedMcqId, setSelectedMcqId] = useState(element?.mcqId || null);
    const [selectedDoQuestionId, setSelectedDoQuestionId] = useState(element?.doQuestionId || null);
    const [selectedTitleId, setSelectedTitleId] = useState(element?.titleId || null);
    const [selectedEventId, setSelectedEventId] = useState(element?.eventId || null);
    const [selectedExamId, setSelectedExamId] = useState(element?.examId || null);
    const [targetImportedElementId, setTargetImportedElementId] = useState(null);
    const DEFAULT_EDITOR_CONTENT = {
      text: element?.text || null,
      text_en: element?.text_en || null,
      text_de: element?.text_de || null,
      text_es: element?.text_es || null,
      text_it: element?.text_it || null
    };
    const [editorContent, setEditorContent] = useState(DEFAULT_EDITOR_CONTENT);

    const [settings, setSettings] = useState({});

    const [inputEnableGroup, setInputEnableGroupAdd] = useState(false);

    const [inputEnableResponsables, setInputEnableResponsables] = useState(false);

    const [formValues, setFormValues] = useState(element);
    const [hasBorder, setHasBorder] = useState(element?.settings?.borderSize ? true : false);

    const [watermarkKey, setWatermarkKey] = useState(0);

    // Reff que l'on va associer au form afin de pouvoir avoir une action extérieur dessus, notamment la sauvegarde depuis le useImperativeHandle
    const formRef = useRef(null);

    // Fonction p
    useImperativeHandle(ref, () => ({
      saveComponant: async () => {
        await formRef?.current?.submit();
      }
    }));

    const formateTrackObject = ({ tracks = {} }) => {
      // Fonction helper qui permet de formater le trackObject de l'élément vidstack pour la gestion des images
      const KEY_MAP = {
        [ONE_TRACK_KEY.ID]: 'id',
        [ONE_TRACK_KEY.THUMBNAIL_SRC]: 'src',
        [ONE_TRACK_KEY.THUMBNAIL_STRUCTURE]: 'file'
      };

      const arrayOfKeys = Object.keys(KEY_MAP);

      // Fonction helper du formatage de trackObject pour le management des images du chapitre vidstack
      return Object.values(tracks).map((track) =>
        Object.entries(track)
          .filter(([key]) => arrayOfKeys.includes(key))
          .reduce((acc, [key, value]) => {
            const newKey = KEY_MAP[key] || key;
            acc[newKey] = value;
            return acc;
          }, {})
      );
    };

    const formateDiapoObject = ({ tracks = {} }) => {
      // Fonction de formatage de l'objet track pour l'upload via un intermédiaire qui gère les fichiers
      const KEY_MAP = {
        [DIAPO_KEY.KEY]: 'id',
        [DIAPO_KEY.BACK_NAME]: 'src',
        [DIAPO_KEY.FRONT_FILE]: 'file'
      };

      const arrayOfKeys = Object.keys(KEY_MAP);

      return Object.values(tracks).map((track) =>
        Object.entries(track)
          .filter(([key]) => arrayOfKeys.includes(key))
          .reduce((acc, [key, value]) => {
            const newKey = KEY_MAP[key] || key;
            acc[newKey] = value;
            return acc;
          }, {})
      );
    };

    useEffect(() => {
      form.resetFields();

      // Permet de présélectionner le type d'élém à la création
      if (preselectedElementType !== null) {
        setSelectedElementType(preselectedElementType);
      } else {
        setSelectedElementType(element?.type);
      }

      setSelectedMcqId(element?.mcqId || null);
      setSelectedDoQuestionId(element?.doQuestionId || null);
      setSelectedTargetFormId(element?.formId || null);
      setEditorContent(DEFAULT_EDITOR_CONTENT);
      setSelectedTitleId(element?.titleId || null);
      setHasBorder(!!element?.settings?.borderSize);

      setSelectedEventId(element?.targetEventId || null);
      setSelectedExamId(element?.targetExamId || null);
      // Set default settings

      const answers = element?.settings?.answers;
      // If some answers have groups attribute, and groups attribute is not empty
      if (answers && answers.some((a) => a?.groups && a?.groups.length > 0)) {
        setInputEnableGroupAdd(true);
      }

      // If some answers have responsible attribute, enable responsible input
      const hasResponsibleGroups =
        answers && answers.some((a) => a?.responsibleGroupIds && a?.responsibleGroupIds.length > 0);
      const hasResponsibleUsers =
        answers && answers.some((a) => a?.responsibleUserIds && a?.responsibleUserIds.length > 0);
      if (hasResponsibleGroups || hasResponsibleUsers) {
        setInputEnableResponsables(true);
      }

      /*
            var expression = /[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)?/gi;
            var regex = new RegExp(expression);
            var t = 'www.google.com';

            if (t.match(regex)) {
              alert("Successful match");
            } else {
              alert("No match");
            }
            */

      if (element?.type === ELEMENTS_TYPE.COURS) {
        setSettings(
          element?.settings || {
            showNotions: false,
            showQCMs: false,
            showRevision: false,
            showAnnales: false
          }
        );
      } else if (element?.type === ELEMENTS_TYPE.IMAGE) {
        setSettings(element?.settings);
      } else if (element?.type === ELEMENTS_TYPE.SHORT_ANSWER) {
        setSettings(element?.settings || { isMandatory: false });
      } else if (element?.type === ELEMENTS_TYPE.FILE) {
        setWatermarkKey(watermarkKey + 1); // permet lorsque l'on a plusieurs Formation Element, de recharger les watermarkSettings
        setSettings(element?.settings || {});
      } else if (element?.type === ELEMENTS_TYPE.BUTTON) {
        setSettings(element?.settings || {});
      }
    }, [element]);

    // Default input element settings
    useEffect(() => {
      if (
        [ELEMENTS_TYPE.SINGLE_SELECT, ELEMENTS_TYPE.MULTIPLE_SELECT].includes(selectedElementType)
      ) {
        // settings first settings, add first answer because it's mandatory
        setSettings(
          element?.settings || { isMandatory: false, answers: [{ text: '', isCorrect: false }] }
        );
      }
    }, [selectedElementType]);

    useEffect(() => {
      if (position) {
        setSettings({ ...settings, position });
      }
    }, [position]);

    const handleFinish = async (data) => {
      try {
        let tempData = data;
        const watermarkCheck = await verifyWatermarkModuleForm(tempData);

        if (watermarkCheck != null) {
          message.error(t(watermarkCheck));
          throw new Error('watermarkCheck failed');
        }

        // Si elementType === VIDEO_AUDIO, alors on met tout de vidstack data dans l'objet de form
        if (
          [ELEMENTS_TYPE.VIDSTACK_VIDEO, ELEMENTS_TYPE.VIDSTACK_AUDIO].includes(
            selectedElementType
          ) ||
          [ELEMENTS_TYPE.VIDSTACK_VIDEO, ELEMENTS_TYPE.VIDSTACK_AUDIO].includes(element?.type)
        ) {
          ///////// Gestion des SRC / File
          // 3 constantes clées à gérer de façon granulaire
          let src = '';
          let s3FileToUpload = vidstackData?.fileStructure;
          let s3FileName = null;

          // Upload si besoin des fichiers type MP4 / MP3
          if (s3FileToUpload) {
            const { sanitizedFilename } = await uploadS3File(s3FileToUpload?.file);
            src = '';
            s3FileName = sanitizedFilename;
            s3FileToUpload = null;
          } else if (typeof vidstackData?.src === 'string') {
            src = vidstackData?.src;
            s3FileToUpload = null;
            s3FileName = null;
          } else if (vidstackData?.s3FileName) {
            s3FileName = vidstackData?.s3FileName;
            src = '';
            s3FileToUpload = null;
          } else {
            throw new Error('error');
          }

          /////////////// Gestion des trackObject
          // formatage des tracks en qqc acceptable pour une mutation graphQl
          const prevTrack = formateTrackObject({
            tracks: element?.settings?.vidstackTrackObject
              ? JSON.parse(element?.settings?.vidstackTrackObject)
              : {}
          });
          const currentTrack = formateTrackObject({ tracks: vidstackData?.tracks });
          const uploadTemplate = cloneDeep(vidstackData?.tracks);

          const results = await uploadTracksImage({
            variables: { previousTrackImages: prevTrack, newTrackImages: currentTrack }
          });
          const { data } = results;
          const { vidstackManageChapterImages } = data;

          ///// itération sur uploadTemplate pour le process avant l'upload
          for (const [id, node] of Object.entries(uploadTemplate)) {
            node[ONE_TRACK_KEY.THUMBNAIL_STRUCTURE] = null; // reset de la structure d'upload à null no matter what => on l'aura forcément upload dans uploadTracksImage mutation
            const updateNode = vidstackManageChapterImages.find((item) => item?.id === id);
            if (updateNode) {
              node[ONE_TRACK_KEY.THUMBNAIL_SRC] = updateNode.src;
            }
          }

          //// get des settings vidStack
          const vidstackSettings = {
            vidstackTitle: vidstackData?.title,
            vidstackQuillDescription: vidstackData?.vidstackQuillDescription,
            vidstackTrackObject: JSON.stringify(uploadTemplate),
            url: src,

            vidStackAutoPlay: vidstackData?.autoPlay,
            vidStackAuthorizeSpeedModification: vidstackData?.authorizeSpeedModification,
            vidStackAuthorizeFullScreen: vidstackData?.authorizeFullScreen,
            vidStackAuthorizePictureInPicture: vidstackData?.authorizePictureInPicture,
            vidStackAuthorizeDownload: vidstackData?.authorizeDownload,
            vidStackVideoWidth: vidstackData?.videoWidth,
            vidstackEnableStatsTracking: vidstackData?.enableStatsTracking,

            vidstackClipStartTime: vidstackData?.clipStartTime,
            vidstackClipEndTime: vidstackData?.clipEndTime
          };

          // Block assez compliqué pour gérer image => Soit file, on envoit file, soit null et on supprime l'image, soit unchangé, et on envoie rien
          delete tempData?.image; // On veut délet l'image, car on s'en occupe ici, et on veut pas que le champ dans le form parasite le management
          const imageFile = vidstackData?.imageFileStructure?.file;
          const imagePath = vidstackData?.imageSrc;
          const imageBool = !!imageFile || vidstackData?.imageSrc === null;
          const imageArgument = imageFile ? imageFile : imagePath;

          tempData = {
            ...tempData,
            s3FileName,
            ...(imageBool ? { image: imageArgument } : {}),

            settings: {
              ...tempData?.settings,
              ...vidstackSettings
            }
          };
        }

        // Gestion des données du contexte
        if (selectedElementType === ELEMENTS_TYPE.DIAPO || element?.type === ELEMENTS_TYPE.DIAPO) {
          const previousTrack = element?.settings?.[BACKEND_SETTINGS_KEY.TRACK]
            ? JSON.parse(element?.settings?.[BACKEND_SETTINGS_KEY.TRACK])
            : {};
          const currentTrack = diapoData?.tracks;
          const uploadDiapoPlaceholder = cloneDeep(diapoData?.tracks);

          const fpt = formateDiapoObject({ tracks: previousTrack });
          const fct = formateDiapoObject({ tracks: currentTrack });

          // Query et récup data
          const results = await uploadTrackFileDiapo({
            variables: { previousTrackDiapo: fpt, newTrackDiapo: fct }
          });
          const { data } = results;
          const { diapoFormationElementManageTrackFile } = data;

          ///// Hydratation de uploadDiapoPlaceholder avec les données
          for (const [id, node] of Object.entries(uploadDiapoPlaceholder)) {
            // Delete des objets que l'on ne veut pas
            delete node[DIAPO_KEY.LOADING];
            delete node[DIAPO_KEY.TEMP_FILE];
            delete node[DIAPO_KEY.FRONT_FILE];

            // Hydratation par le fichier en back
            const updateNode = diapoFormationElementManageTrackFile.find((item) => item?.id === id);
            if (updateNode) {
              node[DIAPO_KEY.BACK_NAME] = updateNode.src;
            }
          }

          const diapoSettings = {
            [BACKEND_SETTINGS_KEY.TRACK]: JSON.stringify(uploadDiapoPlaceholder),
            [BACKEND_SETTINGS_KEY.TITLE]: diapoData?.title,
            [BACKEND_SETTINGS_KEY.DESCRIPTION]: diapoData?.description,
            [BACKEND_SETTINGS_KEY.AUTHORIZE_FULL_SCREEN]: diapoData?.authorizeFullScreen,
            [BACKEND_SETTINGS_KEY.AUTHORIZE_DOWNLOAD]: diapoData?.authorizeDownload,
            [BACKEND_SETTINGS_KEY.WIDTH]: diapoData?.width
          };

          tempData = {
            ...tempData,
            settings: {
              ...tempData?.settings,
              ...diapoSettings
            }
          };
        }

        const { otherData: otherData, watermarkData: watermarkSettings } =
          watermarkFormDataToObject(tempData);

        let newFormationElement;
        /* Handle files */
        newFormationElement = { ...otherData };
        if (file) {
          newFormationElement = { ...newFormationElement, file };
        }
        if (fileImage) {
          newFormationElement = { ...newFormationElement, image: fileImage, uploadInPublicFolder };
        }
        /*
        if (newFormationElement?.order) {
          newFormationElement.order = parseInt(newFormationElement.order, 10)
        }
      */
        /* Handle parent (mandatory) and target id if any */
        newFormationElement.blockId = block?.id;
        newFormationElement.formationStepId = formationStep?.id;
        newFormationElement.objectId = selectedElementObjectId;
        newFormationElement.mcqId = selectedMcqId;
        newFormationElement.doQuestionId = selectedDoQuestionId;
        newFormationElement.questionId = questionId;
        newFormationElement.footerQuestionId = footerQuestionId;
        newFormationElement.correctionSchemaLibraryId = correctionSchemaLibraryId;
        newFormationElement.headerMcqId = headerMcqId;
        newFormationElement.forfaitId = forfaitId;
        newFormationElement.configId = configId;
        newFormationElement.userPropertyFolderId = userPropertyFolderId;
        newFormationElement.settings = {
          ...settings,
          ...tempData?.settings,
          ...watermarkSettings,
          [SCORM_BACKEND_SETTINGS_KEY.SCORM_SANDBOX_MODE_SETTINGS]:scormData?.scormSandboxMode
        };
        newFormationElement.coursSupportId = coursSupportId;
        newFormationElement.eventId = eventId;
        newFormationElement.challengeId = challengeId;
        newFormationElement.titleId = selectedTitleId;
        newFormationElement.diapoSyntheseElementBeforeId = diapoSyntheseElementBeforeId;
        newFormationElement.diapoSyntheseElementAfterId = diapoSyntheseElementAfterId;
        newFormationElement.globalAnnounceType = globalAnnounceType;
        newFormationElement[SCORM_BACKEND_SETTINGS_KEY.SCORM_FILE_UPLOAD]=scormData?.[SCORM_BACKEND_SETTINGS_KEY.SCORM_FILE_UPLOAD] ?? null

        // Rich text: takes from editor
        if (selectedElementType === ELEMENTS_TYPE.RICH_TEXT) {
          newFormationElement = { ...newFormationElement, ...editorContent };
        }
        /* Handle type (mandatory) */
        newFormationElement.type = selectedElementType;
        /* Handle course ID if any */
        newFormationElement.coursId = selectedCoursId;
        /* Target form id if any */
        newFormationElement.formId = selectedTargetFormId;
        /* Target event id if any */
        newFormationElement.targetEventId = selectedEventId;
        /* Target exam id if any */
        newFormationElement.targetExamId = selectedExamId;

        let elementId = element?.id;
        if (modalType === ModalType.UPDATE) {
          await ElementMutation({ variables: { id: element?.id, input: newFormationElement } });
          message.success(t('Updated'));
        } else {
          // Create
          const result = await ElementMutation({ variables: { input: newFormationElement } });
          if (result?.data?.createFormationElement?.id) {
            elementId = result?.data?.createFormationElement?.id;
          }
          message.success(t('Created'));
        }
        form.resetFields();
        await closeModalHandler(elementId);
      } catch (e) {
        console.error(e);
        showGqlErrorsInMessagePopupFromException(e);
      }
    };

    async function deleteElement() {
      try {
        await DeleteElement({
          variables: {
            id: element?.id
          }
        });
        await closeModalHandler(element?.id, 'deleted');
      } catch (e) {
        console.error(e);
      }
    }

    const [previewImage, setPreviewImage] = useState(null);

    const beforeUpload = (file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        // Stockez les données de l'image dans le state
        setPreviewImage(e.target.result);
      };
      reader.readAsDataURL(file);

      // Votre autre logique de beforeUpload
      setFileImage(file);
      return false;
    };

    const onElementTypeClick = (elementType) => {
      if (
        selectedElementType &&
        (fileImage ||
          file ||
          selectedCoursId ||
          selectedMcqId ||
          editorContent ||
          selectedElementObjectId ||
          selectedTitleId)
      ) {
        Modal.confirm({
          content: <>{t('SureToChangeTypeElement')}</>,
          onOk: () => {
            setSelectedElementType(elementType);
          },
          onCancel: () => {
            // nothing
          }
        });
      } else {
        setSelectedElementType(elementType);
      }
    };

    const buttonProps = {
      style: {
        fontWeight: '700',
        height: '100px',
        width: '145px'
      }
    };

    const typeElementChoice = (
      <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
        {showContentTitle && <Divider>Contenu</Divider>}
        {elementsTypesToShow[ELEMENTS_TYPE.TITLE] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.TITLE)}
            icon={<Type />}
            selected={selectedElementType === ELEMENTS_TYPE.TITLE}
            label={t('Title')}
          />
        )}
        {elementsTypesToShow[ELEMENTS_TYPE.COURSE_SHORTCUT] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.COURSE_SHORTCUT)}
            icon={<BookMarked />}
            selected={selectedElementType === ELEMENTS_TYPE.COURSE_SHORTCUT}
            label={t('Course(Shortcut)')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.IMAGE] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.IMAGE)}
            icon={<FileImage />}
            selected={selectedElementType === ELEMENTS_TYPE.IMAGE}
            label={t('Image')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.RICH_TEXT] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.RICH_TEXT)}
            icon={<LetterText />}
            selected={selectedElementType === ELEMENTS_TYPE.RICH_TEXT}
            label={t('Text')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.CALLOUT] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.CALLOUT)}
            icon={<SquareKanban style={{ rotate: '-90deg' }} />}
            selected={selectedElementType === ELEMENTS_TYPE.CALLOUT}
            label={t('Callout')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.FORM] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.FORM)}
            icon={<NotebookPen />}
            selected={selectedElementType === ELEMENTS_TYPE.FORM}
            label={t('Form')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.LINK] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.LINK)}
            icon={<LinkIcon />}
            selected={selectedElementType === ELEMENTS_TYPE.LINK}
            label={t('Link')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.FILE] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.FILE)}
            icon={<File />}
            selected={selectedElementType === ELEMENTS_TYPE.FILE}
            label={t('File')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.BUTTON] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.BUTTON)}
            icon={<CircleArrowOutUpRight />}
            selected={selectedElementType === ELEMENTS_TYPE.BUTTON}
            label={t('Button')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.HTML] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.HTML)}
            icon={<CodeXml />}
            selected={selectedElementType === ELEMENTS_TYPE.HTML}
            label={t('HTML')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.VIDEO] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.VIDEO)}
            icon={<Clapperboard />}
            selected={selectedElementType === ELEMENTS_TYPE.VIDEO}
            label={t('Video')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.MCQ] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.MCQ)}
            icon={<CandlestickChart />}
            selected={selectedElementType === ELEMENTS_TYPE.MCQ}
            label={t('ExoteachQuizz')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.DO_EXERCISE] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.DO_EXERCISE)}
            icon={<ChartNetwork />}
            selected={selectedElementType === ELEMENTS_TYPE.DO_EXERCISE}
            label={t('Exercice')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.DIAPO] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.DIAPO)}
            icon={<Projector />}
            selected={selectedElementType === ELEMENTS_TYPE.DIAPO}
            label={t('DiapoFE.DiapoFEBigButtonLabel')}
          />
        )}

        {isMedisupPPS && !disableDiapoSynthese && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.DIAPO_SYNTHESE)}
            icon={<ListChecks />}
            selected={selectedElementType === ELEMENTS_TYPE.DIAPO_SYNTHESE}
            label={t('DDS.DDS')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.VIDSTACK_VIDEO] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.VIDSTACK_VIDEO)}
            icon={<Clapperboard />}
            selected={selectedElementType === ELEMENTS_TYPE.VIDSTACK_VIDEO}
            label={t('VideoFE.FormationElementVideoLabel')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.VIDSTACK_AUDIO] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.VIDSTACK_AUDIO)}
            icon={<Volume2 />}
            selected={selectedElementType === ELEMENTS_TYPE.VIDSTACK_AUDIO}
            label={t('VideoFE.FormationElementAudioLabel')}
          />
        )}
        {
          elementsTypesToShow[ELEMENTS_TYPE.SCORM] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.SCORM)}
            icon={<PackageOpen/>}

            selected={selectedElementType === ELEMENTS_TYPE.SCORM}
            label={t('ScormFE.ScormFeButtonLabel')}
          />
        )}


        {[
          ELEMENTS_TYPE.SHORT_ANSWER,
          ELEMENTS_TYPE.LONG_ANSWER,
          ELEMENTS_TYPE.SINGLE_SELECT,
          ELEMENTS_TYPE.MULTIPLE_SELECT,
          ELEMENTS_TYPE.FILE_IMPORT,
          ELEMENTS_TYPE.AVATAR_SELECT,
          ELEMENTS_TYPE.DATE_PICKER,
          ELEMENTS_TYPE.DATE_AND_TIME_PICKER,
          ELEMENTS_TYPE.SECTION,
          ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT
        ].some((type) => elementsTypesToShow[type]) && (
          <Divider> Champs à remplir par l’utilisateur</Divider>
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.SHORT_ANSWER] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.SHORT_ANSWER)}
            icon={<TextCursorInput />}
            selected={selectedElementType === ELEMENTS_TYPE.SHORT_ANSWER}
            label={t('ShortAnswer')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.LONG_ANSWER] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.LONG_ANSWER)}
            icon={<ListMinus />}
            selected={selectedElementType === ELEMENTS_TYPE.LONG_ANSWER}
            label={t('LongAnswer')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.SINGLE_SELECT] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.SINGLE_SELECT)}
            icon={<CircleDot />}
            selected={selectedElementType === ELEMENTS_TYPE.SINGLE_SELECT}
            label={t('UniqueChoice')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.MULTIPLE_SELECT] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.MULTIPLE_SELECT)}
            icon={<CopyCheck />}
            selected={selectedElementType === ELEMENTS_TYPE.MULTIPLE_SELECT}
            label={t('MultipleChoice')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.FILE_IMPORT] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.FILE_IMPORT)}
            icon={<FileUp />}
            selected={selectedElementType === ELEMENTS_TYPE.FILE_IMPORT}
            label={t('FileAttachment')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.AVATAR_SELECT] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.AVATAR_SELECT)}
            icon={<CircleUserRound />}
            selected={selectedElementType === ELEMENTS_TYPE.AVATAR_SELECT}
            label={t('ChooseAnAvatar')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.DATE_PICKER] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.DATE_PICKER)}
            icon={<CalendarDays />}
            selected={selectedElementType === ELEMENTS_TYPE.DATE_PICKER}
            label={t('DatePicker')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.DATE_AND_TIME_PICKER] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.DATE_AND_TIME_PICKER)}
            icon={<CalendarClock />}
            selected={selectedElementType === ELEMENTS_TYPE.DATE_AND_TIME_PICKER}
            label={t('DateAndTimePicker')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.SECTION] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.SECTION)}
            icon={<ArrowDownFromLine />}
            selected={selectedElementType === ELEMENTS_TYPE.SECTION}
            label={t('Section')}
          />
        )}

        {elementsTypesToShow[ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT] && (
          <ElementTypeButton
            onClick={() => onElementTypeClick(ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT)}
            icon={<ClipboardList />}
            selected={[
              ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT,
              ELEMENTS_TYPE.REGISTER_FIELD
            ].includes(selectedElementType)}
            label={t('PredefinedUserField')}
          />
        )}
      </div>
    );

    const parametersTab = enableParameterTab && (
      <Tabs.TabPane tab={t('Visibility')} key="2">
        <ElementGroupsAccessManager element={element} />
      </Tabs.TabPane>
    );
    const stylingTab = enableStylingTab && (
      <Tabs.TabPane tab={t('Style')} key="3">
        <Divider>{t('Border')}</Divider>
        <Checkbox checked={hasBorder} onChange={() => setHasBorder(!hasBorder)}>
          Bordure autour du bloc
        </Checkbox>

        {hasBorder && (
          <>
            <Form.Item name={['settings', 'borderColor']} label="Couleur bordure">
              <input type="color" />
            </Form.Item>
            <Form.Item name={['settings', 'borderSize']} label="Épaisseur (en pixel)">
              <Input type="number" suffix="px" placeholder="1" />
            </Form.Item>
            <Form.Item
              name={['settings', 'borderRadius']}
              label="Radius (si bords arrondis, en pixel)"
            >
              <Input type="number" suffix="px" placeholder="1" />
            </Form.Item>
          </>
        )}
        <Divider>Couleur de fond</Divider>
        <Form.Item
          name={['settings', 'backgroundColor']}
          label="Couleur de fond du bloc, hors titre"
        >
          <input type="color" />
        </Form.Item>

        <Divider>Marge</Divider>
        <Form.Item
          name={['settings', 'margin']}
          label="Marge en pixel autour de l'élément."
          help="Mettre 0 pour aucune marge. 5 par défaut"
        >
          <Input type="number" placeholder="5" suffix="px" />
        </Form.Item>

        <Divider />
      </Tabs.TabPane>
    );

    const planificationTab = enablePlanificationTab && (
      <Tabs.TabPane tab={t('Planification.Planification')} key="4">
        <PlanificationTab element={element} />
      </Tabs.TabPane>
    );

    const debouncedSave = useCallback(
      debounce((nextValue) => setFormValues(nextValue), 100),
      [] // will be created only once initially
    );

    /* input elements */
    const onAddOptionSelect = () => {
      setSettings({
        ...settings,
        answers: [
          ...settings?.answers,
          {
            text: '',
            isCorrect: false
          }
        ]
      });
    };
    const onChangeOptionSelect = (index, key, value) => {
      try {
        // Create a new copy of the answers array and the answer object
        const newAnswers = settings?.answers?.map((answer, i) =>
          i === index ? { ...answer, [key]: value } : answer
        );
        // Update the state with the new answers array
        setSettings({
          ...settings,
          answers: newAnswers
        });
      } catch (e) {
        message.error(e);
        console.error(e);
      }
    };
    const onRemoveOptionSelect = (index) => {
      let newAnswers = [...settings?.answers];
      newAnswers.splice(index, 1);
      setSettings({
        ...settings,
        answers: newAnswers
      });
    };

    const hubspotPropertyEditor = (
      <>
        <Form.Item name={['settings', 'hubspotProperty']} label={'ID propriété Hubspot'}>
          <Input />
        </Form.Item>
      </>
    );
    {
      /*
  <Tabs defaultActiveKey={'1'}>
    <Tabs.TabPane tab={t('General')} key="1">

    </Tabs.TabPane>

    <Tabs.TabPane tab={t('Settings')} key="2">

    </Tabs.TabPane>
  </Tabs>
  */
    }

    const inputElementsTypesEdition = (
      <>
        {selectedElementType === ELEMENTS_TYPE.SHORT_ANSWER && (
          <>
            <Tabs defaultActiveKey={'1'}>
              <Tabs.TabPane tab={t('General')} key="1">
                <Tabs defaultActiveKey={i18n.language}>
                  {enabledLanguages &&
                    enabledLanguages?.map((lang) => (
                      <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                        <Form.Item name={tr('name', lang)} label={t('Question')}>
                          <Input type="text" placeholder={t('Title')} />
                        </Form.Item>
                      </Tabs.TabPane>
                    ))}
                </Tabs>
                <br />
                <Form.Item name={['settings', 'isMandatory']} valuePropName="checked">
                  <Checkbox>Obligatoire</Checkbox>
                </Form.Item>
              </Tabs.TabPane>
              <Tabs.TabPane tab={t('Settings')} key="2">
                {hubspotPropertyEditor}
              </Tabs.TabPane>
            </Tabs>
          </>
        )}

        {selectedElementType === ELEMENTS_TYPE.LONG_ANSWER && (
          <>
            <Tabs defaultActiveKey={'1'}>
              <Tabs.TabPane tab={t('General')} key="1">
                <Tabs defaultActiveKey={i18n.language}>
                  {enabledLanguages &&
                    enabledLanguages?.map((lang) => (
                      <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                        <Form.Item name={tr('name', lang)} label={t('Question')}>
                          <Input.TextArea rows={3} type="text" placeholder={t('Title')} />
                        </Form.Item>
                      </Tabs.TabPane>
                    ))}
                </Tabs>
                <br />
                <Form.Item name={['settings', 'isMandatory']} valuePropName="checked">
                  <Checkbox>Obligatoire</Checkbox>
                </Form.Item>
              </Tabs.TabPane>

              <Tabs.TabPane tab={t('Settings')} key="2">
                {hubspotPropertyEditor}
              </Tabs.TabPane>
            </Tabs>
          </>
        )}

        {[ELEMENTS_TYPE.MULTIPLE_SELECT, ELEMENTS_TYPE.SINGLE_SELECT].includes(
          selectedElementType
        ) && (
          <>
            <Tabs defaultActiveKey={'1'}>
              <Tabs.TabPane tab={t('General')} key="1">
                <Tabs defaultActiveKey={i18n.language}>
                  {enabledLanguages &&
                    enabledLanguages?.map((lang) => (
                      <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                        <Form.Item name={tr('name', lang)} label={t('Question')}>
                          <Input size="large" type="text" placeholder={t('Title')} />
                        </Form.Item>
                      </Tabs.TabPane>
                    ))}
                </Tabs>

                <div style={{ marginLeft: 12 }}>
                  {settings?.answers &&
                    settings?.answers?.map((answer, index) => (
                      <div key={index}>
                        <Input.Group compact>
                          <Form.Item
                            label={`${t('Option')} ${index + 1}`}
                            style={{ marginRight: '10px' }}
                          >
                            <Input
                              type="text"
                              placeholder={`${t('Option')} ${index + 1}`}
                              value={answer?.text}
                              onChange={(e) =>
                                onChangeOptionSelect(index, 'text', e?.target?.value)
                              }
                            />
                          </Form.Item>

                          {/* Groupes ajoutés si option sélectionnée */}
                          {inputEnableGroup && (
                            <Form.Item
                              style={{ marginLeft: '10px' }}
                              label="Groupe(s) ajouté si cette réponse est sélectionnée"
                            >
                              <AbstractGroupsManager
                                entityName={''}
                                entityId={element?.id}
                                groupes={allGroupes?.filter((g) =>
                                  answer?.groups?.includes(parseInt(g?.id))
                                )}
                                groupParameterName="groupId"
                                entityParameterName="elementId"
                                enableFolderChecking={false}
                                onChange={(shouldAdd, groupId) => {
                                  if (shouldAdd) {
                                    const currentAnswerGroupIds = answer?.groups || [];
                                    onChangeOptionSelect(index, 'groups', [
                                      ...currentAnswerGroupIds,
                                      groupId
                                    ]);
                                  } else {
                                    onChangeOptionSelect(
                                      index,
                                      'groups',
                                      answer?.groups?.filter((g) => g !== groupId)
                                    );
                                  }
                                }}
                              />
                            </Form.Item>
                          )}

                          {/* Responsables ajoutés si option sélectionnée */}
                          {inputEnableResponsables && (
                            <Form.Item
                              style={{ marginLeft: '10px' }}
                              label="Responsables ajoutés si cette réponse est sélectionnée"
                            >
                              <AbstractGroupsManager
                                entityName={''}
                                entityId={element?.id}
                                groupes={allGroupes?.filter((g) =>
                                  answer?.responsibleGroupIds?.includes(parseInt(g?.id))
                                )}
                                groupParameterName="groupId"
                                entityParameterName="elementId"
                                enableFolderChecking={false}
                                onChange={(shouldAdd, groupId) => {
                                  if (shouldAdd) {
                                    const currentAnswerGroupIds = answer?.responsibleGroupIds || [];
                                    onChangeOptionSelect(index, 'responsibleGroupIds', [
                                      ...currentAnswerGroupIds,
                                      groupId
                                    ]);
                                  } else {
                                    onChangeOptionSelect(
                                      index,
                                      'responsibleGroupIds',
                                      answer?.responsibleGroupIds?.filter((g) => g !== groupId)
                                    );
                                  }
                                }}
                              />

                              <Divider />

                              {/* Groupes individuels */}
                              <IndividualPermissionsManager
                                showText={false}
                                individualGroups={answer?.responsibleUserIds} // TODO fetch individual groups
                                onAdd={async (individualGroupId, userId) => {
                                  const obj = {
                                    id: individualGroupId,
                                    name: userId,
                                    isIndividual: true
                                  };
                                  //setIndividualGroups([...individualGroups, obj]);

                                  const currentAnswerGroupIds = answer?.responsibleUserIds || [];
                                  onChangeOptionSelect(index, 'responsibleUserIds', [
                                    ...currentAnswerGroupIds,
                                    obj
                                  ]);
                                }}
                                onRemove={async (individualGroupId) => {
                                  onChangeOptionSelect(
                                    index,
                                    'responsibleUserIds',
                                    answer?.responsibleUserIds?.filter(
                                      (g) => g !== individualGroupId
                                    )
                                  );
                                }}
                              />
                            </Form.Item>
                          )}

                          {index > 0 && (
                            <Form.Item style={{ marginLeft: '10px', marginTop: '30px' }}>
                              <Button
                                shape="circle"
                                icon={<DeleteOutlined />}
                                danger
                                onClick={() => onRemoveOptionSelect(index)}
                              />
                            </Form.Item>
                          )}
                        </Input.Group>
                      </div>
                    ))}

                  {/* Add button */}
                  <Button
                    onClick={onAddOptionSelect}
                    icon={<PlusOutlined />}
                    style={{ margin: '10px 0' }}
                  >
                    {t('AddOption')}
                  </Button>

                  <Form.Item label={'Ajouter des groupes selon le choix'}>
                    <Switch
                      checkedChildren="Oui"
                      unCheckedChildren="Non"
                      defaultChecked={false}
                      checked={inputEnableGroup}
                      onChange={(checked) => {
                        setInputEnableGroupAdd(checked);
                      }}
                    />
                  </Form.Item>

                  <Form.Item label={'Ajouter des responsables selon le choix'}>
                    <Switch
                      checkedChildren="Oui"
                      unCheckedChildren="Non"
                      defaultChecked={false}
                      checked={inputEnableResponsables}
                      onChange={(checked) => {
                        setInputEnableResponsables(checked);
                      }}
                    />
                  </Form.Item>
                </div>
                <br />
                <Form.Item name={['settings', 'isMandatory']} valuePropName="checked">
                  <Checkbox>Obligatoire</Checkbox>
                </Form.Item>
              </Tabs.TabPane>

              <Tabs.TabPane tab={t('Settings')} key="2">
                {hubspotPropertyEditor}
              </Tabs.TabPane>
            </Tabs>
          </>
        )}

        {selectedElementType === ELEMENTS_TYPE.FILE_IMPORT && (
          <>
            <Tabs defaultActiveKey={i18n.language}>
              {enabledLanguages &&
                enabledLanguages?.map((lang) => (
                  <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                    <Form.Item name={tr('name', lang)} label={t('Question')}>
                      <Input type="text" placeholder={t('Title')} />
                    </Form.Item>
                  </Tabs.TabPane>
                ))}
            </Tabs>

            {/* TODO create/edit/delete options */}
            {/*
          <Form.Item
            name={['settings', 'fileSizeMaxMB']}
            label="Taille max. du fichier acceptée, en Mo"
          >
            <InputNumber min={2} max={100} defaultValue={2}/>
          </Form.Item>

          <Form.Item
            name={['settings', 'fileType']}
            label="Types de fichier acceptés"
          >
            <Checkbox.Group defaultValue="left" buttonStyle="solid">
              <Checkbox value=".pdf">Fichier pdf</Checkbox>
              <Checkbox value=".zip">Fichier zip</Checkbox>
              <Checkbox value="audio">Audio</Checkbox>
              <Checkbox value="video">Vidéo</Checkbox>
              <Checkbox value="image">Image</Checkbox>
              <Checkbox value={null}>Tout</Checkbox>
            </Checkbox.Group>
          </Form.Item>
          */}

            <br />
            <Form.Item name={['settings', 'isMandatory']} valuePropName="checked">
              <Checkbox>Obligatoire</Checkbox>
            </Form.Item>
          </>
        )}

        {selectedElementType === ELEMENTS_TYPE.DATE_PICKER && (
          <>
            <Tabs defaultActiveKey={'1'}>
              <Tabs.TabPane tab={t('General')} key="1">
                <Tabs defaultActiveKey={i18n.language}>
                  {enabledLanguages &&
                    enabledLanguages?.map((lang) => (
                      <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                        <Form.Item name={tr('name', lang)} label={t('Question')}>
                          <Input type="text" placeholder={t('Title')} />
                        </Form.Item>
                      </Tabs.TabPane>
                    ))}
                </Tabs>

                <br />
                <Form.Item name={['settings', 'isMandatory']} valuePropName="checked">
                  <Checkbox>Obligatoire</Checkbox>
                </Form.Item>
              </Tabs.TabPane>

              <Tabs.TabPane tab={t('Settings')} key="2">
                {hubspotPropertyEditor}
              </Tabs.TabPane>
            </Tabs>
          </>
        )}

        {selectedElementType === ELEMENTS_TYPE.AVATAR_SELECT && (
          <>
            <p>
              Permettra à l'utilisateur de choisir un avatar parmi une liste d'avatars prédéfinis.
            </p>

            <Form.Item name={['settings', 'textAlign']} label="Alignement">
              <Radio.Group defaultValue="left" buttonStyle="solid">
                <Radio.Button value="left">
                  <AlignLeftOutlined />
                </Radio.Button>
                <Radio.Button value="center">
                  <AlignCenterOutlined />
                </Radio.Button>
                <Radio.Button value="right">
                  <AlignRightOutlined />
                </Radio.Button>
              </Radio.Group>
            </Form.Item>

            <br />
          </>
        )}

        {selectedElementType === ELEMENTS_TYPE.DATE_AND_TIME_PICKER && (
          <>
            <Tabs defaultActiveKey={'1'}>
              <Tabs.TabPane tab={t('General')} key="1">
                <Tabs defaultActiveKey={i18n.language}>
                  {enabledLanguages &&
                    enabledLanguages?.map((lang) => (
                      <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                        <Form.Item name={tr('name', lang)} label={t('Question')}>
                          <Input type="text" placeholder={t('Title')} />
                        </Form.Item>
                      </Tabs.TabPane>
                    ))}
                </Tabs>
                <br />
                <Form.Item name={['settings', 'isMandatory']} valuePropName="checked">
                  <Checkbox>Obligatoire</Checkbox>
                </Form.Item>
              </Tabs.TabPane>

              <Tabs.TabPane tab={t('Settings')} key="2">
                {hubspotPropertyEditor}
              </Tabs.TabPane>
            </Tabs>
          </>
        )}

        {selectedElementType === ELEMENTS_TYPE.SECTION && (
          <>
            <Alert
              message="Début de section"
              description="Cet élément marque le début d'une section. Permet de séparer le contenu en plusieurs sections. Le contenu de la section suivante ne sera visible que lorsque l'utilisateur aura cliqué sur le bouton 'Suivant'."
              type="info"
              showIcon
            />

            <Tabs defaultActiveKey={i18n.language}>
              {enabledLanguages &&
                enabledLanguages?.map((lang) => (
                  <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                    <Form.Item name={tr('name', lang)} label={t('Name')}>
                      <Input type="text" placeholder={t('Title')} />
                    </Form.Item>
                  </Tabs.TabPane>
                ))}
            </Tabs>
          </>
        )}

        {[ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT, ELEMENTS_TYPE.REGISTER_FIELD].includes(
          selectedElementType
        ) && (
          <>
            <Alert
              message="Importer un champ de propriété utilisateur existant"
              description={
                <>
                  Choisissez un champ de propriété utilisateur parmi ceux existant. <br />
                  <a
                    href={'#/admin/users/users-properties'}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Pour créer ou modifier des champs existants, cliquez ici
                  </a>
                </>
              }
              type="info"
              showIcon
            />

            <br />
            <UserPropertySelector
              onSelect={(selected, type) => {
                if (type === 'registerField') {
                  setSettings({ ...settings, type: selected });
                  setSelectedElementType(ELEMENTS_TYPE.REGISTER_FIELD);
                } else {
                  // Target element
                  setSelectedElementObjectId(selected);
                  setSelectedElementType(ELEMENTS_TYPE.IMPORTED_PREDEFINED_INPUT);
                }
              }}
            />
            <br />

            {selectedElementType === ELEMENTS_TYPE.REGISTER_FIELD && (
              <Form.Item name={['settings', 'isMandatory']} valuePropName="checked">
                <Checkbox>Obligatoire</Checkbox>
              </Form.Item>
            )}
          </>
        )}
      </>
    );

    return (
      <div style={{ visibility: isModalVisible ? 'visible' : 'hidden' }}>
        {/* Show small error(s) if needed */}
        <SmallErrorsAlert error={error} loading={loading} />

        {/* show le modal de loading si on est en train d'upload */}
        <Modal
          open={isUploading}
          footer={null}
          closable={false}
          centered
          title={t('VideoFE.VideoFEModalUploadWaitingLabel')}
        >
          <p>{t('VideoFE.VideoFEModalUploadWaitingExplanation')}</p>
          <Progress percent={progress} status="active" />
        </Modal>

        {showElementChoice && modalType === ModalType.CREATE && (
          <div>
            <h5>
              {t('ElementTypeToInsert')}{' '}
              <Button
                type="text"
                size="small"
                onClick={closeModalHandler}
                icon={<CloseCircleOutlined />}
              />
            </h5>
            {typeElementChoice}
          </div>
        )}

        {selectedElementType && (
          <>
            <Form
              id="formationElementForm"
              layout="vertical"
              onFinish={handleFinish}
              onFinishFailed={(errorInfo) => {
                console.error('onFinishFailed');
                console.error(errorInfo);
              }}
              ref={formRef}
              form={form}
              onValuesChange={async (values) => {
                debouncedSave({ ...formValues, ...values });
                // setFormValues({ ...formValues, ...values }); // real form values
              }}
              initialValues={modalType === ModalType.UPDATE ? element : {}}
            >
              {selectedElementType === ELEMENTS_TYPE.TITLE && (
                <>
                  <Form.Item label={t('TypeOfTitle')}>
                    <Select
                      defaultValue={selectedTitleId}
                      size="large"
                      onChange={(value, item) => {
                        setSelectedTitleId(value);
                      }}
                    >
                      {allTitles?.map((title) => (
                        <Select.Option key={title?.id} value={title?.id}>
                          <Title
                            title={title}
                            element={{ name: title?.name }}
                            showDescription={false}
                            noMargin
                          />
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Tabs defaultActiveKey={i18n.language}>
                    {enabledLanguages &&
                      enabledLanguages?.map((lang) => (
                        <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                          <Form.Item name={tr('name', lang)} label={t('ElementTitle')}>
                            <Input type="text" placeholder="Titre" />
                          </Form.Item>
                          <Form.Item name={tr('description', lang)} label={t('Description')}>
                            <Input type="text" placeholder="Description (optionnel)" />
                          </Form.Item>
                        </Tabs.TabPane>
                      ))}
                  </Tabs>

                  <Form.Item name={['settings', 'textAlign']} label="Alignement">
                    <Radio.Group defaultValue="left" buttonStyle="solid">
                      <Radio.Button value="left">
                        <AlignLeftOutlined />
                      </Radio.Button>
                      <Radio.Button value="center">
                        <AlignCenterOutlined />
                      </Radio.Button>
                      <Radio.Button value="right">
                        <AlignRightOutlined />
                      </Radio.Button>
                    </Radio.Group>
                  </Form.Item>

                  <Divider>Marge</Divider>
                  <Form.Item
                    name={['settings', 'margin']}
                    label="Marge en pixel autour de l'élément."
                    help="Mettre 0 pour aucune marge. 5 par défaut"
                  >
                    <Input type="number" placeholder="5" suffix="px" />
                  </Form.Item>

                  <br />
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.RICH_TEXT && (
                <>
                  <WrapperIsOnlyTextTabBig
                    isOnlyTextTab={isOnlyTextTab}
                    parametersTab={parametersTab}
                    planificationTab={planificationTab}
                    stylingTab={stylingTab}
                  >
                    <Tabs defaultActiveKey={i18n.language}>
                      {enabledLanguages &&
                        enabledLanguages?.map((lang) => (
                          <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                              {enableEnhancement && (
                                <AiEnhancementMenu
                                  style={{
                                    marginTop: -56,
                                    marginBottom: 24,
                                    alignSelf: 'flex-end'
                                  }}
                                  secondary
                                  componantType={enhancementType.FE_TEXT}
                                  refetch={() => {}}
                                  getTextFct={() =>
                                    overrideDataFonctionFormationElementFormater(
                                      editorContent,
                                      setEditorContent,
                                      'text',
                                      lang,
                                      element?.id || 1
                                    )
                                  }
                                />
                              )}
                              <Form.Item>
                                <ExoQuillEditor
                                  defaultValue={editorContent[tr('text', lang)]}
                                  onChange={(editorData) => {
                                    setEditorContent({
                                      ...editorContent,
                                      [tr('text', lang)]: editorData
                                    });
                                  }}
                                  modules={ExoQuillToolbarPresets.globalAnnounce}
                                  quillEditorPreset={quillEditorPreset}
                                />
                              </Form.Item>
                            </div>
                          </Tabs.TabPane>
                        ))}
                    </Tabs>
                  </WrapperIsOnlyTextTabBig>
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.BUTTON && (
                <>
                  {/* texte size type */}

                  <Tabs defaultActiveKey={i18n.language}>
                    {enabledLanguages &&
                      enabledLanguages?.map((lang) => (
                        <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                          <Form.Item name={tr('name', lang)} label={t('ElementTitle')}>
                            <Input type="text" placeholder="Titre" />
                          </Form.Item>
                        </Tabs.TabPane>
                      ))}
                  </Tabs>

                  <Form.Item name={['settings', 'block']} valuePropName="checked">
                    <Checkbox>Block</Checkbox>
                  </Form.Item>

                  <Form.Item name={['settings', 'backgroundColor']} label="Couleur de fond">
                    <input type="color" />
                  </Form.Item>

                  <Form.Item name={['settings', 'size']} label="Taille">
                    <Select>
                      <Select.Option value="small">Petit</Select.Option>
                      <Select.Option value="middle">Moyen</Select.Option>
                      <Select.Option value="large">Grand</Select.Option>
                    </Select>
                  </Form.Item>

                  <Form.Item name={['settings', 'type']} label="Type">
                    <Select>
                      <Select.Option value="default">Default</Select.Option>
                      <Select.Option value="primary">Primary</Select.Option>
                      <Select.Option value="dashed">Dashed</Select.Option>
                      <Select.Option value="link">Link</Select.Option>
                      <Select.Option value="text">Text</Select.Option>
                    </Select>
                  </Form.Item>

                  {/* targetSelectPlatform */}
                  <Form.Item label="Cible" name={['settings', 'target']}>
                    <Radio.Group>
                      <Radio.Button value="link">Lien</Radio.Button>
                      <Radio.Button value="platform">Platforme</Radio.Button>
                      <Radio.Button value="inscription">Page inscription</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                  {form.getFieldValue(['settings', 'target']) === 'link' && (
                    <Form.Item
                      label="Lien"
                      name={['settings', 'link']}
                      rules={[
                        {
                          required: true,
                          message: 'Veuillez entrer un lien'
                        },
                        {
                          pattern: new RegExp('^https://'),
                          message: 'Le lien doit commencer par "https://"'
                        }
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  )}
                  {form.getFieldValue(['settings', 'target']) === 'platform' && (
                    <Form.Item label="Platforme" name={['settings', 'platformId']}>
                      <InputNumber />
                    </Form.Item>
                  )}
                </>
              )}

              {/* Encadré */}
              {selectedElementType === ELEMENTS_TYPE.CALLOUT && (
                <>
                  <Tabs defaultActiveKey={i18n.language}>
                    {enabledLanguages &&
                      enabledLanguages?.map((lang) => (
                        <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                          <Form.Item name={tr('name', lang)} label={t('Text')}>
                            <Input.TextArea type="text" placeholder={t('Title')} />
                          </Form.Item>
                          <Form.Item name={tr('description', lang)} label={t('Description')}>
                            <Input.TextArea type="text" placeholder={t('Description(Optionnal)')} />
                          </Form.Item>
                        </Tabs.TabPane>
                      ))}
                  </Tabs>

                  <Form.Item name="text" label={t('general.type')}>
                    <Radio.Group defaultValue="info" buttonStyle="solid">
                      <Radio.Button value="success">{t('Success')}</Radio.Button>
                      <Radio.Button value="info">{t('Info')}</Radio.Button>
                      <Radio.Button value="warning">{t('Warning')}</Radio.Button>
                      <Radio.Button value="error">{t('Error')}</Radio.Button>
                    </Radio.Group>
                  </Form.Item>

                  <Divider />

                  <Form.Item label={t('Preview')}>
                    <Tabs defaultActiveKey={i18n.language}>
                      {enabledLanguages &&
                        enabledLanguages?.map((lang) => (
                          <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                            <FormationElement
                              element={{
                                type: ELEMENTS_TYPE.CALLOUT,
                                name: form.getFieldValue(tr('name', lang)),
                                description: form.getFieldValue(tr('description', lang)),
                                text: form.getFieldValue('text')
                              }}
                            />
                          </Tabs.TabPane>
                        ))}
                    </Tabs>
                  </Form.Item>
                </>
              )}

              {/* FORM : formulaire cible */}
              {selectedElementType === ELEMENTS_TYPE.FORM && (
                <>
                  <h5>{t('SelectFormToShow')}</h5>

                  <FormSelector
                    selectedFormId={selectedTargetFormId}
                    onFormSelected={(selectedFormId) => {
                      setSelectedTargetFormId(selectedFormId);
                    }}
                  />

                  <Divider />
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.IMAGE && (
                <>
                  <Tabs defaultActiveKey="1" type="card">
                    <Tabs.TabPane tab={t('Content')} key="1">
                      {element?.image && (
                        <div style={{ textAlign: 'center' }}>
                          <Image
                            onError={(e) => {
                              e.target.style = 'display: none';
                            }}
                            style={{ maxWidth: settings?.imageWidth || 'auto', height: 'auto' }}
                            src={getUrlProtectedRessource(
                              GlobalConfig.get().FILES_URL + element?.image
                            )}
                            alt="image"
                          />
                          {element?.description && (
                            <div style={{ color: '#666666', marginTop: '5px' }}>
                              {element?.description}
                            </div>
                          )}
                        </div>
                      )}
                      <br />
                      <Form.Item label={t('Image')} extra={!fileImage && formationStep?.image}>
                        <Upload.Dragger
                          name="image"
                          listType="picture"
                          showUploadList
                          accept="image/*"
                          beforeUpload={beforeUpload}
                          fileList={fileImage ? [fileImage] : []}
                          onRemove={() => {
                            setFileImage('');
                            setPreviewImage(null); // Effacer l'aperçu
                          }}
                        >
                          {previewImage ? (
                            <img
                              src={previewImage}
                              alt="Aperçu"
                              style={{ maxWidth: settings?.imageWidth || '100%', height: 'auto' }}
                            />
                          ) : (
                            <div>
                              {loading ? <LoadingOutlined /> : <PlusOutlined />}
                              <div className="ant-upload-text">{t('general.upload')}</div>
                            </div>
                          )}
                        </Upload.Dragger>
                      </Form.Item>

                      <Form.Item label={t('ImageWidth')}>
                        <Row>
                          <Col span={12}>
                            <Slider
                              min={1}
                              max={1800}
                              onChange={(val) => {
                                setSettings({ ...settings, imageWidth: val });
                              }}
                              value={
                                typeof settings?.imageWidth === 'number' ? settings?.imageWidth : 0
                              }
                            />
                          </Col>
                          <Col span={4}>
                            <InputNumber
                              min={1}
                              max={1800}
                              style={{ margin: '0 16px' }}
                              value={settings?.imageWidth}
                              onChange={(val) => setSettings({ ...settings, imageWidth: val })}
                            />
                          </Col>
                        </Row>
                      </Form.Item>

                      <Form.Item name={['settings', 'notClickable']} valuePropName={'checked'}>
                        <Checkbox>Image non-cliquable</Checkbox>
                      </Form.Item>

                      <Tabs defaultActiveKey={i18n.language}>
                        {enabledLanguages &&
                          enabledLanguages?.map((lang) => (
                            <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                              <Form.Item name={tr('description', lang)} label={t('ImageLegend')}>
                                <Input type="text" placeholder={t('ImageLegend')} />
                              </Form.Item>
                            </Tabs.TabPane>
                          ))}
                      </Tabs>

                      <Form.Item name="gptPrecisionPrompt" label={t('PromptPrecision')}>
                        <Input.TextArea rows={2} />
                      </Form.Item>
                    </Tabs.TabPane>

                    {parametersTab}
                    {planificationTab}
                  </Tabs>
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.LINK && (
                <>
                  <Tabs defaultActiveKey="1" type="card">
                    <Tabs.TabPane tab={t('Content')} key="1">
                      <Tabs defaultActiveKey={i18n.language}>
                        {enabledLanguages &&
                          enabledLanguages?.map((lang) => (
                            <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                              <Form.Item name={tr('name', lang)} label={t('LinkTitle')}>
                                <Input type="text" placeholder={t('LinkTitle')} />
                              </Form.Item>
                              <Form.Item
                                name={tr('description', lang)}
                                label={t('Description(Optionnal)')}
                              >
                                <Input type="text" placeholder={t('Description(Optionnal)')} />
                              </Form.Item>
                            </Tabs.TabPane>
                          ))}
                      </Tabs>
                      <Form.Item label={t('DynamicFormationElement.DynamicSectionLabel')}>
                        <UserPropertySelector
                          onSelect={(value) => {
                            const currentValue = form.getFieldValue('text') ?? '';
                            const newValue =
                              currentValue +
                              `${FORMATION_ELEMENT_DYNAMIC_FLAG_START}${value}${FORMATION_ELEMENT_DYNAMIC_FLAG_END}`;
                            form.setFieldsValue({ text: newValue });
                          }}
                          isUrlDynamicElement={true}
                        />
                      </Form.Item>

                      <Form.Item label={t('Link')} name="text">
                        <Input type="text" placeholder="https://google.fr" />
                      </Form.Item>
                    </Tabs.TabPane>
                    {parametersTab}
                    {planificationTab}
                  </Tabs>
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.FILE && (
                <>
                  <Tabs defaultActiveKey="1" type="card">
                    <Tabs.TabPane tab={t('Content')} key="1">
                      <Tabs defaultActiveKey={i18n.language}>
                        {enabledLanguages &&
                          enabledLanguages?.map((lang) => (
                            <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                              <Form.Item name={tr('name', lang)} label={t('FileName')}>
                                <Input type="text" placeholder={t('FileName')} />
                              </Form.Item>
                              <Form.Item
                                name={tr('description', lang)}
                                label={t('Description(Optionnal)')}
                              >
                                <Input type="text" placeholder={t('Description(Optionnal)')} />
                              </Form.Item>
                            </Tabs.TabPane>
                          ))}
                      </Tabs>

                      <Form.Item
                        label={t('File')}
                        extra={!file && element?.text && `${t('current')}: ${element?.text}`}
                      >
                        <Upload.Dragger
                          name="file"
                          listType="picture"
                          showUploadList={{
                            showPreviewIcon: false,
                            showDownloadIcon: false,
                            showRemoveIcon: true
                          }}
                          beforeUpload={(uploadFile) => {
                            setFile(uploadFile);
                            return false;
                          }}
                          fileList={file ? [file] : []}
                          onRemove={() => setFile('')}
                        >
                          <div>
                            {loading ? <LoadingOutlined /> : <UploadOutlined />}
                            <div className="ant-upload-text">{t('general.upload')}</div>
                          </div>
                        </Upload.Dragger>
                      </Form.Item>

                      <Form.Item
                        label={t('Preview(image)')}
                        extra={!fileImage && formationStep?.image}
                      >
                        <Upload.Dragger
                          name="image"
                          listType="picture"
                          showUploadList
                          accept="image/*"
                          beforeUpload={beforeUpload}
                          fileList={fileImage ? [fileImage] : []}
                          onRemove={() => setFileImage('')}
                        >
                          {/*
                          {image ? <ProtectedImage src={image} alt={"ue"} style={{maxWidth: 115}} /> : ''}
                        */}
                          <div>
                            {loading ? <LoadingOutlined /> : <PlusOutlined />}
                            <div className="ant-upload-text">{t('general.upload')}</div>
                          </div>
                        </Upload.Dragger>
                      </Form.Item>
                    </Tabs.TabPane>
                    {parametersTab}
                    <Tabs.TabPane tab={t('watermark.WatermarkFileOption')} key="3">
                      {element?.id ? (
                        <AgnosticWatermarkModalModule
                          agnosticIdArray={[element?.id]}
                          fileString={element?.text}
                          settings={element?.settings || {}}
                          form={form}
                          type={supportedWatermarkPdfTypes.WATERMARK_FILE}
                          key={watermarkKey}
                        />
                      ) : (
                        <>{t('CreateElementBeforeSettingUpWatermark')}</>
                      )}
                    </Tabs.TabPane>
                    {planificationTab}
                  </Tabs>

                  {/*
                <Form.Item
                  name="description"
                  label="Description (optionnel)"
                >
                  <Input type="text" placeholder="Description (optionnel)"/>
                </Form.Item>
                */}
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.SCORM && (

                <ScormContextProvider
                  formationElementId={element?.id}
                  scormVersion={element?.scormVersion}
                  scormEntryPoint={element?.scormEntryPoint}
                  scormFilename={element?.scormFilename}
                  scormFilePath={element?.scormFilePath}
                  scormParsing={element?.scormParsing}
                  scormSandboxMode={element?.settings?.[SCORM_BACKEND_SETTINGS_KEY.SCORM_SANDBOX_MODE_SETTINGS]}
                  contextDataSetter={setScormData}
                >
                  <Tabs defaultActiveKey="1" type="card" destroyInactiveTabPane={true}>
                    <Tabs.TabPane tab={t('ScormFE.GeneralTabFE')} key="1">
                      <ScormFormationElementGeneral />
                    </Tabs.TabPane>
                    {parametersTab}
                    {planificationTab}
                  </Tabs>

                </ScormContextProvider>
              )}

              {selectedElementType === ELEMENTS_TYPE.VIDEO && (
                <>
                  <Tabs defaultActiveKey="1" type="card">
                    <Tabs.TabPane tab={t('Content')} key="1">
                      <Tabs defaultActiveKey={i18n.language}>
                        {enabledLanguages &&
                          enabledLanguages?.map((lang) => (
                            <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                              <Form.Item name={tr('name', lang)} label={t('VideoTitle(optionnal)')}>
                                <Input type="text" placeholder="" />
                              </Form.Item>
                              <Form.Item
                                name={tr('description', lang)}
                                label={t('Description(Optionnal)')}
                              >
                                <Input type="text" placeholder={t('Description(Optionnal)')} />
                              </Form.Item>
                            </Tabs.TabPane>
                          ))}
                      </Tabs>

                      <Form.Item name="text" label={t('VideoEmbedLink')}>
                        <Input.TextArea
                          onChange={({ target: { value } }) => setTmpVidPreview(value)}
                          rows={4}
                          type="textarea"
                          placeholder=""
                        />
                      </Form.Item>

                      <Form.Item label={t('Preview')}>
                        <EmbeddedVideo video={tmpVidPreview} />
                      </Form.Item>
                    </Tabs.TabPane>

                    {parametersTab}
                    {planificationTab}
                  </Tabs>
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.HTML && (
                <>
                  <Tabs defaultActiveKey="1" type="card">
                    <Tabs.TabPane tab={t('Content')} key="1">
                      <Form.Item name="text" label={t('HTMLCode')}>
                        <Input.TextArea
                          onChange={({ target: { value } }) => setTmpVidPreview(value)}
                          rows={4}
                          type="textarea"
                          placeholder=""
                        />
                      </Form.Item>
                      <Form.Item label={t('Preview')}>{displayDirectHtml(tmpVidPreview)}</Form.Item>
                    </Tabs.TabPane>
                    {parametersTab}
                    {planificationTab}
                  </Tabs>
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.COURSE_SHORTCUT && (
                <>
                  <Form.Item label="Cours">
                    <PedagogicCascaderSelector
                      onSelectTarget={({ label, value, type }) => {
                        setSelectedCoursName(label);
                        setSelectedCoursId(value);
                      }}
                      acceptedTypes={[PedagogicCascaderSelectorTarget.Cours]}
                      excludeImportedCourses
                      placeholder="Sélectionner le cours"
                      selectorType={SelectorType.TreeSelect}
                    />
                  </Form.Item>

                  {selectedCoursName && (
                    <div style={{ marginTop: '14px', marginBottom: '14px' }}>
                      Cours sélectionné: <b>{selectedCoursName}</b>
                    </div>
                  )}
                </>
              )}

              {/* Cours complet */}
              {selectedElementType === ELEMENTS_TYPE.COURS && (
                <>
                  <Form.Item label={t('Courses')}>
                    <Tabs defaultActiveKey="1" type="card">
                      <Tabs.TabPane tab={t('SelectedCourse')} key="1">
                        {selectedCoursId ? (
                          <>
                            <Button
                              type="primary"
                              onClick={() => setSelectedCoursId(null)}
                              icon={<EditOutlined />}
                            >
                              {t('ChangeCourse')}
                            </Button>
                            {/* TODO change */}
                            {/*
                          <CoursPage
                            coursId={selectedCoursId}
                            withLateralMenu={false}
                            hideBanner
                            hideBreadCrumb
                            hideDiscussion
                            showSettings={element?.settings}
                            useSettingsFromParent
                          />
                          */}
                          </>
                        ) : (
                          <PedagogicCascaderSelector
                            onSelectTarget={async ({ label, value, type }) => {
                              setSelectedCoursId(value);
                            }}
                            acceptedTypes={[PedagogicCascaderSelectorTarget.Cours]}
                            placeholder="Sélectionner le cours"
                          />
                        )}
                      </Tabs.TabPane>

                      <Tabs.TabPane tab={t('Settings')} key="2">
                        <h3>{t('RightPart')}</h3>
                        <Form.Item valuePropName="checked">
                          <Checkbox
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                showNotions: e.target.checked
                              })
                            }
                            checked={settings?.showNotions}
                          >
                            {t('NotionModule')}
                          </Checkbox>
                        </Form.Item>
                        <Form.Item valuePropName="checked">
                          <Checkbox
                            checked={settings?.showQCMs}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                showQCMs: e.target.checked
                              })
                            }
                          >
                            {t('ExerciceSerieOfCourse')}
                          </Checkbox>
                        </Form.Item>

                        <Form.Item valuePropName="checked">
                          <Checkbox
                            checked={settings?.showRevision}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                showRevision: e.target.checked
                              })
                            }
                          >
                            {t('ExplanationModuleRevision')}
                          </Checkbox>
                        </Form.Item>
                        <Form.Item valuePropName="checked">
                          <Checkbox
                            checked={settings?.showAnnales}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                showAnnales: e.target.checked
                              })
                            }
                          >
                            {t('ModuleQuickAccessExplanation')}
                          </Checkbox>
                        </Form.Item>
                      </Tabs.TabPane>
                    </Tabs>
                  </Form.Item>
                </>
              )}
              {(selectedElementType === ELEMENTS_TYPE.VIDSTACK_VIDEO ||
                selectedElementType === ELEMENTS_TYPE.VIDSTACK_AUDIO) && (
                <Form.Item>
                  <VidstackContextProvider
                    inFormation={inFormation}
                    initValues={{
                      initUrl: element?.settings?.url,
                      initS3FileName: element?.s3FileName
                    }}
                    contextDataSetter={setVidstackData}
                    autoPlay={element?.settings?.vidStackAutoPlay}
                    authorizeSpeedModification={
                      element?.settings?.vidStackAuthorizeSpeedModification
                    }
                    authorizeFullScreen={element?.settings?.vidStackAuthorizeFullScreen}
                    authorizePictureInPicture={element?.settings?.vidStackAuthorizePictureInPicture}
                    authorizeDownload={element?.settings?.vidStackAuthorizeDownload}
                    videoWidth={element?.settings?.vidStackVideoWidth}
                    enableStatsTracking={element?.settings?.vidstackEnableStatsTracking}
                    title={element?.settings?.vidstackTitle}
                    imageSrc={element?.image}
                    tracksStringObject={element?.settings?.vidstackTrackObject}
                    initDescription={
                      element?.settings?.vidstackQuillDescription ||
                      selectedElementType === ELEMENTS_TYPE.VIDSTACK_VIDEO
                        ? t('VideoFE.ContextDescriptionInitString')
                        : t('AudioFE.ContextDescriptionInitString')
                    }
                    clipStartTime={element?.settings?.vidstackClipStartTime}
                    clipEndTime={element?.settings?.vidstackClipEndTime}
                    formationElementId={element?.id}
                    layoutType={
                      selectedElementType === ELEMENTS_TYPE.VIDSTACK_VIDEO
                        ? 'video'
                        : selectedElementType === ELEMENTS_TYPE.VIDSTACK_AUDIO
                          ? 'audio'
                          : null
                    }
                  >
                    <Tabs defaultActiveKey="1" type="card" destroyInactiveTabPane={true}>
                      <Tabs.TabPane tab={t('VideoFE.General')} key="1">
                        <VidstackFormationElementGeneralEdit
                          selectedElementType
                          modalType={modalType}
                        />
                      </Tabs.TabPane>

                      <Tabs.TabPane tab={t('VideoFE.Parameters')} key="5">
                        <VidstackParameterFormationElementTab />
                      </Tabs.TabPane>
                      {parametersTab}
                      {planificationTab}
                    </Tabs>
                  </VidstackContextProvider>
                </Form.Item>
              )}

              {selectedElementType === ELEMENTS_TYPE.MCQ && (
                <>
                  {selectedMcqId ? (
                    <>
                      <Tabs defaultActiveKey="1" type="card">
                        <Tabs.TabPane tab={t('SelectedQuizz')} key="1">
                          <Button
                            type="primary"
                            onClick={() => setSelectedMcqId(null)}
                            icon={<EditOutlined />}
                          >
                            {t('ChangeQuizz')}
                          </Button>
                          <AdminQuestionSerieElementPreview selectedMcqId={selectedMcqId} />
                          {/*
                            <QcmDetails
                              qcmId={selectedMcqId}
                              showLaunchButton={false}
                              showCorrectionIfAvailable={false}
                            />
                          */}
                          <br />
                        </Tabs.TabPane>

                        <Tabs.TabPane tab={t('QuizzSettings')} key="2">
                          <QuizzParameters element={element} />
                        </Tabs.TabPane>
                      </Tabs>
                    </>
                  ) : (
                    <QCMSelector
                      onSelectQcm={async (qcmId) => {
                        setSelectedMcqId(qcmId);
                      }}
                    />
                  )}
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.DIAPO_SYNTHESE && (
                <EditDiapoSynthese
                  element={element}
                  settings={settings}
                  setSettings={setSettings}
                  enabledLanguages={enabledLanguages}
                  selectedMcqId={selectedMcqId}
                  setSelectedMcqId={setSelectedMcqId}
                />
              )}

              {selectedElementType === ELEMENTS_TYPE.DIAPO && (
                <DiapoViewerContextProvider
                  initValues={{
                    initUrl: element?.settings?.url,
                    initS3FileName: element?.s3FileName
                  }}
                  contextDataSetter={setDiapoData}
                  tracks={element?.settings?.[BACKEND_SETTINGS_KEY.TRACK]}
                  title={element?.settings?.[BACKEND_SETTINGS_KEY.TITLE]}
                  initDescription={
                    element?.settings?.[BACKEND_SETTINGS_KEY.DESCRIPTION] || 'Description'
                  }
                  authorizeDownload={element?.settings?.[BACKEND_SETTINGS_KEY.AUTHORIZE_DOWNLOAD]}
                  authorizeFullScreen={
                    element?.settings?.[BACKEND_SETTINGS_KEY.AUTHORIZE_FULL_SCREEN]
                  }
                  width={element?.settings?.[BACKEND_SETTINGS_KEY.WIDTH]}
                >
                  <Tabs defaultActiveKey="1" type="card" destroyInactiveTabPane={true}>
                    <Tabs.TabPane tab={t('VideoFE.General')} key="1">
                      <DiapoViewerFormationElementGeneralEdit />
                    </Tabs.TabPane>

                    <Tabs.TabPane tab={t('VideoFE.Parameters')} key="5">
                      <DiapoViewerFormationElementParameters />
                    </Tabs.TabPane>
                    {parametersTab}
                    {planificationTab}
                  </Tabs>
                </DiapoViewerContextProvider>
              )}

              {selectedElementType === ELEMENTS_TYPE.DO_EXERCISE && (
                <>
                  <Tabs defaultActiveKey={i18n.language}>
                    {enabledLanguages &&
                      enabledLanguages?.map((lang) => (
                        <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                          <Form.Item name={tr('name', lang)} label={t('ElementTitle')}>
                            <Input type="text" placeholder="Titre" />
                          </Form.Item>
                          <Form.Item name={tr('description', lang)} label={t('Description')}>
                            <Input type="text" placeholder="Description (optionnel)" />
                          </Form.Item>

                          <Form.Item
                            label={t('Preview(image)')}
                            extra={!fileImage && formationStep?.image}
                          >
                            <Upload.Dragger
                              name="image"
                              listType="picture"
                              showUploadList
                              accept="image/*"
                              beforeUpload={beforeUpload}
                              fileList={fileImage ? [fileImage] : []}
                              onRemove={() => setFileImage('')}
                            >
                              {/*
                              {image ? <ProtectedImage src={image} alt={"ue"} style={{maxWidth: 115}} /> : ''}
                            */}
                              <div>
                                {loading ? <LoadingOutlined /> : <PlusOutlined />}
                                <div className="ant-upload-text">{t('general.upload')}</div>
                              </div>
                            </Upload.Dragger>
                          </Form.Item>
                        </Tabs.TabPane>
                      ))}
                  </Tabs>

                  <Divider orientation="left">Exercice</Divider>
                  <SelectExerciseToDoInElement
                    selectedDoQuestionId={selectedDoQuestionId}
                    setSelectedDoQuestionId={setSelectedDoQuestionId}
                  />
                  <br />
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.EVENT && (
                <>
                  <h5>{t('SelectEventToShow')}</h5>
                  <SingleEventSelector
                    setSelectedEventId={setSelectedEventId}
                    selectedEventId={selectedEventId}
                  />
                  <Divider />
                </>
              )}

              {selectedElementType === ELEMENTS_TYPE.EXAM && (
                <>
                  <h5>{t('SelectExamToShow')}</h5>
                  <SingleExamSelector
                    selectedExamId={selectedExamId}
                    setSelectedExamId={setSelectedExamId}
                  />
                  <Divider />
                </>
              )}

              {inputElementsTypesEdition}

              {/*
            <Form.Item
              name="order"
              label={t('DisplayOrder')}
            >
              <Input type="number" placeholder="Ordre affichage parmi les autres éléments"/>
            </Form.Item>
            */}

              <Form.Item>
                <div
                  style={{
                    display: 'flex',
                    gap: 8,
                    flexDirection: 'column',
                    alignItems: 'flex-end',
                  }}
                >
                  {modalType === ModalType.CREATE && (
                    <Alert
                      style={{ width: '100%' }}
                      message={t('ElementNotYetAdded')}
                      type="warning"
                      showIcon
                    />
                  )}

                  <div style={{ display: 'flex', gap: '8px' }}>
                    {modalType === ModalType.UPDATE && (
                      <Space>
                        {showButtons && (
                          <Button onClick={() => form.submit()} type="primary" loading={loading}>
                            {t('Update')}
                          </Button>
                        )}

                        {/* Delete button */}
                        <Popconfirm
                          title={t('SureToDelete')}
                          onConfirm={deleteElement}
                          okText={t('general.yes')}
                          cancelText={t('general.no')}
                        >
                          <Tooltip title={t('Delete')}>
                            <Button
                              disabled={
                                element?.type === ELEMENTS_TYPE.REGISTER_FIELD &&
                                [
                                  RegisterFieldsNames.USERNAME,
                                  RegisterFieldsNames.EMAIL,
                                  RegisterFieldsNames.PASSWORD,
                                ].includes(settings?.type)
                              }
                              shape="circle"
                              danger
                              icon={<DeleteOutlined />}
                            />
                          </Tooltip>
                        </Popconfirm>
                      </Space>
                    )}

                    <div style={{ display: 'flex', gap: '5px', alignItems: 'baseline' }}>
                      {modalType === ModalType.CREATE && (
                        <>
                          <div>
                            <Button onClick={() => form.submit()} type="primary" loading={loading}>
                              {t('general.add')}
                            </Button>
                          </div>
                        </>
                      )}

                      {showButtons && showCancelButton && (
                        <div>
                          <Button onClick={closeModalHandler} loading={loading}>
                            {t('Cancel')}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  {selectedElementType === ELEMENTS_TYPE.TITLE && (
                    <Button
                      type="link"
                      size="small"
                      onClick={() => router.push(`/admin/config/titles`)}
                    >
                      {t('CustomizeTitlesType')}
                    </Button>
                  )}
                </div>
              </Form.Item>
            </Form>
          </>
        )}
      </div>
    );
  }
);
