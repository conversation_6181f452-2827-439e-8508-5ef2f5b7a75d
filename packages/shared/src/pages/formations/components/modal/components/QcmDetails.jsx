import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx'
import { GET_DETAILS_QCM_ID } from '@/shared/graphql/qcm.js'
import { DetailQCMCard } from '@/shared/pages/qcm/details/components/DetailQCMCard.jsx'
import { useQuery } from '@apollo/client'
import React from 'react'

export default function({ qcmId, showLaunchButton, showCorrectionIfAvailable }) {

  // TODO split query
  const { loading, error, data, refetch } = useQuery(GET_DETAILS_QCM_ID, {
    fetchPolicy: "cache-and-network",
    variables: { id: qcmId },
  })
  const qcmObject = data?.qcm

  return (
    <>
      {loading && <SpinnerCentered />}
      {qcmObject && (
        <DetailQCMCard
          title={qcmObject?.titre}
          showLaunchButton={showLaunchButton}
          showCorrectionIfAvailable={showCorrectionIfAvailable}
          name={qcmObject?.description}
          idLien={qcmObject?.id_lien}
          refetch={refetch}
          id={qcmId}
          qcm={qcmObject}
        />
      )}
    </>
  )
}