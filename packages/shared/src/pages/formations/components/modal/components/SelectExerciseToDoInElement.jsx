import QuestionSearchTable, {
  QuestionSearchActionsTypes
} from '@/shared/pages/admin/qcm/components/modal/QuestionSearchTable';
import { CloseOutlined } from '@ant-design/icons';
import { But<PERSON>, Drawer } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function SelectExerciseToDoInElement({
  selectedDoQuestionId,
  setSelectedDoQuestionId
}) {
  const { t } = useTranslation();

  const [isVisible, setIsVisible] = useState(false);

  const showDrawer = () => {
    setIsVisible(true);
  };

  const onCancelModal = () => {
    setIsVisible(false);
  };

  const onSelectExercise = (q) => {
    setSelectedDoQuestionId(q.id_question);
    setIsVisible(false);
  };

  return (
    <>
      {selectedDoQuestionId ? (
        <div>
          <div>Exercice sélectionné: ID {selectedDoQuestionId}</div>
          <br />
          <Button onClick={showDrawer}>Changer d'exercice</Button>
          <br />
        </div>
      ) : (
        <>
          <br />
          <Button type={'primary'} onClick={showDrawer}>
            Sélectionner un exercice...
          </Button>
          <br />
        </>
      )}

      {isVisible && (
        <Drawer
          placement={'bottom'}
          onClose={onCancelModal}
          closeIcon={null}
          extra={<CloseOutlined onClick={onCancelModal} />}
          title={'Sélectionner un exercice'}
          open={isVisible}
          footer={null}
          destroyOnClose
          height={'calc(100% - 64px)'}
        >
          <QuestionSearchTable
            mode={QuestionSearchActionsTypes.selectSingleExercise}
            onFinish={onSelectExercise}
          />
        </Drawer>
      )}
    </>
  );
}
