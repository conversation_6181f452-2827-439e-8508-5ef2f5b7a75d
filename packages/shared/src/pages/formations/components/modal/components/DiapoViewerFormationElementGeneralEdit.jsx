import React from 'react';
import DiapoViewerModifySource from '@/shared/components/diapoViewer/DiapoViewerModifySource';
import DiapoViewerFormationElementDisplay from '@/shared/pages/formations/components/modal/components/DiapoViewerFormationElementDisplay';
import DiapoViewerTableManager from '@/shared/components/diapoViewer/DiapoViewerTableManager';

const DiapoViewerFormationElementGeneralEdit = () => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        gap: '20px',
        marginBottom: '10px',
        flexWrap: 'wrap'
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', flex: 1 }}>
        <DiapoViewerModifySource />
        <DiapoViewerTableManager />
      </div>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', flex: 2 }}>
        <DiapoViewerFormationElementDisplay />
      </div>
    </div>
  );
};
export default DiapoViewerFormationElementGeneralEdit;
