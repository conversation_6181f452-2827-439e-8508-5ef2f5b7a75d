import { useTranslation } from 'react-i18next';
import { Tabs } from 'antd';
import React from 'react';

const WrapperIsOnlyTextTabBig = ({
  children,
  isOnlyTextTab,
  parametersTab,
  stylingTab,
  planificationTab
}) => {
  /* Wrapper qui permet de retirer le clutter lorsque l'on est en isOnlyTextTab = true . Doit être mis là sinon refresh en boucle du createEditFormationElementModal*/
  const { t} = useTranslation();
  return (
    <>
      {isOnlyTextTab ? (
        children
      ) : (
        <Tabs defaultActiveKey="1" type="card">
          <Tabs.TabPane tab={t('Text')} key="1">
            {children}
          </Tabs.TabPane>
          {parametersTab}
          {stylingTab}
          {planificationTab}
        </Tabs>
      )}
    </>
  );
};

export default WrapperIsOnlyTextTabBig;
