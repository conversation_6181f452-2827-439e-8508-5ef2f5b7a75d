import React from 'react';
import DiapoViewerParameters from '@/shared/components/diapoViewer/DiapoViewerParameters';
import DiapoViewerFormationElementDisplay from '@/shared/pages/formations/components/modal/components/DiapoViewerFormationElementDisplay';

const DiapoViewerFormationElementParameters = () => {
  return (
    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '20px' }}>
      <DiapoViewerParameters />
      <div
        style={{
          flexGrow: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <DiapoViewerFormationElementDisplay />
      </div>
    </div>
  );
};

export default DiapoViewerFormationElementParameters;
