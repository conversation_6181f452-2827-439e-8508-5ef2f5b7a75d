import { SimpleMoveTransition } from '@/shared/assets/transitions/SimpleMoveTransition';
import {
  QUERY_ELEMENTS_AFTER_COMPLETION_DIAPO_SYNTHESE,
  QUERY_ELEMENTS_BEFORE_COMPLETION_DIAPO_SYNTHESE
} from '@/shared/graphql/formations';
import FormationEditableElement from '@/shared/pages/formations/components/FormationEditableElement';
import ElementGroupsAccessManager from '@/shared/pages/formations/components/modal/components/ElementGroupsAccessManager';
import QcmDetails from '@/shared/pages/formations/components/modal/components/QcmDetails';
import QCMSelector from '@/shared/pages/formations/components/modal/components/QCMSelector';
import { CreateEditFormationElementModal } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal';
import { ELEMENTS_TYPE } from '@/shared/services/formations';
import { getLanguageName, tr } from '@/shared/services/translate';
import { EditOutlined, PlusCircleTwoTone } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { But<PERSON>, Card, Checkbox, Form, Input, Tabs } from 'antd';
import { AnimatePresence } from 'framer-motion/dist/framer-motion';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function EditDiapoSynthese({
  selectedMcqId,
  enabledLanguages,
  setSelectedMcqId,
  element, // diapo synthese element
  settings,
  setSettings
}) {
  const { t, i18n } = useTranslation();

  /* Elements related */
  const [createVisibleBefore, setCreateVisibleBefore] = useState(false);
  const [createVisibleAfter, setCreateVisibleAfter] = useState(false);
  const [position, setPosition] = useState(null);

  // PROBLEME IL FAUT avoir pré-créé élément pour pouvoir ajouter elements before/after

  const {
    loading: loadingElemsBefore,
    data: { elementsBeforeCompletionDiapoSynthese = [] } = {},
    refetch: refetchElemsBefore
  } = useQuery(QUERY_ELEMENTS_BEFORE_COMPLETION_DIAPO_SYNTHESE, {
    variables: {
      id: element?.id
    },
    skip: !element,
    fetchPolicy: 'cache-and-network'
  });

  const {
    loading: loadingElemsAfter,
    data: { elementsAfterCompletionDiapoSynthese = [] } = {},
    refetch: refetchElemsAfter
  } = useQuery(QUERY_ELEMENTS_AFTER_COMPLETION_DIAPO_SYNTHESE, {
    variables: {
      id: element?.id
    },
    skip: !element,
    fetchPolicy: 'cache-and-network'
  });

  const renderDDSCreationBefore = (elementPosition = null) => (
    <>
      {elementPosition === position && (
        <>
          {createVisibleBefore && (
            <div
              style={{
                border: '1px dashed #b5b5b5',
                borderRadius: '11px',
                margin: 5,
                marginBottom: '15px'
              }}
            >
              <div style={{ margin: '15px' }}>
                <CreateEditFormationElementModal
                  isModalVisible={createVisibleBefore}
                  modalType="CREATE"
                  position={position}
                  closeModalHandler={() => {
                    setCreateVisibleBefore(false);
                    refetchElemsBefore();
                  }}
                  diapoSyntheseElementBeforeId={element?.id}
                  disableDiapoSynthese
                  elementsTypesToShow={{
                    [ELEMENTS_TYPE.TITLE]: true,
                    [ELEMENTS_TYPE.IMAGE]: true,
                    [ELEMENTS_TYPE.MCQ]: false,
                    [ELEMENTS_TYPE.DO_EXERCISE]: false,
                    [ELEMENTS_TYPE.LINK]: true,
                    [ELEMENTS_TYPE.HTML]: true,
                    [ELEMENTS_TYPE.COURS]: false,
                    [ELEMENTS_TYPE.FILE]: true,
                    [ELEMENTS_TYPE.RICH_TEXT]: true,
                    [ELEMENTS_TYPE.VIDEO]: false, // Osbolète
                    [ELEMENTS_TYPE.VIDSTACK_VIDEO]: true,
                    [ELEMENTS_TYPE.VIDSTACK_AUDIO]: true,
                    [ELEMENTS_TYPE.DIAPO]:true,
                    [ELEMENTS_TYPE.CALLOUT]: true,
                    [ELEMENTS_TYPE.SCORM]:true,
                    // INPUTS
                    /*
                    [ELEMENTS_TYPE.SHORT_ANSWER]: true,
                    [ELEMENTS_TYPE.LONG_ANSWER]: true,
                    [ELEMENTS_TYPE.SINGLE_SELECT]: true,
                    [ELEMENTS_TYPE.MULTIPLE_SELECT]: true,
                    [ELEMENTS_TYPE.INTEGER_NUMBER]: true,
                    [ELEMENTS_TYPE.DATE_PICKER]: true,
                    [ELEMENTS_TYPE.DATE_AND_TIME_PICKER]: true,
                    [ELEMENTS_TYPE.FILE_IMPORT]: true,
                     */
                    [ELEMENTS_TYPE.DIAPO_SYNTHESE]: false
                  }}
                />
              </div>
            </div>
          )}
        </>
      )}
      {!createVisibleBefore && (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
          <div style={{ width: '90%' }}>
            <Button
              style={{ marginTop: 10, minHeight: '50px' }}
              type="dashed"
              block
              icon={<PlusCircleTwoTone />}
              onClick={() => {
                setPosition(elementPosition);
                setCreateVisibleBefore(true);
              }}
            >
              {t('AddElementHere')}
            </Button>
          </div>
        </div>
      )}
    </>
  );

  const renderDDSCreationAfter = (elementPosition = null) => (
    <>
      {elementPosition === position && (
        <>
          {createVisibleAfter && (
            <div
              style={{
                border: '1px dashed #b5b5b5',
                borderRadius: '11px',
                margin: 5,
                marginBottom: '15px'
              }}
            >
              <div style={{ margin: '15px' }}>
                <CreateEditFormationElementModal
                  isModalVisible={createVisibleAfter}
                  modalType="CREATE"
                  position={position}
                  closeModalHandler={() => {
                    setCreateVisibleAfter(false);
                    refetchElemsAfter();
                  }}
                  disableDiapoSynthese
                  diapoSyntheseElementAfterId={element.id}
                  elementsTypesToShow={{
                    [ELEMENTS_TYPE.TITLE]: true,
                    [ELEMENTS_TYPE.IMAGE]: true,
                    [ELEMENTS_TYPE.MCQ]: false,
                    [ELEMENTS_TYPE.DO_EXERCISE]: false,
                    [ELEMENTS_TYPE.LINK]: true,
                    [ELEMENTS_TYPE.HTML]: true,
                    [ELEMENTS_TYPE.COURS]: false,
                    [ELEMENTS_TYPE.FILE]: true,
                    [ELEMENTS_TYPE.RICH_TEXT]: true,
                    [ELEMENTS_TYPE.VIDEO]: false, // obsolète
                    [ELEMENTS_TYPE.VIDSTACK_VIDEO]: true,
                    [ELEMENTS_TYPE.VIDSTACK_AUDIO]: true,
                    [ELEMENTS_TYPE.DIAPO]:true,
                    [ELEMENTS_TYPE.CALLOUT]: true,
                    [ELEMENTS_TYPE.SCORM]:true,
                    // INPUTS
                    /*
                    [ELEMENTS_TYPE.SHORT_ANSWER]: true,
                    [ELEMENTS_TYPE.LONG_ANSWER]: true,
                    [ELEMENTS_TYPE.SINGLE_SELECT]: true,
                    [ELEMENTS_TYPE.MULTIPLE_SELECT]: true,
                    [ELEMENTS_TYPE.INTEGER_NUMBER]: true,
                    [ELEMENTS_TYPE.DATE_PICKER]: true,
                    [ELEMENTS_TYPE.DATE_AND_TIME_PICKER]: true,
                    [ELEMENTS_TYPE.FILE_IMPORT]: true,
                    */
                    // medisup only
                    [ELEMENTS_TYPE.DIAPO_SYNTHESE]: false
                  }}
                />
              </div>
            </div>
          )}
        </>
      )}
      {!createVisibleAfter && (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
          <div style={{ width: '90%' }}>
            <Button
              style={{ marginTop: 10, minHeight: '50px' }}
              type="dashed"
              block
              icon={<PlusCircleTwoTone />}
              onClick={() => {
                setPosition(elementPosition);
                setCreateVisibleAfter(true);
              }}
            >
              {t('AddElementHere')}
            </Button>
          </div>
        </div>
      )}
    </>
  );

  const renderElement = (element, previousElement, nextElement, key, columnPosition) => (
    <FormationEditableElement
      style={{ transition: 'transform .35s ease-in-out' }}
      key={key}
      element={element}
      nextElement={nextElement}
      previousElement={previousElement}
      columnPosition={columnPosition}
      canEdit
      refetchAll={() => {
        refetchElemsBefore();
        refetchElemsAfter();
      }}
    />
  );

  return (
    <>
      {selectedMcqId ? (
        <>
          <Tabs defaultActiveKey="1" type="card">
            <Tabs.TabPane tab={t('DDS.DDS')} key="1">
              <Tabs defaultActiveKey={i18n.language}>
                {enabledLanguages &&
                  enabledLanguages?.map((lang) => (
                    <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                      <Form.Item name={tr('name', lang)} label={t('ElementTitle')}>
                        <Input type="text" placeholder="Titre" />
                      </Form.Item>
                      <Form.Item name={tr('description', lang)} label={t('Description')}>
                        <Input type="text" placeholder="Description (optionnel)" />
                      </Form.Item>
                    </Tabs.TabPane>
                  ))}
              </Tabs>
              <Button type="primary" onClick={() => setSelectedMcqId(null)} icon={<EditOutlined />}>
                {t('ChangeQuizz')}
              </Button>
              <Card size="small">
                <QcmDetails
                  qcmId={selectedMcqId}
                  showLaunchButton={false}
                  showCorrectionIfAvailable={false}
                />
              </Card>
              <br />
              <br />
              {element?.id && (
                <>
                  <h5>Affichage DDS AVANT complétion</h5>
                  <AnimatePresence mode="popLayout">
                    {elementsBeforeCompletionDiapoSynthese?.map((elem, k) => (
                      <SimpleMoveTransition id={elem.id} key={elem.id}>
                        {renderElement(
                          elem,
                          elementsBeforeCompletionDiapoSynthese[k - 1],
                          elementsBeforeCompletionDiapoSynthese[k + 1],
                          elem?.id
                        )}
                      </SimpleMoveTransition>
                    ))}
                    {renderDDSCreationBefore()}
                  </AnimatePresence>
                  <br />
                  <h5>Affichage DDS APRÈS complétion</h5>
                  <AnimatePresence mode="popLayout">
                    {elementsAfterCompletionDiapoSynthese?.map((elem, k) => (
                      <SimpleMoveTransition id={elem.id} key={elem.id}>
                        {renderElement(
                          elem,
                          elementsAfterCompletionDiapoSynthese[k - 1],
                          elementsAfterCompletionDiapoSynthese[k + 1],
                          elem?.id
                        )}
                      </SimpleMoveTransition>
                    ))}
                    {renderDDSCreationAfter()}
                  </AnimatePresence>
                </>
              )}

              <br />
              <br />
              <h5>Options</h5>
              <Form.Item name={['settings', 'showProgress']} valuePropName="checked">
                <Checkbox>{t('DDS.ShowProgress')}</Checkbox>
              </Form.Item>
              <Form.Item name={['settings', 'allowRedo']} valuePropName="checked">
                <Checkbox>{t('DDS.AllowRedo')}</Checkbox>
              </Form.Item>
              <Form.Item name={['settings', 'forceToDoInOrder']} valuePropName="checked">
                <Checkbox>{t('DDS.ForceToDoInOrder')}</Checkbox>
              </Form.Item>
            </Tabs.TabPane>
            <Tabs.TabPane tab={t('Settings')} key="2">
              <ElementGroupsAccessManager element={element} />
            </Tabs.TabPane>
          </Tabs>
        </>
      ) : (
        <QCMSelector
          onSelectQcm={async (qcmId) => {
            setSelectedMcqId(qcmId);
          }}
        />
      )}
    </>
  );
}
