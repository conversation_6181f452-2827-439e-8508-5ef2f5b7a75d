import Title from '@/shared/components/Formation/Title.jsx';
import {
  MUTATION_CREATE_FORMATION_BLOCK,
  MUTATION_CREATE_FORMATION_ELEMENT,
  MUTATION_DELETE_FORMATION_BLOCK,
  MUTATION_UPDATE_FORMATION_BLOCK, QUERY_ALL_TITLES,
} from '@/shared/graphql/formations.js';
import { BLOCK_TYPE } from '@/shared/services/formations.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import {
  AlignCenterOutlined,
  AlignLeftOutlined, AlignRightOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Select,
  Space,
  Tooltip,
} from 'antd';
import { useMutation, useQuery } from '@apollo/client';
import React, { useEffect, useState } from 'react';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult';
import double from '@/shared/assets/formation/double.png';
import single from '@/shared/assets/formation/single.png';
import { useTranslation } from 'react-i18next';

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
};

const getMutationFromModalType = (modalType) => {
  switch (modalType) {
    case ModalType.CREATE:
      return MUTATION_CREATE_FORMATION_BLOCK;
    case ModalType.UPDATE:
      return MUTATION_UPDATE_FORMATION_BLOCK;
    default:
      return MUTATION_CREATE_FORMATION_ELEMENT;
  }
};

export const CreateEditFormationBlockModal = (
  {
    closeModalHandler,
    modalType,
    isModalVisible,
    formationStep = null,
    coursId = null,
    refetch,
    block,
  }) => {

  const { t } = useTranslation();

  const [form] = Form.useForm();
  const [ElementMutation, { loading, error }] = useMutation(getMutationFromModalType(modalType));
  const [DeleteBlock] = useMutation(MUTATION_DELETE_FORMATION_BLOCK);
  const [selectedBlockType, setSelectedBlockType] = useState(block?.type || null);
  // Target object id if any
  const [selectedTitleId, setSelectedTitleId] = useState(block?.titleId || null);
  const [hasBorder, setHasBorder] = useState(block?.settings?.borderSize ? true : false);

  const { loading: loadingTitles, error: errorTitles, data: dataTitles, refetch: refetchTitles } = useQuery(
    QUERY_ALL_TITLES, { fetchPolicy: 'cache-and-network' });
  const allTitles = dataTitles?.allTitles;

  useEffect(() => {
    setSelectedBlockType(block?.type);
    setSelectedTitleId(block?.titleId);
    setHasBorder(block?.settings?.borderSize ? true : false)
  }, [block]);

  const handleFinish = async data => {
    try {
      if(!hasBorder) {
        delete data?.settings?.borderRadius
        delete data?.settings?.borderSize
        delete data?.settings?.borderColor
      }

      const newFormationBlock = { ...data };
      if (newFormationBlock && newFormationBlock.order) {
        newFormationBlock.order = parseInt(newFormationBlock.order, 10);
      }
      /* Handle parent (mandatory) and target id if any */
      newFormationBlock.formationStepId = formationStep?.id;
      newFormationBlock.coursId = coursId;
      /* Handle type (mandatory) */
      newFormationBlock.type = selectedBlockType;
      newFormationBlock.titleId = selectedTitleId;

      if (modalType === ModalType.UPDATE) {
        await ElementMutation({ variables: { id: block?.id, input: newFormationBlock } });
        message.success(t('Updated'));
      } else { // Create
        await ElementMutation({ variables: { input: newFormationBlock } });
        message.success(t('Created'));
      }
      await closeModalHandler();
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  async function deleteBlock() {
    try {
      await DeleteBlock({
        variables: {
          id: block?.id,
        },
      });
      await closeModalHandler();
    } catch (e) {
      console.error(e);
    }
  }

  const onBlockTypeClick = (blockType) => {
    setSelectedBlockType(blockType);
  };

  const buttonProps = {
    size: 'large',
    // fontSize: '20px',
    style: {
      height: '120px',
      width: '120px',
    },
  };

  return (
    (<Modal
      title={modalType === ModalType.UPDATE ? `${t('Edit')}` : 'Créer un élément dans le bloc'}
      open={isModalVisible}
      onCancel={() => {
        // RESET
        setSelectedBlockType(undefined);
        form.resetFields();
        closeModalHandler();
      }}
      footer={null}
      closable
      confirmLoading={false}
      bodyStyle={{ paddingTop: 0 }}
      width={1000}
    >
      {/* Show small error(s) if needed */}
      <SmallErrorsAlert error={error} loading={loading}/>
      {modalType === ModalType.CREATE && (
        <div>
          <h2>
            {t('BlockType')}
          </h2>
          <Space size="large">
            <Button
              {...buttonProps}
              onClick={() => onBlockTypeClick(BLOCK_TYPE.SINGLE)}
              icon={<img style={{ height: '50px' }} src={single}/>}
              type={selectedBlockType === BLOCK_TYPE.SINGLE ? 'primary' : 'default'}
            >
              {t('1Column')}
            </Button>
            <Button
              {...buttonProps}
              onClick={() => onBlockTypeClick(BLOCK_TYPE.DOUBLE)}
              icon={<img style={{ height: '50px' }} src={double}/>}
              type={selectedBlockType === BLOCK_TYPE.DOUBLE ? 'primary' : 'default'}
            >
              {t('2Columns')}
            </Button>
          </Space>
        </div>
      )}
      {selectedBlockType && (
        <>
          <br/>
          <hr/>
          <Form
            layout="vertical"
            onFinish={handleFinish}
            form={form}
            initialValues={
              modalType === ModalType.UPDATE ?
                block : {}
            }
          >
            <Form.Item
              label={t('TypeOfTitle')}
            >
              <Select
                defaultValue={selectedTitleId}
                size="large"
                onChange={(value, item) => {
                  setSelectedTitleId(value);
                }}
              >
                {allTitles?.map(title => (
                  <Select.Option key={title?.id} value={title?.id}>
                    <Title
                      title={title}
                      element={{ name: title?.name }}
                      showDescription={false}
                      noMargin
                    />
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="name"
              label={t('BlockTitle')}
            >
              <Input type="text" placeholder="Titre"/>
            </Form.Item>

            <Form.Item
              name={["settings", "textAlign"]}
              label="Alignement titre"
            >
              <Radio.Group defaultValue="left" buttonStyle="solid">
                <Radio.Button value="left"><AlignLeftOutlined /></Radio.Button>
                <Radio.Button value="center"><AlignCenterOutlined /></Radio.Button>
                <Radio.Button value="right"><AlignRightOutlined /></Radio.Button>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name="order"
              label={t('DisplayOrder')}
            >
              <Input type="number" placeholder="Ordre affichage parmi les blocs de la section"/>
            </Form.Item>


            <Divider>Bordure</Divider>
            <Checkbox checked={hasBorder} onChange={() => setHasBorder(!hasBorder)}>Bordure autour du bloc</Checkbox>

            {hasBorder && (
              <>
                <Form.Item
                  name={["settings", "borderColor"]}
                  label="Couleur bordure"
                >
                  <input type="color"/>
                </Form.Item>
                <Form.Item
                  name={["settings", "borderSize"]}
                  label="Épaisseur (en pixel)"
                >
                  <Input type="number" placeholder="1"/>
                </Form.Item>
                <Form.Item
                  name={["settings", "borderRadius"]}
                  label="Radius (si bords arrondis, en pixel)"
                >
                  <Input type="number" placeholder="1"/>
                </Form.Item>
              </>
            )}
            <Divider>Couleur de fond</Divider>
            <Form.Item
              name={["settings", "backgroundColor"]}
              label="Couleur de fond du bloc, hors titre"
            >
              <input type="color"/>
            </Form.Item>

            <Divider />

            <Form.Item>
              {modalType === ModalType.UPDATE && (
                <Space>
                  <Button htmlType="submit" type="primary" loading={loading}>
                    {t('Update')}
                  </Button>
                  <Popconfirm
                    title={t('SureOfDeletion')}
                    onConfirm={deleteBlock}
                    okText={t('general.yes')}
                    cancelText={t('general.no')}
                  >
                    <Tooltip title={t('Delete')}>
                      <Button shape="circle" type="danger" icon={<DeleteOutlined/>}/>
                    </Tooltip>
                  </Popconfirm>
                </Space>
              )}
              {modalType === ModalType.CREATE && (
                <Button htmlType="submit" type="primary" loading={loading}>
                  {t('general.add')}
                </Button>
              )}
            </Form.Item>
          </Form>
        </>
      )}
    </Modal>)
  );
};
