import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { QUERY_ELEMENT_BY_ID_WITH_USERPROPVALUE } from '@/shared/graphql/formations.js';
import FormationElement from '@/shared/pages/formations/components/FormationElement.jsx';
import { isAdmin } from '@/shared/utils/authority.js';
import { useQuery } from '@apollo/client';
import React from 'react';

export const TargetPredefinedInput = ({
  element,
  input,
  customFiles,
  setCustomFiles,
  isEditing = false
}) => {
  // Query element
  const { data, loading } = useQuery(QUERY_ELEMENT_BY_ID_WITH_USERPROPVALUE, {
    variables: { id: element?.objectId },
    fetchPolicy: 'no-cache',
    skip: !element?.objectId
  });
  const targetElement = data?.formationElement;

  if (!element?.objectId) {
    if (isAdmin()) {
      return <div>Erreur : element.objectId est vide, l'élément prédéfini a été supprimé</div>;
    }
    return <></>;
  }
  if (loading) {
    return <SpinnerCentered />;
  }
  if (targetElement === null) {
    if (isAdmin()) {
      return <div>Cet élément prédéfini a été supprimé</div>;
    }
    return <></>;
  }
  return (
    <FormationElement
      element={targetElement}
      input={input}
      customFiles={customFiles}
      setCustomFiles={setCustomFiles}
      isEditing={isEditing}
    />
  );
};
