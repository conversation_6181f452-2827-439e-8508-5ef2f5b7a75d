import { MUTATION_FINISH_FORMATION_STEP, QUERY_BLOCKS_IN_STEP_FORMATION } from '@/shared/graphql/formations.js'
import FormationElementsInBlock from '@/shared/pages/formations/components/FormationElementsInBlock.jsx'
import {
  CreateEditFormationBlockModal,
} from '@/shared/pages/formations/components/modal/CreateEditFormationBlockModal.jsx'
import { FormationContext } from '@/shared/pages/formations/context/FormationContext.jsx'
import { isAdmin } from '@/shared/utils/authority.js'
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js'
import { EditTwoTone, PlusCircleTwoTone } from '@ant-design/icons'
import { useMutation, useQuery } from '@apollo/client'
import { Alert, Button, Divider, notification, Typography } from 'antd'
import dayjs from 'dayjs';
import React, { useContext, useState } from 'react'
import { useTranslation } from 'react-i18next';

export default function({ refetch, formationId, sections }) {
  /* CONTEXT */
  const {t} = useTranslation();
  const {
    currentStepId, currentStep,
    currentProgress,
  } = useContext(FormationContext)

  const {
    loading,
    error,
    data,
    refetch: refetchBlocks,
  } = useQuery(QUERY_BLOCKS_IN_STEP_FORMATION, {
    fetchPolicy: 'cache-and-network',
    variables: {
      id: currentStepId,
    },
    skip: !currentStepId,
  })
  const blocks = data?.blocksInStep

  const [createBlockVisible, setCreateBlockVisible] = useState(false)
  const [updateVisible, setUpdateVisible] = useState(false)
  const [currentBlock, setCurrentBlock] = useState(null)

  const hasSections = sections?.length !== 0
  const canEdit = isAdmin()

  const canShowBlockCreation = isAdmin() && hasSections && currentStepId

  const refetchAll = () => {
    refetch()
    refetchBlocks()
  }

  const [finishStepMutation, { loading: loadingFinishStep }] = useMutation(MUTATION_FINISH_FORMATION_STEP)
  const handleFinishStep = async () => {
    try {
      const result = await finishStepMutation({
        variables: {
          formationStepId: currentStepId,
          formationId,
        },
      })
      const hasMoreSteps = result?.data?.finishFormationStep
      if (!hasMoreSteps) {
        notification.success({
          message: 'Vous avez terminé la formation !',
        })
      }
      refetch()
      // HERE set next current step
      // setCurrentStep()
      // setCurrentStepId(nextStep?.id)
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e)
      console.error(e)
    }
  }

  const currentStepProgress = currentProgress?.find(c => c?.formationStepId === currentStepId)


  const formationBlockCreation = (
    <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
      <div style={{ width: '90%' }}>
        <Button
          style={{ marginTop: 10, minHeight: '50px' }}
          type="dashed"
          block
          onClick={() => {
            setCreateBlockVisible(true)
          }}
        >
          <PlusCircleTwoTone style={{ fontSize: '1.1rem' }}/>
          {t('AddBlock')}
        </Button>
        <CreateEditFormationBlockModal
          isModalVisible={createBlockVisible}
          modalType="CREATE"
          formationStep={currentStep}
          closeModalHandler={() => {
            setCreateBlockVisible(false)
            refetchAll()
          }}
        />
      </div>
    </div>
  )

  const modalBlockUpdate = (
    <>
      {updateVisible && currentBlock && (
        <CreateEditFormationBlockModal
          isModalVisible={updateVisible}
          modalType="UPDATE"
          block={currentBlock}
          formationStep={currentStep}
          closeModalHandler={() => {
            setUpdateVisible(false)
            refetchAll()
          }}
        />
      )}
    </>
  )

  return (
    <React.Fragment>
      {blocks?.map(block => (
        <div key={block?.id}>
          <Typography.Title level={2}>
            {block?.name || ''}
            {canEdit && (
              <>
                <Button
                  type="text"
                  onClick={() => {
                    setUpdateVisible(true)
                    setCurrentBlock(block)
                  }}
                >
                  <EditTwoTone/>
                </Button>
              </>
            )}
          </Typography.Title>

          <FormationElementsInBlock
            refetch={refetch}
            sections={sections}
            formationId={formationId}
            block={block}
          />

          <Divider/>
        </div>
      ))}

      {canShowBlockCreation && formationBlockCreation}

      {modalBlockUpdate}

      <br/>
      <br/>
      {/* TODO move to parent */}
      {currentStep && currentStepProgress && (
        <>
          <Alert
            message={`Vous avez terminé cette partie ${dayjs(currentStepProgress.createdAt).fromNow()}`}
            type="info"
            closeText="Fermer"
          />
        </>
      )}

      {currentStep && !currentStepProgress && (
        <Button
          type="primary"
          size="large"
          block
          loading={loadingFinishStep}
          onClick={handleFinishStep}
        >
          {!currentStepProgress ? (
            <>
              J'ai terminé cette partie
            </>
          ) : (
            <>
              Étape suivante
            </>
          )}

        </Button>
      )}

      <br/>
      {blocks?.length > 0 && (
        <div style={{ marginTop: 20 }}>
          {/*
          <Typography.Title>
            Discussions
          </Typography.Title>

            <Commentaires
              id={cours.id}
              type={CommentairesType.COURS}
              refetch={refetch}
            />
          */}
        </div>
      )}

    </React.Fragment>
  )
}