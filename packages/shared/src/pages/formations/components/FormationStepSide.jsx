import { CreateEditFormationSectionModal } from '@/shared/pages/formations/components/modal/CreateEditFormationSectionModal.jsx';
import { CreateEditFormationStepModal } from '@/shared/pages/formations/components/modal/CreateEditFormationStepModal.jsx';
import { FormationContext } from '@/shared/pages/formations/context/FormationContext.jsx';
import { isAdmin } from '@/shared/utils/authority.js';
import { EditOutlined, PlusCircleTwoTone } from '@ant-design/icons';
import { Button, Steps } from 'antd';
import React, { useContext, useEffect, useState } from 'react';

export default function ({ section, refetch, formationId }) {
  /* CONTEXT */
  const {
    currentSectionId,
    setCurrentSectionId,
    currentStepId,
    setCurrentStepId,
    currentStep,
    setCurrentStep
  } = useContext(FormationContext);

  /* STATE */
  const [createVisible, setCreateVisible] = useState(false);
  const [createSubSectionVisible, setCreateSubSectionVisible] = useState(false);

  const [editVisible, setEditVisible] = useState(false);
  const [stepToEdit, setStepToEdit] = useState(null);

  const [currentStepKey, setCurrentStepKey] = useState(undefined);

  useEffect(() => {
    // find key from id
    const index = section?.steps?.findIndex((step) => step.id === currentStepId);
    setCurrentStepKey(index);
  }, [currentStepId]);

  const canEdit = isAdmin();
  return (
    <>
      <Steps
        current={currentStepKey}
        size="small"
        onChange={(step) => {
          //console.log({ step })
          // todo map step number to step id
          setCurrentStepKey(step);
        }}
        direction="vertical"
      >
        {section?.steps?.map((step, key) => (
          <Steps.Step
            onClick={() => {
              setCurrentStepId(step?.id);
              setCurrentStep(step);
            }}
            key={key}
            title={step?.name}
            description={
              <>
                {step?.description}&nbsp;
                {canEdit && (
                  <Button
                    icon={<EditOutlined />}
                    size="small"
                    onClick={() => {
                      setStepToEdit(step);
                      setEditVisible(true);
                    }}
                  />
                )}
              </>
            }
          />
        ))}
      </Steps>

      {/* CREATE STEP */}
      {canEdit && (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
          <div style={{ width: '90%' }}>
            <Button
              style={{ marginTop: 10, minHeight: '30px' }}
              type="dashed"
              size="small"
              block
              onClick={() => {
                setCreateVisible(true);
              }}
            >
              <PlusCircleTwoTone style={{ fontSize: '1.0rem' }} />
              Ajouter une étape
            </Button>
            <CreateEditFormationStepModal
              isModalVisible={createVisible}
              modalType="CREATE"
              sectionId={section?.id}
              closeModalHandler={() => {
                setCreateVisible(false);
                refetch();
              }}
            />
          </div>
        </div>
      )}

      {canEdit && (
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center'
          }}
        >
          <div style={{ width: '90%' }}>
            {/* Working for display but not with profile / next
            <Button
              style={{ marginTop: 10, minHeight: '30px' }}
              type="dashed"
              size={"small"}
              block
              onClick={() => {
                setCreateSubSectionVisible(true)
              }}
            >
              <PlusCircleTwoTone style={{ fontSize: '1.0rem' }}/>
              Ajouter une sous section
            </Button>
            */}
            {createSubSectionVisible && (
              <CreateEditFormationSectionModal
                isModalVisible={createSubSectionVisible}
                modalType="CREATE"
                formationId={formationId}
                parentSectionId={section?.id}
                closeModalHandler={() => {
                  setCreateSubSectionVisible(false);
                  refetch();
                }}
              />
            )}
          </div>
        </div>
      )}

      {canEdit && stepToEdit && (
        <CreateEditFormationStepModal
          isModalVisible={editVisible}
          modalType="UPDATE"
          formationStep={stepToEdit}
          formationId={formationId}
          closeModalHandler={() => {
            setEditVisible(false);
            refetch();
          }}
        />
      )}
    </>
  );
}
