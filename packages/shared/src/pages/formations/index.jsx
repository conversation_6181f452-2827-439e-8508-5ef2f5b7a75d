import { ErrorResult } from '@/shared/components/ErrorResult.jsx'
import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx'
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx'
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx'
import { QUERY_MY_FORMATIONS } from '@/shared/graphql/formations.js'
import { EditCreateCategoryModal } from '@/shared/pages/cours/components/EditCategorieCardModal.jsx'
import FormationCard from '@/shared/pages/formations/components/FormationCard.jsx'
import { CreateEditFormationModal } from '@/shared/pages/formations/components/modal/CreateEditFormationModal.jsx'
import { isAdmin } from '@/shared/utils/authority.js'
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js'
import { PlusCircleTwoTone } from '@ant-design/icons'
import { useQuery } from '@apollo/client'
import { Button, Col, Row } from 'antd'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next';

export default function(props) {
  const {t} = useTranslation();
  useEffectScrollTop()
  const { loading, error, data, refetch } = useQuery(QUERY_MY_FORMATIONS, {
    fetchPolicy: 'cache-and-network',
  })

  const formations = data?.myFormations
  const [createVisible, setCreateVisible] = useState(false)

  const formationCreation = (
    <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
      <div style={{ width: '80%' }}>
        <Button
          style={{ marginTop: 16, minHeight: '374px' }}
          type="dashed"
          block
          onClick={() => {
            setCreateVisible(true)
          }}
        >
          <PlusCircleTwoTone style={{ fontSize: '2.8rem' }}/>
          <br/>
          {t('Create')}
        </Button>
        <CreateEditFormationModal
          isModalVisible={createVisible}
          modalType="CREATE"
          closeModalHandler={() => {
            setCreateVisible(false)
            refetch()
          }}
        />
      </div>
    </div>
  )

  return (
    <>
      <FullMediParticlesBreadCrumb title={loading ? '' : (`Mes formations`)}/>
      <ExoteachLayout>
        <React.Fragment>
          {loading && <SpinnerCentered/>}

          {/* eslint-disable-next-line no-undef */}
          <Row justify="center" gutter={(MOBILE === 1) ? 0 : [8, 8]} type="flex" key="1">
            <>
              {/* Formations list */}
              {formations?.map((formation, index) => (
                <Col xl={6} lg={6} md={12} sm={12} xs={24} key={index}>
                  <FormationCard
                    formation={formation}
                    refetch={refetch}
                  />
                </Col>
              ))}
            </>
          </Row>

          {/* ERROOOOR :'( */}
          {!loading && error ? (<ErrorResult refetch={refetch} error={error}/>) : ''}

          {/* CREATE FORMATION */}
          {isAdmin() && formationCreation}

        </React.Fragment>
      </ExoteachLayout>
    </>
  )
}