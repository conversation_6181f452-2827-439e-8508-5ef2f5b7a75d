import { ExoUserLight } from '@/shared/components/User/ExoUserLight.jsx';
import SearchUser from '@/shared/components/User/SearchUser.jsx';
import { QUERY_ALL_GROUPS } from '@/shared/graphql/cours.js';
import { QUERY_ALL_FOLDERS } from '@/shared/graphql/folders.js';
import { QUERY_USER_IDS_IN_GROUP } from '@/shared/graphql/groupes.js';
import { CREATE_DISCUSSION, DELETE_DISCUSSION, UPDATE_DISCUSSION } from '@/shared/graphql/message.js';
import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager.jsx';
import { mapGroupsForTreeSelection } from '@/shared/services/groupes.js';
import { getUserInfo } from '@/shared/utils/authority.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button, Divider, Form,
  Input,
  message,
  Modal,
  Popconfirm, Space, Tag,
  Tooltip, TreeSelect,
} from 'antd';
import { useApolloClient, useMutation, useQuery } from '@apollo/client';
import React, { useEffect, useState } from 'react';
import { SmallErrorsAlert } from '@/shared/components/ErrorResult';
import { router } from 'umi';
import { useTranslation } from 'react-i18next';

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
};

const getMutationFromModalType = (modalType) => {
  switch (modalType) {
    case ModalType.CREATE:
      return CREATE_DISCUSSION;
    case ModalType.UPDATE:
      return UPDATE_DISCUSSION;
    default:
      return UPDATE_DISCUSSION;
  }
};

export const CreateEditGroupConversationModal = (
  {
    closeModalHandler,
    modalType,
    isModalVisible,
    discussion,
    refetch,
  }) => {
  const {t} = useTranslation();
  const [DiscussionMutation, { loading, data, error }] = useMutation(getMutationFromModalType(modalType));

  const client = useApolloClient();

  const [deleteDiscussion] = useMutation(DELETE_DISCUSSION);
  const [fileImage, setFileImage] = useState(null);
  const [form] = Form.useForm();
  const [customFields, setCustomFields] = useState([]);
  const [participants, setParticipants] = useState([]);
  const [showUserSearch, setShowUserSearch] = useState(false);
  const [showGroupSearch, setShowGroupSearch] = useState(false);

  /* Group related */
  const [selectedGroupId, setSelectedGroupId] = useState(false);
  const dataGroupes = useQuery(QUERY_ALL_GROUPS, { fetchPolicy: 'cache-and-network' });
  // All Folders query
  const {
    loading: loadingFolders,
    error: errorFolders,
    data: dataFolders,
    refetch: refetchFolders,
  } = useQuery(QUERY_ALL_FOLDERS, { fetchPolicy: 'cache-and-network' });
  const folders = dataFolders?.folders;

  const allGroupes = dataGroupes?.data?.allGroupes;
  const foldersIds = allGroupes?.map(gr => gr?.folderId);
  const foldersToShow = folders?.filter(f => foldersIds?.includes(f.id));
  const groupeTagRender = ({ label, value, closable, onClose, key }) => (
    <Tag value={value} key={key} color="geekblue" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
      {label}
    </Tag>
  );


  /* Set custom values for form */
  useEffect(() => {
    setParticipants(discussion?.participants?.map(u => u?.id) || []);
  }, [discussion]);

  // When delete discussion
  const onDelete = async () => {
    try {
      await deleteDiscussion({
        variables: {
          id: discussion?.id,
        },
      });
      router.push('/messages');
      closeModalHandler();
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  // When form is submitted (save)
  const handleFinish = async data => {
    try {
      let newdiscussion;
      if (fileImage) {
        newdiscussion = { ...data, image: fileImage };
      } else {
        newdiscussion = { ...data };
      }
      newdiscussion.participants = participants;
      newdiscussion.isGroup = true;
      let convId
      if (modalType === ModalType.UPDATE) {
        await DiscussionMutation({ variables: { id: discussion?.id, input: newdiscussion } });
        await closeModalHandler();
      } else { // Create
        const result = await DiscussionMutation({ variables: { input: newdiscussion } });
        convId = result?.data?.createDiscussion?.id
        if(convId) {
          router.push(`/messages/conversation/${convId}`);
        }
        await closeModalHandler();
      }
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      //message.error('Erreur serveur, veuillez réessayer')
    }
  };

  const onDeleteConvMember = (uId) => {
    setParticipants([...(participants.filter((id, k) => id !== uId))]);
  };

  return (
    (<Modal
      title={modalType === ModalType.UPDATE ? `${t('general.Edit')} ${discussion?.name}` : t('CreateGroupChat')}
      open={isModalVisible}
      onCancel={closeModalHandler}
      footer={null}
      closable
      confirmLoading={false}
      bodyStyle={{ paddingTop: 0 }}
      width={1000}
    >
      {/* Show small error(s) if needed */}
      <SmallErrorsAlert error={error} loading={loading}/>
      <Form
        layout="vertical"
        onFinish={handleFinish}
        form={form}
        initialValues={
          modalType === ModalType.UPDATE ?
            discussion : {}
        }
      >
        <Form.Item
          name="name"
          label={t('ChatName')}
          rules={[
            { min: 1, message: `Veuillez entrer le nom` },
          ]}
        >
          <Input type="text" placeholder={t('Title')}/>
        </Form.Item>

        <Divider>{t('Participants')} ({participants?.length})️</Divider>
        {participants?.map((userId, key) => (
          <div key={key} style={{ margin: 20 }}>
            <ExoUserLight id={userId}/>
            {userId !== getUserInfo()?.id && (
              <Tooltip
                title={t('general.Remove')}
              >
                <MinusCircleOutlined
                  className="dynamic-delete-button"
                  onClick={() => onDeleteConvMember(userId)}
                />
              </Tooltip>
            )}
          </div>
        ))}

        {!showGroupSearch && (
          <Form.Item>
            <Button
              type="dashed"
              block
              onClick={() => setShowUserSearch(!showUserSearch)}
              icon={showUserSearch ? null : <PlusOutlined/>}
            >
              {showUserSearch ? t('Cancel') : t('AddAParticipant')}
            </Button>
          </Form.Item>
        )}

        {!showUserSearch && (
          <Form.Item>
            <Button
              type="dashed"
              block
              onClick={() => setShowGroupSearch(!showGroupSearch)}
              icon={showGroupSearch ? null : <PlusOutlined/>}
            >
              {showGroupSearch ? t('Cancel') : t('AddAGroupOfParticipant')}
            </Button>
          </Form.Item>
        )}

        {showUserSearch && (
          <div style={{ marginBottom: '20px', textAlign: 'center' }}>
            <SearchUser
              onSelectUser={async (value, option) => {
                setParticipants([...participants, option?.key]);
              }}
            />
          </div>
        )}
        {showGroupSearch && (
          <div style={{ marginBottom: '20px', textAlign: 'center' }}>

            <AbstractGroupsManager
              treeCheckable={false}
              enableFolderChecking={false}
              onChange={(shouldAdd, groupId) => {
                setSelectedGroupId(groupId);
              }}
              showNumberOfUsers
            />

            {/*
            {allGroupes && foldersToShow && (
              <TreeSelect
                treeNodeFilterProp="title"
                showSearch
                placeholder="Choisir groupe..."
                treeData={mapGroupsForTreeSelection(foldersToShow, allGroupes)}
                style={{ width: '100%' }}
                onChange={async (newValue, label, extra) => {
                  const groupId = extra.triggerValue;
                  if (groupId.startsWith('folder')) {
                    message.error('Vous ne pouvez pas choisir un dossier, veuillez choisir un groupe');
                  } else {
                    setSelectedGroupId(groupId);
                  }
                }}
                tagRender={allGroupes && groupeTagRender}
              />
            )}
            */}

            <Form.Item>
              <br/>
              <Button
                block
                disabled={!selectedGroupId}
                type="primary"
                onClick={async () => {
                  // Get user ids in selected group
                  const { data: dataUserIds } = await client.query({
                    query: QUERY_USER_IDS_IN_GROUP,
                    variables: { groupId: selectedGroupId }, fetchPolicy: 'no-cache',
                  });
                  const userIds = dataUserIds?.getUserIdsInGroup;
                  if (userIds) {
                    // Deduplicate
                    setParticipants([...new Set([...participants, ...userIds])]);
                  }
                  setShowGroupSearch(false);
                  setSelectedGroupId(false);
                }}
              >
                Ajouter tous les membres de ce groupe à la discussion
              </Button>
            </Form.Item>

          </div>
        )}

        <Divider/>

        <Form.Item>
          {modalType === ModalType.UPDATE && (
            <Space>
              <Button htmlType="submit" type="primary" loading={loading}>
                {t('Update')}
              </Button>
              <Popconfirm
                title={t('SureOfDeletion')}
                onConfirm={onDelete}
                okText={t('general.yes')}
                cancelText={t('general.no')}
              >
                <Button danger>{t('DeleteChat')}</Button>
              </Popconfirm>
            </Space>
          )}

          {modalType === ModalType.CREATE && (
            <Button htmlType="submit" type="primary" loading={loading}>
              {t('Create')}
            </Button>
          )}
        </Form.Item>
      </Form>
    </Modal>)
  );
};
