import { useMutation, useQuery } from '@apollo/client';
import { GET_ME } from '@/shared/models/user';
import React, { useState } from 'react';
import { SEND_MESSAGE_IN_DISCUSSION } from '@/shared/graphql/message';
import { isMobile, showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { CustomEditor } from '@/shared/pages/messages/components/CustomEditor';

export default function ComposePrivateMessage({ discussionId, refetchMessages = () => {} }) {
  const [sendMessage, { loading, data, error }] = useMutation(SEND_MESSAGE_IN_DISCUSSION);
  const { data: dataMe } = useQuery(GET_ME, { fetchPolicy: 'cache-first' });
  const [editorContent, setEditorContent] = useState('');

  const [fileContent, setFileContent] = useState(null);
  const [fileImageContent, setFileImageContent] = useState(null);

  const [fileList, setFileList] = useState([]);

  const submitMessage = async () => {
    try {
      const newMessage = {
        discussionId,
        text: editorContent,
        file: fileContent,
        fileImage: fileImageContent,
        fileList
      };

      await sendMessage({ variables: { message: newMessage } });
      // await refetch()
      setEditorContent(''); // vide l'éditeur
      setFileList([]); // vide la liste des fichiers
      setFileContent(null);
      setFileImageContent(null);
      refetchMessages();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error({ e });
    }
  };

  return (
    <div
      style={
        isMobile
          ? {
              width: '100%',
              paddingBottom: 20,
              background: 'white',
              zIndex: 99
            }
          : {}
      }
    >
      <CustomEditor
        loading={loading}
        value={editorContent}
        onChange={(value) => {
          setEditorContent(value);
        }}
        onAddFile={(file, fileType) => {
          if (fileType === 'file') {
            setFileContent(file);
          } else if (fileType === 'fileImage') {
            setFileImageContent(file);
          }
        }}
        onSubmit={submitMessage}
        file={fileContent}
        fileImage={fileImageContent}
        fileList={fileList}
        setFileList={setFileList}
      />
    </div>
  );
}
