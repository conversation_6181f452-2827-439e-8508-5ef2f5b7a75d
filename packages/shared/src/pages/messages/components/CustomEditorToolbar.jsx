import { Divider, Popover, Tooltip } from 'antd';
import { Picker } from 'emoji-mart';
import 'emoji-mart/css/emoji-mart.css';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import {
  CaseSensitive,
  CircleArrowLeft,
  CircleArrowRight,
  CirclePlus,
  Send,
  SmilePlus,
  XCircle
} from 'lucide-react';
import { LoadingOutlined } from '@ant-design/icons';

export const CustomEditorToolbar = ({
  handleAddEmoji,
  emojisVisibles,
  setEmojisVisibles,
  setIsLoadingEmojis,
  isLoadingEmojis,
  onAddFile,
  fileImage,
  loadingFileUpload,
  file,
  loading,
  localFakeLoading,
  handleSubmit,
  toggleShowFileUploader,
  noPadding = false,
  hideUploader = false,
  hideSendButton = false
}) => {
  const { t } = useTranslation();

  const [showTextTools, setShowTextTools] = useState(false);
  const [toolbarWidth, setToolbarWidth] = useState(false);
  const [toolsDisplayed, setToolsDisplayed] = useState('');
  const [toolPageNumber, setToolPageNumber] = useState(1);
  const toolsContainerRef = useRef(null);

  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  const buttonStyle = { width: '24px' };
  const paginationStyle = {
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    zIndex: 1,
    outline: 'none'
  };

  const firstPageElementStyle = useMemo(() =>
    toolsDisplayed !== 'all' && toolsDisplayed !== 'page-1'
      ? {
          display: 'none',
          width: 0
        }
      : { display: 'flex' }
  );
  const secondPageElementStyle = useMemo(() =>
    toolsDisplayed !== 'all' && toolsDisplayed !== 'page-2'
      ? {
          display: 'none',
          width: 0
        }
      : { display: 'flex' }
  );
  const textToolbarStyle = useMemo(
    () =>
      showTextTools
        ? { display: 'flex', alignItems: 'center', height: 32 }
        : {
            display: 'none',
            width: 0
          },
    [showTextTools]
  );

  useEffect(() => {
    const updateWidth = () => {
      if (toolsContainerRef.current) {
        setToolbarWidth(toolsContainerRef.current.getBoundingClientRect().width);
      }
    };

    updateWidth();

    window.addEventListener('resize', updateWidth);

    return () => {
      window.removeEventListener('resize', updateWidth);
    };
  }, [showTextTools]);

  useEffect(() => {
    if (toolbarWidth >= 420) {
      setToolsDisplayed('all');
    } else if (toolbarWidth < 420) {
      setToolsDisplayed(`page-${toolPageNumber}`);
    }
  }, [toolbarWidth, toolPageNumber]);

  const renderUpload = (
    <Tooltip title={t('AddAFile')}>
      <CirclePlus
        height={24}
        width={24}
        style={{ fontSize: 24, ...buttonStyle }}
        className="custom-toolbar-button"
        loading={loadingFileUpload}
        onClick={() => toggleShowFileUploader()}
      />
    </Tooltip>
  );

  const renderEmojis = (
    <>
      <Tooltip title={t('general.Emojis')}>
        <Popover
          content={
            <Picker
              title={t('ChooseEmojis')}
              emoji="point_up"
              theme="light"
              onSelect={handleAddEmoji}
              enableFrequentEmojiSort
            />
          }
          trigger="click"
          open={emojisVisibles}
        >
          <div style={{ fontSize: 24, ...buttonStyle }}>
            <SmilePlus
              className="custom-toolbar-button"
              onClick={() => {
                setIsLoadingEmojis(!isLoadingEmojis);
                setEmojisVisibles(!emojisVisibles);
              }}
            />
          </div>
        </Popover>
      </Tooltip>
    </>
  );

  return (
    <div
      id="toolbar"
      className="custom-editor-toolbar"
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        background: 'none',
        borderBottom: 'none !important',
        padding: noPadding && 0
      }}
    >
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '1fr 30px',
          flex: 1,
          alignItems: 'center',
          justifyContent: 'space-between',
          maxHeight: 45,
          minHeight: 30
        }}
      >
        <div
          style={{
            display: showTextTools ? 'grid' : 'flex',
            gridTemplateColumns: '32px 1fr',
            alignItems: 'center',
            gap: showTextTools ? 4 : 8,
            maxWidth: 'calc(-66px + 100vw)'
          }}
          ref={toolsContainerRef}
        >
          {showTextTools && (
            <XCircle
              className="custom-toolbar-button"
              height={24}
              width={24}
              style={{ fontSize: 24, ...buttonStyle }}
              onClick={() => setShowTextTools(false)}
            />
          )}

          {!showTextTools && (
            <>
              {!hideUploader && <div style={{ width: 24, height: 24 }}>{renderUpload}</div>}
              <div style={{ fontSize: 36, ...buttonStyle, width: 36, height: 32 }}>
                <CaseSensitive
                  className="custom-toolbar-button"
                  onClick={() => setShowTextTools(true)}
                />
              </div>
              <div style={{ width: 24, height: 24 }}>
                <div style={buttonStyle}>{renderEmojis}</div>
              </div>
            </>
          )}

          <div style={textToolbarStyle}>
            {toolsDisplayed === 'page-2' && (
              <div style={buttonStyle}>
                <button style={{ ...paginationStyle }} onClick={() => setToolPageNumber(1)}>
                  <CircleArrowLeft width={20} height={20} />
                </button>
              </div>
            )}

            {/*<div style={secondPageElementStyle}>
              <select className="ql-size" style={{ width: 64 }}>
                <option className="ql-size" value="13px" />
                <option className="ql-size" value="14px" />
                <option className="ql-size" value="15px" />
                <option className="ql-size" value="16px" />
                <option className="ql-size" value="17px" />
                <option className="ql-size" value="18px" />
                <option className="ql-size" value="20px" />
                <option className="ql-size" value="24px" />
              </select>
            </div>*/}

            <div style={firstPageElementStyle}>
              <div style={buttonStyle}>
                <button className="ql-bold" />
              </div>
              <div style={buttonStyle}>
                <button className="ql-italic" />
              </div>
              <div style={buttonStyle}>
                <button className="ql-underline" />
              </div>

              <Divider type="vertical" style={{ height: 24, margin: '0 4px' }} />

              <div style={buttonStyle}>
                <select className="ql-color" />
              </div>
            </div>
            <div style={secondPageElementStyle}>
              <div style={buttonStyle}>
                <select className="ql-background" />
              </div>
            </div>

            <div style={secondPageElementStyle}>
              <Divider type="vertical" style={{ height: 24, margin: '0 4px' }} />

              <div style={buttonStyle}>
                <button className="ql-list" value="ordered" />
              </div>
            </div>
            <div style={firstPageElementStyle}>
              <div style={buttonStyle}>
                <button className="ql-list" value="bullet" />
              </div>
            </div>
            <div style={secondPageElementStyle}>
              <select className="ql-align">
                <option className="ql-align" />
                <option className="ql-align" value="center" />
                <option className="ql-align" value="right" />
                <option className="ql-align" value="justify" />
              </select>
            </div>

            <div style={firstPageElementStyle}>
              <Divider type="vertical" style={{ height: 24, margin: '0 4px' }} />

              <div style={buttonStyle}>
                <button className="ql-equation" />
              </div>
            </div>

            <div style={secondPageElementStyle}>
              <div style={buttonStyle}>
                <button className="ql-code-block" />
              </div>

              <div style={buttonStyle}>
                <button className="ql-blockquote" />
              </div>
            </div>

            <div style={firstPageElementStyle}>
              <div style={buttonStyle}>
                <button className="ql-link" />
              </div>
            </div>

            <div style={secondPageElementStyle}>
              <div style={buttonStyle}>
                <button className="ql-video" />
              </div>
            </div>

            <div style={firstPageElementStyle}>
              <Divider type="vertical" style={{ height: 24, margin: '0 4px' }} />

              <div style={buttonStyle}>
                <button className="ql-clean" />
              </div>
            </div>

            {toolsDisplayed === 'page-1' && (
              <div style={buttonStyle}>
                <button style={{ ...paginationStyle }} onClick={() => setToolPageNumber(2)}>
                  <CircleArrowRight width={20} height={20} />
                </button>
              </div>
            )}
          </div>
        </div>

        {!hideSendButton && (
          <button
            onClick={handleSubmit}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: primaryColor,
              borderRadius: '100%',
              height: 30,
              width: 30,
              padding: 0
            }}
          >
            {loading || localFakeLoading ? (
              <LoadingOutlined style={{ color: 'white' }} />
            ) : (
              <Send style={{ color: 'white', marginRight: 2, marginTop: 2 }} width={18} />
            )}
          </button>
        )}
      </div>
    </div>
  );
};
