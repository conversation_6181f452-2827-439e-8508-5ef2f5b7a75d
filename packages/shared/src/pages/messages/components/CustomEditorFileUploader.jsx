import { Badge, Upload } from 'antd';
import React, { useContext, useState } from 'react';
import { FilePlus } from 'lucide-react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { UploadedFilePreview } from '@/shared/components/Commentaires/UploadedFilePreview';

export function CustomEditorFileUploader({ fileList, setFileList }) {
  const [isHovered, setIsHovered] = useState(false);

  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  const uploadButton = () => {
    return (
      <button
        style={{ border: 0, background: 'none', cursor: 'pointer', lineHeight: 1 }}
        type="button"
      >
        {
          /*loading ? (
          <Spin indicator={<LoadingOutlined spin />} />
        )*/
          <span style={{ color: 'black', fontSize: 50 }}>
            <FilePlus />
          </span>
        }
      </button>
    );
  };

  const handleFileChange = (info) => {
    let newFileList = [...info.fileList];

    newFileList = newFileList.map((file) => {
      if (file.response) {
        file.url = file.response.url;
      } else if (!file.url) file.url = URL.createObjectURL(file.originFileObj);
      return file;
    });

    setFileList(newFileList);
  };

  const handleFileRemove = (file) => {
    setFileList((prevList) => prevList.filter((f) => f.uid !== file.uid));
  };

  return (
    <Badge count={fileList.length} offset={[-13, 13]} color={primaryColor}>
      <div
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          position: 'relative',
          minWidth: 100,
          minHeight: 100,
          border: '1px dashed #ccc',
          borderRadius: '8px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: isHovered ? '#f1f1f1' : '#fff',
          cursor: 'pointer',
          marginRight: 12,
          marginTop: 12
        }}
      >
        <Upload
          multiple
          onChange={handleFileChange}
          showUploadList={false}
          fileList={fileList}
          beforeUpload={() => false}
          maxCount={9}
        >
          <div
            style={{
              padding: 12,
              height: 100,
              width: 100,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {uploadButton()}
          </div>
        </Upload>
        {isHovered &&
          fileList.length > 0 &&
          fileList.map((file) => (
            <div
              key={file.uid}
              style={{ padding: 12, height: 100, display: 'flex', alignItems: 'center' }}
            >
              <UploadedFilePreview file={file} handleFileRemove={handleFileRemove} />
            </div>
          ))}
      </div>
    </Badge>
  );
}
