import FileAttached from '@/shared/components/Commentaires/FileAttached.jsx';
import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import ExoAvatar from '@/shared/components/User/ExoAvatar.jsx';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { downloadFile, FILE_TYPE } from '@/shared/services/file';
import { grey } from '@ant-design/colors';
import { CrownTwoTone, DeleteOutlined, LoadingOutlined } from '@ant-design/icons';
import { Button, Drawer, Image, Popconfirm, Spin, Tooltip } from 'antd';
import { FileDown } from 'lucide-react';
import React, { useContext, useState } from 'react';
import dayjs from 'dayjs';
import {
  DATE_FORMATS,
  displayDirectHtml,
  getUrlProtectedRessource,
  GlobalConfig,
  isMobile
} from '@/shared/utils/utils';
import { useTranslation } from 'react-i18next';
import { UserProfileCard } from '@/shared/components/User/UserProfileCard/UserProfileCard';
import { useLongPress } from 'use-long-press';

const Message = React.forwardRef((props, ref) => {
  const [showMobileActionBar, setShowMobileActionBar] = useState(false);
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;
  const onLongPress = useLongPress(() => {
    if (!isMessageDeleted && ((!isMine && isGroupDiscussionAdmin) || isMine) && isMobile) {
      setShowMobileActionBar(true);
    }
  });
  const { t } = useTranslation();
  const {
    data, // The message
    isMine,
    startsSequence,
    endsSequence,
    showTimestamp,
    isGroupDiscussion = false,
    discussionAuthorId,
    isGroupDiscussionAdmin = false,
    handleDelete = () => {
      /* Handles message delete */
    }
  } = props;

  const { file, fileImage, fileList } = data;

  // Gestion fichiers et images
  const allFilesAndImages = data?.fileList || [];
  // Liste des extensions d'images
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'];
  // Filtrer les fichiers uniquement images
  const imagesOnly = allFilesAndImages.filter((f) =>
    imageExtensions?.some((ext) => f?.file?.toLowerCase()?.endsWith(ext))
  );
  // Filtrer les autres fichiers (tout sauf les images)
  const filesOnly = allFilesAndImages.filter(
    (f) => !imageExtensions.some((ext) => f?.file?.toLowerCase()?.endsWith(ext))
  );
  const hasImage = imagesOnly.length > 0;
  const hasFile = filesOnly.length > 0;
  ////////////////////////////////////////

  const renderFileImage = () => (
    <>
      &nbsp;
      <Image
        width={180}
        src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + fileImage.file)}
      />
    </>
  );
  const renderFileAttached = () => <FileAttached file={file} />;
  const friendlyTimestamp = dayjs(data.createdAt).format(DATE_FORMATS.FULL_FR);

  const bubbleStyleGroup = isGroupDiscussion
    ? {
        marginLeft: !isMine ? '10px' : '',
        marginRight: isMine ? '10px' : ''
      }
    : {};

  const avatarStyleGroup = isGroupDiscussion
    ? {
        marginRight: !isMine ? '10px' : '',
        marginLeft: isMine ? '10px' : ''
      }
    : {};

  const user = data?.user;

  const avatar = (
    <UserProfileCard userId={user?.id} username={user?.username}>
      <ExoAvatar
        //isActive={user.isActive}
        avatar={user.avatar}
        style={{ alignSelf: 'center' }}
      />
    </UserProfileCard>
  );

  const deleteButton = (
    <Tooltip title={t('DeleteThisMessage')} key="comment-admin-delete-tooltip">
      <Popconfirm title={t('SureToDeleteMessage')} onConfirm={() => handleDelete(data?.id)}>
        <span key="delete" style={{ color: '#ff9595', cursor: 'pointer' }}>
          <DeleteOutlined />
        </span>
      </Popconfirm>
    </Tooltip>
  );

  const isMessageDeleted = data?.isDeleted;

  return (
    <div
      ref={ref}
      className={[
        'message',
        `${isMine ? 'mine' : ''}`,
        `${startsSequence ? 'start' : ''}`,
        `${endsSequence ? 'end' : ''}`
      ].join(' ')}
    >
      {showMobileActionBar && (
        <Drawer
          placement="bottom"
          size="default"
          closable={false}
          onClose={() => setShowMobileActionBar(false)}
          open={showMobileActionBar}
          style={{ userSelect: 'none' }}
        >
          <Tooltip title={t('DeleteThisMessage')} key="comment-admin-delete-tooltip">
            <Popconfirm title={t('SureToDeleteMessage')} onConfirm={() => handleDelete(data?.id)}>
              <Button key="delete" style={{ color: '#ff9595', cursor: 'pointer', width: '100%' }}>
                <DeleteOutlined /> {t('Delete')}
              </Button>
            </Popconfirm>
          </Tooltip>
        </Drawer>
      )}
      {showTimestamp && <div className="timestamp">{friendlyTimestamp}</div>}

      {isGroupDiscussion && startsSequence && !isMine && (
        <div className="msg-username-container">
          <div>{user?.username}&nbsp;</div>
          <div style={{ marginLeft: '5px' }}>
            {discussionAuthorId === user?.id && <CrownTwoTone twoToneColor={'#a08400'} />}
          </div>
        </div>
      )}

      <div className="bubble-container">
        {!isMine && isGroupDiscussion && <>{avatar}</>}

        <div style={{ alignSelf: 'center', marginRight: '6px' }}>
          {!isMessageDeleted && isMine && !isMobile && deleteButton}
        </div>

        <div
          className="bubble"
          title={friendlyTimestamp}
          style={bubbleStyleGroup}
          {...onLongPress()}
        >
          {isMessageDeleted ? (
            displayDirectHtml(`<i>${t('DeletedMessage')}</i>`)
          ) : (
            <>
              <RenderQuillHtml>{data.text}</RenderQuillHtml>
              {/*
              {fileImage && fileImage.file && renderFileImage()}
              {file && file.file && renderFileAttached()}
              */}
              {hasImage && (
                <div style={{ display: 'flex', alignItems: 'center', marginLeft: 40 }}>
                  <Image.PreviewGroup>
                    {imagesOnly.map((img, i) => (
                      <Image
                        key={i}
                        width={80}
                        height={80}
                        src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + img.file)}
                      />
                    ))}
                  </Image.PreviewGroup>
                </div>
              )}
              {hasFile &&
                filesOnly?.map((file, i) => (
                  <div
                    key={i}
                    style={{
                      display: 'grid',
                      alignItems: 'center',
                      padding: 8,
                      paddingRight: 12,
                      color: primaryColor,
                      backgroundColor: '#F6F7F7',
                      borderRadius: 12,
                      border: `1px solid ${grey[0]}50`,
                      width: 'fit-content',
                      gridTemplateColumns: '50px 1fr',
                      cursor: 'pointer',
                      marginLeft: 40
                    }}
                    onClick={async () => {
                      await downloadFile(FILE_TYPE.FILE, file.file);
                      return '';
                    }}
                  >
                    <span style={{ fontSize: 30, height: 30 }}>
                      <FileDown />
                    </span>
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
                      <span>{file.file}</span>
                    </div>
                  </div>
                ))}
            </>
          )}
        </div>

        <div style={{ alignSelf: 'center', marginLeft: '6px' }}>
          {!isMessageDeleted && !isMine && isGroupDiscussionAdmin && !isMobile && deleteButton}
        </div>
      </div>
    </div>
  );
});

export default Message;
