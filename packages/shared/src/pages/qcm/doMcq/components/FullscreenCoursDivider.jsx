import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils.js';
import { Avatar, Button, Tag } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

export const FullscreenCoursDivider = ({ linkedCoursArray, onValidate, loadingSectionChange }) => {
  const {t} = useTranslation();
  const linkedCours = linkedCoursArray?.length && linkedCoursArray[0];

  const categoryImage = (
    <Avatar.Group
      style={{
        display: 'inline-block',
        verticalAlign: 'middle',
        height: '100%',
      }}
    >
      <Avatar
        style={{
          width: '32px',
          height: '32px',
          lineHeight: '32px',
        }}
        src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + linkedCours?.ueCategory.image)}
      />
    </Avatar.Group>
  );

  return (
    <div style={{ textAlign: 'center' }}>
      <p>{t('NextQuestionWillBeAbout')}</p>

      {linkedCours?.ueCategory?.name && (
        <Tag
          style={{ marginTop: '5px', fontSize: '12px' }}
          color="blue"
        >
          {linkedCours?.ueCategory?.name} {linkedCours?.ueCategory?.image && categoryImage}
        </Tag>
      )}
      <br/>

      <h2>{linkedCours?.name}</h2>
      {linkedCours?.text && (
        <p>{linkedCours?.text}</p>
      )}
      <br/>
      <br/>
      <Button
        type="primary"
        block
        loading={loadingSectionChange}
        onClick={onValidate}
      >
        {t('Next')}
      </Button>
    </div>
  );
};