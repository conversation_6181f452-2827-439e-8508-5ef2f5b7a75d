import { QUERY_ELEMENTS_IN_HEADER_QCM } from '@/shared/graphql/cours.js';
import FormationEditableElement from '@/shared/pages/formations/components/FormationEditableElement.jsx';
import { FormationContextProvider } from '@/shared/pages/formations/context/FormationContext.jsx';
import { useQuery } from '@apollo/client';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function({ qcmId }) {
  const {t} = useTranslation();
  // Query elements header
  const {
    data: dataElements,
    error: errorElements,
    refetch: refetchElements,
  } = useQuery(QUERY_ELEMENTS_IN_HEADER_QCM, {
    fetchPolicy: 'no-cache',
    variables: { id: qcmId },
  });

  const elements = dataElements?.elementsInQcmHeader;

  const renderElement = (element, previousElement, nextElement, key, columnPosition) => (
    <FormationEditableElement
      style={{ transition: 'transform .35s ease-in-out' }}
      key={key}
      element={element}
      nextElement={nextElement}
      previousElement={previousElement}
      headerMcqId={qcmId}
      columnPosition={columnPosition}
      canEdit={false}
      refetchAll={() => {
      }}
    />
  );

  return (
    <FormationContextProvider>
      {elements?.map((elem, k) => (
        <div key={elem?.id}>
          {renderElement(elem, elements[k - 1], elements[k + 1], elem?.id)}
        </div>
      ))}
    </FormationContextProvider>
  );
}