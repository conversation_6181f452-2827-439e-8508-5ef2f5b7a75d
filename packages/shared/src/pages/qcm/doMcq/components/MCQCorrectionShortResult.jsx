import { GET_CORRECTION_SESSION_QCM, GET_CURRENT_SESSION_DETAILS } from '@/shared/graphql/qcm.js';
import { useQueryCurrentSessionDetails } from '@/shared/hooks/exercises/correction';
import ExerciseGeneratorResultCard from '@/shared/pages/qcm/components/correction/ExerciseGeneratorResultCard';
import { getQuestionsNumberFromQcm } from '@/shared/services/qcm.js';
import { useMathJaxScript } from '@/shared/utils/hooks/useScript.js';
import { GlobalConfig, isMobile } from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { Button, Card, Progress, Space, Spin } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, router } from 'umi';

export default function MCQCorrectionShortResult({ sessionId, userId, qcmId, handleResetMcq }) {
  const { t } = useTranslation();
  const isInSession = !!sessionId;
  const variables = { id: qcmId, sessionId, userId };

  const inExerciseSerie = !!qcmId;

  // Only if we have a parent question serie
  const { loading, error, data, refetch } = useQuery(GET_CORRECTION_SESSION_QCM, {
    variables,
    fetchPolicy: 'cache-and-network'
  });

  // Query current session by ID if sessionId is provided
  const { session, loadingSession } = useQueryCurrentSessionDetails({
    sessionId,
    isInSession,
    qcmId
  });

  const [questionsFiltered, setQuestionFiltered] = useState([]);

  const getQcm = () => data?.qcm;
  const qcm = data?.qcm;
  const getQuestions = () => (isInSession ? questionsFiltered : getQcm() && data.qcm.questions);

  const correctionConfig = data?.qcm?.correctionConfig;

  // Affiche rien si paramètre série parente reglée pour ne rien afficher
  const showNothing =
    inExerciseSerie &&
    !data?.qcm?.correctionConfig?.showCorrection &&
    !data?.qcm?.correctionConfig?.showNote &&
    !data?.qcm?.correctionConfig?.showMoyenne;

  const launchMathJax = useMathJaxScript();
  useEffect(() => {
    if (getQuestions() && getQuestions().length > 0 && !isInSession) {
      launchMathJax();
    }
  }, [data]);

  const isSmartMcq = getQcm()?.questionPickingStrategy === 'smart';

  const formatNote = (note) => parseFloat(note).toFixed(2);
  const moyenneQcm = () => getQcm()?.resultat && formatNote(getQcm().resultat.moyenne);
  const asDejaFaitQcm = () => getQcm()?.resultat && getQcm().resultat.note !== null;
  const getNombreQuestions = () => getQuestionsNumberFromQcm(data?.qcm);
  const maNote = () => {
    return formatNote(getQcm()?.resultat?.note);
  }; // From backend stats

  const surNombreQuestions = () => {
    const nb = getQuestionsNumberFromQcm(qcm);
    if (nb) {
      return `/ ${nb}`;
    }
    return '';
  };

  return (
    <>
      <Card title={t('general.Result')}>
        <div
          id="mcq-result"
          style={{ display: 'flex', justifyContent: 'center', flexGrow: 2, marginBottom: 10 }}
        >
          {showNothing && (
            <div style={{ textAlign: 'center' }}>
              <h2>
                {session?.settings?.infiniteQuestionByQuestion ? (
                  <>{t('CongratsYouCompletedAllQuestionsAvailable')}</>
                ) : (
                  <>{t('ThanksYourAnswersHaveBeenProcessed')}</>
                )}
              </h2>
            </div>
          )}

          {/* Dans résultat entraintemnet (générateur) affiche note finale */}
          {!inExerciseSerie && (
            <ExerciseGeneratorResultCard
              maxPoints={session?.maxPoints}
              myGrade={session?.result?.note}
              loadingSession={loadingSession}
              session={session}
            />
          )}

          {inExerciseSerie && correctionConfig?.showNote && (
            <>
              {isSmartMcq ? (
                <>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      flexGrow: 1,
                      textAlign: 'center'
                    }}
                  >
                    <div style={{ marginBottom: '5px' }}>{t('general.Grade')}</div>
                    <Progress
                      strokeColor={{
                        '0%': '#108ee9',
                        '100%': '#87d068'
                      }}
                      width={160}
                      type="circle"
                      percent={(maNote() / getNombreQuestions()) * 100}
                      format={(percent) => (
                        <span>
                          {maNote()} {surNombreQuestions()}
                        </span>
                      )}
                    />
                  </div>
                  {/*
                  <div style={{ display: 'flex', flexDirection: 'column', flexGrow: 1, textAlign: 'center' }}>
                    <div style={{ marginBottom: '5px' }}>Score</div>
                    <Progress
                      strokeColor={{
                        '0%': '#108ee9',
                        '100%': '#87d068',
                      }}
                      width={160}
                      type="circle"
                      percent={(session?.smartMcqScore?.score1)}
                      format={percent => <span style={{ fontSize: '24px' }}>{session?.smartMcqScore?.score1} / 100</span>}
                    />
                  </div>
                  */}
                </>
              ) : (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    flexGrow: 1,
                    textAlign: 'center'
                  }}
                >
                  <div style={{ marginBottom: '5px' }}>{t('yourGrade')}</div>
                  <Progress
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068'
                    }}
                    width={160}
                    type="circle"
                    percent={(maNote() / getNombreQuestions()) * 100}
                    format={(percent) => (
                      <span>
                        {maNote()} {surNombreQuestions()}
                      </span>
                    )}
                  />
                </div>
              )}
            </>
          )}

          {!isSmartMcq && correctionConfig?.showMoyenne && moyenneQcm() && (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                textAlign: 'center'
              }}
            >
              <div style={{ marginBottom: '5px' }}>{t('general.Average')}</div>
              <Progress
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068'
                }}
                width={160}
                type="circle"
                percent={(moyenneQcm() / getNombreQuestions()) * 100}
                format={(percent) => `${moyenneQcm()} ${surNombreQuestions()}`}
              />
            </div>
          )}
          {/*
            <Statistic title="Ma note" value={maNote()} suffix={surNombreQuestions()}/>
          */}
        </div>
        <br />
        {/*
        {hasNotePonderee && (
          <div>
            Indicateur pondéré de niveau :
            {' '}
            {notePonderee()}
          </div>
        )}
        */}
        <br />

        {isSmartMcq && (
          <>
            <br />
            <p>
              Ce test de diagnostic utilise un algorithme pour sélectionner en fonction de vos
              réponses les questions qui nous permettent de définir le mieux possible votre profil
              sur l'ensemble des sujets de cette thématique.
            </p>
            <br />
          </>
        )}

        {correctionConfig?.showNote && (
          <p>
            {t('YouDidThisExercise')} {t('at')} {dayjs(getQcm().resultat.date).format('DD/MM/YYYY')}
          </p>
        )}

        <br />
        {session?.settings?.infiniteQuestionByQuestion ? (
          <Link to={'/'}>
            <Button>{t('back')}</Button>
          </Link>
        ) : (
          <Space>
            {isMobile ? (
              <>
                <Button
                  style={{ height: 'auto' }}
                  type={'primary'}
                  onClick={() => router.push(`/qcm/correction/${qcmId}/session/${sessionId}/`)}
                >
                  {t('DisplayDetailedCorrection')}
                </Button>
              </>
            ) : (
              <>
                <a
                  target="_blank"
                  rel="noopener noreferrer"
                  href={`${GlobalConfig.get().baseWebsiteUrl}elearning/#/qcm/correction/${qcmId}/session/${sessionId}/`}
                >
                  <Button type={'primary'} style={{ height: 'auto' }}>
                    {t('DisplayDetailedCorrection')}
                  </Button>
                </a>
              </>
            )}

            <Button onClick={handleResetMcq}>{t('general.Redo')}</Button>
          </Space>
        )}
      </Card>
    </>
  );
}
