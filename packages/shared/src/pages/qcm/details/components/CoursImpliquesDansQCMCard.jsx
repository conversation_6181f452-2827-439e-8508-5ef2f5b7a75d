import { Popover, Statistic } from 'antd';
import React from 'react';
import { getColoredUEButtonStyle } from '@/shared/utils/utils';
import { FileListToDownload } from '@/shared/pages/cours/details/components/FileListToDownload';
import { FILE_TYPE } from '@/shared/services/file';

export const CoursImpliquesDansQCMCard = ({
  id,
  loading,
  name,
  title,
  description,
  idLien,
  refetch,
  cours,
  qcm,
  formatter,
  style
}) => {
  return (
    <Popover
      placement="bottom"
      content={
        <div style={{ width: 'auto', minWidth: '350px' }}>
          <FileListToDownload
            isDownloadable
            data={cours}
            fileType={FILE_TYPE.COURS}
            isCoursPdf
            isViewable
            buttonStyle={getColoredUEButtonStyle(qcm?.UE?.color, qcm?.UE?.color2)}
          />
        </div>
      }
    >
      <Statistic title={title} value={cours?.length} formatter={formatter} style={style} />
    </Popover>
  );
};
