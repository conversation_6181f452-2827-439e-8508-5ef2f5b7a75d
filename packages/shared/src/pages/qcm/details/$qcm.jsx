import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import React from 'react';
import { PageHeader } from '@ant-design/pro-layout';
import { router } from 'umi';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import { isMobile } from '@/shared/utils/utils';
import useMediaQuery from '@/shared/hooks/useMediaQuery';
import { useQuery } from '@apollo/client';
import { GET_DETAILS_QCM_ID } from '@/shared/graphql/qcm';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop';
import { McqInfosWrapper } from '@/shared/pages/qcm/components/McqInfosWrapper/McqInfosWrapper';
import { Button } from 'antd';
import { PencilLine } from 'lucide-react';
import { isAdmin, isTuteur } from '@/shared/utils/authority';

export default function QuestionSerieDetailsPage(props) {
  const qcmId = props.match.params.qcm;
  const canEdit = isAdmin() || isTuteur();

  useEffectScrollTop();

  const { loading, data } = useQuery(GET_DETAILS_QCM_ID, {
    fetchPolicy: 'no-cache',
    variables: { id: qcmId }
  });

  const qcmObject = data && data.qcm;
  const isSmallScreen = useMediaQuery('(max-width: 399px)');
  const titreQCM = () => qcmObject && qcmObject.titre;

  return (
    <>
      <FullMediParticlesBreadCrumb
        title={!loading ? `Série : ${titreQCM()}` : 'Chargement...'}
        actionButton={
          canEdit && (
            <>
              <Button
                shape="circle"
                ghost
                onClick={() => {
                  router.push('/admin-series/edit/' + qcmId);
                }}
                icon={<PencilLine />}
              />
            </>
          )
        }
      />
      <ExoteachLayout>
        <PageHeader onBack={() => router.goBack()} title="Retour" />
        <div style={{ padding: isMobile || isSmallScreen ? 12 : 24 }}>
          {loading ? (
            <SpinnerCentered />
          ) : (
            <McqInfosWrapper qcmId={qcmId} key={`mcq-infos-${qcmId}`} isSerieDetails />
          )}
        </div>
      </ExoteachLayout>
    </>
  );
}
