import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { QuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal.jsx';
import { CorrectionQuestionWithAnswers } from '@/shared/pages/qcm/components/QcmQuestion.jsx';
import {
  getChoixGeneratedQCMData,
  getGeneratedQCMData,
  getMaxPointsGeneratedQCM,
  getNoteGeneratedQCM,
  mapQuestionsModelToForm
} from '@/shared/services/qcm.js';
import { useMathJaxScript } from '@/shared/utils/hooks/useScript.js';
import { IS_DEV, scrollTop, tryParseJSONObject } from '@/shared/utils/utils.js';
import { Button, Card, Col, Form, Row, Space, Spin, Statistic } from 'antd';
import React, { useEffect, useState } from 'react';
import { Link } from 'umi';
import { useTranslation } from 'react-i18next';

//TODO voir pour delete ce fichier
export default function (props) {
  const { t } = useTranslation();
  const [generatedQcm, setGeneratedQcm] = useState(null);
  const getQuestions = () => generatedQcm && generatedQcm.questions;
  const [note, setNote] = useState(null);
  const [form] = Form.useForm();
  const renderLoading = () => (
    <div style={{ textAlign: 'center' }}>
      <Spin size="large" tip="Chargement..." />
    </div>
  );
  const launchMathJax = useMathJaxScript();

  const isQuestionsAnnalesFromCours = () => generatedQcm && generatedQcm.isAnnalesGenerated; // Viens de la génération des questions cours
  const coursName = () => generatedQcm && generatedQcm.cours && generatedQcm.cours.name;
  const getGlobalTitle = () =>
    isQuestionsAnnalesFromCours()
      ? `Correction annales du cours ${coursName()}`
      : t('GeneratedQuizzCorrection');

  const maxPoints = getMaxPointsGeneratedQCM();

  useEffect(() => {
    scrollTop();
    const generatedData = getGeneratedQCMData();
    const choix = getChoixGeneratedQCMData();
    let isGoodAlphanum = false;
    if (generatedData?.questions) {
      generatedData.questions = generatedData?.questions?.map((q) => {
        const isAlphanumerical = [
          QuestionAnswerType.NUMERICAL,
          QuestionAnswerType.ALPHANUMERICAL,
          QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL
        ].includes(q?.type);
        const qToMap = choix?.find((a) => String(a?.id_question) === String(q?.id_question));
        if (qToMap) {
          const qcmStatsQuestion = [];
          if (Array.isArray(qToMap?.answers)) {
            if (isAlphanumerical) {
              const idAnswer = q?.answers[0]?.id;
              qcmStatsQuestion.push({
                id: idAnswer, // not important
                answerId: idAnswer,
                value: qToMap?.answers,
                isGood: null
              });
              const arrayOfGoodAnswers = tryParseJSONObject(q?.answers[0]?.text);
              // Si qToMap?.answers inclue toutes les bonnes réponses (peut importe l'ordre) arrayOfGoodAnswers alors c'est bon
              isGoodAlphanum =
                arrayOfGoodAnswers?.length === qToMap?.answers?.length &&
                arrayOfGoodAnswers?.every((a) => qToMap?.answers?.includes(a));
            } else {
              for (const answer of qToMap?.answers) {
                qcmStatsQuestion.push({
                  id: answer, // not important
                  answerId: answer,
                  value: '1',
                  isGood: null
                });
              }
            }
          }
          if (Array.isArray(qToMap?.answers_false)) {
            for (const answer of qToMap?.answers_false) {
              qcmStatsQuestion.push({
                id: answer, // not important
                answerId: answer,
                value: '0',
                isGood: null
              });
            }
          }

          // Cas classique QCM, QCU, QROC
          let answersDataToReturn = qToMap.answers;

          // Cas alphanumérique
          if (isAlphanumerical && isGoodAlphanum) {
            // Cas où tout juste
            answersDataToReturn = q?.answers?.map((a) => a?.id);
          }

          return {
            ...q,
            answerHistory: {
              answersData: answersDataToReturn,
              // here we need:
              qcmStatsQuestion
              /*
              qcmStatsQuestion {
                id
                statsQuestionId
                answerId
                value
                isGood
              }
              */
            }
          };
        }
        return { ...q };
      });
    }

    if (IS_DEV) {
      console.log({ generatedData });
    }

    setGeneratedQcm(generatedData);
    // setGeneratedQcmAnswers(getChoixGeneratedQCMData())
    setNote(getNoteGeneratedQCM());
    form.setFieldsValue(mapQuestionsModelToForm(generatedData.questions));
  }, []);
  useEffect(() => {
    if (getQuestions() && getQuestions().length > 0) {
      launchMathJax(); // Load Mathjax seulement quand les questions sont loaded
    }
  }, [generatedQcm]);

  const surNombreQuestions = () => `/ ${maxPoints}`;

  const layoutQuestions = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 }
  };
  const correctionButtonLayout = {
    wrapperCol: { offset: 0, span: 0 },
    style: { textAlign: 'center' }
  };

  const renderDejaFaitQcm = () => (
    <Card title={t('general.Results')}>
      <div style={{ display: 'flex', justifyContent: 'center', flexGrow: 2, marginBottom: 20 }}>
        <div style={{ display: 'flex', flexDirection: 'column', flexGrow: 1, textAlign: 'center' }}>
          <Statistic
            title={t('MyGrade')}
            value={note}
            precision={2}
            suffix={surNombreQuestions()}
          />
        </div>
      </div>
      <br />
      <Space>
        <Link to="/generateurqcm/do">
          <Button>{t('general.Redo')}</Button>
        </Link>
        {isQuestionsAnnalesFromCours() ? (
          <Link to={`/cours/${generatedQcm.cours && generatedQcm.cours.id}`}>
            <Button>{t('BackToCourse')}</Button>
          </Link>
        ) : (
          <Link to="/qcm">
            <Button>{t('BackToGenerator')}</Button>
          </Link>
        )}
      </Space>
    </Card>
  );

  const renderQcmHeader = () => (
    <div>
      <h3>{getGlobalTitle()}</h3>
      <h5>
        {getQuestions().length} {t('general.questions')}
      </h5>
    </div>
  );

  const renderQuestions = () => (
    <Card title={t('Correction')}>
      <Form
        form={form}
        initialValues={mapQuestionsModelToForm(getQuestions()) || undefined}
        size="large"
      >
        {getQuestions().map((question, key) => (
          <CorrectionQuestionWithAnswers
            question={question}
            key={question.id}
            index={key}
            //userAnswersRaw={form.getFieldValue(question.id_question) || []}
            userAnswersRaw={
              mapQuestionsModelToForm(generatedQcm.questions)[question.id_question] || []
            }
            showYear
            isGenerated
            showEditionButton
            refetch={() => {
              //window.location.reload();
              // Il faudra refetch quand on aura generateur full session ID
            }}
          />
        ))}
      </Form>
    </Card>
  );

  return (
    <>
      <FullMediParticlesBreadCrumb title={getGlobalTitle()} />
      <ExoteachLayout>
        <Row gutter={MOBILE === 1 ? 0 : [8, 8]} justify="center" key="1">
          <Col xl={16} lg={20} md={24} sm={24} xs={24}>
            {getQuestions() && renderQcmHeader()}
            <br />
            {getQuestions() && renderDejaFaitQcm()}
            <br />
            {getQuestions() && renderQuestions()}
          </Col>
        </Row>
      </ExoteachLayout>
    </>
  );
}
