import {
  hierarchyComponantActions,
  HierarchySelecter,
  validesTypes
} from '@/shared/components/HierarchySelecter.jsx';
import { renderIcon } from '@/shared/pages/qcm/$index$.jsx';
import { tr } from '@/shared/services/translate.js';
import { Button } from 'antd';
import React from 'react';

const CoursSelectInUE = ({ mesUEs, ueId, onChange, defaultSettings = null }) => {
  //Query ue and cours associated to this ue

  const ue = mesUEs.find((ue) => ue.id === ueId);

  const [all, setAll] = React.useState(true);

  const [key, setKey] = React.useState(0);

  const [externalAction, setExternalAction] = React.useState(null);

  return (
    <div style={{ marginLeft: '12px' }}>
      <h4>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            width: 'auto'
          }}>
          <div>{ue?.image && renderIcon(ue?.image, 'ue', false, { display: 'block' })}</div>
          <div>
            {ue?.[tr('name')] || ue?.name}
            {ue?.[tr('description')] || ue?.description
              ? ` : ${ue?.[tr('description')] || ue?.description}`
              : ''}
            <Button
              type="link"
              onClick={() => {
                if (!all) {
                  setExternalAction(hierarchyComponantActions.CHECK_ALL);
                } else {
                  setExternalAction(hierarchyComponantActions.UNCHECK_ALL);
                }
                setAll(!all);
              }}
              size="small">
              {all ? 'Tout désélectionner' : 'Tout sélectionner'}
            </Button>
          </div>
        </div>
      </h4>
      <HierarchySelecter
        useTreeSelect
        isTreeSelectCheckable
        multiple
        forUeId={ueId}
        key={key + ueId}
        initAll={defaultSettings?.coursIds ? null : [validesTypes.CTYPE_COURS]}
        initialisationVariable={
          defaultSettings && defaultSettings?.coursIds
            ? { [validesTypes.CTYPE_COURS]: [...defaultSettings.coursIds] }
            : null
        }
        rankToRemoveIfLeaf={[validesTypes.CTYPE_UNKNOWN, validesTypes.CTYPE_PAGE]}
        externalAction={externalAction}
        resetExternalAction={() => {
          setExternalAction(null);
        }}
        setterHookSelection={onChange}
        additionalTreeProps={{
          placement: 'topLeft',
          listHeight: 500,
          style: { width: '99%' },
          popupMatchSelectWidth: true,
          treeLine: true,
          placeholder: 'Sélectionnez'
        }}
      />
    </div>
  );
};

export default CoursSelectInUE;
