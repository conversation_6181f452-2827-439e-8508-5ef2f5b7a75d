import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { AptoriaQcm, cardHeadStyleHome, toInt } from '@/shared/utils/utils.js';
import { gql, useMutation, useQuery } from '@apollo/client';
import { Button, Card, Col, Form, Input, Row, Space } from 'antd';
import React, { useState } from 'react';
import magnifyingGlassIcon from '@/shared/assets/loupe.svg';
import openBookIcon from '@/shared/assets/openbook.svg';
import { Link } from 'umi';
import router from 'umi/router';
import { useTranslation } from 'react-i18next';

const words2 = [
  {
    'page': 'la partie 1 du chapitre 1A (page 18 du livre papier) ?',

  },
  {
    'page': 'la partie 2 du chapitre 1A (page 25 du livre papier) ?',

  },
  {
    'page': 'la partie 3 du chapitre 1A (page 32 du livre papier) ?',

  },
  {
    'page': 'la partie 4 du chapitre 1A (page 36 du livre papier) ?',

  },
  {
    'page': 'la partie 5 du chapitre 1A (page 44 du livre papier) ?',

  },
  {
    'page': 'la partie 6 du chapitre 1A (page 58 du livre papier) ?',

  },
  {
    'page': 'la partie 7 du chapitre 1A (page 64 du livre papier) ?',
  },
  {
    'page': 'la partie 8 du chapitre 1A (page 70 du livre papier) ?',
  },
  {
    'page': 'la partie 2 du chapitre 1B (page 93 du livre papier) ?',
  },
  {
    'page': 'la partie 3 du chapitre 1B (page 101 du livre papier) ?',
  },
  {
    'page': 'la partie 4 du chapitre 1B (page 115 du livre papier) ?',
  },
  {
    'page': 'la partie 5 du chapitre 1B (page 121 du livre papier) ?',
  },
  {
    'page': 'la partie 6 du chapitre 1B (page 130 du livre papier) ?',
  },
  {
    'page': 'la partie 1 du chapitre 2 (page 143 du livre papier) ?',
  },
  {
    'page': 'la partie 4 du chapitre 2 (page 157 du livre papier) ?',
  },
  {
    'page': 'la partie 5 du chapitre 2 (page 163 du livre papier) ?',
  },
  {
    'page': 'la partie 6 du chapitre 2 (page 172 du livre papier) ?',
  },
  {
    'page': 'la partie 1 du chapitre 3 (page 193 du livre papier) ?',
  },
  {
    'page': 'la partie 2 du chapitre 3 (page 202 du livre papier) ?',
  },
  {
    'page': 'la partie 3 du chapitre 3 (page 209 du livre papier) ?',
  },
  {
    'page': 'la partie 5 du chapitre 3 (page 219 du livre papier) ?',
  },
  {
    'page': 'la partie 7 du chapitre 3 (page 227 du livre papier) ?',
  },
  {
    'page': 'la partie 9 du chapitre 3 (page 236 du livre papier) ?',
  },
  {
    'page': 'la partie 2 du chapitre 4 (page 252 du livre papier) ?',
  },
  {
    'page': 'la partie 3 du chapitre 4 (page 261 du livre papier) ?',
  },
  {
    'page': 'la partie 4 du chapitre 4 (page 265 du livre papier) ?',
  },
  {
    'page': 'la partie 6 du chapitre 4 (page 276 du livre papier) ?',
  },
  {
    'page': 'la partie 1 du chapitre 5 (page 286 du livre papier) ?',
  },
  {
    'page': 'la partie 4 du chapitre 5 (page 319 du livre papier) ?',
  },
  {
    'page': 'la partie 5 du chapitre 5 (page 327 du livre papier) ?',
  },
  {
    'page': 'la partie 1 du chapitre 6 (page 344 du livre papier) ?',

  },
  {
    'page': 'la partie 2 du chapitre 6 (page 352 du livre papier) ?',

  },
  {
    'page': 'la partie 3 du chapitre 6 (page 361 du livre papier) ?',

  },
  {
    'page': 'la partie 6 du chapitre 6 (page 401 du livre papier) ?',

  },
  {
    'page': 'la partie 7 du chapitre 6 (page 417 du livre papier) ?',

  },
  {
    'page': 'la partie 8 du chapitre 6 (page 423 du livre papier) ?',

  },
  {
    'page': 'la partie 9 du chapitre 6 (page 423 du livre papier) ?',

  },
  {
    'page': 'la partie 1 du chapitre 7 (page 448 du livre papier) ?',

  },
  {
    'page': 'la partie 2 du chapitre 7 (page 454 du livre papier) ?',

  },
  {
    'page': 'la partie 3 du chapitre 7 (page 468 du livre papier) ?',

  },
  {
    'page': 'la partie 4 du chapitre 7 (page 473 du livre papier) ?',

  },
  {
    'page': 'la partie 6 du chapitre 7 (page 484 du livre papier) ?',

  },
  {
    'page': 'la partie 7 du chapitre 7 (page 499 du livre papier) ?',
  },
  {
    'page': 'la partie 8 du chapitre 7 (page 527 du livre papier) ?',
  },
  {
    'page': 'la partie 1 du chapitre 8 (page 557 du livre papier) ?',

  },
  {
    'page': 'la partie 2 du chapitre 8 (page 567 du livre papier) ?',

  },
  {
    'page': 'la partie 4 du chapitre 8 (page 585 du livre papier) ?',

  },
  {
    'page': 'la partie 5 du chapitre 8 (page 588 du livre papier) ?',

  },
  {
    'page': 'la partie 1 du chapitre 9 (page 606 du livre papier) ?',

  },
  {
    'page': 'la partie 4 du chapitre 9 (page 645 du livre papier) ?',

  },
  {
    'page': 'la partie 2 du chapitre 10 (page 678 du livre papier) ?',
  },
  {
    'page': 'la partie 3 du chapitre 11 (page 706 du livre papier) ?',
  },
  {
    'page': 'la partie 2 du chapitre 12 (page 733 du livre papier) ?',
  },
  {
    'page': 'la partie 3 du chapitre 12 (page 747 du livre papier) ?',
  },
  {
    'page': 'la partie 4 du chapitre 12 (page 751 du livre papier) ?',
  },
];

function random_item(items) {
  return items[Math.floor(Math.random() * items.length)];
}

const groupLivreAssessmentId = 3;
const groupTesters = 4;
const isTester = (myGroups) => {
  return myGroups?.map(g => g.id)?.some(gId => String(gId) === String(groupTesters));
};

const UnlockWithBookView = ({ wantToUnlockWithBook, setWantToUnlockWithBook, refetch }) => {
  const [randomItem, setRandomItem] = useState(random_item(words2));
  const MUT_ACTIVATE_ASSESSMENT = gql`
      mutation activateAssessmentWithBook($page: String, $answer: String) {
          activateAssessmentWithBook(page: $page, answer: $answer)
      }
  `;
  const [activateAssessmentWithBook, { loading: loadingMut }] = useMutation(MUT_ACTIVATE_ASSESSMENT);

  const {t} = useTranslation();

  const [form] = Form.useForm();
  return (
    <>
      <Form
        form={form}
        onFinish={async (data) => {
          let { answer } = data;
          // Add to groupe diagnostic
          try {
            const result = await activateAssessmentWithBook({
              variables: {
                page: randomItem.page,
                answer: answer,
              },
            });

            if (result?.data?.activateAssessmentWithBook) {
              alert('Accès validé !');
            }
            if (!result?.data?.activateAssessmentWithBook) {
              alert('Le mot est incorrect');
              form.resetFields();
              setRandomItem(random_item(words2));
            }
            refetch();
          } catch (e) {
            form.resetFields();
            setRandomItem(random_item(words2));
            alert('une erreur est survenue, veuillez réessayer');
            console.error(e);
          }
        }}
        layout="vertical"
      >
        <Form.Item
          name="answer"
          label={`Quel est le dernier mot de l'encadré "Points clés à connaître" situé à la fin de ${randomItem.page} `}>
          <Input/>
        </Form.Item>

        <Button loading={loadingMut} htmlType="submit" type="primary" block>
          {t('general.Validate')}
        </Button>
      </Form>
      <br/>
      <Button block onClick={() => setWantToUnlockWithBook(false)}>{t('general.back')}</Button>
    </>
  );
};

const DiagnosticDisabledContent = ({ refetch }) => {
  const [wantToUnlockWithBook, setWantToUnlockWithBook] = useState(false);
  const {t} = useTranslation();
  const buttons = (
    <Space direction="vertical">
      <Button block size="large" type="primary" onClick={() => setWantToUnlockWithBook(true)}>
        {t('AlreadyHaveBook')}
      </Button>
      <a href="https://www.pearson.fr/fr/book/?gcoi=**************"
         target="_blank" rel="noopener noreferrer">
        <Button block size="large" type="primary">
          {t('IWantToBuyTheBook')}
        </Button>
      </a>
      <Button block size="large" type="primary" onClick={() => router.push('/account/forfait')}>
        {t('BuyDiagnosis')}
      </Button>
    </Space>
  );

  return (
    <>
      <div style={{ textAlign: 'center' }}>
        <img
          src={openBookIcon}
          alt={t('OpenBook')}
        />
        <br/><br/>
        <Row justify="center" type="flex" key="1" gutter={6}>
          <Col xl={10} lg={16} md={16} sm={24} xs={24}>
            <p>
              {t('ExplanationDiagnosis')}
            </p>
            {/*
            <p>
              L’accès au questionnaire est gratuit pendant deux mois si vous avez le manuel "Réussir l’examen AMF".
            </p>
            <p>
              Ce questionnaire contient 5 questions sur votre background et 80 questions techniques. Il vous permet de découvrir les principaux sujets abordés par le manuel et d’optimiser son utilisation.
            </p>
            */}
          </Col>
        </Row>
      </div>

      <Card
        style={{
          textAlign: 'center',
          display: 'flex',
          justifySelf: 'center',
          width: '500px',
          margin: 'auto',
          justifyContent: 'center',
          marginTop: '20px',
        }}
        headStyle={cardHeadStyleHome}
      >
        {wantToUnlockWithBook ? <UnlockWithBookView refetch={refetch} wantToUnlockWithBook={wantToUnlockWithBook}
                                                    setWantToUnlockWithBook={setWantToUnlockWithBook}/> : buttons}
      </Card>

    </>
  );
};

const DiagnosticEnabledContent = ({ me }) => {
  const {t} = useTranslation();
  const myBackground = me?.background;
  const hasDoneFirstPart = myBackground?.fonction;
  const myGroups = me?.groups;
  const isATester = isTester(myGroups);

  return (
    <>
      <div style={{ textAlign: 'center' }}>
        <img
          src={magnifyingGlassIcon}
        />
        <br/>
        <br/>
        <Row justify="center" type="flex" key="1" gutter={6}>
          <Col xl={10} lg={16} md={16} sm={24} xs={24}>
            <p>
              {/*
              Le questionnaire comprend une partie sur votre background professionnel et une partie avec 80 questions
              pour tester vos connaissances. Un questionnaire complémentaire de 40 questions vous permettra
              d’approfondir si vous le souhaitez le diagnostic.
              */}
              {t('ExplanationDiagnosis')}
            </p>
          </Col>
        </Row>
      </div>

      <br />
      <br />
      <Row justify="center" type="flex" key="1" gutter={6}>
        <Link to="/cours/ue/16">
          <Button size="large" type="primary">
            {t('StartTraining')}
          </Button>
        </Link>
      </Row>
    </>
  );
};
export default function(props) {
  const {t} = useTranslation();
  const GET_MY_GROUPS_AND_BACKGROUND = gql`
      {
          me {
              credits
              groups {
                  id
                  name
              }
              background
          }
      }
  `;
  const { data, loading, refetch, error } = useQuery(GET_MY_GROUPS_AND_BACKGROUND, { fetchPolicy: 'cache-and-network' });
  const me = data?.me;
  const myGroups = me?.groups;
  const hasAccessToAssesment = myGroups?.map(g => g.id)?.some(gId => String(gId) === String(groupLivreAssessmentId));

  return (
    <React.Fragment>
      <FullMediParticlesBreadCrumb title={t('Diagnostic')}/>
      <ExoteachLayout>
        <div style={{ marginTop: 20 }}>
          {hasAccessToAssesment ?
            <DiagnosticEnabledContent me={me}/>
            :
            <DiagnosticDisabledContent me={me} refetch={refetch}/>
          }
        </div>
      </ExoteachLayout>
    </React.Fragment>
  );
}