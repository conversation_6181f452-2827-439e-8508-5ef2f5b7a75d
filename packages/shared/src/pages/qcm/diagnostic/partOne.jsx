import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { questions } from '@/shared/pages/qcm/diagnostic/questionsPartOne.js';
import styles from '@/shared/pages/user/register-result/style.less';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { gql, useMutation } from '@apollo/client';
import { PageHeader } from '@ant-design/pro-layout';
import { Button, Card, Col, Form, Input, Result, Row, Select } from 'antd';
import React, { useState } from 'react';
import { router } from 'umi';
import { useTranslation } from 'react-i18next';

const preciser = 'Pouvez-vous préciser votre fonction actuelle ?\n';

const MUTATION_SET_USER_BACKGROUND = gql`
    mutation setUserBackground($input: JSON!) {
        setUserBackground(input: $input)
    }
`;

const HasDonePartOne = (props) => {
  const {t} = useTranslation();
  return (
    <>
      <Result
        className={styles.registerResult}
        status="success"
        title={t('ResultsSuccessfullySaved')}
      />
    </>
  );
};

const questionEtudiant = questions.find(q => q.question === 'Pouvez-vous préciser votre cursus actuel ?');
const questionAnciennete = questions.find(q => q.question === 'Quelle est votre ancienneté dans votre fonction ?');
const niveauFormationActuel = questions.find(q => q.question === 'Quel est votre niveau de formation actuel ?');
const lienBanqueFinance = questions.find(q => q.question === 'Cette formation a-t-elle un lien avec la banque et la finance ?');

export const DiagnosticPartOne = () => {
  const {t} = useTranslation();
  const [sendUserBackground, { loadingMut }] = useMutation(MUTATION_SET_USER_BACKGROUND);
  /* Dynamic Form data */
  const [typeSociete, setTypeSociete] = useState();
  const [fonction, setFonction] = useState();
  const [typeEtudiant, setTypeEtudiant] = useState();
  const [anciennete, setAnciennete] = useState();
  const [niveauFormation, setNiveauFormation] = useState();
  const [lienBanqueFinanceAnswer, setLienBanqueFinance] = useState();
  /* Send button */
  const [sendVisible, setSendVisible] = useState(false);
  const [hasFinished, setHasFinished] = useState(false);
  /* Questions */
  const questionFonction = questions.find(q => q.ifAnswered === typeSociete);

  const [form] = Form.useForm();
  return (
    <>
      <Row justify="center" key="1">
        <Col xl={16} lg={20} md={24} sm={24} xs={24}>
          {hasFinished ? <HasDonePartOne/> : (
            <Card>
              <Form
                form={form}
                layout="vertical"
                onFinish={async (values) => {
                  const userBackground = {
                    typeSociete: typeSociete === 'Autre' ? values.typeSocieteAutre : typeSociete,
                    fonction: fonction === 'Autre' ? values.fonctionAutre : fonction,
                    typeEtudiant: typeEtudiant === 'Autre' ? values.cursusEtudiantAutre : typeEtudiant,
                    anciennete,
                    niveauFormation,
                    lienBanqueFinance: lienBanqueFinanceAnswer,
                    precisionFormation: values.precisionFormation,
                  };
                  //console.log({ userBackground, values })
                  try {
                    const result = await sendUserBackground({ variables: { input: userBackground } });
                    if (result.data.setUserBackground === true) {
                      setHasFinished(true);
                    }
                  } catch (e) {
                    alert('Un problème est survenu veuillez réessayer');
                    console.error(e);
                  }
                }}
              >

                {/* Type de société */}
                <Form.Item name={questions[0].question} label={questions[0].question}>
                  <Select
                    placeholder={t('Select')}
                    onSelect={(item, a) => {
                      setTypeSociete(item);
                      if(item === "Autre") {
                        setFonction("Autre");
                      }
                      // setTypeSociete
                    }}
                  >
                    {(questions[0].answers.map(answer => (
                      <Select.Option key={answer} value={answer}>{answer}</Select.Option>
                    )))}
                  </Select>
                </Form.Item>

                {typeSociete === 'Autre' && (
                  <Form.Item name="typeSocieteAutre" label="Précisez le type de société">
                    <Input placeholder="Type de société" />
                  </Form.Item>
                )}


                {typeSociete && (
                  <>
                    {/* FONCTION ou Niveau d'étude (étudiant) */}
                    {typeSociete !== 'Autre' && (
                      <Form.Item name={questionFonction.question} label={questionFonction.question}>
                        <Select
                          placeholder="Sélectionnez..."
                          onSelect={(item, a) => {
                            setFonction(item);
                            // setTypeSociete
                          }}
                        >
                          {(questionFonction.answers.map(answer => (
                            <Select.Option key={answer} value={answer}>{answer}</Select.Option>
                          )))}
                        </Select>
                      </Form.Item>
                    )}
                    {(fonction === 'Autre' || typeSociete === 'Autre') && (
                      <Form.Item name="fonctionAutre"
                                 label={typeSociete === 'Etudiant' ? 'Précisez votre niveau d\'étude' : 'Précisez votre fonction'}>

                        <Input placeholder={typeSociete === 'Etudiant' ? 'Votre niveau d\'étude' : 'Votre fonction'}/>
                      </Form.Item>
                    )}

                    {/* Type étudiant */}
                    {typeSociete && typeSociete === 'Etudiant' && (
                      <>
                        {/* Pour quelle type de société .. */}
                        <Form.Item name={questionEtudiant.question} label={questionEtudiant.question}>
                          <Select
                            placeholder="Sélectionnez..."
                            onSelect={(item, a) => {
                              setTypeEtudiant(item);
                              // setTypeSociete
                            }}
                          >
                            {(questionEtudiant.answers.map(answer => (
                              <Select.Option key={answer} value={answer}>{answer}</Select.Option>
                            )))}
                          </Select>
                        </Form.Item>
                        {typeEtudiant === 'Autre' && (
                          <Form.Item name="cursusEtudiantAutre" label="Précisez votre cursus">
                            <Input placeholder="Votre cursus..."/>
                          </Form.Item>
                        )}
                      </>
                    )}
                  </>
                )}

                {/* Quelle est votre ancienneté dans votre fonction ?*/}
                {((typeSociete && typeSociete !== 'Etudiant')) && (
                  <>
                    <Form.Item name={questionAnciennete.question} label={questionAnciennete.question}>
                      <Select
                        virtual={false}
                        placeholder="Sélectionnez..."
                        onSelect={(item, a) => {
                          // setTypeEtudiant(item)
                          setAnciennete(item);
                        }}>
                        {(questionAnciennete.answers.map(answer => (
                          <Select.Option key={answer} value={answer}>{answer}</Select.Option>
                        )))}
                      </Select>
                    </Form.Item>

                  </>
                )}

                {/* Niveau formation actuel */}
                {fonction && typeSociete !== 'Etudiant' && (
                  <Form.Item name={niveauFormationActuel.question} label={niveauFormationActuel.question}>
                    <Select
                      placeholder="Sélectionnez..."
                      onSelect={(item, a) => {
                        setNiveauFormation(item);
                      }}
                    >
                      {(niveauFormationActuel.answers.map(answer => (
                        <Select.Option key={answer} value={answer}>{answer}</Select.Option>
                      )))}
                    </Select>
                  </Form.Item>
                )}


                {(fonction || typeEtudiant) && (
                  <>
                    <Form.Item name={lienBanqueFinance.question} label={lienBanqueFinance.question}>
                      <Select
                        placeholder="Sélectionnez..."
                        onSelect={(item, a) => {
                          setLienBanqueFinance(item);
                          setSendVisible(true);
                        }}>
                        {(lienBanqueFinance.answers.map(answer => (
                          <Select.Option key={answer} value={answer}>{answer}</Select.Option>
                        )))}
                      </Select>
                    </Form.Item>


                    <Form.Item label="Précisez votre formation" name="precisionFormation">
                      <Input/>
                    </Form.Item>

                  </>
                )}

                {sendVisible && (
                  <Button block loading={loadingMut} htmlType="submit" type="primary">
                    {t("send")}
                  </Button>
                )}

              </Form>
            </Card>
          )}
        </Col>
      </Row>

    </>
  );
};

export default function(props) {
  const {t} = useTranslation();
  useEffectScrollTop();

  return (
    <>
      <FullMediParticlesBreadCrumb title={t('DiagnosticPart1')}/>
      <ExoteachLayout>
        <div>
          <PageHeader
            onBack={() => router.goBack()}
            title={t('DiagnosticPart1')}
          />
        </div>
        <DiagnosticPartOne/>
      </ExoteachLayout>
    </>
  );
}