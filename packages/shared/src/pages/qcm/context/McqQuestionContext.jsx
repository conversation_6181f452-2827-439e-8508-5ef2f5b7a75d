import { Form } from 'antd'
import React, { useState } from 'react'

export const McqQuestionContext = React.createContext(undefined)
export const McqQuestionContextProvider = props => {
  const [currentSelection, setCurrentSelection] = useState([])
  const [form] = Form.useForm()

  return (
    <McqQuestionContext.Provider
      value={{ currentSelection, setCurrentSelection, form }}
    >
      {props.children}
    </McqQuestionContext.Provider>
  )
}