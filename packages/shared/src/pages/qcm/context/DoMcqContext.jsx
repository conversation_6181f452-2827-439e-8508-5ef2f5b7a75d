import { IS_DEV } from '@/shared/utils/utils.js';
import { Form } from 'antd';
import React, { useEffect, useState } from 'react';

export const DoMcqContext = React.createContext(undefined);
export const DoMcqContextProvider = (props) => {
  const [currentSelection, setCurrentSelection] = useState([]);
  const [form] = Form.useForm();

  // For mcq having true/false/undefined answers
  const [userChoicesByQuestion, setUserChoicesByQuestion] = useState([
    /*
    {
      id_question: 1,
      answers: [1, 2, 3],
      answers_false: [4],
      answers_undefined: [5], // Probably not needed
    }
    */
  ]);

  const checkIfExerciseIsFlashcardFilled = ({ structure, questionId }) => {
    /* fonction qui check si une structure est !(flashcard et non remplie) */
    if (structure && questionId) {
      throw new Error('Il faut avoir soit structure soit questionId, pas les deux');
    }

    let data = structure ? structure : functionHelperFindStructureWithQuestionId(questionId);

    const isFlashcard = data?.isFlashcard;
    if (!isFlashcard) {
      return { isFlashcard: false, isFilled: null, isOk: true };
    }

    const certainty = data?.certainty != null;
    const isFlashcardAnswer = data?.jsonAnswers?.autoEvaluation != null;

    if (!isFlashcardAnswer) {
      return { isFlashcard: true, isFilled: false, isOk: false };
    }
    return { isFlashcard: true, isFilled: true, isOk: true };
  };

  const checkAllUserAnswerFlashcard = () => {
    /* fonction qui va checker chaque élément de l'user response afin de check si c'est bien rempli */

    const resultOfAnalyseUserResponse = userChoicesByQuestion.map((node) =>
      checkIfExerciseIsFlashcardFilled({ structure: node })
    );

    const acc = resultOfAnalyseUserResponse.reduce((acc, obj) => {
      // Initialisation de l'accumulateur s'il est vide
      acc.isAllFlashcard = acc.isAllFlashcard ?? true;
      acc.isAllFlashcardFilled = acc.isAllFlashcardFilled ?? true;
      acc.isAllOk = acc.isAllOk ?? true;

      // Mise à jour des valeurs de l'accumulateur
      acc.isAllFlashcard = acc.isAllFlashcard && (obj?.isFlashcard ?? false);
      acc.isAllFlashcardFilled = acc.isAllFlashcardFilled && (obj?.isFilled ?? false);
      acc.isAllOk = acc.isAllOk && (obj?.isOk ?? false);

      return acc;
    }, {});

    return acc;
  };

  const functionHelperFindStructureWithQuestionId = (questionId) => {
    const structure = userChoicesByQuestion?.find((q) => q.id_question === questionId);
    if (!structure) {
      return {};
    }
    return structure;
  };

  const hookIsJsonAnswerFilled = (questionId) => {
    /* hook qui s'utilise comme ceci : hookIsJsonAnswerFilled(id_question), et vaudra false tant que userChoiceByQuestion[questionId].jsonAnswer === null, dès que ça change, update la value à true. */

    ////////// Init de la value
    // Regarde si la valeur est !==null && !== undefined => si c'est true, c'est que la question a étée remplie
    const defaultValue =
      userChoicesByQuestion.find((q) => q.id_question === questionId)?.jsonAnswers
        ?.autoEvaluation != null;

    // Init du state
    const [isJsonAnswerFilled, setIsJsonAnswerFilled] = useState(defaultValue);

    // maintain du state
    useEffect(() => {
      // Trouver la question correspondant à l'ID
      const updatedBool =
        userChoicesByQuestion.find((q) => q.id_question === questionId)?.jsonAnswers
          ?.autoEvaluation != null;
      setIsJsonAnswerFilled(updatedBool);
    }, [userChoicesByQuestion, questionId]);

    return isJsonAnswerFilled;
  };

  const hookIsUserAnswerFlashcardMetrics = () => {
    /* Hook qui permet de récupérer la structure retournée par checkAllUserAnswerFlashcard dès que c'est changé */

    const initValues = checkAllUserAnswerFlashcard();
    const [flashcardMetrics, setFlashcardMetrics] = useState(initValues);

    useEffect(() => {
      const actualizedValue = checkAllUserAnswerFlashcard();
      setFlashcardMetrics(actualizedValue);
    }, [userChoicesByQuestion]);

    return flashcardMetrics;
  };

  const checkIfStructureIsCertaintyFilledForFlashcardExercices = ({ questionId }) => {
    const data = functionHelperFindStructureWithQuestionId(questionId);

    const isFlashcard = data?.isFlashcard;
    const isCertaintyFilled = data?.certainty != null;

    const returnValue = isFlashcard && isCertaintyFilled;

    return returnValue;
  };

  const hookIsCertaintyFilledForFlashcardExercices = ({ questionId }) => {
    // Pour le moment, enable uniquement pour les flashcard, car c'est la seule structure qui utilise les userAnswers dans le context
    // Fonction qui returne si oui ou non une structure de question / une question associée à la questionId a sa certainty est filled ou pas

    const [returnValue, setReturnValue] = useState(false);

    useEffect(() => {
      const value = checkIfStructureIsCertaintyFilledForFlashcardExercices({ questionId });
      setReturnValue(value);
    }, [userChoicesByQuestion]);

    return returnValue;
  };

  const [shouldRestoreChoices, setShouldRestoreChoices] = useState(false);

  if (IS_DEV) {
    console.log('re-render DoMcqContext');
  }

  return (
    <DoMcqContext.Provider
      value={{
        currentSelection,
        setCurrentSelection,
        form,
        userChoicesByQuestion,
        shouldRestoreChoices,
        setShouldRestoreChoices,
        setUserChoicesByQuestion,
        checkAllUserAnswerFlashcard,
        checkIfExerciseIsFlashcardFilled,
        hookIsJsonAnswerFilled,
        hookIsUserAnswerFlashcardMetrics,
        functionHelperFindStructureWithQuestionId,
        hookIsCertaintyFilledForFlashcardExercices,
        checkIfStructureIsCertaintyFilledForFlashcardExercices
      }}
    >
      {props.children}
    </DoMcqContext.Provider>
  );
};
