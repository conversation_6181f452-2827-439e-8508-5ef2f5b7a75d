import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { QUERY_EXAM_QUESTION_SERIE_MY_STATS } from '@/shared/graphql/exam/exam_queries.js';
import { GET_CORRECTION_SESSION_QCM } from '@/shared/graphql/qcm.js';
import { useQueryCurrentSessionDetails } from '@/shared/hooks/exercises/correction.js';
import CorrectionQcm from '@/shared/pages/qcm/components/correction/CorrectionQcm.jsx';
import ExerciseGeneratorResultCard from '@/shared/pages/qcm/components/correction/ExerciseGeneratorResultCard.jsx';
import { getCleanFloatNumber, getNoteGeneralQcm, QuestionOrder } from '@/shared/services/qcm.js';
import { getUserInfo, isAdmin, isTuteur, isUser } from '@/shared/utils/authority.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { IS_DEV, isMobile } from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { Button, Col, FloatButton, Layout, Row, Spin } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import logoAptoria from '@/shared/assets/aptoria.png';
import { useTranslation } from 'react-i18next';
import { SerieResults } from '@/shared/pages/qcm/components/correction/SerieResults';
import { PencilLine } from 'lucide-react';
import { router } from 'umi';

export default function CorrectionPage(props) {
  useEffectScrollTop();
  const { t } = useTranslation();
  const qcmId = props.match.params.qcm;
  const { sessionId, action, userId, statId } = props.match.params;

  const canEdit = isAdmin() || isTuteur();
  const isInSession = !!sessionId;

  const inExerciseSerie = !!qcmId;

  /* QUERIES */

  // Query current session by ID if sessionId is provided
  const { session, loadingSession } = useQueryCurrentSessionDetails({
    sessionId,
    isInSession,
    qcmId
  });
  const isExam = !!session?.examQuestionSerieId;

  /* Query Correction, if qcmId provided. If not provided, questions are fetched from session (generator correction) */
  const { loading, error, data, refetch } = useQuery(GET_CORRECTION_SESSION_QCM, {
    variables: {
      id: qcmId,
      sessionId,
      userId,
      /*
      groupIds: [
        ...selectedGroupIds
        //...selectedUserIds.map((g) => parseInt(g.id)),
        //userId ? userId : me.id
      ],
      */
      statId
    },
    fetchPolicy: 'no-cache',
    skip: !qcmId
  });
  const exerciseSerie = data?.qcm;

  /* Query Exam stats if exam */
  const {
    loading: loadingExamStats,
    error: errorExamStats,
    data: dataExamStats,
    refetch: refetchExamStats
  } = useQuery(QUERY_EXAM_QUESTION_SERIE_MY_STATS, {
    variables: { id: session?.examQuestionSerieId, sessionId: session?.id },
    fetchPolicy: 'no-cache',
    skip: !isExam
  });
  const questionSerie = dataExamStats?.examQuestionSerie;

  /* END QUERIES */

  const pdfRef = React.createRef();
  const [questionsFiltered, setQuestionFiltered] = useState([]);
  const [selectedQuestionOrder, setSelectedQuestionOrder] = useState(
    isInSession ? QuestionOrder.SESSION_DEFINED : QuestionOrder.DEFAULT
  );

  const exercises = isInSession ? questionsFiltered : exerciseSerie?.questions;

  const isSmartMcq = exerciseSerie?.questionPickingStrategy === 'smart';
  const correctionConfig = exerciseSerie?.correctionConfig;
  const showNothing =
    !exerciseSerie?.correctionConfig?.showCorrection &&
    !exerciseSerie?.correctionConfig?.showNote &&
    !exerciseSerie?.correctionConfig?.showMoyenne;

  const orderQuestions = (orderType) => {
    // WIP
    const order = {};
    let questionsOrder = session?.questionsIdsDone || [];
    let questions;
    switch (orderType) {
      case QuestionOrder.DEFAULT:
        break;
      case QuestionOrder.SESSION_DEFINED:
        // reorder
        //TODO virer normalement plus besoin (à vérifier pour serie predefinie session defined)
        questionsOrder.forEach((a, i) => {
          order[a] = i;
        });
        if (isSmartMcq) {
          questions = exerciseSerie?.questions?.filter((q) =>
            questionsOrder?.includes(q.id_question)
          );
        } else {
          questions = JSON.parse(JSON.stringify(data.qcm.questions));
        }
        questions.sort((a, b) => order[a.id_question] - order[b.id_question]);
        setQuestionFiltered(questions);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    // Dans une série d'exercice, avec session
    if (exerciseSerie?.questions?.length > 0 && session) {
      orderQuestions(QuestionOrder.SESSION_DEFINED);
    }
    // Depuis Générateur d'exercices ou entraînement => pas de série mais exercices dans session
    if (!exerciseSerie && session?.questionsDone?.length > 0) {
      setQuestionFiltered(session?.questionsDone);
    }
  }, [data, session]);

  const formatNote = (note) => getCleanFloatNumber(note);

  const asDejaFaitQcm = () => exerciseSerie?.resultat && exerciseSerie?.resultat?.note !== null;
  const hasResults = exerciseSerie?.resultat?.moyenne !== null;
  const getQcmTitle = () => {
    if (inExerciseSerie) {
      return exerciseSerie?.titre;
    }
    return t('Training');
  };
  const hasRedoneMCQ = action && action === 'notredone';
  const maNote = () => {
    if (!hasRedoneMCQ) {
      // qcm pas refait (ou session), affiche ancienne note
      if (isExam) {
        return formatNote((exerciseSerie?.resultat?.note / exerciseSerie?.maximumPoints) * 20);
      }
      return formatNote(exerciseSerie?.resultat?.note);
    }
    return formatNote(getNoteGeneralQcm());
  }; // From backend stats

  // Navigation
  const [targetOffset, setTargetOffset] = useState(undefined);
  const [loadingPdf, setLoadingPdf] = useState(false);
  const refContent = useRef();
  const refCorrection = useRef();

  const isAdminOrTutor = isAdmin() || isTuteur();
  const currentUserHasUserRole = isUser();
  const canSeeGrade = (currentUserHasUserRole && correctionConfig?.showNote) || isAdminOrTutor;
  const canSeeAverage = (currentUserHasUserRole && correctionConfig?.showMoyenne) || isAdminOrTutor;

  // const casSeeCorrection = (currentUserHasUserRole && serie?.correctionConfig?.showCorrection) || isAdminOrTutor;
  const showCorrection =
    (exercises && correctionConfig?.showCorrection) || session?.questionsDone?.length > 0;

  return (
    <>
      <FullMediParticlesBreadCrumb
        title={(loading && t('Loading...')) || `${getQcmTitle()} - ${t('general.done')}`}
        subtitle={(loading && '') || exerciseSerie?.description}
        image={loading ? null : exerciseSerie?.UE?.image}
        actionButton={
          canEdit && (
            <>
              <Button
                shape="circle"
                ghost
                onClick={() => {
                  router.push('/admin-series/edit/' + qcmId);
                }}
                icon={<PencilLine />}
              />
            </>
          )
        }
      />
      <Layout>
        {/*showSiderMenu && (
          <SiderMenu targetOffset={targetOffset} shouldShowAnalysis={shouldShowAnalysis} />
        )*/}
        <Layout style={isMobile ? { width: '100vw' } : { padding: '0 12px 12px' }} id="mcq-content">
          <Layout.Content ref={refContent}>
            <ExoteachLayout>
              {/* eslint-disable-next-line no-undef */}
              <Row justify="center" gutter={MOBILE === 1 ? 0 : [1, 1]} key="1">
                <Col xl={22} lg={22} md={24} sm={24} xs={24}>
                  {loading && (
                    <div style={{ textAlign: 'center' }}>
                      <Spin size="large" tip="Chargement..." />
                    </div>
                  )}

                  {/* Div pour export PDF (demande Aptoria), sera sûrement refait côté back car pas un peu buggy côté front */}
                  <div id="content-pdf" ref={pdfRef}>
                    {loadingPdf && (
                      <div style={{ textAlign: 'center', margin: 20 }}>
                        <img src={logoAptoria} alt="logo" width={200} />
                      </div>
                    )}

                    {/* Dans le résultat entrainement (générateur) affiche note finale */}
                    {!inExerciseSerie && (
                      <ExerciseGeneratorResultCard
                        maxPoints={session?.maxPoints}
                        myGrade={session?.result?.note}
                        loadingSession={loadingSession}
                        session={session}
                      />
                    )}

                    {/* Dans série exercice: card résumé du résultat(s) (avec moyenne etc.) */}
                    {exerciseSerie && (
                      <SerieResults
                        serie={exerciseSerie}
                        userId={userId}
                        sessionId={sessionId}
                        statId={statId}
                        canSeeGrade={canSeeGrade}
                        canSeeAverage={canSeeAverage}
                      />
                    )}
                    <br />
                  </div>
                  <br />
                  {/* CORRECTION */}
                  {showCorrection && (
                    <div id="correction-qcm" ref={refCorrection}>
                      <CorrectionQcm
                        qcm={exerciseSerie}
                        questions={exercises}
                        setTargetOffset={setTargetOffset}
                        hasRedoneMCQ={false} // Not used anymore
                        isInSession={isInSession}
                        refetch={refetch}
                        showEditionButton
                      />
                    </div>
                  )}
                  <br />
                </Col>
              </Row>
              <FloatButton.BackTop />
            </ExoteachLayout>
          </Layout.Content>
        </Layout>
      </Layout>
    </>
  );
}
