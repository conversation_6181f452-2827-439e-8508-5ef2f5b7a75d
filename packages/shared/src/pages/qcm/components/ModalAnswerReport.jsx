import { ChoosePostType } from '@/shared/components/Commentaires/ChoosePostType.jsx';
import PostTypeTag from '@/shared/components/Commentaires/PostTypeTag.jsx';
import { CommentairesType } from '@/shared/services/commentaires.js';
import Modal from 'antd/es/modal';
import React, { useState } from 'react';
import CreatePost from '@/components/Commentaires/CreatePost.jsx';
import { useTranslation } from 'react-i18next';
import { LimitationQuestionLayer } from '@/shared/components/LimitationQuestionLayer';

export default function ModalAnswerReport({
  setQuestionModalVisible,
  questionModalVisible,
  questionNumber,
  answerAlphabetValue,
  answer = null, // pass answer or question
  question = null,
  onFinish
}) {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [questionTypeId, setQuestionTypeId] = useState(undefined);

  const [currentPostType, setCurrentPostType] = useState(undefined);

  const onSelectPostType = (postType) => {
    setQuestionTypeId(postType.id);
    setCurrentPostType(postType);
    setCurrentStep(2);
  };

  const fullQuestionNumber = `${questionNumber}${answerAlphabetValue ? answerAlphabetValue : ''}`;
  return (
    <Modal
      footer={null}
      onCancel={() => setQuestionModalVisible(false)}
      open={questionModalVisible}
      closable
      confirmLoading={false}
      title={`${t('ReportErrorOrAskQuestionFor')} ${fullQuestionNumber}`}
      style={{
        marginBottom: 10,
        marginLeft: 'auto',
        marginRight: 'auto'
      }}
    >
      {currentStep === 0 && (
        <LimitationQuestionLayer
          onLimiteValidation={() => {
            setCurrentStep(1);
          }}
        />
      )}
      {currentStep === 1 && (
        <ChoosePostType onSelectPostType={onSelectPostType} type={CommentairesType.QCM} />
      )}
      {currentStep === 2 && (
        <>
          <div style={{ fontSize: '15px', color: 'grey' }}>
            {t('CheckExistingQuestion')}{' '}
            <span role="img" aria-label="emoji">
              😊
            </span>
          </div>
          <br />
          <PostTypeTag postType={currentPostType} />
          <br />
          <CreatePost
            type={answer ? CommentairesType.QUESTION_ANSWER : CommentairesType.EXERCISE}
            refetch={() => {
              // refetch
            }}
            defaultTitle=""
            placeholder={`${t('ExplainQuestionProblem')} ${fullQuestionNumber}`}
            typeId={answer ? answer.id : question.id_question}
            currentPostType={currentPostType}
            parentId={null}
            callScroll={() => {}}
            withTitle
            afterSubmit={() => {
              onFinish();
              setQuestionModalVisible(false);
              setCurrentStep(0);
            }}
          />
        </>
      )}
    </Modal>
  );
}
