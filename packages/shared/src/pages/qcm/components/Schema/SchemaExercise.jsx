import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';
import { QUERY_SCHEMA_BY_ID } from '@/shared/graphql/schemas';
import { SchemaExerciseModes } from '@/shared/pages/admin/qcm/components/modal/components/Schemas/EditSchemaPointAndClick';
import { QuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal';
import { ExoSchemaDefaultOpacity } from '@/shared/pages/admin/schemas/components/constants';
import { FillInLegendsCircles } from '@/shared/pages/admin/schemas/components/FillInLegends/FillInLegendsCircles';
import LegendsRender from '@/shared/pages/admin/schemas/components/Layers/LegendsRender';
import { URLImage } from '@/shared/pages/admin/schemas/components/URLImage';
import ContextMenu from '@/shared/pages/qcm/components/Schema/ContextMenu';
import { HaloCircle } from '@/shared/pages/qcm/components/Schema/HaloCircle';
import RenderLine from '@/shared/pages/qcm/components/Schema/RenderLine';
import FillInLegendsList from '@/shared/pages/qcm/components/Schema/SchemaFillInLegends/FillInLegendsList';
import LegendsListCorrectionFillInLegends from '@/shared/pages/qcm/components/Schema/SchemaFillInLegends/LegendsListCorrectionFillInLegends';
import LegendsListCorrection from '@/shared/pages/qcm/components/Schema/SchemaPointAndClick/LegendsListCorrection';
import PointAndClickLegendsList from '@/shared/pages/qcm/components/Schema/SchemaPointAndClick/PointAndClickLegendsList';
import ZoomControls from '@/shared/pages/qcm/components/Schema/ZoomControls';
import { DoMcqContext } from '@/shared/pages/qcm/context/DoMcqContext';
import { getUrlProtectedRessource, GlobalConfig, IS_DEV } from '@/shared/utils/utils';
import { useQuery } from '@apollo/client';
import { Alert, Avatar, Button, Drawer, Input, Modal, Space } from 'antd';
import React, { useContext, useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Layer, Stage } from 'react-konva';
import { isMobile } from '@/shared/utils/utils.js';

export default function SchemaExercise({
  question,
  form,
  refetch: refetchParent,
  correction = false,
  questionNumber,
  printable,
  hideComments,
  mobileChoices = null
}) {
  const { t } = useTranslation();
  const isSchemaPointAndClick = question?.type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK;
  const isSchemaFillInLegends = question?.type === QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS;

  const {
    userChoicesByQuestion,
    setUserChoicesByQuestion,
    shouldRestoreChoices,
    setShouldRestoreChoices
  } = useContext(DoMcqContext);

  // Correction only
  const jsonAnswers = question?.answerHistory?.jsonAnswers || [];

  // Query le schema
  const { data, loading, error, refetch } = useQuery(QUERY_SCHEMA_BY_ID, {
    variables: {
      id: question.schemaLibraryId
    },
    skip: !question.schemaLibraryId,
    fetchPolicy: 'no-cache'
  });

  const schema = data?.schema;
  const legends = schema?.legends || [];
  const texts = schema?.text || [];
  const lines = schema?.lines || [];
  const image = schema?.image;
  const exerciseSettings = question?.settings || {};
  const mode = question?.type;

  const legendsAsked = legends.filter((legend) =>
    exerciseSettings?.checkedLegends?.includes(legend.id)
  );
  const stageRef = useRef(null);

  const MOBILE_MAX_HEIGHT_RATIO = 0.58;
  const MOBILE_MAX_WIDTH_RATIO = correction ? 0.9 : 0.98;

  const DESKTOP_MAX_HEIGHT_RATIO = 0.74;
  const DESKTOP_MAX_WIDTH_RATIO = 0.67;

  const [settings, setSettings] = useState({
    stageWidth: 800,
    stageHeight: 600,
    scaleX: 1,
    scaleY: 1
  });
  const [points, setPoints] = useState([]);
  const [imageSizeState, setImageSizeState] = useState({ width: 0, height: 0 });
  const [selectedLegendId, setSelectedLegendId] = useState(null);
  const [contextMenuPos, setContextMenuPos] = useState({ x: null, y: null });
  const [isCorrectionPointsSet, setIsCorrectionPointsSet] = useState(false);
  const [hasPositionedMobilePoints, setHasPositionedMobilePoints] = useState(false);

  // Mode fill in legends
  const [editingLegendId, setEditingLegendId] = useState(null);

  // Correction only: set points from jsonAnswers
  useEffect(() => {
    if (correction && !isCorrectionPointsSet) {
      if (isSchemaPointAndClick) {
        // SETUP CORRECTION POINT AND CLICK
        // prevent infinite loop for preview only
        // Bonnes legendes en vert et mauvaises en rouge
        const goodAndBadLegends = jsonAnswers.map((jsonAnswer) => ({
          ...jsonAnswer,
          color: jsonAnswer.isInLegend ? 'green' : 'red'
        }));
        setPoints(goodAndBadLegends);
        setIsCorrectionPointsSet(true);
      } else if (isSchemaFillInLegends) {
        // SETUP CORRECTION FILL IN LEGENDS
        //utiliser setLegendTexts, transformer les jsonAnswers en legendTexts
        // jsonAnswers: [{id: 1, text: 'texte'}]
        // legendTexts: {1: 'texte'}
        const legendTexts = jsonAnswers.reduce((acc, jsonAnswer) => {
          acc[jsonAnswer.id] = jsonAnswer.text;
          return acc;
        }, {});
        setLegendTexts(legendTexts);
        setIsCorrectionPointsSet(true);
      }
    }
    IS_DEV && console.log('useEffect Correction only');
  }, [jsonAnswers, correction, isCorrectionPointsSet]);

  // Restore saved state
  useEffect(() => {
    const jsonAnswersToRestore = userChoicesByQuestion?.find(
      (q) => q.id_question === question.id_question
    )?.jsonAnswers;
    if (shouldRestoreChoices && jsonAnswersToRestore?.length > 0) {
      if (isSchemaPointAndClick) {
        // SETUP CORRECTION POINT AND CLICK
        // prevent infinite loop for preview only
        // Bonnes legendes en vert et mauvaises en rouge
        const goodAndBadLegends = jsonAnswersToRestore.map((jsonAnswer) => ({
          ...jsonAnswer
        }));
        setPoints(goodAndBadLegends);
      } else if (isSchemaFillInLegends) {
        // SETUP CORRECTION FILL IN LEGENDS
        //utiliser setLegendTexts, transformer les jsonAnswers en legendTexts
        // jsonAnswers: [{id: 1, text: 'texte'}]
        // legendTexts: {1: 'texte'}
        const legendTexts = jsonAnswersToRestore.reduce((acc, jsonAnswer) => {
          acc[jsonAnswer.id] = jsonAnswer.text;
          return acc;
        }, {});
        setLegendTexts(legendTexts);
      }
      setShouldRestoreChoices(false);
    }
  }, [shouldRestoreChoices, userChoicesByQuestion]);

  // Mobile only: re-display positioned points
  useEffect(() => {
    if (mobileChoices !== null && !hasPositionedMobilePoints) {
      setPoints(mobileChoices?.jsonAnswers || []);
      setHasPositionedMobilePoints(true);
    }
  }, [mobileChoices, hasPositionedMobilePoints]);

  // MOBILE recalculer scale à partir de stageWidth et stageHeight
  // Calculer l'échelle pour faire rentrer l'image dans le canvas en respectant les contraintes
  const calculateScaleToFit = (imageWidth, imageHeight) => {
    const containerWidth = isMobile
      ? window.innerWidth * MOBILE_MAX_WIDTH_RATIO
      : window.innerWidth * DESKTOP_MAX_WIDTH_RATIO;
    const containerHeight = isMobile
      ? window.innerHeight * MOBILE_MAX_HEIGHT_RATIO
      : window.innerHeight * DESKTOP_MAX_HEIGHT_RATIO;

    const scaleX = containerWidth / imageWidth;
    const scaleY = containerHeight / imageHeight;

    return Math.min(scaleX, scaleY);
  };

  // Effect get schema from query
  useEffect(() => {
    if (schema?.settings?.stageWidth && schema?.settings?.stageHeight) {
      setSettings(schema?.settings || {}); // settings canvas
      //adjustScale(schema?.settings);
    }
    // Setup initial userChoicesByQuestion so we have empty jsonAnswers
    if (legendsAsked.length === 0 || correction) return;
    IS_DEV && console.log('useEffect schema');
  }, [schema]);

  /**
   * Scaled pointer position
   * @param e
   * @returns {{x: number, y: number}}
   */
  function getPointerPosition(e) {
    const stage = e.target.getStage();
    const scale = stage.scale();
    const pointer = stage.getPointerPosition();
    return {
      x: pointer.x / scale.x,
      y: pointer.y / scale.y
    };
  }

  const handleLegendSelect = (sel) => {
    const { key } = sel;
    setSelectedLegendId(parseInt(key));
    setContextMenuPos({ x: null, y: null });
  };

  const [isClicking, setIsClicking] = useState(false);

  // Clic souris
  const handleMouseDown = (e) => {
    if (correction) return;
    setIsClicking(true);
    const { x, y } = getPointerPosition(e);

    // Schema point and click
    if (isSchemaPointAndClick) {
      if (selectedLegendId === null) {
        if (!isMobile) {
          // Desktop only
          const stage = e.target.getStage();
          const pointer = stage.getPointerPosition(); // non scaled pointer
          setContextMenuPos({ x: pointer.x, y: pointer.y });
        }
        return;
      }
      setContextMenuPos({ x: null, y: null }); // Hide context menu
      const currentLegend = legends.find((legend) => legend.id === selectedLegendId);
      // Update display current question points
      setPoints((prevPoints) => {
        const updatedPoints = prevPoints.filter((point) => point.id !== selectedLegendId);
        return [...updatedPoints, { x, y, color: currentLegend.color, id: selectedLegendId }];
      });
    } else if (isSchemaFillInLegends) {
      //TODO
    }
  };

  const handleMouseMove = (e) => {
    if (correction || !selectedLegendId || !isClicking) return;
    const { x, y } = getPointerPosition(e);

    if (isSchemaPointAndClick) {
      const currentLegend = legends.find((legend) => legend.id === selectedLegendId);
      setPoints((prevPoints) => {
        const updatedPoints = prevPoints.filter((point) => point.id !== selectedLegendId);
        return [...updatedPoints, { x, y, color: currentLegend.color, id: selectedLegendId }];
      });
    } else if (isSchemaFillInLegends) {
      //TODO
    }
  };

  // Relâche souris
  const handleMouseUp = (e) => {
    if (correction || !selectedLegendId || !isClicking) return;
    const { x, y } = getPointerPosition(e);

    if (isSchemaPointAndClick) {
      const stage = e.target.getStage();
      const pointer = stage.getPointerPosition(); // non scaled pointer
      const allIntersections = stage.getAllIntersections(pointer);
      let isInLegend = allIntersections?.some((intersection) => {
        return legends?.some(
          (legend) =>
            legend.id === selectedLegendId &&
            legend?.shapes?.some(
              (shape, shapeIndex) => `${legend.id}-${shapeIndex}` === intersection.attrs.id
            )
        );
      });
      const currentLegend = legends.find((legend) => legend.id === selectedLegendId);
      // Update display current question points
      setPoints((prevPoints) => {
        const updatedPoints = prevPoints.filter((point) => point.id !== selectedLegendId);
        return [
          ...updatedPoints,
          { x, y, color: currentLegend.color, id: selectedLegendId, isInLegend }
        ];
      });
      setIsClicking(false);
      if (selectedLegendId) {
        setSelectedLegendId(null); // Désélectionne légende
      }
    } else if (isSchemaFillInLegends) {
      //TODO
    }
  };

  useEffect(() => {
    // Update user choices when points change
    if (!correction) {
      setUserChoicesByQuestion((u) => [
        ...u.filter((q) => String(q.id_question) !== String(question?.id_question)),
        {
          id_question: question?.id_question,
          answers: [], // True
          answers_false: [], // False
          jsonAnswers: points
        }
      ]);
    }
    IS_DEV && console.log('useEffect Update user choices when points change');
  }, [points, correction, question?.id_question, setUserChoicesByQuestion]);

  const adjustCanvasSize = useCallback(
    ({ width, height }) => {
      if (!width) {
        width = imageSizeState.width;
      }
      if (!height) {
        height = imageSizeState.height;
      }
      if (width && height) {
        const scale = calculateScaleToFit(width, height);
        const scaledWidth = width * scale;
        const scaledHeight = height * scale;
        setSettings({
          stageWidth: scaledWidth,
          stageHeight: scaledHeight,
          scaleX: scale,
          scaleY: scale
        });
        if (stageRef.current) {
          stageRef.current.width(scaledWidth);
          stageRef.current.height(scaledHeight);
        }
      }
    },
    [imageSizeState]
  );

  /**
   * Update image size after loading image
   * @param width
   * @param height
   */
  const setImageSize = ({ width, height }) => {
    // Set image size in state after loading
    setImageSizeState({ width, height });
    adjustCanvasSize({ width, height });
  };

  IS_DEV && console.log({ settings });

  const handleClickOnLegend = useCallback(() => {}, []);

  const renderLine = useCallback((line, i) => <RenderLine line={line} index={i} />, []);

  const currentQuestionChoice = useMemo(
    () => userChoicesByQuestion?.find((q) => q?.id_question === question?.id_question),
    [userChoicesByQuestion, question?.id_question]
  );

  const numberOfLegendsToComplete = question?.settings?.checkedLegends?.length;
  const numberOfLegendsCompleted = currentQuestionChoice?.jsonAnswers?.length || 0;

  // Correction only, show single selected legend
  const [pointsFiltered, setPointsFiltered] = useState(points);
  const [hilightedLegendIdCorrection, setHilightedLegendIdCorrection] = useState(null);

  useEffect(() => {
    setPointsFiltered(points);
  }, [points]);

  useEffect(() => {
    if (hilightedLegendIdCorrection === null) {
      setPointsFiltered(points);
    } else {
      setPointsFiltered(points.filter((point) => point.id === hilightedLegendIdCorrection));
    }
  }, [hilightedLegendIdCorrection]);

  const handleZoom = (zoomFactor) => {
    setSettings((prevSettings) => {
      // Calculer les nouvelles dimensions du stage
      const newScaleX = prevSettings.scaleX * zoomFactor;
      const newScaleY = prevSettings.scaleY * zoomFactor;
      const newStageWidth = prevSettings.stageWidth * zoomFactor;
      const newStageHeight = prevSettings.stageHeight * zoomFactor;
      // Mettre à jour les dimensions du stage
      if (stageRef.current) {
        stageRef.current.width(newStageWidth);
        stageRef.current.height(newStageHeight);
        stageRef.current.scale({ x: newScaleX, y: newScaleY });
      }
      return {
        ...prevSettings,
        scaleX: newScaleX,
        scaleY: newScaleY,
        stageWidth: newStageWidth,
        stageHeight: newStageHeight
      };
    });
  };
  // Fonction pour convertir les coordonnées Konva en coordonnées DOM
  const getCanvasOffset = () => {
    if (stageRef.current) {
      const stageBox = stageRef.current.container().getBoundingClientRect();
      return {
        x: stageBox.left,
        y: stageBox.top
      };
    }
    return { x: 0, y: 0 };
  };

  const legendsLayerRef = useRef(null);

  /* POC TO CHANGE */
  const [legendTexts, setLegendTexts] = useState({});
  const handleLegendTextChange = (legendId, text) => {
    setLegendTexts((prevTexts) => ({
      ...prevTexts,
      [legendId]: text
    }));
  };
  /*******************/
  const [inputPosition, setInputPosition] = useState({ x: 0, y: 0 });
  const handleInputBlur = () => {
    setEditingLegendId(null);
  };

  const numberOfLegendsCompletedForFillBlank = Object.keys(legendTexts).filter(
    (key) => legendTexts[key].length > 0
  ).length;

  useEffect(() => {
    // Update user text input choices when legendTexts changes
    if (!correction) {
      // Transforme legendTexts en un tableau d'objets pour jsonAnswers
      const formattedJsonAnswers = Object.entries(legendTexts).map(([id, text]) => ({
        id: parseInt(id, 10),
        text
      }));

      setUserChoicesByQuestion((u) => [
        ...u.filter((q) => String(q.id_question) !== String(question?.id_question)),
        {
          id_question: question?.id_question,
          answers: [], // True
          answers_false: [], // False
          jsonAnswers: formattedJsonAnswers
        }
      ]);
    }
  }, [legendTexts, correction, question?.id_question, setUserChoicesByQuestion]);

  return (
    <>
      {loading ? (
        <SpinnerCentered />
      ) : (
        <>
          <>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '5px'
              }}
            >
              <ZoomControls handleZoom={handleZoom} />
            </div>
          </>
          <Stage
            ref={stageRef}
            width={settings.stageWidth}
            height={settings.stageHeight}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onTouchStart={handleMouseDown}
            onTouchMove={handleMouseMove}
            onTouchEnd={handleMouseUp}
            scaleX={settings.scaleX}
            scaleY={settings.scaleY}
          >
            <Layer
              name="imageLayer"
              preventDefault={selectedLegendId !== null} // Permet scroll mobile
            >
              <URLImage
                imageSrc={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + image)}
                setImageSize={setImageSize}
              />
            </Layer>

            <Layer>
              {/* affichage dessin supplémentaire Layer de dessin, par dessus légendes */}
              {lines?.map((line, i) => renderLine(line, i))}
              {/* mode exercice seulement: dessins additionnels */}
              {exerciseSettings &&
                exerciseSettings.lines &&
                exerciseSettings?.lines?.map((line, i) => renderLine(line, i))}
            </Layer>

            {/* Affiche le rendu des légendes de "legends" */}
            <Layer ref={legendsLayerRef}>
              <LegendsRender
                key={`${settings.scaleX + settings.scaleY}`}
                exerciseSettings={exerciseSettings}
                mode={mode}
                legends={legends}
                selectedTool={null}
                handleClickOnLegend={handleClickOnLegend}
                setSelectedLegendId={setSelectedLegendId}
                opacity={ExoSchemaDefaultOpacity}
                selectedLegendId={selectedLegendId}
                correction={correction}
              />

              {/* Affiche points correspondants mode exercice point and click */}
              {isSchemaPointAndClick &&
                pointsFiltered?.map((point) => (
                  <HaloCircle
                    key={point.id}
                    legendId={point.id}
                    x={point.x}
                    y={point.y}
                    color={point.color}
                    onClick={() => {
                      /*
                    setIsClicking(true);
                    setSelectedLegendId(point.id);
                     */
                    }}
                  />
                ))}

              {/* Affiche cercles fill in legends */}
              {isSchemaFillInLegends && (
                <FillInLegendsCircles
                  setSelectedLegendId={setSelectedLegendId}
                  correction={correction}
                  jsonAnswers={jsonAnswers}
                  setInputPosition={setInputPosition} // Placement popup input texte
                  mode={SchemaExerciseModes.FillInLegends}
                  exerciseMode // mode exercice
                  exerciseSettings={exerciseSettings}
                  setExerciseSettings={() => {
                    /*Seulement pour admin*/
                  }}
                  legends={legends}
                  layerRef={legendsLayerRef}
                  editingLegendId={editingLegendId}
                  setEditingLegendId={setEditingLegendId}
                  legendTexts={legendTexts}
                  selectedLegendId={selectedLegendId}
                />
              )}
            </Layer>
          </Stage>

          {/* Rendre l'Input pour mode fill in legends absolument positionné, mais pas en mode correction */}
          {exerciseSettings &&
            isSchemaFillInLegends &&
            editingLegendId &&
            !correction &&
            !isMobile && (
              <Input
                value={legendTexts[editingLegendId] || ''}
                onChange={(e) => {
                  e.preventDefault();
                  handleLegendTextChange(editingLegendId, e.target.value);
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault(); // Empêche la soumission du formulaire parent
                    setEditingLegendId(null); // ferme input
                  }
                }}
                onBlur={handleInputBlur}
                autoFocus
                style={{
                  position: 'absolute',
                  top: inputPosition.y + 4, //+ getCanvasOffset().y,
                  left: inputPosition.x + 4, //+ getCanvasOffset().x,
                  transform: 'translate(-50%, -50%)',
                  zIndex: 10,
                  width: '160px' // Limite la largeur de l'input
                }}
              />
            )}

          {/* Modal-Input special mobile */}
          {exerciseSettings &&
            isSchemaFillInLegends &&
            editingLegendId &&
            !correction &&
            isMobile && (
              <Modal
                footer={null}
                open={!!editingLegendId}
                closable={false}
                getContainer={false} // Monte le Modal dans le même conteneur (utile si vous avez des problèmes de contexte)
                onClose={() => {
                  setEditingLegendId(null);
                }}
                onCancel={() => {
                  setEditingLegendId(null);
                }}
                centered
                style={{
                  zIndex: 9999991
                }}
                rootStyle={{
                  zIndex: 9999991
                }}
              >
                <Space direction={'horizontal'}>
                  {/* Cercle Coloré */}
                  <Avatar
                    size={24}
                    style={{
                      backgroundColor: '#faad14', // Vert ou Orange
                      color: 'white',
                      //fontWeight: 'bold',
                      fontSize: '16px'
                    }}
                  >
                    {/* Il ne faut pas mettre legendId mais la key correspondante aux legendsAsked */}
                    {legendsAsked.findIndex((l) => l.id === editingLegendId) + 1}
                  </Avatar>
                  <Input
                    value={legendTexts[editingLegendId] || ''}
                    onChange={(e) => {
                      e.preventDefault();
                      handleLegendTextChange(editingLegendId, e.target.value);
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault(); // Empêche la soumission du formulaire parent
                        setEditingLegendId(null); // ferme input
                      }
                    }}
                    onBlur={handleInputBlur}
                    autoFocus
                  />
                  <Button type={'primary'} onClick={() => setEditingLegendId(null)}>
                    {t('OK')}
                  </Button>
                </Space>
              </Modal>
            )}

          {contextMenuPos.x !== null && (
            <ContextMenu
              x={contextMenuPos.x}
              y={contextMenuPos.y}
              legends={legendsAsked.filter((l) => !points.some((p) => p.id === l.id))} // Uniquement les légendes qui ne sont pas déjà placées
              onSelect={handleLegendSelect}
            />
          )}

          {/* Affichage correction */}
          {correction ? (
            <>
              <Alert
                type={'info'}
                message={t('Schemas.GoodAnswersAreGreenBadAnswersAreRed')}
                showIcon
              />
              <h3>Légendes</h3>
              {isSchemaPointAndClick && (
                <LegendsListCorrection
                  hilightedLegendIdCorrection={hilightedLegendIdCorrection}
                  setHilightedLegendIdCorrection={setHilightedLegendIdCorrection}
                  setSelectedLegendId={setSelectedLegendId}
                  legendsAsked={legendsAsked}
                  points={pointsFiltered}
                />
              )}
              {isSchemaFillInLegends && (
                <LegendsListCorrectionFillInLegends
                  legendTexts={legendTexts} // Reponses users
                  jsonAnswers={jsonAnswers} // Reponses users
                  legendsAsked={legendsAsked} // Légendes demandées
                  selectedLegendId={selectedLegendId}
                  setSelectedLegendId={setSelectedLegendId}
                />
              )}
              <br />
              <br />
            </>
          ) : (
            <>
              {isSchemaPointAndClick && (
                <PointAndClickLegendsList
                  legendsAsked={legendsAsked}
                  selectedLegendId={selectedLegendId}
                  setSelectedLegendId={setSelectedLegendId}
                  points={points}
                  isMobile={isMobile}
                  setHilightedLegendIdCorrection={setHilightedLegendIdCorrection}
                  numberOfLegendsCompleted={numberOfLegendsCompleted}
                  numberOfLegendsToComplete={numberOfLegendsToComplete}
                />
              )}

              {isSchemaFillInLegends && (
                <FillInLegendsList
                  legendsAsked={legendsAsked}
                  legendTexts={legendTexts}
                  numberOfLegendsCompletedForFillBlank={numberOfLegendsCompletedForFillBlank}
                  numberOfLegendsToComplete={numberOfLegendsToComplete}
                  handleLegendTextChange={handleLegendTextChange}
                  handleInputBlur={handleInputBlur}
                  isMobile={isMobile}
                />
              )}
            </>
          )}
          <div></div>
        </>
      )}
    </>
  );
}
