import React from 'react';
import { Menu, Dropdown, Badge } from 'antd';

const ContextMenu = ({ x, y, legends, onSelect }) => {
  const menu = (
    <Menu onClick={onSelect}>
      {legends.map((legend) => (
        <Menu.Item key={legend.id}>
          <Badge color={legend?.color} /> &nbsp;&nbsp;{legend.name}
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <Dropdown overlay={menu} visible={!!x && !!y} trigger={['click']}>
      <div
        style={{
          position: 'absolute',
          left: x,
          top: y,
          zIndex: 1000
        }}
      />
    </Dropdown>
  );
};

export default ContextMenu;
