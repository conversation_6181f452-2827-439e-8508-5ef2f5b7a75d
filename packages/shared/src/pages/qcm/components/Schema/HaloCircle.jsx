import Konva from 'konva';
import React, { useEffect, useRef } from 'react';
import { Circle } from 'react-konva';

export const HaloCircle = ({ x, y, color, onClick, legendId }) => {
  const haloRef = useRef(null);

  useEffect(() => {
    const haloAnimation = new Konva.Animation((frame) => {
      const scale = Math.sin(frame.time / 1000) * 0.5 + 1; // Animation scale between 0.5 and 1.5
      if (haloRef.current) {
        haloRef.current.scale({ x: scale, y: scale });
      }
    }, haloRef.current.getLayer());

    haloAnimation.start();

    return () => {
      haloAnimation.stop();
    };
  }, []);

  return (
    <>
      <Circle
        ref={haloRef}
        x={x}
        y={y}
        radius={14}
        stroke={color}
        strokeWidth={2}
        opacity={0.5}
        listening={false} // Prevent the halo from catching events
      />
      <Circle
        x={x}
        y={y}
        radius={10}
        fill={color}
        stroke={'black'}
        strokeWidth={2}
        id={`user-point-${legendId}`}
        onTouchStart={onClick}
        onMouseDown={onClick}
        onClick={onClick}
      />
    </>
  );
};
