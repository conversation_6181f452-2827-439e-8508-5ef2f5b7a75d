import React from 'react';
import { But<PERSON>, Tooltip } from 'antd';
import { ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const ZoomControls = ({ handleZoom }) => {
  const { t } = useTranslation();

  return (
    <div style={{ display: 'flex', flexDirection: 'row', gap: '5px' }}>
      <Tooltip title={t('Schemas.Zoom')}>
        <Button
          onClick={() => {
            handleZoom(1.1);
          }}
          icon={<ZoomInOutlined />}
        />
      </Tooltip>
      <Tooltip title={t('Schemas.Unzoom')}>
        <Button
          onClick={() => {
            handleZoom(0.9);
          }}
          icon={<ZoomOutOutlined />}
        />
      </Tooltip>
    </div>
  );
};

export default ZoomControls;
