import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';
import { QUERY_SCHEMA_BY_ID } from '@/shared/graphql/schemas';
import { QuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal';
import SchemaExercise from '@/shared/pages/qcm/components/Schema/SchemaExercise';
import { DoMcqContext } from '@/shared/pages/qcm/context/DoMcqContext';
import { getCleanFloatNumber } from '@/shared/services/qcm';
import { getUrlProtectedRessource, GlobalConfig, isMobile } from '@/shared/utils/utils';
import { useQuery } from '@apollo/client';
import { Button, Drawer, Progress, Space } from 'antd';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { Pointer } from 'lucide-react';

import { motion } from 'framer-motion/dist/framer-motion';
const ClickableImageWithAntDIcon = ({ src, alt, onClick }) => {
  const handleKeyPress = (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      onClick();
    }
  };

  return (
    <div
      className="image-container"
      onClick={onClick}
      onKeyPress={handleKeyPress}
      role="button"
      tabIndex="0"
      aria-label="Cliquer pour continuer"
    >
      <img
        src={src}
        alt={alt}
        className="clickable-image"
        style={{ maxWidth: 300, height: 'auto' }}
      />
      <motion.div
        className="pointer-icon"
        animate={{
          y: [0, -10, 0] // Déplacement vertical pour l'effet de rebond
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      >
        <Pointer style={{ fontSize: '34px', color: '#ffffff' }} />
      </motion.div>
    </div>
  );
};

/**
 * Do Exercise schema: Point & Click OR Fill in legends
 * @param props
 * @returns {JSX.Element}
 * @constructor
 */
export const DoSchemaExercise = (props) => {
  const { t } = useTranslation();
  const [modalSchemaOpen, setModalSchemaOpen] = React.useState(false);

  const { userChoicesByQuestion, shouldRestoreChoices } = useContext(DoMcqContext);

  const displayForMobile = isMobile;

  // Query just for image for mobile
  const { data, loading, error, refetch } = useQuery(QUERY_SCHEMA_BY_ID, {
    variables: {
      id: props?.question?.schemaLibraryId
    },
    skip: !displayForMobile,
    fetchPolicy: 'no-cache'
  });
  const schema = data?.schema;
  const isSchemaPointAndClick = props?.question?.type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK;
  const isSchemaFillInLegends = props?.question?.type === QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS;

  const currentQuestionChoice = userChoicesByQuestion?.find(
    (q) => q?.id_question === props.question?.id_question
  );
  const numberOfLegendsToComplete = props.question?.settings?.checkedLegends?.length;
  const numberOfLegendsCompleted = currentQuestionChoice?.jsonAnswers?.length || 0;

  return (
    <>
      {displayForMobile ? (
        <div>
          {!modalSchemaOpen && (
            <>
              {loading ? (
                <SpinnerCentered />
              ) : (
                <>
                  <ClickableImageWithAntDIcon
                    src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + schema?.image)}
                    alt={schema?.name}
                    onClick={() => setModalSchemaOpen(true)}
                  />
                  {/*
                  <img
                    src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + schema?.image)}
                    style={{ maxWidth: 300, height: 'auto' }}
                    onClick={() => setModalSchemaOpen(true)}
                    alt={schema?.name}
                  />
                  */}
                  <br />
                  <br />
                  <Space>
                    {isSchemaPointAndClick && (
                      <div>
                        Légendes à positionner ({numberOfLegendsCompleted}/
                        {numberOfLegendsToComplete})
                      </div>
                    )}
                    <Progress
                      percent={getCleanFloatNumber(
                        (numberOfLegendsCompleted / numberOfLegendsToComplete) * 100
                      )}
                      steps={numberOfLegendsToComplete}
                      size="small"
                      strokeColor={'#389e0d'}
                    />
                  </Space>
                </>
              )}
            </>
          )}
          {modalSchemaOpen && (
            <Drawer
              open={modalSchemaOpen}
              onClose={() => setModalSchemaOpen(false)}
              footer={null}
              //destroyOnClose
              width="100%"
              // bodyPadding 8px
              bodyStyle={{
                padding: 8 // reduce padding we need space on mobile
              }}
              rootStyle={{
                zIndex: 999999,
                marginTop: 50
                //marginTop: 104 // 64 + 40 - prevent the drawer to be under the header
              }}
              extra={
                <Space>
                  <Button onClick={() => setModalSchemaOpen(false)}>OK</Button>
                </Space>
              }
            >
              <SchemaExercise {...props} mobileChoices={currentQuestionChoice} />
            </Drawer>
          )}
        </div>
      ) : (
        <SchemaExercise {...props} />
      )}
    </>
  );
};
