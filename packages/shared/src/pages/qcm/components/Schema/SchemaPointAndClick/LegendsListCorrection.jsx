import { Badge, Space, Tag } from 'antd';
import React from 'react';

export default function LegendsListCorrection({
  legendsAsked,
  hilightedLegendIdCorrection,
  setHilightedLegendIdCorrection,
  points,
  setSelectedLegendId
}) {
  return (
    <Space wrap>
      {legendsAsked.map((legend) => {
        const correspondingPoint = points.find((point) => point.id === legend.id);
        const isGood = correspondingPoint?.isInLegend;
        return (
          <Tag
            color="default"
            key={legend.id}
            style={{
              borderColor: isGood ? '#43be5f' : '#ff8181',
              opacity:
                hilightedLegendIdCorrection === null
                  ? 1
                  : hilightedLegendIdCorrection === legend.id
                    ? 1
                    : 0.6,
              cursor: 'pointer'
            }}
            onMouseEnter={() => {
              setHilightedLegendIdCorrection(legend.id);
              if (setSelectedLegendId) {
                setSelectedLegendId(legend.id);
              }
            }}
            onMouseLeave={() => {
              setHilightedLegendIdCorrection(null);
              if (setSelectedLegendId) {
                setSelectedLegendId(null);
              }
            }}
          >
            <Badge color={legend?.color} /> &nbsp;{legend?.name}
          </Tag>
        );
      })}
    </Space>
  );
}
