import useMediaQuery from '@/shared/hooks/useMediaQuery';
import { isAdmin, isTuteur } from '@/shared/utils/authority';
import React, { useContext, useState } from 'react';
import { BookOpenCheck, CalendarFold, CircleX, Clock, Download, History } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Button, Divider } from 'antd';
import { GradeHistoryGraph } from '@/shared/pages/qcm/components/correction/GradeHistoryGraph';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import dayjs from 'dayjs';
import { router } from 'umi';
import { getMaxPointsFromQcm } from '@/shared/services/qcm';
import { downloadFile, FILE_TYPE } from '@/shared/services/file';
import { useMutation } from '@apollo/client';
import { MUTATION_CREATE_XLS_FROM_JSON } from '@/shared/graphql/qcm';

export function SeriePersonalHistorySelector({ serie, gradeOutOf, maxGrade, userId }) {
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;
  const { t } = useTranslation();

  const isSmallScreen = useMediaQuery('(max-width: 600px)');

  const [hoverSelector, setHoverSelector] = useState(false);
  const [openSelector, setOpenSelector] = useState(false);

  const [exportToXlsAndGetLink] = useMutation(MUTATION_CREATE_XLS_FROM_JSON);

  const resultsAmount = serie.resultsForUser.length;

  const displayedAverage = () => {
    const somme = serie.resultsForUser.reduce((sum, obj) => sum + (obj.note || 0), 0);
    const average = somme / serie.resultsForUser.length;
    return `${gradeOutOf ? Math.round(((gradeOutOf * average) / maxGrade) * 100) / 100 : Math.round(average * 100) / 100}/${gradeOutOf ? gradeOutOf : maxGrade}`;
  };

  const handleMouseOver = () => {
    setHoverSelector(true);
  };
  const handleMouseOut = () => {
    setHoverSelector(false);
  };
  const goToResult = (result) => {
    if (isTuteur() || isAdmin()) {
      let correctionLink = `/qcm/correction/${serie?.id_qcm}/user/${userId}/stat/${result?.id}`;
      if (result.qcmSessionId) {
        correctionLink = `/qcm/correction/${serie?.id_qcm}/user/${userId}/session/${result.qcmSessionId}`;
      }
      router.push(correctionLink);
    } else {
      let correctionLink = `/qcm/correction/${serie?.id_qcm}/stat/${result?.id}`;
      if (result.qcmSessionId) {
        correctionLink = `/qcm/correction/${serie?.id_qcm}/session/${result.qcmSessionId}`;
      }
      router.push(correctionLink);
    }
  };

  const getTableValueAsJSON = () => {
    const results = serie.resultsForUser;
    if (results) {
      const r = results.map((row) => {
        return {
          Date: dayjs(row.date).format('DD/MM/YYYY HH:mm:ss'),
          Note: row.note + ' / ' + getMaxPointsFromQcm(serie)
        };
      });
      return r;
    }
  };

  const handleExcelExport = async (e) => {
    e.stopPropagation();
    const datajson = getTableValueAsJSON();
    const { data: dataExport } = await exportToXlsAndGetLink({
      variables: {
        input: datajson,
        name: `Resultats` // @TODO ne fonctionne pas: fichier s'appelle undefined
      }
    });
    const stringFile = dataExport?.exportJsonToXLSDownload;
    if (stringFile) {
      await downloadFile(FILE_TYPE.FILE, stringFile);
    }
  };

  return (
    <div style={{ position: 'relative' }} onMouseOver={handleMouseOver} onMouseOut={handleMouseOut}>
      <div
        style={{
          maxWidth: 420,
          borderRadius: 12,
          padding: 12,
          gap: 12,
          border: '1px solid',
          borderColor: hoverSelector ? primaryColor : 'lightgray',
          transition: 'all 0.3s',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          cursor: 'pointer'
        }}
        onClick={() => setOpenSelector(true)}
      >
        <History style={{ fontSize: 36 }} />
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
          <p>
            <span style={{ fontWeight: 700 }}>{t('History')}</span> :
            {t('Grade', { count: resultsAmount })}
          </p>
          {resultsAmount > 1 && (
            <span>
              {t('general.Average')} {displayedAverage()}
            </span>
          )}
        </div>
        <div
          style={{
            flex: 1,
            maxWidth: '100%', // Empêche le dépassement
            overflow: 'hidden',
            display: 'flex',
            justifyContent: 'center'
          }}
        >
          <GradeHistoryGraph
            resultsToDisplay={serie.resultsForUser}
            displayedSessionDate={serie.resultat?.date}
            gradeOutOf={gradeOutOf}
            maxGrade={maxGrade}
          />
        </div>

        {/* Bouton download only for tutors/admins */}
        {(isTuteur() || isAdmin()) && (
          <Button type="text" onClick={(e) => handleExcelExport(e)}>
            <Download style={{ fontSize: 24 }} />
          </Button>
        )}
      </div>

      {openSelector && (
        <div
          style={{
            maxWidth: 420,
            width: isSmallScreen ? '80vw' : 420,
            minHeight: '100%',
            position: 'absolute',
            border: '1px solid',
            borderRadius: 12,
            padding: 12,
            zIndex: 9,
            background: 'white',
            borderColor: hoverSelector ? primaryColor : 'lightgray',
            transition: 'all 0.3s',
            top: 0
          }}
          onClick={() => setOpenSelector(false)}
        >
          <div>
            <CircleX
              style={{ fontSize: 24, position: 'absolute', right: 12, top: 12, cursor: 'pointer' }}
              onClick={() => setOpenSelector(false)}
            />
          </div>
          <div style={{ marginBottom: 12 }} />

          {serie.resultsForUser.map((result, index) => (
            <div key={`${result.id}-${index}`} style={{ cursor: 'pointer' }}>
              <div
                style={{ display: 'flex', alignItems: 'center', gap: 12 }}
                onClick={() => goToResult(result)}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  <CalendarFold size={16} />
                  {dayjs(result.date).format('DD/MM/YYYY')}
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  <Clock size={16} />
                  {dayjs(result.date).format('HH:mm:ss')}
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  <BookOpenCheck size={16} />
                  {gradeOutOf
                    ? Math.round(((gradeOutOf * result.note) / maxGrade) * 100) / 100
                    : result.note}
                  /{gradeOutOf ? gradeOutOf : getMaxPointsFromQcm(serie)}
                </div>
              </div>
              {index < serie.resultsForUser.length - 1 && <Divider style={{ margin: '12px 0' }} />}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
