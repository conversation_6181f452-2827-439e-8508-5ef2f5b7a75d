import { QUERY_ALL_GROUPS } from '@/shared/graphql/cours.js';
import { QuestionCircleTwoTone } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { Checkbox, Popover, Select, Space, Tag } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

const CorrectionGroupsSelector = ({ selectedGroupIds, setSelectedGroupIds }) => {
  const { t } = useTranslation();
  const [forSpecificGroups, setForSpecifigGroups] = React.useState(false);
  const {
    loading: loadingGroups,
    data: dataAllGroups,
    refetch: refetchGruops
  } = useQuery(QUERY_ALL_GROUPS, {
    fetchPolicy: 'cache-and-network'
  });
  const allGroups = dataAllGroups?.allGroupes;

  return (
    <Space style={{ paddingLeft: '20px', paddingBottom: '20px' }}>
      <Popover
        content={t('VisualizeAverageRankAndAnalysisForAllGroupsText')}
        title={t('general.Help')}
        trigger="hover"
      >
        <QuestionCircleTwoTone />
      </Popover>

      {t('Resultsfor')}
      <Checkbox
        defaultChecked
        value={!forSpecificGroups}
        onChange={() => {
          setSelectedGroupIds([]);
          setForSpecifigGroups(!forSpecificGroups);
        }}
      >
        {t('AllGroups')}
      </Checkbox>
      {forSpecificGroups && (
        <Select
          showArrow
          mode="multiple"
          style={{ minWidth: '350px', width: '100%' }}
          tagRender={({ label, value, closable, onClose, key }) => (
            <Tag
              value={value}
              key={key}
              color="geekblue"
              closable={closable}
              onClose={onClose}
              style={{ marginRight: 3 }}
            >
              {label}
            </Tag>
          )}
          placeholder={t('ChooseGroups')}
          options={allGroups?.map((groupe) => ({
            value: groupe.name,
            key: groupe.id
          }))}
          onDeselect={(_, option) => {
            const newGroupIds = selectedGroupIds.filter((id) => id !== option.key);
            setSelectedGroupIds(newGroupIds);
          }}
          onSelect={(_, option) => {
            setSelectedGroupIds([...selectedGroupIds, option.key]);
          }}
        />
      )}
    </Space>
  );
};

export default CorrectionGroupsSelector;
