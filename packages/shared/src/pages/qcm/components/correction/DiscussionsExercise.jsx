import Commentaires from '@/shared/components/Commentaires/Commentaires';
import { TopicsList } from '@/shared/components/Commentaires/TopicsList';
import { FileImage } from '@/shared/components/FileImage';
import { useFixDrawerKeyboard } from '@/shared/hooks/useFixDrawerKeyboard';
import useMediaQuery from '@/shared/hooks/useMediaQuery';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import ModalAnswerReport from '@/shared/pages/qcm/components/ModalAnswerReport';
import { CommentairesType } from '@/shared/services/commentaires';
import { isMobile } from '@/shared/utils/utils';
import { LeftCircleTwoTone, MessageTwoTone } from '@ant-design/icons';
import { Button, Drawer, Modal, Tooltip } from 'antd';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function DiscussionsExercise({
  printable,
  shouldHideComments,
  question,
  questionNumber
}) {
  const questionId = question?.id_question;

  const defaultCommentaireProps = {
    id: questionId,
    type: CommentairesType.EXERCISE,
    showYearSelection: false,
    showCreatePostButton: false
  };
  const { t } = useTranslation();
  const [questionModalVisible, setQuestionModalVisible] = useState(false);
  const isSmallScreen = useMediaQuery('(max-width: 600px)');
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  const [showAnswerComments, setShowAnswerComments] = useState(false);
  const [commentairesProps, setCommentairesProps] = useState(defaultCommentaireProps);
  const commentairesRef = useRef();
  const [uniqueAnswersTypes, setUniqueAnswerTypes] = useState([]);
  const [answerTypes, setAnswerTypes] = useState([]);

  useEffect(() => {
    if (question) {
      const firstLevelPosts = question?.posts?.filter((p) => p.parentId === null);
      const postTypes = firstLevelPosts?.map((post) => post?.type);
      if (Array.isArray(postTypes) && postTypes.length > 0) {
        setAnswerTypes(postTypes);
        setUniqueAnswerTypes(
          postTypes.filter((v, i, a) => a?.findIndex((t) => t?.id === v?.id) === i)
        );
      }
    }
  }, [question]);

  const additionnalStyle = isSmallScreen
    ? { justifyContent: 'flex-start', marginLeft: question?.posts?.length === 0 ? 24 : 0 }
    : {};

  return (
    <>
      {!printable && !shouldHideComments && (
        <div
          style={{
            display: 'flex',
            alignSelf: 'center',
            justifySelf: 'center',
            marginLeft: isMobile ? '-8px' : 'auto',
            flexWrap: 'wrap',
            marginBottom: 10,
            ...additionnalStyle
          }}
        >
          <a
            style={{
              whiteSpace: 'nowrap'
            }}
            onClick={() => setShowAnswerComments(!showAnswerComments)}
          >
            {question.posts?.length === 0
              ? ` 0 ${t('comment')} | ${t('DoComment')}`
              : ` ${question.posts?.length} ${
                  question.posts?.length > 1 ? t('comments') : t('comment')
                } | ${t('DoComment')}`}
          </a>
          {/* BADGES */}
          <div style={{ marginLeft: 10 }}>
            {uniqueAnswersTypes?.map((type, index) => (
              <Tooltip title={type?.name} key={index}>
                <>
                  <span style={{ marginRight: 6 }}>
                    <FileImage image={type?.image} style={{ maxHeight: 12 }} />
                    <span style={{ fontSize: 8 }}>
                      x{answerTypes?.filter((a) => a?.id === type?.id)?.length}
                    </span>
                    &nbsp;
                  </span>
                </>
              </Tooltip>
            ))}
          </div>

          <br />
          {showAnswerComments && (
            <Drawer
              footer={null}
              onClose={() => setShowAnswerComments(false)}
              open={showAnswerComments}
              placement="bottom"
              closable
              title={`${t('Comments')} ${t('general.Question')} ${questionNumber}`}
              height={isMobile ? 'calc(100% - 109px)' : 'calc(100% - 64px)'}
              confirmLoading={false}
              style={{
                marginBottom: 10,
                marginLeft: 'auto',
                marginRight: 'auto'
              }}
            >
              {/*
              {commentairesProps.isDetailPage ? (
                <Button
                  icon={<LeftCircleTwoTone twoToneColor={primaryColor} />}
                  style={{ marginBottom: 10 }}
                  onClick={() => {
                    setCommentairesProps(defaultCommentaireProps);
                  }}
                >
                  {t('general.back')}
                </Button>
              ) : (
                <Button
                  style={{ height: 'auto' }}
                  type="primary"
                  onClick={() => {
                    setQuestionModalVisible(true);
                  }}
                  icon={<MessageTwoTone twoToneColor={primaryColor} />}
                >
                  {t('ReportErrorOrAskQuestion...')}
                </Button>
              )}

              {questionModalVisible && (
                <ModalAnswerReport
                  //answer={answer}
                  question={question}
                  questionNumber={questionNumber}
                  questionModalVisible={questionModalVisible}
                  setQuestionModalVisible={setQuestionModalVisible}
                  answerAlphabetValue={null}
                  onFinish={async () => {
                    await commentairesRef.current.refetchCommentaires();
                  }}
                />
              )}
              */}

              <TopicsList {...commentairesProps} ref={commentairesRef} />

              {/*
              <Commentaires
                {...commentairesProps}
                ref={commentairesRef}
                openInSamePage
                onOpenInSamePage={(postId) => {
                  // Change Commentaires props
                  setCommentairesProps({
                    isDetailPage: true,
                    postId,
                    id: question.id_question,
                    type: CommentairesType.EXERCISE,
                    showYearSelection: false,
                    showCreatePostButton: false
                  });
                }}
              />
              */}
            </Drawer>
          )}
        </div>
      )}
    </>
  );
}
