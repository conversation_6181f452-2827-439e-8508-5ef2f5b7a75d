import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import CoursCardProgression from '@/shared/components/Progression/components/CoursCardProgression.jsx';
import NotionCardProgression from '@/shared/components/Progression/components/NotionCardProgression.jsx';
import UECardProgression from '@/shared/components/Progression/components/UECardProgression.jsx';
import UECategoryCardProgression from '@/shared/components/Progression/components/UECategoryCardProgression.jsx';
import { QUERY_UE_CATEGORY_ID_WITH_CHILDREN } from '@/shared/graphql/cours.js';
import {
  MUTATION_UPDATE_MY_GOOD_ANSWERS_SYNTHESIS,
  QUERY_COURS_BY_ID_WITH_NOTION_PROGRESS,
  QUERY_COURS_IN_UECATEGORY_WITH_SYNTHESIS,
  QUERY_COURSES_IN_UE_WITH_SYNTHESIS,
  QUERY_MES_MATIERES_WITH_PROGRESSION_SYNTHESIS_V3,
  QUERY_UE_BY_ID_WITH_CHILDREN_AND_SYNTHESIS,
  QUERY_UE_CATEGORIES_IN_UE_WITH_SYNTHESIS
} from '@/shared/graphql/progression.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Card, Divider, Empty, message } from 'antd';
import { useTranslation } from 'react-i18next';
import React from 'react';
import CoursBreadcrumb from '@/shared/components/Cours/CoursBreadcrumb';
// G6 Should be imported that way

export const GlobalUserSubjectsSynthesis = ({
  // Correction and progress
  userId = null
}) => {
  const { t } = useTranslation();

  const [selection, setSelection] = React.useState({
    ueId: null,
    ueCategoryId: null,
    coursId: null
  });

  const shouldFetchMySubjects = !selection?.ueId && !selection?.coursId && !selection?.ueCategoryId;

  const shouldFetchUEContent = selection?.ueId;
  const shouldFetchUECategContent = selection?.ueCategoryId;
  const shouldFetchCoursContent = selection?.coursId;

  // ROOT UE ///////////////////////////////////////////////////////
  const { loading, error, data, refetch } = useQuery(
    QUERY_MES_MATIERES_WITH_PROGRESSION_SYNTHESIS_V3,
    {
      fetchPolicy: 'cache-and-network',
      variables: {
        ...(userId && { userId })
      },
      skip: !shouldFetchMySubjects
    }
  );
  // Exclusion des UEs sans stats
  const mesUEs = data?.mesUEs?.filter(
    (ue) =>
      !ue?.isFolder &&
      ue?.exercisesResultsSummary !== null &&
      ue?.exercisesResultsSummary?.goodAnswers !== null
  );
  //////////////////////////////////////////////////////////////////

  // UE CONTENT ///////////////////////////////////////////////////////
  // Current UE and childrenUEs
  const {
    data: dataUEWithChildren,
    error: errorUE,
    loading: loadingUEAndChildre,
    refetch: refetchUEAndChildren
  } = useQuery(QUERY_UE_BY_ID_WITH_CHILDREN_AND_SYNTHESIS, {
    variables: {
      id: selection?.ueId,
      ...(userId && { userId })
    },
    fetchPolicy: 'cache-and-network',
    skip: !shouldFetchUEContent
  });

  const {
    loading: loadingUECateg,
    data: dataUECategs,
    refetch: refetchUECategs
  } = useQuery(QUERY_UE_CATEGORIES_IN_UE_WITH_SYNTHESIS, {
    fetchPolicy: 'cache-and-network',
    variables: { ueId: selection?.ueId, ...(userId && { userId }) },
    skip: !shouldFetchUEContent
  });
  const {
    loading: loadingCourses,
    error: errorCourses,
    data: dataCourses,
    refetch: refetchCourses
  } = useQuery(QUERY_COURSES_IN_UE_WITH_SYNTHESIS, {
    fetchPolicy: 'cache-and-network',
    variables: { ueId: selection?.ueId, ...(userId && { userId }) },
    skip: !shouldFetchUEContent
  });

  // Catégories de UE parent
  const categories = dataUECategs?.ueCategories;
  // UEs enfants (si dossier)
  const childrenUEs = dataUEWithChildren?.ue?.children;
  const currentUE = dataUEWithChildren?.ue;
  // Cours
  const coursInUE = dataCourses?.coursInUE;
  //////////////////////////////////////////////////////////////////

  // CATEGORY CONTENT ///////////////////////////////////////////////////////
  // Query category and children
  const {
    data: dataCurrentCategory,
    loading: loadingCurrentCategory,
    refetch: refetchCategoryWithChildren
  } = useQuery(QUERY_UE_CATEGORY_ID_WITH_CHILDREN, {
    variables: { id: selection?.ueCategoryId, ...(userId && { userId }) },
    fetchPolicy: 'cache-and-network',
    skip: !shouldFetchUECategContent
  });
  const currentCategory = dataCurrentCategory?.ueCategory;
  const childrenCategories = currentCategory?.children;

  const {
    loading: loadingCoursInUECateg,
    error: errorCoursInUECateg,
    data: dataCoursInUECateg
  } = useQuery(QUERY_COURS_IN_UECATEGORY_WITH_SYNTHESIS, {
    variables: { ueCategoryId: selection?.ueCategoryId, ...(userId && { userId }) },
    fetchPolicy: 'cache-and-network',
    skip: !shouldFetchUECategContent
  });
  const coursInUECategory = dataCoursInUECateg?.coursInUECategory;
  //////////////////////////////////////////////////////////////////

  // COURS CONTENT ///////////////////////////////////////////////////////

  const {
    loading: loadingCours,
    error: errorCours,
    data: dataCours
  } = useQuery(QUERY_COURS_BY_ID_WITH_NOTION_PROGRESS, {
    variables: { id: selection?.coursId, ...(userId && { userId }) },
    fetchPolicy: 'cache-and-network',
    skip: !shouldFetchCoursContent
  });
  const currentCours = dataCours?.cour;
  const currentCoursNotions = currentCours?.notionsResultsSummary;

  //////////////////////////////////////////////////////////////////

  const onUeClick = (id) => {
    setSelection({
      ueId: id,
      ueCategoryId: null,
      coursId: null
    });
  };
  const onUeCategoryClick = (id) => {
    setSelection({
      ueId: null,
      ueCategoryId: id,
      coursId: null
    });
  };
  const onCoursClick = (id) => {
    setSelection({
      ueId: null,
      ueCategoryId: null,
      coursId: id
    });
  };

  const loadingData =
    loading ||
    loadingUEAndChildre ||
    loadingUECateg ||
    loadingCourses ||
    loadingCurrentCategory ||
    loadingCoursInUECateg ||
    loadingCours;
  const allData =
    mesUEs ||
    childrenUEs ||
    coursInUE ||
    categories ||
    childrenCategories ||
    coursInUECategory ||
    currentCoursNotions;
  const loadingAndNoData = loadingData && !allData;
  const nothingToDisplay =
    !loadingData &&
    !mesUEs?.length &&
    !childrenUEs?.length &&
    !coursInUE?.length &&
    !categories?.length &&
    !childrenCategories?.length &&
    !coursInUECategory?.length &&
    !currentCoursNotions?.length;
  const notInRoot = selection?.ueId || selection?.ueCategoryId || selection?.coursId;

  const [updateMySynthesisMutation, { loading: loadingAnalysis }] = useMutation(
    MUTATION_UPDATE_MY_GOOD_ANSWERS_SYNTHESIS
  );

  const launchManualAnalysis = async () => {
    try {
      await updateMySynthesisMutation();
      await refetch();
      message.success('Analyse terminée');
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  return (
    <>
      {/* Détails objet actuel */}
      {notInRoot && (
        <div>
          {selection?.ueId && currentUE && (
            <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'left', gap: '32px' }}>
              <UECardProgression ue={currentUE} onClick={() => {}} />
              <div
                style={{
                  textAlign: 'center',
                  fontSize: '16px'
                }}
              >
                <Card>
                  <b>
                    {currentUE?.myProgression?.qcmFaits || 0} /{' '}
                    {currentUE?.myProgression?.totalQcm || 0}
                  </b>
                  <br />
                  Exercices réalisés
                </Card>
                <Card>
                  <b>
                    {currentUE?.myProgression?.coursVus || 0} /{' '}
                    {currentUE?.myProgression?.totalCours || 0}
                  </b>
                  <br />
                  Cours vus
                </Card>
                {/*
                <Card>
                  <br />
                  Questions posées
                </Card>
                */}
              </div>
            </div>
          )}
        </div>
      )}

      <Divider />

      <CoursBreadcrumb
        ueId={selection?.ueId || null}
        categoryId={selection?.ueCategoryId || null}
        coursId={selection?.coursId || null}
        withRoot
        onClick={(a) => setSelection(a)}
      />

      <br />
      <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: '24px' }}>
        {nothingToDisplay && (
          <>
            {/*
              Sensé afficher matières avec résutlats, si aucun résultat,
              c'est soit pas mis à jour car user inactif, soit user n'a pas de résultats
            */}
            {shouldFetchMySubjects ? (
              <>
                <Empty description="Aucun résultat pour le moment. Votre synthèse est mise à jour toutes les 24h. Si vous le souhaitez, vous pouvez lancer l'analyse manuellement">
                  <Button onClick={launchManualAnalysis} loading={loadingAnalysis}>
                    {loadingAnalysis
                      ? 'Analyse en cours, cela peut prendre quelques minutes maximum...'
                      : "Lancer l'analyse"}
                  </Button>
                </Empty>
              </>
            ) : (
              <Empty />
            )}
          </>
        )}
        {loadingAndNoData && (
          <div style={{ height: '340px' }}>
            <SpinnerCentered />
          </div>
        )}

        {mesUEs?.map((ue) => (
          <UECardProgression key={ue.id} ue={ue} onClick={onUeClick} />
        ))}

        {childrenUEs?.map((ue) => (
          <UECardProgression key={ue.id} ue={ue} onClick={onUeClick} />
        ))}
        {coursInUE
          ?.filter((c) => c?.exercisesResultsSummary !== null)
          ?.map((c) => (
            <CoursCardProgression key={c.id} cours={c} onClick={onCoursClick} />
          ))}

        {coursInUECategory
          ?.filter((c) => c?.exercisesResultsSummary !== null)
          ?.map((c) => (
            <CoursCardProgression key={c.id} cours={c} onClick={onCoursClick} />
          ))}

        {currentCoursNotions?.map((notionProgress) => (
          <NotionCardProgression
            key={notionProgress?.notion?.id}
            currentProgress={notionProgress}
            onClick={() => {}}
          />
        ))}

        {categories
          ?.filter((c) => c?.exercisesResultsSummary !== null)
          ?.map((ueCategory) => (
            <UECategoryCardProgression
              key={ueCategory.id}
              ueCategory={ueCategory}
              onClick={onUeCategoryClick}
            />
          ))}
      </div>
    </>
  );
};
