import { router } from 'umi';
import { Button, Divider } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { renderNote } from '@/shared/pages/profile/components/MainProfileResults/ProfileGradesResults';
import { EyeOutlined } from '@ant-design/icons';

export function SingleSessionHistory({ serie, userId, session }) {
  const { t } = useTranslation();
  const getSessionLink = () => {
    const statId = session?.id;
    if (serie?.mySession) {
      // Cas session 1
      router.push(`/qcm/correction/${serie.id_qcm}/session/${serie?.mySession?.id}`);
    } else {
      const sessionId = serie?.statistiques?.qcmSessionId;
      if (sessionId) {
        // Cas session 2
        router.push(`/qcm/correction/${serie.id_qcm}/user/${userId}/session/${sessionId}`);
      } else {
        // Pas de session
        if (userId) {
          // Résultat(s) spécifique à un utilisateur
          router.push(`/qcm/correction/${serie.id_qcm}/user/${userId}/stat/${statId}`);
        } else {
          // Mon résultat
          router.push(`/qcm/correction/${serie.id_qcm}/stat/${statId}`);
        }
      }
    }
  };
  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div style={{ width: 100 }}>{renderNote(session.note, serie, true)}</div>
        <div>{dayjs(session.date).format('DD/MM/YYYY HH:mm:ss')}</div>
        <Button
          onClick={() => getSessionLink()}
          style={{ marginRight: 16 }}
          type="primary"
          shape="circle"
          icon={<EyeOutlined />}
        />
      </div>
      <Divider />
    </>
  );
}
