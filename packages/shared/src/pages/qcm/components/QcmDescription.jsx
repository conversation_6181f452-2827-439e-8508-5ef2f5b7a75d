import { getQuestionsNumberFromQcm } from '@/shared/services/qcm.js';
import { tr } from '@/shared/services/translate.js';
import React from 'react';
import { useTranslation } from 'react-i18next';


export const QcmTitle = ({ qcm }) => {
  const {t, i18n} = useTranslation();
  return (
    <div style={{textAlign: "center"}}>
      <h3>{qcm?.[tr('titre')]}</h3>
      <h5>{getQuestionsNumberFromQcm(qcm)} {t('general.questions')}</h5>
      <p>{qcm?.[tr('description')]}</p>
    </div>
  )
}