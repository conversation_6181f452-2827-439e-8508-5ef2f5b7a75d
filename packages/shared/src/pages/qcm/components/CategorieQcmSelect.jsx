import i18n from '@/shared/i18n.js';
import { useQuery } from '@apollo/client';
import { GET_SOUS_CATEGORIES_FOR_UE } from '@/shared/graphql/qcm';
import { Select } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

export const ALL_SUBCATEGORIES = {
  value: i18n.t('AllCategories'),
  key: -1
};

export const SousCategorieSelector = ({ ueId, onSousCategorieSelect, selectedId = null }) => {
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(GET_SOUS_CATEGORIES_FOR_UE, {
    fetchPolicy: 'no-cache',
    variables: { ueId }
  });

  const isSubCategoryReady = () => !loading && !error && data && data.sousCategories;

  const getSubCategory = () => {
    if (isSubCategoryReady()) {
      const { sousCategories } = data;
      const tout = {
        name: ALL_SUBCATEGORIES.value,
        id: ALL_SUBCATEGORIES.key
      };
      return [tout, ...sousCategories];
    }
    return {};
  };

  const getOptions = () => {
    return (
      isSubCategoryReady() &&
      getSubCategory().map((categ) => ({
        value: categ.name,
        key: categ.id
      }))
    );
  };

  const defaultValue = ALL_SUBCATEGORIES.value;

  return (
    <>
      <div>
        <Select
          loading={loading}
          size="large"
          style={{ width: 300 }}
          showSearch
          showArrow
          placeholder={t('ChooseCategory')}
          options={getOptions()}
          defaultValue={defaultValue}
          // onChange={handleSousCategorieChange}
          onSelect={onSousCategorieSelect}
          value={getOptions() && getOptions().find((opt) => opt.key === selectedId).value}
          listItemHeight={10}
          listHeight={250}
          virtual={false}
        />
      </div>
    </>
  );
};
