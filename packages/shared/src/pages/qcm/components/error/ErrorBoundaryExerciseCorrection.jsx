import React from 'react';

class ErrorBoundaryExerciseCorrection extends React.Component {
  constructor(props) {
    super(props);
    this.state = { error: '' };
  }

  componentDidCatch(error) {
    this.setState({ error: `${error.name}: ${error.message}` });
  }

  render() {
    const { error } = this.state;
    const { idQuestion } = this.props; // Récupération de la prop idQuestion
    if (error) {
      return (
        <div>
          <p>
            Erreur dans l'exercice {idQuestion} (vérifiez qu'il soit bien configuré) : {error}
          </p>
        </div>
      );
    } else {
      return <>{this.props.children}</>;
    }
  }
}

export default ErrorBoundaryExerciseCorrection;
