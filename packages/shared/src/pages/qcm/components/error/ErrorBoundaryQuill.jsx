import React from 'react';

class ErrorBoundaryQuill extends React.Component {
  constructor(props) {
    super(props);
    this.state = { error: '' };
  }

  componentDidCatch(error) {
    this.setState({ error: `${error.name}: ${error.message}` });
  }

  // Méthode pour réinitialiser l'erreur et recharger le composant
  handleReload = () => {
    this.setState({ error: '' }); // Réinitialise l'état d'erreur
  };

  render() {
    const { error } = this.state;
    if (error) {
      return (
        <div>
          <p>Erreur dans l'éditeur : {error}</p>
          <button onClick={this.handleReload}>Ignorer et Recharger</button>
        </div>
      );
    }
    // Si pas d'erreur, rendre les enfants normalement
    return <>{this.props.children}</>;
  }
}

export default ErrorBoundaryQuill;
