import React from 'react';
import { Card, Tag, Space, Image } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import { useTranslation } from 'react-i18next';
import { buildQcmImage } from '@/shared/services/qcm';

/**
 * Simple correction component for REORDER_ELEMENTS exercises
 * Following business requirements:
 * - Automated backend evaluation (already done)
 * - Visual feedback comparing student answers vs correct order
 * - Simple UI showing correct/incorrect positions
 */
export const ReorderElementsCorrection = ({ question, answerHistory }) => {
  const { t } = useTranslation();

  if (!question || !answerHistory) {
    return (
      <div className="reorder-elements-correction__no-data">
        {t('ReorderElements.NoDataAvailable')}
      </div>
    );
  }

  // Get correct order from question settings
  const correctOrder = question?.settings?.correctOrder || [];
  
  // Extract student answers from answerHistory
  let studentAnswers = [];

  // Try to get student answers from different possible locations
  if (answerHistory?.jsonAnswers) {
    if (Array.isArray(answerHistory.jsonAnswers)) {
      // If jsonAnswers is an array, look for userOrder in first element
      const firstAnswer = answerHistory.jsonAnswers[0];
      if (firstAnswer?.userOrder && Array.isArray(firstAnswer.userOrder)) {
        studentAnswers = firstAnswer.userOrder;
      }
    } else if (answerHistory.jsonAnswers?.userOrder && Array.isArray(answerHistory.jsonAnswers.userOrder)) {
      // If jsonAnswers is an object with userOrder
      studentAnswers = answerHistory.jsonAnswers.userOrder;
    }
  }



  // Fallback: try to extract from qcmStatsQuestion
  if (studentAnswers.length === 0 && answerHistory?.qcmStatsQuestion) {
    studentAnswers = answerHistory.qcmStatsQuestion
      .sort((a, b) => a.answerId - b.answerId) // Sort by position
      .map(stat => stat.value)
      .filter(value => value && value.trim() !== '');
  }

  // Smart detection: determine if we should use ID-based or text-based comparison
  const shouldUseIdComparison = () => {
    // Check if any element is image-only (no text content)
    const hasImageOnlyElements = correctOrder.some(item => {
      if (typeof item === 'object') {
        const hasText = item.content?.replace(/<[^>]*>/g, '').trim();
        const hasImage = item.imageFileName;
        return hasImage && !hasText; // Image-only element
      }
      return false;
    });

    // Check if student answers look like element IDs (start with "element-")
    const answersLookLikeIds = studentAnswers.some(answer =>
      typeof answer === 'string' && answer.startsWith('element-')
    );

    // Check if student answers are mostly empty (image-only case)
    const answersAreEmpty = studentAnswers.every(answer =>
      !answer || answer.trim() === ''
    );

    return hasImageOnlyElements || answersLookLikeIds || answersAreEmpty;
  };

  const useIdComparison = shouldUseIdComparison();

  // Calculate comparison results using appropriate method
  const comparisonResults = [];
  const maxLength = Math.max(studentAnswers.length, correctOrder.length);

  for (let i = 0; i < maxLength; i++) {
    const studentAnswer = studentAnswers[i] || '';
    const correctElement = correctOrder[i];

    let isCorrect = false;
    let studentElement = studentAnswer;

    if (useIdComparison) {
      // ID-based comparison for image-only or new exercises
      const correctId = correctElement?.id;
      isCorrect = studentAnswer === correctId;

      // Find full student element by ID
      if (studentAnswer && studentAnswer.trim() !== '') {
        const matchingElement = correctOrder.find(el => el.id === studentAnswer);
        if (matchingElement) {
          studentElement = matchingElement;
        }
      } else {
        // Special case: empty student answer for image-only elements
        // This means student didn't move the element, so it's in the shuffled position
        // We need to show what element was actually in this position
        // For now, we'll assume the element at this position is what the student "chose"
        studentElement = correctElement; // Show the element that was in this position
        isCorrect = false; // Since they didn't actively choose it, mark as incorrect
      }
    } else {
      // Text-based comparison for backward compatibility
      const correctText = typeof correctElement === 'string'
        ? correctElement
        : correctElement?.content?.replace(/<[^>]*>/g, '').trim() || '';

      isCorrect = studentAnswer === correctText;

      // Find full student element by text content (improved matching)
      if (studentAnswer) {
        const matchingElement = correctOrder.find(element => {
          if (typeof element === 'object') {
            // Try multiple matching strategies for robustness
            const elementText = element.content?.replace(/<[^>]*>/g, '').trim() || '';

            // Direct text match
            if (elementText === studentAnswer) {
              return true;
            }

            // Fallback: check if student answer contains the element text (for partial matches)
            if (elementText && studentAnswer.includes(elementText)) {
              return true;
            }

            // Fallback: check if element text contains student answer
            if (studentAnswer && elementText.includes(studentAnswer)) {
              return true;
            }
          }
          return element === studentAnswer;
        });
        if (matchingElement) {
          studentElement = matchingElement;
        } else {
          // If no exact match found, create a fallback element with the text
          // This ensures we always show something, even if matching fails
          studentElement = {
            content: `<p>${studentAnswer}</p>`,
            text: studentAnswer
          };
        }
      }
    }

    comparisonResults.push({
      position: i + 1,
      studentAnswer: studentElement, // Full element object for student answers
      correctAnswer: correctElement, // Full element object for correct answers
      isCorrect
    });
  }

  // Calculate summary stats
  const correctCount = comparisonResults.filter(r => r.isCorrect).length;
  const totalCount = correctOrder.length;
  const percentage = totalCount > 0 ? Math.round((correctCount / totalCount) * 100) : 0;
  const score = answerHistory?.pointsObtained || 0;
  const maxScore = answerHistory?.pointsPerQuestion || 1;

  const renderElement = (elementData, isCorrect, isStudentAnswer = false) => {
    const getElementClasses = () => {
      let classes = ['reorder-elements-correction__element'];
      if (isCorrect) {
        classes.push('reorder-elements-correction__element--correct');
      } else {
        classes.push('reorder-elements-correction__element--incorrect');
      }
      return classes.join(' ');
    };

    const getIconClasses = () => {
      let classes = ['reorder-elements-correction__element-icon'];
      if (isCorrect) {
        classes.push('reorder-elements-correction__element-icon--correct');
      } else {
        classes.push('reorder-elements-correction__element-icon--incorrect');
      }
      return classes.join(' ');
    };

    // Handle both string content and element objects
    let content = '';
    let imageFileName = null;

    if (typeof elementData === 'string') {
      content = elementData;
    } else if (elementData && typeof elementData === 'object') {
      content = elementData.content || elementData.text || '';
      imageFileName = elementData.imageFileName;
    }

    return (
      <div className={getElementClasses()}>
        {isCorrect ? (
          <CheckCircleOutlined className={getIconClasses()} />
        ) : (
          <CloseCircleOutlined className={getIconClasses()} />
        )}
        <div className="reorder-elements-correction__element-content">
          <div className="reorder-elements-correction__content-wrapper">
            <div className="reorder-elements-correction__text-content">
              {content && typeof content === 'string' ? (
                content.includes('<') || content.includes('&') ? (
                  <RenderQuillHtml content={content} />
                ) : (
                  <span>{content}</span>
                )
              ) : (
                <span className="reorder-elements-correction__element-missing-content">
                  {isStudentAnswer ? 'Aucune réponse' : 'Contenu manquant'}
                </span>
              )}
            </div>

            {imageFileName && (
              <div className="reorder-elements-correction__image-content">
                <Image
                  src={buildQcmImage(imageFileName)}
                  alt={t('ReorderElements.ElementImage')}
                  width={100}
                  height={80}
                  style={{ objectFit: 'cover', borderRadius: '4px' }}
                  preview={true}
                />
              </div>
            )}
          </div>
        </div>
        {!isStudentAnswer && (
          <Tag
            color={isCorrect ? 'green' : 'red'}
            className="reorder-elements-correction__element-tag"
          >
            {isCorrect ? 'Correct' : 'Incorrect'}
          </Tag>
        )}
      </div>
    );
  };

  return (
    <div className="reorder-elements-correction__container">
      {/* Removed separate summary card - score is now displayed in the main correction interface (Alexandre's feedback #5) */}

      <div className="reorder-elements-correction__comparison-container">
        <h4 className="reorder-elements-correction__comparison-title">
          Comparaison des réponses :
        </h4>

        {comparisonResults.map((result, index) => (
          <div key={index} className="reorder-elements-correction__position-group">
            <div className="reorder-elements-correction__position-label">
              {t('ReorderElements.PositionNumber', { position: result.position })}
            </div>

            <div className="reorder-elements-correction__answers-grid">
              <div>
                <div className="reorder-elements-correction__answer-section-label">
                  {t('ReorderElements.YourAnswer')}
                </div>
                {renderElement(result.studentAnswer, result.isCorrect, true)}
              </div>

              <div>
                <div className="reorder-elements-correction__answer-section-label">
                  {t('ReorderElements.ExpectedAnswer')}
                </div>
                {renderElement(result.correctAnswer, true, false)}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Legend */}
      <div className="reorder-elements-correction__legend">
        <Space>
          <CheckCircleOutlined className="reorder-elements-correction__legend-icon--correct" />
          <span>{t('ReorderElements.CorrectPosition')}</span>
          <CloseCircleOutlined className="reorder-elements-correction__legend-icon--incorrect" />
          <span>{t('ReorderElements.IncorrectPosition')}</span>
        </Space>
      </div>
    </div>
  );
};

export default ReorderElementsCorrection;
