import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils.js';
import { Avatar } from 'antd';
import React from 'react';


export const QCMImpliedCategoriesImageGroup = ({ ueCategoriesImpliquees }) => {

  const deduplicated = ueCategoriesImpliquees?.filter((v, i, a) => a?.findIndex(v2 => (v2?.id === v?.id)) === i);

  return (
    <Avatar.Group maxCount={5}>
      {deduplicated?.map(categ => (
        <Avatar size={50} src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + categ?.image)}/>
      ))}
    </Avatar.Group>
  );
};