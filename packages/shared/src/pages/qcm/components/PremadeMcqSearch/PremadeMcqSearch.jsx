import { NewQcmListFiltered } from '@/shared/pages/qcm/components/NewQCMListFiltered';
import { PremadeMcqFilters } from '@/shared/pages/qcm/components/PremadeMcqSearch/PremadeMcqFilters/PremadeMcqFilters';
import { getLastSelectedUE, setLastSelectedUE } from '@/shared/services/qcm';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

export function PremadeMcqSearch({ ueId, sousCategorieId, selectedMcqId, setSelectedMcqId }) {
  const { t } = useTranslation();
  const listQcmRef = useRef(null);

  const [selection, setSelection] = useState({
    ueSelected: getLastSelectedUE()
    //anneeSelected: getLastSelectedYearForQcm(),
    //subCategorySelected: ALL_SUBCATEGORIES.key,
    //coursSelected: ALL_SUBCATEGORIES.key
  });
  const [numberOfMcqDoneInUE, setNumberOfMcqDoneInUE] = useState(null);
  const [numberOfMcqInUE, setNumberOfMcqInUE] = useState(null);
  const [choixTypesQcm, setChoixTypeQcm] = useState([]);

  const setCurrentUEStats = ({ numberOfMcqDone, numberOfMcq }) => {
    setNumberOfMcqDoneInUE(numberOfMcqDone);
    setNumberOfMcqInUE(numberOfMcq);
  };

  // On load set default selection
  useEffect(() => {
    if (ueId) {
      setSelection({
        ...selection,
        ueSelected: ueId
      });
      setLastSelectedUE(ueId);
    }
  }, []);

  return (
    <>
      <PremadeMcqFilters
        selection={selection}
        setSelection={setSelection}
        setChoixTypeQcm={setChoixTypeQcm}
      />

      {choixTypesQcm?.length > 0 && selection.ueSelected && selection?.annees?.length > 0 && (
        <div
          style={{
            margin: 'auto',
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'center',
            width: '100%'
          }}
        >
          <NewQcmListFiltered
            selection={selection}
            setCurrentUEStats={setCurrentUEStats}
            selectedMcqId={selectedMcqId}
            setSelectedMcqId={setSelectedMcqId}
            typeQcms={choixTypesQcm}
          />
        </div>
      )}
    </>
  );
}
