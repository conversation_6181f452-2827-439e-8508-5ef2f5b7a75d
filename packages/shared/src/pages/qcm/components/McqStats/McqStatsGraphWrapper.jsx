import useMediaQuery from '@/shared/hooks/useMediaQuery';
import ErrorBoundaryGraph from '@/shared/pages/qcm/components/error/ErrorBoundaryGraph';
import { McqStatsGraph } from '@/shared/pages/qcm/components/McqStats/McqStatsGraph';
import { getMaxPointsFromQcm } from '@/shared/services/qcm';
import { isAdmin, isTuteur } from '@/shared/utils/authority';
import { Button } from 'antd';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';

export function McqStatsGraphWrapper({ mcq, dataRanking, hideSeeAllButton = false, gradeOutOf }) {
  const { t } = useTranslation();
  const maxGrade = getMaxPointsFromQcm(mcq);
  const isSmallScreen = useMediaQuery('(max-width: 600px)');

  const adaptedDataRanking = useMemo(() => {
    const result = dataRanking ? JSON.parse(JSON.stringify(dataRanking)) : {};
    if (gradeOutOf) {
      result?.monClassementQcm?.notesParEffectif?.forEach((amountPerGrade) => {
        amountPerGrade.note =
          Math.round(((gradeOutOf * amountPerGrade.note) / maxGrade) * 100) / 100;
      });
    }
    return result;
  }, [gradeOutOf, maxGrade, dataRanking]);

  const adaptedGrade = useMemo(() => {
    return gradeOutOf
      ? Math.round(((gradeOutOf * mcq?.resultat?.note) / maxGrade) * 100) / 100
      : mcq?.resultat?.note;
  }, [gradeOutOf, mcq]);

  return (
    <div
      style={{
        flex: isSmallScreen ? 'none' : 3,
        minWidth: 302,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {(isAdmin() || isTuteur()) && !hideSeeAllButton && (
        <div style={{ display: 'flex', justifyContent: 'end' }}>
          <Button
            onClick={() => router.push(`/qcm/${mcq && mcq.id_qcm}/resultats-eleves`)}
            type="primary"
            style={{ marginBottom: 12, height: 'auto' }}
          >
            {t('SeeUserResultsForThisExercise')}
          </Button>
        </div>
      )}
      <div
        style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          minHeight: '300px',
          justifyContent: 'center'
        }}
      >
        <ErrorBoundaryGraph>
          <McqStatsGraph
            dataRanking={gradeOutOf ? adaptedDataRanking : dataRanking}
            gradeOutOf={gradeOutOf}
            maxGrade={gradeOutOf ? gradeOutOf : maxGrade}
            myGrade={gradeOutOf ? adaptedGrade : mcq?.resultat?.note}
            minGrade={mcq?.resultat.minGrade}
          />
        </ErrorBoundaryGraph>
      </div>
    </div>
  );
}
