import { SpringListItemTransition } from '@/shared/assets/transitions/SpringListItemTransition';
import { GET_SESSIONS_QCM } from '@/shared/graphql/qcm';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { McqSingleStat } from '@/shared/pages/qcm/components/McqStats/McqSingleStat';
import { PreviousSession } from '@/shared/pages/qcm/components/McqStats/PreviousSession';
import { getMaxPointsFromQcm } from '@/shared/services/qcm';
import { HourglassTwoTone } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { Button, Popover } from 'antd';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';

export function McqStatsGrid({ mcq, dataRanking }) {
  const { t } = useTranslation();
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  const { data } = useQuery(GET_SESSIONS_QCM, {
    fetchPolicy: 'no-cache',
    variables: { id_qcm: mcq.id_qcm }
  });
  const sessions = data?.sessionsForMcq;

  // Maximum grade obtainable
  const maxGrade = getMaxPointsFromQcm(mcq);
  const rank = dataRanking?.monClassementQcm;
  const rankString =
    rank?.monClassement > rank?.total
      ? null
      : `${rank?.monClassement}${rank?.monClassement === 1 ? 'er' : 'ème'}/${rank?.total}`;

  return (
    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', flex: 1, gap: 24 }}>
      <SpringListItemTransition
        uniqueId="my-grade"
        index={1}
        fullWidth
        style={{ gridColumnStart: 1, gridColumnEnd: 3, textAlign: 'center' }}
      >
        <McqSingleStat
          label={t('MyGrade')}
          value={
            mcq?.resultat?.note !== null ? (
              `${mcq?.resultat?.note}/${maxGrade}`
            ) : (
              <HourglassTwoTone twoToneColor={primaryColor} />
            )
          }
          type={mcq?.resultat?.note !== null ? 'outOf' : 'none'}
          index={1}
          decimals={2}
        >
          {sessions?.length > 0 && (
            <Popover content={<PreviousSession sessions={sessions} mcqId={mcq?.id_qcm} />}>
              <Button>{t('ResultsHistory')}</Button>
            </Popover>
          )}
        </McqSingleStat>
      </SpringListItemTransition>

      <SpringListItemTransition uniqueId="ranking" index={2} fullWidth>
        <McqSingleStat
          label={t('general.Rank')}
          value={
            rankString !== null ? rankString : <HourglassTwoTone twoToneColor={primaryColor} />
          }
          type={rankString ? 'rank' : 'none'}
          index={2}
        />
      </SpringListItemTransition>

      <SpringListItemTransition uniqueId="time-spent" index={3} fullWidth>
        <McqSingleStat
          label={t('TimeSpent')}
          value={
            mcq?.resultat?.seconds !== null ? (
              mcq?.resultat?.seconds
            ) : (
              <HourglassTwoTone twoToneColor={primaryColor} />
            )
          }
          type={mcq?.resultat?.seconds !== null ? 'time' : 'none'}
          index={3}
        />
      </SpringListItemTransition>

      <SpringListItemTransition uniqueId="average-grade" index={4} fullWidth>
        <McqSingleStat
          label={t('general.Average')}
          value={`${mcq?.resultat?.moyenne}/${maxGrade}`}
          type="outOf"
          index={4}
          decimals={2}
        />
      </SpringListItemTransition>

      <SpringListItemTransition uniqueId="additional-stat" index={5} fullWidth>
        <McqSingleStat
          label={t('Completions')}
          value={mcq?.countResultatsEleves}
          index={5}
          statExplanation={t('CompletionsExplanation')}
        />
      </SpringListItemTransition>

      <SpringListItemTransition uniqueId="min-grade" index={6} fullWidth>
        <McqSingleStat
          label={t('MinGrade')}
          value={`${mcq?.resultat?.minGrade}/${maxGrade}`}
          type="outOf"
          index={6}
          decimals={2}
        />
      </SpringListItemTransition>

      <SpringListItemTransition uniqueId="max-grade" index={7} fullWidth>
        <McqSingleStat
          label={t('MaxGrade')}
          value={`${mcq?.resultat?.maxGrade}/${maxGrade}`}
          type="outOf"
          index={7}
          decimals={2}
        />
      </SpringListItemTransition>
    </div>
  );
}
