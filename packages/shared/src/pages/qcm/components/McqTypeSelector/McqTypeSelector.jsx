import personalizedExerciceHeader from '@/shared/assets/personalizedExerciceHeader.png';
import premadeExerciceHeader from '@/shared/assets/premadeExerciceHeader.png';
import useMediaQuery from '@/shared/hooks/useMediaQuery';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { isMobile } from '@/shared/utils/utils';
import { Button } from 'antd';
import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';

export function McqTypeSelector({ selectedExerciceType, setSelectedExerciceType }) {
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  const { t } = useTranslation();

  const [hoverLeft, setHoverLeft] = useState(false);
  const [hoverRight, setHoverRight] = useState(false);

  const isWrapped = useMediaQuery('(max-width: 699px)');

  const isSelectedOrHovered = (side) => {
    switch (side) {
      case 'left':
        return hoverLeft || selectedExerciceType === 'personalized';
      case 'right':
        return hoverRight || selectedExerciceType === 'premade';
      default:
        return false;
    }
  };

  const halfStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    flex: 1,
    top: 0,
    width: '100%',
    height: '100%',
    minWidth: 350,
    overflow: 'hidden',
    transition: 'all 0.6s'
  };

  const descStyle = {
    height: '100%',
    display: 'grid',
    justifyContent: 'end',
    alignItems: 'center',
    gridTemplateRows: '1fr 35px',
    paddingBottom: isMobile || isWrapped ? 25 : 50
  };

  const imgStyle = {
    transition: 'all 0.6s',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: '50% 0',
    backgroundSize: 'contain',
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0
  };

  const titleStyle = {
    zIndex: 2,
    transition: 'all 0.6s',
    fontSize: isWrapped ? 'calc(1vw + 1.8em)' : 52,
    maxWidth: 380,
    textAlign: 'center',
    alignSelf: 'center',
    margin: 0
  };

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        flexWrap: 'wrap',
        flexDirection: isMobile || isWrapped ? 'column' : 'row',
        height: isMobile || isWrapped ? 380 : 350,
        maskImage: 'linear-gradient(rgb(0, 0, 0) 2%, rgb(0, 0, 0) 90%, transparent 100%)'
      }}
    >
      <div
        style={{
          ...halfStyle,
          right: 0,
          backgroundColor: isSelectedOrHovered('right') ? primaryColor : `${primaryColor}25`,
          flex: isSelectedOrHovered('right') ? 1.2 : 1
        }}
        onMouseEnter={() => setHoverRight(true)}
        onMouseLeave={() => setHoverRight(false)}
        onClick={() => setSelectedExerciceType('premade')}
      >
        <div
          style={{
            ...imgStyle,
            backgroundImage: `url(${premadeExerciceHeader})`,
            opacity: isSelectedOrHovered('right') ? 0.3 : 0
          }}
        />
        <div style={descStyle}>
          <h2 style={{ ...titleStyle, color: isSelectedOrHovered('right') ? 'white' : 'black' }}>
            {t('PremadeTraining')}
          </h2>
          <Button
            type="primary"
            size="large"
            style={{ width: 'fit-content', justifySelf: 'center' }}
            onClick={() => setSelectedExerciceType('premade')}
          >
            Chercher une série
          </Button>
        </div>
      </div>

      <div
        style={{
          ...halfStyle,
          left: 0,
          backgroundColor: isSelectedOrHovered('left') ? primaryColor : `${primaryColor}25`,
          flex: isSelectedOrHovered('left') ? 1.2 : 1
        }}
        onMouseEnter={() => setHoverLeft(true)}
        onMouseLeave={() => setHoverLeft(false)}
        onClick={() => setSelectedExerciceType('personalized')}
      >
        <div
          style={{
            ...imgStyle,
            backgroundImage: `url(${personalizedExerciceHeader})`,
            opacity: isSelectedOrHovered('left') ? 0.3 : 0
          }}
        />
        <div style={descStyle}>
          <h2 style={{ ...titleStyle, color: isSelectedOrHovered('left') ? 'white' : 'black' }}>
            {t('PersonalizedTraining')}
          </h2>
          <Button
            type="primary"
            size="large"
            style={{ width: 'fit-content', justifySelf: 'center' }}
            onClick={() => setSelectedExerciceType('personalized')}
          >
            Créer une série
          </Button>
        </div>
      </div>
    </div>
  );
}
