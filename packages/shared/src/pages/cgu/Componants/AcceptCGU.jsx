import React, {useContext, useState} from 'react';
import {Button,Checkbox,Spin,message} from 'antd';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import {gql, useMutation} from "@apollo/client";
import router from 'umi/router';
import {WebSiteCGU} from "@/shared/utils/utils";
import {useTranslation} from "react-i18next";


const ACCEPT_CGU = gql`mutation  {acceptCgu}`;
const REJECT_CGU = gql`mutation {rejectCgu}`

function CGUFrame({path}) {
  // Le componant d'affichage de l'iframe des CGU
  const url=path
  const [isLoading,setIsLoading]=useState(true)

  return (
    <div style={{
      width:"100%",height:"100%",
      boxShadow: "0px 0px 5px 2px rgba(0,0,0,0.75)",
      position:'relative',
      display:'flex',justifyContent: 'center', alignItems: 'center'
    }}>
      {isLoading && (
        <div style={{ position: 'absolute', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin size="large"/>
        </div>
      )}
      <iframe
        src={url}
        style={{width:"100%", height:"100%" , visibility:isLoading ? 'hidden' : 'visible'}}
        onLoad={()=>setIsLoading(false)}
      />
    </div>
  )
}

export const AcceptCGU = () => {
  const { t } = useTranslation();
  const {refetchMe} = useContext(GlobalContext); // récupération de me / refetchMe depuis le blankLayout
  const cguLink=WebSiteCGU
  const [checkBool,setCheckBool]=useState(false)

  // Mutation qui lorsque réussi, push vers /home
  const [acceptCgu]= useMutation(ACCEPT_CGU,{
    onCompleted:async (data)=>{
      await refetchMe()
      if (data.acceptCgu){
        message.success({content:t('Updated')})
        router.push('/home')
      } else {message.error(t("Error"))}
    }
  })

  const [rejectCgu]= useMutation(REJECT_CGU,{
    onCompleted:async (data)=>{
      await refetchMe()
      if (data.rejectCgu){
        message.success({content:t('Updated')})
      } else {message.error(t("Error"))}
    }
  })

  return (
    <div style={{height:"100%",width:"100%",marginTop:'50px'}}>
      <CGUFrame path={cguLink}/>
      <div style={{marginTop:"10px",display:'flex',justifyContent:'center',alignItems:'center',gap:'10px',flexWrap:'wrap'}}>
        <Checkbox
          onChange={(e)=>{setCheckBool(e.target.checked)}}
          style={{fontSize:"24px",display:'flex',alignItems:'center',lineHeight:'1'}}
        >{t("IHaveReadCGUCheckbox")}</Checkbox>
        <div style={{display :'inline'}}>
          <Button disabled={!checkBool} onClick={()=>{acceptCgu()}}>{t("AcceptCGUButton")}</Button>
          {/* Boutton pour rejetter les CGU
          <Button disabled={!checkBool} onClick={()=>{rejectCgu()}}>{t("RejectCGUButton")}</Button>
          */}
        </div>
      </div>
    </div>
  )
};