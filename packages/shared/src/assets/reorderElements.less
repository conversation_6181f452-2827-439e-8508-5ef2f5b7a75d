/* ================================================
   ReorderElements Styles
   ================================================ */

/* ReorderElementsCorrection.less - Styles for reorder elements correction interface */
.reorder-elements-correction {
  &__container {
    margin-top: 16px;
  }

  &__no-data {
    padding: 16px;
    text-align: center;
    color: #999;
  }

  // Removed summary card styles - score is now displayed in main correction interface (<PERSON>'s feedback #5)

  &__comparison-container {
    display: grid;
    gap: 16px;
  }

  &__comparison-title {
    margin: 0 0 12px 0;
    color: #262626;
    font-size: 16px;
    font-weight: 500;
  }

  &__position-group {
    display: grid;
    gap: 8px;
  }

  &__position-label {
    font-size: 14px;
    font-weight: 500;
    color: #595959;
    margin-bottom: 4px;
  }

  &__answers-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  &__answer-section {
    &-label {
      font-size: 12px;
      color: #8c8c8c;
      margin-bottom: 4px;
    }
  }

  &__element {
    padding: 12px;
    border-radius: 6px;
    min-height: 50px;
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid;
    transition: all 0.2s ease;

    &--correct {
      background-color: #f6ffed;
      border-color: #b7eb8f;
    }

    &--incorrect {
      background-color: #fff2f0;
      border-color: #ffccc7;
    }

    &-content {
      flex: 1;
    }

    &-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &-text-content {
      flex: 1;
    }

    &-image-content {
      flex-shrink: 0;
    }

    &-icon {
      font-size: 16px;

      &--correct {
        color: #52c41a;
      }

      &--incorrect {
        color: #ff4d4f;
      }
    }

    &-missing-content {
      color: #999;
      font-style: italic;
    }

    &-tag {
      margin: 0;
    }
  }

  &__legend {
    margin-top: 16px;
    padding: 12px;
    background-color: #fafafa;
    border-radius: 6px;
    font-size: 12px;
    color: #595959;

    &-icon {
      &--correct {
        color: #52c41a;
      }

      &--incorrect {
        color: #ff4d4f;
      }
    }
  }
}

// Responsive adjustments for correction
@media (max-width: 768px) {
  .reorder-elements-correction {
    &__answers-grid {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    &__summary-card {
      .ant-space {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;

        .ant-space-item {
          width: 100%;
        }
      }
    }
  }
}

/* ReorderElementsQuestion.less - Styles for student reorder elements interface */
.reorder-elements-question {
  &__container {
    padding: 16px;
  }

  &__sortable-container {
    min-height: 200px;
    border-radius: 8px;
    padding: 8px;
  }

  &__item {
    margin-bottom: 8px;
    cursor: grab;
    user-select: none;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &--disabled {
      cursor: default;
    }

    &--dragging {
      transform: rotate(2deg);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      opacity: 0.5;
    }

    &--correct {
      background-color: #f6ffed;
      border-color: #b7eb8f;
      box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
    }

    &--incorrect {
      background-color: #fff2f0;
      border-color: #ffccc7;
      box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
    }

    &:hover:not(&--disabled):not(&--dragging) {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }

  &__content {
    display: flex;
    align-items: center;
  }

  &__drag-handle {
    cursor: grab;
    color: #999;
    margin-right: 12px;
    font-size: 16px;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f0f0f0;
    }
  }

  &__position-indicator {
    min-width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    margin-right: 12px;
    transition: all 0.2s ease;

    &--correct {
      background-color: #52c41a;
      color: white;
    }

    &--incorrect {
      background-color: #ff4d4f;
      color: white;
    }

    &--default {
      background-color: #1890ff;
      color: white;
    }
  }

  &__element-content {
    flex: 1;
    font-size: 15px;
    line-height: 1.4;

    // Add font weight for correction mode
    .reorder-elements-question__item--correct &,
    .reorder-elements-question__item--incorrect & {
      font-weight: 500;
    }
  }

  &__content-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &__text-content {
    flex: 1;
  }

  &__image-content {
    flex-shrink: 0;
  }

  &__correction-indicator {
    display: flex;
    align-items: center;
    gap: 4px;

    &--correct {
      color: #52c41a;
    }

    &--incorrect {
      color: #ff4d4f;
    }

    .icon {
      font-size: 16px;
    }

    .position-text {
      font-size: 12px;
      font-weight: bold;
    }
  }

  &__drag-overlay {
    .reorder-elements-question__item {
      background-color: #f0f8ff;
      border: 2px dashed #1890ff;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }
  }
}

/* EditReorderElements.less - Styles for admin reorder elements configuration interface */
.edit-reorder-elements {
  &__container {
    padding: 16px;
  }

  &__header {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-content {
      h3 {
        margin: 0;
        color: #1890ff;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 13px;
      }
    }

    &-actions {
      .ant-btn {
        border-radius: 6px;
      }
    }
  }

  &__instructions {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f0f8ff;
    border-radius: 6px;
    border: 1px solid #d9d9d9;

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  &__sortable-container {
    min-height: 200px;
  }

  &__element-item {
    margin-bottom: 8px;
    background-color: white;
    border: 1px solid #d9d9d9;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &--dragging {
      background-color: #f0f8ff;
      border: 2px dashed #1890ff;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
      opacity: 0.5;
    }

    .ant-card-body {
      padding: 12px;
    }
  }

  &__element-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &__drag-handle {
    cursor: grab;
    color: #999;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f0f0f0;
    }

    &:active {
      cursor: grabbing;
    }
  }

  &__position-indicator {
    min-width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #1890ff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
    flex-shrink: 0;
  }

  &__content-container {
    display: flex;
    gap: 12px;
    align-items: flex-start;
    flex: 1;
  }

  &__editor-container {
    flex: 1;

    .ql-editor {
      border-radius: 6px;
    }
  }

  &__image-container {
    flex-shrink: 0;
    display: flex;
    align-items: center;
  }

  &__delete-button {
    opacity: 1;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover:not(&--disabled) {
      transform: scale(1.1);
      box-shadow: 0 4px 8px rgba(255, 77, 79, 0.3);
    }

    &--disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  &__warning-message {
    padding: 16px;
    background-color: #fff2e8;
    border: 1px solid #ffbb96;
    border-radius: 8px;
    margin-top: 16px;
    box-shadow: 0 2px 4px rgba(255, 187, 150, 0.2);

    p {
      margin: 0;
      color: #d46b08;
      font-size: 14px;
      font-weight: 500;
    }
  }
}
