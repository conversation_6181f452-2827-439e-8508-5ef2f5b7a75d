<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ns1="http://sozi.baierouge.fr" xmlns:cc="http://creativecommons.org/ns#" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:dc="http://purl.org/dc/elements/1.1/" id="svg2" xml:space="preserve" viewBox="0 0 20.7 20.7" version="1.1" inkscape:version="0.91 r13725">
  <defs id="defs6">
    <radialGradient id="radialGradient24" gradientUnits="userSpaceOnUse" cy="-2.6162" cx="42.002" gradientTransform="matrix(.56568 -.56568 .56568 .56568 -14.24 33.64)" r="8.4322">
      <stop id="stop26" style="stop-color:#e8f4f7" offset="0"/>
      <stop id="stop28" style="stop-color:#e6f3f6" offset=".36860"/>
      <stop id="stop30" style="stop-color:#deeff3" offset=".56470"/>
      <stop id="stop32" style="stop-color:#d0e9ef" offset=".7216"/>
      <stop id="stop34" style="stop-color:#bddfe8" offset=".85880"/>
      <stop id="stop36" style="stop-color:#a4d4e0" offset=".98040"/>
      <stop id="stop38" style="stop-color:#9fd1de" offset="1"/>
    </radialGradient>
    <clipPath id="clipPath46">
      <path id="path48" d="m0-0.44h17v17h-17v-17z" inkscape:connector-curvature="0"/>
    </clipPath>
    <mask id="mask50">
      <g id="g52">
        <g id="g54" clip-path="url(#clipPath46)">
          <path id="path56" style="fill:#000000;fill-opacity:.67190" d="m0-0.44h17v17h-17v-17z" inkscape:connector-curvature="0"/>
        </g>
      </g>
    </mask>
    <clipPath id="clipPath62">
      <path id="path64" d="m0-0.44h17v17h-17v-17z" inkscape:connector-curvature="0"/>
    </clipPath>
    <clipPath id="clipPath66">
      <path id="path68" d="m0 0h17v17h-17v-17z" inkscape:connector-curvature="0"/>
    </clipPath>
    <clipPath id="clipPath96">
      <path id="path98" d="m2 0.56h14v9h-14v-9z" inkscape:connector-curvature="0"/>
    </clipPath>
    <mask id="mask100">
      <g id="g102">
        <g id="g104" clip-path="url(#clipPath96)">
          <path id="path106" style="fill:#000000;fill-opacity:.33980" d="m2 0.56h14v9h-14v-9z" inkscape:connector-curvature="0"/>
        </g>
      </g>
    </mask>
    <clipPath id="clipPath112">
      <path id="path114" d="m2 0.56h14v9h-14v-9z" inkscape:connector-curvature="0"/>
    </clipPath>
    <clipPath id="clipPath116">
      <path id="path118" d="m2 1h14v9h-14v-9z" inkscape:connector-curvature="0"/>
    </clipPath>
  </defs>
  <g id="g10" transform="matrix(1.25 0 0 -1.25 0 20.7)">
    <g id="g12">
      <g id="g14">
        <g id="g16">
          <g id="g22">
            <path id="path40" style="fill-rule:evenodd;fill:url(#radialGradient24)" d="m16.359 8.279c0-2.239-0.785-4.145-2.359-5.719-1.602-1.574-3.508-2.359-5.719-2.359-2.242 0-4.148 0.785-5.719 2.359-1.574 1.574-2.363 3.48-2.363 5.719 0 2.242 0.789 4.148 2.363 5.722 1.571 1.571 3.477 2.36 5.719 2.36 2.211 0 4.117-0.789 5.719-2.36 1.574-1.574 2.359-3.48 2.359-5.722z" inkscape:connector-curvature="0"/>
          </g>
        </g>
      </g>
      <g id="g42">
        <g id="g58" mask="url(#mask50)">
          <g id="g70">
            <g id="g72" clip-path="url(#clipPath62)">
              <g id="g74" transform="translate(0 -0.44)">
                <g id="g78">
                  <g id="g80" clip-path="url(#clipPath66)">
                    <g id="g82">
                      <g id="g84" transform="translate(0,17)">
                        <path id="path86" style="stroke:#ffffff;stroke-width:0.4;fill:none" d="m16.359-8.281c0-2.239-0.785-4.145-2.359-5.719-1.602-1.574-3.508-2.359-5.719-2.359-2.242 0-4.148 0.785-5.722 2.359-1.571 1.574-2.36 3.48-2.36 5.719 0 2.242 0.789 4.148 2.36 5.722 1.574 1.571 3.48 2.36 5.722 2.36 2.211 0 4.117-0.789 5.719-2.36 1.574-1.574 2.359-3.48 2.359-5.722z" inkscape:connector-curvature="0"/>
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
      <path id="path88" style="fill-rule:evenodd;fill:#ffffff" d="m4.078 13.439c1.07-0.024 1.203-0.547 0.402-1.559-0.851-1.066-1.441-1.425-1.757-1.082-0.297 0.321-0.27 0.84 0.078 1.563 0.347 0.746 0.773 1.105 1.277 1.078z" inkscape:connector-curvature="0"/>
      <path id="path90" style="fill-rule:evenodd;fill:#ffffff" d="m2.32 10.001c0.266-0.027 0.348-0.23 0.239-0.601-0.106-0.375-0.25-0.492-0.438-0.36-0.187 0.133-0.254 0.333-0.199 0.598 0.078 0.293 0.211 0.414 0.398 0.363z" inkscape:connector-curvature="0"/>
      <g id="g92">
        <g id="g108" mask="url(#mask100)">
          <g id="g120">
            <g id="g122" clip-path="url(#clipPath112)">
              <g id="g124" transform="translate(0 -0.44)">
                <g id="g128">
                  <g id="g130" clip-path="url(#clipPath116)">
                    <g id="g132">
                      <path id="path134" style="fill-rule:evenodd;fill:#ffffff" d="m14.48 7.121c0.454 0.934 0.708 1.797 0.758 2.598 0.16-0.824 0.082-1.758-0.238-2.801-0.641-2.051-2.121-3.57-4.441-4.559-1.731-0.746-3.586-0.691-5.559 0.161-0.941 0.406-1.723 0.886-2.34 1.441 0.469-0.391 1.113-0.711 1.942-0.961 1.757-0.535 3.679-0.426 5.757 0.32 1.895 0.692 3.266 1.961 4.121 3.801z" inkscape:connector-curvature="0"/>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
<metadata><rdf:RDF><cc:Work><dc:format>image/svg+xml</dc:format><dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/><cc:license rdf:resource="http://creativecommons.org/licenses/publicdomain/"/><dc:publisher><cc:Agent rdf:about="http://openclipart.org/"><dc:title>Openclipart</dc:title></cc:Agent></dc:publisher></cc:Work><cc:License rdf:about="http://creativecommons.org/licenses/publicdomain/"><cc:permits rdf:resource="http://creativecommons.org/ns#Reproduction"/><cc:permits rdf:resource="http://creativecommons.org/ns#Distribution"/><cc:permits rdf:resource="http://creativecommons.org/ns#DerivativeWorks"/></cc:License></rdf:RDF></metadata></svg>
