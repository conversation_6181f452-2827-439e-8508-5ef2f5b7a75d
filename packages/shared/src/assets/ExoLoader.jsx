import React from 'react';
import { useContext } from 'react';
import { motion } from 'framer-motion/dist/framer-motion';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { magenta, purple, volcano } from '@ant-design/colors';

import logo from '@/shared/assets/logo_blanc.svg';

export function ExoLoader({ displayLogo = false }) {
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  return (
    <div>
      <motion.div
        style={{ width: 100, height: 100 }}
        animate={{
          scale: [1, 1.8, 1.7, 2, 1],
          rotate: [0, 0, 180, 180, 0],
          background: [primaryColor, purple[5], magenta[4], volcano[4], primaryColor],
          borderRadius: ['5%', '5%', '50%', '50%', '5%']
        }}
        transition={{
          duration: 2.5,
          ease: 'easeInOut',
          times: [0, 0.25, 0.5, 0.75, 1],
          repeat: Infinity
        }}
      ></motion.div>
      {displayLogo && (
        <motion.div
          style={{ position: 'relative' }}
          animate={{
            top: [-95, -105, -95]
          }}
          transition={{
            ease: 'easeInOut',
            duration: 4,
            times: [0, 0.5, 1],
            repeat: Infinity
          }}
        >
          <img src={logo} alt="logo" style={{ width: 100, height: 100 }} />
        </motion.div>
      )}
    </div>
  );
}
