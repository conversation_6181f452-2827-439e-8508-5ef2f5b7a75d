import React from 'react';
import { motion } from 'framer-motion/dist/framer-motion';
import { isMobile } from '@/shared/utils/utils';

export function SpringListItemTransition({
  children,
  uniqueId,
  index,
  initialScale = 0.9,
  baseDelay = 0,
  delayMultiplier = 1,
  layout = true,
  fullWidth = false,
  style
}) {
  return (
    <motion.li
      layout={layout}
      initial={{ scale: initialScale, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: initialScale, opacity: 0 }}
      transition={{
        type: 'spring',
        damping: 12,
        delay: isMobile ? 0 : baseDelay + 0.2 * delayMultiplier * index
      }}
      key={uniqueId}
      style={{
        listStyle: 'none',
        width: fullWidth ? '100%' : 'fit-content',
        ...style
      }}
    >
      {children}
    </motion.li>
  );
}
