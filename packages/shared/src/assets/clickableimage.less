.image-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
  width: 100%;
  max-width: 300px; /* Ajustez selon vos besoins */
  outline: none; /* Supprimer le contour par défaut */
}

.clickable-image {
  /*
  width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  /*animation: pulse 2s infinite;*/
}

.pointer-icon {
  position: absolute;
  bottom: 10px; /* Positionner l'icône */
  left: 50%; /* Centrer horizontalement */
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.5); /* Arrière-plan semi-transparent */
  border-radius: 50%; /* Rend le conteneur circulaire */
  width: 30px; /* Ajustez pour un cercle parfait */
  height: 30px; /* Doit être égal à la largeur */
  display: flex; /* Centrer l'icône */
  justify-content: center; /* Centrer l'icône horizontalement */
  align-items: center; /* Centrer l'icône verticalement */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Ombre légère */
}

/* Styles pour les écrans mobiles */
@media (max-width: 600px) {
  .pointer-icon {
    font-size: 20px;
    bottom: 8px;
    padding: 6px;
  }

  .image-container {
    max-width: 90%;
  }

  .pointer-icon svg {
    width: 20px;
    height: 20px;
  }
}

/* Accessibilité : Indicateur de focus */
.image-container:focus {
  outline: 2px solid #007bff;
  outline-offset: 4px;
}