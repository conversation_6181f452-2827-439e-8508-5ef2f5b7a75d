import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useMutation } from '@apollo/client';
import { MUTATION_CREATE_TEMP_FILE } from '@/shared/graphql/formations';
import { openNotificationWithIcon } from '@/shared/utils/utils';

function useUploadTemp() {
  const { t } = useTranslation();
  const [tempUpload] = useMutation(MUTATION_CREATE_TEMP_FILE);
  const [isUploading, setIsUploading] = useState(null);

  async function uploadTempFile(file) {
    // Fonction qui défini si on utilise le upload multipart ou monopart
    try {
      if (!file) throw new Error(t('DiapoFE.ErrorMessageNoFileToUpload'));

      //Faire l'upload
      setIsUploading(true);

      const { data } = await tempUpload({ variables: { tempFile: file } });
      const sanitizedFilename = data?.uploadTempFile;
      setIsUploading(false);

      return sanitizedFilename;
    } catch (e) {
      setIsUploading(false);

      console.error(e);
      openNotificationWithIcon(
        'error',
        t('VideoFE.ErrorMessageDuringMultipartInitUploadTitle'),
        `${t('VideoFE.ErrorMessageDuringMultipartInitUploadExplanation')} ${e}`
      );
      throw new Error(e);
    }
  }

  return { uploadTempFile, isUploading };
}

export default useUploadTemp;
