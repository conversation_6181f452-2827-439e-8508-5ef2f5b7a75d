import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils.js';
import { Tag } from 'antd'
import React from 'react'
import router from 'umi/router'

export const NotionTag = ({ clickable = true, showImage = true, notion, tagStyle = { cursor: 'pointer' }, color = "blue" }) => {
  let tagStyleConfig = clickable ? { ...tagStyle } : {}
  return (
    <Tag onClick={() => {
      if (clickable) {
        router.push(`/notion/${notion.id}`)
      }
    }} style={tagStyleConfig} key={notion.id} color={color}>{notion.name} {notion.image && (
      <img
        onError={e => {
          e.target.style = 'display: none'
        }}
        style={{ height: '16px', width: 'auto', borderRadius: '2px' }}
        src={showImage && getUrlProtectedRessource(GlobalConfig.get().FILES_URL + notion.image)}
        alt="imageNotion"
      />
    )}
    </Tag>
  )
}