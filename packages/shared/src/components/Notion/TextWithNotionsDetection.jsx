import NotionPopover from '@/shared/components/Notion/NotionPopover.jsx';
import { buildTextWithNotions } from '@/shared/services/notion.js';
import React from 'react';

function TextWithNotionsDetection({ notions, text }) {
  if (!text) {
    return '';
  }
  if (text.includes('$')) {
    return text;
  }
  return (
    <span className={'exo-notion-text'}>
      {buildTextWithNotions(notions, text)?.map((word, key, elements) => {
        if (word?.originalWord?.includes('$')) {
          // Ignorer notions si LaTeX
          return word.originalWord;
        }
        if (word.notions?.length > 0) {
          const nextWord = elements[key + 1];
          // Si contient $ on ignore pour le latex
          return (
            <>
              <NotionPopover notions={word.notions}>
                <u>{word.originalWord}</u>
                {nextWord &&
                word?.notions
                  ?.map((n) => n.id)
                  .some((nnn) => nextWord?.notions?.map((nwn) => nwn.id)?.includes(nnn)) ? (
                  <u>&nbsp;</u>
                ) : (
                  <>&nbsp;</>
                )}
              </NotionPopover>
            </>
          );
        }
        return `${word.originalWord} `;
      })}
    </span>
  );
}

export default React.memo(TextWithNotionsDetection);
