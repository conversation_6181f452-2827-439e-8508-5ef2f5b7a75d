import { gql, useQuery } from '@apollo/client';
import { QUERY_HIERARCHY_TREE } from '@/shared/graphql/cours';
import React, { useEffect, useState } from 'react';
import cloneDeep from 'lodash/cloneDeep';
import {
  BankTwoTone,
  BookTwoTone,
  ContainerTwoTone,
  FileTwoTone,
  FolderTwoTone,
  QuestionCircleTwoTone,
  WarningTwoTone
} from '@ant-design/icons';
import { renderIcon } from '@/shared/pages/qcm/$index$';
import { Tree, TreeSelect, Tag } from 'antd';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';

export const validesTypes = {
  /* Les types pris en charge par le componant et Mapping entre le type de l'arboressence et le type du componant Interne
   * Pour rajouter un type, il suffit de pouvoir l'identifier depuis la fonction function determineAndTagNodeC_TYPE(node)
   * */
  CTYPE_UE: 'ue',
  CTYPE_COURS: 'cours',
  CTYPE_PAGE: 'step',
  CTYPE_FOLDER: 'folder',
  CTYPE_CATEGORY: 'category',
  CTYPE_UNKNOWN: 'unknown'
};

export const hierarchyComponantActions = {
  /* Les types d'actions pris en charge par le componant */
  CHECK_ALL: 'checkAll', // Set la sélection à tous les éléments
  UNCHECK_ALL: 'uncheckAll' // Set la sélection à aucun éléments
};

export const HierarchySelecterWatermarkCustom = ({
  rankToRemoveIfLeaf = [], // exemple = [validesTypes.CTYPE_COURS,validesTypes.CTYPE_FOLDER] // éléments qui seront retirés si ils n'ont pas d'enfants

  // Mono selection
  multiple = true, // Force la sélection à 1 élément. Il faut bien manipuler cette option avec le rankToRemove et tout

  // Récupération de la selection
  setterHookSelection, // Fonction externe qui sera apellée avec les informations retournées par le componant en argument
  simplificationFeature = null, // exemple : validesTypes.CTYPE_CATEGORY,

  // Initialisation de l'arbre
  initialisationVariable = null, // Exemple  = {[validesTypes.CTYPE_CATEGORY]:["4"]},
  initAll = null, // Exemple = [validesTypes.CTYPE_UE,validesTypes.CTYPE_FOLDER],

  // Divers
  additionalTreeProps = {},
  externalAction = null, // Permet de faire des actions sur ce componant en modifiant cette variable. Typiquement, check All / uncheck All
  resetExternalAction = () => {}, // hook de reset de l'externalAction

  // TreeSelect
  useTreeSelect = false, // Utilisation ou non du componant TreeSelect (au lieu de Tree)

  // Voir le tree pour un user spécifique (parents et admins only)
  forUserId = null
}) => {
  /* Componant Assez Complexe qui souhaite simplifier la sélection d'éléments dans la hiérarchie
  *
  * ##################### Utilisation Simple #####################
  *   1) Renseigner les types à tronquer dans rankToRemoveIfLeaf. Exemple : rankToRemoveIfLeaf = ["cours","category"]
  *   2) Renseigner le type à récupérer qui sera assigné par le setter dans simplificationFeature et setterHookFunction.
  *          Exemple : setterHookFunction=(setUeIds) et simplificationFeature="ue"
  *

  * ##################### Détails des Arguments ###################
  *  //// Support pour le tronquage de l'arbre
  *  - rankToRemoveIfLeaf (Array de validesTypes) => Permet de dynamiquement élaguer les éléments de la hiérarchy que l'on ne souhaites pas.
  *         Un élément sera (récursivement) retiré si jamais il est dans cette liste et n'a plus d'enfants.
  *         Exemple d'initilisation : rankToRemoveIfLeaf = [validesTypes.CTYPE_COURS,validesTypes.CTYPE_FOLDER,validesTypes.CTYPE_CATEGORY] (ou ["cours","folder","categories"])
  *         va uniquement permettre la sélection d'UE
  *
  *  //// Support pour la sélection unique
  *  - multiple (boolean) => Permet d'indiquer si l'on souhaite un ou plusieurs éléments.

  *  //// Récupération de la sélection
  *  - simplificationFeature (null | valideType) => Permet de choisir si l'on souhaite utiliser la feature de set simplifié avec le type input. Marche avec setterHookSelection
  *  - setterHookSelection (null | function setter) => Fonction qui va appellée de façon différente conditionnellement à simplificationFeature :
  *       -> Si simplificationFeature === valideType >>> Va utiliser le setter avec la sélection du type sélectionné (idFullyChecked[simplificationFeature])
  *       -> Si !simplificationFeature >>> Va appeller le setter avec la structure contenant les ids de chaque type selectionnés : {}
  *
  *  //// Feature d'initialisation.
  *  - initialisationVariable ({valideType1:[id1,id2,id3] , valideType2:[id4,id5,id6]}) => Permet d'initialiser le componant avec les valeures défini dans la liste
  *  - initAll (Array de validesTypes) => Permet d'initialiser le componant avec Toutes les valeures disponible pour les validesTypes de la liste
  *
  *  //// Divers
  *  - additionalTreeProps ({props}) => permet de custom le componant Tree
  *  - externalActions (string) => permet depuis l'extérieur d'effectuer des actions sur le componant (la variable est écoutée)
  *  - resetExternalAction ()=>{} => fonction permettant de reset external action (un simple : setExernalAction(null) )
  *
  *
  *  ##################### Détail de la logique #####################
  *  Ce componant permet pour des types très controlés de récupérer facilement des les ID de ces éléments. Voici les étapes du componant
  *
  *  ##### Général
  *  - Récupération de la hiérarchie pour l'user
  *  - Copie du résultat, tronquage de la hiérarchie, parse du tree et mise de tous les éléments de chaque type dans [allIds]
  *  - (Optional) => Initialisation avec des valeures
  *  - Une fois le parsing effectué, mise des données formatées dans dataTree
  *  - Lorsque dataTree est != null, affichage du componant.
  * */

  /* ################################################### */
  /* ################# Load des données ################ */
  /* ################################################### */
  const { data: { hierarchicalTreeData: readOnlyTree = null } = {}, error } = useQuery(
    QUERY_HIERARCHY_TREE,
    {
      fetchPolicy: 'cache-and-network',
      variables: { forUserId, getWatermarkCoursData: true }
    }
  );

  /* ################################################### */
  /* ############## Block de vérification ############## */
  /* ################################################### */
  // Si vous avez cette erreure, pensez à importer 'validesTypes' et à les donner en argument de rankToRemove comme ceci : rankToRemoveIfLeaf=[validesTypes.CTYPE_COURS,validesTypes.CTYPE_FOLDER]
  if (!rankToRemoveIfLeaf.every((value) => Object.values(validesTypes).includes(value))) {
    return <div>error, des éléments de rankToRemove sont pas dans validesTypes</div>;
  }

  // Si vous avez cette erreure => simplificationFeature doit être défini avec un type valide
  if (simplificationFeature && !Object.values(validesTypes).includes(simplificationFeature)) {
    return (
      <div>error : si simplification feature, alors il doit être défini avec un type valide</div>
    );
  }

  if (initAll && initialisationVariable) {
    return (
      <div>
        error : initAll et initialisationVariables sont tout les deux init. Il en faut au max qu'une
      </div>
    );
  }

  // test de si initialisationVariable est du bon format
  if (
    false &&
    initialisationVariable &&
    typeof initialisationVariable === 'object' &&
    Object.keys(initialisationVariable).every((key) => validesTypes.hasOwnProperty(key)) &&
    Array.isArray(initialisationVariable[Object.keys(initialisationVariable)[0]])
  ) {
    simplificationFeature = null;
    return (
      <div>
        initialisationVariable n'est pas du bon format. Format attendu :{' '}
        {'{validesTypes.CTYPE...: [ids] }'}
      </div>
    );
  }

  /* #################################################################### */
  /* ################# Définition des variables internes ################ */
  /* #################################################################### */

  // Initie un objet vide pour chaque type que le componant gère : {type1:[], type2:[], type3:[]}
  const blankDataStructure = Object.keys(validesTypes).reduce((acc, key) => {
    acc[validesTypes[key]] = [];
    return acc;
  }, {});

  // La structure qui récupère l'ensemble des données possible pour les types supportés
  let allIds = { ...blankDataStructure };
  const [hookAllIds, setHookAllIds] = useState(null); // Obligé de faire ça pour cope avec l'asynchronicité

  // la structure de data tree après processing
  const [dataTree, setDataTree] = useState(null); // Le tree que l'on utilisera pour le componant

  // la variable qui contient les check pour le Tree
  const [fullyCheckedIds, setFullyCheckedIds] = useState(blankDataStructure);

  // La variable qui contient les keys des éléments sélectionnés pour le TreeSelect
  const [treeSelectValue, setTreeSelectValue] = useState([]);

  /* ############################################################## */
  /* ################# Initialisation du componant ################ */
  /* ############################################################## */
  let checkpoint1 = readOnlyTree ? true : false; // check si les données ont étées loard
  let checkpoint2 = false; // Check si les données originales ont étées procéssées, et principalement, si la structure 'allIds' a finie d'être remplie
  let checkpoint3 = false;
  let checkpoint4 = false;

  useEffect(() => {
    /* UseEffect qui s'assure du bon déroulement du componant, avec différents checkpoints */

    // Checkpoint 1 : On a fini de load les données
    if (!checkpoint1 && readOnlyTree) {
      checkpoint1 = true;
    }

    // Checkpoint 2 : On a fini de processer les données (analyse + remise en forme)
    if (checkpoint1 && !checkpoint2) {
      setDataTree(refilterDataTree(cloneDeep(readOnlyTree)));
      checkpoint2 = true;
    }

    // Checkpoint 3 : On a processé les données. Maintenant on initialise 'hookAllIds' => permet d'avoir tous les éléments de chaque type dans hookAllIds
    if (checkpoint2 && !checkpoint3) {
      setHookAllIds(allIds);
      checkpoint3 = true;
    }

    // Checkpoint 4 : On initialise et le componant, et le hook externe
    if (checkpoint3 && !checkpoint4) {
      if (initAll) {
        /* Depuis allIds, on construit un objet contenant uniquement les champs sélectionnés dans initAll */
        const fiteredInitAllStructure = Object.keys(blankDataStructure).reduce(
          (acc, typeToInit) => ({
            ...acc,
            [typeToInit]: initAll.includes(typeToInit)
              ? [...allIds[typeToInit]]
              : [...blankDataStructure[typeToInit]]
          }),
          {}
        );
        externalHookSetter(fiteredInitAllStructure);
        internalHookSetter(fiteredInitAllStructure);
      }

      if (initialisationVariable) {
        /* Depuis allIds, on construit un objet contenant tous les champs, mais uniquement rempli avec les valeures des champs renseignés */
        const filteredInitVarStructure = Object.keys(blankDataStructure).reduce(
          (acc, typeToInit) => {
            const values = initialisationVariable.hasOwnProperty(typeToInit)
              ? [...initialisationVariable[typeToInit]]
              : [...blankDataStructure[typeToInit]];

            return {
              ...acc,
              [typeToInit]: values
            };
          },
          {}
        );

        externalHookSetter(filteredInitVarStructure);
        internalHookSetter(filteredInitVarStructure);
      }
      checkpoint4 = true;
    }
  }, [readOnlyTree, checkpoint1, checkpoint2, checkpoint3]);

  /* ########################################################## */
  /* ################# Fonctions de processing ################ */
  /* ########################################################## */

  function refilterDataTree(data) {
    /* Fonction récursive de processing et parsing des données. Formate le type de la node en type supporté et ressence chaque ID de chaque type dans (la constante) allIds */

    function determineAndTagNodeC_TYPE(node) {
      /* Pour Détermine le type supporté par ce componant et le return */
      let componantType = null;
      if (node.isFolder) {
        componantType = validesTypes.CTYPE_FOLDER;
      } else if (node.type === validesTypes.CTYPE_UE) {
        componantType = validesTypes.CTYPE_UE;
      } else if (node.type === validesTypes.CTYPE_CATEGORY) {
        componantType = validesTypes.CTYPE_CATEGORY;
      } else if (node.type === validesTypes.CTYPE_COURS && !node?.isStep) {
        componantType = validesTypes.CTYPE_COURS;
      } else if (node.type === validesTypes.CTYPE_COURS && node?.isStep) {
        componantType = validesTypes.CTYPE_PAGE;
      } else {
        componantType = validesTypes.CTYPE_UNKNOWN;
      }
      return componantType;
    }

    function evoluateRecursion(node) {
      /* Fonction récursive de 1) Filtration    2) Parsing des id  3) (optionnel) init des éléments à tout*/

      ///////////////////// Definition de constantes par node
      node.componantType = determineAndTagNodeC_TYPE(node); // Identification / normalisation du type de la node
      const hasChildren = !!node?.children?.length; // Check de si la node a des childrens
      const deletIfLeaf = rankToRemoveIfLeaf.includes(node.componantType); // check de si le type est à delet si leaf
      node.key = `${node.componantType}-${node.id}`; // redéfinition de la key afin de pouvoir récupérer le type depuis la key présente dans checked Keys values

      ////////////////////// Definition des conditions d'arêt

      // Détermination de si ça a des settings de watermark
      const hasWatermarkSettings =
        node?.watermarkData && Object.keys(node?.watermarkData).length > 0;

      // Si ça a pas d'enfant, et pas de watermark settings, alors on tej
      if (!hasChildren && !hasWatermarkSettings) {
        return true;
      }

      // Si ça a des enfants, alors on lance la récusion
      if (hasChildren) {
        node.children = node?.children.filter((obj) => !evoluateRecursion(obj));

        // Si aucun des enfants n'est gardé et que le parent n'a pas de watermark, alors on tej
        if (node.children.length === 0 && !hasWatermarkSettings) {
          return true;
        }
      }

      /*
      // Condition d'arêt 1: Si on a pas d'enfants + type à delet => return true
      if (!hasChildren && deletIfLeaf || (!hasChildren && !hasWatermarkSettings)) {
        return true
      }

      // Condition 2 : Filtration récursive des enfants. Si résultat vide + type à delet => return true
      if (hasChildren) {
        node.children = node?.children.filter(obj => !evoluateRecursion(obj))

        if ( (node.children.length === 0 && deletIfLeaf) || (node.children.length === 0 && !hasWatermarkSettings )) {
          return true
        }
      }

       */

      //// test TreeSelect
      node.value = node.key; // Pour le treeSelect

      //// Disabled des items si multiple === false
      if (simplificationFeature && !multiple && node.componantType !== simplificationFeature) {
        node.disableCheckbox = true;
        node.disabled = true;
      }

      //// Icon rendering, je le met ici pour que ce soit compatible pour TreeSelect et Tree
      node.icon = renderTreeNodeIcon(node);

      ////////////////////// Log de tous les ID restants dans la structure de monitoring
      allIds[node.componantType] = [...allIds[node.componantType], node.id];

      ////////////////////// Init de tous les éléments de initAll  // PAS OPTI // Le sortir et exécuter lorsque l'on a fini de load
      if (initAll && initAll.includes(node.componantType)) {
        setFullyCheckedIds((prevState) => ({
          ...prevState,
          [node.componantType]: [...prevState[node.componantType], node.id]
        }));
      }
      return false;
    }

    // Création d'un placeholder/copie, qui contiendra les données originales que l'on modifiera
    let placeholderData = [];

    // Init de la récursion
    for (const origin of data) {
      if (!evoluateRecursion(origin)) {
        placeholderData.push(origin);
      }
    }

    // return des données modifiées
    return placeholderData;
  }

  /* ########################################################## */
  /* ################# Fonctions d'intéraction ################ */
  /* ########################################################## */
  useEffect(() => {
    /* Fonction qui 'lis' la variable externalAction et modifie le componant en fonction
     * Pour que le componant soit modifié, la externalAction doit prendre une valeur tel que définie dans la structure hierarchyComponantActions de ce componant
     */

    if (dataTree && externalAction) {
      // Permet de check tous les ids. Soit pour la sélection, soit total
      if (externalAction === hierarchyComponantActions.CHECK_ALL) {
        externalHookSetter(hookAllIds);
        internalHookSetter(hookAllIds);
      } else if (externalAction === hierarchyComponantActions.UNCHECK_ALL) {
        externalHookSetter(blankDataStructure);
        internalHookSetter(blankDataStructure);
      } else {
        console.error('action non reconue');
      }

      resetExternalAction();
    }
  }, [externalAction]);

  /* ###################################################################### */
  /* ################# Fonctions de Général d'aide au tree ################ */
  /* ###################################################################### */

  const getComponantTypeAndIdFromKey = (k) => {
    // Fonction helper pour splice et récupérer le type / id

    // pas bien, récupérer le componant type
    const splitted = k?.split('-');
    return { id: splitted?.[1], componantType: splitted?.[0] };
  };

  function externalHookSetter(structure) {
    /* Fonction qui set le hook externe en fonction des paramètres du componant. Prend une structure {C_TYPE:[]} */

    // Si simplification feature, alors on set uniquement le type en question
    if (simplificationFeature) {
      setterHookSelection(structure[simplificationFeature]);
    } else {
      for (const temp of Object.keys(structure)) {
        setterHookSelection((prevState) => ({ ...prevState, [temp]: structure[temp] }));
      }
    }
    // TODO : Si on est en multiple === false => Alors, si on reclic, alors il faut retirer la sélection
  }

  function internalHookSetter(structure) {
    /* Fonction qui set le hook interne en fonction des paramètres du componant. Prend une structure {C_TYPE:[]}. */

    /* Si coché, alors coche tous les enfants */
    if (!useTreeSelect) {
      for (const temp of Object.keys(structure)) {
        setFullyCheckedIds((prevState) => ({ ...prevState, [temp]: structure[temp] }));
      }
    } else {
      setTreeSelectValue(structureToTreeSelectCompatibleArray(structure));
    }
  }

  /* #################################################################### */
  /* ################# Fonctions de Check/Uncheck du Tree ############### */
  /* #################################################################### */

  /* Fonction on check qui set les données interne et appelle le setter externe */
  const onCheck = (checkedKeysValue, info) => {
    /* Fonction qui réalise toute les transaction quand on check pour le Tree*/

    ////// Block qui permet de faire la logique d'une sélection en cascade simple
    const checkedNodes = info?.checkedNodes; // Les nodes checkées

    // Block qui récupère les FULLY-CHECKED NODES . Si une node est half-checked elle sera pas là
    const fullNodes = Object.keys(blankDataStructure).reduce((acc, componantType) => {
      acc[componantType] = [
        ...(checkedNodes
          ?.filter((node) => node?.componantType === componantType)
          .map((c) => String(c?.id)) || [])
      ];
      return acc;
    }, {});

    /* Block qui récupère */
    if (multiple) {
      // Si multiple, alors on fais pas de post-traitement, on set tel quel
      internalHookSetter(fullNodes);
      externalHookSetter(fullNodes);
    } else {
      // Si pas multiple, alors on récupère la node cliquée, et on soit supprime de la sélection, soit on met qu'elle
      const singleNode = info.node;
      const singleStructure = cloneDeep(blankDataStructure);
      const { id: singleId, componantType: singleComponantType } = getComponantTypeAndIdFromKey(
        singleNode.key
      );
      singleStructure[singleComponantType].push(String(singleId));
      internalHookSetter(singleStructure);
      externalHookSetter(singleStructure);
    }
  };

  // TODO : Passer la variable interne du TreeSelect en structure et transformer la structure en array au niveau du componant => permet d'uniformiser input entre Tree et TreeSelect
  /* ############################################################################## */
  /* ################# Fonctions de Check/Uncheck du TreeSelect ################### */
  /* ############################################################################## */

  function structureToTreeSelectCompatibleArray(structure) {
    /* fonction qui converti une structure (blank) en array compatible avec le TreeSelect*/
    let array = [];

    // itération sur la blankStructure
    for (const key in blankDataStructure) {
      structure[key].map((value) => {
        const testvariable = `${key}-${value}`;
        array?.push(testvariable);
      });
    }
    return array;
  }

  function treeSelectOnChange(value, label, extra) {
    /* Fonction qui s'occupe des transaction du treeSelect */

    // Récupération de la valeur de click
    const selection = extra?.triggerValue;
    let valueSnapshot = treeSelectValue;

    //// update du snapshot ( => représentation interne du componant
    // Si le snapshot a déjà la valeur
    if (valueSnapshot.includes(selection)) {
      valueSnapshot.splice(valueSnapshot.indexOf(selection), 1); // Alors on la retire
    } else {
      // Sinon, on l'ajoute, en fonction de si (multiple) ou pas
      if (multiple) {
        valueSnapshot.push(selection);
      } // ajoute
      else {
        valueSnapshot = [selection];
      } // Remplace
    }

    // Update du hook interne
    setTreeSelectValue([...valueSnapshot]);

    // Update du hook externe
    const returnStructure = cloneDeep(blankDataStructure);
    valueSnapshot.map((value) => {
      const { id, componantType } = getComponantTypeAndIdFromKey(value);
      returnStructure[componantType].push(id);
    });

    externalHookSetter(returnStructure);
  }

  /* ######################################################### */
  /* ################# Fonctions de Render ################### */
  /* ######################################################### */
  const renderTreeNodeIcon = (node) => {
    /* permet d'afficher une icone pour les CTYPES_ choisis. sinon l'image*/
    if (node?.image) {
      return renderIcon(node.image);
    } else if (node?.componantType === validesTypes.CTYPE_COURS) {
      return <BookTwoTone />;
    } else if (node?.componantType === validesTypes.CTYPE_PAGE) {
      return <FileTwoTone />;
    } else if (node?.componantType === validesTypes.CTYPE_FOLDER) {
      return <FolderTwoTone />;
    } else if (node?.componantType === validesTypes.CTYPE_CATEGORY) {
      return <ContainerTwoTone />;
    } else if (node?.componantType === validesTypes.CTYPE_UE) {
      return <BankTwoTone />;
    } else if (node?.componantType === validesTypes.CTYPE_UNKNOWN) {
      return <QuestionCircleTwoTone />;
    } else {
      return <WarningTwoTone />;
    }
  };

  const findNodeByValue = (nodes, value) => {
    /* fonction de search récursive dans l'arbre */
    let result = null;

    if (!Array.isArray(nodes)) {
      return result;
    }
    for (const node of nodes) {
      if (node.value === value) {
        return node;
      }
      if (node.children) {
        result = findNodeByValue(node.children, value);
        if (result) {
          return result;
        }
      }
    }
    return result;
  };

  const customTagRender = ({ label, value, closable, onClose }) => {
    /* permet de customiser l'affichage des tags pour le TreeSelect */
    const customColorMap = {
      [validesTypes.CTYPE_UE]: 'purple',
      [validesTypes.CTYPE_COURS]: 'cyan',
      [validesTypes.CTYPE_CATEGORY]: 'blue',
      [validesTypes.CTYPE_FOLDER]: 'geekblue',
      [validesTypes.CTYPE_UNKNOWN]: 'grey'
    };

    const node = findNodeByValue(dataTree, value);

    const { id: singleId, componantType: singleComponantType } =
      getComponantTypeAndIdFromKey(value);

    return (
      <Tag
        color={customColorMap[singleComponantType]}
        style={{
          paddingLeft: '3px',
          padding: '1px',
          borderRadius: '5px',
          margin: '1px',
          display: 'flex'
        }}
      >
        {renderTreeNodeIcon(node)}
        {label}
        {closable && (
          <span
            style={{
              marginLeft: '3px',
              marginRight: '3px',
              cursor: 'pointer',
              fontWeight: 'bold',
              color: 'white'
            }}
            onClick={onClose}
          >
            x
          </span>
        )}
      </Tag>
    );
  };

  return (
    <>
      {checkpoint4 ? (
        <div style={{ height: '200px' }}>
          <SpinnerCentered />
        </div>
      ) : (
        <>
          {useTreeSelect ? (
            <TreeSelect
              treeData={dataTree}
              tagRender={customTagRender}
              //icon={renderTreeNodeIcon}
              value={treeSelectValue}
              //switchterIcon={renderTreeNodeIcon}
              treeIcon={true}
              onChange={treeSelectOnChange}
              showSearch
              multiple
              filterTreeNode={(input, node) =>
                node.title.toLowerCase().includes(input.toLowerCase())
              }
              {...additionalTreeProps}
            />
          ) : (
            <Tree
              treeData={dataTree}
              checkedKeys={Object.values(validesTypes).flatMap((type) => {
                return fullyCheckedIds[type].map((c) => `${type}-${c}`);
              })}
              onCheck={onCheck}
              checkable
              showIcon
              {...additionalTreeProps}
              //icon={renderTreeNodeIcon}

              multiple
            />
          )}
        </>
      )}
    </>
  );
};
