import {gql, useQuery} from "@apollo/client";
import { Select,Button} from "antd";
import React, {useEffect, useState} from "react";
import {QUERY_GET_ALL_WATERMARKS} from "@/shared/services/watermark";
import {useTranslation} from "react-i18next";


export const SelecterWatermarkTemplate=({setSettings})=> {
  /* Petit componant qui permet de choisir et d'importer une configuration de watermark depuis la database Template */

  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_GET_ALL_WATERMARKS, {fetchPolicy: 'no-cache'});

  const watermarkList=data?.getAllWatermarks
  const [locallySelectedWatermark, setLocallySelectedWatermark] = useState(null);

  const handleClick = () => {
    const selectedElement = watermarkList.find(item => item.id === locallySelectedWatermark);
    setSettings({...selectedElement.settings});
  };

  return(<>
  {watermarkList &&
    <>
      <Select
        style={{ width: 300 }}
        placeholder={t('TemplateModule.ImportWatermarkTemplatePlaceholder')}
        onChange={setLocallySelectedWatermark}
        value={locallySelectedWatermark}
      >
        {watermarkList?.map(item => (
          <Option key={item.id} value={item.id}>{item.name}</Option>
        ))}
      </Select>
      <Button
        onClick={handleClick}
        disabled={!locallySelectedWatermark}
        style={{ marginLeft: 8 }}
        type={"primary"}
      >
        {t('TemplateModule.ImportWatermarkTemplate')}
      </Button>
    </>
  }</>)
}
