import {
  Checkbox,
  Form,
  InputNumber,
  Radio,
  Select,
  Space,
  Row,
  Alert,
  Collapse,
  notification
} from 'antd';
import { AgnosticWatermarkInputText } from '@/shared/components/WatermarkComponants/AgnosticWatermarkInputText';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import { HighlightOutlined } from '@ant-design/icons';
import {
  MUTATION_CREATE_OR_DELETE_WATERMARK_PICTURE,
  MUTATION_CREATE_OR_DELETE_WATERMARK_PICTURE_FILE,
  MUTATION_CREATE_OR_DELETE_WATERMARK_PICTURE_TEMPLATE,
  supportedWatermarkPdfTypes,
  uniquesWatermarkFieldStructure as U
} from '@/shared/services/watermark.js';
import pdfWatermarkPicture from '@/shared/assets/pdfWatermarkPicture.png';
import { QUERY_GET_PDF_STATS_COURS, QUERY_GET_PDF_STATS_FILE } from '@/shared/graphql/file';
import {
  validesTypes,
  HierarchySelecterWatermarkCustom
} from '@/shared/components/WatermarkComponants/HierarchySelecterWatermarkCustom';
import { gql } from '@apollo/client';
import { SelecterWatermarkTemplate } from '@/shared/components/WatermarkComponants/SelecterWatermarkTemplate';

export const AgnosticWatermarkModalModule = ({
  agnosticIdArray,
  fileString,
  form,
  settings,
  type
}) => {
  /* Componant qui permet de gérer les données de watermark à un haut niveau, et permet notament l'importation de settings
   *
   *
   * /!\ Doit être dans un componant <Form/> de antDesign
   * */

  const { t } = useTranslation();

  const [selectedCoursIdForSettingImport, setSelectedCoursIdForSettingImport] = useState(null);
  const [dynamicSettings, setDynamicSettings] = useState(settings);
  const [key, setKey] = useState(0);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false); // Contrôle l'état du menu déroulant

  const { data: { cour = null } = {}, error: courError } = useQuery(
    gql`
      query Cour($courId: ID!) {
        cour(id: $courId) {
          settings
        }
      }
    `,
    {
      variables: { courId: selectedCoursIdForSettingImport },
      fetchPolicy: 'no-cache',
      skip: selectedCoursIdForSettingImport === null,
      onCompleted: (data) => {
        const settings = data?.cour?.settings;
        setDynamicSettings(settings);
        setKey(key + 1);
      }
    }
  );

  const triggerNotifSelecter = () => {
    notification.success({
      message: t('TemplateModule.WatermarkTemplateImportNotif'),
      placement: 'topRight',
      key: 'databaseSuccesImportNotifSelecter'
    });
  };

  const triggerNotifTemplate = () => {
    notification.success({
      message: t('TemplateModule.WatermarkTemplateImportNotif'),
      placement: 'topRight',
      key: 'databaseSuccesImportNotifTemplate'
    });
  };

  const forwardComponantHierarchySelecter = (
    <HierarchySelecterWatermarkCustom
      useTreeSelect
      multiple={false}
      additionalTreeProps={{
        placement: 'topLeft',
        listHeight: 500,
        style: { width: '300px', marginRight: '5px' },
        popupMatchSelectWidth: false,
        treeLine: true,
        placeholder: t('watermark.ImportFromCourse')
      }}
      initialisationVariable={
        selectedCoursIdForSettingImport === null
          ? selectedCoursIdForSettingImport
          : { [validesTypes.CTYPE_COURS]: [selectedCoursIdForSettingImport] }
      }
      simplificationFeature={validesTypes.CTYPE_COURS}
      setterHookSelection={(value) => {
        if (value && value.length === 0) {
          setSelectedCoursIdForSettingImport(null);
          setIsPopoverOpen(true);
        } else if (value && value.length === 1) {
          setSelectedCoursIdForSettingImport(value[0]);
          setIsPopoverOpen(true);
          triggerNotifSelecter();
        } else {
          console.error('error hierarchy watermark, value : ', value, '  mise à null');
          setSelectedCoursIdForSettingImport(null);
        }
      }}
    />
  );

  const forwardComponantTemplateSelecter = (
    <SelecterWatermarkTemplate
      setSettings={(value) => {
        setIsPopoverOpen(true);
        setDynamicSettings({ ...value });
        setKey(key + 1);
        setSelectedCoursIdForSettingImport(null);
        triggerNotifTemplate();
      }}
      key={1}
    />
  );

  return (
    <>
      {dynamicSettings && (
        <SubComponantAgnosticWatermark
          agnosticIdArray={agnosticIdArray}
          fileString={fileString}
          form={form}
          settings={dynamicSettings}
          type={type}
          key={key} // Attention: `key` ne devrait pas être utilisé ici si ce composant n'est pas dans une liste
          forwardComponantHierarchySelecter={forwardComponantHierarchySelecter}
          isPopoverOpen={isPopoverOpen}
          setIsPopoverOpen={setIsPopoverOpen}
          forwardComponantTemplateSelecter={forwardComponantTemplateSelecter}
        />
      )}
    </>
  );
};

const SubComponantAgnosticWatermark = ({
  agnosticIdArray,
  fileString,
  form,
  settings,
  type,
  forwardComponantHierarchySelecter,
  isPopoverOpen,
  setIsPopoverOpen,
  forwardComponantTemplateSelecter
}) => {
  /*
   * Le module prend un form, et permet de sélectionner des options de watermark à appliquer à des PDF
   *
   * La particularité de ce module est qu'il est agnostic, c'est à dire qu'il prend uniquement un ou des id ainsi qu'un type.
   * Le module permet d'appliquer les options de watermark à tous les ID fournis, cela est possible en désignant des fonctions dialoguant avec le back pour chaque type.
   *
   * Le module prend :
   *   -> Le type : string => quel type de donnée nous alons traiter.
   *   -> agnosticIdArray : [id] ou [id,id,id] => La ou les ids id correspondant au type de  données à traiter
   *   -> filestring : (optionel) string : Une string pointant vers un pdf du back. Permet de customiser le nombre de page, etc.
   *   -> form : AntDesign Form =>  le form antDesign dans le quel sera ajouté les champs gérés par ce module
   *   -> Settings : {}  => Object soit vide (initialisation par default) soit déjà initialisé par ce module. Contient les options de watermark
   *  */
  // Vérifie que le type est supporté
  if (!Object.values(supportedWatermarkPdfTypes).includes(type)) {
    return <div>type ({type}) non supporté.</div>;
  }

  const { t } = useTranslation();

  ////////////////  Hardcode de différents éléments conditionnelement au type :
  //    -> La query safe pour se renseigner sur un pdf en fonction de son système de fichier : Si il est dans le système cours, ou file.
  //    -> La query qui gère l'ajout / suppression d'une image dans le back.

  // placeolders
  let queryTypeDependant;
  let createOrDeleteWatermarkPicture;

  // Definition des queries conditionnellement
  if (type === supportedWatermarkPdfTypes.WATERMARK_COURS) {
    queryTypeDependant = QUERY_GET_PDF_STATS_COURS;
    createOrDeleteWatermarkPicture = MUTATION_CREATE_OR_DELETE_WATERMARK_PICTURE;
  } else if (type === supportedWatermarkPdfTypes.WATERMARK_FILE) {
    queryTypeDependant = QUERY_GET_PDF_STATS_FILE;
    createOrDeleteWatermarkPicture = MUTATION_CREATE_OR_DELETE_WATERMARK_PICTURE_FILE;
  } else if (type === supportedWatermarkPdfTypes.WATERMARK_TEMPLATE) {
    queryTypeDependant = QUERY_GET_PDF_STATS_COURS;
    createOrDeleteWatermarkPicture = MUTATION_CREATE_OR_DELETE_WATERMARK_PICTURE_TEMPLATE;
  } else {
    return <div>Type pas encore mappé</div>;
  }

  // Récupération des données conditionnellement
  const {
    data: {
      isPdfAndStatsInCoursSystemFolder: statDataCours = null,
      isPdfAndStatsInFileSystemFolder: statDataFile = null
    } = {},
    loading,
    error
  } = useQuery(queryTypeDependant, {
    variables: { fileName: fileString },
    fetchPolicy: 'no-cache',
    notifyOnNetworkStatusChange: true
  });

  // Récupération et uniformisation des données
  const resultStructure = (!loading && statDataCours) || statDataFile || null;
  const isPdf = resultStructure?.isFileAndPdf;
  const pdfNumberOfPages = resultStructure?.numberOfPages || 500;
  const isEditable = !resultStructure?.isPdfAndCrypted || false;

  //////////////////// Init et manipulation du form
  // Création / Rechargement des variables de form
  const [applyWatermark, setApplyWatermark] = useState(
    settings?.applyWatermark !== undefined ? settings.applyWatermark : false
  );
  const [radioValue, setRadioValue] = useState(
    settings?.radioChoice !== undefined ? settings.radioChoice : 'radioChoiceValueAll'
  );

  // fonction de convert active userKey
  const traductFctCollapsePanel = (booleanInput) => {
    if (booleanInput) {
      return '1';
    } else if (booleanInput === false) {
      return '0';
    }
  };

  /* Note */
  /* Il y a une sorte de comportement chelou avec le form et les 'radioChoiceValue'. Pour les champs radioChoiceValueChooseSelected et radioChoiceValueOneEachSelected
   * Si on initialise le Form.initialData={data} ça marche, mais sans ceci, il faut définir au montage du componant quelles valeurs leurs donner
   * Ce que je fais ci dessous
   * */

  // permet de set les variables par default de ce componant lorsqu'elles ne sont pas déjà initialisées
  useEffect(() => {
    form.setFieldsValue({ [U.RADIO_CHOICE]: radioValue });
    form.setFieldsValue({ [U.APPLY_WATERMARK]: applyWatermark });
    if (settings?.[U.RADIO_OPTION_SELECTED]) {
      form.setFieldsValue({ [U.RADIO_OPTION_SELECTED]: settings?.[U.RADIO_OPTION_SELECTED] });
    }
    if (settings?.[U.RADIO_OPTION_ONE_EACH]) {
      form.setFieldsValue({ [U.RADIO_OPTION_ONE_EACH]: settings?.[U.RADIO_OPTION_ONE_EACH] });
    }
  }, []);

  const [selectValues, setSelectValues] = useState([]);

  // Rules pour le radio MultiSelect
  function validateMultiSelectInput(maxValue) {
    return function (_, value) {
      if (radioValue === 'radioChoiceValueChoose') {
        if (!Array.isArray(value) || value.length === 0) {
          return Promise.reject(t('watermark.NeedPage'));
        } // Besoin de renseigner au moins une page
        if (Math.max(...value) > maxValue) {
          return Promise.reject(t('watermark.InputPageTooBig', { maxValue: maxValue }));
        } // Il faut que si il y a des éléments dans la liste (d'un précédent pdf) la valeur max < nombre max de pages
      }
      return Promise.resolve();
    };
  }

  // Rules pour le radio InputNumber
  function validateInputNumber(minValue, maxValue) {
    return function (_, value) {
      if (radioValue !== 'radioChoiceValueOneEach') {
        return Promise.resolve();
      } // Si on a pas sélectionné ce radio
      if (value === null || value === undefined || !Number.isInteger(value)) {
        return Promise.reject(t('watermark.NeedInt'));
      }
      if (value < 1 || value > maxValue) {
        return Promise.reject(t('watermark.InputPageTooBig', { maxValue: maxValue }));
      }
      return Promise.resolve();
    };
  }

  return (
    <>
      <div style={{ width: '100%', marginBottom: '20px' }}>
        <div style={{ display: 'block', alignItems: 'center' }}>
          <Form.Item name={U.APPLY_WATERMARK} valuePropName="checked" style={{ display: 'flex' }}>
            <div style={{ alignItems: 'center', display: 'flex' }}>
              <Checkbox
                onChange={(e) => {
                  setApplyWatermark(e.target.checked);
                }}
                checked={applyWatermark}
              >
                <HighlightOutlined /> &nbsp; {t('watermark.WatermarkModule')}
              </Checkbox>
              {forwardComponantHierarchySelecter}
              {forwardComponantTemplateSelecter}
            </div>
          </Form.Item>
          {applyWatermark && fileString === null && (
            <Alert message={t('watermark.WatermarkMassOperationWarning')} type="warning" />
          )}
          {applyWatermark && !loading && fileString !== null && !isPdf && (
            <Alert message={t('watermark.IsNotPdf')} type="warning" />
          )}
          {applyWatermark && !loading && fileString !== null && isPdf && !isEditable && (
            <Alert message={t('watermark.IsNotEditable')} type="warning" />
          )}
          {applyWatermark && !loading && fileString !== null && (!isPdf || !isEditable) && (
            <Alert message={t('watermark.WatermarkDoesNotApply')} type="error" />
          )}
        </div>
        <Collapse
          activeKey={traductFctCollapsePanel(isPopoverOpen)}
          onChange={(value) => {
            if (value.length > 0) {
              setIsPopoverOpen(true);
            } else {
              setIsPopoverOpen(false);
            }
          }}
        >
          <Collapse.Panel
            key="1"
            header={<div style={{ display: 'flex' }}>{t('watermark.WatermarkParameters')}</div>}
            forceRender
          >
            <div style={{ display: 'flex' }}>
              <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                <AgnosticWatermarkInputText
                  watermarkType="username"
                  settings={settings}
                  agnosticIdArray={agnosticIdArray}
                  disabled={applyWatermark !== true}
                  form={form}
                  uploadPictureMutation={createOrDeleteWatermarkPicture}
                />
                <AgnosticWatermarkInputText
                  watermarkType="firstName"
                  settings={settings}
                  agnosticIdArray={agnosticIdArray}
                  disabled={applyWatermark !== true}
                  form={form}
                  uploadPictureMutation={createOrDeleteWatermarkPicture}
                />
                <AgnosticWatermarkInputText
                  watermarkType="lastname"
                  settings={settings}
                  agnosticIdArray={agnosticIdArray}
                  disabled={applyWatermark !== true}
                  form={form}
                  uploadPictureMutation={createOrDeleteWatermarkPicture}
                />
                <AgnosticWatermarkInputText
                  watermarkType="email"
                  settings={settings}
                  agnosticIdArray={agnosticIdArray}
                  disabled={applyWatermark !== true}
                  form={form}
                  uploadPictureMutation={createOrDeleteWatermarkPicture}
                />
                <AgnosticWatermarkInputText
                  watermarkType="phone"
                  settings={settings}
                  agnosticIdArray={agnosticIdArray}
                  disabled={applyWatermark !== true}
                  form={form}
                  uploadPictureMutation={createOrDeleteWatermarkPicture}
                />
                <AgnosticWatermarkInputText
                  watermarkType="custom"
                  settings={settings}
                  agnosticIdArray={agnosticIdArray}
                  disabled={applyWatermark !== true}
                  form={form}
                  uploadPictureMutation={createOrDeleteWatermarkPicture}
                />
                <AgnosticWatermarkInputText
                  watermarkType="picture"
                  settings={settings}
                  agnosticIdArray={agnosticIdArray}
                  disabled={applyWatermark !== true}
                  form={form}
                  uploadPictureMutation={createOrDeleteWatermarkPicture}
                />
              </div>
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <img src={pdfWatermarkPicture} style={{ width: '100%' }} alt="" />
              </div>
            </div>
            <h3>{t('watermark.radioChoice')}</h3>
            <Form.Item name={U.RADIO_CHOICE}>
              <Radio.Group onChange={(e) => setRadioValue(e.target.value)} value={radioValue}>
                <Space direction="vertical">
                  <Row gutter={8}>
                    <div>
                      <Radio value="radioChoiceValueAll" disabled={!applyWatermark}>
                        {t('watermark.radioChoiceValueAll')}
                      </Radio>
                    </div>
                  </Row>
                  <Row gutter={8}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Radio value="radioChoiceValueChoose" disabled={!applyWatermark}>
                        {t('watermark.radioChoiceValueChoose')}
                      </Radio>
                      <Form.Item
                        name={U.RADIO_OPTION_SELECTED}
                        style={{ margin: '0', padding: '0' }}
                        rules={[{ validator: validateMultiSelectInput(pdfNumberOfPages) }]}
                      >
                        <Select
                          mode="multiple"
                          onChange={(value) => setSelectValues(value)}
                          value={selectValues}
                          disabled={radioValue !== 'radioChoiceValueChoose'}
                          style={{ width: '300px' }}
                        >
                          {[...Array(pdfNumberOfPages).keys()].map((i) => (
                            <Option key={i + 1} value={i + 1}>
                              {' '}
                              {i + 1}{' '}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </div>
                  </Row>
                  <Row gutter={8}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Radio value="radioChoiceValueOneEach" disabled={!applyWatermark}>
                        {t('watermark.radioChoiceValueOneEach')}
                      </Radio>
                      {
                        <Form.Item
                          name={U.RADIO_OPTION_ONE_EACH}
                          style={{ margin: '0', padding: '0' }}
                          rules={[{ validator: validateInputNumber(1, pdfNumberOfPages) }]}
                        >
                          <InputNumber disabled={radioValue !== 'radioChoiceValueOneEach'} />
                        </Form.Item>
                      }
                    </div>
                  </Row>
                </Space>
              </Radio.Group>
            </Form.Item>
          </Collapse.Panel>
        </Collapse>
      </div>
    </>
  );
};
