import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { getParticlesParamsToShow } from '@/shared/services/config.js';
import Particles from 'react-particles';
import React, { useCallback, useContext } from 'react';
import { loadFull } from 'tsparticles';

export const MediParticlesBreadcrumb = ({ height = 100 }) => {
  const { appearance } = useContext(GlobalContext);
  const params = getParticlesParamsToShow(appearance, 'bannerAnimation');

  const particlesInit = useCallback(async engine => {
    await loadFull(engine);
  }, []);

  if (params === null) {
    return null;
  }

  return (
    <Particles
      init={particlesInit}
      height={height}
      style={{
        height
      }}
      options={{
        ...params,
        fullScreen: {
          enable: false
        }
      }}
    />
  );
};
