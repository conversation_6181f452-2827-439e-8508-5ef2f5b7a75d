import { Button, Input } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MessageCirclePlus, Search } from 'lucide-react';

export function SearchTopic({
  setCreateTopic,
  setTopicTitle,
  topicTitle,
  showAskQuestionButton = true
}) {
  const { t } = useTranslation();
  return (
    <div style={{ width: '100%' }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ fontSize: 30, lineHeight: 1 }}>
          <Search />
        </span>
        <Input
          placeholder={t('SearchOrAskQuestion')}
          style={{ border: 'none', boxShadow: 'none', fontSize: 24, fontWeight: 700 }}
          size="large"
          onChange={(e) => setTopicTitle(e.target.value)}
          value={topicTitle}
        />
        {showAskQuestionButton && (
          <Button type="primary" onClick={() => setCreateTopic(true)} style={{ flexShrink: 0 }}>
            <MessageCirclePlus style={{ marginRight: 8 }} />
            {t('AskQuestion')}
          </Button>
        )}
      </div>
    </div>
  );
}
