import { FileImage } from '@/shared/components/FileImage.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { SEARCH_POST_TYPE } from '@/shared/graphql/posts.js';
import { CommentairesType } from '@/shared/services/commentaires.js';
import { useQuery } from '@apollo/client';
import { Button, Space } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

/*
type: CommentaireType
* */
export const ChoosePostType = ({ onSelectPostType, type }) => {
  const { t } = useTranslation();
  const { data, loading } = useQuery(SEARCH_POST_TYPE, {
    fetchPolicy: 'cache-and-network'
  });
  const typeToSearchFor = type === CommentairesType.QUESTION_ANSWER ? CommentairesType.QCM : type;
  const postTypes = data?.postTypes?.filter((p) => p.type === typeToSearchFor);
  return (
    <div style={{ textAlign: 'center' }}>
      <p>{t('YouThinkThat')}</p>
      {loading && <SpinnerCentered />}
      <Space direction="vertical" size="large">
        {postTypes?.map((postType) => (
          <>
            <Button onClick={() => onSelectPostType(postType)} size="large" block type="primary">
              <FileImage image={postType.image} style={{ maxHeight: '24px', height: 'auto' }} />
              &nbsp;{postType.name}
            </Button>
          </>
        ))}
      </Space>
    </div>
  );
};
