import { ChoosePostType } from '@/shared/components/Commentaires/ChoosePostType.jsx';
import { CommentList } from '@/shared/components/Commentaires/CommentList.jsx';
import CreatePost from '@/components/Commentaires/CreatePost.jsx';
import PostTypeTag from '@/shared/components/Commentaires/PostTypeTag.jsx';
import NotificationForm from '@/shared/components/Notification/NotificationForm';
import { NOTIFY_POST_ADMIN } from '@/shared/graphql/notifications';
import { GENERATE_AI_ANSWER_FOR_POST, UPDATE_POST } from '@/shared/graphql/posts.js';
import {
  COMMENT_FILTER,
  CommentairesType,
  getCommentaireTypeFromObject,
  getCurrentScholarYear,
  getYearsFromData,
  getYearsQueryFromCommentaireType
} from '@/shared/services/commentaires.js';
import { formatAnnee } from '@/shared/services/qcm.js';
import { getUserInfo, isAdmin, isTuteur } from '@/shared/utils/authority.js';
import {
  CheckCircleTwoTone,
  EyeOutlined,
  NotificationOutlined,
  RobotOutlined
} from '@ant-design/icons';
import {
  Space,
  Avatar,
  Card,
  Divider,
  Empty,
  List,
  Skeleton,
  Select,
  Button,
  Modal,
  message,
  Popover
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import { ErrorResult } from '@/shared/components/ErrorResult';
import { CommentRenderer } from '@/shared/components/Commentaires/CommentRenderer';
import {
  getDataObjectFromType,
  getQueryFromCommentaireType,
  getQueryVariablesFromCommentaireType
} from '@/shared/services/commentaires';
import {
  isMobile,
  scrollToRef,
  showGqlErrorsInMessagePopupFromException,
  toInt
} from '@/shared/utils/utils';
import MessageOutlined from '@ant-design/icons/lib/icons/MessageOutlined';
import router from 'umi/router.js';
import { useTranslation } from 'react-i18next';
import { LimitationQuestionLayer } from '@/shared/components/LimitationQuestionLayer';

const DEFAULT_SORTING = COMMENT_FILTER.MOST_RECENT;
const Commentaires = forwardRef(
  (
    {
      type,
      id, // typeId
      isDetailPage = false,
      postId = null,
      isForum = false,
      onGetSubject = null,
      onGetNumberOfDiscussions = null,
      showYearSelection = true,
      showAllYears = true,
      showCreatePostButton = true,
      openInNewTab = false,
      openInSamePage = false,
      onOpenInSamePage,
      showAskQuestionButton = true,
      children
    },
    ref
  ) => {
    const { t } = useTranslation();

    const getDefaultFilter = () => {
      if (isDetailPage) {
        return {};
      }
      if (showAllYears) {
        return { sort: DEFAULT_SORTING };
      }
      return { year: getCurrentScholarYear(), sort: DEFAULT_SORTING };
    };

    const [filter, setFilter] = useState(getDefaultFilter());

    const { loading, error, data, refetch } = useQuery(getQueryFromCommentaireType(type), {
      variables: getQueryVariablesFromCommentaireType(type, id, filter),
      fetchPolicy: 'no-cache',
      skip: !id
    });
    const dataObject = (data && !error && getDataObjectFromType(type, data)) || [];

    /* ----------- Special merge ----------- */
    const { data: dataQcmToMerge, error: errorQcmToMerge } = useQuery(
      getQueryFromCommentaireType(CommentairesType.QCM),
      {
        variables: getQueryVariablesFromCommentaireType(CommentairesType.QCM, id, filter),
        fetchPolicy: 'no-cache',
        skip: type !== CommentairesType.QUESTION_ANSWERS_IN_QCM
      }
    );
    const dataQcmObject =
      (dataQcmToMerge &&
        !errorQcmToMerge &&
        getDataObjectFromType(CommentairesType.QCM, dataQcmToMerge)) ||
      [];
    /* -----------  /Special merge ----------- */

    const [updatePostMutation, updateResult] = useMutation(UPDATE_POST);
    const [generateAiAnswer, { loading: loadingAiAnswer }] = useMutation(
      GENERATE_AI_ANSWER_FOR_POST
    );
    useImperativeHandle(ref, () => ({
      async refetchCommentaires() {
        return await refetch();
      }
    }));

    const handleClickRegenerateAIPost = async () => {
      try {
        await generateAiAnswer({
          variables: { postId }
        });
        await refetch();
      } catch (e) {
        console.error(e);
      }
    };

    const messagesEnd = useRef(null);

    const renderLoading = () => (
      <>
        <Skeleton loading active avatar>
          <List.Item.Meta avatar={<Avatar />} title="" description=" " />
        </Skeleton>
      </>
    );

    const autoNestPosts = (items, itemId = null) =>
      items
        .filter((item) => item.parentId === itemId)
        .map((item) => ({ ...item, typeId: id, refetch, children: autoNestPosts(items, item.id) }));

    const getMainPost = (items) => items && items.find((item) => item.id === postId);
    const [editAnnonceVisible, setEditAnnonceVisible] = useState(false);
    const [selectedPostType, setSelectedPostType] = useState(null);
    const [currentStep, setCurrentStep] = useState(0); // La step 0 est la vérifi de post Rule. La rediction conditionnelle se fait à la validation

    const isCommentListLoadedAndNotEmpty =
      (dataObject && dataObject.length > 0) || (dataQcmObject && dataQcmObject.length > 0);

    const mainPost = getMainPost(dataObject);

    const comments =
      isCommentListLoadedAndNotEmpty && autoNestPosts([...dataObject, ...dataQcmObject], postId);

    useEffect(() => {
      if (dataObject && onGetSubject && isCommentListLoadedAndNotEmpty) {
        onGetSubject(mainPost);
      }
      if (dataObject && onGetNumberOfDiscussions && isCommentListLoadedAndNotEmpty) {
        onGetNumberOfDiscussions(comments?.length || 0);
      }
    }, [dataObject]);

    const CancelComment = () => {
      setEditAnnonceVisible(false);
    };
    const showModalComment = () => {
      setEditAnnonceVisible(true);
    };
    const onSelectPostType = (postType) => {
      setSelectedPostType(postType);
      setCurrentStep(2);
    };

    const onLimiteValidation = () => {
      // Fonction appellée lorsque l'user a le droit de créer un nouveau post
      type === CommentairesType.FORUM ? setCurrentStep(2) : setCurrentStep(1);
    };

    const renderModalCreation = () => (
      <Modal
        footer={null}
        onCancel={CancelComment}
        open={editAnnonceVisible}
        closable
        confirmLoading={false}
        title={t('AskNewQuestion')}
        style={{
          marginBottom: 10,
          marginLeft: 'auto',
          marginRight: 'auto'
        }}
      >
        {currentStep === 0 && <LimitationQuestionLayer onLimiteValidation={onLimiteValidation} />}
        {currentStep === 1 && <ChoosePostType onSelectPostType={onSelectPostType} type={type} />}
        {currentStep === 2 && (
          <>
            <div style={{ fontSize: '15px', color: 'grey' }}>
              Vérifiez que votre question ne soit pas déjà posée ci-dessous{' '}
              <span role="img" aria-label="emoji">
                😊
              </span>
            </div>
            <br />
            {selectedPostType && (
              <PostTypeTag
                postType={selectedPostType}
                editable
                onEdit={() => {
                  setCurrentStep(1);
                }}
              />
            )}
            <br />
            <CreatePost
              type={type}
              refetch={refetch}
              typeId={id}
              parentId={null}
              callScroll={() => scrollToRef(messagesEnd)}
              withTitle
              afterSubmit={() => {
                setEditAnnonceVisible(false);
                setCurrentStep(0);
              }}
              currentPostType={selectedPostType}
            />
          </>
        )}
      </Modal>
    );

    const [showCommentSectionThread, setShowCommentSectionThread] = useState(false);

    const me = getUserInfo();

    // Post owner or admin or tutor
    const shouldShowResolvedButton =
      (isAdmin() || isTuteur() || me.id == mainPost?.user?.id) && !mainPost?.isResolved;
    const shouldShowUnResolvedButton =
      (isAdmin() || isTuteur() || me.id == mainPost?.user?.id) && mainPost?.isResolved;

    const canSeeNotificationButton = isAdmin() || isTuteur();

    // Thread / Topic page
    const renderDetailPage = () => {
      if (loading) {
        return <Card>{renderLoading()}</Card>;
      }
      if (!mainPost) {
        return <Empty description={t('CantFindTopic')} />;
      }

      const changeSubjectStatus = async (isResolved) => {
        try {
          await updatePostMutation({
            variables: { id: mainPost.id, post: { isResolved } }
          });
          message.success(`${t('TopicMarkedAs')} ${isResolved ? t('Resolved') : t('NonResolved')}`);
          await refetch();
        } catch (e) {
          showGqlErrorsInMessagePopupFromException(e);
          console.error(e);
        }
      };

      return (
        <>
          {isCommentListLoadedAndNotEmpty && (
            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.16) 0px 1px 4px' }}>
              {shouldShowResolvedButton && (
                <Button
                  icon={<CheckCircleTwoTone twoToneColor="#52c41a" />}
                  size="middle"
                  onClick={() => changeSubjectStatus(true)}
                >
                  {t('MarkAsResolved')}
                </Button>
              )}
              {shouldShowUnResolvedButton && (
                <Button
                  icon={<CheckCircleTwoTone twoToneColor="#aaaaaa" />}
                  size="middle"
                  onClick={() => changeSubjectStatus(false)}
                >
                  {t('MarkAsNonResolved')}
                </Button>
              )}
              {mainPost && canSeeNotificationButton && (
                <Popover
                  content={
                    <NotificationForm
                      GQLFunc={NOTIFY_POST_ADMIN}
                      message={{
                        postId: mainPost.id,
                        route: `/discussions/post/${getCommentaireTypeFromObject(mainPost)}/${mainPost.id}`
                      }}
                      title="Post"
                      body={mainPost.name}
                      showTitleAndBodyEdition={false}
                    />
                  }
                  title="Notifier"
                  trigger="click"
                >
                  <Button style={{ marginLeft: 4 }} icon={<NotificationOutlined />} />
                </Popover>
              )}
              <CommentRenderer
                {...mainPost}
                typeId={id}
                refetch={refetch}
                commentType={type}
                isDetailPage
              />
              <Divider />
              {showCommentSectionThread ? (
                <React.Fragment>
                  <h4 style={{ marginLeft: 15, marginBottom: -4 }}>{t('DoComment')}</h4>
                  <CreatePost
                    isAnswer
                    type={type}
                    refetch={refetch}
                    typeId={id}
                    parentId={postId}
                    callScroll={() => scrollToRef(messagesEnd)}
                    onCancel={() => setShowCommentSectionThread(false)}
                  />
                </React.Fragment>
              ) : (
                <Space>
                  <Button
                    type="primary"
                    onClick={() => setShowCommentSectionThread(true)}
                    shape="round"
                    icon={<MessageOutlined />}
                    size="large"
                  >
                    {t('DoComment')}...
                  </Button>
                  {(isTuteur() || isAdmin()) && (
                    <Button
                      type="default"
                      onClick={handleClickRegenerateAIPost}
                      shape="round"
                      icon={<RobotOutlined />}
                      size="large"
                      loading={loadingAiAnswer}
                    >
                      {t('GenerateAIAnswer')}
                    </Button>
                  )}
                </Space>
              )}
            </Card>
          )}
          {dataObject && dataObject.length === 0 && <Empty description={t('NoTopic')} />}
          {!dataObject && <Empty description={t('TopicNotFound')} />}
        </>
      );
    };

    const renderBoutonVoirCoursQcmInForum = () => {
      switch (type) {
        case CommentairesType.COURS:
          return (
            <Button icon={<EyeOutlined />} onClick={() => router.push(`/cours/${id}`)}>
              {t('SeeRelatedCourse')}
            </Button>
          );
        case CommentairesType.QCM:
          return (
            <Button onClick={() => router.push(`/qcm/${id}`)}>{t('SeeRelatedExercice')}</Button>
          );
        default:
          return <></>;
      }
    };

    const renderBoutonNouveauSujet = () => (
      <Button
        type="primary"
        onClick={showModalComment}
        shape="round"
        icon={<MessageOutlined />}
        size="middle"
      >
        {t('NewDiscussion')}
      </Button>
    );

    const renderBoutonPoserQuestion = () => {
      if (!showCreatePostButton) {
        return '';
      }
      if (isForum) {
        return (
          <div style={{ textAlign: 'left', marginTop: '10px' }}>
            <Space>
              {renderBoutonNouveauSujet()}
              {renderBoutonVoirCoursQcmInForum()}
            </Space>
            {renderModalCreation()}
          </div>
        );
      }
      return (
        <>
          <div style={{}}>
            <Button
              type="primary"
              onClick={showModalComment}
              shape="round"
              icon={<MessageOutlined />}
              size="large"
            >
              {t('AskQuestion')}
            </Button>
          </div>
          {renderModalCreation()}
        </>
      );
    };

    const getCardProps = () => {
      if (isForum) {
        return {
          type: 'inner',
          bodyStyle: { padding: '0px' }
        };
      }
      return {};
    };

    const commentsFilter = (
      <div>
        <Space>
          {showYearSelection && (
            <CommentsYearSelection
              defaultValue={filter.year || t('All')}
              type={type}
              typeId={id}
              onChangeYear={(_, item) => {
                setFilter({ ...filter, year: toInt(item.key) });
              }}
            />
          )}

          <Select
            defaultValue={DEFAULT_SORTING}
            onChange={(_, item) => {
              setFilter({ ...filter, sort: item.key });
            }}
            style={{ minWidth: 106 }}
            placeholder={t('general.Filter')}
            size="medium"
          >
            <Select.Option key={COMMENT_FILTER.TOP} value={COMMENT_FILTER.TOP}>
              {t('general.Top')}
            </Select.Option>
            <Select.Option key={COMMENT_FILTER.MOST_RECENT} value={COMMENT_FILTER.MOST_RECENT}>
              {t('general.MostRecent')}
            </Select.Option>
          </Select>
        </Space>
      </div>
    );

    return (
      <>
        {/* Liste commentaires */}
        <div {...getCardProps()} style={!isMobile ? { margin: '10px' } : {}}>
          {/* Sujet ou bouton poser question (dans cours, qcm) */}
          {showAskQuestionButton && (
            <div style={{ marginBottom: 10 }}>
              {isDetailPage ? renderDetailPage() : renderBoutonPoserQuestion()}
            </div>
          )}

          {children && children}

          {isCommentListLoadedAndNotEmpty ? (
            <Card style={{ overflow: 'hidden', boxShadow: 'rgba(0, 0, 0, 0.16) 0px 1px 4px' }}>
              {!isDetailPage && commentsFilter}
              <CommentList
                comments={comments}
                type={type}
                isForum={isForum}
                openInSamePage={openInSamePage}
                onOpenInSamePage={onOpenInSamePage}
                showAllYears={showAllYears}
              />
              <div ref={messagesEnd} style={{ float: 'left', clear: 'both' }} />
            </Card>
          ) : (
            <Empty description={t('NoResult')} />
          )}

          {!loading && error && <ErrorResult refetch={refetch} error={error} />}
          {loading && !error && renderLoading()}
        </div>
      </>
    );
  }
);

const CommentsYearSelection = ({ defaultValue, type, typeId, onChangeYear }) => {
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(getYearsQueryFromCommentaireType(type), {
    variables: getQueryVariablesFromCommentaireType(type, typeId),
    fetchPolicy: 'no-cache',
    skip: !typeId
  });
  const anneesFromData = data && !error && getYearsFromData(type, data);
  const [annees, setAnnees] = useState([]);
  useEffect(() => {
    if (anneesFromData) {
      const hasNotDefaultValueInList = !anneesFromData?.includes(defaultValue);
      if (hasNotDefaultValueInList) {
        setAnnees([...annees, ...anneesFromData, defaultValue]);
      } else {
        setAnnees([...annees, ...anneesFromData]);
      }
    }
  }, [anneesFromData]);
  return (
    <Select
      defaultValue={defaultValue}
      onChange={onChangeYear}
      loading={loading}
      placeholder={t('ChooseYear')}
      dropdownMatchSelectWidth={false}
    >
      {annees?.map((annee) => (
        <Select.Option key={annee} value={annee}>
          {formatAnnee(annee)}
        </Select.Option>
      ))}
    </Select>
  );
};

export default Commentaires;
