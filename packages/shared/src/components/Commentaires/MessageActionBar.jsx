import { CloseOutlined } from '@ant-design/icons';
import { Picker } from 'emoji-mart';
import {
  BotMessageSquare,
  MessageSquareWarning,
  Pencil,
  Reply,
  ShieldCheck,
  ShieldX,
  SmilePlus,
  Trash2
} from 'lucide-react';
import React, { useContext, useState } from 'react';
import { <PERSON><PERSON>, Popconfirm, Popover, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { IS_DEV, showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils';
import { useMutation } from '@apollo/client';
import { DELETE_POST, SET_AI_ANSWER_VERIFIED } from '@/shared/graphql/posts';
import { getUserInfo, isAdmin, isTuteur } from '@/shared/utils/authority';
import { ReportPostModal } from '@/shared/components/Commentaires/ReportPostModal';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { AiAnswerGenerationModal } from '@/shared/components/Commentaires/AiAnswerGenerationModal';

export function MessageActionBar({
  isHovering,
  message,
  refetch,
  setReplyTo,
  setEditMessageId,
  originalThreadPostId,
  inDrawer = false, // Mobile
  setShowMobileActionBar, // Mobile
  handleReaction // Callback to handle reaction
}) {
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;
  const isTopicMessage = message?.parentId === null;

  const { t } = useTranslation();
  const [openDelete, setOpenDelete] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [reportPostModalVisible, setReportPostModalVisible] = useState(false);
  const [deletePostMutation] = useMutation(DELETE_POST);
  const [setVerified, { loading: loadingSetVerified }] = useMutation(SET_AI_ANSWER_VERIFIED);
  const [generateAiAnswer, setGenerateAiAnswer] = useState(false);

  const canEditOrDelete = () => {
    return (
      //message.children?.length === 0 &&
      isAdmin() ||
      message?.user?.id === getUserInfo().id ||
      (message?.user?.bot === true && (isAdmin() || isTuteur()))
    );
  };

  const handleDeleteMessage = async () => {
    setDeleteLoading(true);
    try {
      // TODO si c'est un thread, on devrait supprimer tous les messages du thread
      await deletePostMutation({ variables: { id: message.id } });
      refetch();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
    setDeleteLoading(false);
  };

  const handleVerify = async () => {
    await setVerified({ variables: { postId: message.id } });
    await refetch();
  };

  const handleReply = () => {
    setReplyTo(message.id);
    setShowMobileActionBar && setShowMobileActionBar(false);
  };

  const handleEdit = () => {
    setEditMessageId(message.id);
    setShowMobileActionBar && setShowMobileActionBar(false);
  };
  const handleGenerateAiAnswer = () => {
    setGenerateAiAnswer(true);
  };

  const emojiButton = (emoji, size = 'large') => {
    return (
      <Button
        size={size}
        style={size === 'large' ? { fontSize: 30 } : {}}
        icon={emoji}
        type={'text'}
        onClick={() => {
          handleReaction(emoji);
          setShowMobileActionBar && setShowMobileActionBar(false);
        }}
      ></Button>
    );
  };
  const [emojisVisibles, setEmojisVisibles] = useState(false);
  const [isLoadingEmojis, setIsLoadingEmojis] = useState(false);

  const handleAddEmojiFromPicker = (emoji) => {
    if (!emoji) {
      console.error('No emoji selected');
      return;
    }
    handleReaction(emoji.native);
    setEmojisVisibles(false);
    setShowMobileActionBar && setShowMobileActionBar(false);
  };
  const emojiPickerButton = (size = 'large') => (
    <>
      <Tooltip title={t('general.Emojis')}>
        <Popover
          content={
            <Picker
              title={t('ChooseEmojis')}
              emoji="point_up"
              theme="light"
              onSelect={handleAddEmojiFromPicker}
              enableFrequentEmojiSort
            />
          }
          trigger="click"
          open={emojisVisibles}
        >
          <Button
            size={size}
            style={size === 'large' ? { fontSize: 30 } : {}}
            type="text"
            onClick={() => {
              setIsLoadingEmojis(!isLoadingEmojis);
              setEmojisVisibles(!emojisVisibles);
            }}
          >
            <SmilePlus className="custom-toolbar-button" />
          </Button>
        </Popover>
      </Tooltip>
    </>
  );

  // Web version small icons
  return (
    <>
      {/* Mobile version long press */}
      {inDrawer ? (
        <div
          style={{
            display: 'flex',
            gap: 8,
            flexDirection: 'column',
            userSelect: 'none'
          }}
        >
          <div
            style={{
              display: 'flex',
              gap: 7,
              flexDirection: 'row',
              marginBottom: 10
            }}
          >
            {/* Emojis (mobile) */}
            {emojiButton('❤️')}
            {emojiButton('😆')}
            {emojiButton('😮')}
            {emojiButton('😥')}
            {emojiButton('👍')}
            {emojiButton('👎')}
            {emojiPickerButton()}
          </div>

          {!isTopicMessage && (
            <Button
              style={{
                justifyContent: 'flex-start'
              }}
              block
              onClick={() => handleReply()}
            >
              <Reply /> {t('Reply')}
            </Button>
          )}

          {canEditOrDelete() && (
            <>
              <Button
                style={{
                  justifyContent: 'flex-start'
                }}
                block
                onClick={() => handleEdit()}
              >
                <Pencil /> {t('Edit')}
              </Button>

              <Popconfirm
                title={t('SureToDeleteMessage')}
                open={openDelete}
                onConfirm={handleDeleteMessage}
                okButtonProps={{ loading: deleteLoading }}
                onCancel={() => setOpenDelete(false)}
              >
                <Button
                  style={{
                    justifyContent: 'flex-start'
                  }}
                  block
                  onClick={() => setOpenDelete(true)}
                >
                  <Trash2 /> {t('Delete')}
                </Button>
              </Popconfirm>
            </>
          )}

          {(isAdmin() || isTuteur()) && (
            <Popover content={t('GenerateAIAnswer')}>
              <Button
                style={{
                  justifyContent: 'flex-start'
                }}
                block
                onClick={() => handleGenerateAiAnswer()}
              >
                <BotMessageSquare /> {t('GenerateAIAnswer')}
              </Button>
            </Popover>
          )}

          {message?.user?.bot === true && (isAdmin() || isTuteur()) && (
            <Popover
              content={message?.verifiedBy !== null ? t('MarkAsUnVerified') : t('MarkAsVerified')}
            >
              <Button
                block
                style={{
                  color: message?.verifiedBy !== null ? 'red' : primaryColor,
                  justifyContent: 'flex-start'
                }}
                disabled={loadingSetVerified}
                onClick={() => handleVerify()}
              >
                {message?.verifiedBy !== null ? <ShieldX /> : <ShieldCheck />}
                {message?.verifiedBy !== null ? t('MarkAsUnVerified') : t('MarkAsVerified')}
              </Button>
            </Popover>
          )}
          <Popover content={t('Report')}>
            {reportPostModalVisible && (
              <ReportPostModal
                closeModalHandler={() => setReportPostModalVisible(false)}
                isVisible={reportPostModalVisible}
                postId={message?.id}
              />
            )}
            <Button
              block
              style={{
                justifyContent: 'flex-start'
              }}
              onClick={() => setReportPostModalVisible(true)}
            >
              <MessageSquareWarning />
              {t('Report')}
            </Button>
          </Popover>

          <Button
            style={{
              justifyContent: 'flex-start'
            }}
            block
            onClick={() => setShowMobileActionBar(false)}
          >
            <CloseOutlined /> {t('Cancel')}
          </Button>
        </div>
      ) : (
        <div
          style={{
            position: 'absolute',
            top: -8,
            alignSelf: 'end',
            display: 'flex',
            gap: 8,
            right: 16,
            zIndex: 9,
            background: 'white',
            border: '1px solid #D9D9D9',
            borderRadius: 4,
            padding: 4,
            opacity: isHovering ? 1 : 0,
            transition: '300ms ease'
          }}
        >
          {emojiButton('👍', 'small')}
          {emojiButton('❤️', 'small')}
          {emojiButton('😆', 'small')}
          {emojiPickerButton('small')}
          {/*
          {emojiButton('😮', 'small')}
          {emojiButton('😥', 'small')}
          {emojiButton('👎', 'small')}
          */}
          {!isTopicMessage && (
            <Button type="text" size="small" onClick={() => handleReply()}>
              <Reply />
            </Button>
          )}

          {canEditOrDelete() && (
            <>
              <Button type="text" size="small" onClick={() => handleEdit()}>
                <Pencil />
              </Button>

              <Popconfirm
                title={t('SureToDeleteMessage')}
                open={openDelete}
                onConfirm={handleDeleteMessage}
                okButtonProps={{ loading: deleteLoading }}
                onCancel={() => setOpenDelete(false)}
              >
                <Button type="text" size="small" onClick={() => setOpenDelete(true)}>
                  <Trash2 />
                </Button>
              </Popconfirm>
            </>
          )}

          {/*
          <Button
            type="text"
            size="small"
            //style={{ color: 'red' }}
            //disabled={loadingDislike || loadingLike}
            onClick={() => handleReaction('👍')}
          >
            👍
          </Button>
          <Button
            type="text"
            size="small"
            //style={{ color: '#2be12b' }}
            //disabled={loadingDislike || loadingLike}
            onClick={() => handleReaction('👎')}
          >
            👎
          </Button>
          <Button
            type="text"
            size="small"
            //disabled={loadingDislike || loadingLike}
            onClick={() => handleReaction('❤️')}
          >
            ❤️
          </Button>
          */}

          {(isAdmin() || isTuteur()) && (
            <Popover content={t('GenerateAIAnswer')}>
              <Button type="text" size="small" onClick={() => handleGenerateAiAnswer()}>
                <BotMessageSquare />
              </Button>
            </Popover>
          )}
          {message?.user?.bot === true && (isAdmin() || isTuteur()) && (
            <Popover
              content={message?.verifiedBy !== null ? t('MarkAsUnVerified') : t('MarkAsVerified')}
            >
              <Button
                type="text"
                size="small"
                style={{ color: message?.verifiedBy !== null ? 'red' : primaryColor }}
                disabled={loadingSetVerified}
                onClick={() => handleVerify()}
              >
                {message?.verifiedBy !== null ? <ShieldX /> : <ShieldCheck />}
              </Button>
            </Popover>
          )}
          <Popover content={t('Report')}>
            {reportPostModalVisible && (
              <ReportPostModal
                closeModalHandler={() => setReportPostModalVisible(false)}
                isVisible={reportPostModalVisible}
                postId={message?.id}
              />
            )}
            <Button type="text" size="small" onClick={() => setReportPostModalVisible(true)}>
              <MessageSquareWarning />
            </Button>
          </Popover>
        </div>
      )}

      <AiAnswerGenerationModal
        generateAiAnswer={generateAiAnswer}
        setGenerateAiAnswer={setGenerateAiAnswer}
        message={message}
        refetch={refetch}
        originalThreadPostId={originalThreadPostId}
      />
    </>
  );
}
