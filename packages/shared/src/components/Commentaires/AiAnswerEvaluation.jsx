import { getUserInfo } from '@/shared/utils/authority';
import { <PERSON><PERSON>, Divider, Space } from 'antd';
import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from '@apollo/client';
import { GIVE_FEEDBACK_AI_ANSWER } from '@/shared/graphql/posts';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { AiAnswerEvaluationForm } from '@/shared/components/Commentaires/AiAnswerEvaluationForm';

export function AiAnswerEvaluation({ message, refetch, originalThreadPostId }) {
  const { t } = useTranslation();
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  const [showHelpForm, setShowHelpForm] = useState(false);
  const [aiPrecisionQuery, setAiPrecisionQuery] = useState('');

  const [feedbackAIAnswer, { loading: loadingGiveFeedback }] = useMutation(GIVE_FEEDBACK_AI_ANSWER);

  const AI_POST_FEEDBACK_STATE = {
    PENDING: 'pending', // waiting user feedback
    ACCEPTED: 'accepted', // user accepted the answer: no need to generate a new one

    REFUSED: 'refused', // user refused the answer
    REFUSED_NEED_HUMAN_HELP: 'refusedNeedHumanHelp',
    REFUSED_NEED_AI_HELP: 'refusedNeedNewAiHelp'
  };

  const onUserClickAiFeedBack = async (feedback) => {
    await feedbackAIAnswer({
      variables: {
        input: {
          postId: message.id,
          feedback,
          message: aiPrecisionQuery,
          originalThreadPostId
        }
      }
    });
    await refetch();
  };
  const acceptFeedBack = () => onUserClickAiFeedBack(AI_POST_FEEDBACK_STATE.ACCEPTED);
  const refuseFeedback = () =>
    onUserClickAiFeedBack(AI_POST_FEEDBACK_STATE.REFUSED_NEED_HUMAN_HELP);
  const generateNewAiAnswer = () => {
    onUserClickAiFeedBack(AI_POST_FEEDBACK_STATE.REFUSED_NEED_AI_HELP);
    /*
    console.info(
      'TODO implement new answer generation, params : ',
      message.text, // message de l'IA
      aiPrecisionQuery,
      message.parent // ID du message parent (peut etre besoin de le recup récursivement pour remonter la chaine a chaque message IA)
    );
    //@TODO poster un commentaire a la place du User, en réponse au message IA (parent = message.id), le contenu du message sera aiPrecisionQuery
     */
  };

  const shouldShowAIFeedback =
    message.state === AI_POST_FEEDBACK_STATE.PENDING &&
    String(message.userIdForAiFeedback) === String(getUserInfo()?.id);

  if (!shouldShowAIFeedback) {
    return null;
  }
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 12, paddingBottom: 12 }}>
      <Divider style={{ margin: 0 }} />
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          border: `1px solid ${primaryColor}`,
          borderRadius: 12,
          padding: 12,
          gap: 12,
          backgroundColor: `${primaryColor}44`
        }}
      >
        {showHelpForm ? (
          <AiAnswerEvaluationForm
            setShowHelpForm={setShowHelpForm}
            refuseFeedback={refuseFeedback}
            generateNewAiAnswer={generateNewAiAnswer}
            loadingGiveFeedback={loadingGiveFeedback}
            aiPrecisionQuery={aiPrecisionQuery}
            setAiPrecisionQuery={setAiPrecisionQuery}
          />
        ) : (
          <>
            <span>{t('AIGeneratedAnswerExplanation')}</span>
            <b>{t('AreYouSatisfiedWithTheAnswer?')}</b>
            <Space>
              <Button
                type="primary"
                onClick={acceptFeedBack}
                loading={loadingGiveFeedback}
                style={{ height: 'auto' }}
              >
                {t('YesThanks!')}
              </Button>
              <Button
                danger
                onClick={() => setShowHelpForm(true)}
                loading={loadingGiveFeedback}
                style={{ height: 'auto' }}
              >
                {t('INeedPrecisions')}
              </Button>
            </Space>
          </>
        )}
      </div>
    </div>
  );
}
