import { CommentRenderer } from '@/shared/components/Commentaires/CommentRenderer.jsx'
import { getCommentaireTypeFromObject } from '@/shared/services/commentaires.js'
import { List } from 'antd'
import QueueAnim from 'rc-queue-anim'
import React from 'react'

export const CommentList = ({ comments, type, showHeader = true, isForum = false, openInSamePage, onOpenInSamePage, showAllYears }) => {
  const isFirstLevel = () => comments && comments.length > 0 && comments[0].parentId === null
  const getHeader = () => {
    if (isFirstLevel()) {
      return `${comments.length} ${comments.length > 1 ? 'discussions' : 'discussion'}`
    }
    return '' // isForum or comment list
  }
  return (
    <List
      dataSource={comments}
      header={getHeader()}
      itemLayout="horizontal"
      renderItem={
        (itemProps, index) =>
          <QueueAnim
            type={['left', 'right']}
            key={index}
          >
            <CommentRenderer
              commentType={getCommentaireTypeFromObject(itemProps)}
              openInSamePage={openInSamePage}
              onOpenInSamePage={onOpenInSamePage}
              showYear={showAllYears}
              {...itemProps}
            />
          </QueueAnim>
      }
    />
  )
}