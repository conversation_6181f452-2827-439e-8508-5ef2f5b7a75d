import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';
import { useFixDrawerKeyboard } from '@/shared/hooks/useFixDrawerKeyboard';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import useMediaQuery from '@/shared/hooks/useMediaQuery';
import {
  COMMENT_FILTER,
  CommentairesType,
  getCurrentScholarYear,
  getDataObjectFromType,
  getMutationVariablesForCommentaire,
  getQueryFromCommentaireType,
  getQueryVariablesFromCommentaireType,
  goToCommentThreadByType
} from '@/shared/services/commentaires';
import { Drawer, Empty, Modal, notification, Select, Skeleton, Space } from 'antd';
import { useMutation, useQuery } from '@apollo/client';
import { TopicDetail } from '@/shared/components/Commentaires/TopicDetail';
import { TopicOverview } from '@/shared/components/Commentaires/TopicOverview';
import { isMobile, showGqlErrorsInMessagePopupFromException, toInt } from '@/shared/utils/utils';
import { SearchOrCreateTopic } from '@/shared/components/Commentaires/SearchOrCreateTopic';
import { CREATE_POST } from '@/shared/graphql/posts';
import { useTranslation } from 'react-i18next';
import { TopicsYearSelection } from '@/shared/components/Commentaires/TopicsYearSelection';
import { ChatBar } from '@/shared/components/Commentaires/ChatBar';
import { NewTopicEditor } from '@/shared/components/Commentaires/NewTopicEditor';
import { LimitationQuestionLayer } from '@/shared/components/LimitationQuestionLayer';

const DEFAULT_SORTING = COMMENT_FILTER.MOST_RECENT;

export function TopicsList({
  id,
  type,
  isDetailPage = false,
  showAllYears = true,
  postId = null,
  showYearSelection = true,
  showAskQuestionButton = true,
  hideIfEmpty = false
}) {
  const isSmallScreen = useMediaQuery('(max-width: 599px)');
  const [selectedTopic, setSelectedTopic] = React.useState(null);
  const [editorContent, setEditorContent] = useState(''); // Partie droite
  const [fileContent, setFileContent] = useState(null);
  const [fileImageContent, setFileImageContent] = useState(null);
  // New file list
  const [fileList, setFileList] = useState([]);
  const [createTopic, setCreateTopic] = useState(false); // Toggle entre la recherche et la création de topic

  //NEW TOPIC RELATED
  const [topicTitle, setTopicTitle] = useState('');
  const [topicMessage, setTopicMessage] = useState('');
  const [topicType, setTopicType] = useState(null);
  const [canPostTopic, setCanPostTopic] = useState(false);
  const onLimiteValidation = () => {
    setCanPostTopic(true);
  };

  const [topicIdToSelect, setTopicIdToSelect] = useState(null);

  const [replyTo, setReplyTo] = useState(null);
  const [createPost, { loading: loadingPostCreation }] = useMutation(CREATE_POST);
  const { t } = useTranslation();

  const getDefaultFilter = () => {
    if (isDetailPage) {
      return {};
    }
    if (showAllYears) {
      return { sort: DEFAULT_SORTING };
    }
    return { year: getCurrentScholarYear(), sort: DEFAULT_SORTING };
  };

  function sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  const [filter, setFilter] = useState(getDefaultFilter());

  // QUERY LISTE TOPICS
  const { loading, error, data, refetch } = useQuery(getQueryFromCommentaireType(type), {
    variables: getQueryVariablesFromCommentaireType(type, id, filter),
    fetchPolicy: 'no-cache',
    skip: !id || createTopic // Si on est en train de créer un topic ou pas de ID, on ne charge pas les messages
  });
  const dataObject = (data && !error && getDataObjectFromType(type, data)) || [];
  const dataQcmObject =
    (dataQcmToMerge &&
      !errorQcmToMerge &&
      getDataObjectFromType(CommentairesType.QCM, dataQcmToMerge)) ||
    [];
  const { data: dataQcmToMerge, error: errorQcmToMerge } = useQuery(
    getQueryFromCommentaireType(CommentairesType.QCM),
    {
      variables: getQueryVariablesFromCommentaireType(CommentairesType.QCM, id, filter),
      fetchPolicy: 'no-cache',
      skip: type !== CommentairesType.QUESTION_ANSWERS_IN_QCM
    }
  );

  const messageBeingReplied = useMemo(() => {
    return [...dataObject, ...dataQcmObject].find((message) => message.id === replyTo);
  }, [replyTo]);

  const topicsFilters = (
    <div>
      <Space>
        {showYearSelection && (
          <TopicsYearSelection
            defaultValue={filter.year || t('All')}
            type={type}
            typeId={id}
            onChangeYear={(_, item) => {
              setFilter({ ...filter, year: toInt(item.key) });
            }}
          />
        )}

        <Select
          defaultValue={DEFAULT_SORTING}
          onChange={(_, item) => {
            setFilter({ ...filter, sort: item.key });
          }}
          style={{ minWidth: 106 }}
          placeholder={t('general.Filter')}
          size="medium"
        >
          <Select.Option key={COMMENT_FILTER.TOP} value={COMMENT_FILTER.TOP}>
            {t('general.Top')}
          </Select.Option>
          <Select.Option key={COMMENT_FILTER.MOST_RECENT} value={COMMENT_FILTER.MOST_RECENT}>
            {t('general.MostRecent')}
          </Select.Option>
        </Select>
      </Space>
    </div>
  );

  const isCommentListLoadedAndNotEmpty =
    (dataObject && dataObject.length > 0) || (dataQcmObject && dataQcmObject.length > 0);
  const autoNestPosts = (items, itemId = null) =>
    items
      .filter((item) => item.parentId === itemId)
      .map((item) => ({ ...item, typeId: id, refetch, children: autoNestPosts(items, item.id) }));
  const topics =
    isCommentListLoadedAndNotEmpty && autoNestPosts([...dataObject, ...dataQcmObject], postId);

  const sortByDateDescending = (array) =>
    array.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

  const handleSubmitTopic = async () => {
    try {
      // Obligation de choisir type de topic partout sauf forum
      /*let topicTypeToSend = topicType;
      if (topicType === '') {
        topicTypeToSend = null;
      }
      */
      if (type !== CommentairesType.FORUM && (topicType === null || topicType === '')) {
        notification.error({
          message: 'Veuillez choisir un type de question'
        });
        return;
      }

      const newPost = await getMutationVariablesForCommentaire(
        topicMessage,
        type,
        id,
        null,
        false,
        fileContent, //legacy
        fileImageContent, //legacy
        topicTitle,
        topicType,
        fileList
      );
      const typeId = id;
      const { data: dataResult } = await createPost({ variables: { post: newPost } });
      notification.success({
        message: 'Message posté !'
        //description: '' //TODO voir si on ajoute un bouton VOIR
      });
      await refetch();
      setTopicTitle(''); // vide l'éditeur
      setTopicMessage(''); // vide l'éditeur
      setReplyTo(null);
      setTopicType(null);
      setFileList([]); // Reset file list
      setCreateTopic(false);
      if (dataResult && dataResult.createPost) {
        const { id } = dataResult.createPost;
        // Sur mobile il faut rediriger vers le topic
        if (isMobile || isSmallScreen) {
          goToCommentThreadByType(type, typeId, id);
          return;
        }
        setTopicIdToSelect(id);
      }
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error({ e });
    }
  };

  const submitMessage = async () => {
    try {
      const newPost = await getMutationVariablesForCommentaire(
        editorContent,
        type,
        id,
        replyTo !== null ? replyTo : selectedTopic.id,
        true,
        fileContent,
        fileImageContent,
        null,
        null,
        fileList
      );
      const { data: dataResult } = await createPost({ variables: { post: newPost } });
      notification.success({
        message: 'Message posté !'
        //description: '' //TODO voir si on ajoute un bouton VOIR
      });
      await refetch();
      setEditorContent(''); // vide l'éditeur
      setReplyTo(null);
      setFileList([]); // Reset file list
      if (dataResult && dataResult.createPost) {
        const { id } = dataResult.createPost;
        await sleep(150);
        const comm = document.getElementById(`mpost_${id}`);
        if (comm) {
          comm.scrollIntoView({
            behavior: 'smooth'
          });
        }
      }
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error({ e });
    }
  };

  //Auto select topic after creation
  useEffect(() => {
    if (topicIdToSelect !== null && Array.isArray(topics)) {
      const topicToSelect = Array.isArray(topics) && topics?.find((t) => t.id === topicIdToSelect);
      setSelectedTopic(topicToSelect);
      setTopicIdToSelect(null);
    }
  }, [topicIdToSelect, topics]);

  // Pour séries seulement masquer si y'a rien
  if (hideIfEmpty && (topics?.length === 0 || topics === false)) {
    return null;
  }

  return (
    <div
      style={{
        paddingLeft: 12,
        paddingRight: 12,
        paddingTop: 12,
        display: 'flex',
        flexDirection: 'column',
        gap: 12
      }}
    >
      {!isDetailPage && topicsFilters}
      <div
        style={{
          transition: '500ms',
          display: 'grid',
          gridTemplateColumns: selectedTopic ? '1fr 2fr' : '1fr 0fr'
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            gap: 12,
            justifyContent: 'start',
            maxHeight: 'calc(100vh - 148px)',
            overflowY: 'scroll',
            position: 'sticky',
            top: 64
          }}
        >
          {loadingPostCreation ? (
            <SpinnerCentered />
          ) : (
            <SearchOrCreateTopic
              topicTitle={topicTitle}
              topicMessage={topicMessage}
              topicType={topicType}
              setTopicTitle={setTopicTitle}
              setTopicMessage={setTopicMessage}
              setTopicType={setTopicType}
              handleSubmitTopic={handleSubmitTopic}
              type={type}
              fileList={fileList}
              setFileList={setFileList}
              handleChangeTopicTitle={(text) => {
                setFilter((f) => ({ ...f, text }));
              }}
              showAskQuestionButton={showAskQuestionButton}
              createTopic={createTopic}
              setCreateTopic={setCreateTopic}
            />
          )}

          {topics?.length > 0 &&
            sortByDateDescending(topics).map((c) => (
              <TopicOverview
                key={c.id}
                topic={c}
                onClick={() => {
                  if (isMobile || isSmallScreen) {
                    goToCommentThreadByType(type, id, c.id);
                  } else {
                    setSelectedTopic(c);
                  }
                  setReplyTo(null);
                  setEditorContent('');
                }}
                isSelected={selectedTopic?.id === c.id}
              />
            ))}
          {topics?.length === 0 && <Empty description={t('NoTopic')} style={{ marginTop: 24 }} />}
          {loading && (
            <>
              <Skeleton avatar paragraph={{ rows: 3 }} />
              <Skeleton avatar paragraph={{ rows: 3 }} />
              <Skeleton avatar paragraph={{ rows: 3 }} />
              <Skeleton avatar paragraph={{ rows: 3 }} />
            </>
          )}
        </div>
        {selectedTopic && !loading && (
          <>
            <div
              style={{
                maxHeight: 'calc(100vh - 148px)',
                overflowY: 'scroll'
              }}
            >
              <TopicDetail
                refetch={refetch}
                selectedTopic={
                  Array.isArray(topics) && topics?.find((t) => t.id === selectedTopic?.id)
                }
                type={type}
                typeId={id}
                showCloseButton
                closeTopic={() => {
                  setSelectedTopic(null);
                  setReplyTo(null);
                  setEditorContent('');
                }}
                setReplyTo={setReplyTo}
              />
              <ChatBar
                replyTo={replyTo}
                messageBeingReplied={messageBeingReplied}
                setReplyTo={setReplyTo}
                loading={loading}
                editorContent={editorContent}
                setEditorContent={setEditorContent}
                setFileContent={setFileContent}
                setFileImageContent={setFileImageContent}
                fileList={fileList}
                setFileList={setFileList}
                submitMessage={submitMessage}
                fileContent={fileContent}
                fileImageContent={fileImageContent}
              />
            </div>
          </>
        )}
        <Drawer
          open={createTopic}
          closable
          confirmLoading={false}
          footer={null}
          size="large"
          placement={isMobile ? 'right' : 'bottom'}
          height={isMobile ? 'calc(100% - 109px)' : 'calc(100% - 64px)'}
          onClose={() => {
            setCreateTopic(false);
            setTopicTitle('');
          }}
          style={{
            marginLeft: 'auto',
            marginRight: 'auto',
            overflow: 'hidden',
            marginTop: isMobile ? 109 : 'auto'
          }}
          bodyStyle={{
            maxHeight: isMobile ? 'calc(100vh - 109px)' : 'calc(100% - 64px)',
            overflowY: 'auto',
            overscrollBehavior: 'contain'
          }}
        >
          <>
            {canPostTopic ? (
              <NewTopicEditor
                topicTitle={topicTitle}
                setTopicTitle={setTopicTitle}
                topicMessage={topicMessage}
                setTopicMessage={setTopicMessage}
                setCreateTopic={setCreateTopic}
                topicType={topicType}
                setTopicType={setTopicType}
                topicTypeToSearch={type}
                handleSubmitTopic={handleSubmitTopic}
                fileList={fileList}
                setFileList={setFileList}
              />
            ) : (
              <LimitationQuestionLayer onLimiteValidation={onLimiteValidation} />
            )}
          </>
        </Drawer>
      </div>
    </div>
  );
}
