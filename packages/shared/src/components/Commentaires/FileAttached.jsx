import { Button } from 'antd';
import DownloadOutlined from '@ant-design/icons/lib/icons/DownloadOutlined';
import React, { useState } from 'react';
import { downloadFile, FILE_TYPE } from '@/shared/services/file';

export default function ({ file }) {
  const [loading, setLoading] = useState(false);

  const onDownload = async (e) => {
    setLoading(true);
    await downloadFile(FILE_TYPE.FILE, file.file);
    setLoading(false);
  };
  return (
    <div style={{ marginTop: 10 }}>
      <Button
        loading={loading}
        key="download"
        onClick={onDownload}
        style={{
          wordBreak: 'break-all',
          height: 'fit-content',
          display: 'flex',
          alignItems: 'center'
        }}>
        <DownloadOutlined />
        <span>{file.file}</span>
      </Button>
    </div>
  );
}
