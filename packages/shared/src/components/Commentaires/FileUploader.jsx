import { Badge, Upload } from 'antd';
import React, { useContext, useState } from 'react';
import { FilePlus } from 'lucide-react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { UploadedFilePreview } from '@/shared/components/Commentaires/UploadedFilePreview';

export function FileUploader({ fileList, setFileList, singleFile = false, customRemove = null }) {
  const [isHovered, setIsHovered] = useState(false);

  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  const uploadButton = () => {
    return (
      <button
        style={{ border: 0, background: 'none', cursor: 'pointer', lineHeight: 1 }}
        type="button"
      >
        {
          /*loading ? (
          <Spin indicator={<LoadingOutlined spin />} />
        )*/
          <span style={{ color: 'black', fontSize: 50 }}>
            <FilePlus />
          </span>
        }
      </button>
    );
  };

  const handleFileChange = (info) => {
    let newFileList = [...info.fileList];

    newFileList = newFileList.map((file) => {
      if (file.response) {
        file.url = file.response.url;
      } else if (!file.url) file.url = URL.createObjectURL(file.originFileObj);
      return file;
    });

    if (singleFile) {
      setFileList(newFileList[0]);
    } else {
      setFileList(newFileList);
    }
  };

  const handleFileRemove = (file) => {
    customRemove
      ? customRemove()
      : setFileList((prevList) => prevList.filter((f) => f.uid !== file.uid));
  };

  return (
    <div
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        position: 'relative',
        width: 100,
        height: 100,
        border: '1px dashed #ccc',
        borderRadius: '8px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: isHovered ? '#f1f1f1' : '#fff',
        cursor: 'pointer',
        marginRight: 12,
        marginTop: 12
      }}
    >
      <Badge count={fileList.length} offset={[16, -16]} color={primaryColor}>
        {fileList.length === 1 ? (
          <UploadedFilePreview
            key={fileList[0].uid}
            file={fileList[0]}
            handleFileRemove={handleFileRemove}
          />
        ) : (
          <Upload
            multiple
            onChange={handleFileChange}
            showUploadList={false}
            fileList={fileList}
            beforeUpload={() => false}
            maxCount={9}
          >
            {uploadButton()}
          </Upload>
        )}
      </Badge>

      {isHovered && fileList.length > 1 && (
        <div
          style={{
            position: 'absolute',
            top: -1,
            right: 90,
            backgroundColor: '#f1f1f1',
            borderTop: '1px dashed #ccc',
            borderLeft: '1px dashed #ccc',
            borderBottom: '1px dashed #ccc',
            padding: '0 12px',
            borderBottomLeftRadius: 12,
            borderTopLeftRadius: 12,
            zIndex: 10,
            height: 100,
            display: 'flex',
            alignItems: 'center',
            gap: 12
          }}
        >
          {fileList.map((file) => (
            <UploadedFilePreview key={file.uid} file={file} handleFileRemove={handleFileRemove} />
          ))}
        </div>
      )}
    </div>
  );
}
