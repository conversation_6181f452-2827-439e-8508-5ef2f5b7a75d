import { Popover, Tag } from 'antd';
import React, { useContext } from 'react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { useTranslation } from 'react-i18next';

export function OPBadge() {
  const { t } = useTranslation();
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  return (
    <Popover content={t('OPExplanation')}>
      <Tag color={primaryColor} style={{ margin: '0 8px' }}>
        OP
      </Tag>
    </Popover>
  );
}
