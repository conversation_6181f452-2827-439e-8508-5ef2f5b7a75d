import { File, Image, Trash2 } from 'lucide-react';
import React, { useState } from 'react';
import { Button } from 'antd';

export function UploadedFilePreview({ file, handleFileRemove }) {
  const [isHovered, setIsHovered] = useState(false);

  const getExtensionFromUrl = (url) => {
    const cleanUrl = url?.split('?')[0]; // Supprimer les query params
    const parts = cleanUrl?.split('.');
    if (!parts || parts.length <= 1) return '';
    return parts.pop().toLowerCase();
  };

  const isImage = (file) => {
    const type = file?.type;
    const url = file?.url;

    if (type) return type.startsWith('image/');

    const ext = getExtensionFromUrl(url);
    return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'].includes(ext);
  };

  const isDisplayable = (file) => {
    const type = file?.type;
    const url = file?.url;

    if (type) return type.startsWith('image/') || type.startsWith('video/');

    const ext = getExtensionFromUrl(url);
    return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg', 'mp4', 'webm', 'ogg'].includes(ext);
  };

  const getFileExtension = (file) => {
    if (file?.name) {
      const parts = file.name.split('.');
      if (parts.length <= 1) return '';
      return parts.pop().toUpperCase();
    }

    const ext = getExtensionFromUrl(file?.url);
    return ext ? ext.toUpperCase() : '';
  };

  return (
    <div
      onMouseOver={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ position: 'relative' }}
    >
      <div
        style={{
          position: 'absolute',
          top: -13,
          right: -13,
          zIndex: 9,
          background: 'white',
          border: '1px solid #D9D9D9',
          borderRadius: 100,
          opacity: isHovered ? 1 : 0,
          transition: '300ms ease'
        }}
      >
        <Button size="small" type="text" icon={<Trash2 />} onClick={() => handleFileRemove(file)} />
      </div>
      {isImage(file) ? (
        <div
          style={{
            width: 75,
            height: 75,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <div
            style={{
              backgroundImage: file.url ? `url("${file.url}")` : undefined,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              width: '100%',
              height: '100%',
              borderRadius: 12,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          ></div>
        </div>
      ) : (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            gap: 4,
            height: 75,
            width: 75,
            background: 'white',
            borderRadius: 12,
            padding: 8
          }}
        >
          <span style={{ fontSize: 30, lineHeight: '22px' }}>
            {isDisplayable(file) ? <Image /> : <File />}
          </span>
          <span
            style={{
              fontSize: 12,
              lineHeight: '12px',
              maxWidth: 59,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis'
            }}
          >
            {getFileExtension(file)}
          </span>
          <span
            style={{
              fontSize: 12,
              lineHeight: '12px',
              maxWidth: 59,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis'
            }}
          >
            {file.name}
          </span>
        </div>
      )}
    </div>
  );
}
