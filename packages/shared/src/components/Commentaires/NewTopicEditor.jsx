import SelectTopicType from '@/shared/components/Commentaires/SelectTopicType';
import { Button, Divider, Input, Popover } from 'antd';
import React from 'react';
import { FileUploader } from '@/shared/components/Commentaires/FileUploader';
import { MessageCirclePlus, MessageCircleX } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { CustomEditor } from '@/shared/pages/messages/components/CustomEditor';

export function NewTopicEditor({
  topicTitle,
  setTopicTitle,
  topicMessage,
  setTopicMessage,
  topicType,
  setTopicType,
  setCreateTopic,
  topicTypeToSearch,
  handleSubmitTopic,
  fileList,
  setFileList
}) {
  const { t } = useTranslation();

  const requireTopicType = topicTypeToSearch !== 'FORUM';

  const eraseAllFields = () => {
    setTopicTitle('');
    setTopicMessage('');
    setTopicType('');
    setFileList([]);
  };

  return (
    <div style={{ width: '100%' }}>
      <div style={{ display: 'grid', gridTemplateColumns: '30px 1fr 125px', position: 'relative' }}>
        <span style={{ fontSize: 30, lineHeight: 1, paddingTop: 7 }}>
          <Popover content={t('EraseAll')}>
            <MessageCircleX onClick={() => eraseAllFields()} style={{ cursor: 'pointer' }} />
          </Popover>
        </span>
        <div style={{ position: 'relative' }}>
          <Input
            placeholder={t('Title')}
            value={topicTitle}
            style={{ fontSize: 24, fontWeight: 700 }}
            variant="borderless"
            size="large"
            onChange={(e) => setTopicTitle(e.target.value)}
            autoFocus
          />
        </div>
        <div
          style={{
            position: 'absolute',
            top: 0,
            right: 0
          }}
        >
          <FileUploader fileList={fileList} setFileList={setFileList} />
        </div>
      </div>
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 125px' }}>
        <CustomEditor
          customBgColor="white"
          onChange={(value) => setTopicMessage(value)}
          value={topicMessage}
          placeholder={t('EnterAMessage')}
          noPadding
          hideUploader
          hideSendButton
          fileList={fileList}
          setFileList={setFileList}
          maxHeight={'40vh'}
        />
      </div>

      <Divider />
      {/* Type de topic à sélectionner, partout sauf sur le forum */}
      {requireTopicType && (
        <SelectTopicType
          topicTypeToSearch={topicTypeToSearch}
          setTopicType={setTopicType}
          topicType={topicType}
          displayError={!topicType && topicTitle.length > 0 && topicMessage.length > 0}
        />
      )}

      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          gap: 8,
          marginTop: 10
        }}
      >
        {requireTopicType && !topicType && topicTitle.length > 0 && topicMessage.length > 0 && (
          <span style={{ color: 'red' }}>{t('TopicTypeRequired')}</span>
        )}
        <Button
          type="primary"
          style={{ fontSize: 22, lineHeight: 1, display: 'flex' }}
          onClick={handleSubmitTopic}
          disabled={(requireTopicType && !topicType) || !topicTitle || !topicMessage}
        >
          <MessageCirclePlus style={{ marginRight: 8 }} />
          {t('send')}
        </Button>
      </div>
    </div>
  );
}
