import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import {
  PROPRIETARY_EXTENSION,
  useDiapoContext
} from '@/shared/components/diapoViewer/DiapoViewerContextProvider';
import DocViewer, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import { ONE_TRACK_KEY } from '@/shared/components/diapoViewer/DiapoViewerTableManager';
import { FILE_TYPE, getProtectedUrl } from '@/shared/services/file';
import debounce from 'lodash.debounce';
import '@cyntler/react-doc-viewer/dist/index.css';
import MyCustomHeader from '@/shared/components/diapoViewer/navBar/MyCustomHeader';
import { useTranslation } from 'react-i18next';
import loading_placeholder from '@/shared/assets/loading_image.png';

const DiapoViewerManager = ({ children }) => {
  const { t } = useTranslation();
  const { width, tracks, viewerInput, setViewerInput, viewerRef, activeDocument } =
    useDiapoContext();

  const DEBOUNCE_DELAY = 500;

  const [docViewerKey, setDocViewerKey] = useState(0);
  const config = useMemo(
    () => ({
      header: {
        overrideComponent: () => <></>
      }
    }),
    []
  );

  ////////////// Correction du bug de clic sur bouton/header avec une ref du wrapper,
  const wrapperRef = useRef('wrapper');

  // Nécessaire pour bloquer le comportement parasite du componant
  useLayoutEffect(() => {
    if (!wrapperRef.current) return;

    // Fonction qui sécurise un noeud et tous ses descendants
    const secureButtons = (node) => {
      if (node.nodeName === 'BUTTON' && !node.hasAttribute('type')) {
        node.setAttribute('type', 'button');
      }
      node
        .querySelectorAll?.('button:not([type])')
        .forEach((btn) => btn.setAttribute('type', 'button'));
    };

    secureButtons(wrapperRef.current);

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((m) => {
        m.addedNodes.forEach(secureButtons);
      });
    });

    observer.observe(wrapperRef.current, { childList: true, subtree: true });
    return () => observer.disconnect();
  }, [wrapperRef]);

  /////////////// Debounce de l'update

  const getData = useCallback(async (tracks) => {
    if (true) {
      const ordered = Object.values(tracks).sort(
        (a, b) => a[ONE_TRACK_KEY.INDEX] - b[ONE_TRACK_KEY.INDEX]
      );

      const temp = [];

      // Pour chaque document dans track
      for (const key in ordered) {
        if (ordered.hasOwnProperty(key)) {
          const subObject = ordered[key];

          ////// On check déjà les cas d'arrêt :
          // Si la file est hidden
          if (subObject[ONE_TRACK_KEY.HIDDEN]) {
            continue;
          }

          // Si la file est un fichier propriétaire et si il est en loading
          if (subObject[ONE_TRACK_KEY.LOADING]) {
            const loadingStructure = {
              ...loadingPng,
              key: subObject[ONE_TRACK_KEY.KEY]
            };

            temp.push(loadingStructure);
            continue;
          }

          ////// Maintenant, on créé le placeholder de l'objet, auquel on ajoutera l'uri
          let data = {
            fileTypes: subObject[ONE_TRACK_KEY.EXTENSION],
            fileName: subObject[ONE_TRACK_KEY.CLEAN_NAME],
            title: subObject[ONE_TRACK_KEY.TITRE],
            allowDownload: subObject[ONE_TRACK_KEY.ALLOW_DOWNLOAD],
            backname: subObject[ONE_TRACK_KEY.BACK_NAME],
            key: subObject[ONE_TRACK_KEY.KEY],
            uri: null
          };

          // préparation
          const backName = subObject[ONE_TRACK_KEY.BACK_NAME];
          const file = subObject[ONE_TRACK_KEY.FRONT_FILE];
          const tempFile = subObject[ONE_TRACK_KEY.TEMP_FILE];

          // Si fichier backend, propriétaire ou pas, alors on file l'URL publique
          if (typeof backName === 'string' && backName !== '') {
            data.uri = await getProtectedUrl(FILE_TYPE.FILE, backName);

            // Si pas backend et que l'on est en prorpiary extension, il faut linker to le temp path
          } else if (PROPRIETARY_EXTENSION.includes(subObject[ONE_TRACK_KEY.EXTENSION])) {
            data.uri = await getProtectedUrl(FILE_TYPE.TEMP, tempFile);
          } else if (file) {
            data.uri = URL.createObjectURL(file);
          }

          temp.push(data);
        }
      }
      setViewerInput(temp);
      setDocViewerKey((prev) => prev + 1);
    }
  }, []);

  const getDataDebounced = useRef(debounce(getData, DEBOUNCE_DELAY)).current;

  useEffect(() => {
    getDataDebounced(tracks);
    return () => getDataDebounced.cancel();
  }, [tracks]);

  const loadingPng = {
    uri: loading_placeholder,
    fileType: 'png',
    fileName: t('DiapoFE.LoadingDocumentFilenamePlaceholder'),
    title: t('DiapoFE.LoadingDocumentTitlePlaceholder'),
    allowDownload: false,
    backname: null,
    key: null,
    fileLoader: ({ fileLoaderComplete }) => fileLoaderComplete()
  };

  return (
    <div
      id={'diapoViewerWithoutDownload'}
      ref={wrapperRef}
      style={{
        minWidth: '300px',
        width: width || '100%',
        maxWidth: '100%',
        height: '100%',
        overflowX: 'auto'
      }}
    >
      {/* Affichage du viewer */}
      {viewerInput.length > 0 && (
        <MyCustomHeader titleAndDescriptionChildren={children}>
          <DocViewer
            key={docViewerKey}
            ref={viewerRef}
            documents={viewerInput}
            activeDocument={activeDocument}
            pluginRenderers={[...DocViewerRenderers]}
            prefetchMethod={'GET'} // compatibilité AWS
            config={config}
            style={{ overflowX: 'auto', maxHeight: '80dvh', overflowY: 'auto' }}
          />
        </MyCustomHeader>
      )}

      {/* Affichage du placeholder */}
      {viewerInput.length === 0 && (
        <div
          style={{
            display: 'flex',
            width: '100%',
            height: '100%',
            backgroundColor: '#eee',
            border: '1px dashed #ccc',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <span style={{ fontSize: '2rem', fontWeight: 'bold', color: '#999' }}>
            {t('DiapoFE.PlaceholderSentenceBeforeAnyDocuments')}
          </span>
        </div>
      )}
    </div>
  );
};

export default DiapoViewerManager;
