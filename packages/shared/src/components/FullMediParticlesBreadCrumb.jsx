import eventIcon from '@/shared/assets/event.svg';
import { GET_CONFIG } from '@/shared/graphql/home.js';
import { CONFIG_KEYS, DEFAULT_CONFIG_VARIABLES, getPublicSrc } from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import {
  getUrlProtectedRessource,
  GlobalConfig,
  isMobile,
  tryParseJSONObject
} from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { MediParticlesBreadcrumb } from '@/shared/components/MediParticlesBreadcrumb';
import React from 'react';
import coursImageLeft from '@/shared/assets/cours-image-left.svg';
import { Textfit } from 'react-textfit';
import { Typography } from 'antd';
import useMediaQuery from '@/shared/hooks/useMediaQuery';

export default function ({
  title,
  image = null,
  imageType = undefined,
  subtitle,
  height = '76px',
  textCenter = false,
  actionButton = null
}) {
  const {
    loading: loadingConfig,
    error: errorConfig,
    data: dataConfig,
    refetch: refetchConfig
  } = useQuery(GET_CONFIG, { fetchPolicy: 'cache-first', ...DEFAULT_CONFIG_VARIABLES });
  const config = dataConfig?.config;

  const isLargeScreen = useMediaQuery('(min-width: 450px)');

  const colors = tryParseJSONObject(
    getValueFromKeyConfigData(config, CONFIG_KEYS.COLORS_BREADCRUMB)
  ) || {
    color: '#090979',
    color2: '#00d4ff'
  };
  const getImage = () => {
    if (imageType === 'fileCours' && !image) {
      const customImageCours = getValueFromKeyConfigData(config, CONFIG_KEYS.LOGO_NAVIGATION_COURS);
      if (customImageCours) {
        return getPublicSrc(customImageCours);
      }
      // Default image
      return coursImageLeft;
    }
    if (imageType === 'event' && !image) {
      return eventIcon;
    }
    return getUrlProtectedRessource(GlobalConfig.get().FILES_URL + image);
  };

  const imageStyle =
    isMobile || !isLargeScreen
      ? {
          maxWidth: '55px',
          marginTop: '-55px',
          marginLeft: '10px',
          marginBottom: '5px'
        }
      : {
          width: '100px',
          maxWidth: '100px',
          marginTop: '-35px',
          marginLeft: '30px',
          marginBottom: '5px'
        };

  const breadCrumbStyle =
    isMobile || !isLargeScreen
      ? {
          fontSize: '28px',
          fontWeight: 'bold'
        }
      : {
          fontWeight: 'bold'
        };

  const backgroundBannerImage = getValueFromKeyConfigData(
    config,
    CONFIG_KEYS.BACKGROUND_BANNER_IMAGE
  );

  return (
    <>
      <div
        style={{
          background: backgroundBannerImage
            ? `url(${getPublicSrc(backgroundBannerImage)})`
            : `linear-gradient(90deg, ${colors?.color} 0%, ${colors?.color2} 100%) no-repeat 50% 50%`,
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          height,
          // https://connect.groupe-medisup.com/medibox2-api/VoH7Gu-x69-fond_medisup.svg
          position: 'relative',
          marginTop: isMobile ? 12 : 0
        }}
      >
        <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height }}>
          <MediParticlesBreadcrumb colors={colors} height={height} />
        </div>
        <div>
          <div
            style={{
              paddingRight: 24,
              height: 76,
              paddingLeft: image || imageType ? (isMobile || !isLargeScreen ? 75 : 160) : 24,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              textAlign: textCenter ? 'center' : 'left',
              background:
                'linear-gradient(90deg, rgba(255,255,255,0.5018601190476191) 0%, rgba(255,255,255,0.4990589985994398) 75%, rgba(255,255,255,0) 100%)'
            }}
          >
            <Textfit
              style={{ fontWeight: 800, flexGrow: 1, maxHeight: 35, lineHeight: 1 }}
              mode="multi"
              max={30}
              min={15}
            >
              {title}
            </Textfit>

            <Typography.Paragraph
              type="secondary"
              style={{ margin: 0, lineHeight: 1.2, fontWeight: 600 }}
              ellipsis={{
                rows: 2,
                expandable: false
              }}
            >
              {subtitle}
            </Typography.Paragraph>
          </div>
          {actionButton !== null && (
            <div
              style={{
                paddingTop: 24,
                paddingRight: 24,
                position: 'absolute',
                right: 0,
                top: 0,
                zIndex: 9
              }}
            >
              {actionButton}
            </div>
          )}
        </div>
      </div>

      {(image || imageType) && (
        <div
          style={{
            position: 'relative',
            marginTop: isMobile || !isLargeScreen ? -19 : -38,
            //height: 37
            minHeight: 38
          }}
        >
          <img src={getImage()} style={imageStyle} alt="imageLogo" />
        </div>
      )}
    </>
  );
}
