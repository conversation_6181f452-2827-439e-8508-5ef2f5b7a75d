import React, { useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PushpinFilled, PushpinOutlined } from '@ant-design/icons';
import { Mail, MailOpen } from 'lucide-react';
import { SwipeableListItem, SwipeAction, TrailingActions } from 'react-swipeable-list';
import { GlobalContext } from '@/shared/layouts/BlankLayout';

export function SwipableNotification({
  children,
  notificationId,
  pinned,
  seen,
  pinAction, // toggle pin/unpin
  markAsOpenedAction, // toggle open/unoepened
  clickAction
}) {
  const { t } = useTranslation();
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  const lucideIconsProps = {
    size: 32,
    strokeWidth: 2
  };

  const pinIcon = useMemo(() => {
    return pinned ? <PushpinFilled style={{ color: 'orange' }} /> : <PushpinOutlined />;
  }, [pinned]);
  const seenIcon = useMemo(() => {
    return seen ? <MailOpen {...lucideIconsProps} /> : <Mail {...lucideIconsProps} />;
  }, [seen]);

  const pinText = useMemo(() => {
    return pinned ? t('notifications.Unpin') : t('notifications.Pin');
  }, [pinned]);
  const seenText = useMemo(() => {
    return seen ? t('notifications.MarkAsUnOpened') : t('notifications.MarkAsOpened');
  }, [seen]);

  const pinLabel = useMemo(() => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        {pinIcon}
        <span>{pinText}</span>
      </div>
    );
  }, [pinned]);
  const seenLabel = useMemo(() => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        {seenIcon}
        <span>{seenText}</span>
      </div>
    );
  }, [seen]);

  const trailingActions = () => (
    <TrailingActions>
      <SwipeAction
        onClick={() => {
          markAsOpenedAction(notificationId);
        }}
      >
        <div
          style={{
            backgroundColor: primaryColor,
            color: 'white',
            height: '100%',
            display: 'flex',
            alignIitems: 'center',
            padding: 8,
            fontSize: 12,
            fontWeight: 500,
            boxSizing: 'border-box',
            userSelect: 'none'
          }}
        >
          {seenLabel}
        </div>
      </SwipeAction>

      <SwipeAction
        onClick={() => {
          pinAction(notificationId);
        }}
      >
        <div
          style={{
            backgroundColor: 'orange',
            color: 'white',
            height: '100%',
            display: 'flex',
            alignIitems: 'center',
            padding: 8,
            fontSize: 12,
            fontWeight: 500,
            boxSizing: 'border-box',
            userSelect: 'none'
          }}
        >
          {pinLabel}
        </div>
      </SwipeAction>
    </TrailingActions>
  );

  return (
    <SwipeableListItem onClick={() => clickAction()} trailingActions={trailingActions()}>
      {children}
    </SwipeableListItem>
  );
}
