import { goToNotifTarget } from '@/shared/services/notification';
import { <PERSON><PERSON>, Divider, Popover } from 'antd';
import { Mail, MailOpen, SquareArrowOutUpRight } from 'lucide-react';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PushpinFilled, PushpinOutlined } from '@ant-design/icons';

export default function NotifToolbar({
  selectedNotif,
  markAsOpenedAction,
  pinAction,
  showDivider = false
}) {
  const { t } = useTranslation();

  const onTogglePin = (e) => {
    pinAction(selectedNotif?.id);
  };
  const onToggleSeen = (e) => {
    markAsOpenedAction(selectedNotif?.id);
  };

  const pinIcon = useMemo(() => {
    return selectedNotif?.pinned ? (
      <PushpinFilled style={{ color: 'orange' }} />
    ) : (
      <PushpinOutlined />
    );
  }, [selectedNotif]);
  const seenIcon = useMemo(() => {
    return selectedNotif?.seen ? (
      <MailOpen {...lucideIconsProps} />
    ) : (
      <Mail {...lucideIconsProps} />
    );
  }, [selectedNotif]);

  const pinLabel = useMemo(() => {
    return selectedNotif?.pinned ? t('notifications.Unpin') : t('notifications.Pin');
  }, [selectedNotif]);
  const seenLabel = useMemo(() => {
    return selectedNotif?.seen
      ? t('notifications.MarkAsUnOpened')
      : t('notifications.MarkAsOpened'); /*  */
  }, [selectedNotif]);

  const lucideIconsProps = {
    size: 32,
    strokeWidth: 2
  };

  if (!selectedNotif) {
    return null;
  }
  return (
    <>
      <div
        style={{
          width: '100%',
          padding: '5px',
          position: 'sticky',
          top: 0,
          background: 'white',
          zIndex: 9
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: 12, justifyContent: 'flex-end' }}>
          <Popover content={pinLabel} style={{ zIndex: 9 }}>
            <Button type="text" icon={pinIcon} onClick={onTogglePin} />
          </Popover>

          <Popover content={seenLabel} style={{ zIndex: 9 }}>
            <Button type="text" icon={seenIcon} onClick={onToggleSeen} />
          </Popover>

          <Popover content={t('notifications.GoToNotification')} style={{ zIndex: 9 }}>
            <Button
              type="text"
              icon={<SquareArrowOutUpRight />}
              onClick={() => goToNotifTarget(selectedNotif)}
            />
          </Popover>
        </div>
        {showDivider && <Divider style={{ margin: 0 }} />}
      </div>
    </>
  );
}
