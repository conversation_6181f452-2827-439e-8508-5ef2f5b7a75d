import { Input, InputNumber } from 'antd';
import React, { useEffect, useState } from 'react';

const EditableByDoubleClick = ({ value, onChange, type = 'text', ...props }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [currentValue, setCurrentValue] = useState(value);

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleBlur = () => {
    setIsEditing(false);
    onChange(currentValue);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleBlur();
    }
  };

  const handleChange = (e) => {
    if (type === 'number') {
      setCurrentValue(e);
    } else {
      setCurrentValue(e.target.value);
    }
  };

  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  return (
    <div onDoubleClick={handleDoubleClick}>
      {isEditing ? (
        type === 'number' ? (
          <InputNumber
            {...props}
            value={currentValue}
            onChange={handleChange}
            onBlur={handleBlur}
            onPressEnter={handleKeyPress}
            autoFocus
          />
        ) : (
          <Input
            {...props}
            value={currentValue}
            onChange={handleChange}
            onBlur={handleBlur}
            onPressEnter={handleKeyPress}
            autoFocus
          />
        )
      ) : (
        <span>{currentValue}</span>
      )}
    </div>
  );
};

export default EditableByDoubleClick;
