import React from 'react';
import PullToRefresh from 'react-simple-pull-to-refresh';
import { useTranslation } from 'react-i18next';

export const ExoPullToRefresh = ({ children, onRefresh, enable = true }) => {
  const { t } = useTranslation();
  if (!enable) {
    return children;
  }
  return (
    <PullToRefresh
      pullingContent={<div style={{ textAlign: 'center' }}>{t('PullToRefresh')}</div>}
      onRefresh={onRefresh}
      //refreshingContent={}
    >
      {children}
    </PullToRefresh>
  );
};
