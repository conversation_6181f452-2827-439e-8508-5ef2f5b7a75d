import React, { useEffect, useState } from 'react';
import { Button, message, Upload , Tag } from 'antd';
import { FileExcelOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const mimeTypes = {
  // Vidéos
  mp4: 'video/mp4',
  webm: 'video/webm',

  // Images
  jpg: 'image/jpeg',
  jpeg: 'image/jpeg',
  png: 'image/png',
  gif: 'image/gif',
  webp: 'image/webp',
  bmp: 'image/bmp',
  svg: 'image/svg+xml',
  tiff: 'image/tiff',

  // Audio
  mp3: 'audio/mpeg',
  wav: 'audio/wav',
  flac: 'audio/flac',
  aac: 'audio/aac',
  m4a: 'audio/mp4',

  // Other
  vtt: 'text/vtt'
};

const UploadFileSelecter = ({
  setter = () => {},
  AUTHORIZED_FILE_EXTENSIONS = [],
  fileStructure = null,
  componantHeight = null,
  componantWidth = null,
  mimeTypeDico = mimeTypes,
  uploadText = '',
  useIcon = <FileExcelOutlined />,
  size = 'normal',
  type = 'default',
  showFile=false,
  showFileIcon=null,
}) => {
  const { t } = useTranslation();
  const [file, setFile] = useState(fileStructure);

  useEffect(() => {
    if (file) {
      setter(file);
    }
  }, [file]);

  return (
    <div
      style={{display:"flex",flexDirection:"column"}}
    >
      <Upload
        name="file"
        listType="file"
        showUploadList={false}
        beforeUpload={async (file, fileList) => {
          // Récupération de l'extension
          const extension = file?.name?.split('.')?.pop().toLowerCase();

        if (AUTHORIZED_FILE_EXTENSIONS.includes(`${extension}`)) {
          const newFile = {
            file: file,
            url: await URL.createObjectURL(file), // Generation de l'URL
            extension,
            mimeType: mimeTypeDico?.[extension],
            name: file?.name
          };
          setFile(newFile);
          return false;
        } else {
          message.error(
            t('UploadBadExtensionErrorMessage', {
              extension,
              validesExtension: AUTHORIZED_FILE_EXTENSIONS.join(', ')
            })
          );

            return Upload.LIST_IGNORE;
          }
        }}
        fileList={file ? [file?.name] : []}
        onRemove={(file) => {
          setFile(null);
        }}
      >
        <Button
          {...(useIcon && { icon: useIcon })}
          style={{
            ...(componantWidth != null && { width: `${componantWidth}px` }), // ajoute width uniquement si ≠ null/undefined
            ...(componantHeight != null && { height: `${componantHeight}px` }),
            size: size
          }}
          type={type}
        >
          {uploadText}
        </Button>
      </Upload>
      {showFile && file &&
        <Tag style={{width:"fit-content",marginTop:'5px'}} color={"green"}>
          <>
            {showFileIcon && showFileIcon } {file?.name}
          </>
        </Tag>
      }
    </div>
  );
};

export default UploadFileSelecter;
