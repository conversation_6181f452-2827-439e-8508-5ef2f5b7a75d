import React, { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { notification } from 'antd';
import { useTranslation } from 'react-i18next';
import {uuidv4} from "@/shared/utils/utils";

const ScormContext=createContext()

export const PROPRIETARY_EXTENSION = ['ppt', 'pptx', 'doc', 'docx', 'xls', 'xlsx', 'odt', 'csv'];
export const PUBLIC_EXTENSION = ['bmp', 'gif', 'jpg', 'jpeg', 'pdf', 'png', 'txt', 'webp'];
export const AUTHORIZED_FILE_EXTENSIONS = [...PUBLIC_EXTENSION, ...PROPRIETARY_EXTENSION];
export const SCORM_BACKEND_SETTINGS_KEY = {
  SCORM_FILE_NAME:'scormFileName', // la var scorm du nom de fichier
  SCORM_FILE_PATH:'scormFilePath', // la var scorm du path (id de fichier quoi)
  SCORM_FILE_UPLOAD:'scormFileToUpload', // Le fichier à upload

  // Dans les settings
  SCORM_SANDBOX_MODE_SETTINGS:'scormSandboxMode'
};
export const VERSION_KEY={
  S11:"SCORM_1_1",
  S12:"SCORM_1_2",
  S2004_V1:"SCORM_2004_V1",
  S2004_V2:"SCORM_2004_V2",
  S2004_V3:"SCORM_2004_V3",
  S2004_V4:"SCORM_2004_V4",
}

export const is2004 = (version)=>{
  return [VERSION_KEY.S2004_V1,VERSION_KEY.S2004_V2,VERSION_KEY.S2004_V3,VERSION_KEY.S2004_V4].includes(version)
}
export const is12=(version)=>{
  return [VERSION_KEY.S11,VERSION_KEY.S12].includes(version)
}


const ScormContextProvider=({
                              formationElementId:formationElementIdArg,
                              scormVersion:scormVersionArg,
                              scormEntryPoint:scormEntryPointArg,
                              scormFilename:scormFilenameArg,
                              scormFilePath:scormFilePathArg,
                              scormParsing:scormParsingArg,
                              scormSandboxMode:scormSandboxModeArg,
                              children,
                              contextDataSetter = () => {} // Setter pour avoir les données en dehors du contexte, notament avec un form. On peut faire un [data,setData]=useState // et faire : contextDataSetter={setData} // Et dans le submit form, récupérer data et remplir l'input
                            })=>{
  const { t } = useTranslation();

  const [api, contextHolder] = notification.useNotification();
  const [scormId,setScormId]=useState()
  const [width, setWidth] = useState('100%'); // value => '100%' or int
  const [scormFileToUpload,setScormFileToUpload]=useState()

  // Mode sans echec (pas encore implémenté) => Mode sandbox
  const [scormSandboxMode,setScormSandboxMode]=useState(false)

  // Variables internes
  const [formationElementId,setFormationElementId]=useState(null)
  const [scormVersion,setScormVersion]=useState(null)
  const [scormEntryPoint,setScormEntryPoint]=useState(null)
  const [scormFilename,setScormFilename]=useState(null)
  const [scormFilePath,setScormFilePath]=useState(null)
  const [scormParsing,setScormParsing]=useState(null)

  const frontId = useMemo(() => uuidv4(), [scormFilePath]);



  // Use Effect qui update/initie la tracks donnée en argument
  function isStrictBoolean(val) {
    return val === true || val === false;
  }

  useEffect(() => {
    if (typeof scormVersionArg === "string") {
      setScormVersion(scormVersionArg)
    }
  }, [scormVersionArg]);

  useEffect(() => {
    if (typeof scormEntryPointArg === "string") {
      setScormEntryPoint(scormEntryPointArg)
    }
  }, [scormVersionArg]);

  useEffect(() => {
    if (typeof scormFilenameArg === "string") {
      setScormFilename(scormFilenameArg)
    }
  }, [scormVersionArg]);

  useEffect(() => {
    if (typeof scormFilePathArg === "string") {
      setScormFilePath(scormFilePathArg)
    }
  }, [scormFilePathArg]);

  useEffect(()=>{
    if (typeof scormParsingArg==="object"){
      setScormParsing(scormParsingArg)
    }
  },[scormParsingArg])

  useEffect(()=>{
    if (typeof formationElementIdArg === 'number' && Number.isInteger(formationElementIdArg)) {
      setFormationElementId(formationElementIdArg);
    } else if (
      typeof formationElementIdArg === 'string' &&
      /^\d+$/.test(formationElementIdArg)
    ) {
      setFormationElementId(parseInt(formationElementIdArg, 10));
    }
  },[formationElementIdArg])

  useEffect(()=>{
    if (isStrictBoolean(scormSandboxMode)) {
      setScormSandboxMode(scormSandboxModeArg);
    }
  },[scormSandboxModeArg])

  /////////////////// Fonctions

  const launchApi = ({
                       type = 'info', // 'success' | 'info' | 'warning' | 'error' | 'open'
                       message = '',
                       description = '',
                       placement = 'topRight' // topLeft | topRight | bottomLeft | bottomRight
                     } = {}) => {
    // Si le type correspond à une méthode dédiée, on l’utilise.
    api.open({ type, message, description, placement });
  };

  //////////////// Memo
  ///// Value
  const value = useMemo(
    () => ({
      launchApi,
      scormId,setScormId,
      width,setWidth,
      scormFileToUpload,setScormFileToUpload,
      scormFilename,setScormFilename,
      scormVersion,setScormVersion,
      scormFilePath,setScormFilePath,
      scormEntryPoint,setScormEntryPoint,
      scormSandboxMode,setScormSandboxMode,
      frontId,
      formationElementId,
      scormParsing,
      objectiveId:scormParsing?.objective_id
    }),
    [
      scormFileToUpload,
      scormId,
      width,
      scormFilename,
      scormVersion,
      scormFilePath,
      scormEntryPoint,
      formationElementId,
      frontId,
      scormParsing,
      scormParsing?.objective_id,
      scormSandboxMode,
    ]
  );

  useEffect(() => {
    contextDataSetter(value);
  }, [value]);

  return (
    <ScormContext.Provider value={value}>
      {contextHolder}
      {children}
    </ScormContext.Provider>
  );

}


const useScormContext = () => useContext(ScormContext);

export { useScormContext , ScormContextProvider };
