import React from 'react';
import {useScormContext} from "@/shared/components/SCORM/ScormContextProvider";
import {QuestionCircleOutlined} from "@ant-design/icons";
import UploadFileSelecter from '@/shared/components/UploadFileSelecter';
import {Package, PackagePlus} from "lucide-react";
import {useTranslation} from "react-i18next";
import {Checkbox, Tooltip} from 'antd'

const UploadScorm=()=>{

  const { t } = useTranslation();
  const {scormFileToUpload,setScormFileToUpload,scormSandboxMode,setScormSandboxMode} =useScormContext()

  const UploadScormPackageFragment = () => (
    <div style={{display:"flex",flexDirection:"column",justifyContent:'center',alignItems:'center'}}>
      <PackagePlus style={{ width: '1.5em', height: '1.5em', strokeWidth: 1 }} />
      {t('ScormFE.PlaceholderUploadFileSelecter')}
    </div>
  );

  return (
    <div style={{margin: "10px"}}>
      <UploadFileSelecter
        AUTHORIZED_FILE_EXTENSIONS={'zip'}
        fileStructure={scormFileToUpload}

        // On fait un custom Setter ici, parce que UploadFile Selecter retourne pas mal de data dont on osef
        setter={(structure) => {
          setScormFileToUpload(structure?.file)
        }}
        componantHeight={75}
        componantWidth={200}
        mimeTypeDico={{"zip": "application/zip"}}
        uploadText={<UploadScormPackageFragment/>}
        useIcon={false}
        showFile={true}
        showFileIcon={<Package style={{width: '1.5em', height: '1.5em', strokeWidth: 1}}/>}
      />

      <div style={{display: 'flex', alignItems: 'center', margin: '10px'}}>
        <Checkbox
          checked={scormSandboxMode}
          onChange={() => setScormSandboxMode(prev => !prev)}
          disabled={true}
        >
          {t('ScormFE.SandboxCheckboxLabel')}
        </Checkbox>
        &nbsp;
        <Tooltip title={t('ScormFE.SandboxCheckboxHooverExplanationLabel')}>
          <QuestionCircleOutlined style={{cursor: 'pointer'}}/>
        </Tooltip>
      </div>


    </div>
  )
}

export default UploadScorm