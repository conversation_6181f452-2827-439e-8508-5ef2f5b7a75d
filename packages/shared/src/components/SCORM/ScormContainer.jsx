import React, {useEffect, useRef, useState } from 'react';
import {useScormContext} from "@/shared/components/SCORM/ScormContextProvider";
import {FILE_TYPE, getDownloadBaseUrl, getProtectedUrl} from "@/shared/services/file";
import {is12,is2004} from "@/shared/components/SCORM/ScormContextProvider";
import {Scorm12API,Scorm2004API} from 'scorm-again';
// import {Scorm12API,Scorm2004API,CrossFrameLMS,CrossFrameAPI} from 'scorm-again'; => Pour le futur
import {useMutation, useQuery} from "@apollo/client";
import {GET_ME} from "@/shared/models/user";
import {onErrorShowErrorsFunction, uuidv4} from "@/shared/utils/utils";
import {INIT_SCORM_RECORD} from "@/shared/graphql/scorm";

const ScormContainer=()=>{

  //////// Context
  const {scormVersion,scormEntryPoint,scormFilename,scormFilePath,formationElementId,frontId,scormParsing,objectiveId}=useScormContext()

  //////// Constantes
  const TOKEN_HEADER_NAME="x-token"

  ///// LOCAL FIX
  const IS_LOCAL_DEV=false

  const BASE_SETTINGS={
    autocommit: true,
    autocommitSeconds: 5,

    // Test sync
    syncOnInitialize:true,
    syncOnTerminate:true,

    // Test self report time
    selfReportSessionTime:true,
    alwaysSendTotalTime:true,

    // test beacon
    useBeaconInsteadOfFetch:'never',

    // Render field
    renderCommonCommitFields:true, // affiche les fields courant à la racine

    // AutoProgress
    autoProgress:true,


    asyncCommit: true,
    lmsCommitUrl:IS_LOCAL_DEV ? `/proxyScorm/scorm/commit` : `${getDownloadBaseUrl(FILE_TYPE.SCORM)}commit`,
    dataCommitFormat: 'json',
    sendFullCommit:true,
    logLevel: 1,
    //xhrWithCredentials:true,

    debug:true
  }

  const IS_CROSS=false //true //false
  const token=localStorage.getItem('token')
  const iref=useRef(null)

  //////// States
  const [me,setMe]=useState(null)
  const [scormUrl,setScormUrl]=useState(null)
  const [isApiReady,setIsApiReady]=useState(false)


  //////// Queries apollo
  useQuery(GET_ME, {
    fetchPolicy: 'cache-and-network',
    onCompleted:(data)=>{setMe(data.me)},
    onError: (errors) => {onErrorShowErrorsFunction(errors);},
  });

  //////// Mutation
  const [initScormRecord]=useMutation(INIT_SCORM_RECORD,{
    onCompleted:(data)=>{
      if (data?.initScormRecord){}
    },
    //onError: (error) => onErrorShowErrorsFunction(error)
  })


  //////// Pre-load SCORM function

  // Init pour les objectives Id
  function preloadObjectives(flat,objectiveNames) {

    objectiveNames.forEach((name, id) => {
      flat[`cmi.objectives.${id}.id`] = name;
      flat[`cmi.objectives.${id}.score.min`] = "0";
      flat[`cmi.objectives.${id}.score.max`] = "100";
      flat[`cmi.objectives.${id}.score.raw`] = "0";

      if (is2004(scormVersion)) {
        flat[`cmi.objectives.${id}.progress_measure`] = "0";
        flat[`cmi.objectives.${id}.success_status`] = "unknown";
        flat[`cmi.objectives.${id}.completion_status`] = "not attempted";
      } else {
        flat[`cmi.objectives.${id}.status`] = "not attempted";
      }
    });
  }

  function preloadInteractions(flat,interactionNames){
    interactionNames.forEach((name,id)=>{
      flat[`cmi.interactions.${id}.id`]=id
    });
  }

  //////// UseEffects

  // Init de l'API
  useEffect(()=>{
    if (scormVersion && me && objectiveId && (typeof scormFilePath === "string" && scormFilePath!=="") && (typeof scormEntryPoint==="string"  && scormEntryPoint!=="")){

      // Settings
      const settings={
        ...BASE_SETTINGS,

        xhrHeaders:{
          [TOKEN_HEADER_NAME]:token ,
          userId:me.id,
          frontId,
          formationElementId,
          scormfilepath:scormFilePath,
          scormentrypoint:scormEntryPoint
        }
      }

      ////////// Declaration de l'API + cross
      const api = is12(scormVersion) ? new Scorm12API(settings) : new Scorm2004API(settings)


      api.on(is12(scormVersion) ? "LMSInitialize":"Initialize",()=>{
        ////////// Setup des hooks et tout
        initScormRecord({
          variables:{
            initScormRecord:{
              frontId,
              formationElementId:formationElementId,
              userId:me.id,
              scormFilePath,
              scormEntryPoint,
            }
          }
        })
      })

      api.on(is12(scormVersion)? "LMSSetValue.cmi.*":"SetValue.cmi.*",(a,b,c)=>{
        //console.log("set Cmi Value ,    a :",a,"    b:",b,"    c:",c)
      })

      ///// Init de l'api => modif du dico flat d'init
      const flat={      }

      // Init des scoItemsIds
      preloadObjectives(flat,objectiveId)

      // init des interactions
      // preloadInteractions(flat,[0, 1,])  => normalement pas besoin de mettre ça, c'est pour le package SCORM qui est buggé

      api.loadFromFlattenedJSON(flat)


      ///////////// Mise à disposition dans la page
      if (is12(scormVersion)){
        window.API=api
      } else {
        window.API_1484_11=api
      }

      setIsApiReady(true)
    }
  },[scormVersion,me,objectiveId,scormFilePath,scormEntryPoint])

  // Init de l'url sécurisée
  useEffect(()=>{
    if ((typeof scormEntryPoint === "string" && scormEntryPoint!=="") && (typeof scormFilePath === "string" && scormFilePath!=="")){
      // Fonction qui récupère un token de connection au package
      const asyncSetup = async (url) => {
        let protectedUrl
        if (IS_LOCAL_DEV){
          protectedUrl = await getProtectedUrl(FILE_TYPE.SCORM, url);
          protectedUrl = protectedUrl.replace('http://localhost:8000','/proxyScorm')
        }else {
          protectedUrl = await getProtectedUrl(FILE_TYPE.SCORM, url);
        }
        setScormUrl(protectedUrl);
      }

      asyncSetup(`${scormFilePath}/${scormEntryPoint}`)
    }
  },[scormEntryPoint,scormFilePath])



  return (
    <>
      { ((typeof(scormUrl) === "string" && scormUrl!=="") && isApiReady) && (
        <iframe
          ref={iref}
          {...(IS_CROSS ? {sandbox : 'allow-scripts allow-same-origin'}:{})}
          src={scormUrl}
          style={{width: '100%', height: '100vh'}}
        />
      )}
    </>
  )
}

export default ScormContainer