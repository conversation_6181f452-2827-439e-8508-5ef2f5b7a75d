import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { ExoUserLight } from '@/shared/components/User/ExoUserLight.jsx';
import { QUERY_USER_LOGS } from '@/shared/graphql/user/index.js';
import { buildThreadLinkFromComment } from '@/shared/services/commentaires.js';
import { LinkOutlined } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { Card, Checkbox, DatePicker, Select, Space, Tag, Timeline } from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'umi';
import { DisplayWatermarkLog, DisplayWatermarkLogPicture } from '@/shared/services/watermark.js';
import { UserProfileCard } from '@/shared/components/User/UserProfileCard/UserProfileCard';
import { SpringListItemTransition } from '@/shared/assets/transitions/SpringListItemTransition';

export const LogOpererationsMapper = {
  // Login/logout
  UserLogin: 'Connexion',
  UserLoginFail: 'Tentative de connexion échouée',
  UserLogout: 'Déconnexion',

  // Courses
  SeenClass: 'Ouverture du cours',
  DownloadCours: 'Cours téléchargé',
  CreateClass: 'Créé le cours',
  EditClass: 'Modifie le cours',
  DeleteClass: 'Supprime le cours',
  EditClassWatermark: 'Modifie les options de filigrane',
  EditClassWatermarkPicture: "Modifie l'image du filigrane",

  CreateMcq: 'Série de QCM créé',
  UpdateMcq: 'Série de QCM mise à jour',
  DeleteMcq: 'Série de QCM créé',
  PublishMcq: 'Série de QCM publiée',
  UnPublishMcq: 'Série de QCM dé-publiée',
  ModifyAuthor: 'Auteur Modifié',

  CreateQuestion: 'Créé une question de QCM',
  UpdateQuestion: 'Met à jour une question de QCM',
  DeleteQuestion: 'Supprime une question de QCM',
  ChangeScale:"Modifie le barème",

  CreateQuestionAnswer: 'Créé un item de QCM',
  UpdateQuestionAnswer: 'Met à jour un item de QCM',
  DeleteQuestionAnswer: 'Supprime un item de QCM',

  FinishMcq: 'Série de QCM terminée',

  ReportContent: 'Signale un contenu',

  //USER
  CreateUser: 'Utilisateur créé',
  UpdateUser: 'Utilisateur modifié',
  DeleteUser: 'Utilisateur supprimé',

  AddGroupPermission: "Ajout de permissions d'un groupe",
  RemoveGroupPermission: "Suppression de permissions d'un groupe",

  //ROLES
  ChangeUserRoles: "Change le rôle d'un utilisateur",
  ChangeUserGroups: "Change le groupe d'un utilisateur",

  AddUserGroup: 'Groupe ajouté à un utilisateur',
  RemoveUserGroup: 'Groupe supprimé à un utilisateur',

  // Posts
  post_create: 'Poste un commentaire',
  post_update: 'Modifie un post',
  post_delete: 'Supprime un post',

  BeforePayment: 'Choisit un forfait',
  CreatedAfterPayment: 'Inscription validée',
  UpdatedAfterPayment: 'Ré-inscription validée',

  AddGroupToDiffusionDate: 'Ajoute un groupe à une date',
  DeleteGroupFromDiffusionDate: "Supprime un groupe d'une date",

  CreateDiffusionDate: 'Ajoute une date',
  UpdateDiffusionDate: 'Mise à jour de date',
  DeleteDiffusionDate: 'Supprime une date',


  // Scales
  CreateScale:"Création d'un barème",
  UpdateScale:"Update d'un barème",
  DeleteScale:"Suppression d'un barème",
  LinkUeToScale:"Ajout d'une Ue au barème",
  RemoveUeFromScale:"Retrait d'une Ue du barème",
  RemoveAllUeFromScale:"Retrait de toutes les Ue du barème",
  LinkExerciseToScale:"Ajout d'un exercice au barème",
  RemoveExerciseFromScale:"Retrait d'un exercice du barème",
  RemoveIsDefaultFromScale:"Retrait de cette scale comme 'par défaut'",
  AddIsDefaultToScale:"Définition de cette scale comme 'par défaut'",
};

const DEFAULT_DATE_BEGIN = dayjs().subtract(2, 'day').toDate();
const DEFAULT_DATE_END = dayjs().add(1, 'day').toDate();

export default function UserLogs({
  userId = null,
  coursId = null,
  scaleId=null,
  questionId = null,
  qcmId = null,
  withTypeSelector = true,
  showUser = false,
  canShowAll = false,
  hideTitle = false
}) {
  const { t } = useTranslation();

  const [filter, setFilter] = useState({
    userId,
    coursId,
    questionId,
    qcmId,
    scaleId,
    dateBegin: canShowAll ? null : DEFAULT_DATE_BEGIN,
    dateEnd: canShowAll ? null : DEFAULT_DATE_END,
    logOperation: null
    // orderBy: 'ASC',
  });

  const { data, loading, error, refetch } = useQuery(QUERY_USER_LOGS, {
    variables: {
      filter
    },
    fetchPolicy: 'cache-and-network'
  });

  const logs = data?.getLogsFor;

  const getOperationName = (log) => {
    const operationKey = log?.logOperation;
    if (LogOpererationsMapper[operationKey]) {
      if (operationKey === 'post_update') {
        if (log?.logData?.isAnswer) {
          return 'Discussion démarrée';
        }
        if (log?.logData?.setResolved) {
          return 'A marqué discussion comme résolue';
        }
        return 'Commentaire posté';
      }
      return LogOpererationsMapper[operationKey];
    }
    return log?.logOperation;
  };

  const getLogOpererationsGroupTextAndColor = (l) => {
    if (
      [
        'SeenClass',
        'DownloadCours',
        'CreateClass',
        'EditClass',
        'DeleteClass',
        'EditClassWatermark',
        'EditClassWatermarkPicture'
      ].includes(l.logOperation)
    ) {
      return { type: 'Cours', color: 'blue' };
    }
    if (
      ['CreateMcq', 'UpdateMcq', 'DeleteMcq', 'PublishMcq', 'FinishMcq'].includes(l.logOperation)
    ) {
      return { type: 'QCM', color: 'green' };
    }
    if(
      ['CreateScale','UpdateScale','DeleteScale',"LinkUeToScale","RemoveUeFromScale","RemoveAllUeFromScale","LinkExerciseToScale","RemoveExerciseFromScale","RemoveIsDefaultFromScale","AddIsDefaultToScale"].includes(l.logOperation)
    ){
      return {type:'Scale',color:"orange"}
    }
    if (['CreateQuestion', 'UpdateQuestion', 'DeleteQuestion','ChangeScale'].includes(l.logOperation)) {
      return { type: 'Question', color: 'green' };
    }
    if (['CreateUser', 'UpdateUser', 'DeleteUser'].includes(l.logOperation)) {
      return { type: 'User', color: 'geekblue' };
    }
    if (['post_create', 'post_update', 'post_delete'].includes(l.logOperation)) {
      return { type: 'Post', color: 'purple' };
    }
    if (['UserLogin', 'UserLoginFail'].includes(l.logOperation)) {
      return { type: 'User', color: 'red' };
    }
    if (['UserLogout'].includes(l.logOperation)) {
      return { type: 'User', color: 'red' };
    }

    return { type: l.logOperation, color: 'default' };
  };

  const getObjectLink = (log, text) => {
    switch (text) {
      case 'Cours':
        return `/cours/${log?.coursId}`;
      case 'QCM':
        return `/qcm/${log?.qcmId}`;
      case 'Question':
        return `/admin-series/edit/${log?.question?.id_qcm}`;
      case 'Post':
        return buildThreadLinkFromComment(log?.post);
      default:
        break;
    }
    return null;
  };

  const formatDate = (date) => dayjs(date).format('DD/MM/YYYY HH:mm');

  const getOperationDetails = (log) => {
    const DATES_DIFF_OPERATIONS = [
      'AddGroupToDiffusionDate',
      'DeleteGroupFromDiffusionDate',
      'CreateDiffusionDate',
      'UpdateDiffusionDate',
      'DeleteDiffusionDate'
    ];
    if (DATES_DIFF_OPERATIONS.includes(log?.logOperation)) {
      let text = '';
      const dateBegin = formatDate(log?.logData?.dateDiff?.date) || '';
      const dateEnd =
        (log?.logData?.dateDiff?.dateEnd && formatDate(log?.logData?.dateDiff?.dateEnd)) || '';
      switch (log?.logOperation) {
        case 'AddGroupToDiffusionDate':
          text = `Ajout du groupe ${log?.logData?.group?.name} de la date ${dateBegin} à ${dateEnd}`;
          break;
        case 'DeleteGroupFromDiffusionDate':
          text = `Retrait du groupe ${log?.logData?.group?.name} de la date ${dateBegin} à ${dateEnd}`;
          break;
        case 'CreateDiffusionDate':
          text = `Création d'une date de diffusion`;
          break;
        case 'UpdateDiffusionDate':
          text = `Mise à jour de la date ${dateBegin} à ${dateEnd}`;
          break;
        case 'DeleteDiffusionDate':
          text = `Suppression de la date ${dateBegin} à ${dateEnd}`;
          break;
        default:
          break;
      }
      return text;
    }

    if (log?.cours) {
      if (log?.logOperation === 'EditClassWatermark') {
        return <DisplayWatermarkLog log={log} />;
      } else if (log?.logOperation === 'EditClassWatermarkPicture') {
        return <DisplayWatermarkLogPicture log={log} />;
      }

      return (
        <>
          {log?.cours?.name} {log?.cours?.text || ''}
          {log?.logData?.text && (
            <>
              <br />- {log?.logData?.text || ''}
            </>
          )}
        </>
      );
    }
    if (log?.qcm) {
      if (['ModifyAuthor'].includes(log?.logOperation)) {
        return `${"Changement d'auteur :"} ${log.logData?.previousAuthorUsername} -> ${log.logData?.newAuthorUsername}`;
      } else {
        return `${log?.qcm?.UE?.name} / ${log?.qcm?.titre}`;
      }
    }
    if (log?.question) {
      if (log?.question && log?.logOperation==="ChangeScale"){
        const data=log?.logData
        const oldName=data?.oldMcqScaleName
        const oldId=data?.oldMcqScaleId
        const newName=data?.newMcqScaleName
        const newId=data?.newMcqScaleId
        return <>{oldName}({oldId}) -> {newName}({newId})</>
      }
      return (
        <>
          {log?.logData?.text || ''} (ID {log?.question?.id_question})
        </>
      );
    }
    if (log?.post) {
      if (log?.post?.courId) {
        return ' (cours) ';
      }
      if (log?.post?.forumId) {
        return ' (forum) ';
      }
      if (log?.post?.qcmIdQcm) {
        return ' (série de qcm) ';
      }
      if (log?.post?.answerId) {
        return ' (item de qcm) ';
      }
    }
    if (['CreatedAfterPayment', 'UpdatedAfterPayment'].includes(log?.logOperation)) {
      return `Forfait(s) : ${log?.logData?.text}`;
    }
    if (['BeforePayment'].includes(log?.logOperation)) {
      return `Forfait(s) : ${log?.logData?.forfaitsText}`;
    }
    if (['AddUserGroup', 'RemoveUserGroup'].includes(log?.logOperation)) {
      return `${log?.user?.username} : ${log?.logData?.text}`;
    }
    if (['AddGroupPermission', 'RemoveGroupPermission'].includes(log?.logOperation)) {
      return `${log?.logData?.text}`;
    }
    if (['CreateUser', 'UpdateUser', 'DeleteUser'].includes(log?.logOperation)) {
      return `${log?.logData?.text}`;
    }
    if (
      ['EditClass', 'CreateClass', 'DeleteClass', 'EditClassWatermark'].includes(log?.logOperation)
    ) {
      return `${log?.logData?.text}`;
    }

    if (log?.logData?.text) {
      return log?.logData?.text;
    }
    return '';
  };

  const [showAll, setShowAll] = useState(canShowAll);

  return (
    <Card>
      <div style={{ textAlign: 'center' }}>
        {canShowAll && (
          <>
            <Checkbox
              checked={showAll}
              size="small"
              onChange={(e) => {
                setShowAll(!showAll);
                if (e.target.checked) {
                  setFilter({
                    ...filter,
                    dateBegin: null,
                    dateEnd: null
                  });
                } else {
                  setFilter({
                    ...filter,
                    dateBegin: DEFAULT_DATE_BEGIN,
                    dateEnd: DEFAULT_DATE_END
                  });
                }
              }}
            >
              {t('ShowAll')}
            </Checkbox>
            <br />
          </>
        )}

        {!showAll && (
          <Space>
            <b>{t('period')}</b>
            <DatePicker
              value={dayjs(filter?.dateBegin)}
              //format="DD/MM/YYYY"
              style={{ display: 'inline-block' }}
              size="middle"
              placeholder=""
              onChange={async (value) => {
                setFilter({ ...filter, dateBegin: value.toDate() });
              }}
            />
            au
            <DatePicker
              value={dayjs(filter?.dateEnd)}
              //format="DD/MM/YYYY"
              style={{ display: 'inline-block' }}
              size="middle"
              placeholder=""
              onChange={async (value) => {
                setFilter({ ...filter, dateEnd: value.toDate() });
              }}
            />
          </Space>
        )}

        <br />

        {withTypeSelector && (
          <div>
            <Select
              style={{ minWidth: '400px', marginTop: 12 }}
              onChange={(value, item) => {
                if (value === null) {
                  setFilter({ ...filter, logOperation: null });
                } else {
                  setFilter({ ...filter, logOperation: item.key });
                }
              }}
              defaultValue={null}
              value={filter?.logOperation ? LogOpererationsMapper[filter?.logOperation] : null}
            >
              <Select.Option value={null}>{t('All')}</Select.Option>
              {Object.keys(LogOpererationsMapper).map((key) => (
                <Select.Option key={key} value={LogOpererationsMapper[key]}>
                  {LogOpererationsMapper[key]}
                </Select.Option>
              ))}
            </Select>
          </div>
        )}
      </div>

      <br />
      <br />
      {loading ? (
        <SpinnerCentered />
      ) : (
        <Timeline mode="left">
          {logs &&
            logs.map((log, index) => {
              const { type, color } = getLogOpererationsGroupTextAndColor(log);
              const ip = log?.ip || log?.logData?.ip;
              const device = log?.deviceFromUserAgent;
              const link = getObjectLink(log, type);
              return (
                <Timeline.Item
                  key={log?.id}
                  label={dayjs(log?.createdAt).format('DD/MM/YYYY HH:mm')}
                >
                  <SpringListItemTransition
                    uniqueId={`log-details-${log?.id}`}
                    index={index}
                    delayMultiplier={0.2}
                  >
                    <Tag color={color}>{getOperationName(log)}</Tag>
                    {showUser && (
                      <div>
                        <UserProfileCard userId={log?.userId} username={log?.user?.username}>
                          <ExoUserLight id={log?.userId} />
                        </UserProfileCard>
                      </div>
                    )}
                    <div>
                      {getOperationDetails(log)}{' '}
                      {link && (
                        <>
                          <Link to={link}>
                            <LinkOutlined />
                          </Link>
                        </>
                      )}
                    </div>
                    {ip && (
                      <div>
                        {device && (
                          <>
                            {device?.device?.type === 'desktop' && '🌐'}
                            {['smartphone', 'phablet'].includes(device?.device?.type) && '📱'}
                            {device?.device?.type === 'tablet' && '📱'}
                            &nbsp;
                            {device?.os?.name} ({device?.client?.family})
                          </>
                        )}
                        &nbsp;({ip})
                      </div>
                    )}
                  </SpringListItemTransition>
                </Timeline.Item>
              );
            })}
        </Timeline>
      )}
    </Card>
  );
}
