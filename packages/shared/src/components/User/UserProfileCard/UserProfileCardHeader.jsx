import { <PERSON><PERSON>, <PERSON>ge, Button, Tooltip } from 'antd';
import { EditFilled, UserOutlined } from '@ant-design/icons';
import { mapRoleToShortRoleName } from '@/shared/utils/authority';
import { getAvatarSrc } from '@/shared/services/user';
import React, { useContext } from 'react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { useQuery } from '@apollo/client';
import { QUERY_COMPLETED_CHALLENGES_TROPHIES } from '@/shared/graphql/challenges';
import ChallengeImage from '@/shared/components/Challenge/ChallengeImage';
import { tr } from '@/shared/services/translate.js';
import Link from 'umi/link';
import { router } from 'umi';

export function UserProfileCardHeader({
  userId,
  role,
  avatar,
  username,
  firstName,
  lastName,
  avatarSize,
  canSeeHiddenInfo,
  canEditProfile
}) {
  const { appearance } = useContext(GlobalContext);
  const { data: completedChallengesData, loading: loadingCompletedChallenges } = useQuery(
    QUERY_COMPLETED_CHALLENGES_TROPHIES,
    {
      fetchPolicy: 'no-cache',
      variables: {
        filter: {
          userId: userId
        }
      }
    }
  );

  const completedChallenges = completedChallengesData?.completedChallenges || [];
  const primaryColor = appearance?.primaryColor;

  return (
    <>
      <div style={{ height: avatarSize, backgroundColor: primaryColor }}>
        {canEditProfile && (
          <span style={{ position: 'relative', float: 'right', top: 8, right: 8 }}>
            <Button
              onClick={() => {
                router.push(`/admin/users/edit/${userId}`);
              }}
              shape="circle"
              ghost
              icon={<EditFilled />}
            />
          </span>
        )}
      </div>

      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-end',
          marginTop: -avatarSize / 2,
          paddingLeft: 12,
          paddingRight: 12
        }}
      >
        <Badge
          className="profile-card-avatar-wrapper"
          color="volcano"
          count={mapRoleToShortRoleName(role)}
          offset={[-avatarSize / 2, avatarSize]}
          style={{ zIndex: 2 }}
        >
          <Avatar
            src={avatar ? getAvatarSrc(avatar) : undefined}
            icon={avatar ? undefined : <UserOutlined />}
            size={avatarSize}
            style={{ zIndex: 1 }}
          />
          <Link to={`/profile/${userId}`}>
            <div
              className="profile-card-avatar-overlay"
              style={{
                position: 'absolute',
                right: 0,
                top: 0,
                cursor: 'pointer',
                color: 'white',
                width: avatarSize - 4,
                height: avatarSize - 4,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                background: '#00000060',
                fontWeight: 800,
                fontSize: 'larger',
                textAlign: 'center',
                border: '2px solid',
                zIndex: 1
              }}
            >
              Voir profil
            </div>
          </Link>
        </Badge>
        {completedChallenges.length > 0 && (
          <div
            style={{
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'flex-end',
              gap: 8,
              maxWidth: 150,
              background: '#00000010',
              borderRadius: 7,
              padding: '4px 8px'
            }}
          >
            {completedChallenges?.slice(-4).map((trophy) => (
              <>
                <Tooltip title={trophy[tr('name')]} key={trophy.id}>
                  <div style={{ display: 'flex' }}>
                    <ChallengeImage image={trophy.image} size={28} />
                  </div>
                </Tooltip>
              </>
            ))}
          </div>
        )}
      </div>

      <div
        style={{
          background:
            'linear-gradient(90deg, rgba(255,255,255,0.5018601190476191) 0%, rgba(255,255,255,0.4990589985994398) 75%, rgba(255,255,255,0) 100%)',
          width: 'fit-content',
          height: avatarSize / 2 + 10,
          position: 'relative',
          top: -(avatarSize + 10),
          paddingLeft: 1.25 * avatarSize,
          paddingRight: 30,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center'
        }}
      >
        <span style={{ fontSize: '1.25em', lineHeight: '1.25em', fontWeight: 800 }}>
          {username}
        </span>

        {canSeeHiddenInfo && (
          <span
            style={{
              fontSize: '0.7em',
              lineHeight: '0.7em',
              fontWeight: '400'
            }}
          >{`${firstName} ${lastName}`}</span>
        )}
      </div>
    </>
  );
}
