import { CreateEditUserPropertyFolderModal } from '@/shared/components/User/UserProperties/CreateEditUserPropertyFolderModal.jsx';
import {
  CREATE_OR_UPDATE_USER_PROPERTY_DATA,
  GET_USER_PROPERTIES_FOLDER,
  UPDATE_USER_PROPERTY_DATA_BY_ID
} from '@/shared/graphql/user/user-properties.js';
import { ModalType } from '@/shared/pages/exam/admin/modals/CreateEditExamSessionModal.jsx';
import FormationElement from '@/shared/pages/formations/components/FormationElement.jsx';
import { CreateEditFormationElementModal } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal.jsx';
import { FormationContextProvider } from '@/shared/pages/formations/context/FormationContext.jsx';
import { downloadFile, FILE_TYPE } from '@/shared/services/file.js';
import { ELEMENTS_TYPE, mapFormFieldsToCustomFields } from '@/shared/services/formations.js';
import { tr } from '@/shared/services/translate.js';
import { isAdmin, isTuteur } from '@/shared/utils/authority.js';
import { IS_DEV } from '@/shared/utils/utils.js';
import {
  DownloadOutlined,
  EditOutlined,
  FolderOpenTwoTone,
  PlusCircleTwoTone,
  PlusOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Collapse, Form, message, Space, Table, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const FormResultValue = ({
  handleMouseOut,
  handleMouseOver,
  element,
  propertyValue,
  propertyValues,
  canEdit,
  isEditing,
  setIsEditing,
  isHovering
}) => {
  const { t } = useTranslation();
  return (
    <div onMouseOut={handleMouseOut} onMouseOver={handleMouseOver}>
      {[
        ELEMENTS_TYPE.SHORT_ANSWER,
        ELEMENTS_TYPE.LONG_ANSWER,
        ELEMENTS_TYPE.INTEGER_NUMBER,
        ELEMENTS_TYPE.FLOAT_NUMBER
      ].includes(element?.type) && <Tag>{propertyValue}</Tag>}

      {[ELEMENTS_TYPE.DATE_PICKER].includes(element?.type) && (
        <>{dayjs(propertyValue).format('DD/MM/YYYY')}</>
      )}
      {[ELEMENTS_TYPE.DATE_AND_TIME_PICKER].includes(element?.type) && (
        <>
          <Tag>{dayjs(propertyValue).format('DD/MM/YYYY HH:mm')}</Tag>
        </>
      )}

      {[ELEMENTS_TYPE.SINGLE_SELECT].includes(element?.type) && (
        <>
          <Tag color="blue">{propertyValue}</Tag>
        </>
      )}

      {[ELEMENTS_TYPE.MULTIPLE_SELECT].includes(element?.type) && (
        <div>
          {propertyValues?.map((v, key) => (
            <Space key={key}>
              <Tag color="blue">{v}</Tag>
            </Space>
          ))}
          {(!propertyValues || propertyValues?.length === 0) && <Tag> </Tag>}
        </div>
      )}

      {[ELEMENTS_TYPE.FILE_IMPORT].includes(element?.type) && (
        <Space>
          <Button
            icon={<DownloadOutlined />}
            onClick={async () => {
              if (propertyValue) {
                await downloadFile(FILE_TYPE.FILE, propertyValue);
              } else {
                await downloadFile(FILE_TYPE.FILE, element?.userPropertyValue?.value);
              }
            }}
          >
            {t('general.Download')}
          </Button>
          <span>({element?.userPropertyValue?.value || propertyValue || 'fichier manquant'})</span>
        </Space>
      )}

      {canEdit && (
        <span style={{ marginLeft: '7px', display: isHovering ? 'inline' : 'none' }}>
          <Button
            icon={<EditOutlined />}
            size="small"
            type="primary"
            onClick={() => setIsEditing(!isEditing)}
          />
        </span>
      )}
    </div>
  );
};

/**
 * EditableFormElementHandler
 *
 * Display a form element result in a user property folder or in a form result.
 *
 *
 * @param userId
 * @param element
 * @param canEdit
 * @param refetch
 * @param userPropertyValueProp
 * @param userPropertyValuesProp
 * @param autoCreateOrUpdateGlobalUserProperty
 * @param id
 * @returns {JSX.Element}
 * @constructor
 */
export const EditableFormElementHandler = ({
  userId,
  element,
  canEdit,
  refetch,
  userPropertyValueProp,
  userPropertyValuesProp,
  autoCreateOrUpdateGlobalUserProperty = true,
  id
}) => {
  const { t } = useTranslation();

  const [updateOrCreate, { loading: loadingMut }] = useMutation(
    autoCreateOrUpdateGlobalUserProperty
      ? CREATE_OR_UPDATE_USER_PROPERTY_DATA
      : UPDATE_USER_PROPERTY_DATA_BY_ID
  );
  const [isEditing, setIsEditing] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const handleMouseOver = useCallback(() => {
    if (canEdit) {
      setIsHovering(true);
    }
  }, [canEdit]);

  const handleMouseOut = useCallback(() => {
    if (canEdit) {
      setIsHovering(false);
    }
  }, [canEdit]);

  const [propertyValue, setPropertyValue] = useState(userPropertyValueProp || '');
  const [propertyValues, setPropertyValues] = useState(userPropertyValuesProp || []);

  const [form] = Form.useForm();
  const [customFiles, setCustomFiles] = useState();

  useEffect(() => {
    if (element) {
      setPropertyValue(userPropertyValueProp || '');
      setPropertyValues(userPropertyValuesProp || []);
    }
  }, [element]);

  // SEND FORM
  const handleSaveValue = async (formInput) => {
    const formItems = JSON.parse(JSON.stringify(formInput));
    if (IS_DEV) {
      console.log(formItems?.customFields);
    }
    const customFields = mapFormFieldsToCustomFields(formItems.customFields, customFiles);

    for (let customField of customFields) {
      let customFieldWithUser = {
        ...customField,
        userId
      };
      if (autoCreateOrUpdateGlobalUserProperty) {
        await updateOrCreate({
          variables: {
            input: customFieldWithUser
          }
        });
      } else {
        if (id) {
          await updateOrCreate({
            variables: {
              id,
              input: customFieldWithUser
            }
          });
        } else {
          const errStr = 'id requis pour mise à jour';
          console.error(errStr);
          message.error(errStr);
        }
      }
    }

    delete formItems.customFields;

    // Refetch
    // Disable editing mode
    setIsEditing(false);
    refetch();
  };

  const displayValue = (
    <FormResultValue
      handleMouseOut={handleMouseOut}
      handleMouseOver={handleMouseOver}
      element={element}
      propertyValue={propertyValue}
      propertyValues={propertyValues}
      canEdit={canEdit}
      isEditing={isEditing}
      setIsEditing={setIsEditing}
      isHovering={isHovering}
    />
  );

  const getInitialValues = () => {
    let valueToShow = propertyValue || propertyValues; // Garder cet ordre pour que les valeurs soient bien mises à jour (!!'' donne false, !![] donne true)
    // Si c'est une date on doit utiliser dayjs
    const { DATE_PICKER, AVATAR_SELECT, FILE_IMPORT, DATE_AND_TIME_PICKER } = ELEMENTS_TYPE;
    if ([DATE_PICKER, DATE_AND_TIME_PICKER].includes(element?.type)) {
      if (valueToShow) {
        valueToShow = dayjs(valueToShow);
      }
    }
    // Pour fichiers on met rien
    if ([FILE_IMPORT, AVATAR_SELECT].includes(element?.type)) {
      valueToShow = undefined;
    }
    return {
      customFields: {
        [`${element?.id}`]: valueToShow
      }
    };
  };

  const editingValue = (
    <FormationContextProvider>
      <Form
        layout="vertical"
        onFinish={handleSaveValue}
        form={form}
        initialValues={getInitialValues()}
      >
        <FormationElement
          element={element}
          input
          customFiles={customFiles}
          setCustomFiles={setCustomFiles}
        />

        <Space>
          <Button size="small" onClick={() => setIsEditing(!isEditing)}>
            {t('Cancel')}
          </Button>

          <Button size="small" type="primary" onClick={() => form.submit()} loading={loadingMut}>
            {t('general.save')}
          </Button>
        </Space>
      </Form>
    </FormationContextProvider>
  );

  return <div>{isEditing ? editingValue : displayValue}</div>;
};

export const UserPropertyTable = ({ elements, userId, refetch, canEdit, userPropertyFolderId }) => {
  const { t } = useTranslation();

  const [updateUserPropertyData, { loading: loadingMut }] = useMutation(
    CREATE_OR_UPDATE_USER_PROPERTY_DATA
  );
  const [isEditing, setIsEditing] = useState(false);

  /* Elements related */
  const [createVisible, setCreateVisible] = useState(false);
  const [position, setPosition] = useState(null);

  const columns = [
    {
      dataIndex: tr('name'),
      key: tr('name'),
      width: '30%',
      render: (text, record) => <div style={{ fontWeight: 500 }}>{record?.[tr('name')]}</div>
    },
    {
      dataIndex: 'value',
      key: 'value',
      width: '70%',
      render: (text, record) => {
        return (
          <>
            <EditableFormElementHandler
              userId={userId}
              element={record}
              canEdit={canEdit}
              refetch={refetch}
              userPropertyValueProp={record?.userPropertyValue?.value}
              userPropertyValuesProp={record?.userPropertyValue?.values}
            />
          </>
        );
      }
    }
  ];

  const elementCreationComponents = (
    <>
      {createVisible && (
        <div
          style={{
            border: '1px dashed #b5b5b5',
            borderRadius: '11px',
            margin: 5,
            marginBottom: '15px'
          }}
        >
          <div style={{ margin: '15px' }}>
            <CreateEditFormationElementModal
              isModalVisible={createVisible}
              modalType="CREATE"
              userPropertyFolderId={userPropertyFolderId}
              position={position}
              closeModalHandler={() => {
                setCreateVisible(false);
                refetch();
              }}
              elementsTypesToShow={{
                [ELEMENTS_TYPE.TITLE]: false,
                [ELEMENTS_TYPE.IMAGE]: false,
                [ELEMENTS_TYPE.MCQ]: false,
                [ELEMENTS_TYPE.LINK]: false,
                [ELEMENTS_TYPE.HTML]: false,
                [ELEMENTS_TYPE.COURS]: false,
                [ELEMENTS_TYPE.FILE]: false,
                [ELEMENTS_TYPE.RICH_TEXT]: false,
                [ELEMENTS_TYPE.VIDEO]: false, // Obsolète, remplacé par vidstack video et audio
                [ELEMENTS_TYPE.VIDSTACK_VIDEO]: false,
                [ELEMENTS_TYPE.VIDSTACK_AUDIO]: false,
                [ELEMENTS_TYPE.DIAPO]:false,
                [ELEMENTS_TYPE.CALLOUT]: false,
                [ELEMENTS_TYPE.SCORM]:false,
                // INPUTS
                [ELEMENTS_TYPE.SHORT_ANSWER]: true,
                [ELEMENTS_TYPE.LONG_ANSWER]: true,
                [ELEMENTS_TYPE.SINGLE_SELECT]: true,
                [ELEMENTS_TYPE.MULTIPLE_SELECT]: true,
                [ELEMENTS_TYPE.INTEGER_NUMBER]: true,
                [ELEMENTS_TYPE.DATE_PICKER]: true,
                [ELEMENTS_TYPE.DATE_AND_TIME_PICKER]: true,
                [ELEMENTS_TYPE.FILE_IMPORT]: true
              }}
            />
          </div>
        </div>
      )}
    </>
  );

  const renderFormationElementCreation = (elementPosition = null) => (
    <>
      {elementPosition === position && elementCreationComponents}
      {!createVisible && (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
          <div style={{ width: '90%' }}>
            <Button
              style={{ marginTop: 10, minHeight: '50px' }}
              type="dashed"
              block
              icon={<PlusCircleTwoTone />}
              onClick={() => {
                setPosition(elementPosition);
                setCreateVisible(true);
              }}
            >
              {t('AddElementHere')}
            </Button>
          </div>
        </div>
      )}
    </>
  );

  return (
    <div>
      <Table dataSource={elements} columns={columns} rowKey="id" pagination={false} />

      {renderFormationElementCreation()}
    </div>
  );
};

export const EditButtonFolder = ({ refetch, folder }) => {
  const [editModalVisible, setEditModalVisible] = useState(false);
  return (
    <>
      <SettingOutlined
        onClick={(event) => {
          setEditModalVisible(true);
          // don't want click extra trigger collapse
          event.stopPropagation();
        }}
      />
      {editModalVisible && (
        <CreateEditUserPropertyFolderModal
          modalVisible={editModalVisible}
          folder={folder}
          modalType={ModalType.UPDATE}
          closeModalHandler={() => {
            setEditModalVisible(false);
            refetch();
          }}
        />
      )}
    </>
  );
};

export default function (props) {
  const { userId = null } = props;
  const { t, i18n } = useTranslation();
  const canEdit = isTuteur() || isAdmin();

  const {
    data: dataFolders,
    loading: loadingUserFolders,
    refetch
  } = useQuery(GET_USER_PROPERTIES_FOLDER, {
    variables: {
      userId
    },
    fetchPolicy: 'cache-and-network'
  });
  const folders = dataFolders?.userPropertiesFolders;
  const [createModalVisible, setCreateModalVisible] = useState(false);

  const onAddClick = () => {
    setCreateModalVisible(true);
  };

  return (
    <React.Fragment>
      <Collapse size="small">
        {folders?.map((folder) => (
          <Collapse.Panel
            header={
              <>
                <FolderOpenTwoTone /> &nbsp;<b>{folder?.name}</b>
              </>
            }
            key={folder?.id}
            extra={<EditButtonFolder folder={folder} refetch={refetch} />}
          >
            <UserPropertyTable
              elements={folder?.elements}
              userId={userId}
              canEdit={canEdit}
              refetch={refetch}
              userPropertyFolderId={folder?.id}
            />
          </Collapse.Panel>
        ))}
      </Collapse>

      <br />

      {/* CRUD folders : modal ? */}
      <Button icon={<PlusOutlined />} onClick={onAddClick}>
        {t('AddUserInfoFolder')}
      </Button>

      <CreateEditUserPropertyFolderModal
        modalVisible={createModalVisible}
        modalType={ModalType.CREATE}
        closeModalHandler={() => {
          setCreateModalVisible(false);
          refetch();
        }}
      />
    </React.Fragment>
  );
}
