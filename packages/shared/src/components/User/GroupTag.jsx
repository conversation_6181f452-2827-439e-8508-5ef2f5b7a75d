import React, { useContext } from 'react';
import groupPlaceholder from '@/shared/assets/group-placeholder.svg';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils';
import { GlobalContext } from '@/shared/layouts/BlankLayout';

export function GroupTag({ label, image }) {
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;
  return (
    <span
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: 4,
        borderRadius: 12,
        border: '2px solid',
        borderColor: primaryColor,
        padding: '0 8px',
        textOverflow: 'ellipsis'
      }}>
      <img
        alt="groupImage"
        style={{ maxHeight: 20, margin: 'auto' }}
        src={
          image ? getUrlProtectedRessource(GlobalConfig.get().FILES_URL + image) : groupPlaceholder
        }
        onError={(e) => {
          e.target.style = 'display: none';
        }}
      />
      {label}
    </span>
  );
}
