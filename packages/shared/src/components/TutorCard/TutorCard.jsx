import ExoAvatar from '@/shared/components/User/ExoAvatar.jsx';
import { <PERSON><PERSON>, Card, Divider, Empty } from 'antd';
import { LikeTwoTone, MessageOutlined, MessageTwoTone } from '@ant-design/icons';
import { goToPrivateDiscussionWith } from '@/shared/services/user.js';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import useMediaQuery from '@/shared/hooks/useMediaQuery.jsx';
import { UserProfileCard } from '@/shared/components/User/UserProfileCard/UserProfileCard';
import { BiographyWrapper } from '@/shared/components/User/BiographyWrapper/BiographyWrapper';

export default function ({ tuteur, primaryColor, secondaryColor }) {
  const { t } = useTranslation();
  const isLargeScreen = useMediaQuery('(min-width: 450px)');
  const isSmallScreen = useMediaQuery('(max-width: 399px)');
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div
      style={{
        maxHeight: isOpen ? '600px' : '275px',
        marginBottom: '42px',
        display: 'flex',
        justifyContent: 'center',
        transition: 'max-height 0.6s'
      }}
    >
      <Card
        style={{
          textAlign: 'center',
          width: `${isLargeScreen ? '450px' : isSmallScreen ? '350px' : '400px'}`
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <UserProfileCard userId={tuteur?.id} username={tuteur?.username}>
            <ExoAvatar
              style={{ cursor: 'pointer' }}
              isActive={tuteur?.isActive}
              avatar={tuteur?.avatar}
              size={90}
            />
          </UserProfileCard>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <span style={{ fontSize: '18px', fontWeight: '600' }}>{tuteur?.username}</span>
            {tuteur.title && (
              <span
                style={{
                  backgroundColor: primaryColor,
                  opacity: '80%',
                  color: 'white',
                  borderRadius: '100px',
                  padding: '0 16px'
                }}
              >
                {tuteur.title}
              </span>
            )}
            <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
              <span>
                <MessageTwoTone twoToneColor={primaryColor} />
              </span>
              <span>{tuteur.stats?.postsSent}</span>
              <span>
                <LikeTwoTone twoToneColor={primaryColor} />
              </span>
              <span>{tuteur.stats?.likesReceived}</span>
            </div>
          </div>

          {tuteur?.isReachableByPrivateMessage && (
            <Button
              style={isSmallScreen && { position: 'absolute', top: '10px', left: '10px' }}
              type="primary"
              shape="circle"
              size="large"
              icon={<MessageOutlined style={{ fontSize: '24px' }} />}
              onClick={() => goToPrivateDiscussionWith(tuteur.id)}
            />
          )}
        </div>
        <Divider>{t('general.biography')}</Divider>
        {tuteur.bio ? (
          <BiographyWrapper bio={tuteur.bio} isOpen={isOpen} setIsOpen={setIsOpen} />
        ) : (
          <div style={{ height: '80px' }}>
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={t('NoBiographyYet')} />
          </div>
        )}
      </Card>
    </div>
  );
}
