import ChallengeImage from '@/shared/components/Challenge/ChallengeImage.jsx';
import { QUERY_CHALLENGE_USER_PROGRESS } from '@/shared/graphql/challenges.js';
import { MUTATION_LAUNCH_QCM_SESSION } from '@/shared/graphql/qcm.js';
import {
  getChallengeDescriptionWithFallback,
  getChallengeNameWithFallback
} from '@/shared/services/translate.js';
import { CheckCircleOutlined, PlayCircleTwoTone, TrophyOutlined } from '@ant-design/icons';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Tag } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';
import Link from 'umi/link.js';

export default function ChallengeItem({ challenge }) {
  const { t } = useTranslation();
  const { image = '', id = '' } = challenge;

  /* LAUNCH SESSION */
  const [launchMcqSession, { loadingMcqSession }] = useMutation(MUTATION_LAUNCH_QCM_SESSION, {
    variables: {
      challengeId: challenge?.id
    }
  });

  const { data: dataUserProgress } = useQuery(QUERY_CHALLENGE_USER_PROGRESS, {
    variables: {
      filter: {
        challengeId: challenge?.id
      }
    }
  });
  const challengeUserProgress = dataUserProgress?.challengeUserProgress;
  const hasNoProgress = !challengeUserProgress;
  const hasNotFinished = challengeUserProgress?.isFinished === false;
  const hasFinished = challengeUserProgress?.isFinished === true;

  const onClickActionButton = async () => {
    if (hasFinished) {
      router.push(`/challenge/${id}`);
      return;
    }
    const { data } = await launchMcqSession();
    const { startOrResumeMcqSession } = data;
    const sessionIdToLaunch = startOrResumeMcqSession?.id;
    router.push(`/challenge/${challenge?.id}/do/${sessionIdToLaunch}`);
  };

  const buttonStartOrSee = (
    <Button
      loading={loadingMcqSession}
      icon={hasNoProgress || hasNotFinished ? <PlayCircleTwoTone /> : <TrophyOutlined />}
      onClick={onClickActionButton}
    >
      {hasNoProgress && <>{t('Start')}</>}
      {hasNotFinished && <>{t('general.Continue')}</>}
      {hasFinished && <>{t('general.See')}</>}
    </Button>
  );

  const linkToChallenge = `/challenge/${id}`;

  return (
    <div key={id} style={{ textAlign: 'left', marginBottom: '15px' }}>
      <div
        style={{
          display: 'flex',
          flexFlow: 'row wrap',
          gap: '12px',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        {' '}
        {/* wrapper */}
        <div style={{ order: 1, alignSelf: 'flex-start' }}>
          <Link to={linkToChallenge}>
            <ChallengeImage image={image} />
          </Link>
        </div>
        <div
          style={{
            flex: '2',
            order: '2',
            alignSelf: 'flex-start'
          }}
        >
          <Link to={linkToChallenge}>
            <h4>{getChallengeNameWithFallback(challenge)}</h4>
          </Link>
          <Link to={linkToChallenge}>
            <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
              {getChallengeDescriptionWithFallback(challenge)}
            </span>
          </Link>
          &nbsp;
          {hasFinished && (
            <Tag icon={<CheckCircleOutlined />} color="success">
              {t('general.done')}
            </Tag>
          )}
        </div>
        <div style={{ order: '3' }}>{buttonStartOrSee}</div>
      </div>
    </div>
  );
}
