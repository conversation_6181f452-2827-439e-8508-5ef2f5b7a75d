/**
 * ExoQuillEditor component
 *
 * This component is a customized ReactQuill editor with additional functionalities
 * such as handling equations, custom toolbars, and emoji insertion.
 *
 * @param {Object} props - The properties object.
 * @param {string} props.defaultValue - The default value to load into the editor.
 * @param {function} props.onChange - Callback function to handle content changes.
 * @param {Object} props.modules - Quill modules to be used in the editor.
 * @param {string} props.placeholder - Placeholder text for the editor.
 * @param {string} [props.customToolbarName=null] - Optional name for a custom toolbar.
 * @param {Object} [props.customToolbarProps={}] - Optional properties for the custom toolbar.
 * @param {string} [props.editorClassName='quill-editor-container'] - CSS class name for the editor container.
 * @param {boolean} [props.shouldEmptyEditor=false] - Flag to indicate if the editor should be emptied.
 *
 * @returns {JSX.Element} The rendered ExoQuillEditor component.
 */
import EquationBlot from '@/shared/components/ExoQuill/Equation/EquationBlot';
import EquationModal from '@/shared/components/ExoQuill/Equation/EquationModal';
import EquationKeyboardHandler from '@/shared/components/ExoQuill/Equation/EquationKeyboardHandler';
import useEquationManager from '@/shared/components/ExoQuill/Equation/useEquationManager';
import {
  cleanKatexHtml,
  convertTextToQuillEquations,
  fontSizeArr
} from '@/shared/components/ExoQuill/utils';
import { CustomEditorToolbar } from '@/shared/pages/messages/components/CustomEditorToolbar';
import ErrorBoundaryQuill from '@/shared/pages/qcm/components/error/ErrorBoundaryQuill';
import QuillAutoDetectUrl from 'quill-auto-detect-url';
import React, { useContext, useEffect, useRef, useState } from 'react';
import ReactQuill, { Quill } from 'react-quill-new';
import 'quill-table-better/dist/quill-table-better.css';
import 'quill-mention/dist/quill.mention.css';
import QuillTableBetter from 'quill-table-better';
import { Mention, MentionBlot } from 'quill-mention';

// KaTeX dependency
import katex from 'katex';
import '@/shared/utils/katex/katex.min.css';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { CustomEditorFileUploader } from '@/shared/pages/messages/components/CustomEditorFileUploader';

window.katex = katex;

const Delta = Quill.import('delta');

// font size
/* QUILL CONFIG */
const icons = Quill.import('ui/icons');
icons['equation'] =
  '<svg viewBox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"></path><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"></rect><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"></path></svg>';

const DEBUG = true;

// Quill Registers
const FontSizeStyle = Quill.import('attributors/style/size');
FontSizeStyle.whitelist = fontSizeArr;

const registerQuillModules = () => {
  Quill.register({ 'modules/table-better': QuillTableBetter }, true);
  Quill.register({ 'blots/mention': MentionBlot, 'modules/mention': Mention });
  Quill.register('modules/autoDetectUrl', QuillAutoDetectUrl);
  Quill.register(FontSizeStyle, true);
  Quill.register(EquationBlot);
  Quill.register('modules/equationKeyboardHandler', EquationKeyboardHandler);
};

registerQuillModules(); // Register global modules outside component

// Les preset disponible
export const QUILL_PRESET = {
  DEFAULT: 'DEFAULT', // Rien
  FLASHCARD: 'FLASHCARD' // Preset flashcard => grosse police, bold et centré
};

export default function ExoQuillEditor({
  defaultValue,
  onChange,
  modules,
  placeholder,

  onBlur = null,
  value = null, // Pour le contrôle de la valeur, optionnel, moins performant

  // Custom toolbar chat (optionnel)
  customToolbarName = null,
  customToolbarProps = {},
  customQuillStyle = {},

  // Full custom toolbar

  editorClassName = 'quill-editor-container',

  shouldEmptyEditor = false,
  isReplying = false,
  noPadding = false,
  hideUploader = false,
  hideSendButton = false,
  customBgColor,

  fileList, // upload files management
  setFileList,
  // permet dans load default content de définir du formatage par default
  quillEditorPreset = QUILL_PRESET.DEFAULT,

  // boolean qui hide overflow ou pas
  shouldHideOverflow = true
}) {
  const editorRef = useRef(null);
  const {
    editingEquation,
    setEditingEquation,
    equationValue,
    insertingNewEquation,
    setInsertingNewEquation,
    setEquationValue,
    handleEquationEdit,
    handleSaveEquation,
    handleSaveNewEquationString,
    handleInsertEquation,
    handleSaveNewEquation,
    handleInteractWithEquation,
    renderEquationsInQuillDOM
  } = useEquationManager();

  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;

  //DEBUG && console.log('re-render ExoQuillEditor');

  const [emojisVisibles, setEmojisVisibles] = useState(false);
  const [isLoadingEmojis, setIsLoadingEmojis] = useState(false);
  const [showFileUploader, setShowFileUploader] = useState(false);

  const loadDefaultContent = (quillEditor) => {
    /* LOAD DEFAULT DATA TO EDITOR WITH TABLE */
    //Pour que si il y a une table elle puisse se load correctement

    let delta;

    // Init du quill editor lorsqu'il est vide.
    if (defaultValue === null || defaultValue === undefined || defaultValue === '') {
      // Init par default (rien)
      if (quillEditorPreset === QUILL_PRESET.DEFAULT) {
        return null;

        // Init flashcard (big police, centré, et bold)
      } else if (quillEditorPreset === QUILL_PRESET.FLASHCARD) {
        delta = quillEditor.clipboard.convert({
          html: '<p style="text-align: center; font-size: 54px;"><strong>&nbsp;</strong></p>',
          text: ''
        });
      }

      // Récup du style déjà existant
    } else {
      delta = quillEditor.clipboard.convert({
        html: convertTextToQuillEquations(defaultValue),
        text: '\n'
      });
    }
    const range = quillEditor.selection.getRange();
    quillEditor.updateContents(delta, Quill.sources.USER);
    quillEditor.setSelection(delta.length() - range.length, Quill.sources.SILENT);
  };

  useEffect(() => {
    if (shouldEmptyEditor) {
      const quillEditor = editorRef.current.getEditor();
      quillEditor.setText('');
    }
  }, [shouldEmptyEditor]);

  // When value change, if it's not null, update editor content
  // Effect pour charger la nouvelle valeur si elle change
  /*
  useEffect(() => {
    const quillEditor = editorRef.current.getEditor();
    // Ne mettre à jour l'éditeur que si la nouvelle valeur est différente
    if (value !== null && !isEqual(previousValue.current, value)) {
      const delta = quillEditor.clipboard.convert({
        html: convertTextToQuillEquations(value),
        text: '\n'
      });
      const range = quillEditor.selection.getRange();
      quillEditor.updateContents(delta, Quill.sources.USER);
      quillEditor.setSelection(delta.length() - range.length, Quill.sources.SILENT);

      // Mettre à jour la valeur précédente pour éviter une boucle infinie
      previousValue.current = value;
    }
  }, [value]);
  */
  const [lastRange, setLastRange] = useState(null);

  const handleCopyPasteEquations = (delta, oldDelta, source) => {
    if (source !== 'user') return; // Ne traiter que les modifications de l'utilisateur

    const quillEditor = editorRef.current.getEditor();
    const equationsToReplace = []; // Liste pour stocker les équations à remplacer
    let currentIndex = 0; // Position actuelle dans le document
    delta.ops.forEach((op) => {
      if (op.retain) {
        currentIndex += op.retain;
      }
      if (op.insert) {
        const insertedText = op.insert;
        if (typeof insertedText === 'string') {
          const equationRegex = /\$(.+?)\$/g;
          let match;
          while ((match = equationRegex.exec(insertedText)) !== null) {
            const equation = match[1];
            const relativeStartIndex = match.index;
            const length = match[0].length;
            const absoluteStartIndex = currentIndex + relativeStartIndex;

            // Stocker les informations de l'équation pour remplacement ultérieur
            equationsToReplace.push({
              equation,
              startIndex: absoluteStartIndex,
              length
            });
          }
        }
        // Mettre à jour l'index actuel
        if (typeof insertedText === 'string') {
          currentIndex += insertedText.length;
        } else {
          currentIndex += 1; // Pour les embeds
        }
      }
      if (op.delete) {
        // Ajuster l'index actuel en fonction du texte supprimé
        // (Bien que les suppressions n'affectent pas directement les équations insérées)
      }
    });
    // Appliquer les remplacements après avoir parcouru toutes les opérations
    // Parcourir les équations à remplacer en ordre inverse pour éviter les problèmes d'indexation
    equationsToReplace.reverse().forEach(({ equation, startIndex, length }) => {
      // Supprimer le texte de l'équation incluant les '$'
      quillEditor.deleteText(startIndex, length, Quill.sources.SILENT);
      // Insérer le blot de l'équation à la position de départ
      quillEditor.insertEmbed(startIndex, 'equation', equation, Quill.sources.SILENT);
    });
  };

  const addAllEventListeners = (quillEditor) => {
    // EVENT LISTENERS
    quillEditor.root.addEventListener('dblclick', handleInteractWithEquation);
    quillEditor.root.addEventListener('contextmenu', handleInteractWithEquation);
    quillEditor.on('selection-change', (range) => {
      if (range && range.length === 0) {
        setLastRange(range); // Sauvegarde uniquement si le curseur est placé
      }
    });

    // Marche parfaitement pour copy paste une ou plusieurs equations avec $ autours
    quillEditor.on('text-change', handleCopyPasteEquations);

    ///////////////////////////
  };
  const cleanupAllEventsListeners = (quillEditor) => {
    quillEditor.root.removeEventListener('dblclick', handleInteractWithEquation);
    quillEditor.root.removeEventListener('contextmenu', handleInteractWithEquation);
    quillEditor.off('selection-change');
    quillEditor.off('text-change');
  };

  const setupToolbarHandlers = (quillEditor) => {
    // Ajouter le bouton d'insertion d'équation dans la toolbar
    const toolbar = quillEditor.getModule('toolbar');
    toolbar.addHandler('equation', handleInsertEquation);
  };

  // USE EFFECT ON LOAD
  useEffect(() => {
    registerQuillModules(); // Register again in case of hot reload to be sure because sometimes it's not registered

    const quillEditor = editorRef?.current?.getEditor();
    if (!quillEditor) return;
    DEBUG && console.log('useEffect ExoQuillEditor');
    DEBUG && console.log(Quill.imports);

    addAllEventListeners(quillEditor);
    setupToolbarHandlers(quillEditor);

    loadDefaultContent(quillEditor);

    // apres avoir inséré contenu initial table-compatible, render les equations sinon elles ne s'affichent pas
    renderEquationsInQuillDOM(quillEditor);

    // Nettoyage events
    return () => {
      cleanupAllEventsListeners(quillEditor);
    };
  }, []);

  const handleChange = (v) => {
    return onChange(cleanKatexHtml(v)); // Return to parent component
  };

  let additionnalProps = {
    //...(onBlur && { onBlur: onBlur })
  };

  const handleAddEmoji = (emoji) => {
    const editor = editorRef.current.getEditor();
    const range = lastRange || editor.getSelection(); // Restaurer la dernière position connue si elle existe
    // Vérifie que le range est bien défini
    if (range) {
      console.log('Range trouvé:', range);
      // Insérer l'emoji à la position actuelle du curseur
      editor.insertText(range.index, emoji.native, Quill.sources.USER);
      // Repositionner le curseur après l'emoji
      editor.setSelection(range.index + emoji.native.length, Quill.sources.SILENT);
    } else {
      console.log('Pas de sélection active');
    }

    setEmojisVisibles(false);
    setIsLoadingEmojis(false);
  };

  const handleOnBlur = (previousRange) => {
    setLastRange(previousRange);
  };

  useEffect(() => {
    if (shouldEmptyEditor) {
      const quillEditor = editorRef.current.getEditor();
      quillEditor.setText(''); // Vider le contenu de l'éditeur
      setShowFileUploader(false);
    }
  }, [shouldEmptyEditor]);

  return (
    <ErrorBoundaryQuill>
      <div
        style={{
          position: 'relative',
          borderRadius: 12,
          backgroundColor: customBgColor,
          color: '#4E5058'
        }}
      >
        {/* Éditeur React Quill */}
        <div
          style={{
            ...(shouldHideOverflow ? { overflow: 'hidden' } : {})
            // padding: noPadding ? 0 : '12px 12px 0 12px'
          }}
        >
          <ReactQuill
            style={customQuillStyle}
            ref={editorRef}
            theme="snow"
            className={editorClassName}
            bounds={`.${editorClassName}`} // A verifier
            onChange={handleChange}
            modules={modules}
            preserveWhitespace
            onBlur={handleOnBlur}
            //style={{ height: '400px' }}
            //onFocus={handleFocus} // Empêche le focus automatique
            placeholder={placeholder}
            {...additionnalProps}
          />
        </div>

        {/* Éditeur d'équation pour l'édition et l'insertion */}
        {(editingEquation || insertingNewEquation) && (
          <EquationModal
            equationValue={equationValue}
            setEquationValue={setEquationValue}
            handleSaveEquation={handleSaveEquation}
            handleSaveNewEquation={() => {
              handleSaveNewEquation(editorRef.current.getEditor());
            }}
            handleSaveNewEquationString={(value) => {
              handleSaveNewEquationString(value, editorRef.current.getEditor());
            }}
            editingEquation={editingEquation}
            hideModal={() => {
              setEditingEquation(false);
              setInsertingNewEquation(false);
            }}
          />
        )}
        {customToolbarName === 'chat' && (
          <>
            <CustomEditorToolbar
              handleAddEmoji={handleAddEmoji}
              emojisVisibles={emojisVisibles}
              setEmojisVisibles={(v) => setEmojisVisibles(v)}
              setIsLoadingEmojis={(v) => setIsLoadingEmojis(v)}
              isLoadingEmojis={isLoadingEmojis}
              {...customToolbarProps}
              toggleShowFileUploader={() => setShowFileUploader(!showFileUploader)}
              noPadding={noPadding}
              hideUploader={hideUploader}
              hideSendButton={hideSendButton}
            />
            {showFileUploader && (
              <div style={{ position: 'absolute', top: isReplying ? -140 : -120 }}>
                <CustomEditorFileUploader fileList={fileList} setFileList={setFileList} />
              </div>
            )}
          </>
        )}
      </div>
    </ErrorBoundaryQuill>
  );
}
