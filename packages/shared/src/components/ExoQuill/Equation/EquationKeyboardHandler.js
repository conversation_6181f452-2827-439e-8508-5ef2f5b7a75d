import { Quill } from 'react-quill-new';

class EquationKeyboardHandler {
  constructor(quill, options) {
    this.quill = quill;
    this.options = options;

    // Lier le gestionnaire d'événements clavier
    this.quill.keyboard.addBinding({ key: '$' }, this.handleDollarSign.bind(this));
  }

  handleDollarSign(range, context) {
    const quill = this.quill;
    const cursorPosition = range.index;
    // on sait que user a tapé un dollar mais on ne sait pas si c'est un début ou une fin d'équation
    // TextBefore est le dernier caractère de l'équation sans le $ du coup
    const textBefore = quill.getText(Math.max(0, cursorPosition - 1), 1);
    //if (textBefore === '$') {
    // Trouver le début de l'équation
    const start = this.findEquationStart(cursorPosition - 2); // OK
    if (start !== null) {
      const equationText = quill.getText(start + 1, cursorPosition - start - 1); // -2
      const textToDelete = quill.getText(start, cursorPosition - start);
      //console.log({ start, equationText, textToDelete });
      // Supprimer les deux '$' et le contenu entre eux
      quill.deleteText(start, cursorPosition - start, Quill.sources.USER);
      // Insérer le blot d'équation
      quill.insertEmbed(start, 'equation', equationText, Quill.sources.USER);
      // Positionner le curseur après le blot
      quill.setSelection(start + 1, Quill.sources.SILENT);
      return false; // Empêcher l'insertion du '$' actuel
    }
    //}
    return true; // Permettre l'insertion du '$' actuel
  }

  findEquationStart(position) {
    const quill = this.quill;
    while (position >= 0) {
      const char = quill.getText(position, 1);
      if (char === '$') {
        return position;
      } else if (char === '\n') {
        // Équation non trouvée dans la ligne courante
        return null;
      }
      position--;
    }
    return null;
  }
}
export default EquationKeyboardHandler;
