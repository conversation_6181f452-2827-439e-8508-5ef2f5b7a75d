import React from 'react';
import { useTranslation } from 'react-i18next';
import { Cascader } from 'antd';

export const CascaderMultiSelect = ({
  rootName = 'root',
  objectData = [{}],
  initData = null,
  initStyle = 'all',
  setSelection = () => {},
  useHookAtInit = true,
  style = {}
}) => {
  /* Petit componant permettant de gérer la sélection d'éléments dans une array à partir d'un componant cascader */
  const { t } = useTranslation();

  const formatedData = objectData && [
    {
      label: t(rootName),
      value: 0,
      id: 0,
      children: [
        ...objectData.map((node) => {
          return { label: node.name, value: node.id };
        })
      ]
    }
  ];

  const uniqueIdsFromObjectData = [...new Set(objectData.map((node) => node.id))];

  function initComponant(inputValue) {
    /* Fonction d'init du componant */

    function arraysHaveSameElementsThanReference(inputValue) {
      /* Fonction qui permet de check si les ids input sont une bijection des id de objectData (appareillage parfait 1:1) */
      // Check de si les deux arrays d'ID sont identique
      const sameLength = inputValue.length === uniqueIdsFromObjectData.length; // même longueur
      const identical = uniqueIdsFromObjectData.every((id) => inputValue.includes(id)); // Check si all unique présent dans init => possible comme ça car on a une array d'unique
      return sameLength && identical;
    }

    let tmp;

    // Si on file pas de données d'initialisation on peux choisir selon plusieures méthodes
    if (inputValue === null) {
      if (initStyle === 'all') {
        if (useHookAtInit) {
          setSelection(uniqueIdsFromObjectData);
        } // On set la totalité de l'array
        return [0]; // On set dans le componant la root
      } else if (initStyle === 'last') {
        tmp = uniqueIdsFromObjectData[uniqueIdsFromObjectData.length - 1];
        if (useHookAtInit) {
          setSelection([tmp]);
        }
        return [0, tmp];
      } else if (initStyle === 'first') {
        tmp = uniqueIdsFromObjectData[0];
        if (useHookAtInit) {
          setSelection([tmp]);
        }
        return [0, tmp];
      }
    }

    // Si on file des données, alors on check
    else if (arraysHaveSameElementsThanReference(inputValue)) {
      return [0];
    } // Si les données initiales === all possibilitées => On select tout
    else {
      return [
        ...inputValue.map((value) => {
          return [0, value];
        })
      ];
    } // Sinon, on reconstruit pour select seulement les présentes
  }

  function onChangerDecoder(componantValue) {
    /* Decode la valeur retournée du componant en une array d'éléments sélectionnés valide et appelle setHook dessus */
    if (componantValue.length > 0) {
      // Si la valeur retournée n'est pas null => Autrement dit, si on a coché qqc

      // Si la valeur retournée par le componant est 1, alors 2 cas : => a) [[all]] => root // b) [[all,fils]] et on doit skip
      if (componantValue.length === 1 && componantValue[0].length === 1) {
        setSelection(uniqueIdsFromObjectData);
      }

      // Bin ducoup, si on est pas à la racine, on itère sur le return du componant, et on retourne les fils
      else {
        setSelection(componantValue.map((temp) => temp[1]));
      }
    }

    // Ducoup, si la valeur retournée du componant est vide, alors on set la sélection à vide.
    else {
      setSelection([]);
    }
  }

  return (
    <>
      {
        <Cascader
          style={{ ...style }}
          options={formatedData}
          onChange={onChangerDecoder}
          multiple
          defaultValue={initComponant(initData)}
          maxTagCount="responsive"
        />
      }
    </>
  );
};
