import React, { useState } from 'react';
import { useVidstackContext } from '@/shared/components/Vidstack/VidstackContextProvider';
import { Button, Checkbox, InputNumber, Slider, Switch, Tooltip, Typography } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
const { Text } = Typography;

const VidstackParameters = () => {
  const { t } = useTranslation();
  const {
    autoPlay,
    setAutoPlay,
    authorizeSpeedModification,
    setAuthorizeSpeedModification,
    authorizeFullScreen,
    setAuthorizeFullScreen,
    authorizePictureInPicture,
    setAuthorizePictureInPicture,
    authorizeDownload,
    setAuthorizeDownload,
    videoWidth,
    setVideoWidth,
    enableStatsTracking,
    setEnableStatsTracking
  } = useVidstackContext();

  const VIDEO_WIDTH_MAX_VALUE = 1800;
  const VIDEO_WIDTH_MIN_VALUE = 250;

  // Value qui sert de buffer et qui est init avec la valeur par default du componant
  const [value, setValue] = useState(500);

  return (
    <>
      <div
        style={{
          minWidth: '300px',
          maxWidth: '500px',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          gap: '10px'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', margin: '2px' }}>
          <div>
            <Text style={{ wordBreak: 'break-word', whiteSpace: 'normal' }}>
              {t('VideoFE.EnableAutoPlayLabel')}{' '}
            </Text>
            &nbsp;
            <Tooltip title={t('VideoFE.EnableAutoPlayExplanation')}>
              <QuestionCircleOutlined style={{ cursor: 'pointer' }} />
            </Tooltip>
            &nbsp; : &nbsp;
          </div>

          <Switch
            style={{ minWidth: '50px' }}
            checkedChildren={t('VideoFE.Yes')}
            unCheckedChildren={t('VideoFE.No')}
            checked={autoPlay}
            onChange={setAutoPlay}
          />
        </div>

        <div style={{ display: 'flex', alignItems: 'center', margin: '2px' }}>
          <div>
            <Text style={{ wordBreak: 'break-word', whiteSpace: 'normal' }}>
              {t('VideoFE.AllowPlaybackRateChangeLabel')}{' '}
            </Text>
            &nbsp; : &nbsp;
          </div>

          <Switch
            style={{ minWidth: '50px' }}
            checkedChildren={t('VideoFE.Yes')}
            unCheckedChildren={t('VideoFE.No')}
            checked={authorizeSpeedModification}
            onChange={setAuthorizeSpeedModification}
          />
        </div>

        <div style={{ display: 'flex', alignItems: 'center', margin: '2px' }}>
          <div>
            <Text style={{ wordBreak: 'break-word', whiteSpace: 'normal' }}>
              {t('VideoFE.AllowFullScreenLabel')}
            </Text>
            &nbsp; : &nbsp;
          </div>

          <Switch
            style={{ minWidth: '50px' }}
            checkedChildren={t('VideoFE.Yes')}
            unCheckedChildren={t('VideoFE.No')}
            checked={authorizeFullScreen}
            onChange={setAuthorizeFullScreen}
          />
        </div>

        <div style={{ display: 'flex', alignItems: 'center', margin: '2px' }}>
          <div>
            <Text style={{ wordBreak: 'break-word', whiteSpace: 'normal' }}>
              {t('VideoFE.AllowDownloadLabel')}
            </Text>
            &nbsp;
            <Tooltip title={t('VideoFE.AllowDownloadExplanation')}>
              <QuestionCircleOutlined style={{ cursor: 'pointer' }} />
            </Tooltip>
            &nbsp; : &nbsp;
          </div>

          <Switch
            style={{ minWidth: '50px' }}
            checkedChildren={t('VideoFE.Yes')}
            unCheckedChildren={t('VideoFE.No')}
            checked={authorizeDownload}
            onChange={setAuthorizeDownload}
          />
        </div>

        <div style={{ display: 'flex', alignItems: 'center', margin: '2px' }}>
          <div>
            <Text style={{ wordBreak: 'break-word', whiteSpace: 'normal' }}>
              {t('VideoFE.AllowEnableStatsTracking')}
            </Text>
            &nbsp;
            <Tooltip title={t('VideoFE.AllowEnableStatsTrackingExplanation')}>
              <QuestionCircleOutlined style={{ cursor: 'pointer' }} />
            </Tooltip>
            &nbsp; : &nbsp;
          </div>

          <Switch
            style={{ minWidth: '50px' }}
            checkedChildren={t('VideoFE.Yes')}
            unCheckedChildren={t('VideoFE.No')}
            checked={enableStatsTracking}
            onChange={setEnableStatsTracking}
          />
        </div>

        <div style={{ display: 'flex' }}>
          <div style={{ minWidth: '50px' }}>
            <Text style={{ wordBreak: 'break-word', whiteSpace: 'normal' }}>
              {t('VideoFE.VideoWidthSelecterLabel')}
            </Text>
            &nbsp; : &nbsp;
          </div>
          <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}>
            <Slider
              min={VIDEO_WIDTH_MIN_VALUE}
              max={VIDEO_WIDTH_MAX_VALUE}
              defaultValue={videoWidth === '100%' ? VIDEO_WIDTH_MAX_VALUE : videoWidth}
              value={videoWidth === '100%' ? VIDEO_WIDTH_MAX_VALUE : videoWidth}
              disabled={videoWidth === '100%'}
              onChange={(val) => {
                setValue(val);
                setVideoWidth(val);
              }}
              style={{ minWidth: '300px' }}
            />
            <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}>
              <InputNumber
                min={VIDEO_WIDTH_MIN_VALUE}
                max={VIDEO_WIDTH_MAX_VALUE}
                style={{ margin: '0 16px', width: '90px' }}
                defaultValue={videoWidth === '100%' ? '100%' : videoWidth}
                value={videoWidth === '100%' ? '100%' : videoWidth}
                disabled={videoWidth === '100%'}
                onChange={(val) => {
                  setValue(val);
                  setVideoWidth(val);
                }}
                size={'small'}
              />
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: '5px',
                  flexWrap: 'wrap'
                }}
              >
                <span>{t('VideoFE.TakeFullPlaceCheckboxLabel')}</span>
                <Checkbox
                  checked={videoWidth === '100%'}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setVideoWidth('100%');
                    } else {
                      setVideoWidth(value);
                    }
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default VidstackParameters;
