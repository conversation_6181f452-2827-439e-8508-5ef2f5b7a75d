import React, { useEffect, forwardRef, useState, useRef } from 'react';
import fetch from 'node-fetch';
import {
  ACTION_NAME,
  LOG_STRUCTURE_KEY,
  useVidstackContext
} from '@/shared/components/Vidstack/VidstackContextProvider';
import { downloadProtectedS3File } from '@/shared/services/file';
import { useTranslation } from 'react-i18next';

const VidstackPlayerWrapper = forwardRef(
  (
    {
      src,
      inputFormat,
      id,
      tracksUrl,
      thumbnailUrl,
      imageSrc = { imageSrc },
      autoPlay,
      title = '',
      clipStartTime = 0,
      clipEndTime = null,
      enableStatsTracking = false,
      authorizeSpeedModification = false,
      authorizeFullScreen = false,
      authorizePictureInPicture = false,
      hasChapter = false,
      hasThumbnail = false,
      authorizeDownload = false,
      layoutType,
      s3FileName
    },
    ref
  ) => {
    /* VidStack est buggé, et lorsque l'on change le componant de vimeo à youtube et vice-versa, celui ci se détruit et lève une erreur non gérée.
     C'est un problème remonté de nombreuse fois sur le github.
     Le workaround que j'ai trouvé à ce problème, est d'avoir une ref unique par type de source. Un viewver pour les vidéos youtube, pour les vimeo, etc...

     Afin d'enforce cette règle, on veut que le componant parent gère la ref, et s'en occupe

     ///// Errors logs :
     - si y a une erreur du genre 't is undefined ' => c'est parce qu'on essaye de donner un attribut non valide au web componant
     - si y a l'erreur 'context is destroyed' => c'est parce que il y a un bug dans la lib, d'une erreure non catch qui fait crash lorsque le componant est démonté (qqc du genre) => Pour ça que je force input ref dans ce componant
     - argument keep alive pour éviter l'erreur du 'context is destroyed'


     ///// Tip implémentation => CDN + CSS + LAYOUT CUSTOM
      - CDN audio : https://vidstack.io/docs/player/getting-started/installation/cdn/?provider=audio&styling=default-layout
      - CDN VIDEO : https://vidstack.io/docs/player/getting-started/installation/cdn/?provider=video&styling=default-layout
      - Reference : https://stackblitz.com/edit/vidstack-examples-u3admhgt?file=src%2Fvideo-player.css,index.html,video-player.html&showSidebar=1&title=Vidstack%20Player%20-%20Web%20Components%20(Default%20Theme)

    // Faut laisser tomber la gestion du componant par JS, ça marche pas à cause des imports etc...


    // Documentations :
    //// intéressant pour le on Destroy : https://chatgpt.com/share/67d2b73f-1900-8009-938f-a7af08edfbf1
    //// https://vidstack.io/docs/player/getting-started/installation/cdn/?provider=video&styling=default-layout

    // Accrochage des events au player. Les events disponible : https://vidstack.io/docs/wc/player/components/core/player/
  */

    /////// CONSTANTES
    const { t } = useTranslation();
    const { wrapperLaunchLog, setDuration } = useVidstackContext();
    const TIME_DELAY_BETWEEN_EACH_LOGS = 10000; // La période entre chaque envoie de log en ms
    const isAudio = layoutType === 'audio'; // TODO : linker ça au web componant, parce que dans le cas où layoutType === null, ça va être audio sans être renseigné ici

    // Message d'erreur si on ne reçoit pas de ref
    if (!ref) {
      return <div>Componant needs a ref</div>;
    }

    // Init de la structure avec les key local à ce componant
    const [logStructure, setLogStructure] = useState({
      [LOG_STRUCTURE_KEY.COMPONANT_ID]: id,
      [LOG_STRUCTURE_KEY.VOLUME]: 1,
      [LOG_STRUCTURE_KEY.SEGMENT_INDEX]: 0,
      [LOG_STRUCTURE_KEY.SEGMENT_RAW]: '[]',
      [LOG_STRUCTURE_KEY.VIDEO_TIMESTAMP]: 0,
      [LOG_STRUCTURE_KEY.ENDED]: false,
      [LOG_STRUCTURE_KEY.PAUSED]: true,
      [LOG_STRUCTURE_KEY.PIP]: false,
      [LOG_STRUCTURE_KEY.PLAYBACK_RATE]: 1,
      [LOG_STRUCTURE_KEY.PLAYING]: false
    });

    const prevDurationRef = useRef(null);
    const logStructureRef = useRef(logStructure);

    /////////// Fonctions
    function serializeTimeRange(timeRange) {
      // Permet de serialiser la donnée 'played' du lecteur pour les stats
      const ranges = [];
      for (let i = 0; i < timeRange.length; i++) {
        ranges.push({
          start: timeRange.start(i),
          end: timeRange.end(i)
        });
      }
      return ranges;
    }

    const wrapperSetAndLog = async (dataDict) => {
      // Lance l'enregistrement d'un log via le contexte
      setLogStructure((prev) => {
        const newValues = { ...prev, ...dataDict };
        if (enableStatsTracking) {
          wrapperLaunchLog(newValues);
        }
        return newValues;
      });
    };

    async function checkVimeoUrl(src) {
      // Fonction qui permet avant de la mettre dans le lecteur, de ping l'URL vimeo pour voir si elle ne va pas faire crasher le componant
      let canceled = false;

      try {
        const vimeoOembedUrl = `https://vimeo.com/api/oembed.json?url=${encodeURIComponent(src)}`;
        const res = await fetch(vimeoOembedUrl);

        if (!canceled) {
          // La vidéo est à priori accessible
          if (res.ok) {
            return true;
          } else {
            return false;
          }
        }
      } catch (error) {
        if (!canceled) {
          return false;
        }
      }
    }

    /////////////// Use effect

    // À chaque update du logStructure on update la ref => permet de synchroniser les données du log Structure avec la subscription du player.
    useEffect(() => {
      logStructureRef.current = logStructure;
    }, [logStructure]);

    // Use effect qui permet de changer la source dans le lecteur et qui dans le cas d'une video VIMEO fait un ping
    useEffect(() => {
      if (!ref?.current) {
        return;
      }
      const validateAndSetSrc = async () => {
        // HARD CODDED => à changer
        let isValid = false;
        if (inputFormat === 'Vimeo') {
          isValid = await checkVimeoUrl(src);
        } else {
          isValid = true;
        }
        if (isValid) {
          if (ref.current) {
            ref.current.src = src;
          }
        }
      };
      validateAndSetSrc();
    }, [src]);

    // UseEffect qui permet de track les stats dans le componant
    useEffect(() => {
      ////////// Creation de la fonction de logging périodique au montage du componant :
      const intervalId = setInterval(() => {
        const player = document.getElementById(id);
        if (player?.paused === false) {
          wrapperSetAndLog({ [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.REPORTING });
        }
      }, TIME_DELAY_BETWEEN_EACH_LOGS);

      // SubscribeFunction
      const subscribeFunction = async () => {
        // 1) on récupère le player
        const player = document.getElementById(id);
        if (!player) return;

        // 2) on attend l’upgrade du custom-element (⟶ subscribe() sera présent)
        await customElements.whenDefined('media-player'); // 🔥 la ligne magique 🔥  :contentReference[oaicite:0]{index=0}

        const off = player?.subscribe(
          ({
            muted,
            volume,
            played,
            ended,
            paused,
            pictureInPicture,
            playbackRate,
            currentTime,
            duration,
            playing
          }) => {
            const currentLog = logStructureRef.current;

            // Update dans le contexte de la duration de la video
            if (duration && prevDurationRef.current !== duration) {
              prevDurationRef.current = duration;
              setDuration(duration);
            }

            // Update du log structure avec le timestamp de la vidéo
            if (currentTime) {
              setLogStructure((prev) => {
                return { ...prev, [LOG_STRUCTURE_KEY.VIDEO_TIMESTAMP]: currentTime };
              });
            }

            // Update de la structure si on est en playing
            if (playing) {
              setLogStructure((prev) => {
                return { ...prev, [LOG_STRUCTURE_KEY.PLAYING]: playing };
              });
            }

            // --- VOLUME --- => envoi d'un log lorsque l'on change le volume
            if (muted || volume === 0) {
              if (currentLog[LOG_STRUCTURE_KEY.VOLUME] !== 0) {
                wrapperSetAndLog({
                  [LOG_STRUCTURE_KEY.VOLUME]: 0,
                  [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.MUTE
                });
              }
            } else if (volume !== currentLog[LOG_STRUCTURE_KEY.VOLUME]) {
              wrapperSetAndLog({
                [LOG_STRUCTURE_KEY.VOLUME]: volume,
                [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.CHANGE_VOLUME
              });
            }

            // --- PLAYED SEGMENTS --- => Envoi d'un log lorsque l'on change de segment (on bouge via la time line)
            const isWatchInitialised = played?.length > 0;
            const isIndexDifferent = played.length !== currentLog[LOG_STRUCTURE_KEY.SEGMENT_INDEX];
            if (isWatchInitialised && isIndexDifferent) {
              const serializedTime = serializeTimeRange(played);
              wrapperSetAndLog({
                [LOG_STRUCTURE_KEY.SEGMENT_INDEX]: played.length,
                [LOG_STRUCTURE_KEY.SEGMENT_RAW]: serializedTime,
                [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.SWITCH_SEGMENT
              });
            }

            // --- ENDED --- => envoi d'un log lorsque fini
            if (ended !== currentLog[LOG_STRUCTURE_KEY.ENDED]) {
              wrapperSetAndLog({
                [LOG_STRUCTURE_KEY.ENDED]: ended,
                [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.ENDED
              });
            }

            // --- PAUSED --- => Envoi d'un log lorsque pause/play
            if (paused !== currentLog[LOG_STRUCTURE_KEY.PAUSED]) {
              wrapperSetAndLog({
                [LOG_STRUCTURE_KEY.PAUSED]: paused,
                [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.PLAY_PAUSE
              }); // Maybe play ici
            }

            // --- PICTURE IN PICTURE --- => envoi log lorsque PIP/UNPIP (jcrois on gère pas dans le player)
            if (pictureInPicture !== currentLog[LOG_STRUCTURE_KEY.PIP]) {
              wrapperSetAndLog({
                [LOG_STRUCTURE_KEY.PIP]: pictureInPicture,
                [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.PIP_UNPIP
              }); // Maybe exit PIP ici
            }

            // --- PLAYBACK RATE --- => Envoi d'un log lorsque l'on modifie le playback rate
            if (playbackRate !== currentLog[LOG_STRUCTURE_KEY.PLAYBACK_RATE]) {
              wrapperSetAndLog({
                [LOG_STRUCTURE_KEY.PLAYBACK_RATE]: playbackRate,
                [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.CHANGE_PLAYBACK_RATE
              });
            }
          }
        );

        // 4) nettoyage
        return () => off?.();
      };

      subscribeFunction();

      return () => {
        // Lorsque l'on démonte, on clear l'interval
        clearInterval(intervalId);

        // Envoi d'un log lorsque l'on démonte le componant
        wrapperSetAndLog({ [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.REMOVE_COMPONANT });
      };
    }, [id]);

    /*
    useEffect(() => {
      ////////// Creation de la fonction de logging périodique au montage du componant :
      const intervalId = setInterval(() => {
        const player = document.getElementById(id);
        if (player && enableStatsTracking && player.paused === false) {
          wrapperSetAndLog({ [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.REPORTING });
        }
      }, TIME_DELAY_BETWEEN_EACH_LOGS);

      /////////// Creation de la subscription au player
      player?.subscribe(
        ({
          muted,
          volume,
          played,
          ended,
          paused,
          pictureInPicture,
          playbackRate,
          currentTime,
          duration,
          playing
        }) => {
          const currentLog = logStructureRef.current;

          // Update dans le contexte de la duration de la video
          if (duration && prevDurationRef.current !== duration) {
            prevDurationRef.current = duration;
            setDuration(duration);
          }

          // Update du log structure avec le timestamp de la vidéo
          if (currentTime) {
            setLogStructure((prev) => {
              return { ...prev, [LOG_STRUCTURE_KEY.VIDEO_TIMESTAMP]: currentTime };
            });
          }

          // Update de la structure si on est en playing
          if (playing) {
            setLogStructure((prev) => {
              return { ...prev, [LOG_STRUCTURE_KEY.PLAYING]: playing };
            });
          }

          // --- VOLUME --- => envoi d'un log lorsque l'on change le volume
          if (muted || volume === 0) {
            if (currentLog[LOG_STRUCTURE_KEY.VOLUME] !== 0) {
              wrapperSetAndLog({
                [LOG_STRUCTURE_KEY.VOLUME]: 0,
                [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.MUTE
              });
            }
          } else if (volume !== currentLog[LOG_STRUCTURE_KEY.VOLUME]) {
            wrapperSetAndLog({
              [LOG_STRUCTURE_KEY.VOLUME]: volume,
              [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.CHANGE_VOLUME
            });
          }

          // --- PLAYED SEGMENTS --- => Envoi d'un log lorsque l'on change de segment (on bouge via la time line)
          const isWatchInitialised = played?.length > 0;
          const isIndexDifferent = played.length !== currentLog[LOG_STRUCTURE_KEY.SEGMENT_INDEX];
          if (isWatchInitialised && isIndexDifferent) {
            const serializedTime = serializeTimeRange(played);
            wrapperSetAndLog({
              [LOG_STRUCTURE_KEY.SEGMENT_INDEX]: played.length,
              [LOG_STRUCTURE_KEY.SEGMENT_RAW]: serializedTime,
              [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.SWITCH_SEGMENT
            });
          }

          // --- ENDED --- => envoi d'un log lorsque fini
          if (ended !== currentLog[LOG_STRUCTURE_KEY.ENDED]) {
            wrapperSetAndLog({
              [LOG_STRUCTURE_KEY.ENDED]: ended,
              [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.ENDED
            });
          }

          // --- PAUSED --- => Envoi d'un log lorsque pause/play
          if (paused !== currentLog[LOG_STRUCTURE_KEY.PAUSED]) {
            wrapperSetAndLog({
              [LOG_STRUCTURE_KEY.PAUSED]: paused,
              [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.PLAY_PAUSE
            }); // Maybe play ici
          }

          // --- PICTURE IN PICTURE --- => envoi log lorsque PIP/UNPIP (jcrois on gère pas dans le player)
          if (pictureInPicture !== currentLog[LOG_STRUCTURE_KEY.PIP]) {
            wrapperSetAndLog({
              [LOG_STRUCTURE_KEY.PIP]: pictureInPicture,
              [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.PIP_UNPIP
            }); // Maybe exit PIP ici
          }

          // --- PLAYBACK RATE --- => Envoi d'un log lorsque l'on modifie le playback rate
          if (playbackRate !== currentLog[LOG_STRUCTURE_KEY.PLAYBACK_RATE]) {
            wrapperSetAndLog({
              [LOG_STRUCTURE_KEY.PLAYBACK_RATE]: playbackRate,
              [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.CHANGE_PLAYBACK_RATE
            });
          }
        }
      );

      return () => {
        // Lorsque l'on démonte, on clear l'interval
        return () => clearInterval(intervalId);

        // Envoi d'un log lorsque l'on démonte le componant
        wrapperSetAndLog({ [LOG_STRUCTURE_KEY.ACTION_NAME]: ACTION_NAME.REMOVE_COMPONANT });
      };
    }, []);


     */
    return (
      <div style={{ width: '100%', height: 'auto' }}>
        <media-player
          ref={ref}
          id={id}
          keep-alive={true}
          playsinline={true}
          title={title}
          poster={imageSrc}
          clip-start-time={clipStartTime}
          clip-end-time={clipEndTime - clipStartTime}
          {...(autoPlay ? { autoplay: true } : {})}
          {...(layoutType ? { 'view-type': layoutType } : {})}
        >
          <media-provider>
            <track src={tracksUrl} kind="chapters" type="vtt" default />
            {!isAudio && <media-poster class="vds-poster" />}
          </media-provider>

          {/* ****************** MEDIA - GESTURE ****************** */}
          <media-gesture
            className={'media-gesture'}
            event="pointerup"
            action="toggle:paused"
          ></media-gesture>

          {/* ********************* BUFFERING ********************* */}
          <div className="media-buffering-indicator">
            <media-spinner
              class="media-buffering-spinner"
              size="96"
              track-width="8"
            ></media-spinner>
          </div>

          {/* ------------- CONTROLS  && MEDIA-CONTROL ---------   */}
          <media-controls>
            {/* Ici, permet de laisser l'espace pour la vidéo*/}
            <div className="media-controls-spacer"></div>

            {/* ********************* TIME MEDIA SLIDER ********************* */}
            <media-controls-group>
              <media-time-slider class="time-media-slider">
                <media-slider-chapters class="media-slider-chapters">
                  <template>
                    <div className="media-slider-chapter">
                      <div className="media-slider-track">
                        <div className="media-slider-track-fill media-slider-track"></div>
                        <div className="media-slider-progress media-slider-track"></div>
                      </div>
                    </div>
                  </template>
                </media-slider-chapters>

                <div className="media-slider-thumb"></div>

                <media-slider-preview class="media-slider-preview">
                  <div className="media-slider-value" data-part="chapter-title"></div>
                  <media-slider-value class="media-slider-value"></media-slider-value>
                </media-slider-preview>
              </media-time-slider>
            </media-controls-group>

            {/* ********************* CONTROLS  ********************* */}
            <media-controls-group>
              {/* ------------- Play Button ---------   */}
              <media-play-button class="media-button">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  className="play-vds-icon"
                >
                  <path
                    d="M10.6667 6.6548C10.6667 6.10764 11.2894 5.79346 11.7295 6.11862L24.377 15.4634C24.7377 15.7298 24.7377 16.2692 24.3771 16.5357L11.7295 25.8813C11.2895 26.2065 10.6667 25.8923 10.6667 25.3451L10.6667 6.6548Z"
                    fill="currentColor"
                  />
                </svg>

                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  className="pause-vds-icon"
                >
                  <path
                    d="M8.66667 6.66667C8.29848 6.66667 8 6.96514 8 7.33333V24.6667C8 25.0349 8.29848 25.3333 8.66667 25.3333H12.6667C13.0349 25.3333 13.3333 25.0349 13.3333 24.6667V7.33333C13.3333 6.96514 13.0349 6.66667 12.6667 6.66667H8.66667Z"
                    fill="currentColor"
                  />
                  <path
                    d="M19.3333 6.66667C18.9651 6.66667 18.6667 6.96514 18.6667 7.33333V24.6667C18.6667 25.0349 18.9651 25.3333 19.3333 25.3333H23.3333C23.7015 25.3333 24 25.0349 24 24.6667V7.33333C24 6.96514 23.7015 6.66667 23.3333 6.66667H19.3333Z"
                    fill="currentColor"
                  />
                </svg>
              </media-play-button>

              {/* ------------- Mute Button ---------- */}
              <media-mute-button class="media-button">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  className="mute-vds-icon"
                >
                  <path
                    d="M17.5091 24.6594C17.5091 25.2066 16.8864 25.5208 16.4463 25.1956L9.44847 20.0252C9.42553 20.0083 9.39776 19.9991 9.36923 19.9991H4.66667C4.29848 19.9991 4 19.7006 4 19.3325V12.6658C4 12.2976 4.29848 11.9991 4.66667 11.9991H9.37115C9.39967 11.9991 9.42745 11.99 9.45039 11.973L16.4463 6.8036C16.8863 6.47842 17.5091 6.79259 17.5091 7.33977L17.5091 24.6594Z"
                    fill="currentColor"
                  />
                  <path
                    d="M28.8621 13.6422C29.1225 13.3818 29.1225 12.9597 28.8621 12.6994L27.9193 11.7566C27.659 11.4962 27.2368 11.4962 26.9765 11.7566L24.7134 14.0197C24.6613 14.0717 24.5769 14.0717 24.5248 14.0197L22.262 11.7568C22.0016 11.4964 21.5795 11.4964 21.3191 11.7568L20.3763 12.6996C20.116 12.9599 20.116 13.382 20.3763 13.6424L22.6392 15.9053C22.6913 15.9573 22.6913 16.0418 22.6392 16.0938L20.3768 18.3562C20.1165 18.6166 20.1165 19.0387 20.3768 19.299L21.3196 20.2419C21.58 20.5022 22.0021 20.5022 22.2624 20.2418L24.5248 17.9795C24.5769 17.9274 24.6613 17.9274 24.7134 17.9795L26.976 20.2421C27.2363 20.5024 27.6585 20.5024 27.9188 20.2421L28.8616 19.2992C29.122 19.0389 29.122 18.6168 28.8616 18.3564L26.599 16.0938C26.547 16.0418 26.547 15.9573 26.599 15.9053L28.8621 13.6422Z"
                    fill="currentColor"
                  />
                </svg>

                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  className="volume-low-vds-icon"
                >
                  <path
                    d="M17.5091 24.6594C17.5091 25.2066 16.8864 25.5207 16.4463 25.1956L9.44847 20.0252C9.42553 20.0083 9.39776 19.9991 9.36923 19.9991H4.66667C4.29848 19.9991 4 19.7006 4 19.3324V12.6658C4 12.2976 4.29848 11.9991 4.66667 11.9991H9.37115C9.39967 11.9991 9.42745 11.99 9.45039 11.973L16.4463 6.80358C16.8863 6.4784 17.5091 6.79258 17.5091 7.33975L17.5091 24.6594Z"
                    fill="currentColor"
                  />
                  <path
                    d="M22.8424 12.6667C22.8424 12.2985 22.544 12 22.1758 12H20.8424C20.4743 12 20.1758 12.2985 20.1758 12.6667V19.3333C20.1758 19.7015 20.4743 20 20.8424 20H22.1758C22.544 20 22.8424 19.7015 22.8424 19.3333V12.6667Z"
                    fill="currentColor"
                  />
                </svg>

                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  className="volume-high-vds-icon"
                >
                  <path
                    d="M17.5091 24.6595C17.5091 25.2066 16.8864 25.5208 16.4463 25.1956L9.44847 20.0252C9.42553 20.0083 9.39776 19.9992 9.36923 19.9992H4.66667C4.29848 19.9992 4 19.7007 4 19.3325V12.6658C4 12.2976 4.29848 11.9992 4.66667 11.9992H9.37115C9.39967 11.9992 9.42745 11.99 9.45039 11.9731L16.4463 6.80363C16.8863 6.47845 17.5091 6.79262 17.5091 7.3398L17.5091 24.6595Z"
                    fill="currentColor"
                  />
                  <path
                    d="M27.5091 9.33336C27.8773 9.33336 28.1758 9.63184 28.1758 10V22C28.1758 22.3682 27.8773 22.6667 27.5091 22.6667H26.1758C25.8076 22.6667 25.5091 22.3682 25.5091 22V10C25.5091 9.63184 25.8076 9.33336 26.1758 9.33336L27.5091 9.33336Z"
                    fill="currentColor"
                  />
                  <path
                    d="M22.1758 12C22.544 12 22.8424 12.2985 22.8424 12.6667V19.3334C22.8424 19.7016 22.544 20 22.1758 20H20.8424C20.4743 20 20.1758 19.7016 20.1758 19.3334V12.6667C20.1758 12.2985 20.4743 12 20.8424 12H22.1758Z"
                    fill="currentColor"
                  />
                </svg>
              </media-mute-button>

              {/* ------------- Volume Slider ---------- */}
              <media-volume-slider class="media-slider">
                <div className="volume-media-slider-track">
                  <div className="volume-slider-track-fill"></div>
                </div>
                <div className="volume-media-slider-thumb"></div>
              </media-volume-slider>

              {/* ------------- Time Group -------------- */}
              <div className="media-time-group">
                <media-time class="media-time" type="current"></media-time>
                <div className="media-time-divider">/</div>
                <media-time class="media-time" type="duration"></media-time>
              </div>

              {/* ------------- SHOW CHAPTER NAME -------------- */}
              <media-chapter-title class="media-chapter-title"></media-chapter-title>

              {/* ------------- SPEED MENU ------------------------ */}
              {authorizeSpeedModification && (
                <media-menu>
                  {/* ------------- SPEED MENU BUTTON ------------------------ */}
                  <media-menu-button
                    class="media-button media-menu-speed-change-button"
                    aria-label="Settings"
                  >
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18.6669 10.4001C18.6669 10.7683 18.3684 11.0667 18.0002 11.0667H16.2668C15.8987 11.0667 15.6002 10.7683 15.6002 10.4001V9.86674C15.6002 9.7931 15.5405 9.73341 15.4669 9.73341H5.99998C5.63179 9.73341 5.33331 9.43493 5.33331 9.06674V7.33341C5.33331 6.96522 5.63179 6.66674 5.99998 6.66674H15.4669C15.5405 6.66674 15.6002 6.60704 15.6002 6.53341V6.00007C15.6002 5.63188 15.8987 5.3334 16.2668 5.3334H18.0002C18.3684 5.3334 18.6669 5.63188 18.6669 6.00007V10.4001Z"
                        fill="currentColor"
                      />
                      <path
                        d="M11.3334 18.8668C11.7016 18.8668 12.0001 18.5683 12.0001 18.2001V13.8001C12.0001 13.4319 11.7016 13.1335 11.3334 13.1335H9.60006C9.23187 13.1335 8.93339 13.4319 8.93339 13.8001V14.3335C8.93339 14.4071 8.8737 14.4668 8.80006 14.4668H6.00006C5.63187 14.4668 5.33339 14.7653 5.33339 15.1335V16.8668C5.33339 17.235 5.63187 17.5335 6.00006 17.5335H8.80006C8.8737 17.5335 8.93339 17.5932 8.93339 17.6668V18.2001C8.93339 18.5683 9.23187 18.8668 9.60006 18.8668H11.3334Z"
                        fill="currentColor"
                      />
                      <path
                        d="M18.6667 26.0001C18.6667 26.3683 18.3682 26.6668 18 26.6668H16.2667C15.8985 26.6668 15.6 26.3683 15.6 26.0001V25.4668C15.6 25.3931 15.5403 25.3334 15.4667 25.3334H6.00014C5.63195 25.3334 5.33348 25.0349 5.33348 24.6668V22.9334C5.33348 22.5652 5.63195 22.2668 6.00014 22.2668H15.4667C15.5403 22.2668 15.6 22.2071 15.6 22.1334V21.6001C15.6 21.2319 15.8985 20.9334 16.2667 20.9334H18C18.3682 20.9334 18.6667 21.2319 18.6667 21.6001V26.0001Z"
                        fill="currentColor"
                      />
                      <path
                        d="M22 24.6668C22 25.0349 22.2985 25.3334 22.6667 25.3334H26.0001C26.3683 25.3334 26.6668 25.0349 26.6668 24.6668V22.9334C26.6668 22.5652 26.3683 22.2668 26.0001 22.2668H22.6667C22.2985 22.2668 22 22.5652 22 22.9334V24.6668Z"
                        fill="currentColor"
                      />
                      <path
                        d="M16.0001 17.5335C15.6319 17.5335 15.3334 17.235 15.3334 16.8668V15.1335C15.3334 14.7653 15.6319 14.4668 16.0001 14.4668H26.0001C26.3683 14.4668 26.6667 14.7653 26.6667 15.1335V16.8668C26.6667 17.235 26.3683 17.5335 26.0001 17.5335H16.0001Z"
                        fill="currentColor"
                      />
                      <path
                        d="M22.0002 9.06674C22.0002 9.43493 22.2987 9.73341 22.6669 9.73341H26C26.3682 9.73341 26.6666 9.43493 26.6666 9.06674V7.3334C26.6666 6.96521 26.3682 6.66674 26 6.66674H22.6669C22.2987 6.66674 22.0002 6.96522 22.0002 7.33341V9.06674Z"
                        fill="currentColor"
                      />
                    </svg>
                  </media-menu-button>

                  {/* ------------- SPEED MENU LAYOUT ------------------------ */}
                  <media-menu-items
                    class="media-menu-speed-change-layout"
                    placement="top"
                    offset="0"
                  >
                    <span
                      style={{
                        color: 'white',
                        textShadow: `
                        -1px -1px 0 black,
                         1px -1px 0 black,
                        -1px  1px 0 black,
                         1px  1px 0 black
                      `
                      }}
                    >
                      {t('VideoFE.SpeedMenuLayoutTitle')}
                    </span>
                    <media-speed-radio-group>
                      <template>
                        <media-radio class="media-radio media-radio-speed-rate">
                          <span data-part="label" className="speed-rate-media-label-button"></span>
                        </media-radio>
                      </template>
                    </media-speed-radio-group>
                  </media-menu-items>
                </media-menu>
              )}

              {/* --------------- CHAPTER BUTTON --------------- */}
              {hasChapter && (
                <media-menu>
                  <media-menu-button
                    class="media-button media-menu-chapters-button"
                    aria-label="Chapters"
                  >
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M16.6927 25.3346C16.3245 25.3346 16.026 25.0361 16.026 24.6679L16.026 7.3346C16.026 6.96641 16.3245 6.66794 16.6927 6.66794L18.6927 6.66794C19.0609 6.66794 19.3594 6.96642 19.3594 7.3346L19.3594 24.6679C19.3594 25.0361 19.0609 25.3346 18.6927 25.3346H16.6927Z"
                        fill="currentColor"
                      />
                      <path
                        d="M24.026 25.3346C23.6578 25.3346 23.3594 25.0361 23.3594 24.6679L23.3594 7.3346C23.3594 6.96641 23.6578 6.66794 24.026 6.66794L26.026 6.66794C26.3942 6.66794 26.6927 6.96642 26.6927 7.3346V24.6679C26.6927 25.0361 26.3942 25.3346 26.026 25.3346H24.026Z"
                        fill="currentColor"
                      />
                      <path
                        d="M5.48113 23.9407C5.38584 24.2963 5.59689 24.6619 5.95254 24.7572L7.88439 25.2748C8.24003 25.3701 8.60559 25.159 8.70089 24.8034L13.1871 8.06067C13.2824 7.70503 13.0713 7.33947 12.7157 7.24417L10.7838 6.72654C10.4282 6.63124 10.0626 6.8423 9.96733 7.19794L5.48113 23.9407Z"
                        fill="currentColor"
                      />
                    </svg>
                  </media-menu-button>
                  <media-menu-items
                    class="media-menu media-menu-chapters"
                    placement="top"
                    offset="0"
                  >
                    <media-chapters-radio-group thumbnails={thumbnailUrl}>
                      <template>
                        <media-radio class="media-radio media-radio-chapters">
                          {hasThumbnail && (
                            <media-thumbnail class="media-thumbnail"></media-thumbnail>
                          )}
                          <div className="chapter-row">
                            <span data-part="label" className="chapter-title"></span>
                            <span data-part="start-time" className="chapter-start"></span>
                            <span data-part="duration" className="chapter-duration"></span>
                          </div>
                        </media-radio>
                      </template>
                    </media-chapters-radio-group>
                  </media-menu-items>
                </media-menu>
              )}

              {/* --------------- DOWNLOAD --------------------- */}
              {authorizeDownload && s3FileName && (
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  className="media-button"
                  onClick={async () => {
                    await downloadProtectedS3File(s3FileName);
                  }}
                >
                  <path
                    d="M14.2225 13.7867C14.3065 13.8706 14.4501 13.8112 14.4501 13.6924V5.99955C14.4501 5.63136 14.7486 5.33289 15.1167 5.33289H16.8501C17.2183 5.33289 17.5167 5.63136 17.5167 5.99955V13.6916C17.5167 13.8104 17.6604 13.8699 17.7444 13.7859L19.9433 11.5869C20.2037 11.3266 20.6258 11.3266 20.8861 11.5869L22.1118 12.8126C22.3722 13.0729 22.3722 13.4951 22.1118 13.7554L16.4549 19.4123C16.1946 19.6726 15.772 19.6731 15.5116 19.4128L9.85479 13.7559C9.59444 13.4956 9.59444 13.0734 9.85479 12.8131L11.0804 11.5874C11.3408 11.3271 11.7629 11.3271 12.0233 11.5874L14.2225 13.7867Z"
                    fill="currentColor"
                  />
                  <path
                    d="M5.99998 20.267C5.63179 20.267 5.33331 20.5654 5.33331 20.9336V25.9997C5.33331 26.3678 5.63179 26.6663 5.99998 26.6663H26C26.3682 26.6663 26.6666 26.3678 26.6666 25.9997V20.9336C26.6666 20.5654 26.3682 20.267 26 20.267H24.2666C23.8985 20.267 23.6 20.5654 23.6 20.9336V22.9333C23.6 23.3014 23.3015 23.5999 22.9333 23.5999H9.06638C8.69819 23.5999 8.39972 23.3014 8.39972 22.9333V20.9336C8.39972 20.5654 8.10124 20.267 7.73305 20.267H5.99998Z"
                    fill="currentColor"
                  />
                </svg>
              )}

              {/* --------------- FULL SCREEN ------------------ */}
              {layoutType !== 'audio' && authorizeFullScreen && (
                <media-fullscreen-button class="media-button">
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 32 32"
                    fill="none"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    className={'vds-fs-enter-icon'}
                  >
                    <path
                      d="M25.3299 7.26517C25.2958 6.929 25.0119 6.66666 24.6667 6.66666H19.3334C18.9652 6.66666 18.6667 6.96514 18.6667 7.33333V9.33333C18.6667 9.70152 18.9652 10 19.3334 10L21.8667 10C21.9403 10 22 10.0597 22 10.1333V12.6667C22 13.0349 22.2985 13.3333 22.6667 13.3333H24.6667C25.0349 13.3333 25.3334 13.0349 25.3334 12.6667V7.33333C25.3334 7.31032 25.3322 7.28758 25.3299 7.26517Z"
                      fill="currentColor"
                    />
                    <path
                      d="M22 21.8667C22 21.9403 21.9403 22 21.8667 22L19.3334 22C18.9652 22 18.6667 22.2985 18.6667 22.6667V24.6667C18.6667 25.0349 18.9652 25.3333 19.3334 25.3333L24.6667 25.3333C25.0349 25.3333 25.3334 25.0349 25.3334 24.6667V19.3333C25.3334 18.9651 25.0349 18.6667 24.6667 18.6667H22.6667C22.2985 18.6667 22 18.9651 22 19.3333V21.8667Z"
                      fill="currentColor"
                    />
                    <path
                      d="M12.6667 22H10.1334C10.0597 22 10 21.9403 10 21.8667V19.3333C10 18.9651 9.70154 18.6667 9.33335 18.6667H7.33335C6.96516 18.6667 6.66669 18.9651 6.66669 19.3333V24.6667C6.66669 25.0349 6.96516 25.3333 7.33335 25.3333H12.6667C13.0349 25.3333 13.3334 25.0349 13.3334 24.6667V22.6667C13.3334 22.2985 13.0349 22 12.6667 22Z"
                      fill="currentColor"
                    />
                    <path
                      d="M10 12.6667V10.1333C10 10.0597 10.0597 10 10.1334 10L12.6667 10C13.0349 10 13.3334 9.70152 13.3334 9.33333V7.33333C13.3334 6.96514 13.0349 6.66666 12.6667 6.66666H7.33335C6.96516 6.66666 6.66669 6.96514 6.66669 7.33333V12.6667C6.66669 13.0349 6.96516 13.3333 7.33335 13.3333H9.33335C9.70154 13.3333 10 13.0349 10 12.6667Z"
                      fill="currentColor"
                    />
                  </svg>

                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 32 32"
                    fill="none"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    className="vds-fs-exit-icon"
                  >
                    <path
                      d="M19.3334 13.3333C18.9652 13.3333 18.6667 13.0349 18.6667 12.6667L18.6667 7.33333C18.6667 6.96514 18.9652 6.66666 19.3334 6.66666H21.3334C21.7015 6.66666 22 6.96514 22 7.33333V9.86666C22 9.9403 22.0597 10 22.1334 10L24.6667 10C25.0349 10 25.3334 10.2985 25.3334 10.6667V12.6667C25.3334 13.0349 25.0349 13.3333 24.6667 13.3333L19.3334 13.3333Z"
                      fill="currentColor"
                    />
                    <path
                      d="M13.3334 19.3333C13.3334 18.9651 13.0349 18.6667 12.6667 18.6667H7.33335C6.96516 18.6667 6.66669 18.9651 6.66669 19.3333V21.3333C6.66669 21.7015 6.96516 22 7.33335 22H9.86669C9.94032 22 10 22.0597 10 22.1333L10 24.6667C10 25.0349 10.2985 25.3333 10.6667 25.3333H12.6667C13.0349 25.3333 13.3334 25.0349 13.3334 24.6667L13.3334 19.3333Z"
                      fill="currentColor"
                    />
                    <path
                      d="M18.6667 24.6667C18.6667 25.0349 18.9652 25.3333 19.3334 25.3333H21.3334C21.7015 25.3333 22 25.0349 22 24.6667V22.1333C22 22.0597 22.0597 22 22.1334 22H24.6667C25.0349 22 25.3334 21.7015 25.3334 21.3333V19.3333C25.3334 18.9651 25.0349 18.6667 24.6667 18.6667L19.3334 18.6667C18.9652 18.6667 18.6667 18.9651 18.6667 19.3333L18.6667 24.6667Z"
                      fill="currentColor"
                    />
                    <path
                      d="M10.6667 13.3333H12.6667C13.0349 13.3333 13.3334 13.0349 13.3334 12.6667L13.3334 10.6667V7.33333C13.3334 6.96514 13.0349 6.66666 12.6667 6.66666H10.6667C10.2985 6.66666 10 6.96514 10 7.33333L10 9.86666C10 9.9403 9.94033 10 9.86669 10L7.33335 10C6.96516 10 6.66669 10.2985 6.66669 10.6667V12.6667C6.66669 13.0349 6.96516 13.3333 7.33335 13.3333L10.6667 13.3333Z"
                      fill="currentColor"
                    />
                  </svg>
                </media-fullscreen-button>
              )}
            </media-controls-group>
          </media-controls>
        </media-player>
      </div>
    );
  }
);

export default VidstackPlayerWrapper;
