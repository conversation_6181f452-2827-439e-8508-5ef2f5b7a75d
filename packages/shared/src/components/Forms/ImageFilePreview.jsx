import React, { useState } from 'react';
import { Button, Image } from 'antd';
import { Trash2 } from 'lucide-react';

export const ImageFilePreview = ({ previewUrl, size, handleFileRemove }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      onMouseOver={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ position: 'relative', borderRadius: 10, border: '1px solid #d9d9d9' }}
    >
      <div
        style={{
          position: 'absolute',
          top: -13,
          right: -13,
          zIndex: 9,
          background: 'white',
          borderRadius: '100%',
          opacity: isHovered ? 1 : 0,
          transition: '300ms ease'
        }}
      >
        <Button
          size="small"
          ghost
          danger
          shape="circle"
          icon={<Trash2 />}
          onClick={() => handleFileRemove()}
        />
      </div>
      <div
        style={{
          width: size,
          height: size,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <div
          style={{
            width: '100%',
            height: '100%',
            borderRadius: 12,
            overflow: 'hidden'
          }}
        >
          <Image src={previewUrl} width={size} height={size} />
        </div>
      </div>
    </div>
  );
};
