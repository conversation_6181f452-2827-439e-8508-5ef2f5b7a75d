import { getPublicSrc } from '@/shared/services/config';
import { buildQcmImage } from '@/shared/services/qcm';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils';
import { DeleteTwoTone, LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Image, Upload } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function ExoFormImage({
  label = 'Image',
  name = 'image',
  defaultValue, // Valeur par défaut de l'image
  beforeUpload, // Fonction appelée avant l'upload
  onDelete, // Fonction appelée lors de la suppression de l'image
  loading = false, // Indicateur de chargement
  size = 100,
  style = {},
  buttonStyle = null,
  round = false,
  showDeleteButton = true,
  fileType = 'protected' // Type de fichier image
}) {
  const { t } = useTranslation();

  const getBackendImageUrl = (fileName) => {
    if (!fileName) {
      return null; // undefined ou null
    }
    if (fileType === 'public') {
      return getPublicSrc(fileName);
    } else if (fileType === 'QCM') {
      return buildQcmImage(fileName);
    } else if (fileType === 'resolved')
      // url déjà résolu
      return fileName;
    return getUrlProtectedRessource(GlobalConfig.get().FILES_URL + fileName);
  };

  const [previewUrl, setPreviewUrl] = useState(getBackendImageUrl(defaultValue));

  useEffect(() => {
    // Mise à jour de l'URL de prévisualisation lors du changement de l'image par défaut
    setPreviewUrl(getBackendImageUrl(defaultValue));
  }, [defaultValue]);

  const beforeUploadImage = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target.result);
    };
    reader.readAsDataURL(file);
    beforeUpload(file);
    return false; // Empêche l'upload automatique
  };

  const handleRemove = () => {
    setPreviewUrl(null);
    onDelete(true);
  };

  const uploadContainerStyle = {
    borderRadius: round ? '12px' : '8px', // Bords arrondis
    height: size,
    width: size,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    border: '2px dashed #1890ff', // Bordure bleue en pointillés
    padding: '8px'
  };

  const imageContainerStyle = {
    width: size,
    height: size,
    borderRadius: round ? '50%' : '8px', // Bords arrondis si non rond
    overflow: 'hidden',
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    border: '2px solid #1890ff'
  };

  const imageStyle = {
    width: '100%',
    height: '100%',
    objectFit: 'cover'
  };

  return (
    <Form.Item
      name={name}
      style={{ position: 'relative', width: size, height: 'auto', textAlign: 'center', ...style }}
    >
      {previewUrl ? (
        <>
          <div style={imageContainerStyle}>
            <Image
              src={previewUrl}
              alt="Image Preview"
              style={imageStyle}
              width={size}
              height={size}
              preview={true}
            />
          </div>
          {showDeleteButton && (
            <Button
              icon={<DeleteTwoTone twoToneColor="#ff6347" />}
              onClick={handleRemove}
              style={buttonStyle ? buttonStyle : { marginTop: 10 }}
              size="small"
            />
          )}
        </>
      ) : (
        <Upload.Dragger
          name="image"
          listType="picture-card"
          showUploadList={false}
          accept="image/*"
          beforeUpload={beforeUploadImage}
          style={uploadContainerStyle}
        >
          {loading ? <LoadingOutlined /> : <PlusOutlined />}
          <div className="ant-upload-text">{t('Upload')}</div>
        </Upload.Dragger>
      )}
    </Form.Item>
  );
}
