import { QUERY_BREADCRUMB_DATA } from '@/shared/graphql/cours';
import { tr } from '@/shared/services/translate';
import { EllipsisOutlined, HomeOutlined } from '@ant-design/icons';
import { Breadcrumb } from 'antd';
import React, { useState } from 'react';
import { useQuery } from '@apollo/client';
import { Link } from 'umi';
import { ArrowRight } from 'lucide-react';

export default function CoursBreadcrumb({
  coursId,
  categoryId,
  ueId,
  withArborescenceAdminLinks = false,
  withRoot = false,
  onClick = null,
  inForum = false,
  showLastItem = false
}) {
  const [expanded, setExpanded] = useState(false);

  const { data } = useQuery(QUERY_BREADCRUMB_DATA, {
    variables: { ueId, categoryId, coursId },
    skip: !coursId && !ueId && !categoryId,
    fetchPolicy: 'cache-and-network'
  });

  const breadcrumbItems = data?.getBreadcrumbData || [];
  const total = breadcrumbItems.length;

  const linkPrefix = inForum
    ? '/discussions'
    : withArborescenceAdminLinks
      ? '/admin/permissions'
      : '/cours';

  const buildItem = (item) => {
    const { id, __typename } = item;
    let path = '';

    switch (__typename) {
      case 'UE':
        path = `${linkPrefix}/ue/${id}`;
        break;
      case 'UECategory':
        if (inForum) {
          path = `${linkPrefix}/ue-categorie/${id}`;
        } else {
          path = `${linkPrefix}/categorie/${id}`;
        }
        break;
      case 'Cours':
        path = `${linkPrefix}/${id}`;
        break;
    }

    const label = item?.[tr('name')] || item?.name || item?.title || `(${__typename})`;

    if (onClick) {
      const clickObj = {
        ueId: __typename === 'UE' ? id : null,
        ueCategoryId: __typename === 'UECategory' ? id : null,
        coursId: __typename === 'Cours' ? id : null
      };

      return {
        title: (
          <a onClick={() => onClick(clickObj)} style={{ height: '100%' }}>
            {label}
          </a>
        ),
        key: `${__typename}-${id}`
      };
    }

    return {
      title: (
        <Link to={path} style={{ height: '100%' }}>
          {label}
        </Link>
      ),
      key: `${__typename}-${id}`
    };
  };

  const buildItems = () => {
    if (total === 0) return [];

    const firstItem = buildItem(breadcrumbItems[0]);

    if (expanded || total <= 4) {
      const slice = showLastItem ? breadcrumbItems : breadcrumbItems.slice(0, -1);
      return slice.map(buildItem);
    }

    const collapsedItems = [
      firstItem,
      {
        key: 'ellipsis',
        title: (
          <a onClick={() => setExpanded(true)} style={{ height: '100%' }}>
            <EllipsisOutlined />
          </a>
        )
      }
    ];

    if (total >= 3 && !showLastItem) {
      const penultimateItem = buildItem(breadcrumbItems[total - 2]);
      collapsedItems.push(penultimateItem);
    }

    if (showLastItem) {
      const lastItem = buildItem(breadcrumbItems[total - 1]);
      collapsedItems.push(lastItem);
    }

    return collapsedItems;
  };

  const items = [];

  if (withRoot) {
    const rootLink = withArborescenceAdminLinks
      ? '/admin/permissions/'
      : inForum
        ? '/discussions'
        : null;

    const rootItem = {
      key: 'root',
      title: rootLink ? (
        <Link to={rootLink} style={{ height: '100%' }}>
          <HomeOutlined style={{ fontSize: 22 }} />
        </Link>
      ) : (
        <a
          onClick={() => onClick?.({ ueId: null, ueCategoryId: null, coursId: null })}
          style={{ height: '100%' }}
        >
          <HomeOutlined style={{ fontSize: 22 }} />
        </a>
      )
    };

    items.push(rootItem);
  }

  items.push(...buildItems());

  return (
    <Breadcrumb
      separator={<ArrowRight style={{ height: '100%' }} />}
      items={items}
      style={{ fontSize: 22, marginLeft: 12, marginBottom: 24, fontWeight: 'bold' }}
    />
  );
}
