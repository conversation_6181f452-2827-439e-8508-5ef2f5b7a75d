import React from 'react';

export function UserHeaderStats({ icon, label, number }) {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    >
      <span style={{ fontSize: 'x-large' }}>{icon}</span>
      <span style={{ fontWeight: 500 }}>{label}</span>
      <span>{number}</span>
    </div>
  );
}
