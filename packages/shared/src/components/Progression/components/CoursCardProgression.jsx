import {
  renderCourseNameAndDescriptionWithFallback,
} from '@/shared/services/translate.js';
import { BookTwoTone, QuestionCircleTwoTone } from '@ant-design/icons';
import { Card, Progress, Tooltip } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Textfit } from 'react-textfit';

export default function({ cours, onClick }) {
  const { t } = useTranslation();

  const progress = cours?.exercisesResultsSummary

  const color = cours?.ueCategory?.color || cours?.ue?.color;

  const renderScoreDetail = (goodAnswers, badAnswers) => {
    return (
      <>
        {goodAnswers} {goodAnswers > 1 ? t('proposals') : t('proposal')} {goodAnswers > 1 ? t('accuratePlural') : t('accurate')} {t('outOf')} {badAnswers + goodAnswers}
      </>
    );
  };

  return (
    <Card
      cover={(
        <div style={{
          height: '70px',
          backgroundColor: color,
          borderRadius: '10px 10px 0px 0px' }}
        />
      )}
      key={cours.id}
      style={{
        width: '275px',
        height: '340px',
        textAlign: 'center',
        border: 0,
      }}
      onClick={() => onClick(cours?.id)}
    >
      <div style={{ height: '100%' }}>

        <div style={{
          height: '80px',
          width: '80px',
          margin: 'auto',
          backgroundColor: 'white',
          borderRadius: '50%',
          border: 'solid 4px',
          borderColor: color,
          marginTop: '-90px',
          textAlign: 'center',
        }}
        >
          <BookTwoTone
            style={{ fontSize: 60 }}
            twoToneColor={color}
          />
        </div>


        {/* Name without description ? */}
        <div style={{
          marginTop: '20px',
          color: 'black',
          maxHeight: '54px',
          lineHeight: '25px',
          overflow: 'hidden',
          fontWeight: '700',
        }}
        >
          <Textfit
            mode="multi" // "single" ou "multi" pour plusieurs lignes
            max={25} // max font size en px (par défaut prend le plus de place possible)
            min={16}
          >
            {renderCourseNameAndDescriptionWithFallback(cours)}
          </Textfit>
        </div>

        {/*
        <div style={{ fontWeight: '700', marginTop: '5px', color: '#9A9A9A' }}>
          <Textfit
            mode="multi" // "single" ou "multi" pour plusieurs lignes
            max={15} // max font size en px (par défaut prend le plus de place possible)
            min={12} // min font size (default min=1)
            style={{
              height: '65px',
              lineHeight: '18px',
              overflow: 'hidden',
            }}
          >
            {renderUEDescriptionWithFallback(ue)}
          </Textfit>
        </div>
        */}

        {/* PROGRESS by good answers */}
        <Progress
          type="circle"
          strokeWidth={10}
          strokeColor={{
            '0%': '#108ee9',
            '100%':'#87d068',
          }}
          percent={((progress?.goodAnswers / (progress?.goodAnswers + progress?.badAnswers)) * 100).toFixed(1)}
          style={{ marginTop: 16 }}
        />
        <div style={{ marginTop: '20px', textAlign: 'right' }}>
          <Tooltip
            title={renderScoreDetail(progress?.goodAnswers, progress?.badAnswers)}
          >
            <QuestionCircleTwoTone/>
          </Tooltip>
        </div>
      </div>
    </Card>
  )
}