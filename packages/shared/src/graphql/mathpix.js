import {gql} from "@apollo/client";

export const QUERY_ADMIN_GET_MATHPIX_INTEGRATIONS=gql`
    query AdminGetMathpixConfigs {
        adminGetMathpixConfigs {
            name
            key
            id
        }
    }
`

export const QUERY_FETCH_MY_MATHPIX_CONFIG=gql`
    query GetMyMathpixConfigs {
        getMyMathpixConfigs {
            name
            id
        }
    }
`

export const MUTATION_LAUNCH_MATHPIX_UPLOAD = gql`
    mutation MathpixUploadPdf($file: Upload!,$mathpixConfigId:ID) {
        mathpixUploadPdf(file: $file,mathpixConfigId:$mathpixConfigId) {
            id
            hash
            algorithm
            mathpixFileId
            file
            description
            image
            fileName
            createdAt
            mathpixConfigId
            authorId
            mathpixieAuthor {
                id
                username
                firstName
                name
                email
                role
                avatar
                isActive
                createdAt
                updatedAt
            }
        }
    }
`;

export const QUERY_ADMIN_DATA_MATHPIXIES=gql`
    query AdminQueryMathpixies($mathpixConfigId: ID!) {
        adminQueryMathpixies(mathpixConfigId: $mathpixConfigId) {
            id
            hash
            algorithm
            mathpixFileId
            file
            description
            image
            fileName
            createdAt
            mathpixConfigId
            authorId
            mathpixieAuthor {
                id
                username
                firstName
                name
                email
                role
                avatar
                isActive
                createdAt
                updatedAt
            }
        }
    }
`

export const MUTATION_ADD_GROUPE_MATHPIX_CONFIG_ACCES=gql`
    mutation AddGroupToMathpixIntegrationId($mathpixIntegrationId: ID, $groupId: ID) {
        addGroupToMathpixIntegrationId(mathpixIntegrationId: $mathpixIntegrationId, groupId: $groupId)
    }
`

export const MUTATION_REMOVE_GROUPE_MATHPIX_CONFIG_ACCES=gql`
    mutation RemoveGroupeToMathpixIntegrationId($mathpixIntegrationId: ID, $groupId: ID) {
        removeGroupeToMathpixIntegrationId(mathpixIntegrationId: $mathpixIntegrationId, groupId: $groupId)
    }
`

export const QUERY_AUTHORIZED_GROUPS=gql`
    query AdminGetGroupsAuthorizedForMathpixIntegration($mathpixIntegrationId: ID) {
        adminGetGroupsAuthorizedForMathpixIntegration(mathpixIntegrationId: $mathpixIntegrationId) {
            id
            name
            isIndividual
        }
    }
`

export const MUTATION_DELETE_MATHPIXIE=gql`
    mutation DeleteMathpixie($mathpixieId: ID!, $mathpixConfigId: ID!) {
        deleteMathpixie(mathpixieId: $mathpixieId, mathpixConfigId: $mathpixConfigId)
    }
`

export const QUERY_PROCESS_IMAGE_MATHPIX=gql`
    query ProcessImage($file: Upload!, $mathpixConfigId: ID) {
        processImage(file: $file, mathpixConfigId: $mathpixConfigId) {
            latex
            text
        }
    }
`