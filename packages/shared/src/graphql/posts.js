import { gql } from '@apollo/client';

export const POST_FRAGMENT = gql`
  fragment PostFields on Post {
    id
    parentId
    isResolved
    user {
      id
      username
      avatar
      role
      isActive
      bot
    }
    file {
      id
      file
      name
      mimetype
    }
    fileImage {
      id
      file
      name
      mimetype
    }
    fileList {
      id
      file
      name
      mimetype
    }
    type {
      id
      name
      image
    }
    answerId
    qcmIdQcm
    courId
    forumId
    eventId
    questionId
    title
    text
    views
    likes
    tag
    createdAt
    updatedAt
    myLastLikeAction
    state
    userIdForAiFeedback
    verifiedBy
    isAskingForHumanHelp
    answeredByAi
    isResolvedByAi
    like_histories {
      userId
    }
    isResolved
  }
`;

export const POST_LIMIT_FRAGMENT = gql`
  fragment LimitRuleFields on LimitRule {
    id
    userId
    limitNumber
    isActive
    timeWindow
    createdAt
    updatedAt
  }
`;

export const GET_MY_POST_LIMIT_RULE = gql`
  ${POST_LIMIT_FRAGMENT}
  query getMyPostLimitationRule {
    getMyPostLimitationRule {
      ...LimitRuleFields
    }
  }
`;

export const GET_USER_POST_LIMIT_RULE = gql`
  ${POST_LIMIT_FRAGMENT}
  query postLimitationRuleForUserId($userId: ID!) {
    postLimitationRuleForUserId(userId: $userId) {
      ...LimitRuleFields
    }
  }
`;

export const GET_CAN_POST_DISCUSSION = gql`
  query canPostDiscussion {
    canPostDiscussion
  }
`;

//////// POST REACTIONS /////////
export const QUERY_GET_POST_REACTIONS = gql`
  query getPostReactions($postId: ID!) {
    getPostReactions(postId: $postId) {
      count
      emoji
      isUserReaction
    }
  }
`;
export const MUTATION_POST_REACTION = gql`
  mutation postReaction($postId: ID!, $emoji: String!) {
    postReaction(postId: $postId, emoji: $emoji)
  }
`;
//////// END POST REACTIONS //////

export const CREATE_USER_POST_LIMITATION_RULE = gql`
  ${POST_LIMIT_FRAGMENT}
  mutation createPostLimitationRule($limitRuleInput: LimitRuleInput!) {
    createPostLimitationRule(limitRuleInput: $limitRuleInput) {
      ...LimitRuleFields
    }
  }
`;

export const UPDATE_USER_POST_LIMITATION_RULE = gql`
  mutation UpdatePostLimitationRule(
    $updatePostLimitationRuleId: ID!
    $limitRuleInput: LimitRuleInput!
  ) {
    updatePostLimitationRule(id: $updatePostLimitationRuleId, limitRuleInput: $limitRuleInput)
  }
`;

/* POST queries */
export const QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_COURS = gql`
  query getYearsAvailableForPostsForCours($coursId: ID!) {
    getYearsAvailableForPostsForCours(coursId: $coursId)
  }
`;
export const QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_QCM = gql`
  query getYearsAvailableForPostsForQcm($qcmId: ID!) {
    getYearsAvailableForPostsForQcm(qcmId: $qcmId)
  }
`;
export const QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_FORUM = gql`
  query getYearsAvailableForPostsForForum($forumId: ID!) {
    getYearsAvailableForPostsForForum(forumId: $forumId)
  }
`;
export const QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_EVENT = gql`
  query getYearsAvailableForPostsForEvent($eventId: ID!) {
    getYearsAvailableForPostsForEvent(eventId: $eventId)
  }
`;

export const QUERY_POST_BY_ID = gql`
  ${POST_FRAGMENT}
  query getPostById($id: ID!) {
    post(id: $id) {
      ...PostFields
    }
  }
`;

export const QUERY_USER_THREAD_POSTS = gql`
  ${POST_FRAGMENT}
  query userThreadPosts($userId: ID, $offset: Int, $limit: Int) {
    userThreadPosts(userId: $userId, offset: $offset, limit: $limit) {
      posts {
        ...PostFields
        cours {
          id
          name
          name_en
          name_de
          name_es
          name_it
          text
          text_en
          text_de
          text_es
          text_it
          customImage
          ueCategory {
            ue {
              image
              name
            }
          }
        }
        forum {
          id
          name
          description
          image
        }
        qcm {
          id_qcm
          titre
          description
          UE {
            image
            name
          }
        }
      }
      count
    }
  }
`;

export const QUERY_USER_ANSWER_POSTS = gql`
  ${POST_FRAGMENT}
  query userAnswerPosts($userId: ID, $offset: Int, $limit: Int) {
    userAnswerPosts(userId: $userId, offset: $offset, limit: $limit) {
      posts {
        ...PostFields
        cours {
          id
          name
          name_en
          name_de
          name_es
          name_it
          text
          text_en
          text_de
          text_es
          text_it
          customImage
          ueCategory {
            ue {
              image
              name
            }
          }
        }
        forum {
          id
          name
          description
          image
        }
        qcm {
          id_qcm
          titre
          description
          UE {
            image
            name
          }
        }
      }
      count
    }
  }
`;

export const QUERY_ADMIN_POST_SEARCH = gql`
  ${POST_FRAGMENT}
  query AdminSearchPosts($filter: AdminPostSearchFilter) {
    adminSearchPosts(filter: $filter) {
      count
      postResults {
        ...PostFields
      }
    }
  }
`;

export const QUERY_LATESTS_POSTS = gql`
  query latestPosts {
    latestPosts {
      id
      parentId
      isResolved
      user {
        id
        username
        avatar
        role
        bot
        isActive
      }
      cours {
        id
        name
        name_en
        name_es
        name_it
        name_de
        ueCategory {
          name
          ue {
            name
            name_en
            name_es
            name_it
            name_de
          }
        }
      }
      qcm {
        id_qcm
        titre
        titre_en
        titre_es
        titre_it
        titre_de
        UE {
          name
          name_en
          name_es
          name_it
          name_de
        }
      }
      forum {
        name
      }
      threadId
      courId
      qcmIdQcm
      forumId
      answerId
      questionId
      eventId
      title
      text
      views
      likes
      tag
      createdAt
      updatedAt
    }
  }
`;
export const SEARCH_POST_TYPE = gql`
  query postTypes {
    postTypes {
      id
      name
      image
      type
      precisionPromptForChatGPT
      firstAnswerByChatGPT
      otherAnswersByChatGPT
    }
  }
`;

/* MUTATIONS on posts */
export const CREATE_POST = gql`
  mutation createPost($post: PostInput!) {
    createPost(post: $post) {
      id
    }
  }
`;
export const UPDATE_POST = gql`
  mutation updatePost($id: ID!, $post: PostInput!) {
    updatePost(id: $id, post: $post)
  }
`;
export const DELETE_POST = gql`
  mutation deletePost($id: ID!) {
    deletePost(id: $id)
  }
`;

export const GIVE_FEEDBACK_AI_ANSWER = gql`
  mutation giveFeedbackToAiAnswer($input: FeedbackToAiAnswerInput) {
    giveFeedbackToAiAnswer(input: $input)
  }
`;
export const SET_AI_ANSWER_VERIFIED = gql`
  mutation setAiAnswerVerified($postId: ID!) {
    setAiAnswerVerified(postId: $postId)
  }
`;

export const GENERATE_AI_ANSWER_FOR_POST = gql`
  mutation generateAiAnswerForPost(
    $postId: ID!
    $aiUserId: ID
    $additionnalPrompt: String
    $originalThreadPostId: ID
  ) {
    generateAiAnswerForPost(
      postId: $postId
      aiUserId: $aiUserId
      additionnalPrompt: $additionnalPrompt
      originalThreadPostId: $originalThreadPostId
    )
  }
`;

export const LIKE_POST = gql`
  mutation likePost($id: ID!) {
    likePost(id: $id) {
      id
      likes
    }
  }
`;
export const DISLIKE_POST = gql`
  mutation dislikePost($id: ID!) {
    dislikePost(id: $id) {
      id
      likes
    }
  }
`;
/* END POST MUTATIONS */
