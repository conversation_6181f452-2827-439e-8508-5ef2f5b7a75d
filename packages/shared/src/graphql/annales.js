import { gql } from '@apollo/client'

export const GET_ANNALES_FOR_UE_ID = gql`
    query annalesInUE($ueId: ID!) {
        annalesInUE(ueId: $ueId) {
            id
            name
            text
            type
            pdf
            epub
            workTime
            tips
            version
            author {
                id
                username
                avatar
            }
            is<PERSON><PERSON><PERSON>
            views
            updateInfos
            difficulty
            createdAt
            updatedAt
        }
    }
`