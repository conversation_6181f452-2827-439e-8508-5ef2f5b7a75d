import { CORE_FORMATION_ELEMENT_FIELDS } from '@/shared/graphql/formations.js';
import { POST_FRAGMENT } from '@/shared/graphql/posts.js';
import { FRAGMENT_UES } from '@/shared/graphql/UE.js';
import { gql } from '@apollo/client';

export const FRAGMENT_UECATEGORIES = gql`
  fragment UECategoryFragment on UECategory {
    id
    name
    name_en
    name_es
    name_it
    name_de
    description
    description_en
    description_es
    description_it
    description_de
    isVisible
    color
    order
    image
    countAccessiblesCourses
    countAccessiblesExercises
    createdAt
    updatedAt
    parentId
  }
`;

export const FRAGMENT_COURS_FOR_NAVIGATION = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  fragment CoursInUeCategoryFragment on Cours {
    id
    name
    name_en
    name_es
    name_it
    name_de
    text
    text_en
    text_es
    text_it
    text_de
    type
    difficulty
    isAnnale
    pdfPreviews
    customImage
    views
    numberOfComments
    layout
    date
    order
    isVisible
    isNew
    countAccessiblesExercises
    datesDiffusion {
      id
      date
      show
      groupes {
        id
        name
      }
    }
    ueCategory {
      ...UECategoryFragment
      ue {
        ...UEFragment
      }
    }
  }
`;

export const FRAGMENT_COURS_LIGHT = gql`
  fragment CoursFragment on Cours {
    id
    name
    name_en
    name_es
    name_it
    name_de
    text
    text_en
    text_es
    text_it
    text_de
    type
    customImage
    difficulty
    isAnnale
    pdfPreviews
    views
    numberOfComments
    customImage
    layout
    date
    order
    isVisible
    isNew
    pdf
    countAccessiblesExercises
    targetCours {
      id
      isVisible
      type
      name
      order
      name_en
      name_es
      name_it
      name_de
      text
      text_en
      text_es
      text_it
      text_de
      countAccessiblesExercises
      customImage
      numberOfComments
      layout
      isNew
      pdfPreviews
    }
    datesDiffusion {
      id
      date
      show
    }
  }
`;

/* END FRAGMENTS */

/* UES */
export const QUERY_CURRENT_UE_WITH_CATEGORIES = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  query ue($id: ID!) {
    ue(id: $id) {
      ...UEFragment
      ueCategories {
        ...UECategoryFragment
      }
    }
  }
`;

export const QUERY_UE_CATEGORIES_FOR_UE = gql`
  ${FRAGMENT_UECATEGORIES}
  query ueCategories($ueId: ID!) {
    ueCategories(ueId: $ueId) {
      ...UECategoryFragment
    }
  }
`;

export const QUERY_COUNT_ACCESSIBLE_COURSES_IN_UE = gql`
  query countAccessiblesCoursesInUE($ueId: ID!) {
    countAccessiblesCoursesInUE(ueId: $ueId)
  }
`;
export const QUERY_COUNT_ACCESSIBLE_EXERCISES_IN_UE = gql`
  query countAccessiblesExercisesInUE($ueId: ID!) {
    countAccessiblesExercisesInUE(ueId: $ueId)
  }
`;

export const QUERY_UE_CATEGORIES_FOR_UE_WITH_COURSES = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_COURS_LIGHT}
  query ueCategories($ueId: ID!) {
    ueCategories(ueId: $ueId) {
      ...UECategoryFragment
      cours {
        ...CoursFragment
      }
    }
  }
`;

export const QUERY_UE_CATEGORIES_COURS_FOR_UE_FORUMS = gql`
  ${FRAGMENT_UECATEGORIES}
  query ueCategories($ueId: ID!) {
    ueCategories(ueId: $ueId) {
      ...UECategoryFragment
      postsNumber
      lastPost {
        id
        title
        text
        updatedAt
        threadId
        courId
        qcmIdQcm
        eventId
        forumId
        parentId
        isResolved
        answerId
        user {
          id
          username
          avatar
        }
      }
    }
  }
`;

// UE by ID
export const QUERY_UE_ID = gql`
  ${FRAGMENT_UES}
  query Ue($id: ID!) {
    ue(id: $id) {
      ...UEFragment
      tuteurs {
        id
        name
        isActive
        username
        avatar
        isReachableByPrivateMessage
        appearsInTeam
      }
    }
  }
`;
export const QUERY_UE_ID_WITH_GROUPS = gql`
  ${FRAGMENT_UES}
  query Ue($id: ID!) {
    ue(id: $id) {
      ...UEFragment
      groupes {
        id
        name
        isIndividual
      }
    }
  }
`;

export const QUERY_UE_CATEGORY_ID_WITH_GROUPS = gql`
  ${FRAGMENT_UECATEGORIES}
  query ueCategory($id: ID!) {
    ueCategory(id: $id) {
      ...UECategoryFragment
      groupes {
        id
        name
        isIndividual
      }
    }
  }
`;

export const QUERY_UE_BY_ID_WITH_CHILDREN_AND_TEACHERS = gql`
  ${FRAGMENT_UES}
  query Ue($id: ID!) {
    ue(id: $id) {
      ...UEFragment
      children {
        ...UEFragment
      }
      tuteurs {
        id
        name
        isActive
        username
        avatar
        isReachableByPrivateMessage
        appearsInTeam
      }
    }
  }
`;

export const QUERY_UE_BY_ID_WITH_CHILDREN = gql`
  ${FRAGMENT_UES}
  query Ue($id: ID!) {
    ue(id: $id) {
      ...UEFragment
      children {
        ...UEFragment
      }
    }
  }
`;

export const QUERY_USER_UES_ACCESSIBLE = gql`
  ${FRAGMENT_UES}
  query UEsAccessibleFor($userId: ID!) {
    UEsAccessibleFor(userId: $userId) {
      ...UEFragment
    }
  }
`;

// UE
export const QUERY_UE_ID_FORUM = gql`
  ${FRAGMENT_UES}
  query Ue($id: ID!) {
    ue(id: $id) {
      ...UEFragment
      tuteurs {
        id
        name
        isActive
        username
        avatar
      }
      postsNumber {
        cours
        qcm
        annale
        total
      }
      lastPostInCours {
        id
        title
        text
        updatedAt
        threadId
        courId
        qcmIdQcm
        forumId
        parentId
        isResolved
        user {
          id
          username
          avatar
        }
      }
      lastPostInQcm {
        id
        title
        text
        updatedAt
        threadId
        courId
        qcmIdQcm
        forumId
        parentId
        isResolved
        user {
          id
          username
          avatar
        }
      }
      lastPostInAnnale {
        id
        title
        text
        updatedAt
        threadId
        courId
        qcmIdQcm
        forumId
        parentId
        isResolved
        user {
          id
          username
          avatar
        }
      }
    }
  }
`;

export const QUERY_UE_CATEGORY_ID = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  query ueCategory($id: ID!) {
    ueCategory(id: $id) {
      ...UECategoryFragment
      ue {
        ...UEFragment
      }
    }
  }
`;

export const QUERY_UE_CATEGORY_ID_WITH_CHILDREN = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  query ueCategory($id: ID!) {
    ueCategory(id: $id) {
      ...UECategoryFragment
      parent {
        id
        ue {
          id
          color
          color2
        }
      }
      children {
        ...UECategoryFragment
      }
      ue {
        ...UEFragment
      }
    }
  }
`;

export const QUERY_UE_CATEGORY_ID_WITH_CHILDREN_AND_COURSES = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  ${FRAGMENT_COURS_LIGHT}
  query ueCategory($id: ID!) {
    ueCategory(id: $id) {
      ...UECategoryFragment
      children {
        ...UECategoryFragment
      }
      ue {
        ...UEFragment
      }
      cours {
        ...CoursFragment
      }
    }
  }
`;

export const UPDATE_UE = gql`
  mutation updateUE($id: ID!, $ue: UEInput!) {
    updateUE(id: $id, ue: $ue)
  }
`;
export const CREATE_UE = gql`
  mutation addUe($ue: UEInput!) {
    addUe(ue: $ue) {
      id
      name
    }
  }
`;
export const DELETE_UE = gql`
  mutation deleteUE($id: ID!) {
    deleteUE(id: $id)
  }
`;

// UE categories
export const CREATE_UECATEGORY = gql`
  mutation createUECategory($category: UECategoryInput!) {
    createUECategory(category: $category) {
      id
    }
  }
`;
export const UPDATE_UECATEGORY = gql`
  mutation updateUECategory($id: ID!, $category: UECategoryInput!) {
    updateUECategory(id: $id, category: $category)
  }
`;
export const DELETE_UECATEGORY = gql`
  mutation deleteUECategory($id: ID!) {
    deleteUECategory(id: $id)
  }
`;

export const QUERY_COURS_IN_UECATEGORY = gql`
  ${FRAGMENT_COURS_FOR_NAVIGATION}
  query ueCategories($ueCategoryId: ID!) {
    coursInUECategory(ueCategoryId: $ueCategoryId) {
      ...CoursInUeCategoryFragment
      targetCoursId
      targetCours {
        ...CoursInUeCategoryFragment
      }
    }
  }
`;

export const QUERY_COURSES_IN_UE = gql`
  ${FRAGMENT_COURS_FOR_NAVIGATION}
  query coursInUE($ueId: ID!) {
    coursInUE(ueId: $ueId) {
      ...CoursInUeCategoryFragment
      targetCoursId
      targetCours {
        ...CoursInUeCategoryFragment
      }
    }
  }
`;

export const QUERY_COURS_IN_UECATEGORY_FORUM = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  query ueCategories($ueCategoryId: ID!) {
    coursInUECategory(ueCategoryId: $ueCategoryId) {
      id
      name
      name_en
      name_es
      name_it
      name_de
      text
      text_en
      text_es
      text_it
      text_de
      type
      difficulty
      pdfPreviews
      layout
      customImage
      isAnnale
      views
      numberOfComments
      date
      isVisible
      isNew
      settings
      countAccessiblesExercises
      datesDiffusion {
        id
        date
        show
        groupes {
          id
          name
        }
      }
      ueCategory {
        ...UECategoryFragment
        ue {
          ...UEFragment
        }
      }
      lastPost {
        id
        title
        isResolved
        answerId
        text
        updatedAt
        threadId
        courId
        eventId
        qcmIdQcm
        forumId
        parentId
        user {
          id
          username
          avatar
        }
      }
    }
  }
`;

export const QUERY_COURS_IN_UECATEGORY_MINIMAL = gql`
  query coursInUECategoryMinimal($ueCategoryId: ID!) {
    coursInUECategory(ueCategoryId: $ueCategoryId) {
      id
      name
      name_en
      name_es
      name_it
      name_de
      text
      text_en
      text_es
      text_it
      text_de
      order
    }
  }
`;

export const QUERY_COURS_POST_DETAIL = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  query cour($id: ID!) {
    cour(id: $id) {
      id
      name
      name_en
      name_es
      name_it
      name_de
      text
      text_en
      text_es
      text_it
      text_de
      settings
      ueCategory {
        ...UECategoryFragment
        ue {
          ...UEFragment
        }
      }
    }
  }
`;

export const QUERY_COURS_GROUPS = gql`
  query cour($id: ID!) {
    cour(id: $id) {
      id
      name
      name_en
      name_es
      name_it
      name_de
      text
      text_en
      text_es
      text_it
      text_de
      settings
      groupes {
        id
        name
        isIndividual
      }
    }
  }
`;

export const QUERY_MY_CUSTOM_COURSES_TABS = gql`
  query myCustomCoursesTabs($domain: String) {
    myCustomCoursesTabs(domain: $domain)
  }
`;

export const QUERY_MES_UES = gql`
  ${FRAGMENT_UES}
  query mesUEs {
    mesUEs {
      ...UEFragment
      tuteurs {
        id
        name
        username
      }
    }
  }
`;

export const QUERY_EVERY_COURS_NAVIGATION = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  query mesUEsCategoriesCours {
    mesUEs {
      ...UEFragment
      tuteurs {
        id
        name
        username
      }
      ueCategories {
        ...UECategoryFragment
        cours {
          id
          isVisible
          name
          name_en
          name_es
          name_it
          name_de
          text
          text_en
          text_es
          text_it
          text_de
          targetCours {
            id
            isVisible
            name
            name_en
            name_es
            name_it
            name_de
            text
            text_en
            text_es
            text_it
            text_de
          }
        }
      }
    }
  }
`;

export const QUERY_MES_UES_FORUM = gql`
  ${FRAGMENT_UES}
  query mesUEsForum {
    mesUEs {
      ...UEFragment
      postsNumber {
        cours
        qcm
        annale
        total
      }
      lastPost {
        id
        title
        text
        updatedAt
        threadId
        courId
        eventId
        qcmIdQcm
        forumId
        answerId
        parentId
        isResolved
        user {
          id
          username
          avatar
        }
      }
      tuteurs {
        id
        name
        username
      }
    }
  }
`;

//TODO move groups queries
export const QUERY_ALL_GROUPS = gql`
  query {
    allGroupes {
      id
      name
      role
      folderId
      createdAt
      updatedAt
      image
      numberOfUsers
      isIndividual
    }
  }
`;

export const QUERY_GROUPS_FOR_PLANNING = gql`
  query {
    groupsForPlanning {
      id
      name
      folderId
      role
      image
      isIndividual
      createdAt
      updatedAt
    }
  }
`;

export const QUERY_ALL_UES = gql`
  ${FRAGMENT_UES}
  query AllUEs {
    allUEs {
      ...UEFragment
      tuteurs {
        id
        name
        username
        avatar
        likesReceived
      }
      parent {
        ...UEFragment
      }
      groupes {
        id
        name
      }
    }
  }
`;

export const QUERY_ALL_UES_FOR_QCM = gql`
  ${FRAGMENT_UES}
  query AllUEsForMCQ {
    allUEs {
      ...UEFragment
    }
  }
`;

export const QUERY_ALL_UE_WITH_CATEGORIES = gql`
  ${FRAGMENT_UES}
  ${FRAGMENT_UECATEGORIES}
  query {
    allUEs {
      ...UEFragment
      ueCategories {
        ...UECategoryFragment
      }
    }
  }
`;

export const QUERY_ALL_UE_WITH_CATEGORIES_COURS_LIGHT = gql`
  ${FRAGMENT_UES}
  ${FRAGMENT_UECATEGORIES}
  query {
    allUEs {
      ...UEFragment
      ueCategories {
        ...UECategoryFragment
        cours {
          id
          isVisible
          name
          name_en
          name_es
          name_it
          name_de
          text
          text_en
          text_es
          text_it
          text_de
          customImage
        }
      }
    }
  }
`;
export const QUERY_MES_UES_LIGHT = gql`
  ${FRAGMENT_UES}
  query mesUEs($forUser: ID) {
    mesUEs(forUser: $forUser) {
      ...UEFragment
    }
  }
`;
export const QUERY_HIERARCHY_TREE = gql`
  query HierarchicalTreeData(
    $selectable: Boolean
    $forUserId: ID
    $getWatermarkCoursData: Boolean = false
    $forUeId: ID = null
  ) {
    hierarchicalTreeData(
      selectable: $selectable
      forUserId: $forUserId
      getWatermarkCoursData: $getWatermarkCoursData
      forUeId: $forUeId
    )
  }
`;

export const QUERY_ALL_UE_WITH_CATEGORIES_AND_QUESTIONS = gql`
  ${FRAGMENT_UES}
  ${FRAGMENT_UECATEGORIES}
  query {
    allUEs {
      ...UEFragment
      ueCategories {
        ...UECategoryFragment
        nombreQuestions
        nombreQuestionsNonConnectees
      }
    }
  }
`;

// COURS //////////////////////////////////////
export const QUERY_COURS_DATES_DIFFUSION = gql`
  query datesDiffusionInCour($id: ID!) {
    datesDiffusionInCour(id: $id) {
      id
      date
      dateEnd
      allDay
      show
      groupes {
        id
        name
      }
      updateInfos
    }
  }
`;
export const QUERY_COURS_NOTION_AUTOADDED = gql`
  query notionsAutoAddedInCour($id: ID!) {
    notionsAutoAddedInCour(id: $id) {
      id
      name
      image
    }
  }
`;
export const QUERY_COURS_NOTION_MANUAL = gql`
  query notionsManuallyAddedInCour($id: ID!) {
    notionsManuallyAddedInCour(id: $id) {
      id
      name
      image
    }
  }
`;
export const QUERY_COURS_FILES = gql`
  query filesInCour($id: ID!) {
    filesInCour(id: $id) {
      id
      name
      file
      image
      mimetype
    }
  }
`;
export const QUERY_COURS_VIDEOS = gql`
  query videosInCour($id: ID!) {
    videosInCour(id: $id) {
      id
      name
      file
      image
      mimetype
      externalLink
    }
  }
`;
export const QUERY_COURS_FICHES = gql`
  query fichesInCour($id: ID!) {
    fichesInCour(id: $id) {
      id
      name
      file
      image
      isAccessible
      groups {
        id
        name
      }
    }
  }
`;

export const DETAIL_COURS_FRAGEMENT = gql`
  ${FRAGMENT_UES}
  ${FRAGMENT_UECATEGORIES}
  fragment DetailCoursFragment on Cours {
    id
    name
    name_en
    name_es
    name_it
    name_de
    text
    text_en
    text_es
    text_it
    text_de
    type
    layout
    video
    pdfPreviews
    customImage
    numberOfPagesPdf
    formationId
    isEnAvant
    pdf
    epub
    workTime
    tips
    date
    duration
    version
    settings
    deleted
    numberOfQuestionsLinked
    gptPrecisionPrompt
    isReviewEnabled
    isFeedbackVisible
    author {
      id
      username
      avatar
    }
    isAnnale
    isNew
    views
    updateInfos
    difficulty
    groupes {
      id
      name
      isIndividual
    }
    typeQcmSettings {
      typeQcm {
        id
        name
      }
      coursId
      typeQcmId
      coursModuleType
    }
    ueCategory {
      ...UECategoryFragment
      ue {
        ...UEFragment
      }
    }
    ue {
      ...UEFragment
    }
    createdAt
    updatedAt
  }
`;

export const QUERY_DETAILS_COURS = gql`
  ${DETAIL_COURS_FRAGEMENT}
  query cour($id: ID!) {
    cour(id: $id) {
      ...DetailCoursFragment
      targetCours {
        ...DetailCoursFragment
      }
    }
  }
`;

export const QUERY_COURS_NEWS_LIST = gql`
  ${DETAIL_COURS_FRAGEMENT}
  query coursNewsList {
    coursNewsList {
      ...DetailCoursFragment
      targetCoursId
      targetCours {
        ...DetailCoursFragment
      }
    }
  }
`;

export const QUERY_EXERCISES_SERIES_FOR_COURS = gql`
  ${FRAGMENT_UECATEGORIES}
  query exercisesSeriesForCours($id: ID!) {
    exercisesSeriesForCours(id: $id) {
      id_qcm
      id_lien
      external
      date_creation
      date_modif
      annee
      annale
      external
      url_image
      id_createur
      pseudo_createur
      difficulty
      ue
      titre
      titre_en
      titre_es
      titre_it
      titre_de
      description
      description_en
      description_es
      description_it
      description_de
      nombreQuestions
      ueCategoriesImpliquees {
        ...UECategoryFragment
      }
      resultat {
        id
        moyenne
        date
        note
      }
    }
  }
`;

export const QUERY_COMMENTAIRES_COURS = gql`
  ${POST_FRAGMENT}
  query postsForCours($coursId: ID!, $filter: PostFilter) {
    postsForCours(coursId: $coursId, filter: $filter) {
      ...PostFields
    }
  }
`;

/* CRUD COURS */
export const CREATE_COURS = gql`
  mutation createCours($cours: CoursInput!, $disableNotionAnalysis: Boolean) {
    createCours(cours: $cours, disableNotionAnalysis: $disableNotionAnalysis) {
      id
    }
  }
`;
export const UPDATE_COURS = gql`
  mutation updateCours($id: ID!, $cours: CoursInput!, $disableNotionAnalysis: Boolean) {
    updateCours(id: $id, cours: $cours, disableNotionAnalysis: $disableNotionAnalysis)
  }
`;
export const DELETE_COURS = gql`
  mutation deleteCours($id: ID!) {
    deleteCours(id: $id)
  }
`;
export const EXTRACT_NOTIONS_COURS = gql`
  mutation extractNotionsFromPdf($coursId: ID!) {
    extractNotionsFromPdf(coursId: $coursId)
  }
`;

// FICHES
export const CREATE_FICHE = gql`
  mutation createFiche($fiche: FicheInput!) {
    createFiche(fiche: $fiche) {
      id
    }
  }
`;
export const UPDATE_FICHE = gql`
  mutation updateFiche($id: ID!, $fiche: FicheInput!) {
    updateFiche(id: $id, fiche: $fiche)
  }
`;
export const DELETE_FICHE = gql`
  mutation deleteFiche($id: ID!) {
    deleteFiche(id: $id)
  }
`;
export const ADD_GROUP_TO_FICHE = gql`
  mutation addGroupToFiche($id: ID, $groupId: ID) {
    addGroupToFiche(id: $id, groupId: $groupId)
  }
`;
export const REMOVE_GROUP_FROM_FICHE = gql`
  mutation removeGroupFromFiche($id: ID, $groupId: ID) {
    removeGroupFromFiche(id: $id, groupId: $groupId)
  }
`;

// FILES / AUTRES FICHIERS UTILES
export const CREATE_FILE = gql`
  mutation createFile($file: FileInput!) {
    createFile(file: $file) {
      id
    }
  }
`;
export const UPDATE_FILE = gql`
  mutation updateFile($id: ID!, $file: FileInput!) {
    updateFile(id: $id, file: $file)
  }
`;
export const DELETE_FILE = gql`
  mutation deleteFile($id: ID!) {
    deleteFile(id: $id)
  }
`;

/* Cours Supports */
export const QUERY_COURS_SUPPORT_IN_COURS = gql`
  query coursSupportsInCours($id: ID!) {
    coursSupportsInCours(id: $id) {
      id
      coursId
      authorId
      name
      order
      createdAt
      updatedAt
    }
  }
`;
export const QUERY_ELEMENTS_IN_SUPPORT_COURS = gql`
  ${CORE_FORMATION_ELEMENT_FIELDS}
  query elementsInCoursSupport($id: ID!) {
    elementsInCoursSupport(id: $id) {
      ...CoreFormationElementFields
    }
  }
`;
export const QUERY_ELEMENTS_IN_EVENT = gql`
  ${CORE_FORMATION_ELEMENT_FIELDS}
  query elementsInEvent($id: ID!) {
    elementsInEvent(id: $id) {
      ...CoreFormationElementFields
    }
  }
`;
export const QUERY_ELEMENTS_IN_CHALLENGE = gql`
  ${CORE_FORMATION_ELEMENT_FIELDS}
  query elementsInChallenge($id: ID!) {
    elementsInChallenge(id: $id) {
      ...CoreFormationElementFields
    }
  }
`;
export const QUERY_ELEMENTS_IN_QUESTION = gql`
  ${CORE_FORMATION_ELEMENT_FIELDS}
  query elementsInQuestion($id: ID!) {
    elementsInQuestion(id: $id) {
      ...CoreFormationElementFields
    }
  }
`;
export const QUERY_ELEMENTS_IN_QUESTION_FOOTER = gql`
  ${CORE_FORMATION_ELEMENT_FIELDS}
  query elementsInQuestionFooter($id: ID!) {
    elementsInQuestionFooter(id: $id) {
      ...CoreFormationElementFields
    }
  }
`;

export const QUERY_QUESTION_COLOR_FROM_QUESTION = gql`
  query getQuestionColor($getQuestionColorId: ID!) {
    getQuestionColor(id: $getQuestionColorId)
  }
`;

export const QUERY_ELEMENTS_IN_HEADER_QCM = gql`
  ${CORE_FORMATION_ELEMENT_FIELDS}
  query elementsInQcmHeader($id: ID!) {
    elementsInQcmHeader(id: $id) {
      ...CoreFormationElementFields
    }
  }
`;

export const QUERY_ELEMENTS_IN_FORFAIT = gql`
  ${CORE_FORMATION_ELEMENT_FIELDS}
  query elementsInForfait($id: ID!) {
    elementsInForfait(id: $id) {
      ...CoreFormationElementFields
    }
  }
`;
export const CREATE_COURS_SUPPORT = gql`
  mutation createCoursSupport($input: CoursSupportInput!) {
    createCoursSupport(input: $input) {
      id
    }
  }
`;

export const MUTATION_ADD_TYPE_QCM_TO_COURS_MODULE = gql`
  mutation addTypeQcmToCoursModule($input: CoursTypesQcmSettingsInput!) {
    addTypeQcmToCoursModule(input: $input)
  }
`;
export const MUTATION_REMOVE_TYPE_QCM_FROM_COURS_MODULE = gql`
  mutation removeTypeQcmFromCoursModule($input: CoursTypesQcmSettingsInput!) {
    removeTypeQcmFromCoursModule(input: $input)
  }
`;

export const UPDATE_COURS_SUPPORT = gql`
  mutation updateCoursSupport($id: ID!, $input: CoursSupportInput!) {
    updateCoursSupport(id: $id, input: $input)
  }
`;
export const DELETE_COURS_SUPPORT = gql`
  mutation deleteCoursSupport($id: ID!) {
    deleteCoursSupport(id: $id)
  }
`;

export const QUERY_COURS_BY_IDS = gql`
  ${FRAGMENT_COURS_LIGHT}
  query CoursIds($ids: [ID]) {
    coursIds(ids: $ids) {
      ...CoursFragment
    }
  }
`;

export const QUERY_COURS_BY_IDS_WITH_PARENTS = gql`
  ${FRAGMENT_COURS_LIGHT}
  query CoursIds($ids: [ID]) {
    coursIds(ids: $ids) {
      ...CoursFragment
      ue {
        id
        color
        color2
      }
      ueCategory {
        id
        ue {
          id
          color
          color2
        }
      }
    }
  }
`;

/* ADMIN SEARCH COURS */
export const QUERY_SEARCH_COURS = gql`
  ${FRAGMENT_UES}
  ${FRAGMENT_UECATEGORIES}
  query cours($filter: CoursFilter) {
    cours(filter: $filter) {
      count
      cours {
        id
        name
        name_en
        name_es
        name_it
        name_de
        text
        text_en
        text_es
        text_it
        text_de
        type
        layout
        deleted
        views
        createdAt
        updatedAt
        author {
          id
          username
          avatar
        }
        isVisible
        formationId
        settings
        targetCoursId
        ueCategory {
          ...UECategoryFragment
          ue {
            ...UEFragment
          }
        }
      }
    }
  }
`;

/* PARENTS QUERY */
export const QUERY_UE_WITH_PARENT = gql`
  ${FRAGMENT_UES}
  query Ue($id: ID!) {
    ue(id: $id) {
      ...UEFragment
      parent {
        ...UEFragment
      }
    }
  }
`;
export const QUERY_UECATEGORY_WITH_PARENT = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  query ueCategory($id: ID!) {
    ueCategory(id: $id) {
      ...UECategoryFragment
      parent {
        ...UECategoryFragment
        ue {
          ...UEFragment
        }
      }
      ue {
        ...UEFragment
      }
    }
  }
`;

const FRAGMENT_COURS_MINIMAL_FOR_NAVIGATION = gql`
  fragment CoursFragment on Cours {
    id
    name
    name_en
    name_es
    name_it
    name_de
    text
    text_en
    text_es
    text_it
    text_de
    uecategoryId
    ueId
    targetCours {
      id
      name
      name_en
      name_es
      name_it
      name_de
      text
      text_en
      text_es
      text_it
      text_de
    }
  }
`;

export const QUERY_COURS_LIGHT = gql`
  ${FRAGMENT_COURS_MINIMAL_FOR_NAVIGATION}
  query cours($id: ID!) {
    cour(id: $id) {
      ...CoursFragment
    }
  }
`;
export const QUERY_COURS_WITH_PARENT = gql`
  ${FRAGMENT_COURS_MINIMAL_FOR_NAVIGATION}
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  query cour($id: ID!) {
    cour(id: $id) {
      ...CoursFragment
      ue {
        ...UEFragment
      }
      ueCategory {
        ...UECategoryFragment
      }
    }
  }
`;

/* ____________________ */
/* FOR CUSTOM COURS TAB */
export const QUERY_UES_IDS = gql`
  ${FRAGMENT_UES}
  query ues($ids: [ID!]) {
    ues(ids: $ids) {
      ...UEFragment
      tuteurs {
        id
        name
        username
      }
    }
  }
`;
export const QUERY_UE_CATEGORIES_BY_IDS = gql`
  ${FRAGMENT_UECATEGORIES}
  ${FRAGMENT_UES}
  ${FRAGMENT_COURS_LIGHT}
  query ueCategoriesAmongIds($ids: [ID!]) {
    ueCategoriesAmongIds(ids: $ids) {
      ...UECategoryFragment
      ue {
        ...UEFragment
      }
      cours {
        ...CoursFragment
      }
    }
  }
`;
/* ____________________ */

export const QUERY_BREADCRUMB_DATA = gql`
  query getBreadcrumbData($coursId: ID, $categoryId: ID, $ueId: ID) {
    getBreadcrumbData(coursId: $coursId, categoryId: $categoryId, ueId: $ueId) {
      ... on Cours {
        id
        name
        name_en
        name_es
        name_it
        name_de
        text
        text_en
        text_es
        text_it
        text_de
        type
        layout
        __typename
      }
      ... on UECategory {
        id
        name
        name_en
        name_es
        name_it
        name_de
        description
        description_en
        description_es
        description_it
        description_de
        __typename
      }
      ... on UE {
        id
        name
        name_en
        name_es
        name_it
        name_de
        description
        description_en
        description_es
        description_it
        description_de
        type
        isFolder
        __typename
      }
    }
  }
`;
