import { gql } from '@apollo/client';

/**
 * FRAGMENTS ------------
 */
export const CORE_FOLDER_FIELDS = gql`
  fragment CoreFolderFields on Folder {
    id
    name
    parentId
    type

    countGroups
    countEvents
    countExams
    countForfaits

    countUsers
    countAccessibleCourses

    createdAt
    updatedAt
  }
`;

/**
 * QUERIES ------------
 */
export const QUERY_ALL_FOLDERS = gql`
  ${CORE_FOLDER_FIELDS}
  query Folders {
    folders {
      ...CoreFolderFields
    }
  }
`;

// query folders tree data
export const QUERY_FOLDERS_TREE_DATA = gql`
  query FoldersTreeData(
    $type: String
    $folderType: String
    $folderCheckable: Boolean
    $showNumberOfUsers: Boolean
    $separateForfaitsByType: Boolean
  ) {
    foldersTreeData(
      type: $type
      folderType: $folderType
      folderCheckable: $folderCheckable
      showNumberOfUsers: $showNumberOfUsers
      separateForfaitsByType: $separateForfaitsByType
    )
  }
`;

export const QUERY_FIND_FOLDERS = gql`
  ${CORE_FOLDER_FIELDS}
  query FindFolders($filter: FolderFilter) {
    findFolders(filter: $filter) {
      ...CoreFolderFields
    }
  }
`;

// query folder by id
export const QUERY_FOLDER_BY_ID = gql`
  ${CORE_FOLDER_FIELDS}
  query Folder($id: ID!) {
    folder(id: $id) {
      ...CoreFolderFields
    }
  }
`;

/*
 * MUTATIONS ------------
 */
export const MUTATION_CREATE_FOLDER = gql`
  mutation createFolder($folder: FolderInput!) {
    createFolder(folder: $folder) {
      id
    }
  }
`;
export const MUTATION_UPDATE_FOLDER = gql`
  mutation updateFolder($id: ID!, $folder: FolderInput!) {
    updateFolder(id: $id, folder: $folder)
  }
`;
export const MUTATION_DELETE_FOLDER = gql`
  mutation deleteFolder($id: ID!) {
    deleteFolder(id: $id)
  }
`;
