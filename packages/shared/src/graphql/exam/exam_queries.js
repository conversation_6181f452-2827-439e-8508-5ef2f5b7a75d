import { CORE_SESSION_FIELDS } from '@/shared/graphql/qcm.js';
import { gql } from '@apollo/client';

const EXAM_QUESTION_SERIES_FIELDS_WITH_MCQ = gql`
  ${CORE_SESSION_FIELDS}
  fragment ExamQuestionSerieFragment on ExamQuestionSeries {
    id
    name
    description
    coefficient
    order
    type
    settings
    mcqId
    examSessionId
    date_begins
    date_end
    isAvailable
    qcmSession {
      ...CoreQcmSessionFields
    }
    myStats {
      myGrade
      minGrade
      maxGrade
      classement
      total
    }
    qcm {
      ueCategoriesImpliquees {
        id
        color
        name
        image
        description
      }

      id_qcm
      id_lien
      external
      date_creation
      date_modif
      annee
      annale
      external
      chronometre
      url_image
      id_createur
      pseudo_createur
      maximumPoints
      difficulty
      timer_delay
      goToNextQuestionWhenTimesUp
      shouldResumeTime
      chronoByQuestionOrGlobal
      isPublished
      isFullscreen
      randomizeQuestions
      randomizeQuestionsAnswers
      showCorrectionAtEachStep
      timesItCanBeDone
      hasCheckboxes
      ue
      UEId
      titre
      titre_en
      titre_es
      titre_it
      titre_de
      description
      description_en
      description_es
      description_it
      description_de
      nombreQuestions
      correctionConfig
    }
    examId
    image
    color1
    color2
    authorId
    createdAt
    updatedAt
  }
`;

/*
  FRAGMENTS -------------------------------------------------------
 */
// EXAM WITH ALL FIELDS
export const CORE_EXAM_FIELDS = gql`
  ${EXAM_QUESTION_SERIES_FIELDS_WITH_MCQ}
  fragment CoreExamFields on Exam {
    id
    name
    name_en
    name_es
    name_it
    name_de
    disabled
    description
    description_en
    description_es
    description_it
    description_de
    image
    color1
    color2
    order
    createdAt
    updatedAt
    isPublished
    folderId
    showFinalResults
    types {
      id
      name
    }
    examSessions {
      id
      name
      description
      date
      dateEnd
      isOpen
      duration
      successConditions
      examId
      order
      authorId
      createdAt
      updatedAt

      examQuestionSeries {
        ...ExamQuestionSerieFragment
      }

      datesDiffusion {
        id
        date
        dateEnd
        allDay
        show
        updateInfos
        availability
        buildingId
        link
        organizersIds
        participantsIds
        participantsPresence {
          userId
          date_diffusion_id
          isPresent
        }
      }
    }
    examScales {
      id
      name
      description
      examId
      isDefault
      examQuestionSeries {
        coefficient
        questionSerie {
          ...ExamQuestionSerieFragment
        }
      }
      authorId
      createdAt
      updatedAt
    }
  }
`;

export const CORE_EXAM_FIELDS_WITHOUT_SERIES = gql`
  fragment CoreExamFieldsWithoutSeries on Exam {
    id
    name
    name_en
    name_es
    name_it
    name_de
    disabled
    description
    description_en
    description_es
    description_it
    description_de
    image
    color1
    color2
    order
    createdAt
    updatedAt
    isPublished
    folderId
    showFinalResults
    types {
      id
      name
    }
    examSessions {
      id
      name
      description
      date
      dateEnd
      isOpen
      duration
      successConditions
      examId
      order
      authorId
      createdAt
      updatedAt

      datesDiffusion {
        id
        date
        dateEnd
        updateInfos
        availability
        allDay
        timezone
        recurringEndDate
        recurringPeriod
        buildingId
        roomId
        link
        show
        organizersIds
        participantsIds
        participantsPresence {
          userId
          date_diffusion_id
          isPresent
        }
      }
    }
    examScales {
      id
      name
      description
      examId
      isDefault
      examQuestionSeries {
        coefficient
      }
      authorId
      createdAt
      updatedAt
    }
  }
`;
export const CORE_EXAM_QUESTION_SERIES_FIELDS = gql`
  fragment CoreExamQuestionSeriesFields on ExamQuestionSeries {
    id
    name
    description
    coefficient
    order
    type
    settings
    mcqId
    examId
    image
    color1
    color2
    authorId
    createdAt
    updatedAt
  }
`;
export const CORE_EXAM_SESSION_RESULT_FIELDS = gql`
  fragment CoreExamSessionResultFields on ExamSessionResult {
    id
    examSessionId
    monClassement
    total
    grade
    notes {
      note
    }
    notesParEffectif {
      effectif
      note
    }
    moyenne
    userId
    createdAt
    updatedAt
  }
`;

export const CORE_EXAM_SCALE_FIELDS = gql`
  fragment CoreExamScaleFields on ExamScale {
    id
    name
    description
    examId
    authorId
    createdAt
    updatedAt
  }
`;

export const CORE_EXAM_SESSION_FIELDS = gql`
  fragment CoreExamSessionFields on ExamSession {
    id
    name
    description
    date
    dateEnd
    isOpen
    duration
    successConditions
    examId
    order
    authorId
    createdAt
    updatedAt
  }
`;

export const CORE_EXAM_SESSION_FIELDS_WITH_QUESTIONS_SERIES_FOR_PLANNING = gql`
  fragment CoreExamSessionFields on ExamSession {
    id
    name
    description
    date
    dateEnd
    isOpen
    duration
    successConditions
    examId
    order
    authorId
    createdAt
    updatedAt
    examQuestionSeries {
      id
      name
      description
      coefficient
      order
      type
      settings
      mcqId
      examId
      image
      authorId
      date_begins
      date_end
      qcm {
        id_qcm
        id_lien
        external
        date_creation
        date_modif
        annee
        annale
        external
        id_createur
        pseudo_createur
        difficulty
        timer_delay
        goToNextQuestionWhenTimesUp
        shouldResumeTime
        chronoByQuestionOrGlobal
        isPublished
        isFullscreen
        randomizeQuestions
        randomizeQuestionsAnswers
        showCorrectionAtEachStep
        timesItCanBeDone
        hasCheckboxes
        ue
        UEId
        titre
        titre_en
        titre_es
        titre_it
        titre_de
        description
        description_en
        description_es
        description_it
        description_de
        nombreQuestions
      }
    }
  }
`;

/*
 * QUERIES -------------------------------------------------------
 */
export const QUERY_ALL_EXAMS = gql`
  ${CORE_EXAM_FIELDS}
  query allExams($filter: ExamFilterInput) {
    allExams(filter: $filter) {
      ...CoreExamFields
    }
  }
`;

export const QUERY_ALL_EXAMS_WITHOUT_SERIES = gql`
  ${CORE_EXAM_FIELDS_WITHOUT_SERIES}
  query allExams($filter: ExamFilterInput) {
    allExams(filter: $filter) {
      ...CoreExamFieldsWithoutSeries
    }
  }
`;

export const MY_EXAMS_SESSION_RESULT = gql`
  query myExamsSessionsDone($userId: ID) {
    myExamsSessionsDone(userId: $userId) {
      id
      name
      description
      date
      dateEnd
      isOpen
      duration
      successConditions
      examId
      order
      authorId
      createdAt
      updatedAt
      exam {
        id
        name
        description
      }
    }
  }
`;

export const QUERY_EXAM_QUESTION_SERIES_IN_SESSION_WITH_RESULT = gql`
  query examSession($id: ID!, $sessionId: ID, $userId: ID) {
    examSession(id: $id) {
      id
      examQuestionSeries {
        qcm {
          id_qcm
          titre
          maximumPoints
          correctionConfig
        }
        myStats(sessionId: $sessionId, userId: $userId) {
          minGrade
          maxGrade
          classement
          myGrade
          total
          notes {
            note
          }
          notesParEffectif {
            effectif
            note
          }
        }
      }
    }
  }
`;

// Exam session with all question serie data
export const QUERY_EXAM_SESSION_WITH_QUESTION_SERIES = gql`
  ${EXAM_QUESTION_SERIES_FIELDS_WITH_MCQ}
  query examSession($id: ID!) {
    examSession(id: $id) {
      id
      examQuestionSeries {
        ...ExamQuestionSerieFragment
      }
    }
  }
`;

export const MY_EXAMS_SESSION_RESULT_WITH_EXAM = gql`
  query myExamsSessionsDone($userId: ID, $filter: ExamSessionDoneFilter) {
    myExamsSessionsDone(userId: $userId, filter: $filter) {
      id
      name
      description
      date
      dateEnd
      isOpen
      duration
      successConditions
      examId
      order
      authorId
      createdAt
      updatedAt
      finishedByMe(userId: $userId)
      userResult(userId: $userId) {
        id
        userId
        grade
        moyenne
        monClassement
        total
        createdAt
        updatedAt
      }
      exam {
        id
        name
        description
        showFinalResults
        examScales {
          id
          name
          description
          examId
          isDefault
        }
        types {
          id
          name
        }
      }
    }
  }
`;

export const QUERY_EXAM_WITH_SCALES_FULL = gql`
  ${EXAM_QUESTION_SERIES_FIELDS_WITH_MCQ}
  query exam($id: ID!) {
    exam(id: $id) {
      id
      examScales {
        id
        name
        description
        examId
        isDefault
        examQuestionSeries {
          coefficient
          questionSerie {
            ...ExamQuestionSerieFragment
          }
        }
        authorId
        createdAt
        updatedAt
      }
    }
  }
`;

export const QUERY_EXAM_BY_ID = gql`
  ${CORE_EXAM_FIELDS}
  query exam($id: ID!) {
    exam(id: $id) {
      ...CoreExamFields
    }
  }
`;

export const QUERY_EXAM_BY_ID_WITH_GRADES = gql`
  ${CORE_EXAM_FIELDS}
  query exam($id: ID!, $sessionId: ID, $userId: ID) {
    exam(id: $id) {
      ...CoreExamFields
    }
  }
`;

export const QUERY_EXAM_SESSION_RESULT = gql`
  ${CORE_EXAM_SESSION_RESULT_FIELDS}
  query examSessionResult($id: ID!, $examScaleId: ID, $userId: ID) {
    examSessionResult(id: $id, examScaleId: $examScaleId, userId: $userId) {
      ...CoreExamSessionResultFields
    }
  }
`;

// ADMIN RESULTS
export const QUERY_EXAM_QUESTION_SERIE_ALL_RESULTS = gql`
  query examQuestionSerie($id: ID!) {
    examQuestionSerie(id: $id) {
      id
      userResults {
        id
        id_qcm
        date
        note
        qcmSessionId
        user {
          id
          username
          firstName
          name
          email
          groups {
            id
            name
          }
          avatar
        }
        session {
          id
          updatedAt
          doneQuestionsCount
          createdAt
          isFinished
          isActive
        }
      }
    }
  }
`;

// QUERY_EXAM_QUESTION_SERIE_MY_STATS
export const QUERY_EXAM_QUESTION_SERIE_MY_STATS = gql`
  query examQuestionSerie($id: ID!, $sessionId: ID) {
    examQuestionSerie(id: $id) {
      id
      myStats(sessionId: $sessionId) {
        minGrade
        maxGrade
        classement
        total
        notes {
          note
        }
        notesParEffectif {
          effectif
          note
        }
      }
    }
  }
`;

// ADMIN RESULTS
export const QUERY_EXAM_SESSION_ALL_RESULTS = gql`
  query examSession($id: ID!, $examScaleId: ID) {
    examSession(id: $id) {
      id
      userResults(examScaleId: $examScaleId) {
        id
        examSessionId
        monClassement
        total
        grade
        notes {
          note
        }
        notesParEffectif {
          effectif
          note
        }
        moyenne
        userId
        user {
          id
          username
          avatar
        }
        createdAt
        updatedAt
      }
    }
  }
`;

export const QUERY_EXAM_SESSION_ALL_GRADES_AND_AVERAGE = gql`
  query examSession($id: ID!, $examScaleId: ID) {
    examSession(id: $id) {
      id
      userResults(examScaleId: $examScaleId) {
        id
        examSessionId
        monClassement
        total
        grade
        moyenne
        userId
        user {
          id
          username
          firstName
          name
          email
          groups {
            id
            name
          }
          avatar
        }
        createdAt
        updatedAt
      }
    }
  }
`;
