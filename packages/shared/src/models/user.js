import { gql } from '@apollo/client';
import { routerRedux } from 'dva/router';
import { setAuthority } from '../utils/authority';
import { getPageQuery } from '../utils/utils';

const UserModel = {
  namespace: 'user',
  state: {
    status: undefined,
    currentUser: undefined
  },
  effects: {
    *updateUser({ payload }, { call, put }) {
      yield put({
        type: 'saveCurrentUser',
        payload
      });
    },
    *login({ payload }, { call, put }) {
      const { banned } = payload;
      // payload.type = 'ok';
      if (!banned) {
        yield put({
          type: 'changeLoginStatus',
          payload
        }); // Login successfully

        yield put({
          type: 'saveCurrentUser',
          payload
        });

        const urlParams = new URL(window.location.href);
        const params = getPageQuery();
        let { redirect } = params;
        if (redirect) {
          const redirectUrlParams = new URL(redirect);
          if (redirectUrlParams.origin === urlParams.origin) {
            redirect = redirect.substr(urlParams.origin.length);
            if (redirect.match(/^\/.*#/)) {
              redirect = redirect.substr(redirect.indexOf('#') + 1);
            }
          } else {
            window.location.href = redirect;
            return;
          }
        }
        yield put(routerRedux.replace(redirect || '/'));
      }
    }
  },
  reducers: {
    changeLoginStatus(state, { payload }) {
      setAuthority(payload.role);
      return { ...state, status: 'ok', type: payload.type };
    },
    saveCurrentUser(state, { payload }) {
      return { ...state, currentUser: payload || {} };
    }
  }
};
export default UserModel;

export const GET_ME = gql`
  query GetMe {
    me {
      id
      username
      name
      email
      role
      avatar
      credits
      banned
      asksForDeletion
      isReachableByPrivateMessage
      lang
    }
  }
`;

export const GET_ME_COMPANIES = gql`
  query GetMe {
    me {
      id
      username
      name
      email
      role
      avatar
      credits
      banned
      asksForDeletion
      isReachableByPrivateMessage
      lang
      companiesDescriptions {
        companyConfigId
        companyName
      }
    }
  }
`;

export const GET_ME_CGU = gql`
  query GetMe {
    me {
      id
      username
      firstName
      name
      addressline1
      addressline2
      postcode
      phone
      city
      bio
      title
      userCodeName
      parentsEmail
      parentsPhone
      parentsProfession
      gender
      birthdate
      nationality
      country
      email
      role
      avatar
      credits
      banned
      asksForDeletion
      isReachableByPrivateMessage
      lang
      hasAcceptedCGU
      isExtraTime
      exostaff
    }
  }
`;

export const GET_FIRST_MANDATORY_FORM_ID_TO_COMPLETE = gql`
  query undoneMandatoryFormIdToComplete {
    undoneMandatoryFormIdToComplete
  }
`;

export const GET_ME_WITH_RESPONSIBILITY = gql`
  query GetMe {
    me {
      id
      myGroupsResponsibility {
        id
        name
        role
        image
      }
    }
  }
`;

export const GET_ME_WITH_STATS = gql`
  query myDashboard {
    me {
      id
      username
      firstName
      name
      email
      role
      avatar
      credits
      banned
      isReachableByPrivateMessage
      stats {
        seenClasses
        seenAnnales
        seenMcq
        mcqDone
        questionsDone
        postsSent
        accountConnections
        likesReceived
        likesGiven
        downloadedFiles
        privateMessagesSent
        privateMessagesReceived
      }
    }
  }
`;

export const GET_ME_WITH_STATS_FOR_PROGRESS_BAR = gql`
  query myDashboard {
    me {
      id
      username
      firstName
      name
      email
      role
      avatar
      credits
      banned
      isReachableByPrivateMessage
      stats {
        seenClasses
        seenAnnales
        seenMcq
        mcqDone
        questionsDone
        postsSent
        accountConnections
        likesReceived
        likesGiven
        downloadedFiles
        privateMessagesSent
        privateMessagesReceived

        uniqueSeenClasses
        totalSeeableClasses
      }
    }
  }
`;
