{"1by1": "1 par 1", "AboutContentAndOffers": "Pour le contenu et les offres:", "AccessExercice": "Accéder à l'exercice", "Activity": "Activité", "ActualLocalization": "Localisation actuelle", "AddACourseToAllQuestions": "📒 Ajouter un cours à toutes les questions :", "AddAFile": "Ajouter un fichier", "AddAPostType": "Ajouter un type de discussion", "AddAType": "Ajouter un type", "TreeStructureForUser": "Arborescence accessible par l'utilisateur", "TreeStructureForGroup": "Arborescence accessible par le groupe", "ModificationsPermissionTreeNotSaved": "Modifications arbre de permission non sauvegardées", "UserDisconnectedFromAllDevices": "Utilisateur déconnecté de tous les appareils", "DisconnectFromAllDevices": "Déconnecter les appareils connectés", "DisconnectFromAllDevicesConfirmation": "Êtes-vous certain de déconnecter tous les appareils connectés à ce compte ?", "Male": "<PERSON><PERSON>", "SelectNotification": "Sélectionnez une notification", "Female": "<PERSON>mme", "Other": "<PERSON><PERSON>", "AddAnImage": "Ajouter une image", "ChooseUsername": "Choisir un nom d'utilisateur", "SelectEventToShow": "Choisir un évènement à afficher", "SelectedEvent": "Évènement sélectionné", "SelectFormToShow": "Sélectionnez le formulaire à afficher", "AddBlock": "Ajouter un bloc", "Advanced": "<PERSON><PERSON><PERSON>", "NoPreviewAvailable": "Aucun aperçu disponible", "AIAnalysis": "Analyse IA", "AIAnalysisTooltip": "Les réponses des utilisateurs seront analysées par l'IA au moment de la correction", "NewAppVersionAvailable": "Une nouvelle version de l'application est disponible. Veuillez la télécharger pour continuer", "AddCategory": "Ajouter une catégorie", "EditThisExercisePack": "Modifier ce lot d'exercice", "DeleteThisExercisePack": "Supprimer ce lot d'exercice", "YouDoNotHaveAccessToTheseExerciseSeriesType": "Vous n'avez pas accès aux séries d'exercices de type ", "AddCustomField": "Ajouter un champ person<PERSON><PERSON>", "Theme": "Thème", "ResponsibleFor...": "Responsable de...", "SupervisedBy...": "Supervisé par...", "AddDate": "Ajouter une date", "FinishTraining": "Terminer l'entraînement", "AddElement": "Ajouter un élément", "AddExercice": "Créer un nouvel exercice", "AddGroups": "Ajout de groupes", "AddItem": "Ajouter une réponse", "AddNewYear": "Ajouter une année", "AddNotion": "Ajouter une notion", "AddQuestions": "Ajouter des questions", "AddSection": "Ajouter une section", "AddUser": "A<PERSON>ter utilisateur", "AdditionnalElementsCorrection": "Élément(s) supplémentaire de correction", "Admin": "Admin", "Training": "Entraînement", "Administration": "Administration", "All": "<PERSON>ut", "AllCategories": "Toutes catégories", "AllDay": "Toute la journée", "Recurring": "<PERSON><PERSON><PERSON><PERSON> ré<PERSON>", "AllDiscussionsType": "Tous les types de discussions", "AllExercices": "Tous les exercices", "AllGroups": "Tous les groupes", "AllMyExercices": "Toutes mes séries d'exercices", "AllMyGroups": "Tous mes groupes", "AllMyNotifications": "Toutes mes notifications", "AllNotions": "Toutes les notions", "AllOrNothing": "Tout ou rien", "AllOrNothingItem": "<PERSON><PERSON> tout ou rien", "AllQuestions": "Tous les exercices", "AllSubjects": "Toutes les matières", "AllTheTime": "Tout le temps", "AllUsers": "Tous les utilisateurs", "AlreadyHaveAccount": "J'ai déjà un compte", "OtherAcceptableAnswers": "Autres réponses acceptées", "AlreadyLinkedToCourse": "<PERSON><PERSON>jà lié à ce cours", "Annal": "<PERSON><PERSON>", "Annals": "<PERSON><PERSON>", "AnnounceForAllGroups": "Annonce pour tous les groupes", "AnswerdQuestion": "Questions répondues", "AnswersWithoutQuestionsCorrection": "Corrigé sans énoncé", "AppendixExplanation": "Ce sont des éléments qui seront accessibles durant toute la série par les étudiants", "AreYouSure": "Êtes vous certain ?", "AskNewQuestion": "Poser une nouvelle question ou démarrer une discussion", "AskQuestion": "Poser une question", "SearchOrAskQuestion": "<PERSON><PERSON><PERSON> ou poser une question", "AssociatedCourseTo": "📒 Cours associé(s) à ce", "AutentificationError": "Erreur lors de l'authentification", "AutomaticallyAdded": "Ajoutées automatiquement", "Available subscription": "Abonnements disponibles", "AvailableExercice": "Exercice disponible", "Availibility": "Disponibilité", "BackToCourse": "Retour au cours", "BackToExam": "Retour à l'examen", "BackToExerciceList": "Retour à la liste des exercices", "BackToExericesList": "Retour liste d'exercices", "BackToGenerator": "Retour au générateur", "BeginDate": "Date de début", "BlockedUsers": "Utilisateurs bloqués", "BottomPart": "<PERSON><PERSON> basse", "Buy1Credit": "J'achète 1 crédit", "BuyCreditExplanation": "Achetez un crédit qui peut être utilisé pour accéder à une offre par la suite !", "By": "Par", "CGU": "CGU", "Callout": "Encadré", "Cancel": "Annuler", "CancelDeletion": "Annuler la suppression", "CancelSimulation": "Annuler simulation", "CantFindTopic": "Sujet introuvable, il a probablement été supprimé.", "Category": "<PERSON><PERSON><PERSON><PERSON>", "CategoryImage": "Image", "CategoryName": "Nom du dossier", "CategoryPermissions": "Permissions de cette catégorie", "CategoryResults": "Résultats par Catégorie", "SelectEvent...": "Sélectionner un évènement...", "CertaintyExercice": "💪 Degré de certitude sur cet exercice", "Change": "Changer", "ChangePassword": "Changer mon mot de passe", "YouWillHaveToReconnectAfterChangingYourPassword": "Le changement de mot passe déconnectera tous les appareils actuellement connectés à ce compte", "ChangePasswordUser": "Changer le mot de passe de l'utilisateur", "ChangeSubjectExercices": "Changer la matière des exercices sélectionnés", "ChatName": "Nom la discussion", "CheckExistingQuestion": "Vérifiez que votre question ne soit pas déjà posée ci-dessous", "ChoiceTimer": " Vous pouvez choisir de faire cet exercice de façon chronométré, ou de ne pas avoir de limite de temps.  Si vous choisissez d'être chronométré, l'exercice se terminera à la fin du compte à rebours.", "Choose": "Choi<PERSON>", "ChooseFolder": "Choisir un dossier", "ChooseASubject": "<PERSON><PERSON> une matière", "ChooseCategory": "Choisir une catégorie...", "ChooseContentType": "Choisir le type de contenu", "ChooseCourse": "Choisir un cours", "ChooseEmojis": "Choisir un emoji", "ChooseExerciceType": "Choisir le(s) type(s) d'exercices...", "ChooseExercicesSerieType": "✅ Choisissez le type des séries d'exercice", "ChooseGradientColor": "Choisir les couleurs pour le dégradé", "ChooseGroupAccess": "Choisir groupes ayant accès", "ChooseGroups": "Choisir groupes...", "ChooseMCQType": "Choisir le type d'exercices...", "ChooseNewPassword": "Veuillez saisir un nouveau mot de passe", "ChooseQuestionType": "Choisir type d'exercice...", "ChooseRandomQuestions": "Choisir les questions au hasard (Sinon ordre chronologique)", "ChooseReferent": "Choisir professeurs...", "ChooseSubject": "Choisissez une matière...", "ChooseSubjects": "🗂 Choisissez les matières", "ChooseYear": "Choisir une année", "ClickHere": "Cliquez ici", "CloseWithoutSaving": "<PERSON>rmer sans enregistrer", "Color": "<PERSON><PERSON><PERSON>", "ColorOne": "Couleur 1", "ColorTwo": "Couleur 2", "Comment": "Commentaire", "Comments": "Commentaires", "CommercialName": "Nom commercial", "CompanyType": "Type de société", "ConfirmDeletion": "Confirmer suppression", "ConfirmPassword": "Confirmer le mot de passe", "ChangingUserPasswordWillDisconnectUser": "Le changement de mot passe déconnectera tous les appareils actuellement connectés à ce compte", "ConfirmPasswordCreation": "Confirmer le mot de passe", "CongratsNoMistake": "Bravo ! Vous n'avez fait aucune erreur !", "Congratulations": "Bravo !", "Connected": "Connectés", "Connections": "Connexions", "ContactEmail": "Email de contact", "ContactPhoneNumber": "Téléphone de contact (facultatif)", "ContentAccessible": "V<PERSON> avez accès aux questions sur les types de contenus :", "ContentType": "Type de contenu", "Copy": "<PERSON><PERSON><PERSON>", "Copied": "<PERSON><PERSON><PERSON>", "AccessibleContentType": "Type de contenu accessible", "AccessibleSerieType": "Les utilisateurs ne verront les séries et exercices sélectionnés que s'ils ont aussi accès aux cours associés", "CopyrightWarning": "Toute reproduction totale ou partielle du contenu du site est strictement interdite et est susceptible de constituer un délit de contrefaçon.", "Correction": "Correction", "CorrectionElements": "Éléments de correction", "CorrectionParameters": "⚙ ️Paramètres de correction", "CourseCategorie": "Catégorie du cours", "CourseDifficulty": "Difficulté du cours", "CourseFile": "Fichier du cours", "CourseLength": "Du<PERSON>e du cours en minutes", "CourseSeen": "Cours vus", "CourseTitle": "Titre du cours", "CourseToAttribute": "Cours à attribuer", "CourseToLook": "Cours à chercher", "CourseToLookInQuestion": "Choisir le cours à chercher dans les questions", "CourseType": "Type de cours (layout)", "Courses": "Cours", "CoursesPermissions": "Permissions des cours", "CoverImage": "Image de couverture (optionnel)", "Create": "<PERSON><PERSON><PERSON>", "CreateElementBeforeSettingUpAccessGroups": "<PERSON><PERSON>ez l'élément avant de pouvoir gérer les groupes d'accès.", "CreateForumHere": "Ajouter un forum dans cette catégorie", "CreateNewCategory": "<PERSON><PERSON>er un nouveau dossier", "CreateNewCourse": "<PERSON><PERSON>er un nouveau cours", "CreateNotion": "<PERSON><PERSON><PERSON> une notion", "CreateUser": "<PERSON><PERSON><PERSON> utilisateur", "CreatedBy": "C<PERSON><PERSON> par", "CreatedExercice": "<PERSON>er<PERSON><PERSON> c<PERSON>", "CurrentSubscription": "Abonnement actuel", "CustomFields": "Champs personnalisés 🔥", "CustomLinks": "<PERSON>ns person<PERSON>", "DateAndUpdate": "Date et mise à jour", "Dates": "Dates", "DefaultScaleForExam": "Barême par défaut de cet examen", "DefinedDates": "Dates définies", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "DeleteChat": "Supprimer la conversation", "DeleteContent": "Supp<PERSON>er le contenu", "DeleteField": "Supprimer ce champ", "DeleteMyAccount": "Supprimer mon compte", "DeleteThisDate": "Supprimer cette date", "DeleteTraining": "Supprimer la formation", "DeletedAccounts": "Comptes supprimés", "DeletedCourses": "Cours supprimés", "DeletedUsers": "Utilisateurs supprimés", "DeletionRequests": "Demandes de suppression", "Description": "Description", "ShortDescription": "Description courte", "LongDescription": "Description longue", "DescriptionExemple": "Description (eg. <PERSON> lipides)", "DescriptionForumCategory": "Description de la catégorie", "DesiredQuestions": "Questions désirées :", "DetailResultsAndComments": "Page détail exercice et commentaires", "Difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DiscussionModuleExplanation": "💬 Module Discussions : Permet aux étudiants de poser des questions sous le cours.", "AllowAIToAccessCourse": "💬 Autoriser l'IA à accéder au cours pour répondre aux questions (fichier PDF ou docx).", "DiscussionSpace": "Espace discussion", "DiscussionType": "Type de discussions", "DiscussionsModule": "Module Discussions", "DisplayAverage": "Afficher la moyenne", "DisplayChildren": "<PERSON><PERSON><PERSON><PERSON> enfants", "DisplayCorrection": "Aff<PERSON>r <PERSON>", "DisplayDetailedCorrection": "Afficher la correction détail<PERSON>e", "DisplayGrade": "Afficher la note obtenue", "DisplayOrder": "Ordre d'affichage", "DisplayOrderOptionnal": "Ordre affichage (optionnel)", "DisplayParents": "Afficher parents", "DisplayResultAnalysis": "Afficher analyse des résultats", "DisplayWeakStrongPoints": "Afficher points forts et faibles", "DoComment": "<PERSON><PERSON><PERSON>", "DoMoreMCQForBetterResults": "Plus vous faites d'exercices, et plus le système pourra vous aider à optimiser vos révisions", "DoThisExercice": "Faire cet exercice", "Done": "Faits", "DoneSingle": "Fait", "DoneOn": "Fait le", "DontStartTimer": "Ne pas lancer le chronomètre", "DoubleParentChildren": "Double parents/enfants", "DownloadTemplateXls": "Télécharger le modèle Excel", "Dynamic": "Dynamique", "Edit": "É<PERSON>er", "EditCategory": "Éditer cette catégorie", "EditCourse": "Mo<PERSON><PERSON> le cours", "EditExerciceInfos": "Éditer informations de l'exercice", "EditGroupPermissions": "Modifier les permissions de groupe", "EditLinkedExercices": "Modifier Exercices attachés", "EditQuestion": "É<PERSON>er exercice", "EditQuestions": "Éditer les exercices", "EditScale": "Modifier le barême", "EditThisElement": "Modifier cet élément", "ElementTypeToInsert": "Type d'élément à insérer", "ElementsAboveOf": "Élément(s) au dessus du", "EmbeddedMainVideoLink": "Lien embedded de vidéo principale", "EnableOrDisableCourseModules": "Activer/Désactiver modules du Cours", "EndDate": "Date de fin", "EndYear": "<PERSON><PERSON>", "EnrichedCourse": "Cours Enrichi", "EnterPseudo": "<PERSON><PERSON><PERSON>", "EquationEditor": "Éditeur d'équations", "Erase": "<PERSON><PERSON><PERSON><PERSON>", "EraseAll": "Tout effacer", "Error": "<PERSON><PERSON><PERSON>", "ErrorDoubleParentChildren": "Erreurs Double parents/enfants", "ErrorParentChildren": "Erreurs Parents/enfants", "ErrorWhileLoadingProfile": "Erreur lors de la récupération de votre profil", "Errors": "<PERSON><PERSON><PERSON>", "EstimatedDifficultyOfQuestion": "Difficulté estimée de l'exercice", "EvaluateCertaintyLevel": "<PERSON><PERSON><PERSON> le niveau de certitude", "EventDescription": "Description de l'évènement", "EventName": "Nom de l'évènement", "Events": "Évènements", "OccurrenceEditorInfo": "Vous pouvez gérer les dates récurrentes masquées ou modifiées depuis l'interface de gestion des dates de diffusion", "ExamName": "Nom de l'examen", "Exams": "Examens", "ExempleCategoryName": "Les glucides", "ExempleCourseTitle": "01: Chapitre 1", "ExempleFolderName": "2020-2021 - Médecine", "ExempleName": "Nom (e.g: Premier semestre)", "ExempleSubject": "Titre (eg. UE4: Biostatistiques)", "ExerciceAndLesson": "Cours & Exercices", "ExerciceCorrection": "Correction de l'exercice", "ExerciseSerieCorrection": "Correction de la série", "ExerciceGenerator": "<PERSON><PERSON>er votre propre entrainement", "ExerciceSerieOfCourse": "✅ Séries d'exercices évoquant le cours", "ExerciceType": "Type d'exercice", "ExerciceFormat": "Format d'exercice", "Exercices": "Exercices", "ExercicesCreationDate": "🗓 Dates des exercices", "ExercicesDone": "Exercices Faits", "ExercicesScale": "Barèmes Exercices", "ExercicesSerie": "Séries d'exercices", "ExercicesType": "Type(s) d'exercices", "TypeWithEmoji": "🗂Type", "ExerciseSets": "Séries d'exercices", "ValidateThisExercisePack": "Valider ce lot d'exercice", "ExercisesSeries": "Séries d'exercices", "ExistingYears": "Années existantes", "ExoteachGivesExoteach": "La plateforme est fournie par la société EXOTEACH :", "ExoteachQuizz": "Série d'exercice", "ExplainWhyTrueFalse": "Expliquer avec précisions pourquoi c'est vrai ou faux", "ExplanationAnalysisResults": "Notre algorithme estime votre niveau de connaissance à partir de vos réponses aux tests de diagnostics et des résultats obtenus par tous les autres candidats. Complétez le maximum de test de diagnostic pour obtenir une estimation la plus précise possible.", "ExplanationDisplayExercice": "L'affichage classique affichera tous les exercices sur une seule page, le 1 par 1 permet de n'afficher qu'un exercice à la fois", "ExplanationModuleRevision": "🎓 Module Révisions : Toutes les questions liés au cours pas encore faites !", "ExplanationQuestionType": "Les types de contenu permettent de changer les permissions d'accès et de classifier les exercices", "ExplanationTags": "Les tags d'exercice permettent de classer vos exercices, cette option est facultative", "ExplanationTimer": "Cette série est chronométré, vous aurez un certain temps par exercice, dès que vous cliquez sur Lancer le chronomètre. A la fin du compte à rebours, la série se terminera !", "ExportOnlyQuestions": "Export exercices seuls", "ExportXLSAllUsers": "Export XLS tous les résultats", "FeaturedMedia": "Support à la une", "FeaturedMediaType": "Type de support à la une", "File": "<PERSON><PERSON><PERSON>", "FileToImportCSV": "Fichier à importer (.csv)", "FileToImportExoqcm": "Fichier à importer (.exoqcm)", "FileToImportXLS": "Fichier à importer (.xls)", "FileToImportXLXS": "Fichier à importer (.xls ou .xlsx)", "FileType": "Type de fichier", "Finish": "<PERSON><PERSON><PERSON>", "FinishExamToGetResult": "Te<PERSON><PERSON>z toutes les séries d'exercices de l'examen pour obtenir votre résultat final ! 🎯", "FirstNameAndNameOfLeader": "Nom et Prénom du responsable", "FixedGradeIfNothingChecked": "Note fixe si rien n'est coché", "FolderName": "Nom du dossier", "FontWeight": "Épaisseur du texte", "Forum": "Forum", "ForumDescription": "Description du forum", "ForumIcon": "Icône du forum", "ForumName": "Nom du forum", "FreeText": "Texte libre", "FromDate": "À partir d'une date", "GainPointCertainty": "Points obtenus par niveau de certitude", "General": "Général", "GeneralConfig": "Configurations générales", "MobileApp": "App mobile", "GeneralProgress": "Progression Générale", "MobileAppHomePage": "Page d'accueil de l'application mobile", "GeneralSynthesis": "Synthèse Générale", "Generalities": "🚀 Généralités", "GenerateExericesSerie": "Générer la série d'exercice", "GlobalView": "Vue globale", "GlobalViewResponsabilities": "Vue globale des responsabilités de groupes", "GoToDiscussion": "Aller à la discussion", "Responsability": "Responsabilités", "responsabilityExplanation": "En précisant les responsabilités de l'utilisateur, cela lui permettra d'accéder aux informations et aux plannings des utilisateurs dont il a la charge.", "responsabilityGroupExplanation": "En précisant les responsabilités du groupe, cela permettra à tous ses membres d'accéder aux informations et aux plannings des utilisateurs dont ils ont la charge", "GroupAccess": "Groupes et accès", "GroupsConcerned": "Planning reliés", "GroupsHavingAccessToThisElement": "Groupes ayant accès à cet élément", "HQaddress": "Adresse du siège", "HQsocial": "Siège social", "HTML": "HTML", "HTMLCode": "Code HTML", "HeaderOf": "Chapeau du", "HowDoesItWorks": "Comment ça marche ?", "HowManyPointsIsTheQuestionWorth": "Valeur de l'exercice (pts)", "HowManyQuestionInSerie": "🗂 Nombre d'exercices souhaités", "HowToBuyCredit": "Vous pouvez vous connecter depuis un navigateur web sur la plateforme pour mettre à jour votre offre, ou acheter des crédits à dépenser ici.", "Image": "Image", "ImageHeight": "<PERSON>ur de l'image", "ImageLegend": "Légende de l'image (description)", "Legends": "Légendes", "Legend": "Légende", "SearchLegend": "Rechercher une légende", "ImageWidth": "Largeur de l'image", "ImportExistingCSV": "Importer des questions existantes depuis un fichier CSV", "ImportFromExcel": "Importer depuis un tableur", "ImportNewOrder": "Importer changement d'ordre au sein de la série", "ImportQuestion": "Importer un exercice", "ImportResultsXLS": "Importer résultats élèves XLS", "ImportUsersFromTable": "Importer des utilisateurs depuis un tableur", "InProgress": "En cours", "Info": "Info", "IsDefaultScale": "Est le barême par défaut", "IsPublishedVisible": "Est publié (visible)", "ItemCorrection": "Correction de l'item", "ItemNumber": "Nombre d'items :", "JSONExport": "Export format JSON", "LaunchRequest": "Lancer la requ<PERSON>", "LaunchTimer": "Lancer le chronomètre !", "LeftPart": "<PERSON>ie gauche", "Legal notices": "Mentions légales", "LegalInformations": "Informations pour facturation et mentions légales", "Like": "Like", "Rank": "<PERSON>ng", "Link": "<PERSON><PERSON>", "LinkName": "Nom du lien", "LinkNameExemple": "Nom du lien (e.g: Forfaits secrets)", "LinkTitle": "Titre du lien", "Localization": "<PERSON><PERSON> ho<PERSON>", "LogInAs": "Se connecter en tant que", "Login": "Connexion", "Logout": "Déconnexion", "LookForPlaform": "Recherchez la plateforme sur laquelle vous souhaitez vous connecter", "LookForSerieExercice": "Rechercher une série d'exercices", "MCQ": "QCM", "MCQGenerator": "⚙ Générateur d'exercice", "MCQType": "Type de séries d'exercices", "MCQs": "QCMs", "ManageExercices": "<PERSON><PERSON><PERSON> les exercices", "ManageFolders": "<PERSON><PERSON><PERSON> les dossier", "ManageGroupAccess": "Gérer groupes et accès", "ManageTeacher": "<PERSON><PERSON><PERSON> les prof", "ManageTitle": "<PERSON><PERSON><PERSON> les titres", "MandatoryTimed": "⏱Chronomètre obligatoire", "MandatoryTimedExplanation": "En cochant cette case, la série sera obligatoirement chronométrée", "Manual": "<PERSON>", "ManuallyAdded": "Ajoutées man<PERSON>", "MarkAllAsRead": "<PERSON><PERSON> tout lu", "MarkAsNonResolved": "Marquer comme non-résolu", "MarkAsResolved": "Marquer comme résolu", "MassOperation": "Opération en masse", "MaxGrade": "Note maximum", "Completions": "Complétions", "CompletionsExplanation": "Le nombre de complétion représente le nombre total de tentative faites par tous les utilisateurs sur cette série d'exercice", "Messages": "Messages", "MessagesPosted": "Messages postés", "MinGrade": "Note minimum", "QuickAccess": "Accès rapide", "ModifyCourse": "<PERSON><PERSON><PERSON> le cours", "ModuleQuickAccessExplanation": "⚡️ Module Accès rapides : Tous les exercices spécifiques liés aux cours + leur correction accessible en un clic", "ModuleEventExplanation": "️Module Évènements : tous les évènements des types sélectionnés qui sont liés à ce cours", "ModuleSchemaTraining": "️Module schémas : tous les schémas des types sélectionnés qui sont liés à ce cours", "Modules": "<PERSON><PERSON><PERSON>", "Monetico": "Monetico", "MostRecents": "Plus récents", "MotiveQuestion": "Sujet ou objet de votre question", "MoveCourse": "<PERSON>é<PERSON>r ce cours", "MyGrade": "Ma note", "MyNotifications": "Mes notifications", "MyPackage": "Mon forfait", "MyProfile": "Profil", "MySubjects": "<PERSON><PERSON> matières", "SelectSubjectsToTrainOn": "Sélectionner la/les matières pour s'entrainer", "MySubscription": "Mon abonnement", "NOLINKEDCOURSE": "AUCUN COURS N'EST LIÉ À CET EXERCICE, VEUILLEZ EN AJOUTER UN 😀", "Name": "Nom", "NameForumCategory": "Nom de la catégorie", "NeverDone": "<PERSON><PERSON> fait", "NewAnnounce": "Nouvelle annonce", "NewCategory": "Nouvelle catégorie", "NewExercice": "Nouvel Exercice", "NewSerieExercice": "Nouvelle série d'exercice", "NewTopic": "Nouveau sujet", "Next": "Suivant", "Previous": "Précédent", "NextQuestionWillBeAbout": "Les exercices suivants vont porter sur :", "NoCreditAvailable": "Vous n'avez pas de crédits à dépenser, ache<PERSON><PERSON> en avant de pouvoir mettre à jour", "NoResult": "Aucun résultat", "NoResultsForSelectedGroups": "Aucun résultat avec les groupes sélectionnés.", "NoTopics": "<PERSON><PERSON><PERSON>", "None": "Aucune", "NoExercisesFound": "Aucun exercice trouvé avec ces paramètres", "exercisesFound": "exercices trouvés", "desiredExercises": "exercices désirés", "NotDoneYet": "Pas encore fait 😢", "NotificationTitle": "Titre de la notification", "Notifications": "Notifications", "Notify": "Notifier", "NotionModule": "🌀 Module Notions (s'il y en a de détectées)", "NotionModuleIfAny": "Modules notions (s'il y en a)", "Notions": "Notions", "NotionsAvailable": "Notions disponibles", "NotionsCovered": "Notions abordées", "NotionsInThisCourse": "Notions dans ce cours", "NumberOfCourses": "Cours", "NumberOfMCQ": "Exercices", "NumberOfNotions": "Notions", "NumberOfQuestions": "Nombre de questions:", "NumberOfExercises": "Nombre d'exercices :", "NumberOfTry": "Nombre d'essai:", "OnlyDisplayMyGroupAgenda": "Afficher le planning de mes groupes seulement", "OnlyUsernameDisplayed": "Seul votre nom d'utilisateur est affiché aux autres membres", "Open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Order": "Ordre", "Organizers": "Organisateurs️", "OtherFiles": "Autres fichiers utiles", "OtherGroupGivingAccess": "Autres groupes donnant accès à des cours", "Outdated": "Hors concours", "Owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Participants": "Participants", "PasswordDontMatch": "Les mots de passe ne correspondent pas", "PixelDecalage": "Décalage en pixels", "Planning": "Planning", "PleaseEnterNumber": "Veuillez entrez un nombre", "Post": "Nouvelle discussion", "TitleDiscussionsInCourse": "{{number}} discussions sur {{courseName}}", "PostList": "Liste des discussions", "Posted": "Postés", "Preview": "<PERSON><PERSON><PERSON><PERSON>", "PrincipalFile": "🎯 Support à la une", "Print": "<PERSON><PERSON><PERSON><PERSON>", "Schemas": {"Schema": "<PERSON><PERSON><PERSON><PERSON>", "Schemas": "<PERSON><PERSON><PERSON><PERSON>", "PointAndClick": "Point n click", "FillInLegends": "Légende à remplir", "OpenSchema": "<PERSON><PERSON><PERSON><PERSON><PERSON> le schéma...", "LegendsToPositionOnSchema": "Légendes à positionner", "LegendsToComplete": "Légendes à compléter", "SchemasLibrary": "Bibliothèque de s<PERSON>mas", "Zoom": "Zoom", "Unzoom": "Dé-zoom", "SchemLibrary": "Bibl<PERSON>", "InstructionsCheckLegends": "Cochez les légendes que l'élève devra pointer sur le schéma. Seules les légendes sélectionnées seront demandées.", "InstructionsPlaceLegendsToComplete": "Cochez les légendes puis déplacer les points associés que l'élève devra compléter. Seules les légendes sélectionnées seront demandées.", "GoodAnswersAreGreenBadAnswersAreRed": "Sur le schéma, vos bonnes réponses sont représentées par des points verts, les mauvaises par des points rouge"}, "Webhooks": {"Events": "Évènements", "Logs": "Logs", "Success": "<PERSON><PERSON><PERSON><PERSON>", "Failed": "<PERSON><PERSON><PERSON>", "Webhooks": "Webhooks", "Webhook": "Webhook", "Endpoints": "Endpoints", "Pending": "En attente", "Warning": "Attention", "TooManyErrors": "Il y a un grand nombre d'erreur, veuillez vérifier vos endpoints", "Processed": "Traité", "Errors": "<PERSON><PERSON><PERSON>", "Documentation": "Documentation", "Endpoint": "Endpoint", "WebhookLogsInfo": "Les événements échoués sont réessayés automatiquement toutes les 5 minutes.", "Payloads": "Payloads", "Payload": "Payload", "secretInfo": "Un secret sera généré pour vous, il est utilisé pour signer les requêtes envoyés à votre endpoint. Ne le partagez pas !", "AddEndpoint": "Ajouter un endpoint", "EditEndpoint": "Modifier un endpoint", "SelectEventsToListenTo": "Sélectionner des événements à écouter", "EndpointsURL": "URL d'endpoint", "EndpointExample": "Exemple d'endpoint", "CreateEditEndpointInfo": "Configurez votre endpoint de webhook pour recevoir des événements en direct de Exoteach."}, "TopicType": "Type de question", "TopicTypeRequired": "Un type de question est requis pour créer la question", "FillInTheBlanks": "Texte à trous", "PrivateMessage": "Message privé", "BadAnswers": "Mauvaises réponses", "PrivateMessages": "Messages privés", "Progress": "Progression", "Combo": "Combo", "PublicationDirector": "Directeur de la Publication", "PullToRefresh": "Tirez pour actualiser", "QCU": "QCU", "QuestionOptions": "🛠 Options de l'exercice", "QuestionScale": "Barême de l'exercice", "QuestionSeriesAboutCourse": "Série d'exercices évoquant le cours", "QuestionTags": "Tag(s) de l'exercice", "QuestionType": "Type d'exercice", "Questions/Answers": "Questions / réponses", "QuestionsAnswer": "Questions / Réponses", "QuestionsWaiting": "Questions en attente de réponse", "QuizzGenerator": "Générateur d'exercices", "RCSnumber": "N° RCS", "RandomlyDrawnQuestions": "Questions tirées aléatoirement", "RandomlyDrownitems": "Items tirés aléatoirement", "RankingByGrade": "Classement par note", "Reason": "<PERSON>son", "Received": "<PERSON><PERSON><PERSON>", "ReferentTeacher": "Professeur<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>s", "Refresh": "Actualiser", "Registered": "Inscrits", "RemoveExerciceSerieOfExam": "Enlever cette série de l'examen", "Reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RepliedTo": "En réponse à", "Report": "Signaler", "RequiredGroups": "Groupes requis", "RequiredSubscription": "Forfait requis au préalable", "ResetFilters": "Réinitialiser les filtres", "Resolved": "R<PERSON>ol<PERSON>", "Results": "Résultats 🏆", "ResultsDetail": "Voir le détail résultats", "ResultsOf": "Résultat de", "ResultsSynthesis": "Synthèse Résultats", "Resultsfor": "Résultats pour :", "RevisionCalendar": "Calendrier révisions", "Revisions": "Révisions", "RevisionsModule": "<PERSON><PERSON>le Révisions", "RichText": "Texte enrichi", "RightPart": "<PERSON><PERSON> droite", "Room": "Salle", "SCQ": "QCU", "SCQs": "QCUs", "SIRET": "SIRET", "SaveAndClose": "Enregistrer et fermer", "ScaleName": "Nom du barême", "Score": "Score", "ScoreType": "Type de barême", "SearchByMail": "Rechercher par email...", "SearchByUsername": "Rechercher par pseudo...", "SearchKeywords": "Rechercher par mot clé...", "SearchNotions": "Rechercher parmi les notions...", "SecondaryMedias": "Supports secondaires", "SectionIcon": "Icône de section (optionnel)", "SectionName": "Nom de la section", "SeeAll": "Voir tout", "ParticipantsWillSeeThisInTheirPlanning": "Les participants verront ceci dans leur planning", "SeeAnswers": "Voir les réponses", "SeeCorrection": "Voir la correction", "SeeCorrectionAndResults": "Voir correction et résultat", "SeeCorrectionOf": "Voir la correction de", "SeeDetails": "Voir les détails", "SeeMcq": "Voir l'exercice", "SeeMySubscription": "Voir mon abonnement", "SeePrintedSerie": "<PERSON><PERSON><PERSON><PERSON>", "SeeProgression": "Voir profil", "SeeQuestionsWithoutAnswers": "Voir les questions sans réponses", "SeeRelatedCourse": "Cours concerné", "SeeRelatedExercice": "Voir l'exercice concerné", "SeeResultAndCorrection": "Voir résultat et correction de l'élève", "SeeThisExercice": "Voir cet exercice", "Seen": "Vus", "Seene": "<PERSON><PERSON>", "SelectCategory": "Sélectionner catégories", "SelectCorrection": "Sélectionnez une correction", "SelectCourse": "Sélectionner cours", "SelectCourseXtoY": "Sé<PERSON><PERSON><PERSON> toutes les questions ayant le cours X et leur attribuer le cours Y", "SelectNotions": "Sélectionner notions", "SentMessages": "Messages envoyés", "SerarchByFirstName": "Rechercher par prénom...", "SerieAppendix": "Annexes de la série", "SerieDescription": "Description de la série", "SerieOrder": "Ordre dans les séries", "SerieTitle": "Titre de la série", "SessionName": "Nom de la session", "Settings": "Paramètres", "Visibility": "Visibilité", "ShowAll": "<PERSON><PERSON> afficher", "SimulateStudentAccount": "Simuler compte étudiant", "SimulateTeacherAccount": "Simuler compte professeur", "SizeInPixels": "Taille en pixels", "SkillLevel": "<PERSON><PERSON><PERSON> <PERSON> ma<PERSON>:", "SorryUnfindableExercice": "<PERSON><PERSON><PERSON><PERSON>, cet exercice est introuvable !", "Start": "<PERSON><PERSON><PERSON><PERSON>", "StartYear": "<PERSON><PERSON>", "State": "État", "StatsAndUserActivity": "Stats & activités utilisateurs", "StepName": "Nom de l'étape", "StudentResults": "Résultats élèves", "SubectsProgress": "Progression par matière", "SubjectReferent": "Responsables matière", "SubjectsProgression": "Progression par Matières", "SubscriptionDescription": "Description de l'offre", "SubscriptionImage": "Image de l'offre", "SubscriptionName": "Titre de l'offre", "Subscriptions": "Abonnements", "Success": "Su<PERSON>ès", "SuccessConditions": "Conditions de réussite", "SummarySerieExercice": "🌟 Résumé sur la série d'exercices", "Supports": "Supports", "SureOfDeletion": "Certain de supprimer?", "SureToChangeTypeElement": "En changeant de type vous allez perdre les modifications de cet élément, êtes vous certain ?", "SureToDeleteGroups": "Êtes vous certain de supprimer ce groupe ? Si il y a des utilisateurs dans ce groupe, ils n'y seront plus", "SureToDelinkCourse": "Certain de l'enlever de ce cours? L'exercice ne sera pas supprimé", "SureToDelinkQuestion": "Certain de retirer la question de la série ?", "SureToQuitUnsavedChange": "Certaines modifications ne sont pas enregistrées. Êtes-vous certain de quitter ?", "Synchronisation": "Synchronisation", "TeacherPanel": "Mes apprenants", "Team": "Équipe", "TermsAndConditions": "Termes et conditions d'utilisations", "TermsAndSellConditions": "Conditions Générales de Vente", "TestYourself": "Se tester", "TheMoreExerciceYoudo": "Plus vous faites d'exercices, plus le système pourra vous aider à optimiser vos révisions !", "TimeByQuestion": "Temps par exercice", "Timed": "Chronomètre", "TimedExercice": "Exercice chronométré", "Title": "Titre", "TitleLevel": "Niveau de titre (1,2,3)", "TitleLevelExplanation": "Les titres de niveau 1 sont plus hauts hiérarchiquement que les niveaux 2, 3, etc.", "TitleName": "Nom du titre", "TodayPlanning": "Planning d'aujourd'hui", "Top": "Top", "Total": "Total", "TotalDuration": "Durée totale", "TrainingMaterialsAboutCourse": "Exercices (annales, ou autres) sur le cours", "TrainingName": "Nom de la formation", "TryThisExercice": "Tester cette série", "Type": "Types", "TypesToShow": "Types à afficher", "UnlockGroupWhenFinishExercice": "Groupes débloqués quand ce quizz est terminé", "UnsavedModificaitons": "Modifications non enregistrées", "Unselect": "Déselectionner", "Update": "Mettre à jour", "UpdateInfos": "Autres informations", "UpdateMedia": "Mettre à jour le support", "UpdatedAt": "Mis à jour à", "UsersRegistered": "Me<PERSON><PERSON>", "VatNumber": "Numéro de TVA", "Version": "Version", "Video": "Vidéo", "Videos": "Vid<PERSON><PERSON>", "VisibleByAll": "Visible par tous", "VisibleInList": "Visible dans la liste", "Visualize": "Visualiser", "Warning": "Warning", "WarningNoLinkedCourses": "Attention, vous n'avez pas sélectionné de cours lié à cet exercice. La plateforme fonctionne moins bien si l'exercice n'est relié à aucun cours. Souhaitez-vous continuer ?", "WebsiteEditor": "  ÉDITEUR du site", "WereNotDebited": "Vous n'avez pas été débité.", "WhatsAnExerciceType": "Qu'est-ce qu'un type d'exercice ?", "WhyDates": "Pourquoi des dates ?", "WhyDoYouReportThisComment": "Pourquoi signalez-vous cette publication ?", "WhyReport": "Pourquoi signalez-vous", "WorkTimeAdvised": "Temps de travail conseillé", "WriteYourAnswer": "Saisissez votre réponse", "WriteYourMessage": "Ecrivez votre message...", "XLSExportUsersAndResults": "Export XLS utilisateurs et résultats", "Year": "<PERSON><PERSON>", "YouAlreadyDidAllExercicesForThisPeriod": "Vous avez déjà fait tous les exercices sur cette période", "YouCanAddMoreAfterCourseCreation": "Vous pourrez ajouter d'autres supports, informations une fois l'item créé.", "YouThinkThat": "Vous pensez que...", "YourChoice": "<PERSON><PERSON><PERSON> choix", "accessibleCourses": "Cours accessibles", "afficherLesImagesSurLesItems": "Afficher les images sur les items", "afficherLesLmentsAuDessusDesQcms": "Afficher les éléments au dessus des exercices", "afficherLesTagsTypeDeQuestionCoursEtc": "Afficher les tags (Type de question, cours, etc.)", "allCategoriesFromSelectedSubjects": "Toutes les catégories des Matières sélectionnées", "allCourses": "Tous les cours", "annalesDuCours": "<PERSON><PERSON>urs", "app_name": "Exoteach", "back": "Retour", "biographyExplanation": "Pour les curieux qui visiteront ce profil", "by": "par", "capitalSocial": "Capital social", "chooseCategories": "Choisissez les catégories", "chooseCourses": "Choisissez les cours", "chooseTheType": "Choisir le type...", "comment": "commentaire", "comments": "commentaires", "correctionGrid": "Grille de correction", "course": "cours", "courseUpdated": "Cours mis à jour", "ValidateMyChoices": "Valider mes choix", "createCourse": "<PERSON><PERSON><PERSON> un cours", "createNewSubject": "<PERSON><PERSON><PERSON> une nouvelle matière", "createNewFormation": "Créer une nouvelle formation", "createUser": "<PERSON><PERSON><PERSON> cet utilisateur", "currentMonth": "<PERSON><PERSON> actuel", "currentPassword": "Mot de passe actuel", "deleteThisCourse": "Supprimer ce cours", "deleteThisResult": "Supprimer ce résultat", "disableAutoNotionAnalysis": "Désactiver analyse automatique des notions", "discussions": "discussions", "doComment": "commenter", "editAvatarPicture": "Modifier avatar...", "email": "email", "IgnoreThisExercise": "Ignorer cet exercice", "faitesCetExerciceAuMoinsUneFois": "Faites cet exercice au moins une fois", "WriteTo": "<PERSON><PERSON><PERSON><PERSON> à", "DeletedMessage": "Message supprimé", "NewGroupConversation": "Nouvelle conversation de groupe", "NoMessage": "Aucun message", "MessageError": "Erreur lors de la récupération utilisateur", "ConvDoesntExistAnymore": "Cette discussion n'existe plus", "AuthorDeletedConv": "Son auteur l'a supprimé ou elle n'est plus accessible", "EnterAMessage": "Entrez un message...", "Calendars": "Calendriers", "UpdateAndSync": "Mettre à jour et synchroniser", "Calendar": {"ShowPlanningRevisions": "Planning des J", "RecurringEventModalTitle": "L'évènement que vous modifiez est récurrent.", "RecurringEventModalDescription": "L'évènement pour lequel vous modifiez la date est récurrent. Souhaitez-vous reporter uniquement cette occurrence au {{dateStart}} ou bien changer la date pour cet évènement et tous les autres à venir ?", "RecurringEventModalModifyAll": "Tous les evenements", "RecurringEventModalModifySingle": "Uniquement cet évènement", "NewEvent": "Nouvel évènement", "URL": "URL", "ical": "iCal", "AutoSelectMyGroupsTooltip": "Sélectionne automatiquement mes groupes et ceux dont je suis responsable", "Type": "Type de calendrier", "ExternalCalendar": "Calendrier externe", "Source": "Source", "OtherUsers": "Autres utilisateurs", "MyAgendas": "Mes agendas", "ShowOnTheSide": "Afficher sur le côté de l'agenda", "Events": "Évènements", "CourseTitle": "Titre du cours", "SureToRemoveCalendar": "Certain de supprimer ce calendrier ?", "Revisions": "Révisions", "RevisionsGenerated": "Révisions générées", "AutoGenerate": "Génération auto", "DateChanged": "Date modifiée", "ChooseASubject": "<PERSON><PERSON> une matière", "CustomizePlanningTitle": "Personnaliser mon planning des J", "Syncing": "Sychronisation en cours...", "Synced": "Sychronisation terminée !", "File": "<PERSON><PERSON><PERSON>", "NewJ0": "Définir un J0", "SearchEvents": "<PERSON><PERSON><PERSON>", "SyncNow": "Synchroniser maintenant", "SureToSyncNow": "Synchroniser ce calendrier maintenant ?", "SureToAutogenerateAll": "<PERSON>la auto-génère toutes les dates de révision à partir des dates initiales de cours dans le calendrier, êtes vous sûr ?", "Updated": "Calendrier mis à jour avec succès", "CreateExternalCalendar": "<PERSON><PERSON>er un calendrier externe", "Warning1": "Les calendriers extérieurs ne peuvent pas être modifiés sur ExoTeach", "InfoForURL": "Le calendrier se mettra à jour automatiquement toutes les 5 minutes avec l'URL", "InfoForFile": "Attention : le calendrier ne se mettra pas à jour automatiquement avec un fichier", "CalendarAdminInfo": "Vous pouvez ajouter des calendrier externes via des URL iCal pour les rendre visibles à des groupes ou utilisateurs.", "Name": "Nom du calendrier"}, "AIUser": "Utilisateur IA", "general": {"Configuration": "Configuration", "Yesterday": "<PERSON>er", "Message": "Message", "Activate": "Activer", "Admin": "Admin", "Properties": "Propriétés", "Advices": "Conseils", "Appendix": "Annexes", "Average": "<PERSON><PERSON><PERSON>", "Avg": "<PERSON><PERSON>.", "By": "Par", "Check": "Vérifier", "Close": "<PERSON><PERSON><PERSON>", "Confirmation": "Confirmation", "Continue": "<PERSON><PERSON><PERSON>", "Create": "<PERSON><PERSON><PERSON>", "Dashboard": "Dashboard", "Details": "Détail", "Disabled": "Désactiver", "Display": "Affichage", "Download": "Télécharger", "Duplicate": "<PERSON><PERSON><PERSON><PERSON>", "CreateAVariant": "<PERSON><PERSON>er une variante", "CreateACopy": "<PERSON><PERSON>er une copie", "Edit": "Modifier", "Emojis": "Emojis", "Events": "Évènements", "Exams": "Examens", "FALSE": "FAUX", "Field": "<PERSON><PERSON>", "Filter": "<PERSON><PERSON>", "Folder": "Dossier", "Folders": "Dossiers", "Grade": "Note", "Groups": "Groupes", "Help": "Aide", "Home": "Accueil", "Ignore": "<PERSON><PERSON><PERSON>", "Informations": "Informations", "Item": "<PERSON><PERSON>", "Lesson": "Cours", "List": "Liste", "Login": "<PERSON><PERSON>", "Manage": "<PERSON><PERSON><PERSON>", "Months": "<PERSON><PERSON>", "MostRecent": "Plus récents", "Notifications": "Notifications", "Notions": "Notions", "OK": "OK", "Page": "Page", "Platform": "Plateforme", "Profile": "Profil", "Published": "<PERSON><PERSON><PERSON>", "Publishede": "Publiée", "Question": "Question", "QuestionAnswer": "Questions / réponses", "Questions": "Questions", "Redo": "<PERSON><PERSON><PERSON>", "Remove": "En<PERSON>er", "Reports": "Signalements", "Result": "Résultat", "Results": "Résultats", "Retry": "<PERSON><PERSON><PERSON><PERSON>", "Role": "R<PERSON><PERSON>", "Scale": "<PERSON><PERSON><PERSON>", "Security": "Sécurité", "to": "vers", "See": "Voir", "SocialNetwork": "Réseaux sociaux", "StartWith": "Commence par", "Statistics": "Statistiques", "Subject": "<PERSON><PERSON>", "Subjects": "<PERSON><PERSON><PERSON>", "Subscription": "Abonnement", "Suggestions": "Suggestions", "SuperAdmin": "SuperAdmin", "TRUE": "VRAI", "Timezone": "<PERSON><PERSON> horaire:", "Title": "Titres", "Top": "Top", "User": "Utilisa<PERSON>ur", "Username": "Nom d'utilisateur", "Validate": "Valider", "Value": "<PERSON><PERSON>", "add": "Ajouter", "address": "<PERSON><PERSON><PERSON>", "avatar": "avatar", "back": "Retour", "biography": "Biographie", "city": "Ville", "Gender": "Genre", "ExtraTime": "Tiers temps", "BirthDate": "Date de naissance", "Nationality": "Nationalité", "classic": "Classique", "confirm": "Confirmer", "dislike": "Dislike", "Move": "<PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON><PERSON>", "email": "Email", "emoji": "emoji", "firstname": "Prénom", "import": "Importer", "includes": "Contient", "logins": "Identifiant", "logo": "logo", "moreAddress": "Adresse complémentaire (optionnel)", "no": "Non", "pedagogy": "Pédagogie", "phoneNumber": "Numéro de téléphone", "preferences": "Préférences", "question": "question", "questions": "questions", "save": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "signUp": "Inscription", "teacher": "Professeur", "total": "Total", "type": "Type", "untreacable": "introuvable", "upload": "<PERSON><PERSON> le <PERSON>", "yes": "O<PERSON>", "Administrators": "Administrateurs", "Beginning": "D<PERSON>but", "Calendar": "<PERSON><PERSON><PERSON>", "Created!": "<PERSON><PERSON>é !", "Edition": "Édition", "End": "Fin", "Group": "Groupe", "Language": "<PERSON><PERSON>", "NotDone": "Non terminé", "Rank": "Classement", "Students": "Étudiants", "Tutors": "Professeurs", "Unclassified": "Non classé", "Updated!": "Mis à jour !", "Updating...": "Mise à jour...", "available": "disponibles", "or": "ou", "rank": "classement", "required": "requis", "ChangeParentSubject": "<PERSON>r <PERSON>", "Forums": "Forums", "Loading...": "Chargement...", "NotPublished": "Non-publié", "Publish": "Publier", "UnPublish": "Dé-publier", "Avatar": "Avatar", "Appearance": "Apparence", "Arborescence": "Arborescence", "ArtificialIntelligence": "Intelligence artificielle", "Buildings": "Bâtiments", "Company": "Entreprise", "Country": "Pays", "Integrations": "Intégrations", "Links": "<PERSON><PERSON>", "Payment": "Paiement", "Subscriptions": "Abonnements", "group": "groupe", "Building": "Bâtiment", "Exercice": "Exercice", "Exercices": "Exercices", "Week": "<PERSON><PERSON><PERSON>", "Weeks": "Se<PERSON>ines", "custom": "<PERSON><PERSON><PERSON><PERSON>", "lastname": "Nom", "reSee": "Revoir", "Commercial": "Commercial", "Parent": "Parent", ">=": "≥", "!=": "≠", "<=": "≤", "<": "<", ">": ">", "==": "=", "SelectCoursesPlaceholder": "Sélectionner des cours", "SelectUeForSeriePlaceholder": "Sé<PERSON><PERSON>ner une matière", "AdminMenuTemplates": "Templates", "FullScreen": "Plein écran"}, "globalAnnounces": "Annonces", "globalCorrection": "Corrigé", "gradeObtained": "Note obtenue", "hide": "Masquer", "lastMessages": "Derniers messages", "login": {"autoConnect": "Se souvenir de moi", "enterYourEmail": "Veuillez renseigner votre email", "enterYourLogin": "Veuillez entrer votre identifiant", "enterYourPassword": "Veuillez entrer votre mot de passe", "error": "Erreur : identifiants incorrects ou reCAPTCHA Invalide", "forgotPassword": "Mot de passe oublié", "forgotPasswordError": "Erreur: Les services sont indisponibles. Veuillez réessayer plus tard.", "subscribeMessage": "Pas encore de compte ? Inscrivez-vous", "tellYourEmail": "Veuillez renseigner votre email", "username": "Nom d'utilisateur ou email"}, "myDiscussions": "Mes discussions", "newEvent": "Nouvel évènement", "newExam": "Nouvel examen 📝", "newPassword": "Nouveau mot de passe", "newScale": "Nouveau barême", "nextMonth": "<PERSON><PERSON> suivant", "none": "aucun", "page": "page", "pageDeGarde": "Page de garde", "pages": "pages", "pasEncoreDeCompteInscrivezvous": "Pas encore de compte ? Inscrivez-vous", "password": "Mot de passe", "period": "🗓 Période", "placeholderDescription": "Cours sur les lipides", "placeholderTitle": "01: <PERSON><PERSON> entier", "pleaseEnterCourseName": "<PERSON><PERSON><PERSON>z entrer le nom du cours", "pourVoirCeCommentaireOuPoserUneQuestion": "pour voir ce commentaire ou poser une question !", "previousMonth": "<PERSON><PERSON>", "printType": "Type d'impression", "probablyDangerousCompetitor": "Sans doute un dangereux concurrent", "pseudoNewUser": "Pseudo (nom utilisateur visible par les autres)", "received": "re<PERSON><PERSON>", "result": "résultat", "results": "résultats", "resultsAnalysis": "Analy<PERSON> de vos résultats", "searchByName": "Rechercher par nom...", "send": "Envoyer", "show": "<PERSON><PERSON><PERSON><PERSON>", "showMyPanel": "Afficher mon panneau", "stripe": "Stripe", "student": "Étudiant", "subjectOnly": "Questions seules", "tab": {"General": "🚀 Général", "Options": "🛠 Options", "Supports": "📒 Supports", "Activity": "⏳ Activité", "Annexe": "📁 Annexe", "ChangeQuestionsOrderCSV": "📥 Changer ordre des questions d'après liste (CSV)", "Dates": "🗓 Dates", "Edit": "🚀 É<PERSON>er", "ImportQuestionsFromFile": "📥 Importer un exercice depuis un fichier", "ImportQuestionsRelationsParentChildrenCSV": "📥 Importer relations parents-enfants (CSV)", "ImportQuestionsXLS": "📥 Import questions (XLS)", "Notifications": "🔔 Notifications", "Preview": "🔎 Aperçu", "Modules": "🛠 <PERSON><PERSON><PERSON>", "FinishedExams": "🎓 Examens terminés", "Exam": "⚙️ Examen", "Progress": "Progression", "Transcript": "Relevé de notes", "TrainingResults": "Résultats entraînement", "Exams": "Examens", "Configuration": "Configuration", "Rewards": "Récompenses", "GoodAnswersBySubject": "Bonnes réponses par matière", "Overview": "Vue d'ensemble", "Publications": "Publications", "TimeSpentExercising": "Temps passé à s'entraîner", "TimeSpentPerDay": "Temps passé par jour", "Move": "<PERSON><PERSON><PERSON><PERSON>", "DatesParticipants": "Dates & Participants", "ExercicesSerie": "✅ Séries d'exercices"}, "updateMyPackage": "Mettre à jour mon abonnement...", "userCode": "Code utilisateur (optionnel)", "users": "utilisateurs", "version": "version", "welcome": "Bienvenue", "whereToFindThisNotion": "<PERSON><PERSON> trouver cette notion", "workInProgress": "Work in progress", "youCanChangelater": "Vous pourrez modifier cet utilisateur, ses groupes plus tard si besoin.", "yourGrade": "Votre note", "zipCode": "Code postal", "1Column": "1 colonne", "1toNGroupsToUsersInGroup": "1 à n groupes aux utilisateurs dans le groupe", "2Columns": "2 colonnes", "ASubjectWithoutGroupWillBeInvisible": "Une matière ou formation sans groupe associé sera invisible", "ActivateNotificationsExplanation": "Activez les notifications par matière pour être notifié des nouveaux sujets postés", "ActiveUsers": "Utilisateurs actifs", "AddAGroupOfParticipant": "Ajouter un groupe de participant", "AddAGroup": "Ajouter un groupe", "AddAParticipant": "Ajouter un participant", "AddContentType": "Ajouter un type de contenu", "AddElementHere": "Ajouter un élément ici", "AddExamSession": "Ajouter une session d'examen", "AllContentTypes": "Tous les types de contenu", "AllowsClassificationOnlyForAdmins": "Permet une classification, visible pour administrateurs uniquement", "AlreadyHaveBook": "J'ai déjà le livre, ou un code d'accès", "AppearAsResponsibleHelpText": "Cochez cette case si vous souhaitez que votre profil apparaisse dans la liste des responsables de matières, si vous en êtes responsable. Uniquement pour professeurs et administrateurs", "AppearInTeamHelpText": "Cochez cette case si vous souhaitez que votre profil apparaisse dans la liste des membres de l'équipe. Uniquement pour professeurs et administrateurs", "AppearInTeamTab": "Apparaitre dans l'onglet Équipe", "AppearsAsSubjectResponsible": "Apparaître comme responsable matière", "AskAQuestion...": "Poser une question...", "AskForDeletionPersonnalData": "Vous pouvez demander à ce que votre compte, et toutes les données personnelles associées, soient supprimées définitivement.", "Attribute1toNNotions": "Attribuer 1 à N notions pour tous les items correspondant", "AttributeCourseToo": "Attribuer aussi sur les cours liés aux exercices", "AttributeNotion": "Attribuer des notions", "AutomaticNotionAnalysis": "Analyser automatiquement les notions", "BasedOnExercisesResultsWeAdviseYouToStudy": "Basé sur vos résultats d'exercice nous vous conseillons les révisions suivantes :", "BeginExercise": "Démarrer la série d'exercice", "BlockTitle": "Titre du bloc", "BlockType": "Type de bloc", "BuyDiagnosis": "Acheter mon diagnostic sans le livre", "ByDefaultCanBeDoneInfinitelyButFirstResultIsSaved": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, peut être refait à l'infini (0), mais seul le premier résultat est enregistré et pris en compte dans les stats.", "CategoryToSelect": "Catégorie à sélectionner", "CertaintyLevelEvaluated": "Niveau certitude évalué", "ChangeUserPassword": "Changer mot de passe", "ChooseCategoryToAttribute": "Choisir sous catégorie à laquelle attribuer un cours", "ChooseSubscriptions": "Choisir abonnements", "TypeAdded": "Type ajouté", "TypeRemoved": "Type enlevé", "ClickHereToManageGroupPermissions": "Cliquez ici pour gérer les permissions par groupe", "ContainsOnlyExternalQuestions": "Contient seulement des exercices externes", "ContinueWithComplementaryQuizz": "Continuer avec le questionnaire complémentaire", "CopyrightExoteach": "© ExoTeach 2020-2024", "CourseThatWillBeAssociated": "Cours qui sera associé à tous les exercices dont la catégorie est ci-dessus", "CreateAndAddAnother": "<PERSON><PERSON><PERSON> et ajouter un autre", "CreateAndAddQuestions": "Créer et ajouter des exercices", "CreateAndClose": "<PERSON><PERSON><PERSON> et fermer", "CreateGroupChat": "<PERSON><PERSON>er une conversation de groupe", "CreateNewQuestion": "Créer nouvel exercice", "CreateNewScale": "<PERSON><PERSON>er un nouveau barême", "CreateNewSerieIn": "Créer nouvelle série dans", "CreateNewSerie": "<PERSON><PERSON>er une nouvelle série", "DateOfCompletion": "Date de réalisation", "Deepen": "Approfondir", "DeleteThisMessage": "Supprimer ce message", "Diagnostic": "Diagnostic", "DiagnosticPart1": "Diagnostic partie 1", "Duration": "<PERSON><PERSON><PERSON>", "EditFiche": "Éditer fiches", "EditThisUser": "Modifier cet utilisateur", "ExamScales": "Barêmes de l'examen", "ExamSession": "Sessions d'examen", "ExerciceExplanation": "explication exercice", "ExerciseSerieRealisedBy": "C<PERSON><PERSON> par", "ExplanationDiagnosis": "Le questionnaire de diagnostic vous permettra d'optimiser votre utilisation du manuel tout en vous faisant découvrir les principaux sujets abordés.", "ExplanationExportPDF": "cliquez ici puis sélectionner \"Enregistrer au format PDF\"", "ExplanationExportResultPDF": "Cliquez sur exporter pour exporter votre résultat au format PDF.", "ExplanationGenerationPlanning": "Cliquer sur Générer mon planning de révision pour générer le planning avec la méthode des J.", "ExportExplanation": "La correction n'est actuellement pas inclue dans l'export. Pour avoir la totalité de la correction au format PDF,", "ExportLoading": "Exportation en cours...", "ExportMyResults": "Exporter mon résultat", "GenerateMyPlanning": "Générer mon planning de révision", "GeneratedQuizz": "Entraînement", "GeneratedQuizzCorrection": "Correction entraînement", "GlobalStats": "Stats globales", "GoodAnswer": "Bonne réponse", "GoodAnswers": "Bonnes répo<PERSON>s", "ICheckedRandom": "J'ai mis au hasard", "ICheckedRandomFlashcard": "<PERSON>", "IWantToBuyTheBook": "Je veux acheter le livre", "ImageCourse": "Image du cours", "Importing...": "Importation en cours....", "KeyWord": "<PERSON>t clé", "mobileMode": "Mode mobile", "MCQTitle": "Titre de l'exercice", "ManageExercicesSeries": "Séries d'exercices", "ModeratlySure": "Moyennement sûr", "ModeratlySureFlashcard": "Plutôt sur", "MoveDown": "<PERSON><PERSON><PERSON><PERSON> vers le bas", "MoveLeft": "<PERSON><PERSON><PERSON>r vers la gauche", "MoveRight": "<PERSON><PERSON><PERSON>r vers la droite", "MoveUp": "<PERSON><PERSON><PERSON><PERSON> vers le haut", "MyTrainings": "Mes formations", "NextQuestionStrategy": "Stratégie de passage des exercices", "NoCoursesOnDate": "<PERSON><PERSON>", "NoUserBlocked": "Aucun utilisate<PERSON> bloq<PERSON>", "Normal": "Normal", "Button": "Bouton", "NotSure": "<PERSON><PERSON> sûr", "NotSureFlashcard": "Hésitant", "OpenBook": "livre ouvert", "OutOfCompetitionDoesNotAffectYourGrade": "Hors concours, n'influe pas sur votre note", "Parents/Childs": "Parents / Enfants", "PaymentMethodUndefinedPleaseContactAdmin": "Moyen de paiement non défini ! Contactez l'administrateur.", "Permissions": "Permissions", "Permissions individuelles": "Résultats élèves", "PleaseCheckAtLeastOneItem": "Veuillez cocher au moins un item avant de continuer", "PleaseChooseAtLeastOnePackageToRegister": "Veuillez choisir au moins un forfait pour vous inscrire !", "PleaseClickOnUpdateOrRefreshManually": "Veuillez cliquer sur le bouton \"Actualiser\" ou actualiser la page manuellement", "PleaseEnterMaterialName": "Veuillez entrer le nom de la matière", "PleaseEnterMcqName": "Veuillez entrer le titre", "PublishedOn": "<PERSON><PERSON><PERSON> le", "QuestionId": "ID question", "ReStudyFromScratch": "Reprendre de<PERSON><PERSON> le dé<PERSON>", "RealGrade": "Note réelle", "RefreshMyPlanning": "Actualiser mon planning", "Remove1toNnotions": "Retirer 1 à N notions pour tous les items correspondant", "RemoveCourseAsWell": "Re<PERSON>rer aussi sur les cours liés aux exercices", "RemoveNotions": "<PERSON><PERSON><PERSON> les notions", "ReportErrorOrAskQuestion...": "<PERSON><PERSON><PERSON><PERSON> une discussion", "ResultsBySeries": "Résultats par série", "ResultsIn": "Résultats en", "ResultsSuccessfullySaved": "Vos résultats ont bien été enregistrés !", "ResumeTraining": "Reprendre la formation", "Resume": "Reprendre", "RevisionPlanning": "🎓 Calendrier de révision", "SeeMyResultAndCorrectionAgain": "Revoir mon résultat et la correction", "SeeOriginalExercice": "Voir exercice original", "SeeOriginalExerciceSerie": "Voir série d'exercice", "Select": "Sélectionnez", "SelectAStepToAddElement": "<PERSON><PERSON><PERSON> et sélectionner une étape sur le menu latéral pour ajouter un élément.", "SelectAllQuestionWithCategory": "Sélectionner tous les exercices avec pour sous catégorie X et les attribuer au cours Y", "SelectAllQuestionsToAttribute": "Sélectionner tous les exercices avec pour sous catégorie X et les attribuer au cours Y", "SelectAnAnswer": "Sélectionnez une réponse", "SelectOneOrMoreAnswers": "Sélectionnez une ou plusieurs réponses", "Smart": "Intelligent", "StartTheTraining": "Commencer la formation", "StartTraining": "Démarrer la formation", "Still": "Encore", "SubscribeForFreeExplanation": "Inscrivez vous gratuitement, puis mettez à jour votre offre en achetant des crédits à dépenser !", "Sure": "Certain", "SureFlashcard": "Certain", "SureToDelete": "Certain de supprimer ?", "SureToDeleteMessage": "Certain de supprimer ce message ?", "SureToDeleteYourAccount": "Êtes vous certain de demander à supprimer votre compte?", "SureToRemoveScale": "Êtes vous certain d'enlever ce barême de l'examen ?", "SureToRemoveSession": "Êtes vous certain d'enlever cette Session de l'examen ?", "TermsAndCondition": "Te<PERSON><PERSON> et conditions", "ThisDeletionWillBe": "Cette suppression sera", "ThisExerciseCanBeDone": "Cette série peut être faite", "TimeRemaining": "Temps restant", "TimesUp!": "Temps écoulé !", "Train": "<PERSON>'entraîner", "TrainOnThisNotion": "Entraînez-vous sur cette notion", "TypeOfTitle": "Type de titre", "UpdateAvailable!": "Mise à jour disponible !", "UserGroups": "Groupes de l'utilisateur", "VisualizeAverageRankAndAnalysisForAllGroupsText": "Visualisez la moyenne, classement et analyse des résultats pour tous les groupes, ou pour un ou plusieurs groupes spécifiques.", "WarningCustomPlanningWillBeErased": "Attention: Vos personnalisation seront écrasées (le J0 sera la date de diffusion en amphi). Con<PERSON><PERSON>?", "YouAlreadyDidAllQuestionForThisPeriod": "Vous avez déjà fait tous les exercices 🎉", "YouDidThisExercise": "Vous avez fait cet exercice", "YouStillHave": "Il vous reste ", "YourAnswer": "Votre réponse", "YourAnswers": "Vos réponses", "YourDeleteRequestWillBeTreated": "Votre demande de suppression a bien été prise en compte et sera traitée sous 48h.", "accurate": "juste", "accuratePlural": "justes", "at": "le", "credit": "crédit", "credits": "crédits", "definitive": "définitive", "effective": "effective", "error": "<PERSON><PERSON><PERSON>", "fiche": "Fiche", "ficheName": "Nom de la fiche", "fiches": "Fiches", "generalPlanning": "📅 Planning général", "groupQuestionsSameCourseWithSeparators": "Grouper les exercices de même cours avec intercalaires", "groupsHavingAccess": "groupes ayant accès", "inSeconds(90secByDefault)": "en secondes, (90 sec par défaut)", "isPublishedVisibleByStudents": "Publié (accès libre par les élèves)", "itGives": "<PERSON><PERSON>", "lastUpdateOn": "dernière mise à jour le", "mitochondrie": "mitochondrie", "ofSuccess": "<PERSON><PERSON>", "on": "sur", "orAskAQuestion...": "ou poser une question...", "outOf": "sur", "pleaseEnterName": "Veuillez entrer le nom", "points": "points", "Points": "Points", "proposal": "proposition", "proposals": "propositions", "ref": "ref", "resetMyPlanning": "Ré-initialiser mon planning", "showCorrectionAfterEachQuestion": "Afficher la correction après chaque exercice", "standardUser": "Utilisateur standard", "thisCourse": "ce cours", "thisNotion": "cette notion", "toUse": "à utiliser", "toUseLater": "à utiliser plus tard", "tutor": "Professeur", "under48h": "sous 48h", "Actions": "Actions", "Action": "Action", "AllMyCourses": "Tous mes cours", "AllowsYouToCombineLearningContentInOrder": "Vous permet de combiner plusieurs contenus pédagogiques de vos différentes matières dans un ordre particulier.", "AllowsYouToCreateEditLearningContent": "Classez votre contenu pédagogique pour un domaine particulier. Vous pouvez créer autant de dossiers / sous dossiers que vous voulez", "CreateSubjectExplanation": "C<PERSON>ez une matière pour rassembler tous vos cours sur un même thème au même endroit. Les étudiants y accèdent librement, sans ordre imposé — idéal pour les formations longues (médecine, lycée, etc.)", "CreateFormationExplanation": "Créez un parcours composé de modules d'apprentissage, d'exercices et d'évaluations que l'étudiant devra compléter afin de valider sa formation. Suivez sa progression pas à pas jusqu'à l'obtention de son certificat.", "CreateCourseExplanation": " Vous pourrez ajouter des supports de cours (PDF, powerpoint, Word etc) ou bien créer directement votre cours via notre éditeur. Tous les cours peuvent être reliés à des exercices.", "CreatePageExplanation": "Personnalisez une page pour vos apprenants qui n'est PAS UN COURS.", "CreatePageExplanation2": "Exemple: Présentations de la matière, liens vers des ressources complémentaires ...", "ImportCourseExplanation": "Vous pouvez importer un cours que vous avez déjà créé ailleurs.", "ImportCourseExplanation2": "Modifier un cours importé le changera partout.", "AreYouSureToDeleteImage": "Certain de supprimer cette image ?", "CreatedAt": "<PERSON><PERSON><PERSON>", "Creator": "<PERSON><PERSON><PERSON>", "Author": "<PERSON><PERSON><PERSON>", "CustomizeTitlesType": "Personnaliser les types de titres...", "DeleteExistingImage": "Supprimer l'image existante", "DoQuestions": "Faire les questions", "DoThisExerciseAtLeastOnceToSeeComments": "Faites cet exercice au moins une fois pour voir les commentaires ou poser une question !", "EditedAt": "<PERSON><PERSON><PERSON><PERSON>", "EditedAtTag": "(modifié)", "Fuse": "<PERSON><PERSON>", "ImpliedCourses": "📒 Cours impliqués", "ImpliedNotions": "🌀 Notions impliquées", "NothingInThisCategory": "Il n'y a rien dans cette catégorie", "SeeUserResultsForThisExercise": "Voir les résultats des élèves", "ShowQuestionCorrection": "Afficher la correction des questions", "Status": "Statut", "StopEditing": "<PERSON><PERSON><PERSON><PERSON>'<PERSON><PERSON><PERSON>", "TitleAndDescription": "Titre et description", "YouAreResponsibleOfTheseGroups": "Vous êtes responsables des groupes suivants", "YourPackageDoesNotAllowYouToHaveAccessToContentForNow": "Votre forfait ne vous permet pas d'avoir accès à du contenu pour le moment...", "createNewGuidedFormation": "Créer une nouvelle formation guidée", "haveBeenFoundForThisCourse": "ont été trouvées pour ce cours", "ofType": "de type", "AddSubLegend": "Ajouter une sous légende", "questionsInThisSubject": "Questions sur cette matière", "selecteds": "sélectionnés", "youAreResponsibleOfAllGroupsAsSuperAdmin": "Vous êtes responsables de tous les groupes en tant que SuperAdmin", "AnnounceByGroups": "Annonces par groupe(s)", "Beginning": "D<PERSON>but", "ChooseOneOrSeveralSubjects": "<PERSON>sir une ou plusieurs matière(s)", "Created": "<PERSON><PERSON><PERSON> avec succès", "DeletedWithSuccess": "Supprimé avec succès", "In": "<PERSON><PERSON>", "NoTopic": "Aucun sujet", "NonResolved": "non-résolu", "RestoredWithSuccess": "Restauré avec succès", "TopicMarkedAs": "Sujet marqué comme", "TopicNotFound": "Sujet introuvable", "Updated": "Mis à jour avec succès", "aSubject": "une matière", "aTraining": "une formation", "aFormation": "une formation", "exerciseGenerator": {"ExerciseTypeText": "Les exercices ont pu être triés selon différents types. Par exemple certaines question peuvent être issues d'annales concours et d'autres sont des questions créées de toutes pièces. Sélectionnez le type de question sur lesquelles vous souhaitez travailler", "HowItWorksText": "Cette page vous permet de créer votre propre série d'exercices avec les paramètres que vous souhaitez ! Choisissez les matières / chapitres et type d'exercice sur lesquels vous souhaitez vous entrainer, et notre système intelligent ira rechercher et concocter pour vous la série d'exercice qu'il vous faut !", "WhyDatesText": "Des exercices ont pu être créés chaque année, vous pouvez indiquer au générateur si vous souhaitez vous entrainer sur des exercices plus ou moins anciens. Les plus récents seront la plupart du temps plus proche du programme", "AddAPackOfExercises": "Ajouter un lot d'exercices..."}, "CreateExercice": "Créer l'exercice", "SelectExerciceType": "Sélectionner un type d'exercice", "ChangeCourse": "Changer de cours", "ChangeQuizz": "Changer de quizz", "Content": "Contenu", "DeleteThisConversation": "Supprimer cette conversation", "Description(Optionnal)": "Description (optionnel)", "ElementTitle": "Titre de l'élément", "Failure": "Échec", "FileName": "Nom du fichier", "LaunchSmartNotionAnalysisForPDF": "Ré-analyser les notions", "MoveThisCourseToAnotherCategory": "<PERSON><PERSON><PERSON><PERSON> le cours dans une autre catégorie", "Preview(image)": "<PERSON><PERSON><PERSON><PERSON> (image)", "QuizzSettings": "Paramètres du quizz", "SelectedCourse": "Cours sélectionné", "SelectedQuizz": "Quizz sélectionné", "Text": "Texte", "VideoEmbedLink": "Lien embedded de la vidéo", "VideoTitle(optionnal)": "Titre de la vidéo (optionnel)", "current": "actuel", "showRevisionModule": "Afficher le module Révisions", "BlockAction": "Bloquer", "ByCategories": "Par catégories", "ByCourses": "Par cours", "ByNotions": "Par notions", "CreateAStep": "<PERSON><PERSON>er une Étape", "Exam": "Examen", "ExamNotFound": "Examen introuvable", "theImportedCourse": "le cours importé", "time": "fois", "given": "donn<PERSON>", "YourAverage": "<PERSON><PERSON><PERSON> moyenne", "YourAnswersAreColorFramed": "Les réponses que vous avez cochées sont encadrées en couleur", "YouNeverDidThisExerciseSerie": "Vous n'avez jamais fait cette série d'exercice.", "ExerciseResumeWithSuccessPercentText": "Résumé de votre exercice, avec % de réussite", "ExportThisUsersResults": "Exporter les résultats de cet utilisateur", "ExportThisUsersPlanning": "Exporter le planning de cet utilisateur", "ExportToExcel": "Exporter en Excel", "GeneralAverage": "Moyenne générale", "ImportExistingCourse": "Importer un cours existant", "LeastMasteredPoints": "Points les moins maitrisés", "MissingTranslation": "Traduction manquante", "NewDiscussion": "Nouvelle discussion", "NoNotification": "Aucune notification", "ProgressOf": "Progression de", "RegisteredSince": "Inscrit depuis le", "ResultsAnalysisGlobalText": "Notre intelligence artificielle a analysé vos résultats globalement. Vous pouvez ainsi visualiser facilement quels sont les sujets que vous maitrisez le mieux et optimisez vos révisions. Dépliez les différentes catégories pour obtenir un maximum d'informations !", "ResultsAnalysisOnExerciceText": "Notre intelligence artificielle a analysé vos résultats sur cette série d'exercice. Vous pouvez ainsi visualiser facilement quels sont les sujets que vous maitrisez le mieux et optimisez vos révisions. Dépliez les différentes catégories pour obtenir un maximum d'informations !", "ResultsHistoryText": "Historique : vous avez fait cette série ", "SeeProgressAnd": "Voir progression et", "SeeProgressAndTranscript": "Voir progression et relevé", "SendAMessage": "Envoyer un message", "ShowOnlyFirstResults": "Afficher seulement les premiers résultats", "StrongPoints": "Points forts", "TimeSchedule": "Emploi du temps", "NewGroup": "Nouveau groupe", "NewFolder": "Nouveau dossier", "ToReworkPoints": "Points à retravailler", "UnblockAction": "Débloquer", "UserProfileNotFound": "Oh, oh ! Ce profil utilisateur est introuvable...", "WeakPoints": "Points faibles", "OnlyFirstTryAverage": "Seule votre 1ère tentative sur cette série d'exercice compte dans la moyenne", "AboutContentType": "A propos des types de contenu", "AdressRequired": "Adresse requise", "ChangeJ0": "Changer le J0", "CityRequired": "Ville requise", "Complexity:High": "Complexité: forte", "Complexity:Low": "Complexité：faible", "Complexity:Medium": "Complexité：moyenne", "ContactableByPrivateMessage": "Contactable par message privé", "CourseNotificationOnMobile": "Notifications de cours sur app mobile", "CustomizeMySchedule": "Personnaliser mon planning", "EmailRequired": "Email requis", "ExercisesNotificationsOnMobile": "Notifications des exercices sur app mobile", "ExplanationContentType": "Exoteach vous permet de tout classifier correctement dans votre plateforme. Ceci est indispensable pour une bonne cohésion pédagogique et optimiser la plateforme pour vos apprenants", "FirstNameRequired": "Prénom requis", "InvalidEmail": "Adresse email non valide", "MyRevisionSchedule": "Mon planning de révision", "NameRequired": "Nom requis", "NoBiographyYet": "Aucune bio pour le moment", "PasswordsDontMatch": "Les mots de passe ne correspondent pas", "PhoneNumberRequired": "Numéro de téléphone requis", "PleaseConfirmPassword": "Veuillez confirmer le mot de passe", "PleaseEnterPassword": "Veuillez saisir le mot de passe", "PleaseTypeAtLeast6Character": "Veuillez saisir au moins 6 caractères. N'utilisez pas de mots de passe faciles à deviner.", "PostCodeRequired": "Code postal requis", "QuestionsDone": "Questions faites", "QuestionsWithoutLinkedCourse": "Exercices sans cours lié", "ReceiveNotificationWhenNewCourseIsPublished": "Recevoir une notification quand un nouveau cours est mis en ligne sur l'application mobile", "ReceiveNotificationWhenNewExerciseIsPublished": "Recevoir une notification quand une nouvelle série d'exercice est mis en ligne sur l'application mobile", "ThanksYourAnswersHaveBeenProcessed": "<PERSON><PERSON><PERSON>, vos réponses ont bien été prises en compte !", "UsernameRequired": "Identifiant requis", "UsersCanSendYouPrivateMessages": "Les utilisateurs peuvent vous envoyer des messages privés", "operationEnMasse": "Opération en masse", "AddModule": "Ajouter un module", "AllChallenges": "Tous les challenges", "Challenge": "Challenge", "ChallengeType": "Type(s) de challenge", "CongratsYouCompletedAllQuestionsAvailable": "<PERSON>, vous avez terminé tous les exercices disponibles ici !", "ExplainQuestionProblem": "Expliquez le problème de la question", "GoToNextQuestionWhenTimesUp": "Passer automatiquement à l'exercice suivant si le temps est écoulé", "HurryUp!": "<PERSON>épê<PERSON>z-vous !", "ReportErrorOrAskQuestionFor": "<PERSON><PERSON><PERSON><PERSON> une discussion", "SecondsPerExercise": "Secondes par exercice", "Stopwatch": "Chronométré", "SureToRemoveChallenge": "Certain de supprimer ce challenge ?", "allChallenges": "Tous les challenges", "messageToShowWhenChallengeDone": "Message à afficher lorsque le challenge est réussi", "AddAcceptedAnswerItem": "Ajouter une réponse acceptée", "Alphanumerical": "Alphanumérique", "ChallengeDetails": "Détail challenge", "CompletedChallenges": "Challenges terminés", "ConserveTimePassedOnSerie": "Conserver la durée en cas d'interruption et de reprise de la série", "DefaultCreatedExerciseTypes": "Type(s) par défaut des exercices créés", "DeleteDefinitely": "Supprimer définitivement", "DeleteDefinitelyThisQuestionOrRemoveFromSerie?": "Supprimer définitivement cet exercice de la base, ou l'enlever de cette série ?", "DoAtLeastOneExerciseInThisSubjectToSeeYourStrongPoints": "Faites au moins un exercice dans cette matière pour voir vos points forts et points faibles !", "EnableQuestionComments": "Activer les commentaires utilisateur sur cet exercice", "FaviconHelp": "Icône affiché dans l'onglet du navigateur. Habituellement au format .ico. Il faut parfois recharger la page ou attendre quelques minutes pour voir l'icône changer.", "GlobalTime": "Temps global", "GroupedActions": "Actions groupées", "IndividualPermissions": "Permissions individuelles", "InfiniteUntilNoMoreQuestions": "Infini - jusqu'à épuisement des exercices", "MyAccount": "Mon compte", "MyPublications": "Mes dernières publications", "NoPosts": "Aucun post", "NotStarted": "Non commencé", "PersonalizedTraining": "Entraînement personnalisé", "Personalized": "<PERSON><PERSON><PERSON><PERSON>", "PremadeTraining": "Série existante", "RemoveFromExerciseSerie": "Enlever de la série", "SubjectsForWhichYoureResponsible": "<PERSON><PERSON>(s) dont vous êtes responsable:", "UserIsResponsibleForThosesSubjects": "Reponsable des matières suivantes...", "TimerOptions": "Options du chronomètre", "TotalTimeSpentThisWeek": "Temps total passé à s'entraîner cette semaine :", "YouHaveNoTimeLeftYourAnswersHaveBeenSaved": "Vous n'avez plus de temps disponible, vos réponses ont été enregistrées !", "YouHaveSuccessfullyCompletedThisChallenge": "Félicitations vous avez terminé le challenge !", "YourAnswersHaveBeenSaved": "Vos réponses ont été enregistrées", "YourResultsWillBeAvailableLaterByYourTeachers": "Vos résultats seront rendus disponibles plus tard par votre équipe pédagogique", "rewardsToUnlock": "récompenses à débloquer", "Unfinished": "Non terminé", "IncludeExercisesAlreadyDone": "Inclure exercices déjà faits", "IncludeSchemasAlreadyDone": "<PERSON><PERSON>re schémas déjà faits", "MyLibrary": "Ma bibliothèque", "aFolder": "un dossier", "createNewFolder": "<PERSON><PERSON>er un nouveau dossier", "numberOfUsers": "Nombre d'étudiant(s)", "AcceptCGUButton": "J'accepte les CGU", "AddAProduct": "Ajouter un produit", "AddAPromoCode": "Ajouter un code promo", "AddNewAttachment": "Ajouter une pièce jointe", "AddOption": "Ajouter une option", "AddUserInfoFolder": "Ajouter un dossier d'informations", "Alls": "Tous", "Attachments": "Pièces jointes", "BilledBy": "Facturé par", "Border": "Bordure", "ChangedAuthorFor": "L'auteur a été changé pour", "CoursePreviewHelp": "Personnalisez l'aperçu avec une image ou l'aperçu automatique", "CreateNewPage": "<PERSON><PERSON>er une nouvelle page", "DateAndTimePicker": "Sélection date et heure", "StartTime": "<PERSON><PERSON> d<PERSON>", "EditThisOccurence": "Modifier cette occurrence", "EndTime": "Heure de fin", "DatePicker": "Sélection de date", "DetailedResults": "Résultats détaillés", "EditUser": "Modifier un utilisateur", "EmailBody": "Corps du mail", "EmailSubject": "Objet du mail", "ErrorChangeAuthorRights": "L'user sélectionné n'a pas les droits pour être auteur d'exercices", "Everyone": "<PERSON>ut le monde", "GroupsSeeingThisOffer": "Groupes pouvant voir cette offre", "EveryoneWillSeeThisForfait": "Tout le monde verra cette offre", "FileAttachment": "Fichier joint", "GeneralDetails": "<PERSON><PERSON><PERSON>", "IAcceptThe": "J'accepte les", "IHaveReadCGUCheckbox": "J'ai lu les CGU", "ImportQuestionWithAI": "Importer exercice(s) par IA", "ImportXlsInfo": "Vous pouvez importer plusieurs élèves en même temps sur votre plateforme à l'aide d'un tableur à remplir. Vous pourrez choisir dans quels groupes les élèves sont ajoutés au moment de l'import", "Installments": "Plusieurs fois", "Invoice": "Facture", "Invoices": "Factures", "LinkToConditionsOfSales": "Lien vers les conditions générales de vente", "LinkToSalesSite": "Lien vers le site de vente", "LongAnswer": "Paragraphe", "Mandatory": "Obligatoire", "ManualAuthorEdition": "Édition manuelle de l'auteur", "ManualAuthorEditionExplication": "Permet d'assigner manuellement un auteur lors de l'édition d'un exercice", "MultipleChoice": "Choix multiple", "MultipleChoiceOffer": "Choix multiple", "NoOne": "<PERSON><PERSON>", "NoOneWillSeeThisForfait": "<PERSON>ne ne verra cette offre", "Offers": "Offres", "ChooseOffers": "Choisir des offres", "OffreCocheParDefaut": "Offre cochée par défaut", "OneTime": "Une fois", "OnlyPeopleInGroupsWillSeeThisForfait": "Seuls les groupes sélectionnés verront cette offre", "Option": "Option", "PaymentAmount": "Paiement en plusieurs fois", "PaymentHistory": "Activité", "PaymentType": "Type de paiement", "PdfPreview": "Aperçu du fichier PDF", "ProductName": "Nom du produit", "PromoCode": "Code promo", "PromoCodesAuthorized": "Code(s) promo autorisés", "QuestionAuthor": "<PERSON><PERSON><PERSON>", "QuestionModification": "Modification", "RejectCGUButton": "Je refuse les CGU", "Rooms": "<PERSON><PERSON>", "Seats": "Places", "ShortAnswer": "Réponse courte", "SimpleOffer": "Offre simple", "UniqueChoice": "Choix unique", "UniqueChoiceOffer": "Choix unique", "UserProperties": "Propriétés utilisateur", "isNotPublishedVisibleByStudents": "Non publié", "offreNonDecochable": "Offre non-décochable", "DDS": {"AllowRedo": "Per<PERSON>re de refaire la DDS", "DDS": "Diapo de synthèse", "ForceToDoInOrder": "Forcer de faire dans l'ordre", "ShowProgress": "<PERSON><PERSON><PERSON><PERSON> la <PERSON>"}, "payment": {"AdditionnalBillingInfo": "Mentions addition<PERSON>es de bas de facture", "EventTypesToAdd": "Types d'évènements à ajouter au webhook", "HookSecret": "Secret du hook (commence généralement par whsec_)", "PaymentMethod": "Moyen de paiement", "PaymentMethodDescription": "Configurez ici un moyen de paiement que vous souhaitez utiliser pour vos ventes. Le paiement par Monetico requiert de contacter le support ExoTeach pour finaliser la configuration.", "PaymentMethodHelp": "<PERSON><PERSON><PERSON> et configurez vos méthodes de paiement dans \"Paiement\"", "PaymentName": "Nom du moyen de paiement", "PrivateKey": "Clé privée (commence généralement par sk_)", "PublicKey": "Clé publique (commence généralement par pk_)", "WebhookUrl": "URL du webhook", "WebhookUrlHelp": "Vous devez ajouter ce webhook dans votre dashboard Stripe pour que les paiements soient pris en compte.", "paymentMethod": {"monetico": "Monetico", "stripe": "Stripe"}, "state": {"cancelled": "<PERSON><PERSON><PERSON>", "paid": "<PERSON><PERSON>", "unpaid": "Non payé"}, "type": {"installment": "Plusieurs fois", "once": "Une fois"}}, "rooms": "salles", "watermark": {"AxisName": "Placement de {{coordonate}} :", "ColorDescription": "Couleur : ", "CoodonnatesInPercentRule": "Le placement sur l'axis {{coordonate}} doit être entre 0 et 100%", "CustomDescription": "Phrase personnalisée :", "InputPageTooBig": "Cette option nécessite de renseigner entre 1 et la taille de votre pdf (nombre de pages : {{maxValue}})", "IsNotEditable": "Le cours est en format PDF mais n'est pas modifiable (probablement protégé par un mot de passe).", "IsNotPdf": "Le cours n'est pas en format PDF. Le Module Watermark ne supporte que des cours en format PDF", "NeedAtLeastOne": "Le module Watermark a été activé, mais aucune option n'a été choisie. Il est nécessaire d'en choisir au moins une.", "NeedCustom": "Le filigrane avec phrase personnalisée a été activé, mais aucune phrase n'a été renseignée. Il est nécessaire de renseigner la phrase.", "NeedInt": "Cette option nécessite de renseigner un entier", "NeedPage": "Cette option nécessite de renseigner au moins une page", "NeedPicture": "Le filigrane sur les images a été activé, mais aucune image n'a été renseignée. Il est nécessaire de renseigner au moins une image.", "OpacityDescription": "Opacité :", "OpacityValueRule": "Veuillez entrer une valeur d'opacité entre 0 et 1", "PoliceSizeDescription": "Taille de police :", "PoliceSizeRule": "Veuillez entrer une taille de police entre 1 et 100", "PresetBlack": "Noir", "PresetBlue": "Bleu", "PresetGreen": "<PERSON>ert", "PresetRed": "Rouge", "RemovePictureFile": "Supprimer l'image", "RotateValueRule": "Veuillez entrer un angle de rotation entre 0 et 360", "RotationDescription": "Rotation :", "WatermarkAtLeastOneCourseForPictureUpload": "Il faut avoir sélectionné au moins un cours pour pouvoir upload une image", "WatermarkDoesNotApply": "Le Module Watermark n'opérera pas. Le téléchargement standard sans filigrane aura lieu.", "WatermarkMassOperationWarning": "Seuls les cours au format PDF et non protégés porteront le filigrane. Les autres cours resteront téléchargeables normalement.", "WatermarkModule": "Module Watermark", "WatermarkPictureFileName": "Nom de l'image :", "applyWatermark": "Module Watermark,", "custom": "Phrase personnalis<PERSON>", "email": "Email", "firstName": "Prénom", "lastname": "Nom", "phone": "Numéro de téléphone", "picture": "Image", "radioChoice": "Détermination des pages concernées ", "radioChoiceValueAll": "Toutes les pages", "radioChoiceValueChoose": "Uniquement ces pages :", "radioChoiceValueOneEach": "Une page sur :", "showLogDetails": "Détail des modifications", "username": "Nom d'utilisateur", "InvalidPictureType": "Le fichier image n'a pas une extension valide", "WatermarkFileOption": "Filigrane", "ImportFromCourse": "Importer les paramètres d'un autre cours", "WatermarkParameters": "Paramètres de la watermark", "WatermarkExistingTemplateOrNeedCours": "Pour modifier ce paramètre, il faut avoir sélectionné un cours ou avoir créé le template."}, "Buildings": "Bâtiments", "Company": "Entreprise", "Payment": "Paiement", "Redoability": "Refaisabilité", "AddCourses": "Ajouter des cours", "AddForumSection": "Ajouter une nouvelle catégorie au forum", "AddRemoveGroups": "Ajouter/Retirer des groupes à ces utilisateurs", "SureToDeleteGroup": "Voulez-vous vraiment supprimer ce groupe ?", "SureToDeleteGroup2": "Cette action est irréversible", "EditGroup": "Modification de groupe", "ProcessingExport": "Exportation des utilisateurs en cours...", "AllPlannings": "Tous les plannings", "AllowIAToAnswer": "Autoriser l'IA à répondre", "AreYouSuretoPublishSerie": "Êtes vous certain de vouloir publier la série ?", "Availability": "Disponibilité", "BuildingName": "Nom du bâtiment", "ByGroups": "Par groupes", "ByUsers": "Par utilisateurs", "ResponsibilityGroups": "Des groupes", "ResponsibilityUsers": "Des utilisateurs", "CORRECTION": "CORRECTION", "ChooseCustomSubjects": "🗂 Choisissez sur quoi vous entrainer", "ChooseRooms": "Choisir des salles...", "Colors": "Couleurs", "CoursesLinked": "Cours liés", "CoursesLinkedToThisEvent": "Cours liés à cet évènement", "CreateNewBuilding": "Ajouter un nouveau Bâtiment", "CurrentQuestionScale": "Barême actuel de l'exercice", "DeleteAllParticipants": "Supprimer tous les participants", "DeleteThisEvent": "Supp<PERSON>er cet évènement", "EditEvent": "Éditer l'évènement", "EditSerie": "Éditer la série d'exercice", "EditSerieInfos": "Éditer informations de la série", "EnableEventComments": "Autoriser les commentaires", "EnabledLanguages": "Langues activées", "Event": "<PERSON><PERSON><PERSON><PERSON>", "EventDiscussion": "Espace discussion 💬", "ExamAvailable": "Examen disponible", "ExplanationDefaultExercice": "Lorsque vous créez un nouvel exercice dans cette série, c'est le type qu'il prendra automatiquement", "FaireAppel": "Faire l'appel", "FilterExercicesSeries": "Filtrer les séries ", "FilterQcm": {"AddYear": "Ajouter une année", "AllSubjectSelected": "Toutes les matières", "AllTypes": "Tous les types", "Allyears": "Toutes les années", "CheckAllUe": "<PERSON><PERSON>", "DeletedExercicesSeries": "Séries supprimées", "NoSubjectSelected": "<PERSON><PERSON><PERSON>", "PerCreationDate": "Par date de création", "PerCreator": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "PerExercicesSeriesTitle": "Par titre de série", "PerLastModifDate": "Dernière modification", "PerSubject": "<PERSON><PERSON>(s)", "PerType": "Par types", "PerYears": "Par ann<PERSON>", "RemoveAllUe": "<PERSON><PERSON>", "ResetFilter": "Réinitialiser", "SearchSerieExercice": "Rechercher une série", "SeeMore": "Voir plus", "SelectMySubjects": "Selection des matières", "SelectType": "Selectionner des type", "VariableSubjectSelected": "{{numberMatieres}} <PERSON><PERSON><PERSON>", "SelectUePlaceholder": "Sélectionner des matières"}, "Fixed": "Fixe", "From...": "À partir de...", "ScaleConfig": "Configuration du barème", "GradeConfig": "Configuration de la notation", "GradeExplanation": "Explications", "IfAnAnswer": "Si une réponse", "InfoPromptPrecision": "Donner des indications supplémentaires à votre IA améliorera grandement la qualité de ses réponses !", "Join": "Rejoindre", "JoinEvent": "Rejoindre le lien de l'évènement", "KeyWords": "<PERSON>ts clés", "Location": "<PERSON><PERSON>", "Locations": "<PERSON><PERSON>", "NumberOfAnswers": "Nombre de questions:", "PlanningForAllMyGroups": "Planning de tous les groupes", "PlanningMode": "Mode planning", "PointsLost": "Points perdus", "PromptPrecision": "Précisions pour l'IA (facultatif)", "ResultsHistory": "Historique de résultats ", "Precision": "Précision", "SelectExerciceSerie": "Sélectionner une série", "point": "point", "RoomsPlanning": "🏛️ 🪑 Planning des salles", "SORTING": "CLASSIFICATION", "SUBJECT": "SUJET", "ScaleDefaultSubjectsInfo": "Par défaut quand vous ajoutez un exercice dans une matière, un barème est pré-sélectionné. Vous pouvez en utiliser d'autres quand même. Pour quelle matières voulez vous que ce barème soit appliqué par défaut", "SeeEvent": "Voir l'évènement", "SerieExercices": "Exercice(s) de la série", "SupportsForThisEvent": "Supports pour cet évènement", "TimeLine": "Timeline", "TimeLineMode": "Mode timeline", "TitleAndDescriptionAndTime": "Titre / Description / Chrono", "Users": "Utilisateurs", "VariableByTotalAnswers": "Variable : par nombre de réponses totales", "VariableByTrueAnswers": "Variable : par nombre de réponses vraies", "YouDidThisSerie": "Vous avez fait cette série d'exercice", "changeModuleLessons": "  Changer les modules de cours", "definedPeriod": "Période Définie", "deleteThis": "Supprimer ce forum", "displayResults": "Afficher le résultat final pour les étudiants", "eventLocation": "Lieu de l'évènement", "exerciceFormat": "Format d'exercice", "finalExamResults": "Résultat final", "infosSerie": "Informations de la série d'exercice", "isChecked": "est cochée", "isNotChecked": "n'est pas cochée", "masteryLevel": "<PERSON><PERSON><PERSON> <PERSON>", "possibleErrors": "Erreur(s) possible(s)", "ApiKeys": {"ApiKey": "Clé API", "ApiKeys": "Clés API", "name": "Nom de la clé", "Create": "<PERSON><PERSON><PERSON> une clé API", "Update": "Mettre à jour une clé API", "description": "Description de la clé"}, "presence": {"Absent": "Absent", "Present": "Présent", "Undefined": "Non-définie"}, "remainingTrials": "tentatives restantes", "AnswerVerifiedBy": "Réponse vérifiée par", "AreYouSatisfiedWithTheAnswer?": "Êtes-vous satisfait de la réponse ?", "AskingForHumanHelp": "Aide humaine demandée", "GenerateAIAnswer": "Générer une réponse IA", "MarkAsVerified": "Marquer comme vérifié", "NoINeeedHumanAdvice": "Non, je souhaite un avis humain", "INeedHumanAnswer": "Réponse humaine", "INeedPrecisions": "La réponse ne me convient pas", "GenerateNewAIAnswer": "Nouvelle réponse IA", "TellUsWhatsWrong": "Préciser ce qui ne va pas", "AiAnswerSatisfaction": {"minimumCharacters": "<PERSON><PERSON><PERSON> ce qui ne convient pas dans la réponse de l'IA (encore {{charsMissing}} caractères)", "GiveUsMoreDetails": "<PERSON><PERSON> de nous aider à mieux vous répondre, merci de préciser ce qui ne convient pas dans la réponse ou ce que vous n'avez pas compris.", "theMoreTheBetter": "Plus votre demande sera détaillée, meilleure sera la réponse", "requiredQuery": "Pour que la réponse soit la plus précise possible, veuillez indiquer ce qui ne convient pas"}, "YesThanks!": "Oui merci !", "SureOfDuplication": "Dupliquer ce formulaire ?", "ReadMore": "Lire la suite", "ChoosePlannings": "Sélectionner des planning", "Course(Shortcut)": "Cours (raccourci)", "CustomPlanningExplanationPlaceholder": "À quelle date aura lieu votre Examen de probabilité ?", "CustomPlanningExplanationLabel": "Expliquer à l'élève quelle date sélectionner pour le J0", "WhoCanUseThisCustomPlanning": "Qui peut utiliser ce planning personnalisé ?", "AddCustomPlanning": "Ajouter un planning personnalisé", "Only...": "Uniquement...", "NotificationContent": "Contenu de la notification", "CustomPlanning": "Planning personnalisé", "ProgressAverage": "Progression moyenne", "HidePrice": "Masquer le prix total", "AfficherAnnexesSerie": "Afficher les annexes de la série", "onlyForParentsWarning": "Attention, cet utilisateur n'est pas un Parent. Vous pouvez changer son rôle dans les détails généraux.", "OrderConfirmation": "Confirmation de votre commande", "UsernameTooltip": "Votre identifiant est un pseudo qui vous permettra de vous connecter sur la plateforme. Il sera visible par les autres utilisateurs", "PasswordTooltip": "Le mot de passe vous servira à vous connecter sur la plateforme", "globalAnnouncesForParents": "Annonces pour les parents", "PayTotal": "Payer le total", "PayByInstallments": "Payer en plusieurs fois", "PayLater": "Payer plus tard", "SeriesDone": "Séries faites", "CoursesSeen": "Cours vus", "QuestionsLinkedToDeletedCourses": "Exercices liés à des cours supprimés", "DoneIn": "Term<PERSON><PERSON> en", "PredefinedUserField": "Champ prédéfini utilisateur", "UserDoesNotExist": "L'utilisateur n'existe pas", "RegisterFields": "Champs d'inscription", "DeferredPayment": "Paie<PERSON> différé (chèque ou autre)", "ExplainToUserHowToFinishSubscription": "Expliquer à l'utilisateur comment terminer son inscription", "ChooseYourAvatar": "Choisissez votre avatar", "News": "News", "ShowInNews": "Afficher dans les news", "FormElements": "Éléments du formulaire", "ChooseAnAvatarOrUploadOne": "Choisir un avatar ou en uploader un", "ChooseAnAvatar": "<PERSON><PERSON> un avatar", "CreateElementBeforeSettingUpWatermark": "<PERSON><PERSON>ez l'élément avant de pouvoir gérer les options de filigrane.", "ConfigIA": "Configuration de l'IA", "CustomMyIA": "Personnaliser mon IA", "CustomMyIAInfo": "Choisissez sur quel cours l'IA intervient et à quel(s) groupe(s) d'élève elle répond", "OpenAIKey": "Clé OpenAI (chatGPT)", "YouMustChooseOpenAIKey": "<PERSON><PERSON> de<PERSON> relier votre IA à une clé OpenAI", "Forms": "Formulaires", "Hidden": "<PERSON><PERSON><PERSON><PERSON>", "FormAlreadyAnswered": "Vous avez déjà répondu à ce formulaire", "YouCantAnswerFormAgain": "Vous ne pouvez pas le recommencer. Si vous pensez qu'il s'agit d'une erreur, contactez vos professeurs.", "backToHome": "Retour à l'accueil", "Planification": {"Planifications": "Planifications", "Planification": "Planification", "AddPlanification": "Ajouter une planification", "Title": "Titre de la planification", "ExecutionDate": "Date d'exécution", "PermissionsAtExecutionDate": "Permissions à la date d'exécution", "NoPlanification": "Aucune planifications"}, "MarkAsUnVerified": "Marquer comme non-vérifié", "FilterQuestions": {"CheckAllCourses": "<PERSON><PERSON>", "UncheckAllCourses": "<PERSON><PERSON>", "FilterQuestions": "Filtrer les questions", "SeeMore": "Voir plus", "PerCourses": "Par cour(s)", "SelectMyCourses": "Sélection des cours", "PerDiscussType": "Par type de discussion", "AllTypes": "Tous les types", "AllDiscussTypes": "Toutes les discussions", "PerCategory": "Par catégories", "SelectAllCategories": "Tous les types de contenu", "PostTypeQCM": "EXERCICE", "PostTypeCOURS": "COURS", "PostTypeFORUM": "FORUM", "PostTypeEVENT": "EVENEMENT", "PostTypeQUESTION_ANSWER": "EXERCICE", "PerResolved": "<PERSON><PERSON><PERSON><PERSON>", "PerAiResolved": "Résolues par l'IA", "PerAiResponse": "Répondues par l'IA", "PerHumanHelp": "<PERSON><PERSON><PERSON>", "SelectedCourses": "Cours sélectionnés", "Resolved": "R<PERSON>ol<PERSON>", "UnResolved": "Non résolu", "ResolvedByAi": "Résolu Par IA", "UnResolvedByAi": "Non résolu par IA", "Creators": "Createur(s)", "PerCreator": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "NotAskingHumanHelp": "Ne demande pas d'aide humaine", "AskingHumanHelp": "<PERSON><PERSON>e aide humaine", "AiAnswered": "Répondu par l'IA", "NotAiAnswered": "Non répondu par l'IA", "PerTitle": "Par titre de question", "SearchTitle": "Rechercher un titre", "PerQuestionContent": "Par contenu de question", "SearchQuestionContent": "Rechercher une question", "ResetFilter": "Réinitialiser", "ResolvePost": "Marquer comme Résolu", "UnResolvePost": "Marquer comme non Résolu", "FilterQuestions.ResultNumber": "Nombre de questions", "ResultNumber": "Nombre de questions", "PerCreationDate": "Date", "CreationDate": "Date de création", "Cours": "Cours", "Forum": "Forum", "Category": "<PERSON><PERSON><PERSON><PERSON>", "State": "État", "Actions": "Actions", "LastFeedback": "Dernier feedback", "LastFeedbackExplanation": "Affiche la dernière réponse postée par un Admin, Tuteur ou IA", "PerQuestionType": "Par type de question", "PerExerciseType": "Par type d'exercice", "PerCategories": "Par catégories", "AllQuestionTypeRootComponant": "Tous les types de question", "AllExerciseTypeRootComponant": "Tous les types d'exercices", "WarningExerciseFilterMultipleCategoriesAndExerciseFiltration": "Attention, vous filtrez sur les types d'exercises mais plusieures catégories ont étées sélectionées. Seuls les questions associées aux exercices sélectionnées seront affichés", "WarningExerciseFilterNoCategoriesAndExerciseFiltration": "Attention, vous filtrez sur des type d'exercices sans avoir sélectionné la catégorie Exercice, rien ne sera affiché. Veuillez sélectionner la catégorie Exercice", "FilterPerAssociatedContent": "Filtrer par type de contenu associé", "FilterPerAssociatedExerciseType": "Par type d'exercice associé", "GlobalPlateFormContentFilter": "Filtre global sur la plateforme", "AllPostTypeTag": "Tous les type de discussions"}, "Review": {"LeaveReview": "Laisser un avis", "ReviewNumber": "<PERSON><PERSON>", "LeaveReviewButton": "Laisser un avis", "PlaceholderInputReview": "Qu'avez vous pensé de ce cours ?", "PublishReview": "Publier mon avis !", "UpdateReview": "Modifier mon avis !", "CancelSubmitReview": "Annuler", "LastUpdate": "édité", "Show": "afficher", "Lines": "<PERSON><PERSON><PERSON>", "Hide": "cacher", "MyReview": "Mon avis", "Reviews": "Les a<PERSON>", "Modify": "modifier", "EnableCoursReview": "Activer la notation du cours", "EnablePublicReview": "Avis visibles par tous", "DeleteReview": "<PERSON><PERSON><PERSON><PERSON>", "RefreshToDelet": "Recharger la page pour actualiser", "AdminDeletConfirmation": "Confirmez-vous la suppression ? L'opération est irréversible.", "GiveMark": "Il faut renseigner une note"}, "UpdateGeneral": "Mettre à jour l'onglet général", "UpdateModule": "Mettre à jour l'onglet module", "ProgressionRadarGraph": {"GraphSelectedUEs": "<PERSON><PERSON><PERSON>", "SelectedCategories": "Catégories", "TooMuchSelection": "La sélection est limitée à {{nb_max}} éléments", "CheckAll": "Tout cocher", "UncheckAll": "<PERSON><PERSON>", "DoMySelection": "Faire ma sélection", "ResultsByCategories": "Résultats par chapitres", "ResultsOverall": "Résultats", "NoResults": "Pas de notes", "SelectRadarGraph": "Radar", "SelectHistoGraph": "Histogramme", "SelectBinYear": "<PERSON><PERSON>", "SelectBinMonth": "<PERSON><PERSON>", "SelectBinWeek": "<PERSON><PERSON><PERSON>", "SelectBinDay": "Jour", "Date": "Date", "Note": "Note", "UserMoyenne": "Moyenne de l'utilisateur", "UserNumber": "Exercices réalisés par vous", "StructureMoyenne": "Moyenne générale", "StructureNumber": "Exercices totaux réalisés", "AverageGrade": "Moyenne générale", "YourGrade": "<PERSON><PERSON><PERSON> moyenne", "QuestionsDone": "Exercices Réalisés", "CanChooseSelection": "Personnaliser ", "SelectedElements": " Éléments sélectionnés", "ShowMyResults": "Ma progression", "ShowStructureResults": "Moyenne générale"}, "AdminStatPage": {"FilterByGroups": "Filtrer par groupes...", "SearchByPseudo": "Ch<PERSON><PERSON> un pseudo", "SearchByRole": "Filter par rôles", "SearchByCompany": "Filtrer par entreprises"}, "EnterCompanyNameForfaitForm": "Veuillez entrer le nom de l'entreprise", "EnterValidEmailCompanyForm": "Veuillez renseigner un email valide", "EnterSocialCapitalCompanyForm": "Veuillez renseigner le capital social de l'entreprise", "EnterCommercialNameCompanyForm": "Veuillez renseigner le nom commercial de votre entreprise", "ValidesCompaniesNames": "Entreprises valides :", "SmallTagErrorInvalidCompanyId": "Entreprise invalide", "ExplanationDuplicateErrorCompaniesNames": "L'import ne peut pas fonctionner s'il y a des entreprises avec des noms identiques.", "UserMassChange": {"UserMassChangeSelectername": "Opération en masse sur les élèves", "ButtonUpdateTypeAdd": "Ajouter", "ButtonUpdateTypeRemove": "En<PERSON>er", "ButtonUpdateTypeReplace": "<PERSON><PERSON>lace<PERSON>", "UserList": "Liste des utilisateurs", "noUsersInGroups": "Aucun utilisateur dans les groupes sélectionnés", "TargetTypeInvalid": "La cible du changement est invalide", "ModifTypeInvalid": "L'argument de type de modification est invalide", "SelectIsEmpty": "Veuillez choisir des groupes ou des users", "ChooseElementsToModify": "Éléments à modifier :", "ModificationType": "Type de modification", "ElementTarget": "Élément à modifier", "Groups": "Groupes", "Companies": "Entreprises", "SelectGroupsToModify": "Sélectionner les groupes à modifier", "SelectGroupsToApply": "Sélectionner les groupes à appliquer", "SelectCompaniesToApply": "Sélectionner les entreprises à appliquer", "SelectUsersToModify": "Sélectionner les utilisateurs à modifier", "AdminReplaceConfirmation": "Confirmez-vous le remplacement ? L'opération est irréversible.", "ConfirmCheckBox": "<PERSON> <PERSON><PERSON>", "ReplaceExplanation": "La configuration de la sélection en cours sera effacée et seuls les éléments que vous avez indiqués seront ajoutés.", "deleteUsersByGroupsWarning": "Les utilisateurs seront retirés de leurs groupes puis supprimés sans possibilité de récupération. S'ils sont connectés, ils seront automatiquement déconnectés de la plateforme. Si il y a des superadmins parmi les utilisateurs sélectionnés, ils ne seront pas supprimés.", "deleteUsersByGroupsTitle": "Supprimer les utilisateurs par groupes", "deleteUsersByGroupsConfirm": "Confirmer la suppression des utilisateurs ?", "deleteUsersByGroupsIrreversible": "Cette action est irréversible. Voulez-vous continuer ?", "deleteUsersByGroupsButton": "Supprimer les utilisateurs sélectionnés", "usersDeletedWithSuccess": "Utilisateurs supprimés avec succès !", "usersDeleteError": "<PERSON><PERSON>ur lors de la suppression des utilisateurs.", "deletingUsers": "Opération en cours", "loadingUsersCount": "Chargement du nombre d'utilisateurs en cours...", "usersInGroupsCount": "Nombre d'utilisateurs dans les groupes sélectionnés (hors superadmin)", "usersInSelectedGroups": "utilisateurs", "deletingUsersInProgress": "Suppression en cours..."}, "ExercisesMassChange": {"ExercisesMassChangeTitle": "Opération en masse sur les exercices", "DeleteExercisesByType": "Supprimer exercices par type", "DeleteSeriesByType": "Supprimer séries d'exercices par type"}, "DeleteMassExerciseByType": {"title": "Suppression d'exercices par type", "deleting": "Suppression en cours...", "deletingInProgress": "Les exercices sont en cours de suppression. Veuillez patienter.", "deletingSuccess": "Suppression terminée", "exercisesDeleted": "exercices ont été supprimés", "deleteConfirmation": "Es-tu sûr de vouloir supprimer tous les exercices liés à ces types ?", "buttonText": "Supprimer les exercices"}, "DeleteMassExerciseSeriesByType": {"title": "Suppression de séries d'exercices par type", "deleting": "Suppression en cours...", "deletingInProgress": "Les séries d'exercices sont en cours de suppression. Veuillez patienter.", "deletingSuccess": "Suppression terminée", "exercisesDeleted": "séries d'exercices ont été supprimées", "deleteConfirmation": "Es-tu sûr de vouloir supprimer toutes les séries d'exercices liées à ces types ?", "buttonText": "Supprimer les séries"}, "Id": "Identifiant", "ExtraTime": "Tier-temps", "IsUserExtraTime": "L'utilisateur est-il tiers temps ?", "InUserEditExtraTimeExplanation": "Cochez cette case si l'utilisateur bénéficie d'un tier-temps supplémentaire", "GetGroupIdAndCompanyIdHelper": {"ComponentName": "Aide pour trouver les identifiants des groupes / entreprises", "GroupsToAttribute": "Quels groupes souhaitez vous attribuer ?", "CompaniesToAttribute": "Quelle entreprise souhaitez vous attribuer ?", "GroupsMultiSelectPlaceholder": "Sélectionner les groupes pour lesquels vous souhaitez les identifiants", "CompaniesMultiSelectPlaceholder": "Sélectionnez l'entreprise dont vous souhaitez obtenir l'identifiant", "IdsOfYourGroups": "Les identifiants de votre sélection :", "IdOfYourCompany": "L'identifiant de votre sélection", "SelectionIsEmpty": "∅", "CopyToClipBoard": "Copier dans le presse-papier", "SuccessfullyCopiedIntoTheClipBoard": "Copié !", "FailCopyClipboard": "Echec de la copie"}, "FilterExercices": {"FilterExercicesTitle": "Filtrer les exercices", "SeeMore": "Voir plus", "withoutLinkedCours": "Exercices sans cours lié", "LinkedToDeletedCourse": "Exercices liés à des cours supprimés", "Published": "<PERSON><PERSON><PERSON>", "AllTypes": "Tous les types", "PerTitle": "<PERSON>r <PERSON>re", "PerId": "Par Identifiant", "PerCreationDate": "Par date de création", "PerModificationDate": "Par date de modification", "ResetFilter": "Réinitialiser les filtres", "Types": "Types", "CourseGeneralSelection": "Link des cours", "SelectMyCourses": "Filtrer les cours liés", "FilterOnLinkedCourses": "Cours:", "WithoutLinkedCourses": "Aucun cours lié", "LinkedToDeletedCourses": "Lié à des cours supprimés", "LinkedToCourses": "Lié à des cours", "Format": "Format d'exercice", "PerCreator": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "PerParticipant": "Par participant", "PerLinkedSeries": "Par séries liées", "TagNoLinkedCourses": "Aucun cours lié", "TagDeletedLinkedCourses": "Cours supprimé", "Unpublished": "Non publié", "TagDateCreation": "Creation : ", "TagDateModif": "Modification : ", "NumberSeriesLinkedStart": "Exercices présents dans ", "NumberSeriesLinkedEnd": "series", "TagAllTypes": "Tout types", "TagAllFormats": "Tout formats"}, "ImportQcm": {"ModalTitle": "Importer une série", "FileSelection": "Sélection du fichier", "FileSelectionExplanation": "Pour importer une série depuis une autre plateforme, exporter la série et importer le fichier .exoteach", "FileToImportExoteach": "fichier à importer (*.exoteach)", "FileNotGoodExtension": "Le fichier n'est pas du bon format (format attendu : *.exoteach)", "CheckFile": "Vérifier le fichier", "ConfigureQcm": "Configurer la série", "ConfigureQcmExplanation": "Pour chaque <PERSON>, sélectionner sa correspondance sur votre plate forme", "AnalysedUe": "Mat<PERSON> d<PERSON>", "SelectUePlaceholder": "Matière correspondante sur votre plateforme", "TitleAndDescription": "Titre et description", "QcmTypeTitle": "Type de la série", "QcmTypeTitlePlaceholder": "Choisissez les types de la série", "QcmDefaultExerciseType": "Type par défault des questions de la série", "QcmDefaultExerciseTypePlaceholder": "Choisissez les types par default des exercices de la série", "MappingUeNotGood": "Veuillez compléter le choix de la matière sur votre UE", "Next": "Suivant", "ConfigureExercises": "Configuration des exercices", "ScaleTitle": "Barèmes", "ChooseScalePlaceholder": "Sélectionner un barème", "ExerciseType": "Type d'exercice", "ChooseMappedExercisesTypes": "Choisir les types d'exercices associés", "MappingScaleNotGood": "Veuillez compléter le choix des barèmes", "ImportCourses": "Import des cours", "configureCourses": "Configuration des cours", "AnalysedCourses": "Cours détectés", "SelectCoursesPlaceholder": "Cours correspondant sur votre plateforme", "ImportTitleFormationElement": "Import de l'élément de formation 'Titre'", "MappingCoursNotGood": "Veuillez compléeter les cours associés", "MappingTitleNotGood": "Veuillez compléter les élément de formation titre associés", "ConfirmModifications": "Confirmation des modifications", "NewTitle": "Nouveau titre", "NewDescription": "Nouvelle description", "ExercisesNumber": "Nombre d'exercices", "ImportButton": "Importer !", "OpenModalImportQcm": "Importer une série", "RecapQcm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ImportQcmModalTitle": "Titre", "ImportQcmModalDescription": "Description"}, "ExportQcmExoteach": "Export en format .exoteach", "TemplateModule": {"GeneralAdminPageTitle": "Templates et modèles", "ImportWatermarkTemplatePlaceholder": "Importer depuis un template enregistré", "ImportWatermarkTemplate": "Importer !", "WatermarkInputName": "Nom du template ", "WatermarkInputDescription": "Description du template ", "CreateNewWatermarkTemplate": "Créer un nouveau template de filigrane", "ColumnWatermarkName": "Nom", "ColumnWatermarkDescription": "Description", "ColumnWatermarkActions": "Actions", "SubmitCreateWatermarkTemplate": "<PERSON><PERSON><PERSON> le template !", "SubmitUpdateWatermarkTemplate": "Mettre à jour le template !", "TemplateWatermarkOperationLoading": "Opération en cours...", "TemplateWatermarkValidationFieldsError": "Validation des champs a échouée. Contacter votre administrateur", "TemplateWatermarkOperationValidation": "Opération réussie !", "TemplateWatermarkOperationFail": "Échec de l'opération.", "TemplateWatermarkActionButtonModif": "Modifier le template", "TemplateWatermarkActionButtonDelete": "Supprimer le template", "TemplateWatermarkDeleteConfirm": "Êtes vous certain de vouloir supprimer ?", "WatermarkModalTitleCreateTemplate": "Créer un template de filigrane", "WatermarkModalTitleUpdateTemplate": "Modifier un template de filigrane", "WatermarkTemplateImportNotif": "Paramètre de filigrane importé !"}, "StatsGraphTraductions": {"Day": "Jour", "Week": "<PERSON><PERSON><PERSON>", "Month": "<PERSON><PERSON>", "Year": "<PERSON><PERSON>", "uniqueSeenClasses": "Cours uniques vu", "exercisesDone": "Exercices réalisés", "activeUsers": "Utilisateur Actifs", "FilterPerCourses": "Filtrer par cours", "AllSeenCourses": "Cours vu", "PostsSent": "Messages postés", "AllDownloadedFiles": "Fichiers téléchargés", "DownloadedFileMedian": "Fichiers téléchargés (médiane)", "allSeenClasses": "Cours vu", "postsSent": "Posts envoyés", "downloadedFiles": "Fichiers téléchargés", "ProgressionMedian": "Progression sur période (moyenne)", "LaunchFetch": "Lancer la recherche !", "ResetFilter": "Réinitialiser le filtre", "UniqueClassesForBatch": "Cours unique disponible", "ProgressGeneral": "Progression générale (moyenne)", "PeriodeBatchProgress": "Progression sur période (moyenne)", "ExcelGeneralProgress": "Progression générale", "ExcelPeriodeProgress": "Progression sur la période", "ExcelUniqueSeenClasses": "Cours uniques vu", "ExcelPostsSent": "Messages postés", "TotalSeeableClasses": "Cours disponibles", "NumberOfSelectedCourses": "( {{n}} cours sélectionnés ) ", "ExcelGroups": "Groupes", "ExcelCompanies": "Entreprises"}, "NumberOfSelectedCoursesMassOperation": "Nombre de cours sélectionnés : ", "CheckBoxDoenstWantCoursesLinkedToExercise": "Aucun cours associé", "ConfirmDoesntWantCoursesLinkedToExerciseTitle": "Êtes-vous sur ?", "ConfirmDoesntWantCoursesLinkedToExerciseExplanation": "Ne pas associer de cours aux exercices désactive de nombreuses fonctionnalités de la plateforme.", "CheckBoxDoesntWantCoursesLinkedToMyExercise": "Pas de cours liés", "NumberOfCoursesLinkedToTheExercise": "Nombre de cours sélectionnés :", "UpdateExerciseItNeedsCoursesOrCheckbox": "<PERSON><PERSON><PERSON> de sélect<PERSON>ner le(s) cours lié(s) avant d'enregistrer", "PleaseFillAllAnswers": "Merci de <PERSON>lectionner VRAI/FAUX pour tous les items avant d'enregistrer", "LimitationQuestionLayer": {"ExplanationUserWithLimite": "Vous avez atteint la limite de questions pour le moment.", "ExplanationUserDeactivated": "Vous ne pouvez pas poser de questions pour le moment.", "NextQuestionSentenceWithTimer": "Vous pourrez à nouveau poser des questions dans {{n_days}} jours,  {{n_hours}} heures, {{n_minutes}} minutes , {{n_seconds}} secondes. ", "LeftTimeBeforeNewQuestion": "Temps restant avant de pouvoir poser une question : ", "Days": "jour(s)", "Hours": "heure(s)", "Minutes": "minute(s)", "Seconds": "seconde(s)", "Preferences": "Préférences", "PreferencesOf": "Préférences de", "LimitDiscussionTitle": "Limiter les discussions", "LimitDiscussionExplanation": "Cette option permet de limiter le nombre de discussion qu'un apprenant peut initier sur une période glissante", "Activate": "Activer ", "LimitNumberNewDiscussion": "Nombre max de discussions", "NewDiscussionExplanation": "Contrôle le nombre de nouvelle discussions que l'apprenant peut initier sur la période de temps définie. Si fixé à 0, alors l'user ne peut pas créer de discussions", "TimeWindow": "Période glissante", "Update": "Mettre à jour !"}, "LinkCoursesToExerciseSelecterPlaceholder": "Écrivez le nom du cours à rechercher", "TutorFromUeHasBeenUpdateConfirmation": "Les matières du responsable ont étées mises à jour", "TutorFromUeHasUpdateFail": "Echec de la mise à jour des matières du responsable", "CheckAll": "Tout cocher", "UncheckAll": "<PERSON><PERSON>", "WriteUeToLinkToTutor": "Écrivez le nom de la matière à associer", "QcmConfigTraductionPackage": {"ModalTitle": "Personnaliser un assistant IA", "ModalNameGeneralConfigLabel": "Nom de la configuration", "ModalNameGeneralConfigPlaceholder": "Renseigner le nom de votre configuration d'exercice", "ModalSelecterIntegrationGpt": "Clé d'API", "ModalSelecterIntegrationGptPlaceholder": "Sélectionner une intégration depuis la liste", "ModalCollapseHeader": "Customisez vos modèles", "AddAModelButtonLabel": "Ajouter un modèle dans la configuration :", "AddModelButtonAvailableModels": "<PERSON><PERSON><PERSON><PERSON> disponi<PERSON>", "AddAModelButton": "Ajouter !", "SubModelModelName": "Nom :", "SubModelModelModel": "Mod<PERSON><PERSON> :", "SubModelModelModelPlaceholder": "Selectionner un model parmis la liste", "SubModelDeletModelButton": "Supprimer ce modèle", "ConfigPermissionManagementTitle": "Gestion des permissions", "ConfigPermissionManagementGroupSubtitle": "Groupes ayant accès", "ConfigPermissionManagementUsersSubtitle": "Utilisateurs ayant accès", "CreateNewConfig": "Nouvel assistant", "TableColumnName": "Nom", "TableColumnIntegrationName": "Clé d'API", "TableColumnModels": "<PERSON><PERSON><PERSON><PERSON>(s)", "TableColumnAction": "Actions", "TableColumnButtonActionLabel": "Modifier", "TableColumnButtonDeleteLabel": "<PERSON><PERSON><PERSON><PERSON>", "NeedIntegrationId": "Erreur : Il y a besoin de définir votre intégration GPT", "UndefinedError": "<PERSON><PERSON><PERSON>"}, "AiQuestionAnswerTabTitle": "IA 🤖 : Réponses aux questions", "AiQcmAssistantTabTitle": "IA 🤖 : Assistant Exercices", "AiQcmCorrectionTabTitle": "IA 🤖 : Correction Exercices", "AiEnhancementModal": {"ChooseModel": "<PERSON>sir un modèle", "ChooseAI": "Choisir une IA", "ApplyChangeProposal": "Appliquer", "ChangeApplied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChangeNotApplied": "Non appliqué", "MainMenuName": "Améliorer avec l'IA", "UpdateAllChildrenButtonLabel": "Tout appliquer", "UpdateAllChildrenTooltip": "Cette action validera toutes les propositions faites par l'IA !", "TradComponantTradTextUpfront": "Langue : ", "TradComponantEnglishLabel": "<PERSON><PERSON><PERSON>", "TradComponantFrenchLabel": "Français", "TradComponantSpanishLabel": "Espagnol", "TradComponantGermanLabel": "Allemand", "TradComponantItalianLabel": "Italien", "TradComponantCustomLabel": "<PERSON><PERSON> ma langue", "TradComponantCustomPlaceholder": "<PERSON><PERSON><PERSON>re la langue dans laquelle le text doit être traduit", "TradComponantExecuteTraduction": "<PERSON><PERSON><PERSON><PERSON>", "CustomPromptComponantExecuteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "CustomPromptComponantPlaceholderTextArea": "Écrivez vos instructions ici", "FixSpellingAndGrammar": "Corriger l'orthographe et la grammaire", "ReformulateText": "Reformuler", "TradInto": "<PERSON><PERSON><PERSON><PERSON>", "CustomGptQuery": "<PERSON><PERSON><PERSON>", "EnhanceCorrection": "Améliorer la correction", "InputTextAreaOriginalText": "Entrer le texte à modifier", "ValidateChange": "Valider", "InputTextAreaOutputText": "Le text modifié", "NoChangesToMakeTriStateButtonValidation": "Inchangé", "ErrorMessageDefineConfigAndModel": "Veuillez définir une configuration ainsi qu'un modèle pour commancer l'amélioration", "InferTitleQcmTitle": "<PERSON><PERSON>re de QCM", "InferTitleFormationElementTitle": "Titre de Formation Element", "InferTitleUnknownComponantType": "", "InferTitleJustification": "Correction ", "InferTitleStating": "Énon<PERSON> ", "InferTitleUnknownSentenceEnding": "", "InitialFetchButtonLabel": "Lancer l'amélioration !", "ChildrenRefetchButtonLabel": "Relancer tout"}, "Formation": {"NonLinearAccess": "Accès non linéaire", "AccessibilityPeriod": "Période d'accessibilité", "AddModuleTitle": "Ajouter un module", "CoursModule": "<PERSON><PERSON>le de cours", "MinimumTime": "Temps minimum", "EstimatedTime": "Temps estimé", "Locked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CompletePreviousModule": "Complétez le module précédent", "ModuleAccessBlocked": "Ce module est verrouillé, complétez le module précédent pour le déverrouiller", "MyTimeSpent": "Mon temps passé", "StepName": "Nom de l'étape", "FinishForm": "Terminer le formulaire", "BackgroundColor": "<PERSON><PERSON><PERSON> de fond", "BackgroundImage": "Image du bandeau", "StartModule": "<PERSON><PERSON><PERSON><PERSON> le module", "Element": "É<PERSON>ment", "steps_one": "étape", "steps_other": "étapes", "ModuleSettings": "Paramètres du module", "NoValidationCriteriaClickingNextWillValidate": "Pas de critère de validation, cliquer sur suivant validera l'étape", "ValidatedSteps": "Étapes validées", "SeeOrEditCourse": "Voir ou éditer le cours", "ValidationCriteriaDefinedByModule": "Le critère de validation est temporel, il est défini par le module parent", "ObtainAtLeast": "Obtenir au moins", "EditCoursSteps": "É<PERSON>er le module de cours", "SpecificGrade": "Une note spécifique : ", "AddStep": "Ajouter une étape", "ModuleCompleted": "<PERSON><PERSON><PERSON> termin<PERSON>", "FinishSerie": "Terminer la série", "FinishExercise": "Te<PERSON>iner l'exercice", "InfoValidationStepsCours": "Se référer au critère de validation des étapes", "CoursSteps": "Étapes du cours", "OR": "OU", "ValidationCriterias": "Critères de validation", "Certificate": "Certificat"}, "CourseTypes": {"Steps": "Étapes", "PDF": "Support principal PDF", "Video": "Support principal <PERSON><PERSON><PERSON><PERSON>", "EnrichedCourse": "Cours enrichi"}, "userProfile": {"Recap": "Synthèse", "Results": "Résultats", "Activity": "Activité", "resultRadar": "Radar", "resultHisto": "Histogramme", "progression": "Progression", "posts": "Sujets", "comments": "Commentaires", "grades": "Notes", "exams": "Examens", "training": "Entraînements"}, "AiQuestionCreationModal": {"DefineParametersStepTitle": "Initialisation", "LastStepTitle": "Importation", "DynamicEditQuestionStep": "Exercice", "EditQuestionButtonAcceptLabel": "Valider", "EditQuestionButtonRejectLabel": "<PERSON><PERSON><PERSON>", "ModalBigTitle": "Créer des exercices avec IA", "DynamicStepBlankStepTitle": "Chargement", "LabelQcm": "QCM", "LabelQcu": "QCU", "QuestionFormatExerciseTypeSelecterLabel": "Définir les types d'exercices :", "QuestionTypeSelecterLabel": "Choisir le format d'exercice :", "QuestionBaremSelecterLabel": "<PERSON><PERSON> le barème :", "LaunchQuestionCreationButtonLabel": "Lancer la création !", "LaunchQuestionImportationButtonLabel": "Lancer l'importation !", "QuestionParameterSelectionTitle": "Options d'exercices :", "NumberOfQuestionToGenerateTitle": "Nombre de question à générer", "CustomPromptDescriptionTitle": "Instructions personnalisées :", "RadioButtonLabelSelectPage": "Page(s)", "RadioButtonLabelSelectInterval": "Intervalle", "RadioButtonLabelAllPages": "<PERSON>ut", "RadioButtonLabelDescripter": "Pages à utiliser pour la génération :", "PlaceholderRadioButtonLabelSelectInterval": "Choisir la ou les pages", "PlaceholderRadioButtonLabelSelectIntervalStart": "<PERSON>", "PlaceholderRadioButtonLabelSelectIntervalEnd": "<PERSON>", "LabelRadioButtonLabelSelectIntervalStart": "Page de début : ", "LabelRadioButtonLabelSelectIntervalEnd": "Page de fin : ", "SelectPdfTitleLabel": "Sélectionner le pdf à utiliser", "PlaceholderSelectPdf": "un cours doit être sélectionné ou un pdf doit être importé", "PlaceholderBaremSelecter": "Un barème doit être sélectionné", "UploaderTitle": "Uploader un pdf", "GetPdfFromCoursesSupport": "Cours lié", "CourseSelecterPlaceholder": "Sélectionner un cours", "TitleChoosePdfForQuestionGeneration": "Erreur : Il faut choisir un pdf qui sera utilisé pour la génération", "PlaceholderNumberOfQuestionToGenerate": "Nombre de questions à générer (1 à {{count}})", "ErrorMessageSelectPdf": "Erreur : <PERSON><PERSON><PERSON>z choisir un pdf à utiliser", "ErrorSelectNumberOfQuestions": "Erreur : <PERSON><PERSON><PERSON>z choisir un nombre valide de question à générer", "ErrorSelectPagesToImport": "Erreur : <PERSON><PERSON><PERSON>z choisir des pages à utiliser", "ErrorSelectBaremForGeneration": "Erreur : il faut choisir un barème", "ErrorLinkageZeroQuestions": "Erreur : Pas de questions à ajouter dans la série", "ErrorNeedLinkWithCourses": "Erreur : Il faut choisir des cours qui seront liés aux exercices créés", "ErrorNeedTypeSelectionForExercise": "Erreur : il faut choisir au moins un type d'exercice", "AddQuestionsToQcm": "Ajouter les questions dans la série !", "MessageSuccesLinkage": "Ajout des questions dans la série réussi !", "LabelButtonFromSerieOpenModal": "Créer Exercices par IA", "NeedPdfErrorName": "L'extension du fichier n'est pas bonne. Un fichier PDF est nécessaire", "DynamicEditQuestionStepWithNumber": "Exercice N°{{number}}", "WarningConfirmationExitModalTitle": "<PERSON><PERSON><PERSON> sans sauvegarder ?", "WarningConfirmationExitModalExplanation": "Êtes vous sûrs de vouloir quitter ? Les modifications non sauvegardées seront annulées", "WarningConfirmationExitModalLeaveButton": "Partir !", "WarningConfirmationExitModalStayButton": "Rester !", "ChooseLinkedCoursesTitle": "Choisir les cours qui seront liés :", "ChoosePdfTitle": "Choisir le fichier pdf source :", "RadioButtonSelectionFromCours": "Depuis les cours sélectionnés", "RadioButtonSelectionFromImport": "De<PERSON><PERSON> mon fichier", "ChooseExempleTitle": "S'inspirer des exercices suivants :", "CollapsePanelTitle": "Cliquer pour ouvrir", "CollapsePanelButtonLabelSeeExercisesWhen1orMore": "Voir la sélection ( {{count}} sélectionné(s) )", "CollapsePanelButtonLabelSeeExercisesWhen0": "Pas de sélection", "CollapsePanelButtonDeleteSelection": "Supprimer la sélection", "AddExemplesButtonPopoverLabel": "Re<PERSON>rer comme exemple", "ErrorFetchScales": "Erreur lors de la récupération de barèmes : ", "NextAndAcceptQuestion": "Valider l'exercice", "NextAndRejectQuestion": "Refuser l'exercice", "AddThisExerciseAsExample": "Ajouter cet exemple", "RemoveThisExerciseAsExample": "Re<PERSON>rer cet exemple", "GeneratingMarker": "En cours de traitement", "ErrorMessageNeedConfigForGpt": "Il faut renseigner une configuration GPT", "ErrorMessageNeedModelForGpt": "Il faut renseigner un model GPT", "LabelFillInTheBlanks": "Texte à trou", "AcceptSmallErrorCheckboxLabel": "Accepter de petites erreurs : ", "isCaseSensibleCheckboxLabel": "Sensible à la casse : ", "ignoreSpecialCharCheckboxLabel": "Ignorer les accents et caractères spéciaux", "ErrorSubscriptionIsStillLoading": "Error : L'importation est encore en cours de réalisation", "ErrorNumberOfQuestionsToImportIsZero": "Error : Il n'y a aucun exercices à importer", "ErreurInIdForFullImportation": "Error : certains ID de question ne sont pas valides", "ErrorUpdateQuestionInFullImportation": "Error : echec d'importation de la question d'id {{qId}}", "ErrorValidationQuestionInFullImportation": "Error : Echec de la validation de la question d'Id {{qId}}", "ErrorLinkageQuestionInFullImportation": "Error : Echec du linkage de la question {{qId}} au qcm {{qcmId}}", "AcceptAllButtonLabel": "Tout accepter", "PdfIsCryptedWarning": "Le pdf est protégé par un mot de passe.", "ErrorCannotReadPdf": "Le pdf n'est pas valide", "ErrorInputTextIsEmpty": "Le texte est vide", "ErrorMessagePdfIsCrypted": "Le pdf est protégé par un mot de passe. La création d'exercice ne peut avoir lieu.", "ChooseMyAiIntegration": "Modèle d'IA pour la génération", "ChooseMyAiIntegrationPlaceholder": "Sélectionner un modèle", "StopGptQueryButtonLabel": "Arrêter la génération", "LabelFlashcard": "Flashcard"}, "MathpixIntegration": {"ComponantTitle": "IA 🤖 : Import PDF", "IntegrationTitleColumnName": "Nom", "EditIntegrationModalTitle": "Modifier les propriétées de : ", "ConfigPermissionManagementGroupSubtitle": "Groupes ayant accès", "ConfigPermissionManagementUsersSubtitle": "Utilisateurs ayant accès", "DivierBeforeMathpixies": "Documents enregistrés", "MathpixMathpixieFilenameColumn": "<PERSON><PERSON><PERSON>", "DateCreationColumnName": "Date de création", "CreatorColumnName": "<PERSON><PERSON><PERSON><PERSON>", "MathpixActionColumnName": "Actions", "DeleteMathpixButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "ErrorMessageDeletion": "La suppression a échoué", "ErrorMessageUpload": "L'upload du fichier a échoué", "ErrorMessageNeedFile": "Il faut choisir un fichier à uploader", "CourseSelecterTitle": "Sélectionner tous les cours abordés dans le fichier", "CourseSelecterExplanation": "chatGPT va automatiquement lier les cours aux exercices", "ChooseMathpixIntegrationTitle": "Choisir un profil d'assistant I<PERSON>", "ChooseMathpixIntegrationPlaceholder": "Choisir un profil d'assistant I<PERSON>", "UploadFileButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> le fi<PERSON>", "ImportFromPdfWithAiMasterModalButton": "Importer par IA", "ModalBigTitle": "Importer exercices PDF IA", "AmeliorationCheckboxFormLabel": "Compléter <PERSON> ", "ButtonLabelNextButLoadingSoDisabled": "Traitement", "ButtonLabelFinishAndRejectExercise": "Rejeter et terminer !", "ButtonLabelFinishAndAcceptExercise": "Accepter et terminer !", "ErrorMessageNoOcrConfigId": "Pas d'ID de config d'OCR", "NoMathpixIntegrationForConfigurationTagWarning": "Vous avez besoin de créer une intégration dans l'onglet Administration -> Configuration -> Intégrations -> Mathpix", "PopoverLimitQuestion": "Actuellement limité à 40 exercices maximum", "RemoveThisExerciseAsExample": "Re<PERSON>rer cet exemple", "LabelDownloadPdfForImportedExerciceButton": "Télécharger le pdf original", "CreationMethodParagraphLabelInExerciceOptions": "Mode de création", "CreationHumanLabel": "<PERSON><PERSON>é par un humain", "CreationGPTLabel": "Créé par IA", "ImportGptLabel": "Importé par IA", "ChooseWhatToImportSelecterLabel": "Source(s) à utiliser pour créer les exercices", "ChooseWhatToImportCoursLabel": "Cours", "ChooseWhatToImportPdfLabel": "PDF", "ChooseWhatToImportRawTextLabel": "Texte", "ChooseWhatToImportImageLabel": "Images", "ChoosePictureUploaderLabel": "Choisir les images", "ErrorDataToSendDoesNotExist": "Il n'y a pas de données à envoyer", "ErrorNoPdfAttached": "Erreur : aucun pdf choisi", "ErrorIfMultiplesDataWeNeedAllPicturesType": "Si il y a plusieurs données à envoyer, alors le type de toutes doit être 'images'", "ErrorExpectedTypeToSendDoesNotMatchDataToSendType": "Le type que l'on s'attends à envoyer est incohérent avec le type que l'on envoie vraiment", "ImportGptPdfLabel": "Importé par IA (pdf)", "ImportGptRawTextLabel": "Importé par IA (texte)", "ImportGptPicturesLabel": "Importé par IA (image)", "ImportGptCourseLabel": "Importé par IA (cours)", "RawInputImportPdfPlaceholder": "Écrire l'exercice à importer ici", "NumberOfQuestionFacultatif": "(facultatif) Nombre d'exercices : ", "ResetNumberOfExpectedQuestionButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "NumberOfExpectedQuestionPlaceholder": "Nombre d'exercices", "ErrorMessageOpenAiCreationWhenNoLmmConfig": "Pour pouvoir utiliser la feature il faut donner les accès dans Administration > Intelligence artificielle > IA : Assistant Exercices", "ErrorMessageOpenAiCreationWhenNoOcrConfig": "Pour pouvoir utiliser la feature il faut donner les accès dans Administration > Intelligence artificielle > Import PDF", "PreviousConfirmationMessage": "Voulez-vous vraiment revenir à la page précédente ?", "ChooseMathpixIntegrationExplanation": "Accéder à la page de configuration : ", "AmeliorationCheckboxExplanation": "<PERSON>, l'IA essaye de créer la correction lorsqu'elle n'existe pas."}, "date": {"DatePlaceholder": "JJ/MM/AAAA", "Norepeat": "Ne pas répéter", "Daily": "Quotidien", "Weekly": "Hebdomadaire", "Monthly": "<PERSON><PERSON><PERSON>", "Yearly": "<PERSON><PERSON>", "Weekday": "<PERSON><PERSON> de se<PERSON> (<PERSON><PERSON> à Vendredi)", "Custom": "Personnaliser", "Monday": "<PERSON><PERSON>", "Tuesday": "<PERSON><PERSON>", "Wednesday": "<PERSON><PERSON><PERSON><PERSON>", "Thursday": "<PERSON><PERSON>", "Friday": "<PERSON><PERSON><PERSON><PERSON>", "Saturday": "<PERSON><PERSON>", "Sunday": "<PERSON><PERSON><PERSON>", "Mo": "<PERSON>", "Tu": "Ma", "We": "Me", "Th": "Je", "Fr": "Ve", "Sa": "Sa", "Su": "Di", "January": "<PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON><PERSON>", "March": "Mars", "April": "Avril", "May": "<PERSON>", "June": "Juin", "July": "<PERSON><PERSON><PERSON>", "August": "Août", "September": "Septembre", "October": "Octobre", "November": "Novembre", "December": "Décembre", "Repeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Day": "Jour", "Days": "jours", "Week": "<PERSON><PERSON><PERSON>", "Weeks": "semaine(s)", "Month": "<PERSON><PERSON>", "Months": "mois", "monthsOn": "mois, le", "Year": "<PERSON><PERSON>", "Years": "année(s)", "DayShorten": "J", "WeekShorten": "S", "MonthShorten": "M", "YearShorten": "A", "the": "le", "ofEach": "de chaque", "RepeatEvery": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout(e)s les", "repeatDayExplanation": "L'évènement aura lieu tous les jours ou tous les X jours", "repeatWeekExplanation": "L'évènement aura lieu toutes les X semaines les jours sélectionnés. Si aucun jour n'est sélectionné, il aura lieu toutes les X semaines le même jour que la date de début.", "repeatMonthExplanation": "L'évènement aura lieu tous les X mois a la date sélectionnée", "repeatYearExplanation": "L'évènement aura lieu toutes les X années a une date spécifique", "RepeatEnd": "Fin de répétition", "Never": "Jamais (se répète indéfiniment)", "ExceptionDate": "Date de l'exception", "Until": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "After": "<PERSON><PERSON>", "Exceptions": "Exceptions", "AddException": "Ajouter une exception", "EditException": "Modifier l'exception", "PleaseSelectDate": "<PERSON><PERSON><PERSON>z sélectionner une date", "NoExceptionYet": "Aucune exception pour le moment", "ExceptionsExplanation": "Spécifiez les jours à exclure de la récurrence", "occurences": "ré<PERSON><PERSON><PERSON><PERSON>(s)"}, "UploadBadExtensionErrorMessage": "Format '{{extension}}' non valide. Les types acceptés sont les suivants '{{validesExtension}}' ", "ImageToLatex": {"UploadExensionErrorText": "Format '{{extension}}' non valide. Les types acceptés sont les suivants '{{validesExtension}}' ", "UploadDraggerLabel": "Ajouter image équation", "MathpixMultipleIntegrationError": "Error : L'intégration OCR doit être limitée à 1. Contactez votre administrateur", "EditQuestionButtonLabelToOpenComponant": "Importer équation", "ErrorOcrConfig": "Erreur : Pas de configuration, contactez votre administrateur", "InsertInQuillEditor": "<PERSON><PERSON><PERSON><PERSON>", "EditInQuillEditor": "É<PERSON>er"}, "userMassActions": {"exportCsv": "Exporter les informations CSV", "addGroup": "Ajouter un groupe", "removeGroup": "Enlever des groupes", "delete": "<PERSON><PERSON><PERSON><PERSON>", "editPreference": "Modifier les préférences", "exportExcelAll": "Exporter tout en Excel"}, "DynamicFormationElement": {"MainButtonLabel": "Lien dynamique", "NameLabelButtonInsert": "Nom", "UsernameLabelButtonInsert": "Prénom", "IdLabelButtonInsert": "Identifiant", "RoleLabelButtonInsert": "R<PERSON><PERSON>", "BannedLabelButtonInsert": "<PERSON><PERSON> ?", "LangLabelButtonInsert": "<PERSON><PERSON>", "EmailLabelButtonInsert": "Email", "DynamicSectionLabel": "Insertion d'éléments dynamique"}, "ReorderElements": {"Name": "Remettre dans l'ordre", "Description1": "<PERSON><PERSON><PERSON>-déposer pour remettre les éléments dans l'ordre", "Description2": "Notation basée sur les positions correctes", "ConfigurationTitle": "Configuration de l'exercice \"Remettre dans l'ordre\"", "ConfigurationSubtitle": "Définissez l'ordre correct des éléments", "AddElement": "Ajouter un élément", "ElementPlaceholder": "Contenu de l'élément {{index}}...", "DeleteConfirmTitle": "Supprimer cet élément ?", "DeleteConfirmDescription": "Cette action est irréversible.", "DeleteConfirmYes": "O<PERSON>", "DeleteConfirmNo": "Non", "InstructionsTitle": "Instructions :", "InstructionsText": "Définissez les éléments dans l'ordre correct. Les étudiants devront les remettre dans cet ordre. Glissez-déposez pour réorganiser.", "MinElementsWarning": "Il faut au moins 2 éléments pour créer un exercice de remise en ordre", "ConfigurationValid": "Configuration valide ! {{count}} éléments définis dans l'ordre correct.", "ElementImage": "Image élément {{index}}", "ImageUploadSuccess": "Image téléchargée avec succès", "ImageDeleteSuccess": "Image supprimée avec succès", "ImageUpdateError": "Erreur lors de la mise à jour de l'image", "DeleteElementTooltip": "Supprimer cet élément entier", "DeleteImageTooltip": "Supprimer uniquement l'image", "DragStart": "Déplacement de l'élément {{id}}", "DragOver": "L'élément {{activeId}} est au-dessus de {{overId}}", "DragOverNone": "L'élément {{activeId}} n'est plus au-dessus d'un élément", "DragEnd": "L'élément {{activeId}} a été déplacé vers la position de {{overId}}", "DragEndNone": "L'élément {{activeId}} a été relâché", "ExerciseTitle": "Remettre dans l'ordre", "CorrectionMode": "(Correction)", "SelectedOrder": "Ordre sélectionné :", "DragInstructions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z les éléments pour les remettre dans le bon ordre.", "PositionCorrect": "Position correcte", "PositionCorrectNumber": "Position correcte: {{position}}", "ContentNotAvailable": "Contenu non disponible", "IncompleteConfiguration": "Configuration de l'exercice incomplète", "NoDataAvailable": "Aucune donnée de correction disponible", "CorrectPositions": "Positions correctes: ", "PositionNumber": "Position {{position}}", "CorrectPosition": "Position correcte", "IncorrectPosition": "Position incorrecte", "YourAnswer": "Votre réponse :", "ExpectedAnswer": "Réponse attendue :"}, "OPExplanation": "Auteur d'origine", "AIGenerateAnswerModal": {"ChooseAIProfile": "Choisir un profil IA", "PersonalizedQuery": "<PERSON><PERSON><PERSON>", "Generate": "<PERSON><PERSON><PERSON><PERSON>"}, "AIGeneratedAnswerExplanation": "Cette réponse vous a été fournie par l'IA, elle peut comprendre des imprécisions ou ne pas répondre correctement à votre question.", "Flashcard": {"Recto": "Recto", "Verso": "Verso", "EditQuestionRectoBoxTitle": "Recto", "EditQuestionVersoBoxTitle": "Verso", "SelectFlashcardBaremTypeLabel": "Auto-évaluation", "SelectFlashcardBaremTypeExplanation": "Choix entre la notation Vrai/Faux ou une notation gradée (Très mauvais à Très bien) pour évaluer le degré de réussite", "SelectFlashcardBaremTypeExplanationTrueFalse": "Notation Vrai/Faux", "SelectFlashcardBaremTypeExplanationGradation": "Notation gradée", "FlashcardBaremRadioSelecterBinary": "Vrai/Faux", "FlashcardBaremRadioSelecterContinuous": "<PERSON><PERSON><PERSON>", "Flashcard": "Flashcard", "ConfidenceMeterUncertain": "Incertain", "ConfidenceMeterConfident": "Confiant", "ConfidenceMeterCertain": "Certain", "AutoEvaluationNoteFailure": "Echec", "AutoEvaluationNotePartial": "Partiel", "AutoEvaluationNoteAverage": "<PERSON><PERSON><PERSON>", "AutoEvaluationNoteSuccess": "Su<PERSON>ès", "AutoEvaluationNoteExcellence": "Excellence", "ConfidenceMeterLabel": "Confiance", "ConfidenceMeterPopoverExplanation": "Répondez honnêtement ! Cela aidera notre intelligence artificielle à vous aider", "AutoEvaluationLabel": "Auto-évaluation ", "AutoEvaluationPopoverExplanation": "É<PERSON><PERSON>z vous", "Response": "Réponse", "Question": "Question", "BaremLabelVeryGood": "<PERSON><PERSON><PERSON> bonne", "BaremLabelGood": "<PERSON><PERSON>", "BaremLabelAverage": "<PERSON><PERSON><PERSON>", "BaremLabelPoor": "<PERSON><PERSON><PERSON><PERSON>", "BaremLabelVeryPoor": "<PERSON><PERSON><PERSON>", "BaremLabelTrue": "O<PERSON>", "BaremLabelFalse": "Non", "BaremLabelDefault": "Aucunes réponse", "BaremTrueFalseEditionSectionTitle": "Type Vrai/Faux", "BaremLadderEditionSectionTitle": "Type Échelle", "BinaryAutoEvaluationOrnamentSentence": "Aviez vous trouvé la bonne réponse ?", "ContinuousAutoEvaluationOrnamentSentence": "Comment noteriez-vous votre réponse ?", "ConfidenceMeterExplanationSentence": "Niveau de confiance dans votre réponse :"}, "FlashcardPageTitle": "Flashcard", "notifications": {"mentionedYou": "vous a mentionné", "Preview": "<PERSON><PERSON><PERSON><PERSON>", "Pin": "<PERSON><PERSON><PERSON>", "Unpin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pinned": "<PERSON><PERSON><PERSON>", "Nonpinned": "Non-épinglé", "MarkAsOpened": "Marquer comme lu", "GoToNotification": "Voir plus", "Read": "<PERSON>", "Unread": "Non-lu", "FilterBy": "Trier par", "OriginUser": "Utilisateur à l'origine", "MarkAsUnOpened": "Marquer comme non-lu"}, "SomeExercicesAreUnanswered": "Attention ! Certains exercices ne sont pas remplis.", "Scale": {"ModalTitleDefaultScale": "Barème par défaut", "ModalTitleDefaultScalePopoverExplanation": "Ce barème sera utilisé par default pour les exercices au format {{type}} si vous n'avez rien spécifié d'autre.", "ModalOtherScales": "Autres barèmes", "ModalCreateNewScale": "<PERSON><PERSON>er un nouveau barème", "ScaleLabel": "Barème(s)", "DefaultScaleCardLabel": "Barème par défaut", "EditScaleConfigButtonLabel": "Éditer la configuration", "NoScaleDefinedWarning": "Pas de barème par défaut configuré", "EditButtonLabel": "Modifier", "DefineDefaultButtonLabel": "Définir comme barème par défaut", "ShowLogModalButtonLabel": "Affiche<PERSON> les logs", "ShowLabelIsDefault": "Barème par défaut", "ShowMoreUeButtonLabel": "Voir plus", "ShowLessUeButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "NotificationCantDeleteScaleTitle": "Vous ne pouvez pas supprimer ce barème", "NotificationCantDeleteScaleExplanation": "Il y a des exercice(s) lié(s) à ce barème. La suppression est interdite. Pour retirer les exercices de ce barème, vous pouvez utiliser les opérations en masse : ", "WarningToUpdateScaleWhenSwitchingQuestionQCUandQCM": "Après modification du type d'exercice vous devez changer le barème !", "ErrorNeedToChangeScale": "Modifier le barême", "Help": "Aide", "ModalConfirmationChangeDefaultScaleTitle": "Changer le barème par défaut ?", "ModalConfirmationChangeDefaultScaleExplanation": "Tous les nouveaux exercices de type {{type}} auront ce barème par défaut.", "ModalConfirmationDeleteScaleTitle": "Supprimer le barème ?", "ModalConfirmationDeleteScaleExplanation": "Confirmez-vous vouloir supprimer le barème ?", "MassUpdateScaleNewTitle": "Modifier le barème", "MassUpdateScaleExplanation": "Cocher les type d'exercice à modifier ainsi que les barèmes à attribuer", "MassUpdateNumberOfElementsToUpdateLabel": "Barèmes qui seront modifiés", "ScaleConfirmMassOperationTitle": "Confirmer la modification en masse ?"}, "ScaleKey": {"mcq": "QCM", "ucq": "QCU", "alphanumerical": "Alphanumérique", "freetext": "Texte libre", "schema": "Schéma P&C", "schemaFillInLegends": "Schéma à compléter", "fillintheblanks": "Texte à trous", "FLASHCARD": "Flashcard", "reorderelements": "Ordre"}, "editAI": {"EditAI": "Modifier mon IA", "TemplateSelector": "Choisir un template", "seeMore": "Plus...", "Choose": "Choi<PERSON>", "SureToApplyTemplateFirstLine": "Choisir un template écrasera la configuration existante.", "SureToApplyTemplateSecondLine": "Êtes-vous sûr de vouloir choisir ce template ?", "UserName": "Pseudo (nom de l'IA)", "RequiredUserName": "<PERSON><PERSON><PERSON><PERSON> entrer le pseudo", "Model": "🧠 Cerveau de l'IA :", "ModelExplanation": "Choisissez ici le modèle qui sera utilisé pour répondre", "RequiredModel": "Veuillez choisir un modèle", "ModelPlaceholder": "<PERSON><PERSON> le modèle", "Personality": "❤️ Coeur de l'IA :", "PersonalityExplanation": "Définissez ici la personnalité de votre IA, sa manière de s'exprimer, de d<PERSON><PERSON>ler...", "PersonalityPlaceholder": "Exemple : <PERSON><PERSON><PERSON> <PERSON> médecine, tu réponds aux élèves de manière sympathique en les tutoyant. Tu cherches toujours à leur faire comprendre le maximum et tu images tes réponses si besoins", "Responsibility": "A qui l'IA doit-elle répondre ?", "ResponsibilityGroups": "Groupes", "ResponsibilityUsers": "Utilisateurs", "accessibleCourses": "Sur quel cours l'IA peut-elle intervenir ?", "modificationsNotSaved": "Modifications non sauvegardées", "sureToDeleteAiTitle": "Supprimer cette IA ?", "sureToDeleteAiDescription": "Cette action est irréversible"}, "NoSubject": "Sans matière", "TimeSpent": "Te<PERSON> passé", "RecalculateGrades": "Recalculer", "SortBy": "Trier par", "RecalculateGradesExplanation": "<PERSON><PERSON> recalculera toutes les notes de la séries si vous avez changé le barème par exemple", "Grade_one": "{{count}} Note", "Grade_other": "{{count}} Notes", "Exaequo": "Ex æquo", "Date": "Date", "resultsOf": "Résultats de", "GradeDisplayOutOf": "Note sur ", "AverageDuration": "<PERSON><PERSON><PERSON> moyenne", "History": "Historique", "FirstTry": "Premiers essais", "FirstTrySingular": "Premier essai", "FirstTryExplanation": "Cela n'affichera que les premières tentative de chaque élève sur la série", "EventFullDay": "Toute la journée", "EventStart": "D<PERSON>but", "EventEnd": "Fin", "EventRecurrence": "<PERSON><PERSON><PERSON><PERSON>", "EventRecurrenceEnd": "Fin de récurrence", "EventOrganizers": "Organisateur(s)", "EventExamSeries": "Ép<PERSON><PERSON>s", "EventNotes": "Notes", "EventCustomTitle": "Titre pour cette occurence", "EventCustomTitleExplanation": "Facultatif, affichera un titre différent dans le planning pour cette date et ses récurrences", "EventSingleDayAllDay": "{{date}} toute la journée", "EventSingleDayTime": "{{date}} de {{timeStart}} à {{timeEnd}}", "EventSeveralDaysAllDay": "Du {{dateStart}} au {{dateEnd}}", "EventSeveralDaysTime": "Du {{dateStart}} {{timeStart}} au {{dateEnd}} {{timeEnd}}", "EventRepeatsDaily": "Se répète tous les jours", "EventRepeatsWeekly": "Se répète toutes les semaines", "EventRepeatsMonthly": "Se répète tous les mois", "AddEventLink": "Ajouter un lien", "NoLinkAvailable": "Aucun lien", "AddEventNotes": "Ajouter des notes", "EventRecurrencePlaceholder": "Pas de r<PERSON>", "EventRecurrenceNever": "<PERSON><PERSON>", "EventRecurrenceUntil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventRecurrenceCount": "<PERSON><PERSON>", "EventRecurrenceCountTimes_one": "répétition", "EventRecurrenceCountTimes_other": "répétitions", "ModifyRecurringEvent": "Modifier l'évènement récurrent :", "ModifyRecurringEventAll": "<PERSON>ut", "ModifyRecurringEventOccurence": "Cette occurence", "AccessEvent": "Accéder à l'évènement", "EventToolbarAccess": "Accéder", "JoinEventLink": "Rejoindre", "EventGoogleMeet": "Google Meet", "EventTeams": "Microsoft Teams", "EventZoom": "Zoom", "Menu": "<PERSON><PERSON>", "AudioFE": {"Yes": "O<PERSON>", "No": "Non", "EnableAutoPlayLabel": "Lecture automatique ", "EnableAutoPlayExplanation": "Lance l'audio automatiquement", "AllowPlaybackRateChangeLabel": "Autoriser le changement de vitesse de lecture ", "AllowFullScreenLabel": "Autoriser l'utilisateur à mettre le lecteur audio en plein écran", "AllowPiPLabel": "Autorise le 'Picture-in-Picture' ", "AllowPiPExplanation": "Le Picture-in-Picture permet d'afficher le lecteur audio dans un onglet flottant.", "AllowDownloadLabel": "Autorise le téléchargement si possible ", "AllowDownloadExplanation": "Le téléchargement est possible pour les fichiers uploadés (mp3, autre)...", "AllowEnableStatsTracking": "Activer le suivi des stats", "AllowEnableStatsTrackingExplanation": "Le suivi des stats permet de récolter des stats précises sur la lecture de l'audio par les users", "General": "Général", "Parameters": "Paramètres", "TitleTextModifier": "Titre", "ContextTitleInitString": "Titre de la piste audio", "ContextDescriptionInitString": "Description de la piste audio", "DescriptionZoneLabel": "Description", "SourceSelecterTitle": "<PERSON><PERSON> la source", "SelecterNameUrl": "Url", "SelecterNameFile": "<PERSON><PERSON><PERSON>", "PlaceholderUrlSelecter": "URL de la piste audio", "PlaceholderFileSelecter": "<PERSON><PERSON><PERSON>", "AudioWidthSelecterLabel": "<PERSON><PERSON>", "FormationElementCreateNewAudioLabel": "Audio", "AudioFEModalUploadWaitingLabel": "Upload en cours...", "AudioFEModalUploadWaitingExplanation": "Veuillez patienter pendant l’envoi.", "FormationElementAudioLabel": "Audio", "PlaceholderSentenceBeforeAnyAudio": "Votre piste audio"}, "VideoFE": {"Yes": "O<PERSON>", "No": "Non", "EnableAutoPlayLabel": "Autoplay ", "EnableAutoPlayExplanation": "Lance la vidéo automatiquement", "AllowPlaybackRateChangeLabel": "Autoriser le changement de vitesse de lecture ", "AllowFullScreenLabel": "Autoriser l'utilisateur à mettre la vidéo en plein écran", "AllowPiPLabel": "Autorise le 'Picture-in-Picture' ", "AllowPiPExplanation": "Le Picture-in-Picture permet d'afficher la vidéo dans un onglet flottant.", "AllowDownloadLabel": "Autorise le téléchargement si possible ", "AllowDownloadExplanation": "Le téléchargement est possible pour les fichiers uploadés (mp4, mp3, autre)...", "AllowEnableStatsTracking": "Activer le suivi des stats", "AllowEnableStatsTrackingExplanation": "Le suivi des stats permet de récolter des stats précises sur la lecture de la vidéo par les users", "General": "Général", "Parameters": "Paramètres", "TitleTextModifier": "Titre", "DescriptionZoneLabel": "Description", "SourceSelecterTitle": "<PERSON><PERSON> la source", "SelecterNameUrl": "Url", "SelecterNameFile": "<PERSON><PERSON><PERSON>", "PlaceholderUrlSelecter": "URL de la vidéo (youtube, vimeo...)", "PlaceholderFileSelecter": "<PERSON><PERSON><PERSON>", "TrackComponantTitle": "Chapitrage :", "ShowAndHideTracksLabel": "É<PERSON>er", "DeleteAllTracksButtonLabel": "<PERSON>ut supprimer", "UploadVttFileLabel": "Upload .vtt file", "DownloadVttFileLabel": "Download .vtt file", "InputTrackNamePlaceholder": "Nom du chapitre", "TimepickerTrackPlaceholder": "Horodatage", "ClipSectionTitle": "Plage de lecture", "ClipStartTimestampPlaceholder": "D<PERSON>but", "ClipEndTimestampPlaceholder": "Fin", "StartClipLabel": "Commencer à :", "EndClipLabel": "Finir à :", "ThumbnailSectionTitle": "Vignette :", "VignetteUploaderPlaceholder": "Upload Image : .png", "ContextTitleInitString": "T<PERSON>re de la vidéo", "ContextDescriptionInitString": "Description de la vidéo", "ButtonLabelUpdateTracksUrl": "Update", "DownloadButtonLabel": "Télécharger", "VideoWidthSelecterLabel": "<PERSON><PERSON>", "FormationElementCreateNewVideoLabel": "Vidéo", "VideoFEModalUploadWaitingLabel": "Upload en cours...", "ThumbnailFileFormatNotSupportedTitle": "Le format non supporté", "ThumbnailFileFormatNotSupportedExplanation": "Format fourni : {{extension}}, formats autorisés : {{authorized}}", "TakeFullPlaceCheckboxLabel": "100%", "SpeedMenuLayoutTitle": "<PERSON><PERSON><PERSON>", "ErrorMessageDuringMultipartInitUploadTitle": "Erreur lors de l'Upload", "ErrorMessageDuringMultipartInitUploadExplanation": "Erreur lors du multipart : ", "ErrorMessageDuringMonopartInitUploadTitle": "Erreur lors de l'Upload", "ErrorMessageDuringMonopartInitUploadExplanation": "<PERSON><PERSON>ur lors du monopart :", "VideoFEModalUploadWaitingExplanation": "Veuillez patienter pendant l’envoi.", "FormationElementVideoLabel": "Vidéo", "FormationElementAudioLabel": "Audio", "ClipSectionTitleExplanation": "Permet de tronquer le début et la fin de la vidéo", "TrackSetChapterTimeErrorMessage": "Un chapitre ne peut pas avoir un horodatage inférieur au précédent", "TrackVerifTimeStampNullBeforeSet": "Un chapitre sans horodatage précède un chapitre ayant un horodatage", "trackVerifErrorMessageTitle": "Erreur d'horodatage", "PlaceholderSentenceBeforeAnyVideo": "Vot<PERSON> vid<PERSON>o", "VidstackMimeTypeNotSupportedTitle": "<PERSON><PERSON><PERSON>", "VidstackMimeTypeNotSupportedDescription": "Type de fichier '{{fileExtension}}' non supporté", "VidstackParsingYoutubeErrorTitle": "Erreur parsing youtube", "VidstackParsingVimeoErrorTitle": "Erreur parsing vimeo"}, "DiapoFE": {"DiapoFEBigButtonLabel": "Diapo", "DiapoNumberXName": "Diapositive N°", "AddOneOrMultipleTabButtonLabel": "Ajouter une/des diapos", "DownloadInViewerButtonLabel": "Télécharger", "HeaderNumberDocumentLabel": "Doc :", "DeleteAllTrackButtonLabel": "<PERSON>ut supprimer", "PlaceholderSentenceBeforeAnyDocuments": "Vos documents seront affichés ici", "ActionButtonDeleteLabel": "Supprime le document.", "ActionButtonHideLabel": "Affiche/Cache le document.", "ActionButtonAllowDownloadLabel": "Autorise/Empêche le téléchargement.", "ActionButtonGoToLabel": "Ouvre le document.", "ActionButtonDownloadLabel": "Télécharge le document. Nécessite d'abord d'être sauvegardé avant de pouvoir être téléchargé", "ActionButtonDownloadSmallLabel": "Télécharger", "DiapoFeDownloadErrorMessageTitle": "<PERSON><PERSON><PERSON>", "DiapoFeDownloadErrorMessageDescription": "Le fichier doit d'abord être sauvegardé avant de pouvoir être téléchargé.", "AddFileRowUploadLabel": "Ajouter un fichier", "OnRowUploader": "<PERSON><PERSON><PERSON><PERSON> ou cliquer pour remplacer le fichier", "ErrorMessageNoFileToUpload": "Aucun fichier fourni à l'upload", "DefaultTitlePlaceholder": "Titre", "LoadingDocumentTitlePlaceholder": "Chargement...", "LoadingDocumentFilenamePlaceholder": "chargement...", "TableTitleTitre": "Titre", "TableTitleFile": "<PERSON><PERSON><PERSON>", "TableTitleActions": "Actions", "ErrorFileUpdateFailed": "Upload de fichier a <PERSON>", "First": "Première diapo", "Previous": "Diapo précédente", "Next": "Diapo su<PERSON>", "Last": "Dernière diapo", "Download": "Télécharger la diapo"}, "VideoPreview": "<PERSON><PERSON><PERSON><PERSON>", "DiapoPreview": "<PERSON><PERSON><PERSON><PERSON>", "EditThumbnail": "Éditer la vignette", "HideEditThumbnail": "<PERSON><PERSON> l'éditeur de vignette", "ImportTracks": "Importer les chapitres", "ExportTracks": "Exporter les chapitres", "ElementNotYetAdded": "Cet élément n'est pas encore ajouté", "EditSecondarySupports": "Activer le mode édition", "EditMainCourse": "<PERSON><PERSON><PERSON> le cours", "CourseGroupsAccess": "{{groupsCount}} groupes ont accès à ce cours", "PlusUsersCount": "+{{usersCount}} utilisateurs.", "FormationCompletion": "{{percent}}% terminée", "FormationResponsible_one": "Responsable", "FormationResponsible_other": "Responsables", "Hours": "<PERSON><PERSON>", "Minutes": "Minutes", "Seconds": "Secondes", "UnavailableFeature": "Bientôt disponible ! 🚀", "NewCourseImage": "Illustration", "ScormFE": {"ScormFeButtonLabel": "Package SCORM", "GeneralTabFE": "Package", "PlaceholderUploadFileSelecter": "upload scorm", "SandboxCheckboxLabel": "Mode Sandbox", "SandboxCheckboxHooverExplanationLabel": "Corrige certain soucis de navigation SCORM (en développement)"}}