{"1by1": "1 by 1", "AboutContentAndOffers": "For content and offers:", "AccessExercice": "Access the exercise", "Activity": "Activity", "ActualLocalization": "Current location", "AddACourseToAllQuestions": "📒 Add a lesson to all questions:", "AddAFile": "Add a file", "AddAPostType": "Add a discussion type", "AddAType": "Add type", "AddAnImage": "Add a picture", "AddBlock": "Add a block", "AddCategory": "Add a category", "AddCustomField": "Add a custom field", "AddDate": "Add a date", "AddElement": "Add an item", "AddExercice": "Create a new exercise", "AddGroups": "Adding groups", "AddItem": "Add a reply", "AddNewYear": "Add a year", "AddNotion": "Add a concept", "AddQuestions": "Add questions", "AddSection": "Add section", "AddUser": "Add user", "AdditionnalElementsCorrection": "Additional correction item(s)", "Admin": "admin", "Administration": "Administration", "All": "All", "AllCategories": "All categories", "AllDay": "All day", "AllDiscussionsType": "All types of discussions", "AllExercices": "All exercises", "AllGroups": "All groups", "AllMyExercices": "All my exercise series", "AllMyGroups": "All my groups", "AllMyNotifications": "All my notifications", "AllNotions": "All the concepts", "AllOrNothing": "All or nothing", "AllOrNothingItem": "All or nothing item", "AllQuestions": "All exercises", "AllSubjects": "All the subjects", "AllTheTime": "All the time", "AllUsers": "All the users", "AlreadyHaveAccount": "I already have an account", "AlreadyLinkedToCourse": "Already linked to this course", "Annal": "<PERSON><PERSON>", "Annals": "Annals", "AnnounceForAllGroups": "Announcement for all groups", "AnswerdQuestion": "Questions answered", "AnswersWithoutQuestionsCorrection": "Corrected without statement", "AppendixExplanation": "These are elements that will be accessible throughout the series by students", "AreYouSure": "Are you sure?", "AskNewQuestion": "Ask a new question or start a discussion", "AskQuestion": "Ask a question", "AssociatedCourseTo": "📒 Course(s) associated with this", "AutentificationError": "Error during authentication", "AutomaticallyAdded": "Added automatically", "Available subscription": "Subscriptions available", "AvailableExercice": "Exercise available", "Availibility": "Availablity", "BackToCourse": "Back to course", "BackToExam": "Back to review", "BackToExerciceList": "Return to exercise list", "BackToExericesList": "Back to list of exercises", "BackToGenerator": "Return to generator", "BeginDate": "Start date", "BlockedUsers": "Blocked users", "BottomPart": "Lower part", "Buy1Credit": "I buy 1 credit", "BuyCreditExplanation": "Buy a credit that can be used to access an offer afterwards!", "By": "By", "CGU": "CGU", "Callout": "Boxed", "Cancel": "Cancel", "CancelDeletion": "Undelete", "CancelSimulation": "Cancel simulation", "CantFindTopic": "Topic not found, probably deleted.", "Category": "Category", "CategoryImage": "Picture", "CategoryName": "Folder name", "CategoryPermissions": "Permissions of this category", "CategoryResults": "Results by Category", "CertaintyExercice": "💪 Degree of certainty on this exercise", "Change": "To change", "ChangePassword": "change my password", "ChangePasswordUser": "Change user password", "ChangeSubjectExercices": "Change the material of the selected exercises", "ChatName": "Name the discussion", "CheckExistingQuestion": "Check that your question is not already asked below", "ChoiceTimer": " You can choose to do this exercise in a timed way, or to have no time limit. If you choose to be timed, the workout will end when the countdown ends.", "Choose": "<PERSON><PERSON>", "ChooseASubject": "Choose a subject", "ChooseCategory": "Choose a category...", "ChooseContentType": "Choose content type", "ChooseCourse": "Choose a course", "ChooseEmojis": "Choose an emoji", "ChooseExerciceType": "Choose the type(s) of exercises...", "ChooseExercicesSerieType": "✅ Choose the type of exercise series", "ChooseGradientColor": "Choose the colors for the gradient", "ChooseGroupAccess": "Choose groups with access", "ChooseGroups": "Choose groups...", "ChooseMCQType": "Choose the type of exercises...", "ChooseNewPassword": "Please enter a new password", "ChooseQuestionType": "Choose question type...", "ChooseRandomQuestions": "Choose questions at random (Otherwise chronological order)", "ChooseReferent": "Choose teachers...", "ChooseSubject": "Choose a subject...", "ChooseSubjects": "🗂 Choose the materials", "ChooseYear": "Choose a year", "ClickHere": "Click here", "CloseWithoutSaving": "Close without saving", "Color": "Color", "ColorOne": "Color 1", "ColorTwo": "Color 2", "Comment": "Comment", "Comments": "Comments", "CommercialName": "Trade name", "CompanyType": "Type of society", "ConfirmDeletion": "Confirm deletion", "ConfirmPassword": "Confirm password", "ConfirmPasswordCreation": "Confirm password", "CongratsNoMistake": "Well done ! You made no mistake!", "Congratulations": "Well done !", "Connected": "Connected", "Connections": "Connections", "ContactEmail": "Contact email", "ContactPhoneNumber": "Contact phone (optional)", "ContentAccessible": "You have access to questions on types of content:", "ContentType": "Content type", "CopyrightWarning": "Any total or partial reproduction of the content of the site is strictly prohibited and may constitute an offense of counterfeiting.", "Correction": "Correction", "CorrectionElements": "Correction items", "CorrectionParameters": "⚙️Fix settings", "CourseCategorie": "Course category", "CourseDifficulty": "Course difficulty", "CourseFile": "Course file", "CourseLength": "Course duration in minutes", "CourseSeen": "Courses viewed", "CourseTitle": "Course title", "CourseToAttribute": "Course to assign", "CourseToLook": "Courses to search", "CourseToLookInQuestion": "Choose the course to search for in the questions", "CourseType": "Class type (layout)", "Courses": "Course", "CoursesPermissions": "Course permissions", "CoverImage": "Cover image (optional)", "Create": "Create", "CreateElementBeforeSettingUpAccessGroups": "Create the item before you can manage access groups.", "CreateForumHere": "Add a forum to this category", "CreateNewCategory": "Create a new folder", "CreateNewCourse": "Create a new course", "CreateNotion": "Create a concept", "CreateUser": "Create user", "CreatedBy": "Created by", "CreatedExercice": "Exercise created", "CurrentSubscription": "Current subscription", "CustomFields": "Custom fields 🔥", "CustomLinks": "Custom links", "DateAndUpdate": "Date and update", "Dates": "Dates", "DefaultScaleForExam": "Default scale for this exam", "DefinedDates": "Dates set", "Delete": "DELETE", "DeleteChat": "Delete the conversation", "DeleteContent": "Delete content", "DeleteField": "Delete this field", "DeleteMyAccount": "Delete my account", "DeleteThisDate": "Delete this date", "DeleteTraining": "Remove Format", "DeletedAccounts": "Accounts deleted", "DeletedCourses": "Courses removed", "DeletedUsers": "Users deleted", "DeletionRequests": "Deletion requests", "Description": "Description", "DescriptionExemple": "Description (eg. <PERSON><PERSON><PERSON>)", "DescriptionForumCategory": "Category Description", "DesiredQuestions": "Desired questions:", "DetailResultsAndComments": "Exercise detail page and comments", "Difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DiscussionModuleExplanation": "💬 Discussions Module: Allows students to ask questions under the course.", "DiscussionSpace": "Discussion area", "DiscussionType": "Type of discussions", "DiscussionsModule": "Discussion module", "DisplayAverage": "Show average", "DisplayChildren": "Show children", "DisplayCorrection": "Show correction", "DisplayDetailedCorrection": "Show detailed correction", "DisplayGrade": "Show the grade obtained", "DisplayOrder": "order of appearance", "DisplayOrderOptionnal": "Display order (optional)", "DisplayParents": "Show Parents", "DisplayResultAnalysis": "Show results analysis", "DisplayWeakStrongPoints": "Show strong and weak points", "DoComment": "Comment", "DoMoreMCQForBetterResults": "The more exercises you do, the more the system will be able to help you optimize your revisions", "DoThisExercice": "Do this exercise", "Done": "Facts", "DoneOn": "Do it", "DontStartTimer": "Do not start the stopwatch", "DoubleParentChildren": "Dual parents/children", "DownloadTemplateXls": "Download the Excel template", "Dynamic": "Dynamic", "Edit": "Edit", "EditCategory": "Edit this category", "EditCourse": "Edit the course", "EditExerciceInfos": "Edit exercise information", "EditGroupPermissions": "Change group permissions", "EditLinkedExercices": "Edit Attached Exercises", "EditQuestion": "Edit question", "EditQuestions": "Edit exercises", "EditScale": "Modify the scale", "EditThisElement": "Edit this item", "ElementTypeToInsert": "Type of element to insert", "ElementsAboveOf": "Element(s) above the", "EmbeddedMainVideoLink": "Main video embedded link", "EnableOrDisableCourseModules": "Enable/Disable Course modules", "EndDate": "End date", "EndYear": "End year", "EnrichedCourse": "Enriched Course", "EnterPseudo": "Enter a user's nickname", "EquationEditor": "Equation editor", "Error": "Error", "ErrorDoubleParentChildren": "Dual Parent/Child Errors", "ErrorParentChildren": "Parent/Child Errors", "ErrorWhileLoadingProfile": "Error retrieving your profile", "Errors": "Errors", "EstimatedDifficultyOfQuestion": "Estimated difficulty of the exercise", "EvaluateCertaintyLevel": "Assess the level of certainty", "EventDescription": "Description of the event", "EventName": "Event name", "Events": "Events", "ExamName": "Exam name", "Exams": "<PERSON><PERSON>", "ExempleCategoryName": "Carbohydrates", "ExempleCourseTitle": "01: Chapter 1", "ExempleFolderName": "2020-2021 - Medicine", "ExempleName": "Name (e.g: First semester)", "ExempleSubject": "Title (eg. UE4: Biostatistics)", "ExerciceAndLesson": "Courses & Exercises", "ExerciceCorrection": "Correction of the exercise", "ExerciceGenerator": "Create your own workout", "ExerciceSerieOfCourse": "✅ Series of exercises evoking the course", "ExerciceType": "Type of exercise", "Exercices": "Exercises", "ExercicesCreationDate": "🗓 Choose the dates for creating the Exercises", "ExercicesDone": "Exercises Facts", "ExercicesScale": "Exercise scales", "ExercicesSerie": "Exercise series", "ExercicesType": "Type(s) of exercises", "ExerciseSets": "Exercise series", "ExercisesSeries": "Exercise sets", "ExistingYears": "Existing years", "ExoteachGivesExoteach": "The platform is provided by EXOTEACH:", "ExoteachQuizz": "Exercise series", "ExplainWhyTrueFalse": "Explain precisely why it is true or false", "ExplanationAnalysisResults": "Our algorithm estimates your level of knowledge based on your answers to the diagnostic tests and the results obtained by all other candidates. Complete as many diagnostic tests as possible to obtain the most accurate estimate possible.", "ExplanationDisplayExercice": "The classic display will display all the exercises on a single page, the 1 by 1 allows you to display only one exercise at a time", "ExplanationModuleRevision": "🎓 Revisions module: All questions related to the course not yet done!", "ExplanationQuestionType": "Content types allow you to change access permissions and classify exercises", "ExplanationTags": "Exercise tags allow you to classify your exercises, this option is optional", "ExplanationTimer": "This series is timed, you will have a certain time per exercise, as soon as you click on Start the stopwatch. At the end of the countdown, the series will end!", "ExportOnlyQuestions": "Export exercises only", "ExportXLSAllUsers": "Export XLS all results", "FeaturedMedia": "Featured media", "FeaturedMediaType": "Featured media type", "File": "File", "FileToImportCSV": "File to import (.csv)", "FileToImportExoqcm": "File to import (.exoqcm)", "FileToImportXLS": "File to import (.xls)", "FileToImportXLXS": "File to import (.xls or .xlsx)", "FileType": "File type", "FinishExamToGetResult": "Complete all sets of exercises in the exam to get your final result! 🎯", "FirstNameAndNameOfLeader": "Name and First name of manager", "FixedGradeIfNothingChecked": "Fixed rating if nothing is checked", "FolderName": "Folder name", "FontWeight": "Text thickness", "Forum": "Forum", "ForumDescription": "Forum description", "ForumIcon": "Forum icon", "ForumName": "Forum name", "FreeText": "Free text", "FromDate": "From a date", "GainPointCertainty": "Points obtained by level of certainty", "General": "General", "GeneralConfig": "General settings", "GeneralProgress": "General Progress", "GeneralSynthesis": "General Summary", "Generalities": "🚀 General", "GenerateExericesSerie": "Generate the exercise series", "GlobalView": "Overview", "GlobalViewResponsabilities": "Overview of group responsibilities", "GroupAccess": "Groups and access", "GroupsConcerned": "Related schedules", "GroupsHavingAccessToThisElement": "Groups with access to this item", "HQaddress": "Address of headquarters", "HQsocial": "The head office", "HTML": "HTML", "HTMLCode": "HTML code", "HeaderOf": "Hat of", "HowDoesItWorks": "How it works ?", "HowManyPointsIsTheQuestionWorth": "Exercise value (pts)", "HowManyQuestionInSerie": "🗂 How many questions in your set of exercises", "HowToBuyCredit": "You can log in from a web browser on the platform to update your offer, or buy credits to spend here.", "Image": "Picture", "ImageHeight": "Image height", "ImageLegend": "Image caption (description)", "ImageWidth": "Image width", "ImportExistingCSV": "Import existing questions from a CSV file", "ImportFromExcel": "Import from a spreadsheet", "ImportNewOrder": "Import change of order within the series", "ImportQuestion": "Import an exercise", "ImportResultsXLS": "Import XLS student results", "ImportUsersFromTable": "Import users from a spreadsheet", "InProgress": "In progress", "Info": "Information", "IsDefaultScale": "Is the default scale", "IsPublishedVisible": "Is published (visible)", "ItemCorrection": "Correction of the item", "ItemNumber": "Number of items:", "JSONExport": "Export JSON format", "LaunchRequest": "Run the query", "LaunchTimer": "Start the timer!", "LeftPart": "Left part", "Legal notices": "Legal Notice", "LegalInformations": "Billing information and legal notices", "Like": "Like", "Link": "Link", "LinkName": "Link name", "LinkNameExemple": "Link Name (e.g: Secret Packages)", "LinkTitle": "Link title", "Localization": "Time zone", "LogInAs": "<PERSON><PERSON> as", "Login": "Connection", "Logout": "Disconnect", "LookForPlaform": "Find the platform you want to connect to", "LookForSerieExercice": "Find a set of exercises", "MCQ": "MCQ", "MCQGenerator": "⚙ Exercise generator", "MCQType": "Type of exercise series", "MCQs": "MCQs", "ManageExercices": "Manage exercises", "ManageFolders": "Manage folders", "ManageGroupAccess": "Manage groups and access", "ManageTeacher": "Manage teachers", "ManageTitle": "Manage titles", "MandatoryTimed": "⏱Stopwatch obligatory", "MandatoryTimedExplanation": "By checking this box, the series will be timed", "Manual": "Manual", "ManuallyAdded": "Added manually", "MarkAllAsRead": "<PERSON> all read", "MarkAsNonResolved": "<PERSON> as unresolved", "MarkAsResolved": "Mark as solved", "MassOperation": "Mass operation", "MaxGrade": "Maximum score", "Messages": "Messages", "MessagesPosted": "Messages posted", "MinGrade": "Minimum score", "ModifyCourse": "Edit course", "ModuleQuickAccessExplanation": "⚡️ Quick access module: All specific exercises related to the courses + their correction accessible in one click", "Modules": "<PERSON><PERSON><PERSON>", "Monetico": "Monetico", "MostRecents": "Most recent", "MotiveQuestion": "Subject or subject of your question", "MoveCourse": "Move this course", "MyGrade": "My grade", "MyNotifications": "My Notifications", "MyPackage": "My package", "MyProfile": "Profile", "MySubjects": "My subjects", "MySubscription": "My subscription", "NOLINKEDCOURSE": "NO COURSE IS LINKED TO THIS EXERCISE, PLEASE ADD ONE 😀", "Name": "Name", "NameForumCategory": "Category name", "NeverDone": "Never done", "NewAnnounce": "New announcement", "NewCategory": "New category", "NewExercice": "New Year", "NewSerieExercice": "New exercise series", "NewTopic": "New topic", "Next": "Following", "NextQuestionWillBeAbout": "The following exercises will focus on:", "NoCreditAvailable": "You don't have credits to spend, buy some before you can upgrade", "NoResult": "No result", "NoResultsForSelectedGroups": "No results with selected groups.", "NoTopics": "<PERSON><PERSON><PERSON>", "None": "None", "NotDoneYet": "Not done yet 😢", "NotificationTitle": "Notification title", "Notifications": "Notifications", "Notify": "Notify", "NotionModule": "🌀 Notions module (if any are detected)", "NotionModuleIfAny": "Concept modules (if any)", "Notions": "Notions", "NotionsAvailable": "Concepts available", "NotionsCovered": "Concepts covered", "NotionsInThisCourse": "Notions in this course", "NumberOfCourses": "Course", "NumberOfMCQ": "Exercises", "NumberOfNotions": "Notions", "NumberOfQuestions": "Number of questions:", "NumberOfTry": "Number of trials:", "OnlyDisplayMyGroupAgenda": "Show the schedule of my groups only", "OnlyUsernameDisplayed": "Only your username is displayed to other members", "Order": "Order", "Organizers": "Organizers️", "OtherFiles": "Other useful files", "OtherGroupGivingAccess": "Other groups giving access to courses", "Outdated": "Standout", "Owner": "Owner", "Participants": "Participants", "PasswordDontMatch": "Passwords do not match", "PixelDecalage": "Offset in pixels", "Planning": "Schedule", "PleaseEnterNumber": "Please enter a number", "Post": "New discussion", "PostList": "List of threads", "Posted": "Posted", "Preview": "Preview", "PrincipalFile": "🎯 Featured Support", "Print": "To print", "PrivateMessage": "Private message", "PrivateMessages": "Private messages", "Progress": "Progress", "PublicationDirector": "Publication Director", "PullToRefresh": "Pull to refresh", "QCU": "QCU", "QuestionOptions": "🛠 Exercise options", "QuestionScale": "Scale of the exercise", "QuestionSeriesAboutCourse": "Series of exercises evoking the course", "QuestionTags": "Exercise Tag(s)", "QuestionType": "Type of exercise", "Questions/Answers": "Questions answers", "QuestionsAnswer": "Questions answers", "QuestionsWaiting": "Questions awaiting answer", "QuizzGenerator": "Exercise generator", "RCSnumber": "RCS number", "RandomlyDrawnQuestions": "Questions drawn randomly", "RandomlyDrownitems": "Randomly drawn items", "RankingByGrade": "Ranking by rating", "Reason": "Reason", "Received": "Receipts", "ReferentTeacher": "Referent professors", "Refresh": "Refresh", "Registered": "Registered", "RemoveExerciceSerieOfExam": "Remove this series from review", "Reply": "Answer", "Report": "Report", "RequiredGroups": "Required groups", "RequiredSubscription": "Package required in advance", "ResetFilters": "Reset filters", "Resolved": "Resolved", "Results": "Results 🏆", "ResultsDetail": "See detailed results", "ResultsOf": "Result of", "ResultsSynthesis": "Summary Results", "Resultsfor": "Results for:", "RevisionCalendar": "Revision calendar", "Revisions": "Revisions", "RevisionsModule": "Revisions Module", "RichText": "Rich text", "RightPart": "Right part", "Room": "Room", "SCQ": "QCU", "SCQs": "QCUs", "SIRET": "SIRET", "SaveAndClose": "Save and close", "ScaleName": "Scale name", "Score": "Score", "ScoreType": "Scale type", "SearchByMail": "Search by email...", "SearchByUsername": "Search by nickname...", "SearchKeywords": "Search by keyword...", "SearchNotions": "Search among the concepts...", "SecondaryMedias": "Secondary supports", "SectionIcon": "Section icon (optional)", "SectionName": "Section name", "SeeAll": "See everything", "SeeAnswers": "See the answers", "SeeCorrection": "See the fix", "SeeCorrectionAndResults": "See correction and result", "SeeCorrectionOf": "See the correction of", "SeeDetails": "See the details", "SeeMcq": "See the exercise", "SeeMySubscription": "View my subscription", "SeePrintedSerie": "To print", "SeeProgression": "View profile", "SeeQuestionsWithoutAnswers": "See unanswered questions", "SeeRelatedCourse": "Related course", "SeeRelatedExercice": "See the exercise concerned", "SeeResultAndCorrection": "View student result and correction", "SeeThisExercice": "See this exercise", "Seen": "Seen", "Seene": "Views", "SelectCategory": "Select categories", "SelectCorrection": "Select a correction", "SelectCourse": "Select course", "SelectCourseXtoY": "Select all questions having course X and assign them course Y", "SelectNotions": "Select concepts", "SentMessages": "Sent message", "SerarchByFirstName": "Search by first name...", "SerieAppendix": "Appendices to the series", "SerieDescription": "Description of the series", "SerieOrder": "Order in series", "SerieTitle": "Series title", "SessionName": "Session name", "Settings": "Settings", "ShowAll": "Show all", "SimulateStudentAccount": "Simulate student account", "SimulateTeacherAccount": "Simulate teacher account", "SizeInPixels": "Size in pixels", "SkillLevel": "Mastery level:", "SorryUnfindableExercice": "Sorry, this exercise cannot be found!", "Start": "To start up", "StartYear": "Start year", "State": "State", "StatsAndUserActivity": "User stats & activities", "StepName": "Stage name", "StudentResults": "Student results", "SubectsProgress": "Progress by subject", "SubjectReferent": "Material managers", "SubjectsProgression": "Progression by Subject", "SubscriptionDescription": "Description of the offer", "SubscriptionImage": "Offer Picture", "SubscriptionName": "Title of the offer", "Subscriptions": "Subscriptions", "Success": "Success", "SuccessConditions": "Conditions for success", "SummarySerieExercice": "🌟 Summary on the series of exercises", "Supports": "Brackets", "SureOfDeletion": "Sure to delete?", "SureToChangeTypeElement": "By changing type you will lose the modifications of this element, are you sure?", "SureToDeleteGroups": "Are you sure to delete this group? If there are users in this group, they will no longer be there.", "SureToDelinkCourse": "Certain to remove it from this course? The exercise will not be deleted", "SureToDelinkQuestion": "Certain to remove the question from the series?", "SureToQuitUnsavedChange": "Some changes are not saved. Are you sure you're leaving?", "Synchronisation": "Synchronization", "TeacherPanel": "Tutors panel", "Team": "Team", "TermsAndConditions": "Terms and conditions of use", "TermsAndSellConditions": "Terms of Sales", "TestYourself": "Test yourself", "TheMoreExerciceYoudo": "The more exercises you do, the more the system can help you optimize your revisions!", "TimeByQuestion": "Time per exercise", "Timed": "Stopwatch", "TimedExercice": "Timed exercise", "Title": "Title", "TitleLevel": "Title level (1,2,3)", "TitleLevelExplanation": "Level 1 titles are hierarchically higher than level 2, 3, etc.", "TitleName": "Title name", "TodayPlanning": "Today's schedule", "Top": "top", "Total": "Total", "TotalDuration": "Total duration", "TrainingMaterialsAboutCourse": "Exercises (annals, or others) on the course", "TrainingName": "Name of training", "TryThisExercice": "Test this series", "Type": "Kinds", "TypesToShow": "Types to display", "UnlockGroupWhenFinishExercice": "Groups unlocked when this quiz is completed", "UnsavedModificaitons": "Unsaved changes", "Unselect": "Deselect", "Update": "To update", "UpdateInfos": "Other information", "UpdateMedia": "Update support", "UpdatedAt": "Updated at", "UsersRegistered": "Members", "VatNumber": "VAT number", "Version": "Version", "Video": "Video", "Videos": "Videos", "VisibleByAll": "Visible to everyone", "VisibleInList": "Visible in the list", "Visualize": "View", "Warning": "Warning", "WarningNoLinkedCourses": "Please note that you have not selected a course linked to this exercise. The platform works less well if the exercise is not linked to any course. Would you like to continue ?", "WebsiteEditor": "  EDITOR of the site", "WereNotDebited": "You have not been charged.", "WhatsAnExerciceType": "What is a type of exercise?", "WhyDates": "Why dates?", "WhyDoYouReportThisComment": "Why are you reporting this post?", "WhyReport": "Why are you reporting", "WorkTimeAdvised": "Recommended working time", "WriteYourAnswer": "Enter your answer", "WriteYourMessage": "Write your message...", "XLSExportUsersAndResults": "Export XLS users and results", "Year": "Year", "YouAlreadyDidAllExercicesForThisPeriod": "You have already done all the exercises in this period", "YouCanAddMoreAfterCourseCreation": "You will be able to add other supports and information once the item has been created.", "YouThinkThat": "You think that...", "YourChoice": "Your choice", "accessibleCourses": "accessible courses", "afficherLesImagesSurLesItems": "Show images on items", "afficherLesLmentsAuDessusDesQcms": "Show items above exercises", "afficherLesTagsTypeDeQuestionCoursEtc": "Show tags (Question type, course, etc.)", "allCategoriesFromSelectedSubjects": "All categories of selected subjects", "allCourses": "All courses", "annalesDuCours": "Records of the course", "app_name": "Exoteach", "back": "Back", "biographyExplanation": "For the curious who will visit this profile", "by": "by", "capitalSocial": "Share capital", "chooseCategories": "Choose categories", "chooseCourses": "Choose the courses", "chooseTheType": "Choose the type...", "comment": "comment", "comments": "comments", "correctionGrid": "Correction grid", "course": "course", "courseUpdated": "Course updated", "createCourse": "Create a course", "createNewSubject": "Create a new material", "createUser": "Create this user", "currentMonth": "Current month", "currentPassword": "Current Password", "deleteThisCourse": "Delete this course", "deleteThisResult": "Delete this result", "disableAutoNotionAnalysis": "Disable automatic concept analysis", "discussions": "discussions", "doComment": "comment", "editAvatarPicture": "Edit avatar...", "email": "E-mail", "faitesCetExerciceAuMoinsUneFois": "Do this exercise at least once", "general": {"Activate": "Enable", "Admin": "admin", "Advices": "Advice", "Appendix": "Appendices", "Average": "Average", "By": "By", "Check": "Check", "Close": "Close", "Confirmation": "Confirmation", "Continue": "Continue", "Create": "Create", "Dashboard": "Dashboard", "Details": "Detail", "Disabled": "Disable", "Display": "Display", "Download": "Download", "Duplicate": "Duplicate", "Edit": "To modify", "Emojis": "Emojis", "Events": "Events", "Exams": "<PERSON><PERSON>", "FALSE": "FAKE", "Field": "Field", "Filter": "Sort", "Folder": "Case", "Folders": "Folders", "Grade": "Note", "Groups": "Groups", "Help": "Help", "Home": "Welcome", "Ignore": "Ignore", "Informations": "Information", "Item": "<PERSON><PERSON>", "Lesson": "Course", "List": "List", "Login": "<PERSON><PERSON>", "Manage": "Manage", "Months": "Month", "MostRecent": "Most recent", "Notifications": "Notifications", "Notions": "Notions", "OK": "OK", "Platform": "Platform", "Profile": "Profile", "Published": "Published", "Publishede": "Published", "Question": "Question", "QuestionAnswer": "Questions answers", "Questions": "Questions", "Redo": "Redo", "Remove": "To remove", "Reports": "Reports", "Result": "Result", "Results": "Results", "Retry": "Try again", "Role": "Role", "Scale": "Scale", "Security": "Security", "See": "See", "SocialNetwork": "Social networks", "StartWith": "Starts with", "Statistics": "Statistics", "Subject": "Matter", "Subjects": "Materials", "Subscription": "Subscription", "Suggestions": "Hints", "SuperAdmin": "SuperAdmin", "TRUE": "TRUE", "Timezone": "Time zone:", "Title": "Securities", "Top": "top", "User": "User", "Username": "username", "Validate": "To validate", "Value": "Value", "add": "Add", "address": "Address", "avatar": "avatar", "back": "Back", "biography": "Biography", "city": "City", "classic": "Classic", "confirm": "Confirm", "dislike": "Dislike", "done": "Finished", "Finish": "Finish", "email": "E-mail", "emoji": "emoji", "firstname": "First name", "import": "Import", "includes": "Contains", "logins": "ID", "logo": "logo", "moreAddress": "Additional address (optional)", "no": "No", "pedagogy": "Pedagogy", "phoneNumber": "Phone number", "preferences": "Preferences", "question": "question", "questions": "questions", "save": "To safeguard", "search": "To research", "signUp": "Registration", "teacher": "Teacher", "total": "Total", "type": "Kind", "untreacable": "not found", "upload": "Upload", "yes": "Yes", "Administrators": "Administrators", "Beginning": "Beginning", "Calendar": "Calendar", "Created!": "Created!", "Edition": "Editing", "End": "END", "Group": "Band", "Language": "Language", "NotDone": "Not Completed", "Rank": "Ranking", "Students": "Students", "Tutors": "Teachers", "Unclassified": "Unclassified", "Updated!": "Update !", "Updating...": "Update...", "available": "available", "or": "Or", "rank": "ranking", "required": "required", "ChangeParentSubject": "Change subject", "Forums": "Forums", "Loading...": "Loading...", "NotPublished": "Non published", "Publish": "Publish", "UnPublish": "To publish", "Avatar": "Avatar", "Appearance": "Appearance", "Arborescence": "Tree structure", "ArtificialIntelligence": "Artificial intelligence", "Buildings": "Buildings", "Company": "Business", "Country": "Country", "Integrations": "Integrations", "Links": "Connections", "Payment": "Payment", "Subscriptions": "Subscriptions", "group": "band", "Building": "Building", "Exercice": "Exercise", "Exercices": "Exercises", "Week": "Week", "Weeks": "Weeks", "custom": "Custom", "lastname": "Name", "reSee": "Goodbye", "Commercial": "Commercial", "!=": "≠", "<": "<", "<=": "≤", "==": "=", ">": ">", ">=": "≥", "AdminMenuTemplates": "Templates", "CreateACopy": "Create a copy", "CreateAVariant": "Create a variation", "Move": "Move", "Parent": "Parent", "SelectCoursesPlaceholder": "Select courses", "SelectUeForSeriePlaceholder": "Select a subject", "to": "towards"}, "globalAnnounces": "Advertisement", "globalCorrection": "Corrected", "gradeObtained": "Score obtained", "hide": "<PERSON>de", "lastMessages": "Latest posts", "login": {"autoConnect": "Remember me", "enterYourEmail": "Please enter your email", "enterYourLogin": "Please enter your username", "enterYourPassword": "Please enter your password", "error": "Error: Incorrect credentials or Invalid reCAPTCHA", "forgotPassword": "Forgot your password", "forgotPasswordError": "Error: Services are unavailable. Please try again later.", "subscribeMessage": "No account yet? Register", "tellYourEmail": "Please enter your email", "username": "username or email"}, "myDiscussions": "My chats", "newEvent": "New event", "newExam": "New exam 📝", "newPassword": "New Password", "newScale": "New scale", "nextMonth": "Next month", "none": "none", "page": "page", "pageDeGarde": "cover page", "pages": "pages", "pasEncoreDeCompteInscrivezvous": "No account yet? Register", "password": "Password", "period": "🗓 Period", "placeholderDescription": "Lipid course", "placeholderTitle": "01: Whole course", "pleaseEnterCourseName": "Please enter course name", "pourVoirCeCommentaireOuPoserUneQuestion": "to see this comment or ask a question!", "previousMonth": "Last month", "printType": "Type of printing", "probablyDangerousCompetitor": "Undoubtedly a dangerous competitor", "pseudoNewUser": "<PERSON><PERSON><PERSON> (user name visible to others)", "received": "received", "result": "result", "results": "results", "resultsAnalysis": "Analysis of your results", "searchByName": "Search by name...", "send": "Send", "show": "Display", "showMyPanel": "Show my panel", "stripe": "stripe", "student": "Student", "subjectOnly": "Questions alone", "tab": {"General": "🚀 General", "Options": "🛠 Options", "Supports": "📒 Supports", "Activity": "⏳ Activity", "Annexe": "📁 Annex", "ChangeQuestionsOrderCSV": "📥 Change order of questions from list (CSV)", "Dates": "🗓 Dates", "Edit": "🚀 Edit", "ImportQuestionsFromFile": "📥 Import an exercise from a file", "ImportQuestionsRelationsParentChildrenCSV": "📥 Import parent-child relationships (CSV)", "ImportQuestionsXLS": "📥 Import questions (XLS)", "Notifications": "🔔 Notifications", "Preview": "🔎 Preview", "Modules": "🛠 Mods", "FinishedExams": "🎓 Completed Exams", "Exam": "⚙️ Review", "Progress": "📊 Progress", "Transcript": "💯 Transcript", "Exams": "🎓 Exams", "Configuration": "Configuration", "Rewards": "Awards", "GoodAnswersBySubject": "Correct answers by subject", "Overview": "👁️ Overview", "Publications": "💬 Posts", "TimeSpentExercising": "Time spent training", "TimeSpentPerDay": "Time spent per day", "Move": "Move", "DatesParticipants": "Dates & Attendees", "ExercicesSerie": "✅ Series of exercises", "TrainingResults": "Training results"}, "updateMyPackage": "Update my subscription...", "userCode": "User code (optional)", "users": "users", "version": "version", "welcome": "Welcome", "whereToFindThisNotion": "Where to find this concept", "workInProgress": "Work in progress", "youCanChangelater": "You can modify this user and its groups later if necessary.", "yourGrade": "Your Rating", "zipCode": "Postal code", "1Column": "1 column", "1toNGroupsToUsersInGroup": "1 to n groups to users in the group", "2Columns": "2 columns", "ASubjectWithoutGroupWillBeInvisible": "A subject or training without an associated group will be invisible", "ActivateNotificationsExplanation": "Activate notifications by subject to be notified of new subjects posted", "ActiveUsers": "Active users", "AddAGroupOfParticipant": "Add a participant group", "AddAParticipant": "Add a participant", "AddContentType": "Add a content type", "AddElementHere": "Add item here", "AddExamSession": "Add an exam session", "AllContentTypes": "All content types", "AllowsClassificationOnlyForAdmins": "Allows classification, visible to administrators only", "AlreadyHaveBook": "I already have the book, or an access code", "AppearAsResponsibleHelpText": "Check this box if you want your profile to appear in the list of subject managers, if you are responsible for them. For teachers and administrators only", "AppearInTeamHelpText": "Check this box if you would like your profile to appear in the list of team members. For teachers and administrators only", "AppearInTeamTab": "Appear in the Team tab", "AppearsAsSubjectResponsible": "<PERSON><PERSON>ar as subject manager", "AskAQuestion...": "Ask a question...", "AskForDeletionPersonnalData": "You can request that your account, and all associated personal data, be permanently deleted.", "Attribute1toNNotions": "Assign 1 to N notions for all corresponding items", "AttributeCourseToo": "Assign also on courses related to exercises", "AttributeNotion": "Assign concepts", "AutomaticNotionAnalysis": "Automatically analyze concepts", "BasedOnExercisesResultsWeAdviseYouToStudy": "Based on your exercise results we recommend the following revisions:", "BeginExercise": "Start the exercise series", "BlockTitle": "Block title", "BlockType": "Block type", "BuyDiagnosis": "Buy my diagnosis without the book", "ByDefaultCanBeDoneInfinitelyButFirstResultIsSaved": "By default, can be redone infinitely (0), but only the first result is recorded and taken into account in the stats.", "CategoryToSelect": "Category to select", "CertaintyLevelEvaluated": "Assessed certainty level", "ChangeUserPassword": "Change password", "ChooseCategoryToAttribute": "Choose under category to which to assign a course", "ChooseSubscriptions": "Choose subscriptions", "ClickHereToManageGroupPermissions": "Click here to manage permissions by group", "ContainsOnlyExternalQuestions": "Contains only external exercises", "ContinueWithComplementaryQuizz": "Continue with the supplementary questionnaire", "CopyrightExoteach": "© ExoTeach 2020-2024", "CourseThatWillBeAssociated": "Course which will be associated with all the exercises whose category is above", "CreateAndAddAnother": "Create and add another", "CreateAndAddQuestions": "Create and add exercises", "CreateAndClose": "Create and close", "CreateGroupChat": "Create a group chat", "CreateNewQuestion": "Create new exercise", "CreateNewScale": "Create a new scale", "CreateNewSerieIn": "Create new series in", "DateOfCompletion": "Date of completion", "Deepen": "Go deeper", "DeleteThisMessage": "Delete this post", "Diagnostic": "Diagnostic", "DiagnosticPart1": "Diagnosis part 1", "Duration": "Duration", "EditFiche": "Edit records", "EditThisUser": "Edit this user", "ExamScales": "Examination scales", "ExamSession": "Exam sessions", "ExerciceExplanation": "explanation exercise", "ExerciseSerieRealisedBy": "Exercise carried out by", "ExplanationDiagnosis": "The diagnostic questionnaire will allow you to optimize your use of the manual while introducing you to the main subjects covered.", "ExplanationExportPDF": "click here then select \"Save as PDF\"", "ExplanationExportResultPDF": "Click export to export your result to PDF format.", "ExplanationGenerationPlanning": "Click on Generate my revision schedule to generate the schedule using the J method.", "ExportExplanation": "The fix is currently not included in the export. To have the entire correction in PDF format,", "ExportLoading": "Export in progress...", "ExportMyResults": "Export my result", "GenerateMyPlanning": "Generate my revision schedule", "GeneratedQuizz": "Quiz generated", "GeneratedQuizzCorrection": "Fixed generated quiz", "GlobalStats": "Overall Stats", "GoodAnswer": "Right answer", "GoodAnswers": "Correct answers", "ICheckedRandom": "I put at random", "IWantToBuyTheBook": "I want to buy the book", "ImageCourse": "Course image", "Importing...": "Import in progress....", "KeyWord": "Keyword", "MCQTitle": "Exercise title", "ManageExercicesSeries": "Exercise series", "ModeratlySure": "Moderately safe", "MoveDown": "Move down", "MoveLeft": "Move left", "MoveRight": "Move right", "MoveUp": "Move up", "MyTrainings": "My trainings", "NextQuestionStrategy": "Strategy for passing exercises", "NoCoursesOnDate": "Nothing planned", "NoUserBlocked": "No users blocked", "Normal": "Normal", "NotSure": "Unsafe", "OpenBook": "open book", "OutOfCompetitionDoesNotAffectYourGrade": "Out of competition, does not affect your grade", "Parents/Childs": "Parents/children", "PaymentMethodUndefinedPleaseContactAdmin": "Payment method not defined! Contact the administrator.", "Permissions": "Permissions", "Permissions individuelles": "Student results", "PleaseCheckAtLeastOneItem": "Please check at least one item before continuing", "PleaseChooseAtLeastOnePackageToRegister": "Please choose at least one plan to register!", "PleaseClickOnUpdateOrRefreshManually": "Please click the \"Refresh\" button or refresh the page manually", "PleaseEnterMaterialName": "Please enter the name of the material", "PleaseEnterMcqName": "Please enter the title", "PublishedOn": "Published on", "QuestionId": "Question ID", "ReStudyFromScratch": "Start from the beginning", "RealGrade": "Actual Rating", "RefreshMyPlanning": "Update my schedule", "Remove1toNnotions": "Remove 1 to N notions for all corresponding items", "RemoveCourseAsWell": "Also withdraw from classes related to exercises", "RemoveNotions": "remove notions", "ReportErrorOrAskQuestion...": "Start a chat", "ResultsBySeries": "Results by series", "ResultsIn": "Results in", "ResultsSuccessfullySaved": "Your results have been saved!", "ResumeTraining": "Resume Training", "RevisionPlanning": "🎓 Revision schedule", "SeeMyResultAndCorrectionAgain": "Review my result and correction", "SeeOriginalExercice": "See original exercise", "Select": "Select", "SelectAStepToAddElement": "Create and select a step on the side menu to add an item.", "SelectAllQuestionWithCategory": "Select all exercises with subcategory X and assign them to course Y", "SelectAllQuestionsToAttribute": "Select all exercises with subcategory X and assign them to course Y", "SelectAnAnswer": "Select an answer", "SelectOneOrMoreAnswers": "Select one or more answers", "Smart": "<PERSON><PERSON><PERSON>", "StartTheTraining": "start training", "StartTraining": "Start training", "Still": "Again", "SubscribeForFreeExplanation": "Register for free, then update your offer by purchasing credits to spend!", "Sure": "Certain", "SureToDelete": "Sure to delete?", "SureToDeleteMessage": "Sure to delete this message?", "SureToDeleteYourAccount": "Are you sure you want to delete your account?", "SureToRemoveScale": "Are you sure you want to remove this scale from the exam?", "SureToRemoveSession": "Are you sure to remove this Session from the exam?", "TermsAndCondition": "Terms and conditions", "ThisDeletionWillBe": "This deletion will be", "ThisExerciseCanBeDone": "This series can be made", "TimeRemaining": "Remaining time", "TimesUp!": "Time elapsed !", "Train": "Practice", "TrainOnThisNotion": "Practice this concept", "TypeOfTitle": "Type of title", "UpdateAvailable!": "Update available!", "UserGroups": "User groups", "VisualizeAverageRankAndAnalysisForAllGroupsText": "View the average, ranking and analysis of results for all groups, or for one or more specific groups.", "WarningCustomPlanningWillBeErased": "Warning: Your customizations will be overwritten (J0 will be the date of broadcast in the lecture hall). Continue?", "YouAlreadyDidAllQuestionForThisPeriod": "You have already done all the exercises 🎉", "YouDidThisExercise": "You did this exercise", "YouStillHave": "You still have", "YourAnswer": "Your answer", "YourAnswers": "Your answers", "YourDeleteRequestWillBeTreated": "Your deletion request has been taken into account and will be processed within 48 hours.", "accurate": "just", "accuratePlural": "fair", "at": "THE", "credit": "credit", "credits": "credits", "definitive": "definitive", "effective": "effective", "error": "Error", "fiche": "Form", "ficheName": "Sheet name", "fiches": "Files", "generalPlanning": "📅 General planning", "groupQuestionsSameCourseWithSeparators": "Group exercises of the same course with dividers", "groupsHavingAccess": "groups with access", "inSeconds(90secByDefault)": "in seconds, (90 sec by default)", "isPublishedVisibleByStudents": "Published (free access by students)", "itGives": "given", "lastUpdateOn": "last updated on", "mitochondrie": "mitochondria", "ofSuccess": "of success", "on": "on", "orAskAQuestion...": "or ask a question...", "outOf": "on", "pleaseEnterName": "Please enter name", "points": "points", "proposal": "proposal", "proposals": "proposals", "ref": "ref", "resetMyPlanning": "Reset my schedule", "showCorrectionAfterEachQuestion": "Show correction after each exercise", "standardUser": "standard user", "thisCourse": "this course", "thisNotion": "this concept", "toUse": "use", "toUseLater": "to use later", "tutor": "Teacher", "under48h": "within 48 hours", "Actions": "Actions", "AllMyCourses": "All my courses", "AllowsYouToCombineLearningContentInOrder": "Allows you to combine several educational contents of your different subjects in a particular order.", "AllowsYouToCreateEditLearningContent": "Categorize your educational content for a particular subject area. You can create as many folders / subfolders as you want", "AreYouSureToDeleteImage": "Sure to delete this image?", "CreatedAt": "Created", "Creator": "Author", "CustomizeTitlesType": "Customize title types...", "DeleteExistingImage": "Delete existing image", "DoQuestions": "Ask the questions", "DoThisExerciseAtLeastOnceToSeeComments": "Do this exercise at least once to see the comments or ask a question!", "EditedAt": "Amended", "Fuse": "<PERSON><PERSON>", "ImpliedCourses": "📒 Classes involved", "ImpliedNotions": "🌀 Notions involved", "NothingInThisCategory": "There is nothing in this category", "SeeUserResultsForThisExercise": "See student results", "ShowQuestionCorrection": "Show question correction", "Status": "Status", "StopEditing": "stop editing", "TitleAndDescription": "Title and description", "YouAreResponsibleOfTheseGroups": "You are responsible for the following groups", "YourPackageDoesNotAllowYouToHaveAccessToContentForNow": "Your plan does not allow you to access content at the moment...", "createNewGuidedFormation": "Create a new guided training", "haveBeenFoundForThisCourse": "were found for this course", "ofType": "Of type", "questionsInThisSubject": "Questions about this subject", "selecteds": "selected", "youAreResponsibleOfAllGroupsAsSuperAdmin": "You are responsible for all groups as SuperAdmin", "AnnounceByGroups": "Announcements by group(s)", "Beginning": "Beginning", "ChooseOneOrSeveralSubjects": "Choose one or more subject(s)", "Created": "Created successfully", "DeletedWithSuccess": "Successfully deleted", "In": "In", "NoTopic": "No subject", "NonResolved": "unresolved", "RestoredWithSuccess": "Restored successfully", "TopicMarkedAs": "Topic marked as", "TopicNotFound": "Subject not found", "Updated": "Updated successfully", "aSubject": "material", "aTraining": "a training", "exerciseGenerator": {"ExerciseTypeText": "The exercises could be sorted into different types. For example, some questions may come from competition records and others are questions created from scratch. Select the type of question you want to work on", "HowItWorksText": "This page allows you to create your own set of exercises with the settings you want! Choose the subjects/chapters and type of exercise you want to train on, and our intelligent system will search for and put together the series of exercises you need for you!", "WhyDatesText": "Exercises could have been created each year, you can indicate to the generator if you wish to train on more or less old exercises. The newer ones will most of the time be closer to the program", "AddAPackOfExercises": "Add a batch of exercises..."}, "ChangeCourse": "Change course", "ChangeQuizz": "Change quizzes", "Content": "Content", "DeleteThisConversation": "Delete this conversation", "Description(Optionnal)": "Description (optional)", "ElementTitle": "Item title", "Failure": "Failure", "FileName": "File name", "LaunchSmartNotionAnalysisForPDF": "Re-analyze the concepts", "MoveThisCourseToAnotherCategory": "Move course to another category", "Preview(image)": "Preview (image)", "QuizzSettings": "Quiz settings", "SelectedCourse": "Selected course", "SelectedQuizz": "Selected quiz", "Text": "Text", "VideoEmbedLink": "Embedded video link", "VideoTitle(optionnal)": "Video title (optional)", "current": "current", "showRevisionModule": "Show the Revisions module", "BlockAction": "To block", "ByCategories": "By categories", "ByCourses": "Course", "ByNotions": "By notions", "CreateAStep": "Create a Step", "Exam": "Exam", "ExamNotFound": "<PERSON><PERSON> not found", "theImportedCourse": "the imported course", "time": "times", "given": "given", "YourAverage": "Your average", "YourAnswersAreColorFramed": "The answers you checked are framed in color", "YouNeverDidThisExerciseSerie": "You have never done this series of exercises.", "ExerciseResumeWithSuccessPercentText": "Summary of your exercise, with % success", "ExportThisUsersResults": "Export results for this user", "ExportToExcel": "Export to Excel", "GeneralAverage": "Overall average", "ImportExistingCourse": "Import an existing course", "LeastMasteredPoints": "Least mastered points", "MissingTranslation": "Translation missing", "NewDiscussion": "New discussion", "NoNotification": "No notifications", "ProgressOf": "progress of", "RegisteredSince": "Registered since", "ResultsAnalysisGlobalText": "Our artificial intelligence analyzed your results globally. You can easily see which subjects you master best and optimize your revisions. Expand the different categories to get as much information as possible!", "ResultsAnalysisOnExerciceText": "Our artificial intelligence has analyzed your results on this series of exercises. You can thus easily visualize which subjects you master best and optimize your revisions. Unfold the different categories to get as much information as possible!", "ResultsHistoryText": "History: you made this series", "SeeProgressAnd": "See progress and", "SeeProgressAndTranscript": "See progress and report", "SendAMessage": "Send a message", "ShowOnlyFirstResults": "Show only first results", "StrongPoints": "Strong points", "TimeSchedule": "Timetable", "ToReworkPoints": "Points to rework", "UnblockAction": "Unblock", "UserProfileNotFound": "Oh oh ! This user profile cannot be found...", "WeakPoints": "Weak points", "YouCanRedoThisExerciseButNewResultsWillNotBeCountedInAverage": "You can repeat this exercise but your new score will not be taken into account in the average.", "AboutContentType": "About content types", "AdressRequired": "Address required", "ChangeJ0": "Change the J0", "CityRequired": "City required", "Complexity:High": "Complexity: high", "Complexity:Low": "Complexity：low", "Complexity:Medium": "Complexity：medium", "ContactableByPrivateMessage": "Contactable by private message", "CourseNotificationOnMobile": "Class notifications on mobile app", "CustomizeMySchedule": "Personalize my schedule", "EmailRequired": "Email required", "ExercisesNotificationsOnMobile": "Exercise notifications on mobile app", "ExplanationContentType": "Exotach allows you to classify everything correctly in your platform. This is essential for good pedagogical cohesion and to optimize the platform for your learners.", "FirstNameRequired": "First name required", "InvalidEmail": "Email address Invalid", "MyRevisionSchedule": "My revision schedule", "NameRequired": "Name required", "NoBiographyYet": "No bio at the moment", "PasswordsDontMatch": "Passwords do not match", "PhoneNumberRequired": "Phone number required", "PleaseConfirmPassword": "Please confirm the password", "PleaseEnterPassword": "Please enter the password", "PleaseTypeAtLeast6Character": "Please enter at least 6 characters. Don't use easy-to-guess passwords.", "PostCodeRequired": "Zip code required", "QuestionsDone": "Questions asked", "QuestionsWithoutLinkedCourse": "Exercises without related course", "ReceiveNotificationWhenNewCourseIsPublished": "Receive a notification when a new course is posted on the mobile application", "ReceiveNotificationWhenNewExerciseIsPublished": "Receive a notification when a new series of exercises is posted on the mobile application", "ThanksYourAnswersHaveBeenProcessed": "Thank you, your answers have been taken into account!", "UsernameRequired": "Required login", "UsersCanSendYouPrivateMessages": "Users can send you private messages", "operationEnMasse": "Mass operation", "AddModule": "Add a module", "AllChallenges": "All the challenges", "Challenge": "challenge", "ChallengeType": "Type(s) of challenge", "CongratsYouCompletedAllQuestionsAvailable": "Congratulations, you have completed all the exercises available here!", "ExplainQuestionProblem": "Explain the problem of the question", "GoToNextQuestionWhenTimesUp": "Automatically move on to the next exercise if time is up", "HurryUp!": "Hurry up !", "ReportErrorOrAskQuestionFor": "Start a chat", "SecondsPerExercise": "Seconds per exercise", "Stopwatch": "Stopwatch", "SureToRemoveChallenge": "Sure to remove this challenge?", "allChallenges": "All Challenges", "messageToShowWhenChallengeDone": "Message to display when the challenge is successful", "AddAcceptedAnswerItem": "Add accepted answer", "Alphanumerical": "Alphanumeric", "ChallengeDetails": "Challenge detail", "CompletedChallenges": "Completed challenges", "ConserveTimePassedOnSerie": "Maintain the duration in case of interruption and resumption of the series", "DefaultCreatedExerciseTypes": "Default type(s) of exercises created", "DeleteDefinitely": "Delete definitely", "DeleteDefinitelyThisQuestionOrRemoveFromSerie?": "Delete this exercise permanently from the base, or remove it from this series?", "DoAtLeastOneExerciseInThisSubjectToSeeYourStrongPoints": "Do at least one exercise in this subject to see your strengths and weaknesses!", "EnableQuestionComments": "Enable user feedback on this exercise", "FaviconHelp": "Icon displayed in the browser tab. Usually in .ico format. Sometimes you have to reload the page or wait a few minutes to see the icon change.", "GlobalTime": "Overall time", "GroupedActions": "Bulk actions", "IndividualPermissions": "Individual permissions", "InfiniteUntilNoMoreQuestions": "Infinite - until exhaustion of exercises", "MyAccount": "My account", "MyPublications": "My latest publications", "NoPosts": "No posts", "NotStarted": "Unstarted", "PersonalizedTraining": "Personalized training", "RemoveFromExerciseSerie": "Remove from series", "SubjectsForWhichYoureResponsible": "Material(s) for which you are responsible:", "TimerOptions": "Stopwatch Options", "TotalTimeSpentThisWeek": "Total time spent training this week:", "YouHaveNoTimeLeftYourAnswersHaveBeenSaved": "You have no more time available, your answers have been recorded!", "YouHaveSuccessfullyCompletedThisChallenge": "Congratulations, you have completed the challenge!", "YourAnswersHaveBeenSaved": "Your answers have been saved", "YourResultsWillBeAvailableLaterByYourTeachers": "Your results will be made available later by your teaching team", "rewardsToUnlock": "rewards to unlock", "Unfinished": "Not Completed", "IncludeExercisesAlreadyDone": "Include exercises already done", "MyLibrary": "My library", "aFolder": "a file", "createNewFolder": "Create a new folder", "numberOfUsers": "Number of student(s)", "AcceptCGUButton": "I accept the T&Cs", "AddAProduct": "Add a product", "AddAPromoCode": "Add a promo code", "AddNewAttachment": "Add attachment", "AddOption": "Add an option", "AddUserInfoFolder": "Add an information folder", "Alls": "All", "Attachments": "Attachments", "BilledBy": "Billed by", "Border": "border", "ChangedAuthorFor": "The author has been changed to", "CoursePreviewHelp": "Customize the preview with an image or automatic preview", "CreateNewPage": "Create a new page", "DateAndTimePicker": "Date and time selection", "DatePicker": "Date selection", "DetailedResults": "Detailed results", "EditUser": "Edit a user", "EmailBody": "Email body", "EmailSubject": "Mail object", "ErrorChangeAuthorRights": "The selected user does not have the rights to be an exercise author", "Everyone": "Everyone", "EveryoneWillSeeThisForfait": "Everyone will see this offer", "FileAttachment": "Attached file", "GeneralDetails": "General details", "IAcceptThe": "I accept the", "IHaveReadCGUCheckbox": "I have read the T&Cs", "ImportQuestionWithAI": "Import exercise(s) by AI", "ImportXlsInfo": "You can import several students at the same time onto your platform using a fillable spreadsheet. You will be able to choose in which groups the students are added at the time of import", "Installments": "Several times", "Invoice": "Bill", "Invoices": "Invoices", "LinkToConditionsOfSales": "Link to general conditions of sale", "LinkToSalesSite": "Link to sales site", "LongAnswer": "Paragraph", "Mandatory": "Mandatory", "ManualAuthorEdition": "Manual Author Editing", "ManualAuthorEditionExplication": "Allows you to manually assign an author when editing an exercise", "MultipleChoice": "Multiple choice", "MultipleChoiceOffer": "Multiple choice", "NoOne": "Person", "NoOneWillSeeThisForfait": "No one will see this offer", "Offers": "Offers", "OffreCocheParDefaut": "Offer checked by default", "OneTime": "Once", "OnlyPeopleInGroupsWillSeeThisForfait": "Only selected groups will see this offer", "Option": "Option", "PaymentAmount": "Payment in installments", "PaymentHistory": "Activity", "PaymentType": "Mode of payment", "PdfPreview": "PDF File Preview", "ProductName": "Product Name", "PromoCode": "Promo code", "PromoCodesAuthorized": "Promo code(s) allowed", "QuestionAuthor": "Author", "QuestionModification": "Edit", "RejectCGUButton": "I refuse the CGU", "Rooms": "Rooms", "Seats": "Seats", "ShortAnswer": "Short answer", "SimpleOffer": "Simple offer", "UniqueChoice": "Single choice", "UniqueChoiceOffer": "Single choice", "UserProperties": "User Properties", "isNotPublishedVisibleByStudents": "Non published", "offreNonDecochable": "Non-removable offer", "payment": {"AdditionnalBillingInfo": "Additional statements at the bottom of the invoice", "EventTypesToAdd": "Types of events to add to the webhook", "HookSecret": "Hook secret (usually starts with whsec_)", "PaymentMethod": "Means of payment", "PaymentMethodDescription": "Set up a payment method here that you want to use for your sales. Payment by Monetico requires contacting ExoTeach support to finalize the configuration.", "PaymentMethodHelp": "Create and configure your payment methods in \"Payment\"", "PaymentName": "Name of payment method", "PrivateKey": "Private key (usually starts with sk_)", "PublicKey": "Public key (usually starts with pk_)", "WebhookUrl": "Webhook URL", "WebhookUrlHelp": "You must add this webhook to your Stripe dashboard for payments to be taken into account.", "paymentMethod": {"monetico": "Monetico", "stripe": "stripe"}, "state": {"cancelled": "Canceled", "paid": "Paid", "unpaid": "Unpaid"}, "type": {"installment": "Several times", "once": "Once"}}, "rooms": "halls", "watermark": {"AxisName": "Placement of {{coordinate}}:", "ColorDescription": "Color :", "CoodonnatesInPercentRule": "The placement on the {{coordonate}} axis must be between 0 and 100%", "CustomDescription": "Personalized phrase:", "InputPageTooBig": "This option requires entering between 1 and the size of your pdf (number of pages: {{maxValue}})", "IsNotEditable": "The course is in PDF format but is not editable (probably password protected).", "IsNotPdf": "The course is not in PDF format. The Watermark Module only supports courses in PDF format", "NeedAtLeastOne": "The Watermark module has been activated, but no options have been chosen. It is necessary to choose at least one.", "NeedCustom": "Watermark with custom phrase was enabled, but no phrase was entered. It is necessary to fill in the sentence.", "NeedInt": "This option requires entering an integer", "NeedPage": "This option requires filling in at least one page", "NeedPicture": "Watermarking on images has been enabled, but no images have been populated. It is necessary to enter at least one image.", "OpacityDescription": "Opacity:", "OpacityValueRule": "Please enter an opacity value between 0 and 1", "PoliceSizeDescription": "Font size :", "PoliceSizeRule": "Please enter a font size between 1 and 100", "PresetBlack": "Black", "PresetBlue": "Blue", "PresetGreen": "Green", "PresetRed": "Red", "RemovePictureFile": "Delete picture", "RotateValueRule": "Please enter a rotation angle between 0 and 360", "RotationDescription": "Rotation :", "WatermarkAtLeastOneCourseForPictureUpload": "You must have selected at least one course to be able to upload an image", "WatermarkDoesNotApply": "The Watermark Module will not operate. Standard download without watermark will take place.", "WatermarkMassOperationWarning": "Only courses in PDF format and not protected will carry the watermark. The other courses will remain downloadable normally.", "WatermarkModule": "Watermark module", "WatermarkPictureFileName": "Image Name:", "applyWatermark": "Watermark module,", "custom": "personalized phrase", "email": "E-mail", "firstName": "First name", "lastname": "Name", "phone": "Phone number", "picture": "Picture", "radioChoice": "Determination of affected pages", "radioChoiceValueAll": "All Pages", "radioChoiceValueChoose": "Only these pages:", "radioChoiceValueOneEach": "A page on:", "showLogDetails": "Details of modifications", "username": "username", "InvalidPictureType": "The image file does not have a valid extension", "ImportFromCourse": "Import settings from another course", "WatermarkExistingTemplateOrNeedCours": "To modify this parameter, you must have selected a course or created the template.", "WatermarkFileOption": "Watermark", "WatermarkParameters": "Watermark settings"}, "Buildings": "Buildings", "Company": "Business", "Payment": "Payment", "Redoability": "Refeasibility", "AddCourses": "Add courses", "AddForumSection": "Add a new forum category", "AddRemoveGroups": "Add/Remove groups to these users", "AllPlannings": "All schedules", "AllowIAToAnswer": "Allow AI to respond", "AreYouSuretoPublishSerie": "Are you sure you want to publish the series?", "Availability": "Availability", "BuildingName": "Name of Building", "ByGroups": "By groups", "ByUsers": "By users", "CORRECTION": "CORRECTION", "ChooseCustomSubjects": "🗂 Choose what to train on", "ChooseRooms": "Choosing rooms...", "Colors": "Colors", "CoursesLinked": "Related courses", "CoursesLinkedToThisEvent": "Courses related to this event", "CreateNewBuilding": "Add a new Building", "CurrentQuestionScale": "Current scale for the year", "DeleteAllParticipants": "Delete all participants", "DeleteThisEvent": "Delete this event", "EditEvent": "Edit event", "EditSerie": "Edit exercise series", "EditSerieInfos": "Edit series information", "EnableEventComments": "Allow comments", "EnabledLanguages": "Enabled languages", "Event": "Event", "EventDiscussion": "Discussion area 💬", "ExamAvailable": "Exam available", "ExplanationDefaultExercice": "When you create a new exercise in this series, this is the type it will automatically take", "FaireAppel": "Make the call", "FilterExercicesSeries": "Filter series", "FilterQcm": {"AddYear": "Add a year", "AllSubjectSelected": "All the subjects", "AllTypes": "All types", "Allyears": "Every year", "CheckAllUe": "Select all", "DeletedExercicesSeries": "Deleted series", "NoSubjectSelected": "No Material", "PerCreationDate": "By creation date", "PerCreator": "By creator", "PerExercicesSeriesTitle": "By series title", "PerLastModifDate": "Last modification", "PerSubject": "By subject(s)", "PerType": "By types", "PerYears": "By years", "RemoveAllUe": "unselect all", "ResetFilter": "Reset", "SearchSerieExercice": "Search for a series...", "SeeMore": "See more", "SelectMySubjects": "Selection of materials", "SelectType": "Select types", "VariableSubjectSelected": "{{numberMaterials}} Subjects", "SelectUePlaceholder": "Select materials"}, "Fixed": "Fixed", "From...": "From...", "GradeConfig": "Scoring Setup", "IfAnAnswer": "If a response", "InfoPromptPrecision": "Giving your AI additional guidance will greatly improve the quality of its responses!", "Join": "Join", "JoinEvent": "Join event link", "KeyWords": "Keywords", "Location": "Place", "Locations": "Places", "NumberOfAnswers": "Number of questions:", "PlanningForAllMyGroups": "Schedule of all groups", "PlanningMode": "Planning mode", "PointsLost": "Points lost", "PromptPrecision": "Prompt precision (optional)", "ResultsHistory": "Results history", "RoomsPlanning": "🏛️ 🪑 Room schedule", "SORTING": "CLASSIFICATION", "SUBJECT": "SUBJECT", "ScaleDefaultSubjectsInfo": "By default when you add an exercise in a subject, a scale is pre-selected. You can still use others. For which subjects do you want this scale to be applied by default?", "SeeEvent": "See the event", "SerieExercices": "Exercise(s) in the series", "SupportsForThisEvent": "Support for this event", "TimeLine": "Timeline", "TimeLineMode": "Timeline mode", "TitleAndDescriptionAndTime": "Title / Description / Timeline", "Users": "Users", "VariableByTotalAnswers": "Variable: by number of total responses", "VariableByTrueAnswers": "Variable: by number of true answers", "YouDidThisSerie": "You have done this set of exercises", "changeModuleLessons": "  Change course modules", "definedPeriod": "Defined Period", "deleteThis": "Delete this forum", "displayResults": "Show final result for students", "eventLocation": "Location of the event", "exerciceFormat": "Exercise Format", "finalExamResults": "Final result", "infosSerie": "Exercise series information", "isChecked": "is checked", "isNotChecked": "is not checked", "masteryLevel": "Mastery level", "possibleErrors": "Possible error(s)", "presence": {"Absent": "Absent", "Present": "Here", "Undefined": "Not defined"}, "remainingTrials": "remaining attempts", "AnswerVerifiedBy": "Answer verified by", "AreYouSatisfiedWithTheAnswer?": "Are you satisfied with the response?", "AskingForHumanHelp": "Human help requested", "GenerateAIAnswer": "Generate an AI response", "MarkAsVerified": "Mark as verified", "NoINeeedHumanAdvice": "No, I want a human opinion", "YesThanks!": "Yes thanks !", "SureOfDuplication": "Duplicate this form ?", "AccessibleContentType": "Type of accessible content", "AccessibleSerieType": "By giving access to a type of series, students will only be able to see them if they have access to the associated subjects", "Action": "Action", "AddAGroup": "Add a group", "AddCustomPlanning": "Add a personalized schedule", "AdminStatPage": {"FilterByGroups": "Filter by groups...", "SearchByCompany": "Filter by companies", "SearchByPseudo": "Find a nickname", "SearchByRole": "Filter by roles"}, "Advanced": "Advance", "AfficherAnnexesSerie": "Show series appendices", "AuthorDeletedConv": "Its author deleted it or it is no longer accessible", "Button": "<PERSON><PERSON>", "CheckAll": "Check all", "CheckBoxDoenstWantCoursesLinkedToExercise": "No associated course", "CheckBoxDoesntWantCoursesLinkedToMyExercise": "No linked courses", "ChooseAnAvatar": "Choose an avatar", "ChooseAnAvatarOrUploadOne": "Choose an avatar or upload one", "ChoosePlannings": "Select schedules", "ChooseYourAvatar": "Choose your avatar", "Combo": "Combo", "ConfigIA": "AI setup", "ConfirmDoesntWantCoursesLinkedToExerciseExplanation": "Not associating courses with exercises disables many features of the platform.", "ConfirmDoesntWantCoursesLinkedToExerciseTitle": "Are you sure ?", "ConvDoesntExistAnymore": "This discussion no longer exists", "Course(Shortcut)": "Course (shortcut)", "CoursesSeen": "Courses viewed", "CreateElementBeforeSettingUpWatermark": "Create the item before you can manage the watermark options.", "CreateNewSerie": "Create a new series", "CustomMyIA": "Customize my AI", "CustomMyIAInfo": "Choose which course the AI intervenes in and which group(s) of students it responds to", "CustomPlanning": "Personalized schedule", "CustomPlanningExplanationLabel": "Explain to the student which date to select for D0", "CustomPlanningExplanationPlaceholder": "When will your Probability Exam take place?", "DeferredPayment": "Deferred payment (check or other)", "DeleteThisExercisePack": "Delete this exercise batch", "DeletedMessage": "Message deleted", "DoneIn": "Finished in", "DoneSingle": "Do", "EditThisExercisePack": "Edit this exercise batch", "EnterCommercialNameCompanyForm": "Please enter the trading name of your company", "EnterCompanyNameForfaitForm": "Please enter company name", "EnterSocialCapitalCompanyForm": "Please provide the company's share capital", "EnterValidEmailCompanyForm": "Please provide a valid email", "ExplainToUserHowToFinishSubscription": "Explain to the user how to complete their registration", "ExplanationDuplicateErrorCompaniesNames": "The import cannot work if there are companies with identical names.", "ExportQcmExoteach": "Export in .exotach format", "ExtraTime": "Third-time", "FilterExercices": {"AllTypes": "All types", "CourseGeneralSelection": "Course link", "FilterExercicesTitle": "Filter exercises", "FilterOnLinkedCourses": "Course:", "Format": "Exercise format", "LinkedToCourses": "Linked to courses", "LinkedToDeletedCourse": "Exercises linked to deleted courses", "LinkedToDeletedCourses": "Linked to deleted courses", "NumberSeriesLinkedEnd": "series", "NumberSeriesLinkedStart": "Exercises present in", "PerCreationDate": "By creation date", "PerCreator": "By creator", "PerId": "By Identifier", "PerLinkedSeries": "By linked series", "PerTitle": "By Title", "Published": "Published", "ResetFilter": "Reset filters", "SeeMore": "See more", "SelectMyCourses": "Filter related courses", "TagAllFormats": "All formats", "TagAllTypes": "All types", "TagDateCreation": "Creation:", "TagDateModif": "Change:", "TagDeletedLinkedCourses": "Course deleted", "TagNoLinkedCourses": "No related courses", "Types": "Types", "Unpublished": "Non published", "WithoutLinkedCourses": "No related courses", "withoutLinkedCours": "Exercises without a linked course"}, "FilterQuestions": {"Actions": "Actions", "AiAnswered": "Answered by AI", "AllDiscussTypes": "All discussions", "AllExerciseTypeRootComponant": "All types of exercises", "AllPostTypeTag": "All types of discussions", "AllQuestionTypeRootComponant": "All question types", "AllTypes": "All types", "AskingHumanHelp": "Request human help", "Category": "Category", "CheckAllCourses": "Select all", "Cours": "Course", "CreationDate": "Creation date", "Creators": "Creator(s)", "FilterPerAssociatedContent": "Filter by related content type", "FilterPerAssociatedExerciseType": "By type of associated exercise", "FilterQuestions": "Filter questions", "FilterQuestions.ResultNumber": "Number of questions", "Forum": "Forum", "GlobalPlateFormContentFilter": "Global filter on the platform", "LastFeedback": "Latest feedback", "LastFeedbackExplanation": "Shows the last answer posted by an Admin, Tutor or AI", "NotAiAnswered": "Not answered by AI", "NotAskingHumanHelp": "Don't ask for human help", "PerAiResolved": "Solved by AI", "PerAiResponse": "Answered by AI", "PerCategories": "By categories", "PerCategory": "By categories", "PerCourses": "Course)", "PerCreationDate": "Date", "PerCreator": "By creator", "PerDiscussType": "By type of discussion", "PerExerciseType": "By type of exercise", "PerHumanHelp": "Human Need", "PerQuestionContent": "By question content", "PerQuestionType": "By question type", "PerResolved": "Resolved", "PerTitle": "By question title", "PostTypeCOURS": "COURSE", "PostTypeEVENT": "EVENT", "PostTypeFORUM": "FORUM", "PostTypeQCM": "EXERCISE", "PostTypeQUESTION_ANSWER": "EXERCISE", "ResetFilter": "Reset", "ResolvePost": "<PERSON> as <PERSON><PERSON>", "Resolved": "Resolved", "ResolvedByAi": "Solved By AI", "ResultNumber": "Number of questions", "SearchQuestionContent": "Search for a question", "SearchTitle": "Search for a title", "SeeMore": "See more", "SelectAllCategories": "All content types", "SelectMyCourses": "Course selection", "SelectedCourses": "Selected courses", "State": "State", "UnResolvePost": "<PERSON> as unresolved", "UnResolved": "Unresolved", "UnResolvedByAi": "Unresolved by AI", "UncheckAllCourses": "unselect all", "WarningExerciseFilterMultipleCategoriesAndExerciseFiltration": "Please note, you are filtering on the types of exercises but several categories have been selected. Only questions associated with the selected exercises will be displayed", "WarningExerciseFilterNoCategoriesAndExerciseFiltration": "Please note, if you filter on exercise types without having selected the Exercise category, nothing will be displayed. Please select the Exercise category"}, "FinishTraining": "Complete the workout", "FormAlreadyAnswered": "You have already responded to this form", "FormElements": "Form elements", "Forms": "Forms", "GetGroupIdAndCompanyIdHelper": {"CompaniesMultiSelectPlaceholder": "Select the company whose ID you want to obtain", "CompaniesToAttribute": "Which company would you like to assign to?", "ComponentName": "Help finding group/company IDs", "CopyToClipBoard": "Copy to clipboard", "FailCopyClipboard": "Co<PERSON> failed", "GroupsMultiSelectPlaceholder": "Select the groups for which you want identifiers", "GroupsToAttribute": "Which groups do you want to assign?", "IdOfYourCompany": "The identifier of your selection", "IdsOfYourGroups": "The identifiers of your selection:", "SelectionIsEmpty": "∅", "SuccessfullyCopiedIntoTheClipBoard": "Copied!"}, "Hidden": "Mask", "HidePrice": "Hide total price", "Id": "Identifier", "IgnoreThisExercise": "Skip this exercise", "ImportQcm": {"AnalysedCourses": "Courses detected", "AnalysedUe": "Material detected", "CheckFile": "Check file", "ChooseMappedExercisesTypes": "Choose the associated types of exercises", "ChooseScalePlaceholder": "Select a scale", "ConfigureExercises": "Exercise Setup", "ConfigureQcm": "Configure the series", "ConfigureQcmExplanation": "For each element, select its correspondence on your platform", "ConfirmModifications": "Confirming changes", "ExerciseType": "Type of exercise", "ExercisesNumber": "Number of exercises", "FileNotGoodExtension": "The file is not in the correct format (expected format: *.exotach)", "FileSelection": "File selection", "FileSelectionExplanation": "To import a series from another platform, export the series and import the .exotach file", "FileToImportExoteach": "file to import (*.exotach)", "ImportButton": "Import !", "ImportCourses": "Importing courses", "ImportQcmModalDescription": "Description", "ImportQcmModalTitle": "Title", "ImportTitleFormationElement": "Importing the 'Title' training element", "MappingCoursNotGood": "Please complete the associated courses", "MappingScaleNotGood": "Please complete the choice of scales", "MappingTitleNotGood": "Please complete the associated title training elements", "MappingUeNotGood": "Please complete the choice of material on your EU", "ModalTitle": "Import a series", "NewDescription": "New description", "NewTitle": "New title", "Next": "Following", "OpenModalImportQcm": "Import a series", "QcmDefaultExerciseType": "Default question type in the series", "QcmDefaultExerciseTypePlaceholder": "Choose the default types of exercises in the series", "QcmTypeTitle": "Series Type", "QcmTypeTitlePlaceholder": "Choose series types", "RecapQcm": "Summary", "ScaleTitle": "Scales", "SelectCoursesPlaceholder": "Matching course on your platform", "SelectUePlaceholder": "Corresponding material on your platform", "TitleAndDescription": "Title and description", "configureCourses": "Course setup"}, "InUserEditExtraTimeExplanation": "Check this box if the user benefits from additional part-time", "IsUserExtraTime": "Is the user third-time?", "LimitationQuestionLayer": {"Activate": "Enable", "Days": "days)", "ExplanationUserDeactivated": "You cannot ask questions at this time.", "ExplanationUserWithLimite": "You have reached the question limit at this time.", "Hours": "hours)", "LeftTimeBeforeNewQuestion": "Time remaining before you can ask a question:", "LimitDiscussionExplanation": "This option allows you to limit the number of discussions that a learner can initiate over a rolling period.", "LimitDiscussionTitle": "Limit discussions", "LimitNumberNewDiscussion": "Max number of discussions", "Minutes": "minutes)", "NewDiscussionExplanation": "Controls the number of new discussions the learner can initiate over the defined time period. If set to 0, then the user cannot create discussions", "NextQuestionSentenceWithTimer": "You will be able to ask questions again in {{n_days}} days, {{n_hours}} hours, {{n_minutes}} minutes, {{n_seconds}} seconds.", "Preferences": "Preferences", "PreferencesOf": "Preferences of", "Seconds": "second(s)", "TimeWindow": "Rolling period", "Update": "To update !"}, "LinkCoursesToExerciseSelecterPlaceholder": "Write the name of the course to search", "MarkAsUnVerified": "Mark as unverified", "MessageError": "Error during user recovery", "MobileApp": "Mobile app", "MobileAppHomePage": "Mobile app homepage", "NewFolder": "New folder", "NewGroup": "New group", "NewGroupConversation": "New group chat", "News": "News", "NoExercisesFound": "No exercises found with these settings", "NoMessage": "No message", "NotificationContent": "Content of the notification", "NumberOfCoursesLinkedToTheExercise": "Number of courses selected:", "NumberOfExercises": "Number of exercises:", "NumberOfSelectedCoursesMassOperation": "Number of courses selected:", "Only...": "Uniquely...", "OpenAIKey": "OpenAI key (chatGPT)", "OrderConfirmation": "confirmation of your order", "PasswordTooltip": "The password will be used to connect to the platform", "PayByInstallments": "Pay in multiple times", "PayLater": "Pay later", "PayTotal": "Pay the total", "Planification": {"AddPlanification": "Add a schedule", "ExecutionDate": "execution date", "NoPlanification": "No schedules", "PermissionsAtExecutionDate": "Permissions on execution date", "Planification": "Planning", "Planifications": "Planning", "Title": "Schedule title"}, "PredefinedUserField": "User predefined field", "ProgressAverage": "Average progress", "ProgressionRadarGraph": {"AverageGrade": "Overall average", "CanChooseSelection": "Customize the analysis", "CheckAll": "Check all", "Date": "Date", "DoMySelection": "Make my selection", "GraphSelectedUEs": "Materials", "NoResults": "No notes", "Note": "Note", "QuestionsDone": "Exercises Completed", "ResultsByCategories": "Results by chapters", "ResultsOverall": "Results", "SelectBinDay": "Day", "SelectBinMonth": "Month", "SelectBinWeek": "Week", "SelectBinYear": "Year", "SelectHistoGraph": "Histogram", "SelectRadarGraph": "Radar", "SelectedCategories": "Categories", "SelectedElements": " Selected items", "ShowMyResults": "My progress", "ShowStructureResults": "Overall average", "StructureMoyenne": "Overall average", "StructureNumber": "Total exercises completed", "TooMuchSelection": "The selection is limited to {{nb_max}} elements", "UncheckAll": "Uncheck all", "UserMoyenne": "User average", "UserNumber": "Exercises carried out by you", "YourGrade": "Your average"}, "QuestionsLinkedToDeletedCourses": "Exercises linked to deleted courses", "QuickAccess": "Quick access", "Rank": "Rank", "ReadMore": "Read more", "RegisterFields": "Registration fields", "Responsability": "Responsibilities", "ResponsibilityGroups": "Groups", "ResponsibilityUsers": "Users", "Review": {"AdminDeletConfirmation": "Do you confirm the deletion? The operation is irreversible.", "CancelSubmitReview": "Cancel", "DeleteReview": "DELETE", "EnableCoursReview": "Enable course grading", "EnablePublicReview": "Reviews visible to all", "GiveMark": "You must enter a note", "Hide": "hide", "LastUpdate": "edited", "LeaveReview": "Leave a review", "LeaveReviewButton": "Leave a review", "Lines": "Lines", "Modify": "to modify", "MyReview": "My opinion", "PlaceholderInputReview": "What did you think of this course?", "PublishReview": "Publish my opinion!", "RefreshToDelet": "Reload page to refresh", "ReviewNumber": "Notice", "Reviews": "The opinions", "Show": "display", "UpdateReview": "Edit my review!"}, "SeeOriginalExerciceSerie": "See exercise series", "SelectSubjectsToTrainOn": "Select the subject(s) to practice", "SeriesDone": "Series made", "ShowInNews": "Show in news", "SmallTagErrorInvalidCompanyId": "Invalid business", "StatsGraphTraductions": {"AllDownloadedFiles": "Downloaded files", "AllSeenCourses": "Course viewed", "Day": "Day", "DownloadedFileMedian": "Files downloaded (median)", "ExcelCompanies": "Businesses", "ExcelGeneralProgress": "General progress", "ExcelGroups": "Groups", "ExcelPeriodeProgress": "Progress over the period", "ExcelPostsSent": "Messages posted", "ExcelUniqueSeenClasses": "Unique courses seen", "FilterPerCourses": "Filter by course", "LaunchFetch": "Start a reseach !", "Month": "Month", "NumberOfSelectedCourses": "({{n}} selected courses)", "PeriodeBatchProgress": "Progress over period (average)", "PostsSent": "Messages posted", "ProgressGeneral": "General progress (average)", "ProgressionMedian": "Progress over period (average)", "ResetFilter": "Reset filter", "TotalSeeableClasses": "Courses available", "UniqueClassesForBatch": "Single course available", "Week": "Week", "Year": "Year", "activeUsers": "Active User", "allSeenClasses": "Course viewed", "downloadedFiles": "Downloaded files", "exercisesDone": "Exercises performed", "postsSent": "Posts sent", "uniqueSeenClasses": "Unique courses seen"}, "TemplateModule": {"ColumnWatermarkActions": "Actions", "ColumnWatermarkDescription": "Description", "ColumnWatermarkName": "Name", "CreateNewWatermarkTemplate": "Create a new watermark template", "GeneralAdminPageTitle": "Templates and templates", "ImportWatermarkTemplate": "Import !", "ImportWatermarkTemplatePlaceholder": "Import from a saved template", "SubmitCreateWatermarkTemplate": "Create the template!", "SubmitUpdateWatermarkTemplate": "Update the template!", "TemplateWatermarkActionButtonDelete": "Delete template", "TemplateWatermarkActionButtonModif": "Edit template", "TemplateWatermarkDeleteConfirm": "Are you sure you want to delete?", "TemplateWatermarkOperationFail": "Operation failed.", "TemplateWatermarkOperationLoading": "Operation in progress...", "TemplateWatermarkOperationValidation": "Successful operation !", "TemplateWatermarkValidationFieldsError": "Field validation failed. Contact your administrator", "WatermarkInputDescription": "Description of the template", "WatermarkInputName": "Template name", "WatermarkModalTitleCreateTemplate": "Create a watermark template", "WatermarkModalTitleUpdateTemplate": "Edit a watermark template", "WatermarkTemplateImportNotif": "Imported watermark setting!"}, "Theme": "Theme", "TitleDiscussionsInCourse": "{{number}} discussions on {{courseName}}", "Training": "Training", "TutorFromUeHasBeenUpdateConfirmation": "The manager's materials have been updated", "TutorFromUeHasUpdateFail": "Failed to update manager materials", "TypeWithEmoji": "🗂Type", "UncheckAll": "Uncheck all", "UpdateExerciseItNeedsCoursesOrCheckbox": "Please select the linked course(s) before saving", "PleaseFillAllAnswers": "Please fill all TRUE/FALSE", "UpdateGeneral": "Update general tab", "UpdateModule": "Update module tab", "UserDoesNotExist": "The user does not exist", "UserIsResponsibleForThosesSubjects": "Responsible for the following matters...", "UserMassChange": {"AdminReplaceConfirmation": "Do you confirm the replacement? The operation is irreversible.", "ButtonUpdateTypeAdd": "Add", "ButtonUpdateTypeRemove": "To remove", "ButtonUpdateTypeReplace": "Replace", "ChooseElementsToModify": "Items to modify:", "Companies": "Businesses", "ConfirmCheckBox": "I confirm", "ElementTarget": "Item to modify", "Groups": "Groups", "ModifTypeInvalid": "The modification type argument is invalid", "ModificationType": "Type of modification", "ReplaceExplanation": "The current selection configuration will be cleared and only the items you specified will be added.", "SelectCompaniesToApply": "Select companies to apply", "SelectGroupsToApply": "Select groups to apply", "SelectGroupsToModify": "Select groups to edit", "SelectIsEmpty": "Please choose groups or users", "SelectUsersToModify": "Select users to edit", "TargetTypeInvalid": "The change target is invalid", "UserMassChangeSelectername": "Mass operation on students"}, "UsernameTooltip": "Your identifier is a nickname that will allow you to connect to the platform. It will be visible to other users", "ValidateMyChoices": "Validate my choices", "ValidateThisExercisePack": "Validate this batch of exercises", "ValidesCompaniesNames": "Valid businesses:", "WhoCanUseThisCustomPlanning": "Who can use this personalized schedule?", "WriteTo": "Write to", "WriteUeToLinkToTutor": "Write the name of the material to associate", "YouCantAnswerFormAgain": "You can't start it again. If you think this is an error, contact your teachers.", "YouMustChooseOpenAIKey": "You need to link your AI to an OpenAI key", "backToHome": "Back to Home", "desiredExercises": "desired exercises", "exercisesFound": "exercises found", "globalAnnouncesForParents": "Announcements for parents", "onlyForParentsWarning": "Please note, this user is not a Parent. You can change its role in general details.", "responsabilityExplanation": "By specifying the user's responsibilities, this will allow him to access the information and schedules of the users for whom he is responsible.", "responsabilityGroupExplanation": "By specifying the responsibilities of the group, this will allow all its members to access the information and schedules of the users for whom they are responsible.", "ReorderElements": {"Name": "Reorder Elements", "Description1": "Drag and drop to reorder elements", "Description2": "Scoring based on correct positions", "ConfigurationTitle": "Configuration of \"Reorder Elements\" exercise", "ConfigurationSubtitle": "Define the correct order of elements", "AddElement": "Add element", "ElementPlaceholder": "Content of element {{index}}...", "DeleteConfirmTitle": "Delete this element?", "DeleteConfirmDescription": "This action is irreversible.", "DeleteConfirmYes": "Yes", "DeleteConfirmNo": "No", "InstructionsTitle": "Instructions:", "InstructionsText": "Define the elements in the correct order. Students will need to reorder them. Drag and drop to reorganize.", "MinElementsWarning": "At least 2 elements are required to create a reorder exercise", "ConfigurationValid": "Valid configuration! {{count}} elements defined in correct order.", "ElementImage": "Element {{index}} image", "ImageUploadSuccess": "Image uploaded successfully", "ImageDeleteSuccess": "Image deleted successfully", "ImageUpdateError": "Error updating image", "DeleteElementTooltip": "Delete this entire element", "DeleteImageTooltip": "Delete image only", "DragStart": "Moving element {{id}}", "DragOver": "Element {{activeId}} is over {{overId}}", "DragOverNone": "Element {{activeId}} is no longer over an element"}}