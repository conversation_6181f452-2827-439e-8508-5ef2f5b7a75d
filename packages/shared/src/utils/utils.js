import dayjs from 'dayjs';
import { parse } from 'querystring';
import pathRegexp from 'path-to-regexp';
import { Badge, message, notification } from 'antd';
import React from 'react';
import { getAuthToken } from '@/shared/utils/authority';
import defaultSettings from '@/../config/defaultSettings';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

// dependent on utc plugin
dayjs.extend(utc);
dayjs.extend(timezone);

export const toInt = (variable) => {
  if (variable) {
    return parseInt(variable, 10);
  }
  return 0;
};

export const IS_DEV = REACT_APP_ENV === 'dev';
export const DATE_FORMATS = {
  FULL_FR: 'DD/MM/YYYY HH:mm'
};
// eslint-disable-next-line no-undef
export const isMobile = MOBILE == 1;

/**
 * Get Price TTC from price HT (returns a string)
 * @param tvaPercent
 * @param priceHt
 * @returns {number}
 */
export const getPriceTTC = (tvaPercent, priceHt) => {
  if (priceHt === '' || priceHt === null || isNaN(priceHt)) {
    return 0;
  }
  return parseFloat(priceHt * (1 + tvaPercent / 100));
};
export const getPriceHT = (tvaPercent, priceTTC) => {
  if (priceTTC === '' || priceTTC === null || isNaN(priceTTC)) {
    return 0;
  }
  // Augmentez la précision en évitant de forcer l'arrondi trop tôt
  const priceHtPrecise = priceTTC / (1 + tvaPercent / 100);
  // Forcez l'arrondi à deux décimales seulement à la fin
  return parseFloat(priceHtPrecise);
};

// TODO move to global context (so it can be retrieved by api)
export const FEATURE_FLAGS = {
  EnableImportedCourses: true
};

export const getAntdHexColorIfExists = (color) => {
  // For new picker
  if (color?.toHexString) {
    return color?.toHexString();
  }
  return color;
};

export const TimeZoneManager = {
  getDefaultTimeZone: () => dayjs.tz.guess(),
  getLocalStorageTimeZoneKey: () => 'exoteach-selected-timezone-key'
};

/* Global Config */
const SELECTED_PLATFORM_STORAGE_KEY = 'selectedPlatform';
const SELECTED_PLATFORM_BOOLEAN_KEY = 'hasSelectedPlatform';
export const GlobalConfig = {
  get: () => {
    if (!isMobile) {
      // eslint-disable-next-line no-undef
      const configs = { ...CONFIGS };

      // Setup helpers url for platform
      configs.FILES_URL = `${configs.serverUrl}files/`;
      configs.FICHES_URL = `${configs.serverUrl}files/fiche/`;
      configs.COURS_URL = `${configs.serverUrl}files/cours/`;
      configs.BILLS_URL = `${configs.serverUrl}files/bill/`;
      configs.PIXIES_URL = `${configs.serverUrl}files/pixies`; // C'est normalement le folder mathpix, mais on obfusce pour cacher le nom mathpix
      configs.S3FILES_URL = `${configs.serverUrl}s3File/`;
      configs.AVATARS_URL = `${configs.serverUrl}avatars/`;
      configs.TEMP_URL = `${configs.serverUrl}files/temp/`;
      configs.PUBLIC_URL = configs.serverUrl;
      configs.SCORM_URL=`${configs.serverUrl}scorm/`;
      return configs;
    }
    const storedConfig = GlobalConfig.getStoredPlatformId();

    //****** Update temporelle du 29/04 => Injecte la route REST S3_FILE dans une config mobile déjà existante et valide ( valide = route FILES_URL Existe déjà) pour mobile
    if (storedConfig) {
      if (storedConfig.hasOwnProperty('FILES_URL') && !storedConfig.hasOwnProperty('S3FILES_URL')) {
        storedConfig.S3FILES_URL = `${storedConfig.serverUrl}s3File/`;
      }
      if (storedConfig.hasOwnProperty('FILES_URL') && !storedConfig.hasOwnProperty('TEMP_URL')) {
        storedConfig.TEMP_URL = `${storedConfig.serverUrl}files/temp/`;
      }
    }
    //*******

    if (!storedConfig) {
      // No default truc
      // eslint-disable-next-line no-undef
      return GlobalConfig.setStoredPlatformId(CONFIGS); // default config,
    }
    return storedConfig;
  },
  set: (config) => {
    GlobalConfig.setStoredPlatformId(config);
  },
  reloadPage: () => {
    window.location.reload(true);
    return false;
    // window.location.href = window.location.href;
  },

  setHasSelectedPlatform: (value) => {
    return localStorage.setItem(SELECTED_PLATFORM_BOOLEAN_KEY, JSON.stringify(!!value));
  },
  hasSelectedPlatform: () => {
    return JSON.parse(localStorage.getItem(SELECTED_PLATFORM_BOOLEAN_KEY));
  },

  getStoredPlatformId: () => {
    try {
      const confString = localStorage.getItem(SELECTED_PLATFORM_STORAGE_KEY);
      if (confString === null) {
        return null;
      }
      const confJson = JSON.parse(confString);
      if (!confJson.serverUrl) {
        return CONFIGS;
      }
      return JSON.parse(confString) || null;
    } catch (e) {
      return null;
    }
  },
  // Save backend configuration
  setStoredPlatformId: (c) => {
    const configs = { ...c };
    // Setup helpers url for platform
    configs.FILES_URL = `${configs.serverUrl}files/`;
    configs.FICHES_URL = `${configs.serverUrl}files/fiche/`;
    configs.COURS_URL = `${configs.serverUrl}files/cours/`;
    configs.BILLS_URL = `${configs.serverUrl}files/bill/`;
    configs.TEMP_URL = `${configs.serverUrl}files/temp/`;
    configs.PIXIES_URL = `${configs.serverUrl}files/pixies`; // C'est normalement le folder mathpix, mais on obfusce pour cacher le nom mathpix
    configs.S3FILES_URL = `${configs.serverUrl}s3File/`;
    configs.AVATARS_URL = `${configs.serverUrl}avatars/`;
    configs.PUBLIC_URL = configs.serverUrl;
    localStorage.setItem(SELECTED_PLATFORM_STORAGE_KEY, JSON.stringify(configs));
    return configs;
  }
};

/* End Global Config */

/* eslint no-useless-escape:0 import/prefer-default-export:0 */
const reg =
  /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;
export const isUrl = (path) => reg.test(path);

export const scrollToRef = (ref) =>
  window.scrollTo({ behavior: 'smooth', top: ref?.current?.offsetTop, left: 0 });
export const DEFAULT_SPIN_DELAY = 500;

// For images
export const getUrlProtectedRessource = (ressource) => `${ressource}?tk=${getAuthToken()}`;
export const getPageQuery = () => parse(window.location.href.split('?')[1]);

export const alphabetArray = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z'
];
export const alphabetAndNumbersArray = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
  '0',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '00',
  '10',
  '20',
  '30',
  '40',
  '50',
  '60',
  '70',
  '80',
  '90',
  '000',
  '100',
  '200',
  '300',
  '400',
  '500',
  '600',
  '700',
  '800',
  '900'
];
export const numericalArrayForCombinations = [
  '0',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '00',
  '10',
  '20',
  '30',
  '40',
  '50',
  '60',
  '70',
  '80',
  '90',
  '000',
  '100',
  '200',
  '300',
  '400',
  '500',
  '600',
  '700',
  '800',
  '900'
];

/**
 * props.route.routes
 * @param router [{}]
 * @param pathname string
 */
export const getAuthorityFromRouter = (router = [], pathname) => {
  const authority = router.find(
    ({ routes, path = '/' }) =>
      (path && pathRegexp(path).exec(pathname)) ||
      (routes && getAuthorityFromRouter(routes, pathname))
  );
  if (authority) return authority;
  return undefined;
};
export const getRouteAuthority = (path, routeData) => {
  let authorities;
  routeData.forEach((route) => {
    // match prefix
    if (pathRegexp(`${route.path}/(.*)`).test(`${path}/`)) {
      if (route.authority) {
        authorities = route.authority;
      } // exact match

      if (route.path === path) {
        authorities = route.authority || authorities;
      } // get children authority recursively

      if (route.routes) {
        authorities = getRouteAuthority(path, route.routes) || authorities;
      }
    }
  });
  return authorities;
};

export const getCurrentISODate = () => new Date().toISOString();

export const truncateText = (input, maxLength = 300) =>
  input.length > maxLength ? `${input.substring(0, maxLength)}...` : input;

// TODO refactor (no css here)
export const cardHeadStyle = {
  background: defaultSettings.colorCard,
  textAlign: 'center',
  fontSize: '1.48em',
  color: 'white'
};

export const getLinearGradientBackgroundValue = (color1, color2) =>
  `linear-gradient(90deg, ${color1} 0%, ${color2} 100%) no-repeat 50% 50%`;
export const getColoredUEHeadStyle = (
  color1 = defaultSettings.colorCard,
  color2 = defaultSettings.colorCard
) => {
  /*
  if(!color1) {
    color1 = defaultSettings.colorCard;
  }
  if(!color2) {
    color2 = defaultSettings.colorCard;
  }
  */
  return {
    backgroundColor: color1,
    textAlign: 'center',
    fontSize: '1.5em',
    color: 'white'
  };
};

export const getColoredUEButtonStyle = (color1, color2) => ({
  background: getLinearGradientBackgroundValue(color1, color2),
  borderColor: color1,
  textAlign: 'center',
  // fontSize: '1.5em',
  color: 'white'
});

export const cardHeadStyleHome = {
  background: defaultSettings.colorCard,
  textAlign: 'center',
  fontSize: '1.5em',
  color: 'white'
};

export const cardHeadStyleEDT = {
  background: '#00ced1',
  textAlign: 'center',
  fontSize: '1.5em',
  color: 'white'
};

export function renderBadge(value) {
  return <Badge style={{ boxShadow: 'none' }} count={value} />;
}

function createMarkup(value) {
  return { __html: value };
}

export function capitalizeFirstWord(str) {
  if (!str || typeof str !== 'string') {
    return '';
  }

  const words = str.split(' ');
  words[0] = words[0].charAt(0).toUpperCase() + words[0].slice(1);
  return words.join(' ');
}

export function displayDirectHtml(value) {
  // eslint-disable-next-line react/no-danger
  return <span dangerouslySetInnerHTML={createMarkup(unescape(value))} />;
}

export function removeEmptyHtmlTags(htmlString) {
  let result = htmlString;
  result = result.replace(/<(\w+)(\s*[^>]*)><\/\1>/g, '');
  result = result.replace(/^(<p>\s*<br\s*>\s*<\/p>\s*)+|(\s*<p>\s*<br\s*>\s*<\/p>)+$/g, '');

  return result;
}

export const stripHtml = (htmlString) => {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlString;
  return tempDiv.textContent || tempDiv.innerText || '';
};

// TODO better could be done in CSS
export const addReturnToHtmlText = (v) => v?.replace(new RegExp('\r?\n', 'g'), '<br />');

export const NOTIFICATION_KEY_ERROR = 'nke';

// 'success' 'info' warning error
export const openNotificationWithIcon = (type, message, description = undefined, key = null) => {
  notification[type]({
    message,
    description,
    key
  });
};

export const scrollTop = () => window.scrollTo(0, 0);

export const showGqlErrorsInMessagePopupFromException = (exception, key = 'update') => {
  exception &&
    exception.graphQLErrors &&
    exception.graphQLErrors.map(({ message: msgException }) =>
      message.error({ content: msgException, key })
    );
};

export const getUniqueListBy = (arr, key) => {
  return [...new Map(arr?.map((item) => [item[key], item]))?.values()];
};

export const setMyDashboardVisibility = (v) => {
  localStorage.setItem('myDashBoardVisibility', JSON.stringify(v));
};
export const getMyDashboardVisibility = () => {
  if (localStorage.getItem('myDashBoardVisibility') === null) {
    setMyDashboardVisibility(true);
    return true;
  }
  return JSON.parse(localStorage.getItem('myDashBoardVisibility') || null) || null;
};

export const setNotionsInTextEnabled = (v) => {
  localStorage.setItem('notionsInText', v);
};
export const getNotionsInTextEnabled = () => {
  /*
  if (localStorage.getItem('notionsInText') === '1') {
    return true
  }
  return false
   */
  return true;
};

export const setFrontendActualVersion = (v) =>
  localStorage.setItem('actualVersion', JSON.stringify(v));
export const getFrontendActualVersion = () => localStorage.getItem('actualVersion');

// TODO removed not used anymore???
export const loginToPhpApps = async (jwt) => {
  // eslint-disable-next-line no-undef
  const rest = await fetch(
    `${GlobalConfig.get().baseWebsiteUrl}qcm/sessionfromjwt.php?jwt=${jwt}`,
    { mode: 'no-cors' }
  );
  return rest;
};

// eslint-disable-next-line no-undef
export const isAptoria = GlobalConfig.get().appName === 'Aptoria' || IS_DEV;
export const isDiploma =
  GlobalConfig.get().appName === 'Diploma' || GlobalConfig.get().appName === 'ENSAO';
export const isTestServer = GlobalConfig.get().appName === 'passlastonannee';
// eslint-disable-next-line no-undef
export const isMedisupPPS =
  [
    'Medisup PPS',
    'Médisup',
    'Medisup JPO',
    'passlastonannee' // autorisé sur serveur de TEST
  ].includes(GlobalConfig.get().appName) || IS_DEV; // Permettre en local

// eslint-disable-next-line no-undef
export const isAntemed = GlobalConfig.get().appName === 'Antemed';

export const AptoriaQcm = {
  QCM_ID_DIAGNOSTIC_1: 9,
  QCM_ID_DIAGNOSTIC_2: 10,

  QCM_gestion_ACTIFS: 20,
  QCM_MARCHES_FINANCIERS: 21,
  QCM_PRODUITS_FINANCIERS: 22,
  QCM_REGLEMENTATION: 23,
  QCM_RELATION_CLIENT: 24,
  QCM_ENVIRONNEMENT: 18
};

/**
 * If you don't care about primitives and only objects then this function
 * is for you, otherwise look elsewhere.
 * This function will return `false` for any valid json primitive.
 * EG, 'true' -> false
 *     '123' -> false
 *     'null' -> false
 *     '"I'm a string"' -> false
 */
export function tryParseJSONObject(jsonString) {
  try {
    const o = JSON.parse(jsonString);
    // Handle non-exception-throwing cases:
    // Neither JSON.parse(false) or JSON.parse(1234) throw errors, hence the type-checking,
    // but... JSON.parse(null) returns null, and typeof null === "object",
    // so we must check for that, too. Thankfully, null is falsey, so this suffices:
    if (o && typeof o === 'object') {
      return o;
    }
  } catch (e) {}
  return false;
}

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE'
};

// siteKey de reCAPTCHA API
export const recaptchaSiteKey = '6LencBkmAAAAAOkXbYe4_iGw8Jsw0Xf6frObnXVa';

// Page où sont localisées les CGU :
export const WebSiteCGU = 'https://www.exoteach.com/cgu/';

///// fonction très cool qui affiche les erreures en "onError" des queries/mutations
export const onErrorShowErrorsFunction = ({ graphQLErrors = [], networkError }, duration = 5) => {
  /* petite fonction qui permet d'afficher les erreures de useQuery d'une requête dans son hook 'onError'  */
  // Pour mutation utiliser comme ceci : useMutation(MUTATION_DELETE_MCQ_SCALE,{onError:(error)=>onErrorShowErrorsFunction(error)});
  // Pour Query peut s'utiliser comme ceci : onError: onErrorShowErrorsFunction,
  const errorArray = [...(graphQLErrors || []), ...(networkError ? [networkError] : [])];
  for (const e of errorArray) {
    const notifConfig = {
      description: e?.message || 'error not defined',
      duration,
      placement: 'topRight'
    };

    notification.error(notifConfig);
  }
};

export function uuidv4() {
  /* fonction qui permet de générer un uuidv4 dans le front */
  const bytes = crypto.getRandomValues(new Uint8Array(16));
  bytes[6] = (bytes[6] & 0x0f) | 0x40; // Version 4
  bytes[8] = (bytes[8] & 0x3f) | 0x80; // Variant 10xx
  const id = [...bytes].map((b) => b.toString(16).padStart(2, '0')).join('');
  return `${id.substring(0, 8)}-${id.substring(8, 12)}-${id.substring(12, 16)}-${id.substring(16, 20)}-${id.substring(20)}`;
}

export const shouldShowMobileNavbarFct = (pathname) => {
  const pathsToHideNavbar = [
    '/discussions/post',
    '/qcm/faireqcm',
    '/generateurqcm/do',
    '/messages/conversation/',
    '/event/',
    '/exam/',
    '/qcm/',
    '/cours/',
    '/admin/',
    '/planning'
  ];

  return !pathsToHideNavbar.some((path) => {
    if (path === '/cours/') {
      // Exemple /cours/5
      // Doit marcher pour tous integer après /cours/
      // Ne doit pas être triggered pour /cours/ue/14 par exemple, uniquement /cours/5
      const regex = /^\/cours\/\d+$/;
      return regex.test(pathname);
    }
    return pathname.startsWith(path);
  });
};
