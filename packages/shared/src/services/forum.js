import i18n from '@/shared/i18n.js';
import React, { useContext } from 'react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils';

export const ForumType = {
  QCM: 'qcm',
  COURS: 'cours',
  ANNALE: 'annales',

  EVENT: 'event',

  FORUM: 'forum',
  MATIERES: 'matieres',
  UE: 'ue',
  UE_QCM: 'ue_qcm',
  CHOIX_COURS_QCM_ANNALE: 'choix-matiere',

  UECategory: 'ue-categorie',
  UECategoryQCM: 'ue-categorie-qcm'
};

const DEFAULT_SLICING_CARACTERS_NUMBER_FORUM = 34;

export const sliceLastMessage = (text) => {
  if (text) {
    if (text.length > DEFAULT_SLICING_CARACTERS_NUMBER_FORUM) {
      return `${text.slice(0, DEFAULT_SLICING_CARACTERS_NUMBER_FORUM)}...`;
    }
    return text;
  }
  return '';
};

export const renderPostParentName = (post) => {
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;
  const primaryTransparent = `${primaryColor}a0`;

  const cours = post?.cours;
  const qcm = post?.qcm;
  const forum = post?.forum;
  const translateIn = i18n.t('In');
  if (cours && cours?.name) {
    return (
      <>
        <span
          style={{
            fontWeight: 100,
            marginRight: 4
          }}
        >{`${translateIn} ${i18n.t('course')} :`}</span>
        <span
          style={{
            background: primaryTransparent,
            color: 'white',
            padding: '0 8px',
            borderRadius: 12
          }}
        >
          {cours?.customImage ? (
            <img
              src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + cours.customImage)}
              style={{ maxHeight: 12, marginRight: 4 }}
              alt="aperçu"
            />
          ) : cours?.ueCategory?.ue?.image ? (
            <img
              src={getUrlProtectedRessource(
                GlobalConfig.get().FILES_URL + cours?.ueCategory.ue.image
              )}
              style={{ maxHeight: 12, marginRight: 4 }}
              alt="aperçu"
            />
          ) : null}
          {`${cours?.ueCategory?.ue?.name ? `${cours?.ueCategory?.ue?.name} / ` : ''}${cours?.name}`}
        </span>
      </>
    );
  }
  if (qcm && qcm?.titre && qcm?.UE && qcm?.UE?.name) {
    return (
      <>
        <span
          style={{ fontWeight: 100, marginRight: 4 }}
        >{`${translateIn} ${i18n.t('MCQ')} :`}</span>
        <span
          style={{
            background: primaryTransparent,
            color: 'white',
            padding: '0 8px',
            borderRadius: 12
          }}
        >
          {qcm?.UE?.image && (
            <img
              src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + qcm.UE.image)}
              style={{ maxHeight: 12, marginRight: 4 }}
              alt="aperçu"
            />
          )}
          {`${qcm.UE?.name ? `${qcm.UE.name} / ` : ''}${qcm.titre}`}
        </span>
      </>
    );
  }
  if (forum && forum.name) {
    return (
      <>
        <span style={{ fontWeight: 100, marginRight: 4 }}>{`${translateIn} `}</span>
        <span
          style={{
            background: primaryTransparent,
            color: 'white',
            padding: '0 8px',
            borderRadius: 12
          }}
        >
          {forum?.image && (
            <img
              src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + forum.image)}
              style={{ maxHeight: 12, marginRight: 4 }}
              alt="aperçu"
            />
          )}
          {`${forum.name}`}
        </span>
      </>
    );
  }
  return '';
};
