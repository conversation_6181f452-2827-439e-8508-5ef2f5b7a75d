import { tr } from '@/shared/services/translate';

////////////////////// Fonctions d'override
///// Pour le titre
export const OverrideTitleSetter = (form, field, lang, response) => {
  /* fonction qui permet de set le title d'un qcm en utilisant le form associé */
  try {
    const newText = response?.text;
    form?.setFieldValue(tr(field, lang), newText);
    return true;
  } catch (e) {
    throw new Error("erreur lors de l'update du champ de titre :", e);
  }
};

export const overrideDataFonctionTitleFormater = (form, field, lang, id, triggerUpdate = null) => {
  return [
    {
      text: form?.getFieldValue(tr(field, lang)) || '',
      id: `${id}`,
      uniqueId: `${id}`,
      overrideSetterFunction: (response) => {
        const value = OverrideTitleSetter(form, field, lang, response);
        if (triggerUpdate) {
          setTimeout(() => {
            triggerUpdate();
          }, 100);
        }
        return value;
      }
    }
  ];
};

////// Pour les formations éléments
export const OverrideFormationElementSetter = (setter, field, lang, response) => {
  try {
    const newText = response?.text;
    setter((prev) => ({
      ...prev,
      [tr(field, lang)]: newText // Correction de "nexText" en "newText" et ajustement de la syntaxe
    }));
    return true;
  } catch (e) {
    throw new Error("erreur lors de l'update du champ de titre du formation élément :", e);
  }
};

export const overrideDataFonctionFormationElementFormater = (
  editorContent,
  setEditorContent,
  field,
  lang,
  id
) => {
  return [
    {
      text: editorContent[tr(field, lang)] || '',
      id: `${id}`,
      uniqueId: `${id}`,
      overrideSetterFunction: (response) =>
        OverrideFormationElementSetter(setEditorContent, field, lang, response)
    }
  ];
};

////// Pour les Ones Propositions
export const overrideEnonceSetter = (
  form,
  setFormValuesForPreviewState,
  fieldEnonce,
  lang,
  response
) => {
  try {
    const newText = response?.text;
    form?.setFieldValue(tr(fieldEnonce, lang), newText);
    return true;
  } catch (e) {
    throw new Error("erreur lors de l'update de l'énoncé' :", e);
  }
};

export const overrideJustificationSetter = (
  form,
  setFormValuesForPreviewState,
  fieldJustification,
  lang,
  response
) => {
  try {
    const newText = response?.text;

    if (response?.isTrue !== undefined && response?.isTrue !== null) {
      form?.setFieldsValue({
        [tr(fieldJustification, lang)]: newText,
        ['isTrue']: response?.isTrue
      });

      setFormValuesForPreviewState((prev) => {
        return { ...prev, isTrue: response?.isTrue };
      });
    } else {
      form?.setFieldsValue({
        [tr(fieldJustification, lang)]: newText
      });
    }
    return true;
  } catch (e) {
    console.error('erreur dans update enonce :', e);
    throw new Error(`erreur lors de l'update de l'énoncé' : ${e} `);
  }
};

export const overrideDataFonctionOnePropositionFormater = (
  form,
  setFormValuesForPreviewState,
  fieldEnonce,
  fieldJustification,
  lang,
  id,
  isTrue,
  triggerUpdate = null
) => {
  return [
    {
      text: form.getFieldValue(tr(fieldEnonce, lang)) || '',
      id: `${id}`,
      uniqueId: `${id}_énoncé`,
      additionalInputJson: { isTrue: isTrue },
      overrideSetterFunction: (response) => {
        console.log('calling overrideSetterFunction');
        const value = overrideEnonceSetter(
          form,
          setFormValuesForPreviewState,
          fieldEnonce,
          lang,
          response
        );
        if (triggerUpdate) {
          /*
          console.log('triggering update enonce');
          setTimeout(() => {
            triggerUpdate();
          }, 100);
           */
        }
        return value;
      }
    },
    {
      text: form.getFieldValue(tr(fieldJustification, lang)) || '',
      id: `${id}`,
      uniqueId: `${id}_justification`,
      additionalInputJson: { isTrue: isTrue },
      overrideSetterFunction: (response) => {
        const value = overrideJustificationSetter(
          form,
          setFormValuesForPreviewState,
          fieldJustification,
          lang,
          response
        );
        if (triggerUpdate) {
          /*
          console.log('triggering update justification');
          setTimeout(() => {
            triggerUpdate();
          }, 100);
          */
        }
        return value;
      }
    }
  ];
};
