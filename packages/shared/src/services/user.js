import { GlobalConfig } from '@/shared/utils/utils.js';
import { AUTHORITIES, setAuthority } from '@/shared/utils/authority'
import router from 'umi/router'

export const goToPrivateDiscussionWith = (id) => router.replace(`/messages/conversations/${id}`)

export const deleteLocalUserData = client => {
  localStorage.removeItem('token')
  localStorage.removeItem('mdbx-authority')
  window.localStorage.clear();
  window.sessionStorage.clear();
  // Apollo client clear store
  client.clearStore().then(() => {
    client.resetStore()
  });
}

export const UserStatsOperations = {
  incrementSeenClasses: 'seenClasses',
  incrementSeenAnnales: 'seenAnnales',
  incrementSeenMcq: 'seenMcq',
}

// eslint-disable-next-line no-undef
export const getAvatarSrc = avatar => GlobalConfig.get().AVATARS_URL + avatar
export const AdminRoleSimulator2000 = {
  simulerCompteTuteur: () => {
    localStorage.setItem('simulatorType', AUTHORITIES.TUTEUR)
    setAuthority(AUTHORITIES.TUTEUR)
  },
  simulerCompteEtudiant: () => {
    localStorage.setItem('simulatorType', AUTHORITIES.USER)
    setAuthority(AUTHORITIES.USER)
  },
  backToAdmin: () => {
    localStorage.removeItem('simulatorType')
    setAuthority(AUTHORITIES.ADMIN)
  },
  getSimulatorType: () => {
    return localStorage.getItem('simulatorType')
  },
}
// Helper functions
export const isRoleSimulated = () => localStorage.getItem('simulatorType')
export const isTuteurRoleSimulated = () => isRoleSimulated() && AdminRoleSimulator2000.getSimulatorType() === AUTHORITIES.TUTEUR
export const isUserRoleSimulated = () => isRoleSimulated() && AdminRoleSimulator2000.getSimulatorType() === AUTHORITIES.USER
