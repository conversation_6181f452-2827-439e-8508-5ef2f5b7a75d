/* Utility and service functions for comments */

import { getMentionData } from '@/shared/components/ExoQuill/Mention/mentions';
import { QUERY_COMMENTAIRES_COURS } from '@/shared/graphql/cours';
import { QUERY_COMMENTAIRES_EVENT } from '@/shared/graphql/events.js';
import {
  QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_COURS,
  QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_EVENT,
  QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_FORUM,
  QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_QCM
} from '@/shared/graphql/posts.js';
import {
  QUERY_COMMENTAIRES_EXERCISE,
  QUERY_COMMENTAIRES_QCM,
  QUERY_POSTS_QUESTION_ANSWER,
  QUERY_POSTS_QUESTION_ANSWER_IN_QCM
} from '@/shared/graphql/qcm';
import { QUERY_FORUM_POSTS } from '@/shared/graphql/qcm.js';
import dayjs from 'dayjs';
import router from 'umi/router.js';
import useSessionStorage from '@/shared/hooks/useSessionStorage';

/**
 * @param CommentairesType commentaireType
 */

export const typePostSelecter = {
  select_category: 'categoryType',
  select_exercise: 'exerciseType'
};

// Initialise un filter par default (pas mal si on veut vérif la cohérence des données avec l'ancienne data et la nouvelle
const defaultFilter = {
  coursIds: null, // Conditionnellement aux droits de l'user => Doit être déterminé au chargement du componant de sélection des UE,
  typeIds: null,
  category: null, //['QCM','COURS','EVENT','FORUM','QUESTION_ANSWER'],
  userIds: null,
  participantsUserIds: null,
  startDateCreationFilter: dayjs().subtract(1, 'month').toDate(),
  endDateCreationFilter: dayjs().add(1, 'day').toDate(),
  isResolved: false,
  titreFilter: null,
  contentFilter: null,
  iaAnswered: null,
  categoryExerciceAdvancedFilter: null, // mettre null ici permet de séparer les cas où init mais vide, et non init
  categoryEventAdvancedFilter: null, // mettre null ici permet de séparer les cas où init mais vide, et non init
  isAskingForHumanHelp: null,
  isResolvedByAi: null,
  questionTypeIds: null,

  pointerCategoryOrExercise: typePostSelecter.select_exercise // ne sera pas envoyé dans la query, sert pour le front, pour envoyer soit exercise filter, soit category filter
};

export const initPostSearchFilter = () => {
  /* Fonction qui retourne un react Hook du searchQcmFilter depuis le sessionStorage. Si présent, récupère, si absent, init
   * /!\ Faire attention à la sync (update)
   * /!\ Faire attention à l'update de droit (accès à des UE auquel l'user n'a plus les droits
   * */
  return useSessionStorage('localPostFilter', defaultFilter);
};

export const resetPostSearchFilter = () => {
  window.sessionStorage.setItem('localPostFilter', JSON.stringify(defaultFilter));
  return JSON.parse(window.sessionStorage.getItem('localPostFilter'));
};

export const goToCommentThreadByType = (commentaireType, typeId, id, openInNewTab) => {
  const path = `/discussions/post/${commentaireType}/${typeId}/${id}`;
  router.push(path);
};

export const CommentairesType = {
  QCM: 'QCM', // Obsolète
  COURS: 'COURS',
  FORUM: 'FORUM',
  EVENT: 'EVENT',
  QUESTION_ANSWER: 'QUESTION_ANSWER', //
  EXERCISE: 'EXERCISE', //
  QUESTION_ANSWERS_IN_QCM: 'QUESTION_ANSWERS_IN_QCM' // réponse à une question sur un QCM
};

export const getCommentaireTypeName = (commentaireType) => {
  switch (commentaireType) {
    case CommentairesType.QCM:
    case CommentairesType.QUESTION_ANSWER:
    case CommentairesType.QUESTION_ANSWERS_IN_QCM:
      return 'QCM';
    case CommentairesType.EXERCISE:
      return 'Exercice';
    case CommentairesType.COURS:
      return 'Cours';
    case CommentairesType.FORUM:
      return 'Forum';
    case CommentairesType.EVENT:
      return 'Evenement';
    default:
      return 'inconnu';
  }
};
export const getTypeIdFromObject = (post, type) => {
  switch (type) {
    case CommentairesType.COURS:
      return post.courId;
    case CommentairesType.QCM:
      return post.qcmIdQcm;
    case CommentairesType.FORUM:
      return post.forumId;
    case CommentairesType.EVENT:
      return post.eventId;
    case CommentairesType.QUESTION_ANSWER:
    case CommentairesType.QUESTION_ANSWERS_IN_QCM:
      return post.answerId;
    case CommentairesType.EXERCISE:
      return post.questionId;
    default:
      return undefined;
  }
};
export const getCommentaireTypeFromObject = (post) => {
  const { courId, qcmIdQcm, forumId, answerId, eventId = null, questionId } = post;
  if (courId) return CommentairesType.COURS;
  if (qcmIdQcm) return CommentairesType.QCM;
  if (forumId) return CommentairesType.FORUM;
  if (answerId) return CommentairesType.QUESTION_ANSWER;
  if (eventId) return CommentairesType.EVENT;
  if (questionId) return CommentairesType.EXERCISE;
  return undefined;
};

export const buildThreadLinkFromComment = (post) => {
  if (!post) {
    return '';
  }
  const type = getCommentaireTypeFromObject(post);
  const typeId = getTypeIdFromObject(post, type);
  return `/discussions/post/${type}/${typeId}/${post.threadId || post.id}`;
};

export const getCurrentScholarYear = () => {
  const now = dayjs();
  const year = now.year();
  if (now.isBetween(`${year}-07-01`, `${year + 1}-06-30`)) {
    return year;
  }
  return year - 1;
};

export const getScholarYearFromDate = (date) => {
  const year = date.year();
  if (date.isBetween(`${year}-07-01`, `${year + 1}-06-30`)) {
    return year;
  }
  return year - 1;
};

export const LIKE_TYPE = {
  LIKE: 'POST_LIKE',
  DISLIKE: 'POST_DISLIKE'
};
export const COMMENT_FILTER = {
  TOP: 'TOP',
  MOST_RECENT: 'MOST_RECENT'
};

/**
 * Get query object before sending it
 * @param {string} text
 * @param {*} type
 * @param {*} typeId
 * @param {*} parentId
 * @param {*} isAnswer
 * @param {*} file
 * @param {*} fileImage
 * @param title
 * @param selectedPostTypeId
 * @param fileList
 */
export const getMutationVariablesForCommentaire = async (
  text,
  type,
  typeId,
  parentId = null,
  isAnswer = false,
  file = null,
  fileImage = null,
  title = null,
  selectedPostTypeId = null,
  fileList = []
) => {
  const mentionedUserIds = getMentionData() || [];
  if (isAnswer) {
    const commonValues = {
      text,
      parentId,
      file, // legacy
      fileImage, // legacy
      fileList,
      title,
      mentionedUserIds,
      postTypeId: selectedPostTypeId
    };
    if (type === CommentairesType.COURS) {
      return { courId: typeId, ...commonValues };
    }
    if (type === CommentairesType.QCM) {
      return { qcmIdQcm: typeId, ...commonValues };
    }
    if (type === CommentairesType.FORUM) {
      return { forumId: typeId, ...commonValues };
    }
    if (type === CommentairesType.QUESTION_ANSWER) {
      return { answerId: typeId, ...commonValues };
    }
    if (type === CommentairesType.EVENT) {
      return { eventId: typeId, ...commonValues };
    }
    if (type === CommentairesType.EXERCISE) {
      return { questionId: typeId, ...commonValues };
    }
  } else {
    const commonValues = {
      text,
      file, // legacy
      fileImage, // legacy
      fileList,
      title,
      mentionedUserIds,
      postTypeId: selectedPostTypeId
    };
    if (type === CommentairesType.COURS) {
      return { courId: typeId, ...commonValues };
    }
    if (type === CommentairesType.QCM) {
      return { qcmIdQcm: typeId, ...commonValues };
    }
    if (type === CommentairesType.FORUM) {
      return { forumId: typeId, ...commonValues };
    }
    if (type === CommentairesType.QUESTION_ANSWER) {
      return { answerId: typeId, ...commonValues };
    }
    if (type === CommentairesType.EVENT) {
      return { eventId: typeId, ...commonValues };
    }
    if (type === CommentairesType.EXERCISE) {
      return { questionId: typeId, ...commonValues };
    }
  }
  return '';
};

export const getQueryFromCommentaireType = (type) => {
  switch (type) {
    case CommentairesType.COURS:
      return QUERY_COMMENTAIRES_COURS;
    case CommentairesType.QCM:
      return QUERY_COMMENTAIRES_QCM;
    case CommentairesType.FORUM:
      return QUERY_FORUM_POSTS;
    case CommentairesType.QUESTION_ANSWER:
      return QUERY_POSTS_QUESTION_ANSWER;
    case CommentairesType.QUESTION_ANSWERS_IN_QCM:
      return QUERY_POSTS_QUESTION_ANSWER_IN_QCM;
    case CommentairesType.EVENT:
      return QUERY_COMMENTAIRES_EVENT;
    case CommentairesType.EXERCISE:
      return QUERY_COMMENTAIRES_EXERCISE;
    default:
      console.warn('Unknown type in getQueryFromCommentaireType');
      return QUERY_COMMENTAIRES_COURS;
  }
};

/* YEARS */
export const getYearsQueryFromCommentaireType = (type) => {
  switch (type) {
    case CommentairesType.COURS:
      return QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_COURS;
    case CommentairesType.QCM:
    case CommentairesType.QUESTION_ANSWERS_IN_QCM:
    case CommentairesType.EXERCISE:
      return QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_QCM;
    case CommentairesType.FORUM:
      return QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_FORUM;
    case CommentairesType.EVENT:
      return QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_EVENT;
    default:
      return QUERY_AVAILABLE_YEARS_FOR_POSTS_FOR_COURS;
  }
};
export const getYearsFromData = (type, data) => {
  switch (type) {
    case CommentairesType.COURS:
      return data?.getYearsAvailableForPostsForCours;
    case CommentairesType.QCM:
    case CommentairesType.QUESTION_ANSWERS_IN_QCM:
    case CommentairesType.EXERCISE:
      return data?.getYearsAvailableForPostsForQcm;
    case CommentairesType.FORUM:
      return data?.getYearsAvailableForPostsForForum;
    case CommentairesType.EVENT:
      return data?.getYearsAvailableForPostsForEvent;
    default:
      return '';
  }
};
export const getQueryVariablesFromCommentaireType = (type, id, filter) => {
  switch (type) {
    case CommentairesType.COURS:
      return { coursId: id, filter };
    case CommentairesType.QCM:
    case CommentairesType.QUESTION_ANSWERS_IN_QCM:
      return { qcmId: id, filter };
    case CommentairesType.FORUM:
      return { forumId: id, filter };
    case CommentairesType.QUESTION_ANSWER:
      return { answerId: id, filter };
    case CommentairesType.EVENT:
      return { eventId: id, filter };
    case CommentairesType.EXERCISE:
      return { questionId: id, filter };
    default:
      return undefined;
  }
};
export const getDataObjectFromType = (type, data) => {
  switch (type) {
    case CommentairesType.COURS:
      return data && data.postsForCours;
    case CommentairesType.QCM:
      return data && data.postsForQcm;
    case CommentairesType.FORUM:
      return data && data.postsForForum;
    case CommentairesType.QUESTION_ANSWER:
      return data && data.postsForAnswer;
    case CommentairesType.QUESTION_ANSWERS_IN_QCM:
      return data && data.postsForAnswersInQcm;
    case CommentairesType.EVENT:
      return data && data.postsForEvent;
    case CommentairesType.EXERCISE:
      return data && data.postsForExercise;
    default:
      return '';
  }
};
