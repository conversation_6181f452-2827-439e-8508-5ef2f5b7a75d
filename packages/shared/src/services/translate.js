import i18n from '@/shared/i18n.js';
import React from 'react';

export const ALL_AVAILABLE_LANGUAGES = [
  { value: 'fr', label: '🇫🇷 Français' },
  { value: 'en', label: '🇬🇧 English' },
  { value: 'es', label: '🇪🇸 Español' },
  { value: 'de', label: '🇩🇪 Deutsch' },
  { value: 'it', label: '🇮🇹 Italiano' },
];

export const missingTranslation = (
  <React.Fragment>
    <i style={{ color: '#ec4648' }}>
      {i18n.t('MissingTranslation')}
    </i>
  </React.Fragment>
);

export const getLanguageName = (lang) => {
  switch (lang) {
    case 'en':
      return '🇬🇧 EN';
    case 'fr':
      return '🇫🇷 FR';
    case 'es':
      return '🇪🇸 ES';
    case 'it':
      return '🇮🇹 IT';
    case 'de':
      return '🇩🇪 DE';
    default:
      return '🇫🇷 FR';
  }
};

export const tr = (fieldName, lang = i18n.language) => {
  if (lang === 'fr') {
    return `${fieldName}`;
  }
  return `${fieldName}_${lang}`;
};

export const trWithFallback = (obj, field) => obj[tr(field)] || obj[field] || '';

export const trWithMissingTranslationText = (obj, field) => {
  return obj[tr(field)] || missingTranslation;
};

export const getTrSuffix = (lang = i18n.language) => lang === 'fr' ? '' : `_${lang}`;

/* UE */
export const renderUENameWithFallback = (ue) => ue?.[tr('name')] || ue?.name;
export const renderUEDescriptionWithFallback = (ue) => ue?.[tr('description')] || ue?.description || ''; // Optional description
export const renderUENameAndDescriptionWithFallback = (ue) => `${renderUENameWithFallback(ue)} : ${renderUEDescriptionWithFallback(ue)}`;

/* Cours */
export const renderCourseNameAndDescriptionWithFallback = (c) => {
  const description = c?.[tr('text')] || c?.text || '';
  return `${c?.[tr('name')] || c?.name}${description ? ` : ${description}` : ''}`;
};

export const renderQuestionNameWithFallback = (question) => {
  return question?.[tr('question')] || question?.question || question?.[tr('question', 'en')];
};

export const renderAnswerNameWithFallback = (answer) => {
  return answer?.[tr('text')] || answer?.text || answer?.[tr('text', 'en')];
};
export const renderAnswerExplanationWithFallback = (answer) => {
  return answer?.[tr('explanation')] || answer?.explanation || answer?.[tr('explanation', 'en')];
};
export const renderAnswerTextWithFallback = (answer) => {
  return answer?.[tr('text')] || answer?.text || answer?.[tr('text', 'en')];
};

export const renderElementNameWithFallback = (element) => {
  return element?.[tr('name')] || element?.name || element?.[tr('name', 'en')];
};

export const renderElementDescriptionWithFallback = (element) => {
  return element?.[tr('description')] || element?.description || element?.[tr('description', 'en')];
};

// Challenges
export const getChallengeNameWithFallback = (challenge) => {
  return challenge?.[tr('name')] || challenge?.name || challenge?.[tr('name', 'en')];
};
export const getChallengeDescriptionWithFallback = (challenge) => {
  return challenge?.[tr('description')] || challenge?.description || challenge?.[tr('description', 'en')];
};