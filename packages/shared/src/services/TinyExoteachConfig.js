import { GlobalConfig } from '@/shared/utils/utils.js';

export const TinyExoteachConfig = {
  getAPIKey() {
    /*
    if (isDiploma) {
      return 'sapb2wiul7znyx5eveuql891h29g3g3lxnbzltje9dnvm57p'; // Paid diploma
    }
    */
    return 'x22lhu1bxy49trfvm2zs6diuzjo5w4mh7qh255i4khqq91t0'; // Default free api key
  },

  getPlugins() {
    const premiumPlugins =
      'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount checklist mediaembed casechange export formatpainter pageembed linkchecker a11ychecker tinymcespellchecker permanentpen powerpaste advtable advcode editimage tableofcontents footnotes mergetags autocorrect typography inlinecss help';
    const standardPlugins = [
      'advlist',
      'autolink',
      'lists',
      'link',
      'image',
      'charmap',
      'anchor',
      'searchreplace',
      'visualblocks',
      'code',
      'fullscreen',
      'insertdatetime',
      'media',
      'table',
      'preview',
      'help',
      'wordcount'
    ];
    return standardPlugins;
  },

  getToolbar() {
    const toolbarPremium =
      'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough forecolor | link image media table mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent | emoticons charmap mathjax | removeformat | help';
    const toolbarStandard =
      'undo redo | blocks fontsize | ' +
      'bold italic underline strikethrough forecolor | alignleft aligncenter ' +
      'alignright alignjustify | bullist numlist outdent indent | mathjax ' +
      'removeformat | help';
    return toolbarStandard;
  }
};
