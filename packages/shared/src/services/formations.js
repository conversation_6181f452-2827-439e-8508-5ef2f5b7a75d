import dayjs from 'dayjs';

export const ELEMENTS_TYPE = {
  TITLE: 'title',
  IMAGE: 'image',
  RICH_TEXT: 'text',
  CALLOUT: 'callout',
  LINK: 'link',
  VIDEO: 'video',
  FILE: 'file',
  MCQ: 'mcq', // Faire serie entiere
  DO_EXERCISE: 'doexercise', // Faire qu'un seul exo

  DIAPO_SYNTHESE: 'diaposynthese', // Diapo synthèse, exclusif medisup

  COURS: 'cours',
  COURSE_SHORTCUT: 'course_shortcut',
  HTML: 'html',
  FORM: 'form',

  BUTTON: 'button',

  EVENT: 'event',
  EXAM: 'exam',

  // Input
  SHORT_ANSWER: 'shortAnswer',
  LONG_ANSWER: 'longAnswer',
  INTEGER_NUMBER: 'IntNumber',
  FLOAT_NUMBER: 'FloatNumber',

  SINGLE_SELECT: 'singleSelect',
  MULTIPLE_SELECT: 'multipleSelect',
  DATE_PICKER: 'datePicker',
  DATE_AND_TIME_PICKER: 'dateTimePicker',

  RADIO_CHOICE_ANSWER: 'radioChoiceAnswer',
  CHECKBOXES_ANSWER: 'checkBoxesAnswer',
  FILE_IMPORT: 'fileImport',

  AVATAR_SELECT: 'avatarSelect',
  SECTION: 'section',

  // Issu de propriétés user global
  IMPORTED_PREDEFINED_INPUT: 'importedInput',

  REGISTER_FIELD: 'registerField', // champ d'inscription utilisateur (nom, prenom, address, etc)

  VIDSTACK_AUDIO: 'vidstackAudio', // Componant Vidstack avec layout Audio
  VIDSTACK_VIDEO: 'vidstackVideo', // Componant Vidstack avec layout Video

  DIAPO: 'diapo',

  // Package SCORM
  SCORM:'SCORM'
};
export const DEFAULT_ANSWER_SETTINGS = {
  mandatory: false
};

export const BLOCK_TYPE = {
  SINGLE: 'single',
  DOUBLE: 'double'
};

export const RegisterFieldsNames = {
  // Mandatory fields
  USERNAME: 'username',
  EMAIL: 'email',
  PASSWORD: 'password', // Password includes confirm-password
  // Optionnal fields
  NAME: 'name',
  FIRST_NAME: 'firstName',
  ADDRESS: 'addressline1',
  ADDRESS2: 'addressline2',
  POSTCODE: 'postcode',
  CITY: 'city',
  COUNTRY: 'country',
  BIRTHDATE: 'birthdate',
  NATIONALITY: 'nationality',
  GENDER: 'gender',
  EXTRATIME: 'isExtraTime',
  PHONE: 'phone',
  TITLE: 'title'
};
export const RegisterFieldsNamesArray = [
  RegisterFieldsNames.USERNAME,
  RegisterFieldsNames.EMAIL,
  RegisterFieldsNames.PASSWORD,
  RegisterFieldsNames.NAME,
  RegisterFieldsNames.FIRST_NAME,
  RegisterFieldsNames.ADDRESS,
  RegisterFieldsNames.ADDRESS2,
  RegisterFieldsNames.POSTCODE,
  RegisterFieldsNames.CITY,
  RegisterFieldsNames.COUNTRY,
  RegisterFieldsNames.BIRTHDATE,
  RegisterFieldsNames.NATIONALITY,
  RegisterFieldsNames.GENDER,
  RegisterFieldsNames.PHONE,
  RegisterFieldsNames.TITLE,
  RegisterFieldsNames.EXTRATIME
];

// Les champs statiques que l'on peut mettre dans l'url Dynamique
export const AuthorizedDynamicUrlFieldsArray = [
  RegisterFieldsNames.USERNAME,
  RegisterFieldsNames.EMAIL,
  //RegisterFieldsNames.PASSWORD,
  RegisterFieldsNames.NAME,
  RegisterFieldsNames.FIRST_NAME,
  RegisterFieldsNames.ADDRESS,
  RegisterFieldsNames.ADDRESS2,
  RegisterFieldsNames.POSTCODE,
  RegisterFieldsNames.CITY,
  RegisterFieldsNames.COUNTRY,
  RegisterFieldsNames.BIRTHDATE,
  RegisterFieldsNames.NATIONALITY,
  RegisterFieldsNames.GENDER,
  RegisterFieldsNames.PHONE,
  RegisterFieldsNames.TITLE,
  RegisterFieldsNames.EXTRATIME
];

// Mandatory fields
export const MandatoryRegisterFieldsArray = [
  RegisterFieldsNames.USERNAME,
  RegisterFieldsNames.EMAIL,
  RegisterFieldsNames.PASSWORD
];

/**
 * From elements, extract initial values for Antd Form initialValues
 *
 * @param elementsInStep
 * @returns {{customFields: {}}}
 */
export function extractInitialValuesFromElementsUserProperties(elementsInStep) {
  // Pour chaque element associer bonne default value
  let customFields = {};
  if (!Array.isArray(elementsInStep)) {
    return {};
  }
  for (const element of elementsInStep) {
    const propertyValue = element?.userPropertyValue?.value;
    const propertyValues = element?.userPropertyValue?.values;
    let valueToShow = propertyValue || propertyValues; // Garder cet ordre pour que les valeurs soient bien mises à jour (!!'' donne false, !![] donne true)
    // Si c'est une date on doit utiliser dayjs
    if (valueToShow) {
      const { DATE_PICKER, AVATAR_SELECT, FILE_IMPORT, DATE_AND_TIME_PICKER } = ELEMENTS_TYPE;
      if ([DATE_PICKER, DATE_AND_TIME_PICKER].includes(element?.type)) {
        if (valueToShow) {
          valueToShow = dayjs(valueToShow);
        }
      }
      // Pour fichiers on met rien
      if ([FILE_IMPORT, AVATAR_SELECT].includes(element?.type)) {
        valueToShow = undefined;
      }
      // Associe à l'objet
      const parentElementId = element?.userPropertyValue?.elementId;
      customFields[parentElementId] = valueToShow;
    }
  }
  return {
    customFields
  };
}

/**
 *
 * @param elements
 * @param setElementsSplittedInSteps
 */
export function extractElementsInSteps(elements, setElementsSplittedInSteps) {
  if (elements) {
    let tempSteps = [];
    let currentStepElements = [];
    for (let element of elements) {
      if (element.type === 'section' && currentStepElements.length) {
        tempSteps.push(currentStepElements);
        currentStepElements = [];
      }
      currentStepElements.push(element);
    }
    if (currentStepElements.length) tempSteps.push(currentStepElements);
    setElementsSplittedInSteps(tempSteps.length ? tempSteps : [elements]);
  }
}

export const getElementTypeName = (el) => {
  switch (el) {
    case ELEMENTS_TYPE.RICH_TEXT:
      return 'Texte enrichi';
    case ELEMENTS_TYPE.TITLE:
      return 'Titre';
    case ELEMENTS_TYPE.IMAGE:
      return 'Image';
    case ELEMENTS_TYPE.LINK:
      return 'Lien';
    case ELEMENTS_TYPE.VIDEO: // obsolète
      return 'Vidéo';
    case ELEMENTS_TYPE.VIDSTACK_AUDIO:
      return 'Audio';
    case ELEMENTS_TYPE.VIDSTACK_VIDEO:
      return 'Vidéo';
    case ELEMENTS_TYPE.FILE:
      return 'Fichier';
    case ELEMENTS_TYPE.COURS:
      return 'Cours';
    case ELEMENTS_TYPE.HTML:
      return 'HTML';
    case ELEMENTS_TYPE.CALLOUT:
      return 'Encadré';
    case ELEMENTS_TYPE.MCQ:
      return 'Quizz';
    case ELEMENTS_TYPE.SCORM:
      return 'SCORM'
    default:
      return '';
  }
};

export const getElementOrBlockStyle = (block) => {
  if (!block?.settings) {
    return {};
  }
  const s = block?.settings;
  let style = {};
  if (s?.borderRadius) {
    style = { ...style, borderRadius: `${s?.borderRadius}px` };
  }
  if (s?.borderSize) {
    style = { ...style, border: `${s?.borderSize}px solid ${s?.borderColor || 'black'}` };
  }
  // background
  if (s?.backgroundColor) {
    style = { ...style, backgroundColor: s?.backgroundColor };
  }
  return style;
};

export const mapFormFieldsToCustomFields = (formCustomFields, customFiles) => {
  if (!formCustomFields) {
    return null;
  }
  const customFields = [];
  Object.keys(formCustomFields).forEach((elementId) => {
    let toAdd = {
      elementId
    };
    if (Array.isArray(formCustomFields[elementId])) {
      toAdd = {
        ...toAdd,
        values: formCustomFields[elementId]
      };
    } else {
      toAdd = {
        ...toAdd,
        value: formCustomFields[elementId]
      };
    }
    customFields.push(toAdd);
  });
  // custom files not in form
  if (customFiles?.length > 0) {
    customFiles?.forEach((customFile) => {
      customFields?.push({
        elementId: customFile?.elementId,
        file: customFile?.file
      });
    });
  }
  return customFields;
};
