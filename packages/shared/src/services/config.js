import { GlobalConfig, isMobile } from '@/shared/utils/utils.js';

export const CONFIG_KEYS = {
  WEBSITE_BASE_URL: 'WEBSITE_BASE_URL',
  WEBSITE_NAME: 'WEBSITE_NAME',
  ANNONCE_GENERALE: 'ANNONCE_GENERALE',
  MAINTENANCE_MODE: 'MAINTENANCE_MODE',
  TWITTER_ACCOUNT: 'TWITTER_ACCOUNT',
  FACEBOOK_ACCOUNT: 'FACEBOOK_ACCOUNT',
  USER_CONNECTED_TO_ADD: 'USER_CONNECTED_TO_ADD',
  USER_TOTAL_TO_ADD: 'USER_TOTAL_TO_ADD',
  USER_QCM_STATS_TO_ADD: 'USER_QCM_STATS_TO_ADD',
  PLANNING_FEATURE_ACTIVATED: 'PLANNING_FEATURE_ACTIVATED',
  MCQ_CONFIG: 'MCQ_CONFIG',
  WEBSITE_LOGO_FILE: 'WEBSITE_LOGO_FILE',
  DEFAULT_PAYMENT_SYSTEM: 'DEFAULT_PAYMENT_SYSTEM', // stripe
  PAYMENT_METHOD: 'PAYMENT_METHOD',

  DEFAULT_AVATARS: 'DEFAULT_AVATARS',

  LOGO_LOGINPAGE: 'LOGO_LOGINPAGE',
  LOGO_MENUBAR: 'LOGO_MENUBAR',
  BACKGROUND_BANNER_IMAGE: 'BACKGROUND_BANNER_IMAGE',

  COLORS_LOGINPAGE: 'COLORS_LOGINPAGE',
  COLORS_BREADCRUMB: 'COLORS_BREADCRUMB',

  HUBSPOT_INTEGRATION: 'HUBSPOT_INTEGRATION',

  NOTIONS_SYNCHRONISATION_ENDPOINT: 'NOTIONS_SYNCHRONISATION_ENDPOINT',

  LINK_TO_SALES_SITE: 'LINK_TO_SALES_SITE',
  REGISTERING_OPEN_ON_MOBILE_APP: 'REGISTERING_OPEN_ON_MOBILE_APP',
  REGISTER_PAGE_SELECTED_FORFAITS: 'REGISTER_PAGE_SELECTED_FORFAITS',
  COMPANY_INFORMATION: 'COMPANY_INFORMATION',
  CGV: 'CGV',

  LOGO_PRINT: 'LOGO_PRINT',
  LOGO_NAVIGATION_COURS: 'LOGO_NAVIGATION_COURS',
  PRIMARY_COLOR: 'PRIMARY_COLOR',
  SECONDARY_COLOR: 'SECONDARY_COLOR',
  ENABLED_LANGUAGES_FOR_CONTENT: 'ENABLED_LANGUAGES_FOR_CONTENT',
  APPEARANCE: 'APPEARANCE',
  CUSTOM_MOBILE_CONFIG: 'CUSTOM_MOBILE_CONFIG',

  /* Chat GPT API key integration */
  CHAT_GPT_INTEGRATION: 'CHAT_GPT_INTEGRATION',
  CHAT_GPT_SETTINGS: 'CHAT_GPT_SETTINGS',

  // TODO remove now it's CHAT_GPT_INTEGRATION json
  CHAT_GPT_API_KEY: 'CHAT_GPT_API_KEY',
  // TODO will be removed
  CHAT_GPT_AUTO_ANSWER_QUESTIONS_FEATURE_ENABLED: 'CHAT_GPT_AUTO_ANSWER_QUESTIONS_FEATURE_ENABLED',

  /* MATHPIX_INTEGRATION */
  MATHPIX_INTEGRATION:'MATHPIX_INTEGRATION'
};

export const CONFIG_VALUES_TEXT = {
  MAINTENANCE_MODE: 'Mode maintenance (désactiver accès au site)',
  WEBSITE_NAME: 'Nom du site',
  PLANNING_FEATURE_ACTIVATED: 'Activer le planning',
  USER_CONNECTED_TO_ADD: 'Utilisateurs connectés à ajouter aux stats',
  USER_TOTAL_TO_ADD: 'Utilisateurs inscrits à ajouter aux stats',
  USER_QCM_STATS_TO_ADD: 'Utilisateurs ayant faits un qcm à ajouter aux stats',
  DEFAULT_PAYMENT_SYSTEM: 'Mode de paiement par défaut', // stripe

  LOGO_LOGINPAGE: 'Logo page de login',
  LOGO_MENUBAR: 'Logo barre de menu',

  COLORS_LOGINPAGE: 'Couleurs page de login',
  COLORS_BREADCRUMB: 'Couleurs bandeau',

  REGISTERING_OPEN_ON_MOBILE_APP: 'Inscription ouverte sur l’app mobile',
  LINK_TO_SALES_SITE: 'Lien vers le site de vente',
  COMPANY_INFORMATION: 'Informations sur la société',
  CGV: 'Conditions Générales de Vente',

  LOGO_PRINT: 'Logo pour l’impression',
  PRIMARY_COLOR: 'Couleur principale',
  SECONDARY_COLOR: 'Couleur secondaire',

  ENABLED_LANGUAGES_FOR_CONTENT: 'Langues activées',

  CHAT_GPT_API_KEY: '[CHAT GPT] Clé API Chat GPT',
  CHAT_GPT_AUTO_ANSWER_QUESTIONS_FEATURE_ENABLED:
    '[CHAT GPT] Activer la fonctionnalité de réponse automatique aux questions',

  APPEARANCE: 'APPEARANCE',
};

export const CONFIG_VALUES_TYPE = {
  WEBSITE_NAME: 'text',
  PLANNING_FEATURE_ACTIVATED: 'boolean',
  USER_CONNECTED_TO_ADD: 'int',
  USER_QCM_STATS_TO_ADD: 'int',
  DEFAULT_PAYMENT_SYSTEM: 'stripeOrMonetico',

  LOGO_LOGINPAGE: 'file',
  LOGO_MENUBAR: 'file',
  LOGO_PRINT: 'file',

  COLORS_LOGINPAGE: 'colors',
  COLORS_BREADCRUMB: 'colors',

  LINK_TO_SALES_SITE: 'text',
  REGISTERING_OPEN_ON_MOBILE_APP: 'boolean',
  CGV: 'text', // Should be Quill
  COMPANY_INFORMATION: 'company',

  ENABLED_LANGUAGES_FOR_CONTENT: 'array',
  APPEARANCE: 'appearance',

  CHAT_GPT_API_KEY: 'text',
  CHAT_GPT_AUTO_ANSWER_QUESTIONS_FEATURE_ENABLED: 'boolean',
};

// CONFIGS
// eslint-disable-next-line no-undef
export const getPublicSrc = file => {
  return GlobalConfig.get().PUBLIC_URL + file;
};
export const getCurrentDomain = () => {
  // For mobile app, handles same server with different domain, gets different configs
  if (isMobile) {
    const appId = GlobalConfig.get()?.appId;
    if (appId === 'GalienMarseille') {
      return 'cours-galien-marseille.exoteach.com';
    } else if (appId === 'hermione') {
      return 'hermione.exoteach.com';
    } else if (appId === 'ensao') {
      return 'ensao.exoteach.com';
    } else if (appId === 'diploma-audio') {
      return 'diploma-audio.exoteach.com';
    } else if (appId === 'LASPASCARAIBES') {
      return 'laspasscaraibes.exoteach.com';
    } else if (appId === 'passlastonannee') {
      return 'passlastonannee.exoteach.com';
    } else if (appId === 'aptoria') {
      return 'aptoria.exoteach.com';
    } else if (appId === 'diploma') {
      return 'diploma.exoteach.com';
    } else if (appId === 'ensao') {
      return 'ensao.exoteach.com';
    }
  }
  return window.location.hostname;
};

// To get all domains config (for mobile app support, mobile app should always get default domains and not custom domains)
export const DEFAULT_CONFIG_VARIABLES = { variables: { defaultOnly: false } };

export const ParticlesConfig = {
  snow: {
    particles: {
      number: { value: 20, density: { enable: true, value_area: 800 } },
      color: { value: '#fff' },
      shape: {
        type: 'circle',
        stroke: { width: 0, color: '#000000' },
        polygon: { nb_sides: 5 },
        image: { src: 'img/github.svg', width: 100, height: 100 },
      },
      opacity: {
        value: 0.5,
        random: true,
        anim: { enable: false, speed: 1, opacity_min: 0.1, sync: false },
      },
      size: {
        value: 10,
        random: true,
        anim: { enable: false, speed: 40, size_min: 0.1, sync: false },
      },
      line_linked: { enable: false, distance: 500, color: '#ffffff', opacity: 0.4, width: 2 },
      move: {
        enable: true,
        speed: 6,
        direction: 'bottom',
        random: false,
        straight: false,
        out_mode: 'out',
        bounce: false,
        attract: { enable: false, rotateX: 600, rotateY: 1200 },
      },
    },
    interactivity: {
      detect_on: 'canvas',
      events: {
        onhover: { enable: true, mode: 'bubble' },
        onclick: { enable: true, mode: 'repulse' },
        resize: true,
      },
      modes: {
        grab: { distance: 400, line_linked: { opacity: 0.5 } },
        bubble: { distance: 400, size: 4, duration: 0.3, opacity: 1, speed: 3 },
        repulse: { distance: 200, duration: 0.4 },
        push: { particles_nb: 4 },
        remove: { particles_nb: 2 },
      },
    },
    retina_detect: true,
  },

  stars: {
    particles: {
      number: { value: 160, density: { enable: true, value_area: 800 } },
      color: { value: '#ffffff' },
      shape: {
        type: 'circle',
        stroke: { width: 0, color: '#000000' },
        polygon: { nb_sides: 5 },
        image: { src: 'img/github.svg', width: 100, height: 100 },
      },
      opacity: {
        value: 1,
        random: true,
        anim: { enable: true, speed: 1, opacity_min: 0, sync: false },
      },
      size: {
        value: 3,
        random: true,
        anim: { enable: false, speed: 4, size_min: 0.3, sync: false },
      },
      line_linked: { enable: false, distance: 150, color: '#ffffff', opacity: 0.4, width: 1 },
      move: {
        enable: true,
        speed: 1,
        direction: 'none',
        random: true,
        straight: false,
        out_mode: 'out',
        bounce: false,
        attract: { enable: false, rotateX: 600, rotateY: 600 },
      },
    },
    interactivity: {
      detect_on: 'canvas',
      events: {
        onhover: { enable: true, mode: 'bubble' },
        onclick: { enable: true, mode: 'repulse' },
        resize: true,
      },
      modes: {
        grab: { distance: 400, line_linked: { opacity: 1 } },
        bubble: { distance: 250, size: 0, duration: 2, opacity: 0, speed: 3 },
        repulse: { distance: 400, duration: 0.4 },
        push: { particles_nb: 4 },
        remove: { particles_nb: 2 },
      },
    },
    retina_detect: true,
  },

  particlesLoginPage: {
    particles: {
      number: {
        value: 60,
        density: {
          enable: true,
          value_area: 1000,
        },
      },
      color: {
        value: '#ffffff',
      },
      shape: {
        type: 'circle',
        stroke: {
          width: 0,
          color: '#000000',
        },
        polygon: {
          nb_sides: 5,
        },
      },
      opacity: {
        value: 0.5,
        random: false,
        anim: {
          enable: false,
          speed: 1,
          opacity_min: 0.1,
          sync: false,
        },
      },
      size: {
        value: 5,
        random: true,
        anim: {
          enable: false,
          speed: 40,
          size_min: 0.1,
          sync: false,
        },
      },
      line_linked: {
        enable: true,
        distance: 150,
        color: '#ffffff',
        opacity: 0.4,
        width: 1,
      },
      move: {
        enable: true,
        speed: 4,
        direction: 'none',
        random: false,
        straight: false,
        out_mode: 'out',
        attract: {
          enable: false,
          rotateX: 600,
          rotateY: 1200,
        },
      },
    },
    interactivity: {
      detect_on: 'canvas',
      events: {
        onhover: {
          enable: true,
          mode: 'repulse',
        },
        onclick: {
          enable: true,
          mode: 'push',
        },
        resize: true,
      },
      modes: {
        grab: {
          distance: 200,
          line_linked: {
            opacity: 1,
          },
        },
        bubble: {
          distance: 400,
          size: 40,
          duration: 2,
          opacity: 8,
          speed: 3,
        },
        repulse: {
          distance: 150,
        },
        push: {
          particles_nb: 4,
        },
        remove: {
          particles_nb: 2,
        },
      },
    },
    retina_detect: true,
  },

  particlesLink: {
    particles: {
      number: {
        value: 20,
        density: {
          enable: true,
          value_area: 200,
        },
      },
      color: {
        value: '#ffffff',
      },
      shape: {
        type: 'circle',
        stroke: {
          width: 0,
          color: '#000000',
        },
        polygon: {
          nb_sides: 5,
        },
      },
      opacity: {
        value: 0.5,
        random: false,
        anim: {
          enable: false,
          speed: 1,
          opacity_min: 0.1,
          sync: false,
        },
      },
      size: {
        value: 3,
        random: true,
        anim: {
          enable: false,
          speed: 40,
          size_min: 0.1,
          sync: false,
        },
      },
      line_linked: {
        enable: true,
        distance: 150,
        color: '#ffffff',
        opacity: 0.4,
        width: 1,
      },
      move: {
        enable: true,
        speed: 2,
        direction: 'none',
        random: false,
        straight: false,
        out_mode: 'out',
        attract: {
          enable: false,
          rotateX: 600,
          rotateY: 1200,
        },
      },
    },
    interactivity: {
      detect_on: 'canvas',
      events: {
        onhover: {
          enable: true,
          mode: 'repulse',
        },
        onclick: {
          enable: true,
          mode: 'push',
        },
        resize: true,
      },
      modes: {
        grab: {
          distance: 400,
          line_linked: {
            opacity: 1,
          },
        },
        bubble: {
          distance: 400,
          size: 40,
          duration: 2,
          opacity: 8,
          speed: 3,
        },
        repulse: {
          distance: 50,
        },
        push: {
          particles_nb: 4,
        },
        remove: {
          particles_nb: 2,
        },
      },
    },
    retina_detect: true,
  },
};

/* type: loginPageBackgroundAnimation || bannerAnimation */
export const getParticlesParamsToShow = (appearance, type) => {
  switch (appearance[type]) {
    case 'stars':
      return ParticlesConfig.stars;
    case 'particles':
      if (type === 'loginPageBackgroundAnimation') {
        return ParticlesConfig.particlesLoginPage;
      }
      if (type === 'bannerAnimation') {
        return ParticlesConfig.particlesLink;
      }
      return null;
    case 'snow':
      return ParticlesConfig.snow;
    case 'none':
      return null;
  }
  return null;
};

export const getApparenceAttribute = (appearance, attribute) => {
  if (appearance.hasOwnProperty(attribute)) {
    return appearance[attribute];
  }
  // Default values if not defined in DB //

  if (attribute === 'showFooterStats') {
    return true;
  }
  if (attribute === 'showGlobalAnnounce') {
    return true;
  }

  // Cours page defaults tab: all activated except custom tab
  if(attribute === 'showCustomTab') {
    return false; // default, deactivated
  }

  if(attribute === 'showHomepageProgressBar') {
    return false; // Default, deactivated
  }

  // Other default, activated by default (not changed in Admin > Config > Appearance)
  return true;
};
