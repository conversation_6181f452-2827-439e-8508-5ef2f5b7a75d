{"name": "@medibox/shared", "version": "1.0.413", "private": true, "description": "", "watch": {"sync": {"patterns": ["src", "test"], "extensions": "js,jsx,ejs,less,css"}}, "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "lint": "npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint:prettier": "prettier --check \"**/*\"", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write \"**/*\"", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tsc": "tsc", "ui": "umi ui", "tag": "pushd ../../ && npm version patch && git push && popd", "sync:web": "sync-glob 'src/*' 'src/**/*' ../mediweb/src/shared/ --delete", "sync:phone": "sync-glob 'src/*' 'src/**/*' ../mediphone/src/shared/ --delete", "sync:medisupmobile": "sync-glob 'src/*' 'src/**/*' ../medisupmobile/src/shared/ --delete", "sync:web:watch": "sync-glob --watch 'src/*' 'src/**/*' ../mediweb/src/shared/ --delete", "sync:phone:watch": "sync-glob --watch 'src/*' 'src/**/*' ../mediphone/src/shared/ --delete", "sync:medisupmobile:watch": "sync-glob --watch 'src/*' 'src/**/*' ../medisupmobile/src/shared/ --delete", "sync": "concurrently npm:sync:web npm:sync:phone npm:sync:medisupmobile", "watch:sync": "concurrently npm:sync:web:watch npm:sync:phone:watch npm:sync:medisupmobile:watch"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["defaults", "not ie <= 10"], "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/compatible": "^5.1.1", "@ant-design/dark-theme": "^1.0.3", "@ant-design/icons": "^5.1.0", "@ant-design/pro-components": "^2.4.15", "@ant-design/pro-layout": "^7", "@ant-design/pro-table": "3.6.10", "@antv/g2": "^4.1.20", "@antv/g6": "^4.3.3", "@antv/g6-react-node": "^1.4.4", "@antv/util": "^2.0.14", "@antv/vis-predict-engine": "^0.1.1", "@apollo/client": "^3.7.14", "@cyntler/react-doc-viewer": "^1.17.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@mobiscroll/react": "^5.28.2", "@stripe/stripe-js": "^1.14.0", "@types/lodash.debounce": "^4.0.6", "@types/lodash.isequal": "^4.5.5", "@types/react-router": "^5.0.2", "@umijs/route-utils": "^4.0.1", "antd": "^5.20.6", "apollo-upload-client": "^12.1.0", "apollo-utilities": "^1.3.3", "chart.js": "^3.9.1", "chartjs-plugin-annotation": "^2.2.1", "classnames": "^2.2.6", "dayjs": "^1.11.7", "dva": "^2.6.0-beta.16", "easytimer-react-hook": "^1.0.3", "easytimer.js": "^4.3.4", "emoji-mart": "^3.0.0", "framer-motion": "^11.0.23", "graphql": "^15.8.0", "graphql-ws": "^5.13.1", "html2canvas": "^1.1.4", "i18next": "^21.9.2", "i18next-browser-languagedetector": "^6.1.5", "i18next-http-backend": "^1.4.4", "inspirational-quotes": "^1.0.8", "jodit-react": "^1.3.1", "jspdf": "^2.3.1", "katex": "^0.11.1", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.460.0", "moment": "^2.24.0", "moment-timezone": "^0.5.34", "omit.js": "^1.0.2", "path-to-regexp": "2.4.0", "prop-types": "^15.5.10", "qs": "^6.9.0", "quill-auto-detect-url": "^0.2.1", "quill-mention": "^3.4.1", "quill-table-better": "^1.0.7", "rc-queue-anim": "^1.8.4", "react": "^18.2.0", "react-chartjs-2": "^4.3.1", "react-countup": "^6.4.2", "react-doc-viewer": "^0.1.14", "react-dom": "^18.2.0", "react-google-recaptcha": "^2.1.0", "react-helmet": "^5.2.1", "react-i18next": "^11.18.6", "react-mathquill": "^1.0.3", "react-organizational-chart": "^2.2.0", "react-particles": "^2.9.3", "react-pdf": "^9.2.1", "react-quill-new": "^3.3.0", "react-reader": "^0.18.3", "react-simple-pull-to-refresh": "^1.2.3", "react-slidedown": "^2.4.5", "react-sortable-hoc": "^1.11.0", "react-textfit": "^1.1.1", "scorm-again": "^2.6.7", "shave": "^2.5.9", "subscriptions-transport-ws": "^0.10.0", "sweetalert2": "^9.10.12", "tsparticles": "^2.9.3", "umi": "^2.13.13", "umi-plugin-antd-icon-config": "^1.0.2", "umi-plugin-antd-theme": "^1.0.15", "umi-plugin-pro-block": "^1.3.2", "umi-plugin-react": "^1.14.10", "umi-request": "^1.3.5", "use-long-press": "^3.2.0", "use-merge-value": "^1.0.1"}, "devDependencies": {"@ant-design/pro-cli": "^3.1.0", "@antv/data-set": "^0.11.4", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^25.1.0", "@types/lodash": "^4.14.144", "@types/qs": "^6.5.3", "@types/react": "^16.9.17", "@types/react-dom": "^16.8.4", "@types/react-helmet": "^5.0.13", "@umijs/fabric": "^2.0.2", "chalk": "^3.0.0", "concurrently": "^4.1.2", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "cypress": "^7.2.0", "dva-logger": "^1.0.0", "enzyme": "^3.11.0", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "express": "^4.17.1", "gh-pages": "^2.0.1", "husky": "^4.0.7", "jest-puppeteer": "^4.4.0", "jsdom-global": "^3.0.2", "konva": "^9.3.6", "lint-staged": "^10.0.0", "mockjs": "^1.0.1-beta3", "node-fetch": "^2.6.0", "npm-watch": "^0.7.0", "pm2": "^4.3.0", "prettier": "^3.2.5", "react-konva": "^18.2.10", "stylelint": "^13.0.0", "sync-glob": "^1.4.0", "umi-plugin-antd-icon-config": "^1.0.2", "umi-plugin-ga": "^1.1.3", "umi-plugin-pro": "^1.0.2", "umi-types": "^0.5.9", "use-image": "^1.1.1"}, "optionalDependencies": {"puppeteer": "^2.0.0"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}