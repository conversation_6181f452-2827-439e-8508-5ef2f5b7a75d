import CheckMobileAppVersionWrapper from '@/shared/components/CheckMobileAppVersionWrapper';
import Avatar from '@/shared/components/GlobalHeader/AvatarDropdown.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { GET_CONFIG } from '@/shared/graphql/home';
import { GET_MY_NOTIFICATIONS } from '@/shared/graphql/notifications.js';
import { SET_DEVICE_TOKEN_FOR_USER } from '@/graphql/notifications';
import { GET_ME } from '@/shared/models/user.js';
import { getValueFromKeyConfigData } from '@/shared/services/home';
import { deleteLocalUserData } from '@/shared/services/user.js';
import { client } from '@/shared/utils/apolloClient.js';
import {
  BellOutlined,
  CheckSquareOutlined,
  CloseOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MessageOutlined,
  NotificationOutlined,
  TeamOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import CalendarOutlined from '@ant-design/icons/lib/icons/CalendarOutlined';
import { Badge } from '@awesome-cordova-plugins/badge';
import { Device } from '@capacitor/device';
import { Icon, NavBar, TabBar } from 'antd-mobile';
import { ApolloProvider, useApolloClient, useMutation, useQuery } from '@apollo/client';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Link, Redirect } from 'umi';
import { connect } from 'dva';
import { Badge as AntdBadge, Button, Drawer, Result } from 'antd';
import Authorized from '@/shared/utils/Authorized';
import { getAuthorityFromRouter, shouldShowMobileNavbarFct } from '@/shared/utils/utils';
import { BookOutlined, HomeOutlined } from '@ant-design/icons/lib/icons';
import Spin from 'antd/es/spin';
import MediSpin from '@/shared/components/PageLoading/MediSpin';
import router from 'umi/router';
import NotificationService from '../components/NotificationService/NotificationService.jsx';
import { getUnseenNotifCount, NotificationType } from '@/shared/services/notification.js';
import { useTranslation } from 'react-i18next';
import { CONFIG_KEYS, DEFAULT_CONFIG_VARIABLES, getApparenceAttribute, getPublicSrc } from '@/shared/services/config';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { tr } from '@/shared/services/translate';
import { isParent } from '@/shared/utils/authority';
import { AdminMenu } from '@/shared/pages/admin/components/AdminMenu';
import { useLocation } from 'react-router';

/**
 * use Authorized check all menu item
 */

const menuDataRender = menuList =>
  menuList?.map(item => {
    const localItem = { ...item, children: item.children ? menuDataRender(item.children) : [] };
    return Authorized.check(item.authority, localItem, null);
  });

function usePrevious(value) {
  const ref = useRef();
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
}

const LoggedInLayout = props => {
  const {
    dispatch,
    children,
    settings,
    global,
    location = {
      pathname: '/'
    }
  } = props;

  console.log('LoggedInLayout mobile', props);

  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);

  // TODO fetch user + notifs from global redux
  const { data: { myNotifications = [] } = {}, refetch: refetchNotifs } = useQuery(
    GET_MY_NOTIFICATIONS,
    {
      pollInterval: 10000
    }
  );
  const {
    loading: loadingConfig,
    error: errorConfig,
    data: dataConfig,
    refetch: refetchConfig
  } = useQuery(GET_CONFIG, { fetchPolicy: 'cache-and-network', ...DEFAULT_CONFIG_VARIABLES });
  const config = dataConfig?.config;
  const webSiteName = getValueFromKeyConfigData(config, CONFIG_KEYS.WEBSITE_NAME);
  const logoInMenuBar = getValueFromKeyConfigData(config, CONFIG_KEYS.LOGO_MENUBAR);
  const clientApp = useApolloClient();

  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor || '#1677ff';

  const countDiscussionIdsHavingUnseenMessages = useMemo(() => {
    const unreadMessageNotifications =
      myNotifications &&
      myNotifications?.filter(notif => !notif.seen && notif.type === NotificationType.NEW_MESSAGE);
    return [...new Set(unreadMessageNotifications?.map(notif => notif?.parentId))]?.length;
  }, [myNotifications]);

  const setAppBadgeCount = async count => {
    if (await Badge.isSupported()) {
      await Badge.set(count);
    }
  };

  useEffect(() => {
    if (myNotifications && myNotifications.length > 0) {
      const unseenCount = getUnseenNotifCount(myNotifications);
      setAppBadgeCount(unseenCount);
      props.dispatch({
        type: 'global/changeUnreadNotificationsCount',
        payload: unseenCount
      });
      props.dispatch({
        type: 'global/setNotifications',
        payload: myNotifications
      });
    }
  }, [myNotifications]);

  const currentLocation = useLocation();
  const pathname = currentLocation.pathname;

  Spin.setDefaultIndicator(<MediSpin />);

  // TODO cache user query
  const { loading, data, error } = useQuery(GET_ME, { fetchPolicy: 'no-cache' });

  const [platform, setPlatform] = React.useState(null);
  const getPlatform = async () => {
    const { platform } = await Device.getInfo();
    setPlatform(platform);
  };
  const [setDeviceTokenForUser] = useMutation(SET_DEVICE_TOKEN_FOR_USER);
  useEffect(() => {
    try {
      NotificationService.init(setDeviceTokenForUser);
      getPlatform();
    } catch (e) {
      console.error(e);
    }
  }, []);

  /**
   * init variables
   */
  const handleMenuCollapse = payload => {
    if (dispatch) {
      dispatch({
        type: 'global/changeLayoutCollapsed',
        payload
      });
    }
  }; // get children authority

  const authorized = getAuthorityFromRouter(props.route.routes, location.pathname || '/') || {
    authority: undefined
  };

  const isNotConnected = !loading && data && data.me == null;
  if (isNotConnected) {
    if (window.location.pathname !== '/user/login') {
      console.log('isNotConnected in loggedInLayout, redirecting to /user/login');
      deleteLocalUserData(clientApp);
      return <Redirect to="/user/login" />;
    }
  }
  const [isCollapsedNav, setIsCollapsedNav] = useState(true);

  useEffect(() => {
    setIsCollapsedNav(true);
  }, [pathname]);

  const disableBackButton = pathname => {
    switch (pathname) {
      case '/':
      case '/home':
      case '/discussions':
      case '/cours':
      case '/notifications':
      case '/qcm':
      case '/profile':
        return true;
      default:
        return false;
    }
  };

  const isAdminPath = pathname => {
    return pathname.startsWith('/admin') || pathname.startsWith('/tuteur-panel');
  };

  const navBarTitle = () => {
    if (pathname.startsWith('/cours')) {
      return t('Courses');
    }
    if (pathname.startsWith('/qcm')) {
      return t('Exercices');
    }
    if (pathname.startsWith('/home')) {
      return t('general.Home');
    }
    if (pathname.startsWith('/annale')) {
      return 'Annales';
    }
    if (pathname.startsWith('/discussions')) {
      return t('Forum');
    }
    if (pathname.startsWith('/profile')) {
      return t('Progress');
    }
    if (pathname.startsWith('/messages')) {
      return t('Messages');
    }
    if (pathname.startsWith('/notifications')) {
      return t('Notifications');
    }
    if (pathname.startsWith('/account/settings')) {
      return t('general.Profile');
    }
    return 'Medisup';
  };

  const shouldShowNavbar = shouldShowMobileNavbarFct(pathname);

  const renderMobileNavbar = () => {
    return (
      <NavBar
        style={{
          zIndex: 10000 /* position: 'fixed', width: '100%', top: 0, marginBottom: '100px' */
        }}
        mode="light"
        icon={
          disableBackButton(pathname) ? (
            ''
          ) : isAdminPath(pathname) ? (
            isCollapsedNav ? (
              <>
                <MenuUnfoldOutlined /> <span style={{ marginLeft: 8 }}>{t('Menu')}</span>
              </>
            ) : (
              <>
                <MenuFoldOutlined /> <span style={{ marginLeft: 8 }}>{t('Menu')}</span>
              </>
            )
          ) : (
            <Icon type="left" size={'lg'} />
          )
        }
        onLeftClick={() => {
          if (pathname.startsWith('/qcm/correction/')) {
            // Dans une correction serie on veut pas revenir en arrière
            router.push('/qcm');
          } else if (isAdminPath(pathname)) {
            setIsCollapsedNav(!isCollapsedNav);
          } else {
            router.goBack();
          }
        }}
        rightContent={[
          // <Icon key="0" type="search" style={{ marginRight: '16px' }}/>
          <CalendarOutlined
            style={{ fontSize: '22px', marginRight: '6px', color: primaryColor }}
            onClick={() => router.push('/planning')}
          />,

          <div style={{ marginLeft: '6px', marginRight: '12px' }}>
            <AntdBadge count={countDiscussionIdsHavingUnseenMessages} size={'small'}>
              <MessageOutlined
                style={{ fontSize: '22px', color: primaryColor }}
                onClick={() => router.push('/messages')}
              />
            </AntdBadge>
          </div>,

          <Avatar menu />
          //<Notifications style={{marginLeft: 5}} />
        ]}
      >
        <div onClick={() => router.push('/home')}>
          {logoInMenuBar && (
            <img
              style={{
                width: 'auto',
                height: '26px',
                marginTop: '2px'
              }}
              onError={e => {
                // Hide
                e.target.style.display = 'none';
              }}
              src={getPublicSrc(logoInMenuBar)}
            />
          )}
        </div>
      </NavBar>
    );
  };
  const noMatch = (
    <Result
      status="403"
      title="403"
      subTitle="Désolé, vous n'avez pas l'autorisation d'accéder à cette page"
      extra={
        <Button type="primary">
          <Link to="/user/login">{t('Login')}</Link>
        </Button>
      }
    />
  );
  const renderContent = () => (
    <>
      <Authorized authority={authorized.authority} noMatch={noMatch}>
        {/* div pour ajouter du blanc pour pas que le contenu soit au dessus de la navbar */}
        <div
          style={{
            zIndex: 10000,
            position: 'fixed',
            height: 'env(safe-area-inset-top)',
            backgroundColor: 'white',
            width: '100%'
          }}
        />
        {renderMobileNavbar()}
        <div
          style={{
            marginTop: platform === 'android' ? 33 : 90,
            marginBottom: 50,
            width: '100%',
            display: 'block',
            float: 'none'
          }}
        >
          <Drawer
            open={!isCollapsedNav}
            placement="left"
            onClose={() => setIsCollapsedNav(true)}
            closeIcon={<CloseOutlined style={{ color: 'white' }} />}
            height={'calc(100% - 109px)'}
            rootStyle={{
              zIndex: 10001
            }}
            styles={{
              body: { padding: 0 },
              header: {
                backgroundColor: '#001628',
                borderBottom: 'none'
              }
            }}
          >
            <AdminMenu location={location} collapsed={isCollapsedNav} />
          </Drawer>

          {children}
        </div>
      </Authorized>
    </>
  );

  const navbarLabels = getApparenceAttribute(appearance, 'navbarLabels');

  const showCourses = getApparenceAttribute(appearance, 'navbarShowCourses');
  const showExercises = getApparenceAttribute(appearance, 'navbarShowExercices');
  const showProfile = getApparenceAttribute(appearance, 'navbarShowMyProfile');
  const showForum = getApparenceAttribute(appearance, 'navbarShowForum');
  const showTeam = getApparenceAttribute(appearance, 'navbarShowTeam');

  if (isLoading) {
    return <SpinnerCentered />;
  }
  return (
    <div style={{ height: '100vh', display: 'flex', flex: '1', flexDirection: 'column' }}>
      <div style={{ flex: '1', display: 'flex' }}>{renderContent()}</div>

      {shouldShowNavbar && (
        <div style={{ flex: 0 }}>
          <TabBar
            unselectedTintColor="#949494"
            tintColor={primaryColor}
            barTintColor="white"
            prerenderingSiblingsNumber="0"
          >
            <TabBar.Item
              title={t('general.Home')}
              key="Accueil"
              icon={<HomeOutlined style={{ fontSize: '19px' }} />}
              selectedIcon={<HomeOutlined style={{ fontSize: '19px' }} />}
              selected={pathname === '/' || pathname === '/home'}
              badge={0}
              onPress={() => router.push('/home')}
              data-seed="logId"
            />
            {showCourses && !isParent() && (
              <TabBar.Item
                icon={<BookOutlined style={{ fontSize: '19px' }} />}
                selectedIcon={<BookOutlined style={{ fontSize: '19px' }} />}
                title={navbarLabels?.[tr('Courses')] || t('Courses')}
                key="Matières"
                badge={0}
                selected={pathname.startsWith('/cours')}
                onPress={() => router.push('/cours')}
                data-seed="logId1"
              />
            )}
            {showExercises && !isParent() && (
              <TabBar.Item
                icon={<CheckSquareOutlined style={{ fontSize: '19px' }} />}
                selectedIcon={<CheckSquareOutlined style={{ fontSize: '19px' }} />}
                title={navbarLabels?.[tr('Exercices')] || t('Exercices')}
                key="QCMs"
                badge={0}
                selected={pathname.startsWith('/qcm')}
                onPress={() => router.push('/qcm')}
                data-seed="logId1"
              />
            )}
            {showProfile && !isParent() && (
              <TabBar.Item
                icon={<TrophyOutlined style={{ fontSize: '19px' }} />}
                selectedIcon={<TrophyOutlined style={{ fontSize: '19px' }} />}
                title={navbarLabels?.[tr('MyProfile')] || t('MyProfile')}
                key="progresison"
                selected={pathname.startsWith('/profile')}
                onPress={() => router.push('/profile')}
              />
            )}

            {showForum && !isParent() && (
              <TabBar.Item
                icon={<NotificationOutlined style={{ fontSize: '19px' }} />}
                selectedIcon={<NotificationOutlined style={{ fontSize: '19px' }} />}
                title={navbarLabels?.[tr('Forum')] || t('Forum')}
                key="Forum"
                selected={pathname.startsWith('/discussions')}
                onPress={() => router.push('/discussions')}
              />
            )}
            {showTeam && !isParent() && (
              <TabBar.Item
                icon={<TeamOutlined style={{ fontSize: '19px' }} />}
                selectedIcon={<TeamOutlined style={{ fontSize: '19px' }} />}
                title={navbarLabels?.[tr('Team')] || t('Team')}
                key="Team"
                selected={pathname.startsWith('/tuteurs')}
                onPress={() => router.push('/tuteurs')}
              />
            )}

            <TabBar.Item
              icon={<BellOutlined style={{ fontSize: '19px' }} />}
              selectedIcon={<BellOutlined style={{ fontSize: '19px' }} />}
              title={t('general.Notifications')}
              key="notifications"
              badge={global.unreadNotificationsCount}
              selected={pathname.startsWith('/notifications')}
              onPress={() => router.push('/notifications')}
            />
          </TabBar>
        </div>
      )}
      {/*
      <ProLayout
        logo={false}
        menuHeaderRender={(logoDom, titleDom) => (
          <Link to="/">
            {logoDom}
            {titleDom}
          </Link>
        )}
        onCollapse={handleMenuCollapse}
        menuItemRender={(menuItemProps, defaultDom) => {
          if (menuItemProps.isUrl || menuItemProps.children || !menuItemProps.path) {
            return defaultDom
          }

          return <Link to={menuItemProps.path}>{defaultDom}</Link>
        }}
        breadcrumbRender={(routers = []) => [
          {
            path: '/',
            breadcrumbName: 'Accueil',
          },
          ...routers,
        ]}
        itemRender={(route, params, routes, paths) => {
          const first = routes.indexOf(route) === 0
          return first ? (
            <Link to={paths.join('/')}>{route.breadcrumbName}</Link>
          ) : (
            <span>{route.breadcrumbName}</span>
          )
        }}
        footerRender={footerRender}
        menuDataRender={menuDataRender}
        rightContentRender={() => <RightContent/>}
        {...props}
        {...settings}
        contentStyle={{ marginLeft: '8px', marginRight: '8px' }}
      >
        <Authorized authority={authorized.authority} noMatch={noMatch}>
          {children}
          <NotificationServiceHoC></NotificationServiceHoC>
        </Authorized>
      </ProLayout>
      {/*
      <SettingDrawer
      settings={settings}
      onSettingChange={config =>
       dispatch({
         type: 'settings/changeSetting',
         payload: config,
       })
      }
      />*/}
    </div>
  );
};

const BasicLayout = props => {
  console.log('BasicLayout mobile', props);
  return (
    <ApolloProvider client={client}>
      <CheckMobileAppVersionWrapper>
        <LoggedInLayout {...props} />
      </CheckMobileAppVersionWrapper>
    </ApolloProvider>
  );
};

export default connect(({ global, settings }) => ({
  collapsed: global.collapsed,
  global,
  settings
}))(BasicLayout);
