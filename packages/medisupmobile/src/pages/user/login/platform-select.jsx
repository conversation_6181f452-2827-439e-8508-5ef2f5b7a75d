import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { GlobalConfig } from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import router from 'umi/router';
import { GET_ALL_SERVER_CONFIGS } from './index.jsx';

/**
 * Page de chargement de plateforme passée en id dans l'url
 *
 * @param props
 * @returns {JSX.Element}
 */
export default function(props) {
  const { t } = useTranslation();
  const platformId = props.match.params.id;

  const {
    loading: loadingConfigs,
    error: errorConfigs,
    data: dataConfigs,
    refetch: refetchConfigs,
  } = useQuery(GET_ALL_SERVER_CONFIGS, {
    fetchPolicy: 'no-cache',
  });

  const [hasSelectedPlatform, setHasSelectedPlatform] = useState(GlobalConfig.hasSelectedPlatform());

  useEffect(() => {
    if(dataConfigs?.exoBackConfigs) {
      const allPlatformsConfigs = dataConfigs?.exoBackConfigs;
      const config = allPlatformsConfigs?.find(c => c.id === platformId)?.config;
      GlobalConfig.set(config);
      GlobalConfig.setHasSelectedPlatform(true);
      setHasSelectedPlatform(true);
      GlobalConfig.reloadPage();
    }
  }, [dataConfigs]);

  useEffect(() => {
    if(hasSelectedPlatform) {
      router.replace('/user/login');
    }
  }, [hasSelectedPlatform]);

  return (
    <>
      <SpinnerCentered loading={loadingConfigs} />
    </>
  )
}