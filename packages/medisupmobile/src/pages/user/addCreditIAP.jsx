import { ErrorResult } from '@/shared/components/ErrorResult.jsx'
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx'
import {
  MUTATION_UPDATE_FORFAIT_USER_TMP,
  QUERY_FORFAITS_CUSTOM_LINK,
  QUERY_MES_FORFAITS_DISPONIBLES,
} from '@/shared/graphql/forfaits.js'
import { ForfaitImage } from '@/shared/pages/account/forfait/components/ForfaitImage.jsx'
import { MultipleForfaits } from '@/shared/pages/account/forfait/components/MultiplesForfaits.jsx'
import { RadioForfait } from '@/shared/pages/account/forfait/components/RadioForfaits.jsx'
import { ForfaitTypes } from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal.jsx'
import { RegisterFormItems } from '@/shared/pages/user/register/components/RegisterFormItems.jsx'
import {
  card<PERSON>eadStyle,
  GlobalConfig,
  isAptoria,
  isMobile,
  showGqlErrorsInMessagePopupFromException,
} from '@/shared/utils/utils.js';
import { useMutation, useQuery } from '@apollo/client'
import { Card, Checkbox, Form, Button, Statistic } from 'antd'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next';
import router from 'umi/router'

export const renderCreditCost = (forfait, hide = false) => {
  if (hide) return ''
  if (forfait && forfait.creditCost && forfait.creditCost > 0) {
    return <React.Fragment> ou {forfait.creditCost} crédit{forfait.creditCost > 1 ? 's' : ''}</React.Fragment>
  }
  return ''
}

export const renderCreditGiven = (forfait) => {
  if (forfait && forfait.creditGiven && forfait.creditGiven > 0) {
    return <React.Fragment> (donne {forfait.creditGiven} crédit{forfait.creditGiven > 1 ? 's' : ''} à utiliser plus
      tard)</React.Fragment>
  }
  return ''
}

export const UpdateForfaitIAP = (
  {
    mesGroupes = null,
    withRegisterForm = false,
    withCustomLink = false,
    customLink,
    mesCredits = 0,
    showChoiceSummary = false,
  }) => {
  const {t} = useTranslation();
  const getQuery = () => withCustomLink ? QUERY_FORFAITS_CUSTOM_LINK : QUERY_MES_FORFAITS_DISPONIBLES
  const getQueryOptions = () => withCustomLink ? {
    fetchPolicy: 'no-cache',
    variables: { link: customLink },
  } : { fetchPolicy: 'no-cache' }
  const { data, error, loading, refetch } = useQuery(getQuery(), getQueryOptions())
  const [Mutation, { loading: loadingMut }] = useMutation(MUTATION_UPDATE_FORFAIT_USER_TMP)

  const mesForfaitsDisponibles = data && (withCustomLink ? data.forfaitsFromCustomLink : data.mesForfaitsDisponibles)
  const [form] = Form.useForm()

  const [forfaitsSelected, setForfaitsSelected] = useState([])
  const [prixTotal, setPrixTotal] = useState(0)
  const [creditsAvailable, setCreditsAvailable] = useState(0)

  useEffect(() => {
    setCreditsAvailable(parseInt(mesCredits))
  }, [mesCredits])

  const getParentsForfaits = () => {
    return mesForfaitsDisponibles && mesForfaitsDisponibles.filter(f => f.parentId === null)
  }

  const handleFinish = async (formItems) => {
    // Next page Monetico payment
    try {
      const result = await Mutation({
        variables: {
          forfaitsIds: forfaitsSelected.map(f => f.id),
          userInput: withRegisterForm ? formItems : {},
        },
      })
      // id temporary user for monetico
      const id = result.data.createTempUserForfaitSelection
      // REDIRECT TO PHP
      if (isMobile) {
        router.push(`/user/register/confirm/${id}`)
        return
      }
      if (!isAptoria) {
        window.location = `${GlobalConfig.get().baseWebsiteUrl}${GlobalConfig.get().legacyPhpHomePath}?page=inscription-confirm-exoteach&tmpId=${id}`
      }
      router.push(`/user/register/confirm/${id}`)
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e)
    }
  }

  const forfaitsCochables = mesForfaitsDisponibles && mesForfaitsDisponibles.filter(f => f.type !== ForfaitTypes.Radio)

  /* for display only */
  const handleCreditConsume = (forfaitCreditCost) => {
    if (forfaitCreditCost !== 0 && (forfaitCreditCost <= creditsAvailable)) {
      /* Removing credits */
      setCreditsAvailable(creditsAvailable - forfaitCreditCost)
    }
  }
  const handleCreditUnconsume = (forfaitCreditCost) => {
    if (forfaitCreditCost !== 0) {
      /* adding credits */
      setCreditsAvailable(creditsAvailable + forfaitCreditCost)
    }
  }

  // Check/uncheck
  const onRadioForfaitCheck = (newForfait, olderPossibleForfaitCollection) => {
    const forfaitCreditCost = newForfait && parseInt(newForfait.creditCost)
    const fff = forfaitsSelected.filter(
      (f) => !olderPossibleForfaitCollection.some((of) => f.id === of.id))
    if (newForfait) {
      /* Users check forfait */
      handleCreditConsume(forfaitCreditCost)
      setForfaitsSelected([...fff, newForfait])

      //let forfaitToDisable = forfaitsSelected.find(f => f.id === newForfait.requiredForfaitId)
      //forfaitToDisable.isDisabled = true
      //setForfaitsSelected([...forfaitsSelected, ...forfaitToDisable])

    } else {
      /* Users unselect all forfaits child */
      olderPossibleForfaitCollection.forEach(f => handleCreditUnconsume((parseInt(f.creditCost))))
      setForfaitsSelected([...fff])
    }
  }
  const onCheckForfait = (e, forfait) => {
    const otherForfaits = forfaitsSelected.filter(f => f.id !== forfait.id)
    const forfaitCreditCost = forfait && parseInt(forfait.creditCost)
    if (e.target.checked) {
      /* Users check forfait */
      // handleCreditConsume(forfaitCreditCost)
      setForfaitsSelected([...otherForfaits, forfait]) // Ajoute
    } else {
      /* Users un-check forfait */
      // handleCreditUnconsume(forfaitCreditCost)
      const forfaitsDependantsDeCeluiDecoched = forfaitsSelected.filter(f => f.requiredForfaitId === forfait.id)

      const otherForfaitsSansCeuxDependants = otherForfaits.filter(f => !(forfaitsDependantsDeCeluiDecoched.map(d => d.id)).includes(f.id))
      if (otherForfaitsSansCeuxDependants) {
        setForfaitsSelected([...otherForfaitsSansCeuxDependants]) // Enlève
      } else {
        setForfaitsSelected([...otherForfaits]) // Enlève

      }
      // Uncheck childs

    }
  }

  const [hasInit, setHasInit] = useState(false)

  /* Check default forfaits */
  useEffect(() => {
    if (forfaitsCochables && forfaitsCochables.length > 0 && !hasInit) {
      const fASelectionner = forfaitsCochables.filter(f => f.defaultChecked === true)
      if (fASelectionner && !forfaitsSelected.find(f => fASelectionner.some(fas => f.id === fas.id))) {
        setForfaitsSelected([...forfaitsSelected, ...fASelectionner])
        setHasInit(true)
      }
    }
  }, [forfaitsCochables])

  /* Calcul total price on forfaits selected change */
  useEffect(() => {
    let creditsAvailableTmp = mesCredits
    if (forfaitsSelected && forfaitsSelected.length > 0) {
      setPrixTotal(
        parseFloat(
          forfaitsSelected.map(f => {
            const forfaitCreditCost = parseInt(f.creditCost)
            if (forfaitCreditCost !== 0) {
              // If we have enough credits
              if (creditsAvailableTmp >= forfaitCreditCost) {
                // handleCreditConsume(forfaitCreditCost)
                creditsAvailableTmp -= forfaitCreditCost
                return 0.0 // this one is free lel
              }
            }
            return parseFloat(f.price)
          }).reduce((acc, price) => acc + price)))
    }
    setCreditsAvailable(creditsAvailableTmp)
    if (forfaitsSelected && forfaitsSelected.length === 0) {
      setPrixTotal(0)
    }
  }, [forfaitsSelected])

  const isDisabled = (forfait) => {
    if (forfait.isLocked) {
      return [true, null]
    }
    const hasRequiredForfait = forfait.requiredForfaitId !== null
    if (hasRequiredForfait) {
      // todo add champ de retour dans l'API pour savoir
      const forfaitRequisDispo = mesForfaitsDisponibles.find(f => f.id === forfait.requiredForfaitId)
      const forfaitRequisSelected = (forfaitsSelected.find(f => f.id === forfait.requiredForfaitId))
      // dans la liste et sélectionné
      if (forfaitRequisDispo && forfaitRequisSelected) {
        return [false, forfaitRequisDispo] // forfait requis ET sélectionné
      }
      if (forfaitRequisDispo && !forfaitRequisSelected) {
        return [true, forfaitRequisDispo] // forfait requis dispo ET PAS sélectionné
      }
      if (!forfaitRequisDispo) {
        // todo add champ de retour dans l'API pour savoir ou assumer que c'est ok
        return [false, forfaitRequisDispo]
      }
    }
    return [false, null]
  }

  const renderForfaitUnique = (forfait) => {
    const [disabled, forfaitRequis] = isDisabled(forfait)
    return (
      <Form.Item
        help={disabled && forfaitRequis && forfaitRequis.name && (
          <>
            {forfaitRequis.name} requis
          </>
        )}>
        <Checkbox
          disabled={disabled}
          value={forfait.id}
          defaultChecked={forfait.defaultChecked}
          onChange={e => onCheckForfait(e, forfait)}
        >
          <span>
            <ForfaitImage image={forfait.image} style={{ maxWidth: '70px', width: '60%', marginRight: 10 }}/>
          </span>
          <span style={{ fontSize: 20 }}>
            {forfait.name} ({forfait.price}€{renderCreditCost(forfait, withRegisterForm)})
          </span>
          <div style={{ marginLeft: 38, color: '#636363' }}>
            {forfait.description}{renderCreditGiven(forfait)}
          </div>
        </Checkbox>
      </Form.Item>
    )
  }
  if (error && !loading) {
    return <ErrorResult refetch={refetch} error={error}/>
  }

  const renderRadioForfait = (forfait) => (
    <Card style={{ marginTop: 20 }}>
      <RadioForfait
        mesGroupes={mesGroupes}
        forfaitParent={forfait}
        forfaitsSelected={forfaitsSelected}
        mesForfaitsDisponibles={mesForfaitsDisponibles}
        onSelection={onRadioForfaitCheck}
        isDisabled={isDisabled(forfait)}
        withRegisterForm={withRegisterForm}
      />
    </Card>
  )

  const renderForfaitMultiple = (forfait) => (
    <Card style={{ marginTop: 20 }}>
      <MultipleForfaits
        mesGroupes={mesGroupes}
        forfaitParent={forfait}
        mesForfaitsDisponibles={mesForfaitsDisponibles}
        onSelection={onCheckForfait}
        isDisabled={isDisabled(forfait)}
        withRegisterForm={withRegisterForm}
      />
    </Card>
  )

  const renderCreditsAvailable = () => {
    if (mesCredits && (mesCredits > 0))
      return (
        <div>
          Il vous reste <b>{creditsAvailable} crédit{creditsAvailable > 1 ? 's' : ''}</b> à utiliser.
          <br/>
        </div>
      )
    return ''
  }

  const renderChoiceSummary = () => (
    <Card title={t('YourChoice')} headStyle={cardHeadStyle}>
      {forfaitsSelected.map(forfait => (
        <div key={forfait.id}>
          - <ForfaitImage image={forfait.image} style={{ maxHeight: 16 }}/> {forfait.name}
        </div>
      ))}
      <br/>
      <Statistic
        title={t('general.total')}
        value={isMobile ? (prixTotal * 1.33) : (prixTotal)}
        precision={2}
        suffix="€"
      />
    </Card>
  )

  return (
    <>
      {loading && !error && (
        <SpinnerCentered/>
      )}
      {renderCreditsAvailable()}
      <Form
        layout="vertical"
        initialValues={{}}
        onFinish={handleFinish}
        form={form}
        size="large"
      >
        {showChoiceSummary && (
          <>
            <br/>
            {renderChoiceSummary()}
          </>
        )}
        <br/>
        {/* Si pas connecté, affiche morceau de formulaire inscription */}
        {withRegisterForm && (
          <RegisterFormItems form={form}/>
        )}
        <Button
          size="large"
          type="primary"
          block
          htmlType="submit"
          loading={loadingMut}>
          {t('general.confirm')}
        </Button>
      </Form>
    </>
  )
}
