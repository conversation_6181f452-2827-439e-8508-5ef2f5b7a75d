import {ButtonCreateCategoryOrFolder} from '@/shared/pages/cours/components/ButtonCreateCategoryOrFolder.jsx';
import ButtonCreateCourse from '@/shared/pages/cours/components/ButtonCreateCourse.jsx';
import ButtonCreatePage from '@/shared/pages/cours/components/ButtonCreatePage.jsx';
import ButtonCreateSubject from '@/shared/pages/cours/components/ButtonCreateSubject.jsx';
import { UEItemsList } from '@/shared/pages/cours/components/ListViews/UEItemsList.jsx';
import { CategoryCard } from '@/shared/pages/cours/components/PedagogicalHierarchyElements/CategoryCard.jsx';
import { CoursCard } from '@/shared/pages/cours/components/PedagogicalHierarchyElements/CoursCard.jsx';
import { Spin } from 'antd';
import { isAdmin } from '@/shared/utils/authority';
import { useQuery } from '@apollo/client';
import {
  QUERY_COURSES_IN_UE,
  QUERY_UE_CATEGORIES_FOR_UE_WITH_COURSES
} from '@/shared/graphql/cours.js';
import { ErrorResult } from '@/shared/components/ErrorResult';
import { TuteursReferentCard } from '@/shared/pages/cours/components/TuteurReferent';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { SpringListItemTransition } from '@/shared/assets/transitions/SpringListItemTransition';

export const UEContentCards = ({ ue, ueId, setCreateModuleVisible }) => {
  const { t } = useTranslation();
  const { listOrCardNavigation } = useContext(GlobalContext);

  // UECategories dans UE
  const { loading, error, data, refetch } = useQuery(QUERY_UE_CATEGORIES_FOR_UE_WITH_COURSES, {
    fetchPolicy: 'cache-and-network',
    variables: { ueId }
  });
  //Cours dans UE
  const {
    loading: loadingCourses,
    error: errorCourses,
    data: dataCourses,
    refetch: refetchCourses
  } = useQuery(QUERY_COURSES_IN_UE, {
    fetchPolicy: 'cache-and-network',
    variables: { ueId }
  });

  const refetchAll = () => {
    refetch();
    refetchCourses();
  };

  const isLoadingSomething = (loadingCourses || loading) && (!data || !dataCourses);

  // Catégories de UE parent
  const categories = data?.ueCategories;
  // UEs enfants (si dossier)
  const childrenUEs = ue?.children;
  // Cours
  const cours = dataCourses?.coursInUE;

  const ueCategoryReady = () => data && data.ueCategories;

  const renderCategories = () =>
    categories?.map((category, index) => (
      <React.Fragment key={index}>
        {(category.isVisible || isAdmin()) && (
          <div style={listOrCardNavigation === 'List' ? { width: '100%' } : {}}>
            <SpringListItemTransition
              key={category?.id}
              uniqueId={category?.id}
              initialScale={0.8}
              delayMultiplier={0.25}
              index={index}
              layout={false}
              fullWidth={listOrCardNavigation === 'List'}
            >
              <CategoryCard
                category={category}
                name={category.name}
                categoryId={category.id}
                image={category.image}
                views="0"
                loading={loading}
                ueId={ueId}
                refetch={refetch}
                size="large"
                color={ue && ue.color}
                color2={ue && ue.color2}
                description={category.description}
                order={category.order}
                isVisible={category.isVisible}
                countAccessiblesCourses={category?.countAccessiblesCourses}
                countAccessiblesExercises={category?.countAccessiblesExercises}
              />
            </SpringListItemTransition>
          </div>
        )}
      </React.Fragment>
    ));
  const renderCours = (
    cours, // Current
    targetCours // Original
  ) => (
    <React.Fragment key={cours?.id}>
      {(cours.isVisible || isAdmin()) && (
        <div style={{ display: 'flex', width: listOrCardNavigation === 'List' ? '100%' : 'auto' }}>
          <CoursCard
            cours={cours}
            targetCours={targetCours}
            color={ue && ue.color}
            color2={ue && ue.color2}
            refetch={refetchAll}
            ueType={ue && ue?.type}
          />
        </div>
      )}
    </React.Fragment>
  );

  return (
    <div style={{ width: '100%' }}>
      {/* LOADING */}
      {isLoadingSomething && <Spin />}
      {/* DONE LOADING */}
      {ueCategoryReady() && (
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignContent: 'center',
            margin: 'auto',
            justifyContent: 'center'
          }}
        >
          {!error && ue && ue.tuteurs && ue.tuteurs.length > 0 && (
            <div
              style={{
                width: '90%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <TuteursReferentCard
                tuteurs={!error && ue && ue.tuteurs}
                color={ue?.color}
                color2={ue?.color}
                ueId={ueId}
                title={t('ReferentTeacher')}
                setCreateModuleVisible={setCreateModuleVisible}
              />
            </div>
          )}
        </div>
      )}

      <div
        style={
          listOrCardNavigation === 'Card'
            ? {
                display: 'flex',
                alignContent: 'center',
                margin: 'auto',
                gap: '30px',
                flexWrap: 'wrap',
                justifyContent: 'center'
              }
            : {
                display: 'flex',
                flexWrap: 'wrap',
                alignContent: 'center'
              }
        }
      >
        {/* UE enfant */}
        {childrenUEs?.length > 0 && (
          <UEItemsList ues={childrenUEs} refetch={refetchAll} loading={loading} />
        )}

        {/* Categories list in this ue */}
        {ueCategoryReady() && renderCategories()}

        {/* Cours list in this ue */}
        {ueCategoryReady() &&
          cours?.map((cours, index) => (
            <SpringListItemTransition
              key={cours?.id}
              uniqueId={cours?.id}
              initialScale={0.8}
              delayMultiplier={0.25}
              index={index + categories?.length}
              layout={false}
              fullWidth={listOrCardNavigation === 'List'}
            >
              {renderCours(cours, cours?.targetCours)}
            </SpringListItemTransition>
          ))}
      </div>

      {/* ERROOOOR :'( */}
      {!isLoadingSomething && error ? <ErrorResult refetch={refetchAll} error={error} /> : ''}

      {/* BUTTONS FOR CREATION */}
      {isAdmin() && (
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            gap: '16px',
            flexWrap: 'wrap'
          }}
        >
          {/* Créer catégorie seulement si dans matière */}
          {!ue?.isFolder && (
            <ButtonCreateCategoryOrFolder
              refetch={refetch}
              ueId={ueId}
              //parentCategory={parentCategory}
            />
          )}

          {/* Créer matière, ou nouveau dossier, seulement si parent est un dossier et pas une matière */}
          {ue?.isFolder && (
            <>
              <ButtonCreateSubject refetch={refetch} shouldCreateFolder parentUe={ue} />
              <ButtonCreateSubject refetch={refetch} parentUe={ue} />
            </>
          )}

          {/* Créer cours seulement si matière */}
          {!ue?.isFolder && (
            <>
              <ButtonCreateCourse refetch={refetchCourses} ueId={ue?.id} />
              <ButtonCreatePage refetch={refetchCourses} ueId={ue?.id} />
              {/*
                <ButtonImportCourse refetch={refetch} ueId={ue?.id}/>
              */}
            </>
          )}
        </div>
      )}
    </div>
  );
};
