import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { HierarchyElement } from '@/shared/pages/cours/components/PedagogicalHierarchyElements/HierarchyElement.jsx';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils.js';
import React, { Fragment } from 'react';
import { useQuery } from '@apollo/client';
import { ErrorResult } from '@/shared/components/ErrorResult';
import { QUERY_MES_UES } from '@/shared/graphql/cours.js';
import { useRefetchOnLoad } from '@/shared/utils/hooks/useRefetchOnLoad.js';
import {UECardsContent} from "@/shared/pages/cours/components/UECardsContent";

export const renderUeImage = (image) => {
  if (!image) {
    return '';
  }
  return (
    <div style={{ textAlign: 'center' }}>
      <img
        src={getUrlProtectedRessource(GlobalConfig.get().FILES_URL + image)}
        style={{ maxWidth: '132px', width: '60%' }}
        alt=""/>
    </div>
  );
};

export const UEsLoadingView = ({ loading }) => (
  <HierarchyElement
    title={''}
    description=""
    coursId="1"
    qcmId="1"
    ueId={''}
    color={''}
    order={1}
    loading={loading}
  />
);

export const UECards = props => {
  const { loading, error, data, refetch } = useQuery(QUERY_MES_UES, {
    pollInterval: 20000,
  });
  useRefetchOnLoad(refetch);

  return (
    <Fragment>
      {/* DONE LOADING */}
      {loading && !error && (
        <SpinnerCentered/>
      )}
      {!loading && !error && data && data.mesUEs && (
        <div key="1">
          <UECardsContent
            data={data}
            loading={loading}
            refetch={refetch}
          />
        </div>
      )}
      {/* ERROOOOR :'( */}
      {!loading && error && (<ErrorResult refetch={refetch} error={error}/>)}
    </Fragment>
  );
};
