import Commentaires from '@/shared/components/Commentaires/Commentaires.jsx'
import { UEForumBreadCrumb } from '@/shared/components/Commentaires/UEForumBreadCrumb.jsx'
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx'
import { QUERY_COURS_POST_DETAIL } from '@/shared/graphql/cours.js'
import { QUERY_FORUM_PARENTS } from '@/shared/graphql/forum.js'
import { QUERY_QCM_POST_DETAIL } from '@/shared/graphql/qcm.js'
import { ForumBreadCrumb } from '@/shared/pages/forum/components/ForumBreadcrumb.jsx'
import { ForumType } from '@/shared/services/forum.js'
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js'
import { useQuery } from '@apollo/client'
import { Breadcrumb } from 'antd'
import React, { useRef, useState } from 'react'
import { useTranslation } from 'react-i18next';
import { Link } from 'umi/index.js'
import { ExoPullToRefresh } from '../../components/ExoPullToRefresh.jsx'

export default function(props) {
  useEffectScrollTop()
  const childRef = useRef();
  // id du post et typeId
  const { typeId, postId, type } = props.match.params
  const {t} = useTranslation();
  const { loading, error, data, refetch: refetchForumParents } = useQuery(QUERY_FORUM_PARENTS, {
    fetchPolicy: 'no-cache', variables: {
      parentId: typeId,
    },
    skip: (type !== 'FORUM'),
  })

  const { data: dataCours, refetch: refetchCoursPostDetail } = useQuery(QUERY_COURS_POST_DETAIL, {
    fetchPolicy: 'no-cache', variables: {
      id: typeId,
    },
    skip: (type !== 'COURS'),
  })
  const cours = dataCours && dataCours.cour


  const { data: dataQcm, refetch: refetchQcmPostDetail } = useQuery(QUERY_QCM_POST_DETAIL, {
    fetchPolicy: 'no-cache', variables: {
      id: typeId,
    },
    skip: (type !== 'QCM'),
  })
  const qcm = dataQcm && dataQcm.qcm

  const getForumParents = () => data && data.forumCategories && data.forumCategories.parents
  const breadCrumbName = () => {
    if (getForumParents()) {
      const [lastForum] = getForumParents().slice(-1)
      return ((lastForum && lastForum.name)) || 'Forum'
    }
    return ''
  }

  const [subject, setSubject] = useState('')

  const renderBreadCrumb = () => {
    switch (type) {
      case 'FORUM':
        return <ForumBreadCrumb type={ForumType.FORUM} forumId={typeId} parents={getForumParents()} subject={subject}/>
      case 'COURS':
        return <UEForumBreadCrumb ue={cours && cours.ueCategory.ue} ueCategory={cours && cours.ueCategory}
                                  cours={cours && cours}
        />
      case 'QCM':
        return (
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link to={`/qcm/${typeId}`}>
                {t('SeeMcq')}
              </Link>
            </Breadcrumb.Item>
          </Breadcrumb>
        )
      default:
        return <></>
    }
  }

  return (
    <ExoPullToRefresh onRefresh={async () => {
      await childRef.current.refetchCommentaires()
    }}>
      <FullMediParticlesBreadCrumb title={breadCrumbName()}/>
      {renderBreadCrumb()}
      &nbsp;
      <Commentaires
        id={typeId}
        isDetailPage
        postId={postId}
        type={type}
        ref={childRef}
        onGetSubject={(p) => {
          if (p && p.title) {
            setSubject(p.title)
          }
        }}
      />
    </ExoPullToRefresh>
  )
}