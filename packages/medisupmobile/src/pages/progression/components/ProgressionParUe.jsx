import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import {
  QUERY_UE_CATEGORY_ID_PROGRESSION,
  QUERY_UE_CATEGORY_ID_PROGRESSION_FOR_USER
} from '@/shared/graphql/progression.js';
import { getProgressionFromUe } from '@/pages/profile/components/Progression.jsx';
import { StatsItemsVusFaitsUE } from '@/pages/profile/components/StatsItemsVusFaitsUE.jsx';
import { useQuery } from '@apollo/client';
import Empty from 'antd/es/empty';
import React from 'react';
import { useTranslation } from 'react-i18next';

const getQueryProgressionByUE = userId => {
  if (userId) {
    return QUERY_UE_CATEGORY_ID_PROGRESSION_FOR_USER;
  }
  return QUERY_UE_CATEGORY_ID_PROGRESSION;
};
const getQueryOptions = (id, userId) => {
  const options = { fetchPolicy: 'no-cache' };
  if (userId) {
    return { ...options, variables: { ueId: id, userId } };
  }
  return { ...options, variables: { ueId: id } };
};

export const ProgressionParUe = ({ ue, userId = null }) => {
  const { t } = useTranslation();
  const { id } = ue;
  const myProgression = getProgressionFromUe(ue, userId);
  const { loading, error, data, refetch } = useQuery(
    getQueryProgressionByUE(userId),
    getQueryOptions(id, userId)
  );

  const ueCategories = data && data.ueCategories;

  if (!myProgression) {
    return <Empty />;
  }

  const header = (
    <div>
      <StatsItemsVusFaitsUE key={id} myProgression={myProgression} />
      <div style={{ textAlign: 'center', marginTop: 40 }}>
        <h1 style={{ fontSize: 30 }}>Résultats en {ue.name}</h1>
      </div>
    </div>
  );
  if (loading && !error && !ueCategories) {
    return (
      <div style={{ height: 400 }}>
        {header}
        <SpinnerCentered />
      </div>
    );
  }

  if (
    !error &&
    myProgression &&
    (myProgression.qcmFaits === null || parseInt(myProgression.qcmFaits) === 0)
  ) {
    return (
      <div style={{ height: 400 }}>
        {header}
        <Empty description="Faites au moins un QCM dans cette UE pour voir vos points forts et points faibles !" />
      </div>
    );
  }

  return (
    <>
      {header}
      {/* Points forts et faibles temporairement désactivés */}
      {/*
      <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-evenly' }}>
        <div style={{ width: '500px', marginBottom: 20, fontSize: 20, textAlign: 'center' }}>
          <span>Points Forts (Taux de bonnes réponses)</span>
          <JusteParCategorie userId={userId} id={'j' + id} ueCategories={ueCategories} type='juste'/>
        </div>
        <div style={{ width: '500px', fontSize: 20, textAlign: 'center' }}>
          <span>Points faible  (Taux de mauvaises réponses) </span>
          <JusteParCategorie userId={userId} id={'f' + id} ueCategories={ueCategories} type='faux'/>
        </div>
      </div>
      */}
      <div style={{ textAlign: 'center' }}>
        <h1 style={{ fontSize: 30 }}>{t('CategoryResults')} </h1>
      </div>
      <div style={{ width: 500, margin: 'auto' }}>
        {/*
        <GraphRadarMoyenne
          userId={userId}
          id={id}
          graphData={ueCategories}
          containerName={'graphradarmoyenne'}
        />
        */}
      </div>

      {/*
      <div style={{ textAlign: 'center', fontSize: 40 }}><h1>Sélectionner une ou plusieurs catégories </h1>
      </div>
      <div style={{ width: '70%', margin: 'auto' }}>
        <Select
          mode="multiple"
          style={{ width: '100%' }}
          placeholder="Sélectionner au moins une catégorie"
          defaultValue={['Glucides']}
          optionLabelProp="label"
        >
          <Option value="glucides" label="Glucides">
            <div className="demo-option-label-item">
        <span role="img" aria-label="Glucides">
          🇨🇳
        </span>
              Glucides
            </div>
          </Option>
          <Option value="thermodynamique" label="Thermodynamique">
            <div className="demo-option-label-item">
        <span role="img" aria-label="Thermodynamique">
          🇨🇳
        </span>
              Thermodynamique
            </div>
          </Option>
          <Option value="chimie" label="Chimie">
            <div className="demo-option-label-item">
        <span role="img" aria-label="Chimie">
          🇨🇳
        </span>
              Chimie
            </div>
          </Option>
          <Option value="lipides" label="Lipides">
            <div className="demo-option-label-item">
        <span role="img" aria-label="Lipides">
          🇨🇳
        </span>
              Lipides
            </div>
          </Option>
        </Select>
      </div>

      <div>
        <RepartitionReponse/>
      </div>
      */}
    </>
  );
};
