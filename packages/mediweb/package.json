{"name": "@medibox/web", "version": "5.5.2922", "private": true, "description": "", "scripts": {"analyze": "export NODE_OPTIONS=--openssl-legacy-provider ; cross-env ANALYZE=1 umi build", "build": "export NODE_OPTIONS=--openssl-legacy-provider ; umi build", "build:staging": "printf $npm_package_version > ./public/version && cross-env MOBILE=0 REACT_APP_VERSION=$npm_package_version REACT_APP_ENV=staging umi build", "build:production": "export NODE_OPTIONS=--openssl-legacy-provider ;printf $npm_package_version > ./public/version && cross-env MOBILE=0 NODE_ENV=production REACT_APP_VERSION=$npm_package_version REACT_APP_ENV=production umi build", "build:productionWithSourceMaps": "export NODE_OPTIONS=--openssl-legacy-provider ;printf $npm_package_version > ./public/version && cross-env MOBILE=0 NODE_ENV=production REACT_APP_VERSION=$npm_package_version GENERATE_SOURCEMAP=true REACT_APP_ENV=production umi build", "dev": "export NODE_OPTIONS=--openssl-legacy-provider ; npm run start:dev", "lint": "npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint:prettier": "prettier --check \"**/*\"", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write \"**/*\"", "serve:dist": "npx serve -s dist", "start": "npm run start:dev", "start:dev": "cross-env MOBILE=0 REACT_APP_VERSION=$npm_package_version REACT_APP_ENV=dev MOCK=none PORT=8001 UMI_UI=none NODE_OPTIONS='--openssl-legacy-provider' umi dev ", "start:no-mock": "cross-env MOBILE=0 REACT_APP_VERSION=$npm_package_version MOCK=none umi dev", "start:no-ui": "cross-env MOBILE=0 REACT_APP_VERSION=$npm_package_version REACT_APP_ENV=dev PORT=8001 UMI_UI=none umi dev", "start:pre": "cross-env MOBILE=0 REACT_APP_VERSION=$npm_package_version REACT_APP_ENV=pre umi dev", "start:test": "cross-env MOBILE=0 REACT_APP_VERSION=$npm_package_version REACT_APP_ENV=test MOCK=none umi dev", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tsc": "tsc", "ui": "umi ui", "sync": "pushd ../shared && npm run sync && popd", "tag": "pushd ../../ && npm version patch && git push && popd", "cypress": "$(npm bin)/cypress open", "createRelease": "npm version patch && git add . && git commit -m \"$(node -p \"require('./package.json').version\")\" && git push", "autoDeployProd": "npm run build:production && npm run deployProd", "autoDeployProdMedisupPPS": "EXOTEACH_NAME=MEDISUP_PPS npm run build:production && node deployment/ecosystem-medisup-pps.config.js", "autoDeployProdMedisupJPO": "EXOTEACH_NAME=MEDISUP_JPO npm run build:production && node deployment/ecosystem-medisup-jpo.config.js", "autoDeployRopsten": "EXOTEACH_NAME=ROPSACADEMY npm run build:production && node deployment/ecosystem-ropsten.config.js", "autoDeployTest": "EXOTEACH_NAME=ROPSACADEMY npm run build:production && node deployment/ecosystem-ropsten.config.js", "autoDeployAptoria": "EXOTEACH_NAME=APTORIA npm run build:production && node deployment/ecosystem-aptoria.config.js", "autoDeployLasPassCaraibes": "EXOTEACH_NAME=LASPASCARAIBES npm run build:production && node deployment/ecosystem-laspascaraibes.config.js production", "autoDeployMonBacDeFrancais": "EXOTEACH_NAME=MONBACDEFRANCAIS npm run build:production && node deployment/ecosystem-monbacdefrancais.config.js", "autoDeployProdPetitGrain": "EXOTEACH_NAME=PETITGRAIN npm run build:production && node deployment/ecosystem-petitgrainbigaradier.config.js", "autoDeployProdGalienLyon": "EXOTEACH_NAME=GALIEN_LYON npm run build:production && node deployment/ecosystem-galien-lyon.config.js", "autoDeployProdGalienBordeaux": "EXOTEACH_NAME=GALIEN_BORDEAUX npm run build:production && node deployment/ecosystem-galien-bordeaux.config.js", "autoDeployProdAgora": "EXOTEACH_NAME=AGORA npm run build:production && node deployment/ecosystem-agora.config.js", "autoDeployProdGalienBrest": "EXOTEACH_NAME=GALIEN_BREST npm run build:production && node deployment/ecosystem-galien-brest.config.js", "autoDeployProdGalienClermont": "EXOTEACH_NAME=GALIEN_CLERMONT npm run build:production && node deployment/ecosystem-galien-clermont.config.js", "autoDeployProdGalienDIJON": "EXOTEACH_NAME=GALIEN_DIJON npm run build:production && node deployment/ecosystem-galien-dijon.config.js", "autoDeployProdGalienGRENOBLE": "EXOTEACH_NAME=GALIEN_GRENOBLE npm run build:production && node deployment/ecosystem-galien-grenoble.config.js", "autoDeployProdGalienMontpellier": "EXOTEACH_NAME=GALIEN_MONTPELLIER npm run build:production && node deployment/ecosystem-galien-montpellier.config.js", "autoDeployProdGalienNantes": "EXOTEACH_NAME=GALIEN_NANTES npm run build:production && node deployment/ecosystem-galien-nantes.config.js", "autoDeployProdGalienRENNES": "EXOTEACH_NAME=GALIEN_RENNES npm run build:production && node deployment/ecosystem-galien-rennes.config.js", "autoDeployProdGalienStEtienne": "EXOTEACH_NAME=GALIEN_ST_ETIENNE npm run build:production && node deployment/ecosystem-galien-st-etienne.config.js", "autoDeployProdGalienStrasbourg": "EXOTEACH_NAME=GALIEN_STRASBOURG npm run build:production && node deployment/ecosystem-galien-strasbourg.config.js", "autoDeployProdGalienLille": "EXOTEACH_NAME=GALIEN_LILLE npm run build:production && node deployment/ecosystem-galien-lille.config.js", "autoDeployProdGalienTerminale": "EXOTEACH_NAME=GALIEN_TERMINALE npm run build:production && node deployment/ecosystem-galien-terminale.config.js", "autoDeployProdGalienMarseille": "EXOTEACH_NAME=GALIEN_MARSEILLE npm run build:production && node deployment/ecosystem-galien-marseille.config.js", "autoDeployProdPreVision": "EXOTEACH_NAME=PRE_VISION npm run build:production && node deployment/ecosystem-pre-vision.config.js", "autoDeployProdDiploma": "EXOTEACH_NAME=DIPLOMA npm run build:production && node deployment/ecosystem-diploma.config.js", "autoDeployProdHermione": "EXOTEACH_NAME=HERMIONE npm run build:production && node deployment/ecosystem-hermione.config.js", "autoDeployProdEnsao": "EXOTEACH_NAME=ENSAO npm run build:production && node deployment/ecosystem-ensao.config.js", "autoDeployProdBeAir": "EXOTEACH_NAME=BEAIR npm run build:production && node deployment/ecosystem-be-air.config.js", "autoDeployProdMediboxBordeaux": "EXOTEACH_NAME=MEDIBOX_BORDEAUX npm run build:production && node deployment/ecosystem-medibox-bordeaux.config.js", "autoDeployProdMediconcours": "EXOTEACH_NAME=MEDICONCOURS npm run build:production && node deployment/ecosystem-mediconcours.config.js", "autoDeployProdMediboxLille": "EXOTEACH_NAME=MEDIBOX_LILLE npm run build:production && node deployment/ecosystem-medibox-lille.config.js", "autoDeployProdMediboxMontpellier": "EXOTEACH_NAME=MEDIBOX_MONTPELLIER npm run build:production && node deployment/ecosystem-medibox-montpellier.config.js", "autoDeployProdParcoursmed": "EXOTEACH_NAME=PARCOURSMED npm run build:production && node deployment/ecosystem-parcoursmed.config.js", "autoDeployProdMediboxAmiens": "EXOTEACH_NAME=MEDIBOX_AMIENS npm run build:production && node deployment/ecosystem-medibox-amiens.config.js", "autoDeployAptoriaStaging": "REACT_APP_ENV=staging EXOTEACH_NAME=APTORIA npm run build:staging && node deployment/ecosystem-aptoria.config.js staging --update-env", "autoDeployExoteachDev": "EXOTEACH_NAME=EXOTEACH_DEV npm run build:staging && node deployment/ecosystem-exoteach-dev.config.js", "deployProd": "node deployment/ecosystem.config.js", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org exoteach --project exoteach-front ./dist && sentry-cli sourcemaps upload --org exoteach --project exoteach-front ./dist"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["defaults", "not ie <= 10"], "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/compatible": "^5.1.1", "@ant-design/cssinjs": "^1.17.0", "@ant-design/dark-theme": "^1.0.3", "@ant-design/icons": "^5.1.0", "@ant-design/pro-components": "^2.4.15", "@ant-design/pro-layout": "^7", "@ant-design/pro-table": "3.6.10", "@antv/g2": "^4.1.20", "@antv/g6": "^4.3.3", "@antv/g6-react-node": "^1.4.4", "@antv/util": "^2.0.14", "@antv/vis-predict-engine": "^0.1.1", "@apollo/client": "^3.7.14", "@cyntler/react-doc-viewer": "^1.17.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@mobiscroll/react": "^5.32.1", "@sentry/cli": "^2.43.1", "@sentry/react": "^9.14.0", "@sentry/webpack-plugin": "^3.3.1", "@stripe/stripe-js": "^1.14.0", "@types/lodash.debounce": "^4.0.6", "@types/lodash.isequal": "^4.5.5", "@types/react-router": "^5.0.2", "@umijs/route-utils": "^4.0.1", "antd": "^5.22.5", "apollo-upload-client": "^12.1.0", "apollo-utilities": "^1.3.3", "chart.js": "^3.9.1", "chartjs-plugin-annotation": "^2.2.1", "classnames": "^2.2.6", "dayjs": "^1.11.13", "dva": "^2.6.0-beta.16", "easytimer-react-hook": "^1.0.3", "easytimer.js": "^4.3.4", "emoji-mart": "^3.0.0", "framer-motion": "^11.0.23", "graphql": "^15.8.0", "graphql-ws": "^5.13.1", "html-react-parser": "^5.1.15", "html2canvas": "^1.1.4", "i18next": "^21.9.2", "i18next-browser-languagedetector": "^6.1.5", "i18next-http-backend": "^1.4.4", "inspirational-quotes": "^1.0.8", "jspdf": "^2.3.1", "katex": "^0.11.1", "konva": "^9.3.6", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "moment": "^2.24.0", "moment-timezone": "^0.5.34", "omit.js": "^1.0.2", "path-to-regexp": "2.4.0", "prop-types": "^15.5.10", "qs": "^6.9.0", "quill": "^2.0.2", "quill-auto-detect-url": "^0.2.1", "quill-mention": "^6.0.1", "quill-table-better": "1.0.9", "rc-queue-anim": "^1.8.4", "react": "^18.2.0", "react-chartjs-2": "^4.3.1", "react-countup": "^6.4.2", "react-dom": "^18.2.0", "react-google-recaptcha": "^2.1.0", "react-helmet": "^5.2.1", "react-i18next": "^11.18.6", "react-konva": "^18.2.10", "react-mathquill": "^1.0.3", "react-organizational-chart": "^2.2.0", "react-particles": "^2.9.3", "react-pdf": "^9.2.1", "react-quill-new": "^3.3.0", "react-reader": "^0.18.3", "react-simple-pull-to-refresh": "^1.2.3", "react-slidedown": "^2.4.5", "react-sortable-hoc": "^1.11.0", "react-textfit": "^1.1.1", "scorm-again": "^2.6.7", "shave": "^2.5.9", "subscriptions-transport-ws": "^0.10.0", "sweetalert2": "^9.10.12", "tsparticles": "^2.9.3", "umi": "^2.13.13", "umi-plugin-antd-icon-config": "^1.0.2", "umi-plugin-antd-theme": "^1.0.15", "umi-plugin-pro-block": "^1.3.2", "umi-plugin-react": "^1.14.10", "umi-request": "^1.3.5", "use-image": "^1.1.1", "use-long-press": "^3.2.0", "use-merge-value": "^1.0.1"}, "devDependencies": {"@ant-design/pro-cli": "^3.1.0", "@antv/data-set": "^0.11.4", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^25.1.0", "@types/lodash": "^4.14.144", "@types/qs": "^6.5.3", "@types/react": "^16.9.17", "@types/react-dom": "^16.8.4", "@types/react-helmet": "^5.0.13", "@umijs/fabric": "^2.0.2", "chalk": "^3.0.0", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "cypress": "^7.7.0", "dva-logger": "^1.0.0", "enzyme": "^3.11.0", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "express": "^4.17.1", "gh-pages": "^2.0.1", "husky": "^4.0.7", "jest-puppeteer": "^4.4.0", "jsdom-global": "^3.0.2", "lint-staged": "^10.0.0", "mockjs": "^1.0.1-beta3", "node-fetch": "^2.6.1", "pm2": "^4.3.0", "prettier": "^3.2.5", "stylelint": "^13.13.1", "uglify-js": "^3.19.3", "uglifyjs-webpack-plugin": "^2.2.0", "umi-plugin-antd-icon-config": "^1.0.2", "umi-plugin-ga": "^1.1.3", "umi-plugin-pro": "^1.0.2", "umi-types": "^0.5.9"}, "optionalDependencies": {"puppeteer": "^2.0.0"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}