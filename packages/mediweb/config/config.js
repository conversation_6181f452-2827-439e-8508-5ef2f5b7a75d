import slash from 'slash2'
import { theme } from 'antd/lib';
import { convertLegacyToken } from '@ant-design/compatible/lib';
import { getProductionConfigForApp } from './config-deploy-helper.js'
import webpackPlugin from './plugin.config'
import routes from './routes'

const { defaultAlgorithm, defaultSeed } = theme;
const mapToken = defaultAlgorithm(defaultSeed);
const v4Token = convertLegacyToken(mapToken);


const { REACT_APP_ENV, REACT_APP_VERSION, EXOTEACH_NAME, MOBILE } = process.env

// Alex met ça à false pour utiliser backend de STAGING
const UTILISER_BACKEND_LOCAL_DEV = true

const localDevConfig = {
  legacyPhpHomePath: 'app/', // UNUSED TO BE REMOVED
  serverUrl: 'http://localhost:8000/',
  graphqlServerUrl: 'http://localhost:8000/graphql',
  webSocketsGraphqlServerUrl: 'ws://localhost:8000/graphql',
  // A priori pas utilisé => c'est hard codé en back pour les ressources de QCM
  baseWebsiteUrl: 'http://localhost:8000/', // DOIT FINIR PAR '/'
  qcmBase: 'http://localhost/medibox/qcm/', // Pour images qcm (depuis système legacy)
  publicPath: '/',
  //appName: 'Aptoria',
  //appName: 'Ropsacademy',
  appName: 'Médibox',
  menuConfig: {
    hideCours: false,
    hideQcm: true,
    hideAnnales: true,
    hideForum: true,
    hideProgression: true,
    addDiagnostic: true,
  },
}
const stagingConfig = {
  legacyPhpHomePath: '', // UNUSED TO BE REMOVED
  serverUrl: 'https://test.exoteach.com/medibox2-api/',
  graphqlServerUrl: 'https://test.exoteach.com/medibox2-api/graphql',
  webSocketsGraphqlServerUrl: 'wss://test.exoteach.com/medibox2-ws/graphql',
  baseWebsiteUrl: 'https://test.exoteach.com/', // DOIT FINIR PAR '/'
  qcmBase: 'https://test.exoteach.com/qcm/',
  publicPath: '/elearning/',
  appName: 'passlastonannee',
  appId: 'passlastonannee',
  logoFilename: 'cg.png',
  menuConfig: {
    hideCours: false,
    hideQcm: false,
    hideAnnales: false,
    hideForum: false,
    hideProgression: false,
    addDiagnostic: false,
    hideEquipe: false,
  },
}

// BUILD TIME: set config for production
export const configs = {
  dev: UTILISER_BACKEND_LOCAL_DEV ? localDevConfig : stagingConfig,
  staging: getProductionConfigForApp(EXOTEACH_NAME, {env: 'staging'}),
  production: !EXOTEACH_NAME && {
    baseWebsiteUrl: 'https://medibox-marseille.fr/',
    legacyPhpHomePath: '',
    serverUrl: 'https://medibox-marseille.fr/medibox2-api/',
    graphqlServerUrl: 'https://medibox-marseille.fr/medibox2-api/graphql',
    webSocketsGraphqlServerUrl: 'wss://medibox-marseille.fr/medibox2-ws/graphql',
    publicPath: '/elearning/',
    appName: 'Médibox',
    logoFilename: 'logo2.png',
  } || getProductionConfigForApp(EXOTEACH_NAME, { env: 'production' }),

}[REACT_APP_ENV || 'dev']

export default {
  plugins: [
    ['umi-plugin-antd-icon-config', {}],
    [
      'umi-plugin-react',
      {
        antd: false, // need to disable for antd 5
        dva: {
          hmr: true, // Hot module replacement
        },
        locale: {
          enable: true,
          default: 'fr-FR',
          // default true, when it is true, will use `navigator.language` overwrite default
          baseNavigator: true,
        },
        dynamicImport: {
          loadingComponent: 'shared/components/PageLoading/index',
          webpackChunkName: false,
          level: 3,
        },
        fastClick: false,
        pwa: false,
        /*
        workboxPluginMode: 'InjectManifest',
        workboxOptions: {
          importWorkboxFrom: 'local',
          swSrc: 'path/to/service-worker.js')
        }
        */
      },
    ],
    ['umi-plugin-pro-block', {
      moveMock: false,
      moveService: false,
      modifyRequest: true,
      autoAddMenu: true, //todo change?
    },
    ],
  ],
  hash: true, // hash file suffix
  /*
  targets: {
    //ie: 11,
  },
  */
  // umi routes: https://umijs.org/zh/guide/router.html
  routes: routes,
  // Theme for antd: https://ant.design/docs/react/customize-theme
  /*
  theme: {
    //'@primary-color': '#1890ff', //default
    //'@border-radius-base': '4px', // major border radius
    //'@btn-border-radius-base': '20px',
    //'@card-radius': '10px',
  },
  */
  define: {
    REACT_APP_ENV: REACT_APP_ENV || false,
    REACT_APP_VERSION: REACT_APP_VERSION || '?',
    ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: '',
    // preview.pro.ant.design only do not use in your production ; preview.pro.ant.design。
    CONFIGS: configs,

    FILES_URL: configs.serverUrl + 'files/',
    FICHES_URL: configs.serverUrl + 'files/fiche/',
    COURS_URL: configs.serverUrl + 'files/cours/',
    BILLS_URL: configs.serverUrl + 'files/bill/',
    AVATARS_URL: configs.serverUrl + 'avatars/',
    PUBLIC_URL: configs.serverUrl,
    MOBILE: MOBILE,
  },
  ignoreMomentLocale: true,
  lessLoaderOptions: {
    javascriptEnabled: true,
    modifyVars: v4Token,
  },
  disableRedirectHoist: false,
  cssLoaderOptions: {
    modules: true,
    getLocalIdent: (context, _, localName) => {
      if (
        context.resourcePath.includes('node_modules') ||
        context.resourcePath.includes('ant.design.pro.less') ||
        context.resourcePath.includes('global.less') ||
        context.resourcePath.includes('katex.min.css')
      ) {
        return localName
      }
      const match = context.resourcePath.match(/src(.*)/)

      if (match && match[1]) {
        const antdProPath = match[1].replace('.less', '')
        const arr = slash(antdProPath)
          .split('/')
          .map(a => a.replace(/([A-Z])/g, '-$1'))
          .map(a => a.toLowerCase())
        return `antd-pro${arr.join('-')}-${localName}`.replace(/--/g, '-')
      }

      return localName
    },
  },
  manifest: {
    basePath: configs.publicPath,
  },
  chainWebpack: webpackPlugin,
  // runtimePublicPath: configs.publicPath,
  publicPath: configs.publicPath,

  // TODO => Remove avant prod
  // En local only
  //proxy:{
  //  '/proxyScorm':{
  //    target:'http://localhost:8000',
  //    changeOrigin:true,
  //    pathRewrite: (path) => path.replace(/^\/proxyScorm/, ''),
  //  },
  //},

  // proxy: proxy[REACT_APP_ENV || 'dev'],
  /*
  exportStatic: {
    htmlSuffix: true,
    dynamicRoot: true,
  },
  */

  // Dépendences ayant besoin de babel pour être compilées
  extraBabelIncludes: [
    /node_modules[\\/]react-quill-new/,
    /node_modules[\\/]quill/,
    /node_modules[\\/]use-long-press/,
    /node_modules[\\/]@sentry/,
    /node_modules[\\/]@cyntler[\\/]react-doc-viewer/,
    /node_modules[\\/]scorm-again/,
  ],

  history: 'hash',
}

