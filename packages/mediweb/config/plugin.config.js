import path from 'path';
import UglifyJsPlugin from 'uglifyjs-webpack-plugin';

const { theme } = require('antd/lib');
const { convertLegacyToken } = require('@ant-design/compatible/lib');
const { defaultAlgorithm, defaultSeed } = theme;
const mapToken = defaultAlgorithm(defaultSeed);
const v4Token = convertLegacyToken(mapToken);

function getModulePackageName(module) {
  if (!module.context) return null;
  const nodeModulesPath = path.join(__dirname, '../node_modules/');

  if (module.context.substring(0, nodeModulesPath.length) !== nodeModulesPath) {
    return null;
  }

  const moduleRelativePath = module.context.substring(nodeModulesPath.length);
  const [moduleDirName] = moduleRelativePath.split(path.sep);
  let packageName = moduleDirName; // handle tree shaking

  if (packageName && packageName.match('^_')) {
    // eslint-disable-next-line prefer-destructuring
    packageName = packageName.match(/^_(@?[^@]+)/)[1];
  }

  return packageName;
}

const webpackPlugin = config => {

  const generateSourceMap = process.env.GENERATE_SOURCEMAP === 'true' || process.env.GENERATE_SOURCEMAP === '1';
  if (generateSourceMap) {
    config.devtool('source-map');
  }

  config.optimization // share the same chunks across different modules
    .runtimeChunk(false)
    .splitChunks({
      chunks: 'async',
      name: 'vendors',
      maxInitialRequests: Infinity,
      minSize: 0,
      cacheGroups: {
        vendors: {
          test: module => {
            const packageName = getModulePackageName(module) || '';

            if (packageName) {
              return [
                'bizcharts',
                'gg-editor',
                'g6',
                '@antv',
                'l7',
                'gg-editor-core',
                'bizcharts-plugin-slider',
                'emoji-mart',
                'katex',

                'quill',
                'react-quill', 'jodit',
                'tsparticles'
              ].includes(packageName);
            }
            return false;
          },
          name(module) {
            const packageName = getModulePackageName(module);
            if (packageName) {
              if (['bizcharts', '@antv_data-set', '@antv'].indexOf(packageName) >= 0) {
                return 'viz'; // visualization package
              }
              /*
              if (['quill', 'react-quill', 'jodit'].indexOf(packageName) >= 0) {
                return 'wysiwyg';
              }


              if (['tsparticles'].indexOf(packageName) >= 0) {
                return 'particles';
              }
               */

            }
            return 'misc';
          },
        },
      },
    });


  config.resolve.alias
    .set('@', __dirname.slice(0, -('/Config'.length)) + '/src');


  // Evite d'avoir probleme compilation: Unexpected token punc «{», expected punc «(»
  config.optimization.minimizer('uglifyjs').use(UglifyJsPlugin, [
    {
      sourceMap: generateSourceMap,
      uglifyOptions: {
        ecma: 6,
        compress: true,
        mangle: true,
      },
      parallel: true, // Active la parallélisation
    },
  ]);

  //config.plugin('context-replacement').use(require('webpack').ContextReplacementPlugin, [/dayjs[/\\]locale$/, /fr/]);


/*
  config.module.rules.less = {
    test: /\.less$/,
    use: [
      'style-loader',
      'css-loader',
      'less-loader',
      {
        loader: 'less-loader',
        options: {
          lessOptions: {
            modifyVars: v4Token,
          },
        },
      },
    ],
  };
*/

};

export default webpackPlugin;