import { getProductionConfigForApp } from './config-deploy-helper.js';

const { EXOTEACH_NAME } = process.env;
const config = getProductionConfigForApp(EXOTEACH_NAME, { env: 'production' });
const { menuConfig } = config;

export default [
  {
    path: '/',
    component: '../shared/layouts/BlankLayout',
    routes: [
      /* PUBLIC ROUTES */
      {
        path: '/user/register',
        name: 'Inscription',
        hideInMenu: true,
        component: '../shared/pages/user/register'
      },
      {
        path: '/user/register/confirm/:id',
        name: 'Confirmation inscription',
        hideInMenu: true,
        component: '../shared/pages/user/register/register-confirm/index'
      },
      {
        path: '/user/register/cancel',
        name: 'Annulation inscription',
        hideInMenu: true,
        component: '../shared/pages/user/register/register-confirm/cancel'
      },
      {
        path: '/user/register/success',
        name: 'Inscription réussie',
        hideInMenu: true,
        component: '../shared/pages/user/register/register-confirm/success'
      },
      {
        path: '/user/register/:customLink',
        name: 'Inscription',
        hideInMenu: true,
        component: '../shared/pages/user/register/$register-custom$'
      },
      {
        path: '/user/login/sso/barchen/:jwt',
        name: 'Connexion',
        component: '../shared/pages/user/login/sso'
      },

      { path: '/terms', name: 'Termes et conditions', component: '../shared/pages/terms/index' },

      {
        path: '/aptoria-diag/partOne',
        name: ' Diagnostic partie 1',
        icon: 'solution',
        component: '../shared/pages/qcm/diagnostic/partOneNoHeader',
        hideInMenu: true
      },
      {
        path: '/aptoria-diag/home',
        name: ' Diagnostic',
        icon: 'solution',
        component: '../shared/pages/qcm/diagnostic/diagnostic.jsx',
        hideInMenu: true
      },

      {
        path: '/user',
        component: '../shared/layouts/UserLayout',
        // LOGIN / REGISTER
        routes: [
          { path: '/user', redirect: '/user/login' },
          { path: '/user/login', name: 'Connexion', component: '../shared/pages/user/login' },
          {
            path: '/user/register-result',
            name: 'register-result',
            icon: 'smile',
            component: '../shared/pages/user/register-result'
          },
          {
            path: '/user/createlegacysession',
            name: '',
            component: '../shared/pages/user/createlegacysession'
          },
          {
            path: '/user/forgot-password',
            name: '',
            component: '../shared/pages/user/forgot-password'
          },
          {
            path: '/user/reset-password',
            name: '',
            component: '../shared/pages/user/reset-password'
          },
          {
            path: '/user/setup-password',
            name: '',
            component: '../shared/pages/user/setup-password'
          },
          { component: '../shared/pages/404' }
        ]
        /* END PUBLIC ROUTES */
      },
      // MAIN LAYOUT (connected)
      {
        path: '/',
        component: '../layouts/BasicLayout',
        Routes: ['src/shared/pages/Authorized'],
        authority: ['ADMIN', 'USER', 'TUTEUR'],
        routes: [
          {
            path: '/home',
            name: ' Accueil',
            icon: 'home',
            component: '../shared/pages/home/<USER>',
            hideInMenu: true
          },

          // CGU
          { path: '/CGU', name: ' CGU', component: '../shared/pages/cgu/index', hideInMenu: true },

          // Formations LEGACY
          {
            path: '/formations',
            name: ' Formations',
            icon: 'book',
            component: '../shared/pages/formations/index',
            hideInMenu: true
          },
          {
            path: '/formation/:formationId',
            name: ' Formation',
            icon: 'book',
            component: '../shared/pages/formations/formation',
            hideInMenu: true
          },

          // Cours
          { path: '/cours', name: `Courses`, icon: 'book', component: '../pages/cours/index' },
          { path: '/cours/ue/:ue', component: '../pages/cours/index', hideInMenu: true },
          { path: '/cours/ue/:ue/module/:ueModuleId', component: '../shared/pages/cours/formation/DoModule/module', hideInMenu: true },
          {
            path: '/cours/ue/:ue/planning',
            component: '../shared/pages/cours/$planning',
            hideInMenu: true
          },
          {
            path: '/cours/categorie/:categorie',
            component: '../pages/cours/index',
            hideInMenu: true
          },
          { path: '/cours/:cours', component: '../pages/cours/index', hideInMenu: true },
          {
            path: '/cours/:cours/reader/:type/:doc',
            component: '../shared/pages/cours/details/$reader$',
            hideInMenu: true
          },
          {
            path: '/create/cours/:categorie?',
            name: 'Création de cours',
            component: '../shared/pages/cours/$create$',
            hideInMenu: true
          },

          // EXAMS
          {
            path: '/admin-exams/:folderId?',
            name: ' Examens',
            icon: 'book',
            component: '../shared/pages/exam/admin/exams.jsx',
            hideInMenu: true
          },
          {
            path: '/exam/:examId',
            name: ' Examen',
            icon: 'book',
            component: '../shared/pages/exam/exam.jsx',
            hideInMenu: true
          },

          {
            path: '/exam/:examId/results',
            name: ' Examens',
            icon: 'book',
            component: '../shared/pages/exam/admin/examResults.jsx',
            hideInMenu: true,
            authority: ['admin', 'ADMIN', 'tuteur', 'TUTEUR']
          },
          {
            path: '/exam/:examId/correction/:examSessionId',
            name: ' Résultat examen',
            icon: 'book',
            component: '../shared/pages/exam/examCorrection.jsx',
            hideInMenu: true
          },

          // EVENTS
          {
            path: '/admin-events/:folderId?',
            name: ' Évènements',
            icon: 'book',
            component: '../shared/pages/event/admin/allEvents.jsx',
            hideInMenu: true
          },
          {
            path: '/event/:eventId',
            name: ' Évènement',
            component: '../shared/pages/event/event.jsx',
            hideInMenu: true
          },

          // Notions
          { path: '/notions', component: '../shared/pages/notions/index', hideInMenu: true },
          {
            path: '/notion/:notionId',
            component: '../shared/pages/notions/notion',
            hideInMenu: true
          },

          // Formulaire
          { path: '/form/:uuid', component: '../shared/pages/forms/form', hideInMenu: true },

          /* APTORIA */
          {
            path: '/diagnostic/partOne',
            name: ' Diagnostic partie 1',
            icon: 'solution',
            component: '../shared/pages/qcm/diagnostic/partOne',
            hideInMenu: true
          },

          // MCQs
          {
            path: '/qcm',
            icon: 'checkSquare',
            name: 'Exercices',
            component: '../shared/pages/qcm/$index$'
          },
          {
            path: '/qcm/ue/:ue?/:categorie?',
            icon: 'checkSquare',
            name: ' QCM',
            component: '../shared/pages/qcm/$index$',
            hideInMenu: true
          },
          {
            path: '/qcm/:qcm',
            name: 'QCM',
            component: '../shared/pages/qcm/details/$qcm',
            hideInMenu: true
          },
          {
            path: '/qcm/:qcm/resultats-eleves',
            name: 'Résultats élèves',
            component: '../shared/pages/qcm/details/$resultatsEleves',
            hideInMenu: true
          },
          {
            path: '/qcm/faireqcm/:qcm',
            icon: 'checkSquare',
            name: 'QCM',
            component: '../shared/pages/qcm/$faireqcm',
            hideInMenu: true
          },
          {
            path: '/qcm/faireqcm/:qcm/session/:sessionId',
            icon: 'checkSquare',
            name: 'QCM',
            component: '../shared/pages/qcm/$faireqcm',
            hideInMenu: true
          },
          {
            path: '/qcm/faireqcm/:qcm/session/:sessionId/examSession/:examSessionId',
            icon: 'checkSquare',
            name: 'QCM',
            component: '../shared/pages/qcm/$faireqcm',
            hideInMenu: true
          },

          {
            path: '/qcm/correction/:qcm/:action?',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/qcm/correction/:qcm/session/:sessionId/:action?',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/qcm/correction/:qcm/session/:sessionId/examSession/:examSessionId/:action?',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/qcm/correction/:qcm/user/:userId/stat/:statId?',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/qcm/correction/:qcm/user/:userId/session/:sessionId?/:action?',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/qcm/correction/:qcm/stat/:statId',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },

          {
            path: '/qcm/correction-session/:sessionId',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },

          {
            path: '/qcm/correctionimprimable/:qcm',
            icon: 'checkSquare',
            name: 'Correction série imprimable',
            component: '../shared/pages/qcm/$correctionimprimable',
            hideInMenu: true
          },

          {
            path: '/qcm/annaleducours/:coursId',
            icon: 'checkSquare',
            name: "Questions d'annales",
            component: '../shared/pages/qcm/$annalesDuCours',
            hideInMenu: true
          },
          {
            path: '/qcm/annaleducours/:coursId/:action',
            icon: 'checkSquare',
            name: "Questions d'annales",
            component: '../shared/pages/qcm/$annalesDuCours',
            hideInMenu: true
          },




          {
            path: '/series',
            icon: 'checkSquare',
            name: 'Exercices',
            component: '../shared/pages/qcm/$index$',
            hideInMenu: true
          },
          {
            path: '/series/ue/:ue?/:categorie?',
            icon: 'checkSquare',
            name: ' QCM',
            component: '../shared/pages/qcm/$index$',
            hideInMenu: true
          },
          {
            path: '/serie/:qcm',
            name: 'QCM',
            component: '../shared/pages/qcm/details/$qcm',
            hideInMenu: true
          },
          {
            path: '/serie/:qcm/resultats-eleves',
            name: 'QCM: Résultats élèves',
            component: '../shared/pages/qcm/details/$resultatsEleves',
            hideInMenu: true
          },
          {
            path: '/serie/faireqcm/:qcm',
            icon: 'checkSquare',
            name: 'QCM',
            component: '../shared/pages/qcm/$faireqcm',
            hideInMenu: true
          },
          {
            path: '/serie/faireqcm/:qcm/session/:sessionId',
            icon: 'checkSquare',
            name: 'QCM',
            component: '../shared/pages/qcm/$faireqcm',
            hideInMenu: true
          },
          {
            path: '/serie/faireqcm/:qcm/session/:sessionId/examSession/:examSessionId',
            icon: 'checkSquare',
            name: 'QCM',
            component: '../shared/pages/qcm/$faireqcm',
            hideInMenu: true
          },
          {
            path: '/serie/correction/:qcm/:action?',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/serie/correction/:qcm/session/:sessionId/:action?',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/serie/correction/:qcm/session/:sessionId/examSession/:examSessionId/:action?',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/serie/correction/:qcm/user/:userId/stat/:statId?',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/serie/correction/:qcm/user/:userId/session/:sessionId?/:action?',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/serie/correction/:qcm/stat/:statId',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/serie/correction-session/:sessionId',
            icon: 'checkSquare',
            name: 'Correction série',
            component: '../shared/pages/qcm/$correction',
            hideInMenu: true
          },
          {
            path: '/serie/correctionimprimable/:qcm',
            icon: 'checkSquare',
            name: 'Correction série imprimable',
            component: '../shared/pages/qcm/$correctionimprimable',
            hideInMenu: true
          },
          {
            path: '/serie/annaleducours/:coursId',
            icon: 'checkSquare',
            name: "Questions d'annales",
            component: '../shared/pages/qcm/$annalesDuCours',
            hideInMenu: true
          },
          {
            path: '/serie/annaleducours/:coursId/:action',
            icon: 'checkSquare',
            name: "Questions d'annales",
            component: '../shared/pages/qcm/$annalesDuCours',
            hideInMenu: true
          },





          {
            path: '/generateurqcm/do',
            name: 'QCM généré',
            component: '../shared/pages/qcm/generateur/do',
            hideInMenu: true
          },
          {
            path: '/generateurqcm/do/:qcmSessionId',
            name: 'QCM généré',
            component: '../shared/pages/qcm/generateur/do',
            hideInMenu: true
          },
          {
            path: '/generateurqcm/result',
            name: 'Résultat QCM généré',
            component: '../shared/pages/qcm/generateur/result',
            hideInMenu: true
          },

          /* Challenges */
          {
            path: '/challenge/:challengeId/:action?',
            name: 'Challenge',
            component: '../shared/pages/challenge/details',
            hideInMenu: true
          },
          {
            path: '/challenge/:challengeId/do/:sessionId',
            name: 'Challenge',
            component: '../shared/pages/challenge/do',
            hideInMenu: true
          },
          {
            path: '/challenges/:challengeFolderId',
            name: 'Challenges',
            component: '../shared/pages/challenge/allChallenges',
            hideInMenu: true
          },

          {
            path: '/discussions',
            icon: 'notification',
            name: 'Forum',
            component: '../shared/pages/forum/$index$'
          },

          {
            path: '/discussions/:forumId',
            name: 'Forum',
            component: '../shared/pages/forum/$index$',
            hideInMenu: true
          },
          {
            path: '/discussions/:type/:typeId',
            name: 'Forum',
            component: '../shared/pages/forum/$index$',
            hideInMenu: true
          },
          {
            path: '/discussions/:type/:ueId/categorie/:categorie',
            name: 'Forum',
            component: '../shared/pages/forum/$index$',
            hideInMenu: true
          },

          {
            path: '/discussions/post/:type/:typeId/:postId',
            name: ' Discussion',
            component: '../shared/pages/forum/$post',
            hideInMenu: true
          },

          {
            path: '/profile',
            icon: 'trophy',
            name: 'MyProfile',
            component: '../shared/pages/profile/MainProfile'
          },
          {
            path: '/profile/:userId',
            icon: 'checkSquare',
            name: 'Profile',
            component: '../shared/pages/profile/MainProfile',
            hideInMenu: true
          },

          {
            path: '/planning',
            icon: 'calendar',
            name: 'Planning',
            component: '../shared/pages/planning/index',
            hideInMenu: true
          },

          // Methode des J
          {
            path: '/planning/revisions',
            name: 'Révisions - Méthodes des J',
            component: '../shared/pages/planning/revisions/index',
            hideInMenu: true
          },
          {
            path: '/planning/rooms',
            name: 'Planning',
            component: '../shared/pages/planning/RoomsLegacy',
            hideInMenu: true
          },

          {
            path: '/notifications',
            name: 'Toutes mes notifications',
            component: '../shared/pages/user/notifications',
            hideInMenu: true
          },

          {
            path: '/messages',
            name: 'Messages privés',
            hideInMenu: true,
            routes: [
              { path: '/messages', redirect: '/messages/conversations/:userId?' },
              {
                path: '/messages/conversations/:userId?',
                name: 'Messages privés',
                component: '../shared/pages/messages/$index$',
                hideInMenu: true
              },
              {
                path: '/messages/conversation/:conversationId',
                name: 'Conversation privée',
                component: '../shared/pages/messages/$conversation$',
                hideInMenu: true
              }
            ]
          },

          {
            name: 'account',
            icon: 'user',
            path: '/account',
            hideInMenu: true,
            routes: [
              {
                path: '/account/settings',
                name: 'Mon compte',
                component: '../shared/pages/account/settings'
              },
              {
                path: '/account/forfait',
                name: 'Mon forfait',
                component: '../shared/pages/account/forfait'
              }
            ]
          },

          // tous les tuteurs
          {
            path: '/tuteurs',
            name: 'Team',
            component: '../shared/pages/tuteurs/index',
            icon: 'team'
          },

          {
            path: '/admin',
            name: 'Administration',
            hideInMenu: true,
            authority: ['admin', 'ADMIN', 'COMMERCIAL'],
            routes: [
              { path: '/admin', redirect: '/admin/permissions' },

              {
                path: '/admin/dashboard',
                name: 'Dashboard',
                component: '../shared/pages/admin/dashboard/index'
              },

              {
                path: '/admin/groupes/:folderId?',
                name: 'Groupes',
                component: '../shared/pages/admin/groupes'
              },
              {
                path: '/admin/groupes/:folderId?/:groupId?',
                name: 'Groupes',
                component: '../shared/pages/admin/groupes'
              },
              {
                path: '/admin/paiements',
                name: 'Payment',
                component: '../shared/pages/admin/paiements/index',
                authority: ['admin', 'ADMIN', 'COMMERCIAL']
              },
              {
                path: '/admin/config',
                name: 'Configuration',
                component: '../shared/pages/admin/config/index'
              },
              {
                path: '/admin/appearance',
                name: 'Appearance',
                component: '../shared/pages/admin/config/appearance'
              },

              {
                path: '/admin/mobile-app',
                name: 'MobileApp',
                component: '../shared/pages/admin/config/mobile-app'
              },

              {
                path: '/admin/api-keys',
                name: 'ApiKeys.ApiKeys',
                component: '../shared/pages/admin/config/api'
              },

              {
                path: '/admin/api-docs',
                name: 'ApiKeys.ApiKeys',
                component: '../shared/pages/admin/config/api/docs'
              },

              {
                path: '/admin/custom-plannings',
                name: 'CustomPlanning',
                component: '../shared/pages/admin/custom-planning/index'
              },

              {
                path: '/admin/company',
                name: 'Company',
                component: '../shared/pages/admin/config/company/index'
              },
              {
                path: '/admin/template',
                name: 'Template',
                component: '../shared/pages/admin/config/template/index'
              },

              {
                path: '/admin/payment',
                name: 'Payment',
                component: '../shared/pages/admin/config/payment/index'
              },
              {
                path: '/admin/buildings',
                name: 'Buildings',
                component: '../shared/pages/admin/config/buildings/index'
              },
              {
                path: '/admin/building/:buildingId',
                name: 'Buildings',
                component: '../shared/pages/admin/config/buildings/rooms'
              },
              {
                path: '/admin/integrations',
                name: 'Integrations',
                component: '../shared/pages/admin/config/integrations/index'
              },
              {
                path: '/admin/webhooks',
                name: 'Webhooks.Webhooks',
                component: '../shared/pages/admin/webhooks/index'
              },
              {
                path: '/admin/parents',
                name: 'Parents',
                component: '../shared/pages/admin/parents/index'
              },

              {
                path: '/admin/users',
                name: 'Utilisateurs',
                component: '../shared/pages/admin/users/index'
              },
              {
                path: '/admin/users/users-properties',
                name: 'UserProperties',
                component: '../shared/pages/admin/users/adminUserProperties'
              },
              {
                path: '/admin/cours',
                name: 'Tous les cours',
                component: '../shared/pages/admin/cours/index'
              },

              {
                path: '/admin/ai-settings',
                name: 'Intelligence artificielle',
                component: '../shared/pages/admin/ai/index'
              },
              {
                path: '/admin/ai-settings/:id',
                name: 'Intelligence artificielle',
                component: '../shared/pages/admin/ai/index'
              },

              {
                path: '/admin/users-deletion-panel',
                name: 'Utilisateurs demandant à être supprimés',
                component: '../shared/pages/admin/users/accountDeletion/index'
              },

              {
                path: '/admin/groups-visualizer',
                name: 'Synthèse hiérarchique de groupes',
                component: '../shared/pages/admin/visualizer/index'
              },

              {
                path: '/admin/users/stats',
                name: 'Stats utilisateurs',
                component: '../shared/pages/admin/users/stats'
              },

              // Old users in groups: will be removed
              {
                path: '/admin/users/ingroup/:groupId',
                name: 'Utilisateurs',
                component: '../shared/pages/admin/users/index'
              },
              {
                path: '/admin/users/add',
                name: 'Ajouter un utilisateur',
                component: '../shared/pages/admin/users/add'
              },
              {
                path: '/admin/users/edit/:userId',
                name: 'Utilisateurs',
                component: '../shared/pages/admin/users/$edit'
              },

              /* Formulaires */
              {
                path: '/admin/forms',
                name: 'Forms',
                component: '../shared/pages/admin/forms/index'
              },
              {
                path: '/admin/forms/results/:id',
                name: 'Forms',
                component: '../shared/pages/admin/forms/results'
              },
              {
                path: '/admin/forms/results/:formId/:userId',
                name: 'Forms',
                component: '../shared/pages/admin/forms/specific-user-result'
              },

              {
                path: '/admin/forfaits/:folderId?',
                name: 'Offers',
                component: '../shared/pages/admin/forfaits/index'
              },
              {
                path: '/admin/forfaits-customlinks',
                name: 'Offres : liens personnalisés',
                component: '../shared/pages/admin/forfaits/customlinks'
              },

              {
                path: '/admin/antifraude',
                name: 'Antifraude',
                component: '../shared/pages/admin/antifraude/index'
              },

              /* Arborescence */
              {
                path: '/admin/permissions',
                name: 'Arborescence',
                component: '../shared/pages/admin/permissions'
              },
              {
                path: '/admin/permissions/ue/:ueId',
                name: 'Arborescence',
                component: '../shared/pages/admin/permissions'
              },
              {
                path: '/admin/permissions/categorie/:categoryId',
                name: 'Arborescence',
                component: '../shared/pages/admin/permissions'
              },

              {
                path: '/admin/challenges',
                name: 'Challenges',
                component: '../shared/pages/admin/challenges'
              },

              {
                path: '/admin/qcm/masschanges',
                name: 'QCMs changements en masse',
                component: '../shared/pages/admin/qcm/masschanges'
              },
              {
                path:'/admin/bareme',
                name:"Barèmes",
                component: '../shared/pages/admin/bareme/bareme'
              },
              {
                path:'/admin/bareme/:questionType',
                name:'Barèmes',
                component: '../shared/pages/admin/bareme/componants/subBareme'
              },
              {
                path: '/admin/qcm/config',
                name: 'Configuration générale QCMs',
                component: '../shared/pages/admin/qcm/config/config'
              },

              {
                path: '/admin/config/qcm/posttype',
                name: 'Type de posts QCMs',
                component: '../shared/pages/admin/qcm/posttype/posttype'
              },
              {
                path: '/admin/config/qcm/types',
                name: 'Admin types QCMs',
                component: '../shared/pages/admin/qcm/types-qcm/index'
              },
              {
                path: '/admin/config/titles',
                name: 'Admin titres',
                component: '../shared/pages/admin/titres/index'
              },
              {
                path: '/admin/config/folders',
                name: 'Dossiers',
                component: '../shared/pages/admin/folders/index'
              },

              {
                path: '/admin/calendars',
                name: 'Calendars',
                component: '../shared/pages/admin/calendars/index'
              },

              { path: '/admin/notifications', name: 'Notifications', component: '../shared/pages/admin/notifications/index', },

              { path: '/admin/schemas', name: 'Schemas.SchemasLibrary', component: '../shared/pages/admin/schemas/index', },
              { path: '/admin/schemas/edit/:id', name: 'Schemas.SchemasLibrary', component: '../shared/pages/admin/schemas/edit', },
            ],
          },
          { path: '/admin-schemas', name: 'Schemas.SchemasLibrary', component: '../shared/pages/admin/schemas/index', authority: ['admin', 'ADMIN', 'tuteur', 'TUTEUR'], hideInMenu: true},
          { path: '/admin-schemas/edit/:id', name: 'Schemas.SchemasLibrary', component: '../shared/pages/admin/schemas/edit', authority: ['admin', 'ADMIN', 'tuteur', 'TUTEUR'], hideInMenu: true },
          {
            path: '/tuteur-panel',
            name: 'Panneau administration tuteurs',
            hideInMenu: true,
            authority: ['admin', 'ADMIN', 'tuteur', 'TUTEUR'],
            routes: [
              { path: '/tuteur-panel', redirect: '/tuteur-panel/users' },
              {
                path: '/tuteur-panel/users',
                name: 'Utilisateurs',
                component: '../shared/pages/admin/users/tuteur-panel'
              },
              {
                path: '/tuteur-panel/sujets-sans-reponses',
                name: 'Sujets sans réponses',
                component: '../shared/pages/admin/forum/sujets-sans-reponses'
              }
            ]
          },

          {
            path: '/admin-qcm',
            name: 'Administration QCMs',
            hideInMenu: true,
            authority: ['admin', 'ADMIN', 'tuteur', 'TUTEUR'],
            routes: [
              { path: '/admin-qcm', redirect: '/admin-qcm/index' },
              {
                path: '/admin-qcm/index',
                name: 'Admin séries',
                component: '../shared/pages/admin/qcm/index'
              },
              {
                path: '/admin-qcm/edit/:qcmId/:questionId?',
                name: 'Édition série',
                component: '../shared/pages/admin/qcm/questionEdition'
              }
            ]
          },

          {
            path: '/admin-series',
            name: 'Administration séries exercices',
            hideInMenu: true,
            authority: ['admin', 'ADMIN', 'tuteur', 'TUTEUR'],
            routes: [
              { path: '/admin-series', redirect: '/admin-series/index' },
              {
                path: '/admin-series/index',
                name: 'Toutes les séries',
                component: '../shared/pages/admin/qcm/index'
              },
              {
                path: '/admin-series/edit/:qcmId/:questionId?',
                name: 'Édition série',
                // QuestionEdition
                component: '../shared/pages/admin/qcm/questionEdition'
              }
            ]
          },

          {
            path: '/admin-reports',
            name: 'Signalements utilisateur',
            hideInMenu: true,
            authority: ['admin', 'ADMIN', 'tuteur', 'TUTEUR'],
            routes: [
              { path: '/admin-reports', redirect: '/admin-reports/index' },
              {
                path: '/admin-reports/index',
                name: 'Signalements utilisateur',
                component: '../shared/pages/admin/user-reports/index'
              }
            ]
          },

          {
            path: '/admin-questions',
            name: 'Toutes les questions',
            hideInMenu: true,
            authority: ['admin', 'ADMIN', 'tuteur', 'TUTEUR'],
            routes: [
              { path: '/admin-questions', redirect: '/admin-questions/index' },
              {
                path: '/admin-questions/index',
                name: 'Toutes les questions',
                component: '../shared/pages/admin/qcm/allQuestions'
              }
            ]
          },

          {
            path: '/admin-notions',
            name: 'Administration Notions',
            hideInMenu: true,
            authority: ['admin', 'ADMIN', 'tuteur', 'TUTEUR'],
            routes: [
              { path: '/admin-notions', redirect: '/admin-notions/index' },
              {
                path: '/admin-notions/index',
                name: 'Notions - Admin',
                component: '../shared/pages/admin/notions/index'
              }
            ]
          },

          // DEFAULT WHEN CONNECTED
          {
            path: '/',
            redirect: '/home',
            authority: ['admin', 'ADMIN', 'user', 'USER', 'tuteur', 'TUTEUR']
          },
          { component: '../shared/pages/404' }
        ]
      }
    ]
  }
];
