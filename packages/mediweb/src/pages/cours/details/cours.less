/* @import '~antd/es/style/themes/default.less'; */

.phraseGentille {
  font-size: 15px;
  font-style: italic;
  opacity: 0.5;
}

.breadcrumbText {
  color: #fff !important;
  font-weight: lighter;
  text-align: center;
  font-size: 20px;
}

/* cause problem with width 100vw */
.breadcrumb{
  width: 99vw;
}

.breadcrumbTextLast {
  color: white !important;
  font-weight: 300;
  font-size: 30px;
}

@media (max-width: 900px) {
  .breadcrumbText {
  }
  .breadcrumbTextLast {
  }
}

.divParticleBreadcrumbsContainer{
  display: flex;
  align-items: center;
  justify-items: center;
  width: 100%;
  height: 100px;
  text-align: center;
  background: linear-gradient(90deg, rgba(9, 9, 121, 1) 0%, rgba(0, 212, 255, 1) 100%) no-repeat 50% 50%;
}

.title{
  color: white;
}

.divParticleBreadcrumbsContainerProfile{
  display: flex;
  align-items: center;
  justify-items: center;
  width: 100vw;
  height: 100px;
  text-align: center;
  background: linear-gradient(90deg, rgb(121, 26, 46) 0%, rgb(26, 100, 255) 100%) no-repeat 50% 50%;
}

/* cause problem with width 100vw */
.divjustParticleBreadcrumbs{
  background-size : cover;
  height: 100px;
}

.breadcrumbItem{
}

.divBreadcrumbsContainer{
}
