import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { tr } from '@/shared/services/translate.js';
import {
  QUERY_UE_CATEGORY_ID_PROGRESSION,
  QUERY_UE_CATEGORY_ID_PROGRESSION_FOR_USER
} from '@/shared/graphql/progression.js';
import { getProgressionFromUe } from '@/pages/profile/components/Progression.jsx';
import { StatsItemsVusFaitsUE } from '@/pages/profile/components/StatsItemsVusFaitsUE.jsx';
import { RepartitionErreursCorrection } from '@/shared/pages/qcm/components/correction/RepartitionErreursCorrection.jsx';
import { useQuery } from '@apollo/client';
import { Card } from 'antd';
import Empty from 'antd/es/empty';
import React from 'react';
import { useTranslation } from 'react-i18next';

const getQueryProgressionByUE = (userId) => {
  if (userId) {
    return QUERY_UE_CATEGORY_ID_PROGRESSION_FOR_USER;
  }
  return QUERY_UE_CATEGORY_ID_PROGRESSION;
};
const getQueryOptions = (id, userId) => {
  const options = { fetchPolicy: 'no-cache' };
  if (userId) {
    return { ...options, variables: { ueId: id, userId } };
  }
  return { ...options, variables: { ueId: id } };
};

export const ProgressionParUe = ({ ue, userId = null }) => {
  const { t } = useTranslation();
  const { id } = ue;
  const myProgression = getProgressionFromUe(ue, userId);
  // const { loading, error, data, refetch } = useQuery(getQueryProgressionByUE(userId), getQueryOptions(id, userId));
  //const ueCategories = data && data.ueCategories;

  if (!myProgression) {
    return <Empty />;
  }

  const header = (
    <div>
      <StatsItemsVusFaitsUE key={id} myProgression={myProgression} />
      <div style={{ textAlign: 'center', fontSize: 30, marginTop: 40 }}>
        <h1>
          {t('ResultsIn')} {ue?.[tr('name')] || ue?.name}{' '}
          {ue?.[tr('description')] || ue?.description || ''}
        </h1>
      </div>
    </div>
  );
  /*
  if (loading && !error && !ueCategories) {
    return (
      <div style={{ height: 400 }}>
        {header}
        <SpinnerCentered/>
      </div>
    );
  }
   */

  /*
    if (myProgression && (myProgression?.qcmFaits === null || parseInt(myProgression?.qcmFaits) === 0)) {
      return (
        <div style={{ height: 400 }}>
          {header}
          <Empty description={t('DoAtLeastOneExerciseInThisSubjectToSeeYourStrongPoints')}/>
        </div>
      );
    }
  */

  return (
    <>
      {header}
      {/*
      <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-evenly' }}>
        <div style={{ width: '500px', marginBottom: 20, fontSize: 20, textAlign: 'center' }}>
          <span>Points Forts (Taux de bonnes réponses)</span>
          <JusteParCategorie userId={userId} id={'j' + id} ueCategories={ueCategories} type='juste'/>
        </div>
        <div style={{ width: '500px', fontSize: 20, textAlign: 'center' }}>
          <span>Points faible  (Taux de mauvaises réponses) </span>
          <JusteParCategorie userId={userId} id={'f' + id} ueCategories={ueCategories} type='faux'/>
        </div>
      </div>
      */}

      <br />

      <RepartitionErreursCorrection userId={userId} ueId={id} shouldShowAnalysis isGlobalProgress />
      <br />

      {/* Disabled for now

      <div style={{ textAlign: 'center', fontSize: 30 }}>
        <h1>Résultats par catégorie</h1>
      </div>
      <div style={{ margin: 'auto' }}>
        <GraphRadarMoyenne
          userId={userId}
          id={id}
          graphData={ueCategories}
          containerName={'graphradarmoyenne'}
        />
      </div>
      */}
    </>
  );
};
