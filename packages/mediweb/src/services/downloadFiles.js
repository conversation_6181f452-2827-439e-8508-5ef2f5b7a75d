import { getAuthToken } from '@/shared/utils/authority.js';
import request, { extend } from 'umi-request';

export const openInBrowser = (url) => {};
const getDownloadTokenUrl = `${FILES_URL}getdownloadtoken`;

export const getSupportProtectedS3File = async (url) => {
  // fonction pour récupérer un lien d'accès S3 pour visionner le fichier
  try {
    const response = await request(url, {
      method: 'GET',
      responseType: 'json',
      headers: {
        'x-token': getAuthToken()
      }
    });

    return response?.signedUrl;
  } catch (e) {
    console.error(e);
    return null;
  }
};

export const downloadS3File = async (url) => {
  // fonction pour récupérer un lien d'accès S3 pour download le fichier
  try {
    const response = await request(url, {
      method: 'GET',
      responseType: 'json',
      headers: {
        'x-token': getAuthToken()
      }
    });
    const signedUrl = response?.signedUrl;
    if (signedUrl && typeof signedUrl === 'string' && signedUrl !== '') {
      window.open(signedUrl, '_blank') || window.location.replace(signedUrl);
      return 'ok';
    }
    return 'ko';
  } catch (e) {
    console.error(e);
    return null;
  }
};

export const getProtectedFileUrl = async (url) => {
  try {
    const request = await extend({
      responseType: 'json',
      headers: {
        // 'Access-Control-Allow-Origin': '*',
        'x-token': getAuthToken()
      }
    });
    const downloadToken = await request(getDownloadTokenUrl);
    if (downloadToken && downloadToken.token) {
      const downloadURI = `${url}?tk=${downloadToken.token}`;
      return downloadURI;
    }
    return 'ko';
  } catch (e) {
    console.error(e);
    return null;
  }
};

export const getHrefProtectedFileUrl = async (url) => {
  try {
    const urlWithToken = await getProtectedFileUrl(url);

    if (urlWithToken !== 'ko') {
      window.open(urlWithToken, '_blank') || window.location.replace(urlWithToken);
      return 'ok';
    }
    return 'ko';
  } catch (e) {
    console.error(e);
    return null;
  }
};
