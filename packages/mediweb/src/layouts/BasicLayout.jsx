import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';
import { GET_CONFIG } from '@/shared/graphql/home.js';
import { GET_ME } from '@/shared/models/user.js';
import {
  CONFIG_KEYS,
  DEFAULT_CONFIG_VARIABLES,
  getApparenceAttribute,
  getPublicSrc
} from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import { deleteLocalUserData } from '@/shared/services/user.js';
import { client } from '@/shared/utils/apolloClient';
import { ApolloProvider, useApolloClient, useQuery } from '@apollo/client';
import React, { useEffect, useRef, useState } from 'react';
import { Link, Redirect } from 'umi';
import { connect } from 'dva';
import { Button, Drawer, Layout, Result } from 'antd';
import Authorized from '@/shared/utils/Authorized';
import RightContent from '@/shared/components/GlobalHeader/RightContent';
import { getAuthorityFromRouter, tryParseJSONObject } from '@/shared/utils/utils';
import Spin from 'antd/es/spin';
import MediSpin from '@/shared/components/PageLoading/MediSpin';
import { useTranslation } from 'react-i18next';
import i18n from '@/shared/i18n.js';
import { tr } from '@/shared/services/translate';
import { isAdmin, isCommercial, isParent, isTuteur } from '@/shared/utils/authority';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { AdminMenu } from '@/shared/pages/admin/components/AdminMenu';
import AppMenu from '@/shared/layouts/components/AppMenu';
import { useLocation } from 'react-router';

const { Header, Content, Footer: AntFooter } = Layout;

/**
 * use Authorized check all menu item
 */
function usePrevious(value) {
  const ref = useRef();
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
}

const LoggedInLayout = (props) => {
  const { dispatch, children, settings } = props;
  const { t } = useTranslation();

  Spin.setDefaultIndicator(<MediSpin />);
  const {
    loading,
    data,
    error,
    refetch: refetchMe
  } = useQuery(GET_ME, { fetchPolicy: 'cache-and-network' });

  const location = useLocation();

  const {
    loading: loadingConfig,
    error: errorConfig,
    data: dataConfig,
    refetch: refetchConfig
  } = useQuery(GET_CONFIG, { fetchPolicy: 'cache-and-network', ...DEFAULT_CONFIG_VARIABLES });
  const config = dataConfig?.config;
  const webSiteName = getValueFromKeyConfigData(config, CONFIG_KEYS.WEBSITE_NAME);
  const logoInMenuBar = getValueFromKeyConfigData(config, CONFIG_KEYS.LOGO_MENUBAR);
  const appearance = tryParseJSONObject(getValueFromKeyConfigData(config, CONFIG_KEYS.APPEARANCE));
  const showWebsiteNameInBar = appearance?.showPlatformNameMenuBar;
  const hasCustomBackgroundPlatform = !!appearance?.backgroundImagePlatform;

  const [showNavigation, setShowNavigation] = useState(true);
  const [showAdminNavigation, setShowAdminNavigation] = useState(true);
  const [siderCollapsed, setSiderCollapsed] = useState(false);
  const [isSmallWindow, setIsSmallWindow] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  const [showHeaderBar, setShowHeaderBar] = useState(true);
  const [showSider, setShowSider] = useState(true);

  useEffect(() => {
    const checkMobile = () => {
      setIsSmallWindow(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setSiderCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const isAdminRoute =
      location.pathname.startsWith('/admin') || location.pathname.startsWith('/tuteur-panel');

    // Special case: inside formation module, hide everything except the content
    const isCourseModule = location.pathname.match(/^\/cours\/ue\/\d+\/module\/\d+$/);
    if (isCourseModule) {
      setShowHeaderBar(false);
      setShowSider(false);
    } else {
      setShowHeaderBar(true);
      setShowSider(true);
    }

    setShowAdminNavigation(isAdminRoute && (isAdmin() || isTuteur()));
    setShowNavigation(!isAdminRoute && !isCourseModule);
    if (isSmallWindow) setMobileMenuVisible(false);
  }, [location.pathname]);

  const onCollapse = () => {
    if (isSmallWindow) {
      setMobileMenuVisible(!mobileMenuVisible);
    } else {
      const newCollapsedState = !siderCollapsed;
      setSiderCollapsed(newCollapsedState);
      if (dispatch) {
        dispatch({
          type: 'global/changeLayoutCollapsed',
          payload: newCollapsedState
        });
      }
    }
  };

  // get children authority
  const authorized = getAuthorityFromRouter(props.route.routes, location.pathname || '/') || {
    authority: undefined
  };

  const clientApp = useApolloClient();

  const getLogo = () => {
    if (appearance?.showLogoMenuBar) {
      return <img src={getPublicSrc(logoInMenuBar)} alt={'logo'} style={{ height: 32 }} />;
    }
    return null;
  };

  const footerRender = () => <div style={{ height: 45, backgroundColor: '#ffffff' }} />;

  // Corrected background style property
  const backgroundStyleProperty = hasCustomBackgroundPlatform
    ? {
        background: `linear-gradient(to bottom, rgba(255,255,255,0.3) 70%, rgba(255,255,255,1) 100%), url(${getPublicSrc(appearance?.backgroundImagePlatform)}) no-repeat center center`,
        backgroundSize: 'cover'
      }
    : {};

  const itemRender = (route, params, routes, paths) => {
    const first = routes.indexOf(route) === 0;
    return first ? (
      <Link to={paths.join('/')}>{route.breadcrumbName}</Link>
    ) : (
      <span>{route.breadcrumbName}</span>
    );
  };

  if (!loading && !error && data?.me == null) {
    // me is null
    console.log('User is not logged in, disconnecting...');
    if (window.location.pathname !== '/user/login') {
      deleteLocalUserData(clientApp);
      return <Redirect to="/user/login" />;
    }
  }

  const navbarLabels = getApparenceAttribute(appearance, 'navbarLabels');

  // Improved menuDataRender function to handle Umi routes correctly
  const menuDataRender = (menuList) => {
    // First filter the menuList based on hideInMenu and other conditions
    const filteredMenuList = menuList
      ?.filter((item) => {
        // Skip items with hideInMenu flag
        /*
        if (item.hideInMenu) {
          return false;
        }
        */

        // Special case for parents and commercial
        if (isParent() || isCommercial()) {
          return false;
        }

        // Check if the item has a display rule in appearance
        const showItemKey = `navbarShow${item.name}`;
        return getApparenceAttribute(appearance, showItemKey) !== false; // Default to showing if not explicitly set
      })
      ?.map((item) => {
        // Process the authorized item like in the original code
        const authorizedItem = Authorized.check(item.authority, item, null);

        // Skip if not authorized
        if (!authorizedItem) {
          return null;
        }

        // Process children recursively
        const children = item.children ? menuDataRender(item.children) : [];

        // Filter out null items from children
        const filteredChildren = children.filter((child) => child !== null);

        // Translate the label
        const translatedLabel = navbarLabels?.[tr(item.name)];
        const label = translatedLabel || i18n.t(item.name);

        // Convert to Ant Design Menu item format
        return {
          key: item.path || item.name,
          label: item.path ? <Link to={item.path}>{label}</Link> : label,
          children: filteredChildren.length > 0 ? filteredChildren : undefined,
          icon: item.icon
        };
      })
      .filter((item) => item !== null); // Filter out null items

    return filteredMenuList;
  };

  // Do not show homepage when disconnected
  if (loading || !data?.me) {
    return (
      <>
        <SpinnerCentered />
      </>
    );
  }

  // Using standard Ant Design Layout
  return (
    <Layout className="layout" style={{ minHeight: '100vh', background: '#fff' }}>
      {showHeaderBar && (
        <Header
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '0 24px',
            background: '#fff',
            boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)',
            position: 'sticky',
            top: 0,
            zIndex: 1000,
            width: '100%'
          }}
        >
          <Button
            type="text"
            icon={
              siderCollapsed ? (
                <MenuUnfoldOutlined style={{ fontSize: 24 }} />
              ) : (
                <MenuFoldOutlined style={{ fontSize: 24 }} />
              )
            }
            onClick={onCollapse}
            style={{
              marginRight: 15,
              padding: '4px 0'
            }}
          />

          <Link to={'/'}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {getLogo()}
              {showWebsiteNameInBar && (
                <span
                  style={{
                    color: 'rgba(0, 0, 0, 0.85)',
                    margin: 0,
                    marginLeft: 10,
                    fontSize: 16,
                    fontWeight: 600
                  }}
                >
                  {webSiteName}
                </span>
              )}
            </div>
          </Link>
          <div style={{ marginLeft: 'auto' }}>
            <RightContent appearance={appearance} />
          </div>
        </Header>
      )}
      <Layout style={{ background: '#fff' }}>
        {showSider && (
          <>
            {!isSmallWindow ? (
              <Layout.Sider
                theme="light"
                width={showAdminNavigation ? 240 : 200}
                collapsed={siderCollapsed}
                collapsedWidth={90}
                trigger={null}
                style={{
                  background: '#fff',
                  borderRight: '1px solid #f0f0f0',
                  overflow: 'auto',
                  height: 'calc(100vh - 64px)',
                  position: 'fixed',
                  left: 0,
                  top: 64
                }}
              >
                {showNavigation && <AppMenu location={location} siderCollapsed={siderCollapsed} />}
                {showAdminNavigation && (
                  <AdminMenu
                    location={location}
                    siderCollapsed={siderCollapsed}
                    openSider={() => setSiderCollapsed(false)}
                  />
                )}
              </Layout.Sider>
            ) : (
              (showNavigation || showAdminNavigation) && (
                <Drawer
                  placement="left"
                  closable={false}
                  onClose={() => setMobileMenuVisible(false)}
                  open={mobileMenuVisible}
                  width={showAdminNavigation ? 240 : 200}
                  bodyStyle={{ padding: 0 }}
                >
                  {showNavigation && <AppMenu location={location} siderCollapsed={false} />}
                  {showAdminNavigation && <AdminMenu location={location} siderCollapsed={false} />}
                </Drawer>
              )
            )}
          </>
        )}

        <Layout
          style={{
            padding: `0 0 0 ${isSmallWindow ? '0' : siderCollapsed ? `${showNavigation || showAdminNavigation ? 90 : 0}px` : `${showNavigation ? 200 : showAdminNavigation ? 240 : 0}px`}`,
            background: '#fff',
            transition: 'padding 0.2s'
          }}
        >
          <Content
            style={{
              padding: '24px',
              ...backgroundStyleProperty,
              backgroundColor: '#fff',
              minHeight: 'calc(100vh - 64px - 45px)' // viewport height minus header and footer
            }}
          >
            <Authorized
              authority={authorized.authority}
              noMatch={
                <Result
                  status="403"
                  title="403"
                  subTitle="Désolé, vous n'avez pas l'autorisation d'accéder à cette page"
                  extra={
                    <Button type="primary">
                      <Link to="/user/login">{t('Login')}</Link>
                    </Button>
                  }
                />
              }
            >
              {children}
            </Authorized>
          </Content>
          <AntFooter style={{ padding: 0, background: '#fff' }}>{footerRender()}</AntFooter>
        </Layout>
      </Layout>
    </Layout>
  );
};

const BasicLayout = (props) => {
  return (
    <ApolloProvider client={client}>
      <LoggedInLayout {...props} />
    </ApolloProvider>
  );
};

export default connect(({ global, settings }) => ({
  collapsed: global.collapsed,
  settings
}))(BasicLayout);
