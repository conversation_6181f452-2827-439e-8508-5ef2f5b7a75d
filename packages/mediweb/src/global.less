// Global layout settings
@import './shared/layouts/globalLayout.less';
@import './shared/assets/animations.less';
@import "./shared/assets/exoquill.less";
@import "./shared/assets/clickableimage.less";
@import "./shared/components/Vidstack/vidstackStyle.less";
@import "./shared/assets/reorderElements.less";

body {
  margin: 0;
}

html,
body,
#root {
  height: 100%;
}

.exoteach-rich-text img {
  max-width: 100%;
  height: auto;
}

/* Default custom antd variables, doesnt work with custom colors */
//@border-radius-base: 4px;
@btn-border-radius-base: 20px;
@card-radius: 10px;

.ant-layout, .ant-layout-footer {
  background: #ffffff !important;
}

.ant-layout-sider-light .ant-layout-sider-trigger, .ant-layout-sider-light .ant-layout-sider-zero-width-trigger {
  border: 1px solid #dbdbdb;
}

/* Print settings */
@media print {
  @page {
    size: A4; /* DIN A4 standard, Europe */
    margin: 0;
  }

  div {
    break-inside: avoid;
  }

  body {
    padding-top: 18mm;
    padding-bottom: 18mm;
  }

  .ant-card {
    border: none !important;
    box-shadow: none;
  }
}


.ant-layout-sider-light {
  border: 1px solid #808a943b !important;
}

.ant-layout.ant-layout-has-sider > .ant-layout, .ant-layout.ant-layout-has-sider > .ant-layout-content {
  width: auto !important;
}


//////// CUSTOM FONT TEST
/*
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  src: local('Open Sans Light'), local('OpenSans-Light'), url(https://fonts.gstatic.com/s/opensans/v13/DXI1ORHCpsQm3Vp6mXoaTXhCUOGz7vYGh680lGh-uXM.woff) format('woff');
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src: local('Open Sans'), local('OpenSans'), url(https://fonts.gstatic.com/s/opensans/v13/cJZKeOuBrn4kERxqtaUH3T8E0i7KZn-EPnyo3HZu7kw.woff) format('woff');
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 600;
  src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(https://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNSnhCUOGz7vYGh680lGh-uXM.woff) format('woff');
}
body {
  font-family: "Open Sans", -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
  'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
  'Noto Color Emoji';
}
h1, h2, h3 {
  font-family: "Josefin Sans", -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
  'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
  'Noto Color Emoji';
}
*/
////////////////////////////////////

//.html2canvas-container { width: 30000px !important; height: 300000px !important; }

/* WHEN PRINTING */
@media print {
  @page {
    margin: 0
  }

  .ant-layout-footer {
    display: none !important;
  }

  /*
  add className='no-print' (or add the no-print class to an existing class statement) in your HTML that you don't want to appear in the printed version, such as your button.
  */
  .no-print, .no-print * {
    display: none !important;
  }
  .ant-layout-sider {
    display: none !important;
  }
  .ant-layout-header {
    display: none !important;
  }
  .ant-layout {
    padding: 0 !important;
  }
}


.colorWeak {
  filter: invert(80%);
}

.speedometer {
  max-height: 210px;
}

// BIGGER CHECKBOXES everywhere
.ant-checkbox-inner {
  width: 20px !important;
  height: 20px !important;
}

.ant-btn {
  white-space: normal !important;
}

.ant-btn-lg {
  white-space: normal;
  height: auto !important;
  line-height: 18px;
}

.ant-pro-base-menu-horizontal-item-text {
  font-weight: 470;
}

/* tag break spaces if too long (important for small screens) */
.ant-tag {
  white-space: break-spaces;
}

.ant-card {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
}

iframe {
  display: block;
  border-style: none;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*
input[type='text'], input[type='password'], input[type='number'], textarea {
  border-radius: 0 !important;
}
 */
/*
ul,
ol {
  list-style: none;
}
*/

@media only print {
  .ant-layout-header {
    display: none;
  }

  /* Ajouter des marges générales pour la page imprimable */
  @page {
    margin: 18mm; /*(en mm, cm, ou %) */
  }

  /* Ajuster les marges spécifiques pour ton contenu */
  /*
  body {
    margin: 20mm;
    padding: 0;
  }
  */
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

// IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

@mainBorderRadius: 20px;
@mediumBorderRadius: 15px;
@lowBorderRadius: 10px;

.ant-card-bordered {
  //border: 0 !important;
  //border: 1px solid #dadada !important
}

.ant-card-actions {
  border-bottom-right-radius: @mainBorderRadius;
  border-bottom-left-radius: @mainBorderRadius;
}

/*
// Mieux mais à terminer
.ant-select, .ant-select-selector, .ant-picker {
  border-radius: 15px !important;
}
.ant-select-dropdown {
  border-bottom-left-radius: 15px !important;
  border-bottom-right-radius: 15px !important;
}
*/

/*
.ant-select, .ant-select-selector {
  overflow-y: scroll;
}
*/

/* -- Show select scrollbar when it opens -- */
::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 9px;
}

::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: rgba(0, 0, 0, .3);
  -webkit-box-shadow: 0 0 1px rgba(205, 205, 205, .5);
}

/* -- */

.ant-picker, .ant-input-affix-wrapper, .ant-input, .ant-select-selector, .ant-dropdown-menu {

}

.ant-input-search {

}

// Quill message
.message-editor-wrapper {
  position: relative;
  border: 1px solid lightgrey !important;

  .ql-tooltip {
    top: 0 !important;
  }
}

.chat-editor-wrapper {
  position: relative;

  .ql-tooltip {
    top: -25px !important;
  }
}

.message-editor-container {
  //z-index: 999;
}

.ql-tooltip {
  border: none !important;
  box-shadow: none !important;
  position: relative !important;
  width: 100%;
}

.message-editor-container .ql-picker-options {
  //z-index: 999;
}

.message-editor-container .ql-toolbar.ql-snow {
  // position: absolute;
  //z-index: 999;
  //bottom: 0;
  width: 100%;
  //transform: translateY(100%);
  //border: 1px solid #e6e6e6 !important;
}


.message-editor-container .ql-container.ql-snow {
  //border: 1px solid #e6e6e6 !important;
  border: none !important;
  //z-index: 999;
}

.conversationMobile {
  display: flex;
}

@media (max-width: 500px) {
  .conversationMobile {
    display: none;
  }
}


// preview pdf
.modalPdf {
  width: fit-content !important;
}

.modalPdf .ant-modal-content {
  width: fit-content;
}

@import './shared/pages/messages/components/Messaging/global.less';

.row-dragging {
  background: #fafafa;
  border: 1px solid #ccc;
}

.row-dragging td {
  padding: 16px;
  visibility: hidden;
}

.row-dragging .drag-visible {
  visibility: visible;
}

.ant-comment-nested {
  margin-left: 22px !important;
}

.ant-comment-inner {
  padding: 8px 0 !important;
}

.ant-comment {
  z-index: 3 !important;
}

.ant-comment-actions {
  margin-top: 4px !important;
}

.ant-comment-content-detail img {
  max-width: 100% !important;
  margin: auto !important;
}

.ant-list-split .ant-list-header {
  border-bottom: none !important;
}

.ant-list-header .ant-list-header {
  padding-top: 0;
  padding-bottom: 4px;
}

.vertical-bar-comment {
  display: block;
  border-right: 2px solid #d5d5d5 !important;
  height: 100%;
  width: 50%;
}

.vertical-bar-container {
  box-sizing: border-box;
  display: inline-block;
  height: 100%;
  margin-left: 8px;
  vertical-align: top;
  margin-top: 45px;
  width: 16px;
}

.vertical-bar-wrapper {
  bottom: 0;
  left: 0;
  position: absolute;
  top: 0;
  z-index: 0;
}

.ant-layout-content {
  margin: 0 !important;
  /* Always full width */
  min-width: 100% !important;
  /* Fix when content has element with high width (canvas) */
  width: fit-content !important;
}

.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
  background-color: transparent !important;
}

.ant-menu-item-selected::after {
  border-bottom-color: @primary-color !important;
}

.ant-menu-horizontal > .ant-menu-item-selected {
  color: @primary-color !important;
}

// Mention CSS override
.mention {
  height: 24px;
  width: 65px;
  border-radius: 6px;
  background-color: #c3c3c361 !important;
  padding: 3px 0;
  margin-right: 2px;
  -webkit-user-select: all;
  -moz-user-select: all;
  -ms-user-select: all;
  user-select: all;
}


.dynamic-delete-button {
  position: relative;
  top: 4px;
  margin: 0 8px;
  color: #999;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s;
}

.dynamic-delete-button:hover {
  color: #777;
}

.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

[data-theme="dark"] .dynamic-delete-button {
  color: rgba(205, 205, 205, .45);
}

[data-theme="dark"] .dynamic-delete-button:hover {
  color: rgba(205, 205, 205, .65);
}


.ant-radio-wrapper {
  word-wrap: normal !important; /* Allow multi-line radio choice */
  display: inline-flex !important; // Align multi line with flexbox
  align-items: flex-start;
}

.ant-radio {
  margin-top: 3px !important; // Fix radio button vertical alignment caused by flex-start
}

.ant-form-item-label > label {
  display: inline-flex !important;
  height: auto !important; // Fix default min height
  align-items: flex-start !important; // Align label for multi line flexbox
}

.ant-form-item-control-input {
  min-height: auto !important;
}

.ant-form-item-label {
  font-weight: 450 !important;
}

.ant-checkbox-wrapper {
  word-wrap: normal !important;
  align-items: flex-start;
}

.ant-checkbox {
  //margin-top: 3px !important;
}


.g6-component-tooltip {
  width: fit-content;
  padding: 10px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.65);
  border-radius: 4px;
  box-shadow: rgb(174, 174, 174) 0 0 10px;
}

.g6-tooltip {
  border: 1px solid #e2e2e2;
  border-radius: 4px;
  font-size: 12px;
  color: #545454;
  background-color: rgba(205, 205, 205, 0.9);
  padding: 5px 5px;
  box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  max-width: 350px;
  //max-height: 300px;
}

.ant-image-img {
  width: 100%;
  //max-height: 150px;
  object-fit: contain;
}

.image-description {
  padding: 15px;
  text-align: center;
}


.ant-card-head-title {
  white-space: normal !important;
  line-height: 1.2;
}

// Hide antd checkbox if checkbox exoteach
.checkboxExoteach .ant-checkbox {
  display: none !important;
  white-space: normal;
}

// Hide antd  radio if exoteach radio
.checkboxExoteach .ant-radio {
  display: none !important;
  white-space: normal;
}

// Fix width limitation for item with image
.checkboxExoteach span {
  //display: block !important;
  //width: 100% !important;
}

// Fix notion detection taking all line width
.exo-notion-text span {
  display: inline !important;
}


.selectedCollapseTitle > .ant-collapse-header {
  background-color: #0a80ed;
  color: white !important;
}

.titleWithGradient {
  font-size: 72px;
  font-weight: 700 !important;
  background: -webkit-linear-gradient(45deg, #004fa0, #00add4 80%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.centered-text-on-image {
  position: absolute;
  top: 50%;
  left: 50%;
  color: white;
  transform: translate(-50%, -50%);
}


/* ------------------------------ */

.ant-tabs {
  overflow: visible;
}

.ant-avatar {
  /*background: #b8b8b8 !important;*/
}

/* Fix avatar group on mobile */
.ant-avatar-group {
  /*display: block;*/
}

@import "./shared/assets/mobiscroll-custom.less";

/* ========================================
   ReorderElements Styles
   ======================================== */





.ant-table .disabled-row {
  background-color: #dcdcdc;
  pointer-events: none;
}

.profile-card-avatar-wrapper {
  .profile-card-avatar-overlay {
    transition: all .3s ease-in-out;
    opacity: 0;
  }
}

.profile-card-avatar-wrapper:hover {
  .profile-card-avatar-overlay {
    opacity: 1;
  }
}

.bigSelector {
  .ant-select-selector {
    min-height: 58px;
  }
}

.lucide {
  width: 1em;
  height: 1em;
  stroke-width: 1.25;
}

.ant-tag > .lucide + span, .ant-tag > span + .lucide {
  margin-inline-start: 7px;
}

.solved-switch .ant-switch-checked {
  background: #F6FFEC !important;
}

.solved-switch .ant-switch-inner-checked {
  color: #389E0E !important;
}


@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.4);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(0, 255, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 0, 0);
  }
}

.ant-radio-group {
  font-size: inherit !important; // Fix antd text correction exercice QCU not showing text
}

.mbsc-ios.mbsc-eventcalendar .mbsc-calendar-header, .mbsc-ios.mbsc-eventcalendar .mbsc-calendar-week-days {
  background: white !important;
}

.mbsc-ios.mbsc-eventcalendar-schedule .mbsc-calendar-day, .mbsc-ios.mbsc-schedule-wrapper {
  background: white !important;
}

.mbsc-ios.mbsc-eventcalendar .mbsc-calendar-header, .mbsc-ios.mbsc-eventcalendar .mbsc-calendar-week-days {
  background: white !important;
}

.mbsc-ios.mbsc-calendar {
  z-index: 0;
}

// Permet de cacher le bouton download dans le FE Diapo pour les pdf
#diapoViewerWithoutDownload{
  #pdf-download {
    display: none !important;
  }

  #image-renderer {
    background-image: none !important;
  }

  #no-renderer-download {
    display: none !important;
  }
}

.ant-menu-light .ant-menu-item-selected {
  font-weight: 700;
}

.ant-menu .ant-menu-item .ant-menu-item-icon {
  vertical-align: -10px;
}

.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  align-items: center;
}
