import { <PERSON>ron<PERSON>ob } from 'cron';
import { AverageHistoryService } from '../graph-api/average-history/average-history-service.js'
import { syncICalCalendars } from '../graph-api/edt/calendar/syncICalCalendars';
import { UploadService } from '../graph-api/file/upload-service'
import { FormationService } from '../graph-api/formation/formation-service'
import QcmGraphService from '../graph-api/qcm/qcm-graph-service.js';
import { QCMStatsService } from '../graph-api/qcm/qcm-stats-service.js';
import { QuestionsService } from '../graph-api/qcm/questions/questions-service'
import { ScheduledTasksService } from '../graph-api/scheduled-tasks/scheduled-tasks-service.js';
import { UserService } from '../graph-api/user/user-service';
import {} from 'dotenv/config';
import { UserSessionsSynthesisService } from '../graph-api/user/user-sessions-synthesis-service.js';
import { UserPropertyService } from '../graph-api/user/user_property_service.js';
import { WebhookEventsService } from '../graph-api/webhooks/webhook-events-service';
const isDev = process.env.CURRENT_ENV === 'dev';

const TIMEZONE = 'Europe/Paris';

// Every minute
const executeScheduledTasks = new CronJob('* * * * *', async function() {
  try {
    // Toute les minutes
    await ScheduledTasksService.executeScheduledTasks();
  }
  catch (e) {
    console.error(e);
  }
}, null, true, TIMEZONE);

let updateUsersActivity = new CronJob('*/7 * * * *', async function() {
  try {
    // Toute les 5 minutes, statistiques
    await UserService.updateAllUsersActivity();
  } catch (e) {
    console.error(e);
  }
}, null, true, TIMEZONE);

let linkUserPropertiesDataIfNeeded = new CronJob('*/5 * * * *', async function() {
  try {
    // Toute les 5 minutes, statistiques
    await UserPropertyService.linkUserPropertyDataToUsers();
  } catch (e) {
    console.error(e);
  }
}, null, true, TIMEZONE);

let syncExternalCalendars = new CronJob('*/5 * * * *', async function() {
  try {
    // Toute les 5 minutes, sync calendars
    await syncICalCalendars();
  } catch (e) {
    console.error(e);
  }
}, null, true, TIMEZONE);

let processPendingWebhooks = new CronJob('*/5 * * * *', async function() {
  try {
    // Toute les 5 minutes, webhooks
    await WebhookEventsService.processPendingEvents();
  } catch (e) {
    console.error(e);
  }
}, null, true, TIMEZONE);


let updateMoyennesQcmUE =new CronJob('*/40 * * * *', async function() {
  // Toute les 30 minutes
  try {
    await QCMStatsService.updateMoyenneGeneraleUes();
    //console.log('Moyennes générales de toutes les UEs mises à jour');
  } catch (e) {
    console.error(e);
  }
}, null, true, TIMEZONE);

let updateAverageHistoryTable = new CronJob('*/60 * * * *', async function() {
  try {
    await AverageHistoryService.createAndMaintainTableMutation()
    console.log("AverageHistory table a été mise à jour")
  }catch(e){
    console.error(`Error update Average HistoryTable. Check sequelize option. e : ${e}`)
  }
}, null, true, TIMEZONE);


let updateMoyennesQcmUECategories = new CronJob('*/45 * * * *', async function() {
  // Toute les 30 minutes
  try {
    await QCMStatsService.updateMoyenneGeneraleCategories();
    //console.log('Moyennes générales de toutes les catégories mises à jour');
  } catch (e) {
    console.error(e);
  }
}, null, true, TIMEZONE);

let cleanupUploadTempFolder=new CronJob('0,45 * * * *',async function (){ // '*/40 * * *'
  try {
    await UploadService.cronDeleteUploadTempFolder({maxDurationInMinute:120})
  }catch(e){
   console.error(`Erreur Cron job : cleanupUploadTempFolder, e : ${e}`)
  }
},null,true,TIMEZONE)

let updateStatistiquesQCM = new CronJob('20 3 * * 1,4', async function() {
  //console.log('Mise à jour des notes stats qcm...');
  await QCMStatsService.updateAllScores();
  //console.log('Done note stats qcm update');
}, null, true, TIMEZONE);

// Every day at 1:00 AM
let updateGoodAnswersStatsUserSynthesisForActiveUsers = new CronJob('0 1 * * *', async function() {
  await QcmGraphService.cronUpdateGoodAnswersStatsUserSynthesisForActiveUsers(true);
}, null, true, TIMEZONE);

// Every day at 2:00 AM
let createUpdateUserSynthesis = new CronJob('0 2 * * *', async function() {
  await UserSessionsSynthesisService.autoCreateUpdateUserSynthesis();
}, null, true, TIMEZONE);

// Every day at 4:00 AM
let autoDetectNotionsFromCourses = new CronJob('0 4 * * *', async function() {
  //await NotionSynchronizationService.updateNotionsAutoAddedForPdfCourses();
  //await NotionSynchronizationService.updateAllNotionsMCQByKeywordDetection();
}, null, true, TIMEZONE);

// Every day at 5:00 AM
let checkVersionCGUVersionAndUpdateUserBase = new CronJob('0 5 * * *', async function() {
  await UserService.synchroniseWebSiteCGUWithUserDatabase()
  try {
    await QuestionsService.cronJobDeleteRejectedAiGeneratedQuestions()
  }catch(e){
    console.error(`dans CRONJOB erreur dans deleteNotValidatedAiQuestion : ${e}`)
  }
}, null, true, TIMEZONE);

// Cache formationElement => permet d'avoir le formationElementId depuis un filename => tous les matins à 4h30
let remakeFormationElementCache = new CronJob('30 4 * * *', async function (){
  try {
    await FormationService.FormationElementCacheManager.rebuildCache()
  }catch(e){
    console.error(`dans CRONJOB, erreur dans remakeFormationElementCache : ${e}`)
  }
})

export const startCrons = () => {
  console.log('[INFO] Scheduler start');

  updateUsersActivity.start();
  updateMoyennesQcmUE.start();
  updateMoyennesQcmUECategories.start();

  updateAverageHistoryTable.start();

  updateStatistiquesQCM.start();

  autoDetectNotionsFromCourses.start();

  updateGoodAnswersStatsUserSynthesisForActiveUsers.start();

  checkVersionCGUVersionAndUpdateUserBase.start();

  createUpdateUserSynthesis.start();
  executeScheduledTasks.start();

  linkUserPropertiesDataIfNeeded.start();

  processPendingWebhooks.start();

  syncExternalCalendars.start();

  cleanupUploadTempFolder.start();

  remakeFormationElementCache.start();
};


// Pour mettre à jour au déploiement, décommenter lignes correspondantes
//QCMStatsService.updateAllScores();
//QCMStatsService.updateMoyenneGeneraleUes()
//QCMStatsService.updateMoyenneGeneraleCategories()

startCrons();