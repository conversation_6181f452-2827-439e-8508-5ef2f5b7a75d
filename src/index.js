import 'dotenv/config';
import './util/sentry.js';
import { expressMiddleware } from '@apollo/server/express4';
import { json } from 'body-parser';
import cors from 'cors';
import express from 'express';
import * as admin from 'firebase-admin';
import helmet from 'helmet';
import http from 'http';
import * as mkdirp from 'mkdirp';
import moment from 'moment-timezone';
import morgan from 'morgan';
import config from './config/config.js';
import { redisClient, sequelize } from './models/index';
import externalApi from './rest/external/api.js';
import filesRouter from './rest/files';
import notionsRouter from './rest/notions.js';
import ssoRouter from './rest/sso.js';
import stripeRouter from './rest/stripe.js';
import userRouter from './rest/users';
import s3Router from './rest/s3files'
import scormRouter from './rest/scorm'
import { S3Service } from './graph-api/s3/s3-service.js'

import { apolloContext, createApolloServer } from './util/apolloServer';
import { PUBLIC_DIR, UPLOAD_FOLDER_MAP, validateExternalApiKey, validateUserRestRessource } from './util/utils';
import { validateApiKey } from './util/utils.js';
import * as Sentry from "@sentry/node";

const swaggerUi = require('swagger-ui-express');
const swaggerSpec = require('./doc/swaggerConfig');

const { graphqlUploadExpress } = require('graphql-upload');

console.log('GOOGLE_APPLICATION_CREDENTIALS: ' + process.env.GOOGLE_APPLICATION_CREDENTIALS);
export const isDev = process.env.CURRENT_ENV === 'dev';
console.log({ isDev });

//let SequelizeMagicHelper
/*
if(isDev) {
  require('appmetrics-dash').monitor();
}
*/

console.log(`[INFO] CONFIG :`);
console.log({ config });

/* EXPRESS CONFIG */
console.log(`[INFO] Express is starting... `);

const app = express();
app.set('trust proxy', 1); // trust first proxy
app.use(helmet());
app.use((req, res, next) => {
  if (req.originalUrl === '/stripe/webhook') {
    next();
  } else {
    express.json({ limit: '200mb' })(req, res, next);
  }
});
// admin
app.use(cors());
app.use(morgan(isDev ? 'dev' : 'combined', !isDev && ({
  skip: function(req, res) {
    return (res.statusCode < 400 || res.statusCode === 404 || res.statusCode === 200);
  },
})));

// REST ROUTES
app.use('/files', validateUserRestRessource, filesRouter); // Fichiers protégés
app.use('/s3File',validateUserRestRessource,s3Router);
app.use('/users', validateApiKey, userRouter); // API REST pour les utilisateurs utilisé par Exoteach (Monetico etc)
app.use('/sso', ssoRouter); // SSO API - non utilisé pour le moment
app.use('/stripe', stripeRouter); // Stripe
app.use('/notions', notionsRouter); // Notions synchronisation entre plateformes
app.use('/external/v1', validateExternalApiKey, externalApi); // External Exoteach REST API pour clients
app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec,  {
  //customCss: '.swagger-ui .topbar { display: none }'
  customSiteTitle: 'Exoteach API Docs', // Changer le titre de la page
  //customfavIcon: '/path/to/favicon.ico', // Changer l'icône fav
  customCssUrl: '/swagger/swagger-custom.css', // Changer le CSS
  //explorer: true
}));
app.use('/scorm',scormRouter)
app.use(express.static(PUBLIC_DIR)); // Pour servir les fichiers statiques (avatars utilisateurs, etc)
app.use('/qcm/uploads', express.static(UPLOAD_FOLDER_MAP.qcm)); // Pour servir les images QCM localement

// Create all folders if they don't exist
Object.keys(UPLOAD_FOLDER_MAP).forEach(key => mkdirp.sync(UPLOAD_FOLDER_MAP[key]));

// Creation du bucket S3 si il n'existe pas déjà
S3Service.initS3Module.createBuckIfNotAlreadyHere()

/* Http server */
let httpServer = http.createServer(app);
/* Create Apollo server instance using httpServer */
const {apolloServer, wsServer} = createApolloServer(httpServer);
apolloServer.start().then(() => {
  app.use(
    '/graphql',
    cors(),
    json(),
    graphqlUploadExpress(),
    expressMiddleware(apolloServer, {
      context: apolloContext,
    }),
  );
});

// Firebase initialization
let firebaseDbUrl = process.env.GOOGLE_APPLICATION_URL;
if (firebaseDbUrl && process.env.GOOGLE_APPLICATION_CREDENTIALS) {
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
    databaseURL: firebaseDbUrl, // récupérer l'addresse dans le google-services du dossier mediphone/android/app
  });
}

const bootstrapExoBack = () => {
  console.log(`[INFO] ExoBack start... `);
  httpServer.on('error', function(e) {
    console.error(e);
  });
  httpServer.listen({ port: config.PORT }, () => {
    console.log(`---------------------------------------------------`);
    console.log(`- ExoBack IS READY - ${process.env.CURRENT_ENV}`);
    console.log(`Node ${process.version} | Moment timezone: ${moment.tz.guess()}`);
    console.log(`Apollo Server on :${config.PORT}/graphql | Public express folder is under :${config.PORT}/`);
    console.log(`---------------------------------------------------`);
    if (process.send) {
      console.log(`[DEBUG] Sending process.send('ready')`);
      process.send('ready'); // Tell PM2 process is ready
    }
  });

  httpServer.setTimeout(9000000);
};

bootstrapExoBack();

process.on('SIGINT', async () => {
  try {
    await Sentry.flush(2000); // attendre 2s max
    await apolloServer?.stop(); // Cela déclenche drainHttpServer + dispose websocket
    console.log('[SIGINT] apolloServer stopped.');
    // 1. Stopper WebSocket server
    if (wsServer && wsServer.close) {
      await wsServer.close();
      console.log('[SIGINT] WebSocket fermé.');
    }
    if(sequelize?.close) {
      // Fermer la connexion à la base de données
      await sequelize.close();
      console.log('[SIGINT] Sequelize fermé.');
    }
    // Fermer le serveur HTTP, normalement apollo le fait déjà
    if (httpServer && httpServer?.close) {
      await httpServer?.close();
      console.log('[SIGINT] Serveur HTTP fermé.');
    }
    // Fermer Redis proprement
    if (redisClient && redisClient?.isOpen) {
      await redisClient.quit();
      console.log('[SIGINT] Redis fermé.');
    }
    // 4. Quitter le processus
    process.exit(0);
  } catch (err) {
    console.error('[SIGINT] Error during shutdown', err);
    process.exit(1);
  }
});