const pdfMakePrinter = require('pdfmake/src/printer');

const fontsPaths = {
  Ubuntu: {
    normal: `./public/Ubuntu-Regular.ttf`,
    bold: `./public/Ubuntu-Bold.ttf`,
  }
};

const printer = new pdfMakePrinter(fontsPaths);

/**
 * @param docDefinition document to print as pdf
 * @returns {Promise<Buffer>} pdf as binary data
 */
export const generatePdf = (docDefinition) => {

  return new Promise((success, reject) => {
    try {

      const doc = printer.createPdfKitDocument(docDefinition);

      let chunks = [];

      doc.on('data', (chunk) => {
        chunks.push(chunk);
      });

      doc.on('end', () => {
        success(Buffer.concat(chunks));
      });

      doc.end();

    } catch (err) {
      reject(err)
    }
  })
};

