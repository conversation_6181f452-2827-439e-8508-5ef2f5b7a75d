const { getNetTotal, getGrossTotal, getVATTotal } = require('./calculator.utils');

const Intl = require('intl');
const { getGross } = require('./calculator.utils.js');
const formatter = new Intl.NumberFormat('fr-FR', {
  style: 'currency',
  currency: 'EUR',
  minimumFractionDigits: 2,
});

const RIGHT = 'right';

const printPrice = (price) => formatter.format(price);
const printPercent = (ratio) => ratio * 100 + ' %';

const printLines = (lines) => lines?.map(line => [
  line?.detail, // nom produit
  //line?.endDate,
  printPrice(line?.price), // prix unitaire HT
  line?.quantity, // qqté
  printPercent(line?.vat), // TVA %
  {
    //text: printPrice(line?.quantity * line?.price), // Prix HT
    text: printPrice(getGross(line)), // Prix TTC
    alignment: RIGHT
  },
]);

const getInvoiceDocument = (invoice) => {

  return {
    content: [
      // Image
      invoice.pathToLogo ? {
        absolutePosition: {
          x: 470,
          y: 60
        }, // Ajustez les valeurs x et y selon vos besoins
        image: invoice.pathToLogo,
        width: 80,
        height: 80,
      } : undefined,

      { text: `Facture nº ${invoice.number}`, style: 'h4' },
      { text: `Date: ${invoice.date}`, style: 'h4' },
      invoice.purchaseOrder ? {
        text: `Bon de commande: ${invoice.purchaseOrder}`, style: 'h4',
      } : undefined,
      '\n\n',
      { text: 'De', style: 'h4' },
      `${invoice.company.name}`,
      `${invoice.company.address.street}`,
      `${invoice.company.address.zipCode}, ${invoice.company.address.city}`,
      `${invoice.company.email}`,
      { text: 'Client', alignment: RIGHT, style: 'h4' },
      { text: invoice.customer.name, alignment: RIGHT },
      { text: invoice.customer.address.street, alignment: RIGHT },
      { text: `${invoice.customer.address.zipCode} ${invoice.customer.address.city}`, alignment: RIGHT },
      '\n\n\n',
      '\n\n\n',

      {
        table: {
          headerRows: 1,
          widths: [
            '40%',
            //  '*',
            'auto',
            'auto',
            '*',
            '*',
          ],
          // 5 colonnes pour 5 champs
          body: [
            [
              { text: 'Désignation', style: 'h4' },
              //{ text: 'Date de fin', style: 'h4' },
              { text: 'Prix u. HT', style: 'h4' },
              { text: 'Qté', style: 'h4' },
              { text: 'TVA', style: 'h4' },
              { text: 'Prix TTC', style: 'h4', alignment: RIGHT },
            ],
            ...printLines(invoice.lines),
            ['', '', '', '', ''],
            ['', '', '', 'Total HT', { text: printPrice(getNetTotal(invoice.lines)), alignment: RIGHT }],
            ['', '', '', 'Total TVA', { text: printPrice(getVATTotal(invoice.lines)), alignment: RIGHT }],
            ['', '', '', { text: 'TOTAL TTC', style: 'h4' }, {
              text: printPrice(getGrossTotal(invoice.lines)),
              alignment: RIGHT,
            }],
          ],
        },
        layout: 'noBorders',
      },
      '\n\n\n\n\n\n\n',
      invoice.company.iban ? { text: `IBAN ${invoice.company.name}: ${invoice.company.iban}`, style: 'h4' } : undefined,
      '\n',
      // `La facture est payable sous ${invoice.paymentDelay} jours. `,
      '\n',
      {
        text: invoice.additionnalBillingInfo || '',
      }
      /*
      {
        text: 'Tout règlement effectué après expiration du délai donnera lieu, à titre de pénalité de retard, ' +
          'à l\'application d\'un intérêt égal à celui appliqué par la Banque Centrale Européenne à son opération de ' +
          'refinancement la plus récente, majoré de 10 points de pourcentage, ainsi qu\'à une indemnité forfaitaire pour' +
          ' frais de recouvrement d\'un montant de 40 €.', style: 'small',
      },
      'Les pénalités de retard sont exigibles sans qu\'un rappel soit nécessaire.',
      */
    ],
    footer: [
      {
        text: `${invoice.company.name} - ${invoice.company.address.street} - ${invoice.company.address.zipCode} - ${invoice.company.address.city}`,
        alignment: 'center',
        style: 'footer',
      },
      {
        text: `SIRET : ${invoice.company.siret} - TVA : ${invoice.company.vat} ${invoice.company.regNumber ? `- RCS : ${invoice.company.regNumber}` : ``} `,
        alignment: 'center',
        style: 'footer',
      },
      {
        text: `${invoice.company.legalForm} ${invoice.company.founds ? `au capital de ${invoice.company.founds}€` : ``}`,
        alignment: 'center',
        style: 'footer',
      },
    ],
    pageMargins: [40, 80, 40, 60],
    defaultStyle: {
      fontSize: 10,
      font: 'Ubuntu',
    },
    styles: {
      notice: {
        fontSize: 9,
      },
      h4: {
        bold: true,
        lineHeight: 1.4,
      },
      footer: {
        fontSize: 9,
        bold: true,
      },
    },
  };
};

module.exports = getInvoiceDocument;
