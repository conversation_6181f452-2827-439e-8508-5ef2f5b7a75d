import { combineResolvers } from 'graphql-resolvers';
import { Op } from 'sequelize';
import { CONFIG_KEYS } from '../../models/config.js';
import models from '../../models/index.js';
import { isAdmin, isAdminOrCommercial, isSuperAdmin } from '../authorization';
import { BillingService } from './billing-service';
import { ForfaitService } from './forfait-service.js';
import { PaymentService } from './payment-service.js';

export default {
  Query: {
    forfaits: combineResolvers(
      isAdmin,
      async (parent, args, { models, me }) => models.Forfait.findAll(),
    ),
    forfait: async (parent, { id }, { models, me }) => {
      return models.Forfait.findByPk(id);
    },
    searchForfaits: async (parent, { filter }, { models, me }) => {
      return ForfaitService.searchForfaits(filter);
    },
    forfaitBeforePayment: async (parent, params, { models, me }) => {
      return ForfaitService.forfaitBeforePayment(params, me);
    },
    mesForfaitsDisponibles: async (parent, { id, domain = null }, { models, me }) => {
      if (me && me.id) {
        return ForfaitService.getForfaitsDisponiblesPourUser(me.id, me.role);
      }
      // Forfaits public: publiés (ancienne méthode)
      /*
      return models.Forfait.findAll({
        where: { isPublished: true },
        include: models.Groupe,
        order: [['order', 'ASC']],
        exclude: ['promoCodes']
      })
      */

      // Recup les forfaits ids de config qui sont setup
      let config = await models.Config.findOne({
        where: {
          key: CONFIG_KEYS.REGISTER_PAGE_SELECTED_FORFAITS,
          domain,
        },
      });
      if (!config) {
        config = await models.Config.findOne({
          where: {
            key: CONFIG_KEYS.REGISTER_PAGE_SELECTED_FORFAITS,
            domain: null,
          },
        });
      }
      const forfaitIdsFromConfig = JSON.parse(config?.value) || [];

      return models.Forfait.findAll({
        where: {
          id: forfaitIdsFromConfig,
          //isPublished: true
        },
        include: models.Groupe,
        order: [['order', 'ASC']],
        exclude: ['promoCodes'],
      });
    },
    forfaitsFromCustomLink: async (parent, { link }, { models, me }) => {
      return await ForfaitService.getForfaitsFromCustomLink(link);
    },

    elementsInForfait: async (p, args, ctx) => ForfaitService.getElementsInForfait(args, ctx),
    elementsInForfaits: async (p, args, ctx) => ForfaitService.getElementsInForfaits(args, ctx),

    customLinksForfaits: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await models.CustomForfaitsLinks.findAll({
        where: {
          link: {
            [Op.ne]: null,
          },
        },
      }),
    ),

    payments: combineResolvers(
      isAdminOrCommercial,
      async (parent, args, ctx) => await PaymentService.getPayments(args, ctx),
    ),
  },

  CustomLinkForfait: {
    forfaits: combineResolvers(
      isAdmin,
      async (customLinkForfait, args, ctx) => await customLinkForfait.getForfaits(),
    ),
  },

  Mutation: {
    createTempUserForfaitSelection: async (_, args, { me }) => await ForfaitService.createTempUserForfaitSelection(args, me),

    createFreeUser: async (_, args, ctx) => await ForfaitService.createFreeUser(args, ctx),

    createUpdateTemporaryPayment: async (_, args, ctx) => await PaymentService.createUpdateTemporaryPayment(args, ctx),

    payLater: async (_, args, ctx) => await PaymentService.payLater(args, ctx),

    duplicateForfait: async (_, args, ctx) => await ForfaitService.duplicateForfait(args, ctx),


    regenerateBill: combineResolvers(
      isAdmin,
      async (_, args, ctx) => {
        const bill = await models.Bill.findByPk(args.id);
        await BillingService.regenerateBill(bill);
      },
    ),

    /* FORFAITS */
    createForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.createForfait(args),
    ),
    updateForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.updateForfait(args),
    ),
    deleteForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.deleteForfait(args),
    ),
    addForfaitToGroup: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.addForfaitToGroup(args),
    ),
    removeForfaitFromGroup: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.removeForfaitFromGroup(args),
    ),

    addGroupToForfaitNotification: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.addGroupToForfaitNotification(args),
    ),
    removeGroupFromForfaitNotification: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.removeGroupFromForfaitNotification(args),
    ),

    addRequiredGroupToForfait: combineResolvers(
      isAdmin, async (parent, args, ctx) => await ForfaitService.addRequiredGroupToForfait(args),
    ),
    removeRequiredGroupFromForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.removeRequiredGroupFromForfait(args),
    ),

    /*Payment*/
    updatePayment: combineResolvers(
      isSuperAdmin,
      async (parent, args, ctx) => await PaymentService.updatePayment(args, ctx),
    ),

    /* CUSTOM LINK */
    createCustomLinkForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.createCustomLinkForfait(args),
    ),
    updateCustomLinkForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.updateCustomLinkForfait(args),
    ),
    deleteCustomLinkForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.deleteCustomLinkForfait(args),
    ),
    addForfaitToCustomLink: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.addForfaitToCustomLink(args),
    ),
    removeForfaitFromCustomLink: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.removeForfaitFromCustomLink(args),
    ),

    addElementToForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.addElementToForfait(args, ctx),
    ),
    removeElementFromForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.removeElementFromForfait(args, ctx),
    ),

    forfaitMassActions: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.forfaitMassActions(args, ctx),
    ),
    /*
    addChildToForfait(forfaitId: ID!, childId: ID!): Boolean
    removeChildFromForfait(forfaitId: ID!, childId: ID!): Boolean
    */

    addChildToForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.addChildToForfait(args, ctx),
    ),
    removeChildFromForfait: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ForfaitService.removeChildFromForfait(args, ctx),
    ),
  },

  Payment: {
    forfaits: async (payment, args, ctx) => {
      return await payment.getForfaits();
    },
    // Might be null
    user: async (payment, args, ctx) => {
      return await models.User.findByPk(payment.userId);
    },
    // Might be null
    logData: async (payment, args, ctx) => {
      const log = await models.Log.findByPk(payment.logId);
      return log?.logData;
    },
    userPropertyData: async (payment, args, ctx) => {
      return models.UserPropertiesData.findAll({
        where: {
          logObjectId: payment.logId,
        },
      });
    },
    bills: async (payment, args, ctx) => {
      return models.Bill.findAll({ where: { paymentId: payment.id } });
    },
  },

  Forfait: {
    groupes: async (forfait, args, ctx) => await forfait.getGroupes(),

    groupesNotified: async (forfait, args, ctx) => {
      const fgn = await models.ForfaitGroupsNotification.findAll({
        where: {
          forfaitId: forfait.id,
        }, raw: true,
      });
      return models.Groupe.findAll({ where: { id: fgn.map(g => g.groupId) } });
    },
    requiredGroups: async (forfait, args, { models }) => {
      const forfaitt = await models.Forfait.findByPk(forfait.id);
      const groupIds = forfaitt.requiredGroups;
      return models.Groupe.findAll({ where: { id: groupIds } });
    },
    customElementsIds: async (forfait, args, ctx) => {
      return ForfaitService.getElementsIdsInForfait(forfait?.id);
    },
    childs: async (forfait, args, ctx) => {
      return await ForfaitService.getChildsForfaits(forfait.id, args, ctx);
    },
  },

};
