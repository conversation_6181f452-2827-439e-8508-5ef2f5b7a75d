import fs from 'fs';
import moment from 'moment-timezone';
import path from 'path';
import { isDev } from '../../index.js';
import models from '../../models/index.js';
import { EmailService } from '../../service/email-service.js';
import { UPLOAD_FOLDER_MAP } from '../../util/utils.js';
import { ConfigService } from '../config/config-service.js';
import { UploadService } from '../file/upload-service';
import { generatePdf } from './invoice/generate-pdf';
import getInvoiceDocument from './invoice/get-invoice-document.js';
import { writeFile } from './invoice/utils.js';

export const BillingService = {

  // ALL Bills if file does not exist
  async regenerateBillFiles() {
    const allBills = await models.Bill.findAll();
    for (const bill of allBills) {
      if (bill.file) {
        const billExist = await UploadService.checkIfFileExists(bill?.file, UPLOAD_FOLDER_MAP.bills);
        if (!billExist) {
          try {
            await BillingService.regenerateBill(bill);
            console.log('Missing bill file regenerated : ', bill.id);
          } catch (e) {
            console.error(e);
            continue;
          }
        }
      }
    }
  },
  async regenerateBill(bill) {
    try {
      const user = await models.User.findByPk(bill.userId);
      if (!user) {
        console.error('No user found for bill : ', bill.id);
        return;
      }
      // Il faut retrouver forfait choisi
      const correspondingPayment = await models.Payments.findOne({
        where: {
          userId: user.id,
          state: 'paid',
        },
      });
      if (!correspondingPayment) {
        console.error('No payment found for user : ', user.id);
        return;
      }
      const logId = correspondingPayment?.logId;
      const log = await models.Log.findByPk(logId);
      const logData = log?.logData;
      const forfaitsIds = logData?.forfait;
      for (const forfaitId of forfaitsIds) {
        try {
          const forfait = await models.Forfait.findByPk(forfaitId);
          await BillingService.createInvoiceForUser(user, forfait, logData, logId, { updateOnly: true, billId: bill.id });
        } catch (e) {
          console.error(e);
          continue;
        }
      }
    } catch (e) {
      console.error(e);
    }
  },
  // Créé factures qui n'ont pas été générées

  async generateMissingBillsFiles() {
    const paidPayemnts = await models.Payments.findAll({
      where: {
        state: 'paid',
      },
    });
    for(const payment of paidPayemnts) {
      // Check si y'a au moins un bill
      const correspondingBill = await models.Bill.findOne({
        where: {
          userId: payment.userId,
        }
      });
      if(!correspondingBill) {
        const logId = payment?.logId;
        const log = await models.Log.findByPk(logId);
        const logData = log?.logData;
        const forfaitsIds = logData?.forfait || [];
        const user = await models.User.findByPk(payment.userId);
        for (const forfaitId of forfaitsIds) {
          try {
            console.log('Creating invoice for user : ', user.id);
            const forfait = await models.Forfait.findByPk(forfaitId);
            await BillingService.createInvoiceForUser(user, forfait, logData, logId, { updateOnly: false, billId: null });
          } catch (e) {
            console.error(e);
            continue;
          }
        }
      }
    }
  },


  async createInvoiceForUserAndSendEmail(userCustomer, forfaitId = null, logData, logId) {
    if (forfaitId === null) {
      console.error('No forfaitId provided');
      return;
    }
    const forfait = await models.Forfait.findByPk(forfaitId);

    let sendBill = true;
    // Cas particulier : le forfait coute X credits et l'utilisateur a utilisé X credit on n'envoie pas la facture
    if(logData?.creditsUsed && logData?.creditsUsed !== 0) {
      sendBill = false;
    }

    const {
      outputFileName,
      companyInfos,
    } = await BillingService.createInvoiceForUser(userCustomer, forfait, logData, logId,{updateOnly:false, billId:null});

    const billFileNameToSend = sendBill ? outputFileName : null;

    return await BillingService.sendInvoiceByEmailToUser({
      userCustomer, // model
      outputFileName: billFileNameToSend, // string ou null
      forfait, // model
      companyName: companyInfos?.commercialName,
      companyInfos,
    });
  },

  async createInvoiceForUser(userCustomer, forfait, logData, logId, { updateOnly = false, billId = null }={}) {
    const DEBUG_GENERATION = false;

    const products = forfait.products;
    const paymentInfo = forfait?.paymentSettings;
    const companyId = paymentInfo?.companyId;
    const companyInfos = await ConfigService.getCompanyInfosById(companyId);
    /*
    {
       "siret":"*********",
       "rcs":"123123",
       "email":"<EMAIL>",
       "tva":"123123",
       "commercialName":"Exoteach",
       "capital":"9999",
       "addressline1":"123 Rue",
       "postcode":"34000",
       "city":"Montpel",
       "country":"Fronce",
       "responsableName":"Sylvon potit",
       "file":"Rj7gQOsXA-fox.jpg"
     }
     */
    if (!companyInfos) {
      const errorStr = `No company infos found for config : ${companyId}`;
      console.error(errorStr);
      throw new Error(errorStr);
    }

    let bill;
    if(updateOnly && billId) {
      bill = await models.Bill.findByPk(billId);
    } else {
      // Pre-create bill
      bill = await models.Bill.create({
        userId: userCustomer.id,
        file: 'generating...',
      });
    }

    if (logId) {
      const logObj = await models.Log.findByPk(logId);
      let logDataUpdated = JSON.parse(JSON.stringify(logObj?.logData));
      logDataUpdated.billId = bill.id;
      logObj.logData = logDataUpdated;
      await logObj.save();
    }

    if (isDev) {
      console.log(logData);
    }

    let additionalBillingInfo;

    if (logData?.paymentType && logData?.paymentType === 'installments') {
      const amount = (parseFloat(logData.price) / parseFloat(logData.numberOfInstallments)).toFixed(2);
      additionalBillingInfo = `Payé : ${amount}€ \n (Paiement en ${logData.numberOfInstallments} fois)\n ${paymentInfo?.additionnalBillingInfo || ''}`;
    } else {
      additionalBillingInfo = paymentInfo?.additionnalBillingInfo || '';
    }

    const logoExists = await UploadService.checkIfFileExists(companyInfos?.file, UPLOAD_FOLDER_MAP.public);

    let invoiceDate;
    if(updateOnly) {
      // Use bill createdAt date to keep it
      invoiceDate = moment(bill.createdAt).format('DD/MM/YYYY');
    } else {
      invoiceDate = moment().format('DD/MM/YYYY'); // NOW
    }
    const invoice = {
      'pathToLogo': logoExists ? `${UPLOAD_FOLDER_MAP.public}/${companyInfos?.file}` : null,
      'number': userCustomer?.id + '-' + bill.id,
      // 'purchaseOrder': 'ref-xxx',
      'date': invoiceDate,
      'additionnalBillingInfo': additionalBillingInfo,
      'company': {
        'name': companyInfos?.commercialName || '',
        'address': {
          'street': companyInfos?.addressline1,
          'zipCode': companyInfos?.postcode,
          'city': `${companyInfos?.city}  ${companyInfos?.country || ''}`,
        },
        'email': companyInfos?.email || '',
        'legalForm': companyInfos?.companyType || '',
        'ape': companyInfos?.ape || 'null',
        'vat': companyInfos?.tva || '',
        'founds': companyInfos?.capital || null,
        'regNumber': companyInfos?.rcs || '',
        'siret': companyInfos?.siret || '',
        // 'iban': '', // Possible d'ajouter IBAN
      },
      'customer': {
        'name': `${userCustomer.firstName || ''} ${userCustomer.name || ''}`,
        'address': {
          'street': `${userCustomer?.addressline1 || ''} ${userCustomer?.addressline2 || ''}`,
          'zipCode': userCustomer?.postcode || '',
          'city': userCustomer?.city || '',
        },
        'vat': null,
      },
      'lines': [
        ...products?.map(product => {
          let price = parseFloat(product.priceHt);
          /*
            if(logData?.paymentType && logData?.paymentType === 'installment') {
              price = (parseFloat(logData.price) / parseFloat(logData.numberOfInstallments)).toFixed(2)
            }
          */
          return (
            {
              detail: product?.name,
              price,
              vat: (parseFloat(product?.tvaPercent) / 100) || 0,
              quantity: 1,
              endDate: moment().format('DD/MM/YYYY'),
            }
          );
        }),
      ],
      'paymentDelay': 30,
    };

    const outputFileName = `Facture-${invoice.number}.pdf`;
    bill.name = outputFileName;
    bill.file = outputFileName;
    await bill.save();

    DEBUG_GENERATION && console.log('invoice file name:', outputFileName);

    const fullFilePath = `${UPLOAD_FOLDER_MAP.bills}/${outputFileName}`;

    const invoiceDocument = getInvoiceDocument(invoice);

    if (!invoiceDocument) {
      DEBUG_GENERATION && console.error('-- Invoice document is not defined --');
    } else {
      DEBUG_GENERATION && console.log('invoiceDocument defined:', invoiceDocument);
    }
    DEBUG_GENERATION && console.log('generating pdf...');
    const pdfData = await generatePdf(invoiceDocument);
    DEBUG_GENERATION && console.log('pdf generated');
    DEBUG_GENERATION && console.log('writing file...');
    const result = await writeFile(fullFilePath, pdfData);
    DEBUG_GENERATION && console.log('file written');

    return {
      companyInfos,
      outputFileName,
    };
  },

  async sendInvoiceByEmailToUser(
    {
      userCustomer, // model
      outputFileName, // string
      forfait, // model
      companyName = '',
      companyInfos,
    }) {
    const emailService = new EmailService();

    return await emailService.sendWelcomeEmailWithBill(
      {
        email: userCustomer?.email,
        subject: forfait?.emailSettings?.subject,
        html: forfait?.emailSettings?.body,
        billFilename: outputFileName,
        otherFileNames: forfait?.emailSettings?.attachments?.map(a => a.file) || [],
      },
      companyName,
      companyInfos,
    );
  },


};