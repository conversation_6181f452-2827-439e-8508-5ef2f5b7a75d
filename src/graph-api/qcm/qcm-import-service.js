import { GraphQLError } from 'graphql';
import xlsx from 'node-xlsx';
import { ELEMENTS_TYPE } from '../../models/formation/formation_element.js'
import { LOG_OPERATIONS } from '../../models/log/log.js'
import { McqScaleQuestionType } from '../../models/qcm/mcq_scale.js';
import { UPLOAD_FOLDER_MAP } from '../../util/utils.js';
import { AI_CREATION_TYPE } from '../chat-gpt/chat-gpt-service.js'
import { ExamService } from '../exam/exam-service.js';
import { FileService } from '../file/file-service.js';
import { UploadService } from '../file/upload-service.js';
import { FormationService } from '../formation/formation-service'
import LogService from '../log/log-service.js'
import { UserStatsService } from '../user/userStats-service.js';
import { alphabetArray, QCMService, QuestionAnswerType } from './qcm-service.js'
import { QuestionsService } from './questions/questions-service.js';
import { McqScaleService } from './scales/mcq-scale-service.js';
import models from '../../models/index.js';
import moment from 'moment';
import cloneDeep from 'lodash.clonedeep';

const fs = require('fs');
const csvParser = require('csv-parser');

export const QcmImportService = {

  // Temporary function to import questions
  /*
  async linkDifficultyToTypeBA() {
    const allQuestions = await models.Question.findAll();
    for(const question of allQuestions) {
      const difficulty = question.definedDifficulty;
      if(difficulty) {
        let typeId;
        if(difficulty === 1) {
          typeId = 7;
        }
        if(difficulty === 2) {
          typeId = 8;
        }
        if(difficulty === 3) {
          typeId = 9;
        }
        await models.QuestionTypeQcm.create({
          typeQcmId: typeId,
          questionId: question.id_question,
        })
      }
    }
    return true;
  },
  async updateFilesFroamtionBA() {
    const elements = await models.FormationElement.findAll({
      where: {
        image: {
          [Op.not]: null,
        },
      },
    });
    for (const element of elements) {
      let fileName = element.image;
      if (fileName) {
        if (element.questionId) {
          let filenameCleaned = fileName.replace('\/uploads\/questions\/', '');
          element.image = filenameCleaned;
          await element.save();
        } else if (element.footerQuestionId) {
          let filenameCleaned = fileName.replace('\/uploads\/explanations\/', '');
          element.image = filenameCleaned;
          await element.save();
        } else {
          console.log('no questionId or footerQuestionId');
        }
      }
    }
    return true;
  },
  importBeAirDataToExoteach: async () => {
    try {
      console.log('start importing');
      // Delete all questions
      try {
        await models.FormationElement.destroy({ where: {} });
        await models.QuestionAnswers.destroy({ where: {} });
        await models.QuestionTypeQcm.destroy({ where: {} });
        await models.Question.destroy({ where: {} });
      } catch (error) {
        console.log('error deleting questions', error);
      }


      const questionsBeAir = await models.QuestionsBeAirTmp.findAll();
      console.log(questionsBeAir.length + ' questions BA to import');

      const scale = await models.McqScale.findOne({
        where: {
          isDefault: true,
        },
      });

      for (const q of questionsBeAir) {
        // Map to question, answers, etc.
        const beAirCategId = q.chapter_id;
        if (beAirCategId === null) {
          console.log('no chapter_id for question oldId=' + q.id);
        }
        // find corresponding category
        const chapterBeAir = await models.Chapters_be_air_tmp.findByPk(beAirCategId);

        const category = await models.UECategory.findOne({
          where: {
            name: chapterBeAir?.nameFr,
          },
        });

        const newQuestion = await models.Question.create({
          question: q.text_fr,
          question_en: q.text,
          isPublished: q.to_validate,
          isAnswerFreeText: q.isFreeText,
          isCheckbox: false, // Only radio buttons here
          mcqScaleId: scale.id,
          definedDifficulty: q.difficulty,
          id_sous_categorie: category?.id,

          date_creation: new Date(),
          date_modif: new Date(),
        });
        if (!category) {
          console.log(`No corresponding category found for question newId=${newQuestion.id_question}, oldId=${q.id}`);
        }
        // Header element image
        // url_image_q: q.picFile,
        if (q.picFile) {
          const headerElementImage = await models.FormationElement.create({
            type: 'image',
            image: q.picFile, // TODO check path !!
            questionId: newQuestion.id_question,
          });
          // CHANGE ORDRE
          headerElementImage.order = headerElementImage.id;
          await headerElementImage.save();
        }

        // Footer element image
        // url_image_q: q.explanations_picFile,
        if (q.explanations_picFile) {
          const footerElementImage = await models.FormationElement.create({
            type: 'image',
            image: q.explanations_picFile, // TODO check path !!
            footerQuestionId: newQuestion.id_question,
          });
          // CHANGE ORDRE
          footerElementImage.order = footerElementImage.id;
          await footerElementImage.save();
        }
        if (q.explanations || q.explanations_fr) {
          const elementExpli = await models.FormationElement.create({
            type: 'callout',
            text: 'info',
            name: q.explanations_fr,
            name_en: q.explanations,
            footerQuestionId: newQuestion.id_question,
            settings: {},
          });
          // CHANGE ORDRE
          elementExpli.order = elementExpli.id;
          await elementExpli.save();
        }

        const answerA = await models.QuestionAnswers.create({
          text: q.answer_a_fr,
          text_en: q.answer_a,
          isTrue: q.good_answer === 0,
          questionId: newQuestion.id_question,
        });
        const answerB = await models.QuestionAnswers.create({
          text: q.answer_b_fr,
          text_en: q.answer_b,
          isTrue: q.good_answer === 1,
          questionId: newQuestion.id_question,
        });
        const answerC = await models.QuestionAnswers.create({
          text: q.answer_c_fr,
          text_en: q.answer_c,
          isTrue: q.good_answer === 2,
          questionId: newQuestion.id_question,
        });

        // Link to question type (other table)
        const questionCategQuestionForCurrent = await models.QuestionsCategoryQuestionBeAirTmp.findAll({
          where: {
            questions_id: q.id,
          },
        });
        for (const qcq of questionCategQuestionForCurrent) {
          //1: B1 => 2
          //2: B1.1 => 3
          //3: B1.2 => 4
          //4: B2 => 5
          //5: B1.3 => 6
          const mapper = {
            1: 2,
            2: 3,
            3: 4,
            4: 5,
            5: 6,
          };
          let idToAdd = mapper[qcq.question_category_id];
          const type = await models.TypeQcm.findByPk(idToAdd);
          if (!type) {
            console.error('No type found for id', idToAdd);
            throw new Error(`No type found for id  ${idToAdd}`);
          }
          await models.QuestionTypeQcm.create({
            questionId: newQuestion.id_question,
            typeQcmId: type.id,
          });
        }

        // link cours avec mass link
        // pour les étoiles map difficulté to type qcm
      }

      console.log('importing DONE');
    } catch (e) {
      console.error(e);
    }
  },
  */

  //////////// FormationElement Importation
  formationElementRemapping:async(elem,coursMappingResolved,titleMappingResolved)=>{
    /* Fonction qui remap inplace les éléments modifiés des formations éléments. Pour le moment :
    * - CoursID
    * - TitleId
    *
    *
    * Si pas de remplacement valide dans le coursMappingResolved // titleMappingResolved, alors on met à null
    *  */

    try {
      if (elem.coursId) {
        let hasSwitched=false

        for (const node of coursMappingResolved) {
          if (parseInt(node.oldCoursId) === elem.coursId.id) {

            const newCoursId = node.newCoursId
            const resolvedCours = await models.Cours.findByPk(newCoursId)

            if (resolvedCours) {
              elem.coursId = newCoursId
              hasSwitched=true
            } else {console.error("le cours de formationElementRemapping n'est pas dans la db, on switch à null");elem.coursId=null}
          }
        }

        if (hasSwitched===false){console.error(`Non remplacement dans formationElementRemapping. On met le coursId à null`);elem.coursId=null}
      }


    }catch(e){console.error("error dans formationElementRemapping : le elem.coursId n'a pas été modifié... Mise à null. Error :",e);elem.coursId=null}

    try{
      if (elem.titleId){
        let hasSwitched=false

        for (const node of titleMappingResolved){

          if (parseInt(node.oldTitleId)=== elem.titleId.id){
            const newTitleId=node.newTitleId
            const resolvedTitle=await models.Title.findByPk(newTitleId)

            if (resolvedTitle){
              elem.titleId=newTitleId
              hasSwitched=true
            }else {console.error("le Title de formationElementRemapping n'est pas dans la db, on switch à null");elem.titleId=null}
          }
        }

        if (hasSwitched===false){console.error(`Non remplacement dans formationElementRemapping. On met le titleId à null`);elem.titleId=null}
      }
    }catch(e){console.error("error dans formationElementRemapping : le elem.titleId n'a pas été modifié... Mise à null. Error :",e); elem.titleId=null}
  },

  formationElementReadFile:async(elem,doImport,mappingArray)=>{
    /* Fonction qui lis le fichier d'importation, ré-importe les images et créé un mapping à remplir. */
    try{

      /// Rewrite des files
      if (doImport && elem.image){
        try{
          const fileName=elem.image.fileName
          await FileService.writeFileFromBase64(elem.image.data,`${UPLOAD_FOLDER_MAP.files}/${fileName}`)
          elem.image=fileName
        }catch(e){
          console.error("error dans formationElementReadFile => le fichier de elem.image n'a pas été importé... mise à elem.image à null Error :",e)
          elem.image=null
        }
      }

      if (doImport && elem.type ==="file" && elem.text){
        try{
          const fileName=elem.text.fileName
          await FileService.writeFileFromBase64(elem.text.data,`${UPLOAD_FOLDER_MAP.files}/${fileName}`)
          elem.text=fileName
        }catch(e){
          console.error("error dans formationElementReadFile => le fichier de elem.type==='file' et elem.text n'a pas été importé... Mise de elem.text à null.   Error :",e)
          elem.text=null
        }
      }

      if (doImport && elem.type==="file" && elem.settings && elem.settings.picture_picture){
        try{
          const fileName=elem.settings.picture_picture.fileName
          await FileService.writeFileFromBase64(elem.settings.picture_picture.data,`${UPLOAD_FOLDER_MAP.files}/${fileName}`)
          elem.settings.picture_picture=fileName
          elem.watermarkPicturePath=fileName
        }catch(e){
          console.error("error dans formationElementReadFile => le fichier de elem.type==='file' et elem.settings et elem.settings.picture_picture (watermark image) n'a pas été importé..." + "" +
            "Mise de elem.settings.picture_picture et elem.watermarkPicture à null      Error :",e)
          elem.settings.picture_picture=null
          elem.watermarkPicturePath=null
        }
      }

      // Push du mapping de cours
      if (elem.coursId){
        mappingArray.push({coursId:elem.coursId.id,description:elem.coursId.description})
      }

      if (elem.titleId){
        mappingArray.push({titleId:elem.titleId.id,description:elem.titleId.data})
      }

    }catch(e){
      console.error("Error inconue dans formationElementReadFile :",e)
    }
  },

  formationElementCreation:async(elem,questionId,headerMcqId,footerQuestionId,ctx)=>{
    /* Fonction qui prend un elem et s'occupe de créer l'élément en base depuis une duplication */

    // Deletion des différents champs et notament des champs de liaison à questionHeader / questionFooter / Mcq
    delete elem.id
    delete elem.createdAt
    delete elem.modifiedAt
    delete elem.questionId // Question Header
    delete elem.headerMcqId // Mcq Header
    delete elem.footerQuestionId // Question Footer

    // Remplacement des nouveaux ID
    if (questionId){elem.questionId=questionId}
    if (headerMcqId){elem.headerMcqId=headerMcqId}
    if (footerQuestionId){elem.footerQuestionId=footerQuestionId}



    // Il faut override le   "element = await FormationService.handleUploadFiles(element);" de createFormationElement
    // Il faut override le elem.order = elem.id;   de CreateFormationElements

    /////// Override de handleUploadFiles en retirant elem.file qui trigger l'upload de fichiers
    let elemFile=null // variable placeholder
    let elemImage=null
    let elemOrder=elem?.order

    if (elem?.file){
      elemFile=elem.file
      delete elem.file
    }

    if (elem?.image){
      elemImage=elem.image
      delete elem.image
    }

    const newElem=await FormationService.createFormationElement(elem,ctx?.me?.id,ctx)

    newElem.file=elemFile
    newElem.image=elemImage
    newElem.order=elemOrder

    await newElem.save()
  },


  //////////// Question Importation
  questionReadFile:async(q,doImport,mappingArray,mappingToResolve)=>{
    /* Fonction qui lit les questions et push les mappings à effectuer dans une array */
    try {

      // Pour chaque question ( i.e : chaque fois qu'on appelle cette fonction) alors on incrémente la var nb question
      try{
        mappingToResolve.numberExercises+=1
      }catch(e){console.error("error dans questionReadFile pour le count de question.     e:",e)}

      // Traitement du scaleId de la question. => On push le mqcScaleId et la description dans le mapping à résolve
      if (q.mcqScaleId){
        try {
          mappingArray.push({ "mcqScaleId": q.mcqScale.id, description: q.mcqScale.name })
        }catch (e){console.error("error dans questionReadFile pour le mcqScaleId.    e:",e)}
      }

      // Traitement des cours reliés à la question => On push les [coursId , `${cours.name - cours.text}`] dans le mapping à résolve
      if (q.cours){
        try {
          for (const cours of q?.cours){
            const reformedName=`${cours?.name || ""} - ${cours?.text || ""}`
            mappingArray.push({"coursId":cours?.id,description:reformedName})
          }
        }catch(e){console.error("error dans questionReadFile pour les cours.     e:",e)}
      }

      // traitement des types_qcm (type d'exercice de la question)
      if (q.type_qcms){
        try {
          for (const typeQcm of q.type_qcms) {
            mappingArray.push({ "exerciseTypeId": typeQcm.id, "description": typeQcm.name })
          }
        }catch(e){console.error("error dans questionReadFile pour les type_qcms.    e:",e)}
      }


      // traitement des questions_answer et notament du réupload des images
      if (q.question_answers){
        try{
          for (const a of q.question_answers){
            try{
              if (a.url_image && a.url_image !== null){
                const fileName=a.url_image["fileName"]
                const data=a.url_image["data"]

                if (doImport){
                  await FileService.writeFileFromBase64(data,`${UPLOAD_FOLDER_MAP.qcm}/${fileName}`)
                  a.url_image=`/qcm/uploads/${fileName}`
                }
              }
            }catch(e){throw new Error(`error url_image (pas explanation),   e : ${e}`)}


            try {
              if (a.url_image_explanation && a.url_image_explanation !== null) {
                const fileName = a.url_image_explanation["fileName"]
                const data = a.url_image_explanation["data"]

                if (doImport) {
                  await FileService.writeFileFromBase64(data, `${UPLOAD_FOLDER_MAP.qcm}/${fileName}`)
                  a.url_image_explanation = `/qcm/uploads/${fileName}`
                }
              }
            }catch(e){throw new Error(`error url_image_explanation,   e : ${e}`)}
          }
        }catch(e){console.error("error dans questionReadFile pour les images.    e:",e)}
      }


    }catch(e){
      console.error("error non identifiée dans questionReadFile,    e:",e)
    }
  },

  questionRemapping:async(q,questionMappingResolved)=>{
    try{

      ///// Resolution du mapping de scaleId
      if (questionMappingResolved && questionMappingResolved.mappingScaleId){
        let hasSwitched=false

        // On itérate sur les scaleMapping
        for (const node of questionMappingResolved.mappingScaleId){

          //// Get des différents IDs
          const newMappingScaleId = parseInt(node.newMcqScale)     // Le new id du mapping
          const oldMappingEntryId = parseInt(node.oldMcqScale)     // le old id du mapping (à remplacer)
          const oldScaleId= q.mcqScaleId                           // l'id du fichier

          // Si on a un match, alors on switch
          if (oldMappingEntryId === oldScaleId){

            // on check si le new Id existe
            const resolvedScale = await models.McqScale.findByPk(newMappingScaleId)

            // Si ça existe, alors on switch
            if (resolvedScale){
              q.mcqScaleId=newMappingScaleId
              hasSwitched=true
            } else {console.error("le mcqScaleId input n'est pas présent dans la DB, on change à null"); q.mcqScaleId=null}
          }
        }
        if (hasSwitched===false){console.error(`Non remplacement dans questionRemapping. On met l'ancien mqcScaleId (${q.mcqScaleId}) à null`);q.mcqScaleId=null}
      }



      ///// Resolution de mapping de question Cours   => Si on a un mapping renseigné
      if (questionMappingResolved && questionMappingResolved?.mappingCours){

        //// Pour chaque cours de chaque question, il faut tenter de switch
        for (const cours of q?.cours){

          let hasSwitched=false

          for (const node of questionMappingResolved.mappingCours){

            const newMappingCoursId = parseInt(node.newCoursId)
            const oldMappingEntryId = parseInt(node.oldCoursId)
            const oldCoursId = cours.id

            // Si on a un match entre le mapping a resolve et le coursId alors on vérifie que le cours existe
            if (oldMappingEntryId === oldCoursId){
              const resolvedCours = await models.Cours.findByPk(newMappingCoursId)

              if (resolvedCours){
                cours.id=newMappingCoursId
                hasSwitched=true
              } else {console.error("le coursId input n'est pas présent dans la DB, on change à null"); cours.coursId=null}
            }
          } if (hasSwitched===false){console.error(`Non remplacement dans questionRemapping. On met l'ancien coursId (${cours.id}) à null`);cours.coursId=null}
        }
      }

      /* Ce bout de code est un peu différent du reste. On part du principe que l'on peut avoir plusieurs, un ou aucun typeId
      * Associé à chaque typeId ancien.
      * Pour chaque typeIdAncien, on a une array de nouveaux ID.
      * L'idée est donc pour chaque ancienID, concaténer les nouveauxID, et ensuite de faire la liaisons entre q <-> newId
      *  */
      if (questionMappingResolved && questionMappingResolved?.newExercisesTypes){
        try {
          let typeQcmConcatArray = []
          for (const typeQcm of q.type_qcms) {
            const previousTypeQcmId=typeQcm.id

            for (const node of questionMappingResolved.newExercisesTypes){
              if (parseInt(node.oldExerciseTypeId) === previousTypeQcmId){
                typeQcmConcatArray = [...new Set([...typeQcmConcatArray, ...node.newExerciseTypeIdArray])]
              }
            }
          }

          q.type_qcms = typeQcmConcatArray
        }catch(e){console.error("Error dans newExercisesTypeId.    e:",e)}
      }else{q.type_qcms=[]}
    }catch(e){console.error(" e   :",e)}
  },

  questionCreation:async(q,newQcmId,ctx)=>{
    /* fonction qui se charge de l'importation de questions . Est utilisé dans l'import de QCM ET dans la création de Question par AI */
    try{
      // modif des différents champs pour la création
      let oldOrder=q.id_question
      delete q.id_question
      q.date_creation=moment().toDate()
      q.date_modif=q.date_creation
      q.authorId=ctx?.me?.id

      // Creation de la question à la main
      const createdQ=await models.Question.create(q)
      const newQuestionId=createdQ.id_question

      // Petit fix, car si on est dans la création de question par AI, on a pas de oldOrder, donc on le créé avec l'id de la new question
      if (oldOrder === undefined || oldOrder===null){oldOrder=createdQ?.id_question}

      // Création du log de question
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Question.Create.action,
        foreignIds: {
          questionId: newQuestionId,
          userId:ctx?.me?.id,
          qcmId:newQcmId,
        },
        ip:ctx?.ip,
        models,
        userId:ctx?.me?.id,
      });

      // ajout des formations elements à la nouvelle question
      for (const elem of q.footerElements){await QcmImportService.formationElementCreation(elem,null,null,newQuestionId,ctx)}
      for (const elem of q.headerElements){await QcmImportService.formationElementCreation(elem,newQuestionId,null,null,ctx)}

      // Ajout des questions_answers à la question. Pas de table de mapping so il faut juste renseigner les ID adéquates
      for (const qa of q.question_answers){
        delete qa.id // Delete de la qa.id  => éviter duplication de champ
        qa.image_explanation=null // Mise à null de ce champ => il est utilisé comme marqueur d'importation de file, le truck c'est qu'on a déjà fait l'importation alors on delete
        qa.questionId=newQuestionId
        await QCMService.createAnswer({answer:qa},ctx)
      }

      // Rajout des cours aux questions. Ici, table de mapping so on utilise la fonction déjà faite pour dans le back
      for (const cours of q.cours){
        await QuestionsService.addCoursToQuestion({coursId:cours.id,questionId:newQuestionId},ctx.me.id)
        await LogService.logAction({
          logOperation: LOG_OPERATIONS.Question.Update.action,
          foreignIds: {
            questionId: newQuestionId,
            coursId:cours.id,
            userId:ctx?.me?.id,
            qcmId:newQcmId,
          },
          ip:ctx?.ip,
          models,
          userId:ctx?.me?.id,
        });
      }

      // Rajout des questionsTypeQcm. Ici, table de mapping donc on créé un enregistrement dans la table
      try{
        for (const questionTypeQcm of q.type_qcms)
        // Vu que l'on a totalement modifié les questionTypeId, il peut y avoir des null qu'il faut filtrer
        if (questionTypeQcm !== null) {
          await models.QuestionTypeQcm.create({ typeQcmId: questionTypeQcm, questionId: newQuestionId })
          await LogService.logAction({
            logOperation: LOG_OPERATIONS.Question.Update.action,
            foreignIds: {
              questionId: newQuestionId,
              userId:ctx?.me?.id,
              data:{typeQcmId:questionTypeQcm},
              qcmId:newQcmId,
            },
            ip:ctx?.ip,
            models,
            userId:ctx?.me?.id,
          });
        }
      }catch(e){console.error("error dans questionCreation pour typeId !       e:",e)}

      // Rajout de la question au QCM. Ici, table de mapping donc on créer un enregisterement dans la table
      if (newQcmId !== null ){
        await models.QuestionsQcm.create({qcmId:newQcmId,questionId:newQuestionId,order:oldOrder})
        await LogService.logAction({
          logOperation: LOG_OPERATIONS.Question.Update.action,
          foreignIds: {
            questionId: newQuestionId,
            userId:ctx?.me?.id,
            data:{order:oldOrder},
            qcmId:newQcmId,
          },
          ip:ctx?.ip,
          models,
          userId:ctx?.me?.id,
        });
      }

      return newQuestionId

      // Creation de la question /!\ pas oublier d'ajouter du log
    }catch(e){
      console.error("error questionCreation :",e)
    }
  },

  //////////// Qcm Importation
  qcmReadFile:async(qcm,doImport,mappingArray,mappingToResolve)=>{
    try {
      // Push des information de l'UE dans l'array des mapping à resolve
      if (qcm.UE) {
        try {
          const ueDescription = `${qcm.UE?.name || ""} - ${qcm.UE?.description || ""}`
          const oldUeId = qcm.UE?.id
          mappingArray.push({"ueId":oldUeId,"description":ueDescription})
        } catch (e) {
          console.error("error dans qcmReadFile dans la préparation du mapping UE :", e)
        }
      }


      if (qcm.type_qcms){
        try{
          for (const node of qcm.type_qcms){
            mappingArray.push({"serieTypeId":node?.id,"description":node?.name})
          }
        }catch(e){console.error("error dans qcm.typeQcm   e:",e)}
      }

      if (qcm.defaultTypeQcmForQuestions){
        try {
          for (const node of qcm.defaultTypeQcmForQuestions) {
            mappingArray.push({ "defaultQuestionTypeId": node?.id, "description": node?.name })
          }
        }catch(e){console.error("error dans defaultTypeQcmForQuestions ,  e:",e)}
      }

      if(qcm.titre){
        try{
          mappingToResolve.qcmTitre=qcm?.titre
        }catch(e){console.error("error dans qcm.Titre,   e : ",e)}
      }

      if (qcm.description){
        try{
          mappingToResolve.qcmDescription=qcm?.description
        }catch(e){console.error("error dans qcm.Description,   e : ",e)}
      }
    }catch(e){console.error("dans qcmReadFile, error non connue :",e)}
  },

  qcmRemapping:async(qcm,qcmMappingResolved)=> {
    try {
      if (qcm?.UE) {
        try {
          // Normalement, si y a de l'UE, alors le mapping est resolu et on itère dessus
          if (qcmMappingResolved && qcmMappingResolved.mappingUe) {
            for (const node of qcmMappingResolved.mappingUe) {

              const newMappingUeId=parseInt(node.newUeId)
              const oldMappingUeId=parseInt(node.oldUeId)
              const oldUeId=qcm.UE.id

              // On check si y a un match entre l'UE Id du qcm et le mapping
              if (oldMappingUeId === oldUeId){
                const resolvedUe=await models.UE.findByPk(newMappingUeId)

                if (resolvedUe){
                  qcm.UEId=newMappingUeId
                } else {console.error("QcmUeId non retrouvé, on set à null "); qcm.UEId=null}
              }
            }
          }
        }catch(e){console.error("error dans le remapping des UE,   e:",e)}
      }

      // Si on a defini des nouveaux type de qcm, alors on écrase les anciens, qu'il y en ai ou pas avant
      if (qcmMappingResolved && qcmMappingResolved.newSerieTypeId){
        try{
          qcm.type_qcms=qcmMappingResolved.newSerieTypeId
          qcm.typeQcmIDS=qcmMappingResolved.newSerieTypeId
        } catch(e){
          qcm.type_qcms=[]
          qcm.typeQcmIDS=[]
        }
      }

      // Si on a defini de nouveau defaultTypeQcmForQuestion, alors on écrase les anciens, qu'il y en ai ou pas
      if (qcmMappingResolved && qcmMappingResolved.newQuestionDefaultType){
        try{
          qcm.defaultTypeQcmForQuestions=qcmMappingResolved.newQuestionDefaultType
          qcm.defaultQuestionsTypeQcmIDS=qcmMappingResolved.newQuestionDefaultType
        } catch(e){
          qcm.defaultTypeQcmForQuestions=[]
          qcm.defaultQuestionsTypeQcmIDS=[]
        }
      }

      // Remapping du nom et description
      if(qcmMappingResolved && qcmMappingResolved.newQcmTitre){
        try{
          qcm.titre=qcmMappingResolved.newQcmTitre
        }catch(e){console.error("error dans qcm remapping du Titre.     e : ",e)}
      }

      if (qcmMappingResolved && qcmMappingResolved.newQcmDescription){
        try{
          qcm.description = qcmMappingResolved.newQcmDescription
        }catch(e){console.error("error dans qcm remapping des descriptions     e : ",e)}
      }

    }catch(e){console.error("error non déterminée dans qcmRemapping :",e)}
  },

  qcmCreation:async(qcm,ctx)=>{
    try {
      // Creation du nouveau QCM et récupération de son ID
      let newQcmId
      try {
        const createdQcm = await QCMService.createQcm({ qcm }, ctx.me.id, ctx.ip)
        if (createdQcm) {
          newQcmId=createdQcm.id_qcm
        }
      }catch(e){throw new Error(`QCM Creation : error lors de la creation du QCM !    e : ${e}`)}

      // Création des différents FormationsElement du qcm
      try{
        for (const elem of qcm.headerElements){
          await QcmImportService.formationElementCreation(elem,null,newQcmId,null,ctx)
        }
      }catch(e){throw new Error(`QCM Creation : Error lors de la création de FormationElemen !    e : ${e}`)}

      // Creation des différentes question
      try{
        for (const q of qcm.importedQuestion){
          await QcmImportService.questionCreation(q,newQcmId,ctx)
        }
      }catch(e){throw new Error(`QCM Creation : Error lors de la creation des questions !    e : ${e}`)}

      return newQcmId
    } catch(e){console.error("error dans qcmCreation.     e :",e)}
  },

  testImportMapping:async(_,{file,resolvedMapping,doImport},ctx,___)=>{
    try{

      if (!file){throw new Error("pas de file")}

      const { createReadStream, filename, mimetype, encoding } = await file.file;
      let stream = createReadStream();

      let mappingToResolve={
        qcmMappingToResolve:[],
        questionMappingToResolve:[],
        formationElementMappingToResolve:[],
        numberExercises:0,
        qcmTitre:"",
        qcmDescription:"",
      }

      /* fonction de reading du fichier et replacement des éléments */
      async function processStream(stream){
        return new Promise((resolve,reject)=>{
          let final = '';

          stream.on('data', function(chunk) {
            final += chunk;
          }).on('end', async () => {
            try {
              // Parse du fichier en un objet Qcm et récupération des resolvedMapping
              const newQcm = JSON.parse(final);

              const qcmMappingResolved=resolvedMapping?.qcmMappingResolved || null
              const questionMappingResolved=resolvedMapping?.questionMappingResolved || null
              const formationElementMappingResolved=resolvedMapping?.formationElementMappingResolved || null

              ///////////////////////
              // Reading du QCM et des file Elements du QCM
              ///////////////////////

              // Reading du QCM (hors formation éléments) et remapping si le mapping est présent
              await QcmImportService.qcmReadFile(newQcm.qcm,doImport,mappingToResolve.qcmMappingToResolve,mappingToResolve)
              if (doImport && qcmMappingResolved){ await QcmImportService.qcmRemapping(newQcm.qcm,qcmMappingResolved)}


              // Gestion des formations elements du QCM header (y a pas de footer
              if (newQcm?.qcm?.headerElements){
                for (const elem of newQcm.qcm.headerElements){
                  await QcmImportService.formationElementReadFile(elem,doImport,mappingToResolve?.formationElementMappingToResolve)
                  if (doImport && formationElementMappingResolved && questionMappingResolved){await QcmImportService.formationElementRemapping(elem,questionMappingResolved?.mappingCours,formationElementMappingResolved?.mappingTitle)}
                }
              }


              ////////////////////////////
              // Traitement pour les Questions du qcm
              ////////////////////////////

              // Si il y a des questions et pour chaque question:
              if (newQcm?.qcm && newQcm.qcm?.importedQuestion){
                for (const q of newQcm.qcm.importedQuestion){

                  // Read et remapping des formationsElements du question.header
                  if (q.footerElements){
                    for (const elem of q.footerElements) {
                      await QcmImportService.formationElementReadFile(elem, doImport, mappingToResolve?.formationElementMappingToResolve)
                      if (doImport && formationElementMappingResolved && questionMappingResolved) {await QcmImportService.formationElementRemapping(elem, questionMappingResolved.mappingCours, formationElementMappingResolved.mappingTitle)}
                    }
                  }

                  // Read et remapping des formationsElements du question.footer
                  if (q.headerElements){
                    for (const elem of q.headerElements){
                      await QcmImportService.formationElementReadFile(elem,doImport,mappingToResolve?.formationElementMappingToResolve)
                      if (resolvedMapping && resolvedMapping.questionMappingResolved && resolvedMapping.formationElementMappingResolved){await QcmImportService.formationElementRemapping(elem, questionMappingResolved.mappingCours, formationElementMappingResolved.mappingTitle)}
                    }
                  }

                  // Read et remapping des questions
                  await QcmImportService.questionReadFile(q,doImport,mappingToResolve.questionMappingToResolve,mappingToResolve)
                  await QcmImportService.questionRemapping(q,resolvedMapping?.questionMappingResolved)
                }
              }

              // Retour du qcm readed
              resolve(newQcm);
            }catch(e){
              console.error("dans processStream, error. On return Null      error :",e)
              reject(null)
            }
          }).on('error', (e) => {
            console.error("dans processStream, error. NE DEVRAIT JAMAIS ARRIVER On return Null      error :",e)
            reject(null);
          });
        })
      }


      // Application du reading
      let newQcm = null
      try{
        newQcm = await processStream(stream);
      } catch(e){
        console.error("erreur de processing :",e)
      }

      // If import, alors
      if(doImport && newQcm){
        try {
          mappingToResolve.newQcmId=await QcmImportService.qcmCreation(newQcm.qcm,ctx) // qcmCreation
        }catch(e){console.error("catched error")}
      }

      /* fonction qui permet d'unifier les sous-objets de mapping de façon à avoir uniquement 1 fois chaque objet à mapper */
      const unifySubObject = (inputObject)=>{
        const outputObject = new Set();

        const filteredArray = inputObject.filter(item => {
          const stringifiedItem = JSON.stringify(item);
          if (!outputObject.has(stringifiedItem)) {
            outputObject.add(stringifiedItem);
            return true;
          }
          return false;
        });
        return filteredArray
      }

      mappingToResolve.questionMappingToResolve=unifySubObject(mappingToResolve.questionMappingToResolve)
      mappingToResolve.qcmMappingToResolve=unifySubObject(mappingToResolve.qcmMappingToResolve)
      mappingToResolve.formationElementMappingToResolve=unifySubObject(mappingToResolve.formationElementMappingToResolve)

      return mappingToResolve

    }catch(e){
      console.error("error dans testImportMapping :",e)
    }
  },

  importFullMcqFromJson: async ({ file, ueId, typesIdsForImport, annee }, models) => {
    try {
      const tfile=await file

      if (!file) {
        return;
      }

      if (!tfile){return ;}

      const { createReadStream, filename, mimetype, encoding } = await file.file;

      let stream = createReadStream();
      let final = '';
      stream.on('data', function(chunk) {
        final += chunk;
      }).on('end', async () => {
        // Write files to data folder from the file
        let qcm = await JSON.parse(final);
        let createdMcq = await models.Qcm.create(qcm.qcm, {
          include: [
            {
              model: models.Question,
              include: [
                models.QuestionAnswers,
                {
                  model: models.FormationElement,
                  as: 'headerElements',
                },
                {
                  model: models.FormationElement,
                  as: 'footerElements',
                },
              ],
            },
            {
              model: models.FormationElement,
              as: 'headerElements',
            },
          ],
        });
        createdMcq.UEId = ueId;
        createdMcq.annee = annee;
        createdMcq.hasExternalQuestions = true;
        await createdMcq.save();
        for (const file of qcm.files) {
          await FileService.writeFileFromBase64(file.data, `${UPLOAD_FOLDER_MAP.qcm}/../..${file.filename}`);
        }
        for (const file of qcm.filesElements) {
          await FileService.writeFileFromBase64(file.data, `${UPLOAD_FOLDER_MAP.files}/${file.filename}`);
        }
        const questions = await models.Question.findAll({
          where: {
            id_qcm: createdMcq.id_qcm,
          },
        });

        /* Add type on serie */
        for (const typeId of typesIdsForImport) {
          const typeQcm = await models.TypeQcm.findByPk(typeId);
          await createdMcq.addType_qcm(typeQcm);
          /* Add type on questions */
          for (const quest of questions) {
            await quest.addType_qcm(typeQcm);
          }
        }

        for (const quest of questions) {
          await models.QuestionsQcm.create({
            questionId: quest.id_question,
            qcmId: createdMcq.id_qcm,
            order: quest.id_question // TODO save order and restore in json
          })
          let typeQ;
          if (quest?.isCheckbox) {
            typeQ = McqScaleQuestionType.MultipleChoice;
          } else {
            typeQ = McqScaleQuestionType.UniqueChoice;
          }
          const mcqScale = await McqScaleService.getDefaultMcqScaleForUeId(ueId, typeQ);
          if (mcqScale) {
            quest.mcqScaleId = mcqScale?.id;
            await quest.save();
          }
        }

        return createdMcq;
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  importGptOneQuestionCreationV3:async({
    q,
    logger,
    index,
    attributes:{
      coursIdArray,
      exerciseType,
      exerciseFormat,
      scaleId,
      mathpixFolderPath,
      mathpixieId,
      gptImportType
    },
    options:{
      generateError,
      forceQuestionCreationOnError,
    },
    ctx
  })=>{


    // Logger
    const key=`entry in importGptOneQuestionCreationV3 index : (${index})`
    logger?.addCheckpoint(key,"unknown")
    logger?.addData(key,{
      generateError,
      q,
      index,
      coursIdArray,
      exerciseType,
      exerciseFormat,
      scaleId,
      mathpixieId,
      gptImportType,
      forceQuestionCreationOnError,
    })


    // Creation du errorTemplate = la structure qui sera renvoyée en cas d'erreure
    const errorTemplate={
      question:"Erreur lors de la génération",
      question_answers:[
        {
          text:"Erreur lors de la génération",
          isTrue:false,
          explanation:"erreur"
        }
      ],
      cours:[],
      footerElements:[],
      headerElements:[],
      type_qcms:[],
      isAiGenerated:true,
      isAiGenerationError:true,
      aiGenerationHasBeenValidated:false,
      mcqScaleId: null,
      creationType: gptImportType ?? AI_CREATION_TYPE.GPT_IMPORTATION,
      mathpixieId:null,
      isCheckbox:true,
    }
    const placeholder=cloneDeep(errorTemplate) // Copie de l'erreur template pour avoir le placeholder => contiendra les valeures modifiées à sauvegarder

    try {
      if (generateError){throw new Error("Lancement d'une erreur pour créer une question Vide")}

      //////// Constantes
      const models=ctx?.models
      const imageFolderPath= `${mathpixFolderPath}/images`;

      /////////////// Verif
      // Cours
      const entriesCours = await models.Cours.count({where:{id:coursIdArray}})
      let reformatedCoursIdListOfObject
      if (entriesCours !== coursIdArray.length) {
        console.warn(`Il n'y a pas toutes les entries de cours en database (count demandé ${coursIdArray?.length})  trouvés : ${entriesCours}`)
        reformatedCoursIdListOfObject=[]
      }else {
        reformatedCoursIdListOfObject=coursIdArray?.map((value) =>{return {id:value}})
      }

      // baremes
      const result=await models.McqScale.findByPk(scaleId)
      if (result===undefined){throw new Error(`scale pour import gpt one question non trouvé`)}
      if (result?.questionType !==McqScaleQuestionType?.MultipleChoice){throw new Error("scale pas pour QCM")}


      // remplissage du placeholder
      placeholder["question"]=q?.["question"] || ""
      placeholder["cours"]=reformatedCoursIdListOfObject || []
      placeholder["type_qcms"]=exerciseType
      placeholder["mcqScaleId"]=scaleId
      placeholder["question_answers"]=[]

      if (Number.isInteger(mathpixieId)){
        placeholder["mathpixieId"]=mathpixieId
      }

      // Remplissage des questions answers
      for (const [aIndex,a] of q?.question_answers.entries()){

        const tempAnswer={}
        tempAnswer["text"]=a?.text||""
        tempAnswer["isTrue"]=a?.isTrue ?? null
        tempAnswer["explanation"]=a?.explanation || ""

        // Ajout des images dans l'énoncé des propositions
        if (a?.url_image && a.url_image!==""){
          const new_url_image=await UploadService.duplicateMathpixPictureToAnotherFolderWrapperForQcmRessources({
            mathpixPictureName:a?.url_image,
            mathpixFolderPath:imageFolderPath,
            newFolder:UPLOAD_FOLDER_MAP.qcm
          })
          tempAnswer["url_image"]=new_url_image;
        }


        // Ajout des images dans la correction des propositions
        if (a?.url_image_explanation && a?.url_image_explanation!==""){
          const new_url_image_explanation=await UploadService.duplicateMathpixPictureToAnotherFolderWrapperForQcmRessources({
            mathpixPictureName:a?.url_image_explanation,
            mathpixFolderPath:imageFolderPath,
            newFolder:UPLOAD_FOLDER_MAP.qcm
          })
          tempAnswer["url_image_explanation"]=new_url_image_explanation
        }
        placeholder?.question_answers.push(tempAnswer)
      }


      // Creation de la question
      const newQuestionId = await QcmImportService.questionCreation(placeholder,null,ctx)

      // importation des formations éléments
      // Importer les formations elements header
      if (q?.imagesEnonce && q?.imagesEnonce?.length>0){
        for (const imageName of q.imagesEnonce){
          // Move de l'image
          const newFormationElementHeaderImagePath=await UploadService.duplicateMathpixPictureToAnotherFolder({
            mathpixPictureName:imageName,
            mathpixFolderPath:imageFolderPath,
            newFolder:UPLOAD_FOLDER_MAP.files
          })

          // creation de l'elem avant ( vu que j'ai déjà mon image, je dois déjà créer, puis rajouter l'image
          const placeholderElem={
            //name:"test title",
            //description:"test description",
            type:"image",
            questionId:newQuestionId // C'est pour le footer element, sinon, il faut mettre ça dans le 'footerQuestionId'
          }

          // Creation du formation element
          const newElem= await FormationService.createFormationElement(placeholderElem,ctx?.me?.id,ctx)

          // ajout de l'image à l'élément nouvellement créé
          newElem.image=newFormationElementHeaderImagePath

          // save
          await newElem.save()
        }
      }


      if (q?.imagesCorrection && q?.imagesCorrection?.length>0){

        for (const imageName of q.imagesCorrection){

          // Move de l'image
          const newFormationElementFooterImagePath=await UploadService.duplicateMathpixPictureToAnotherFolder({
            mathpixPictureName:imageName,
            mathpixFolderPath:imageFolderPath,
            newFolder:UPLOAD_FOLDER_MAP.files
          })

          // creation de l'elem avant ( vu que j'ai déjà mon image, je dois déjà créer, puis rajouter l'image
          const placeholderElem={
            //name:"test title",
            //description:"test description",
            type:"image",
            footerQuestionId:newQuestionId
          }

          // Creation du formation element
          const newElem= await FormationService.createFormationElement(placeholderElem,ctx?.me?.id,ctx)

          // ajout de l'image à l'élément nouvellement créé
          newElem.image=newFormationElementFooterImagePath

          // save
          await newElem.save()
        }
      }

      // Mise du bool 'isAiGenerationError= false'
      const newQuestion=await models.Question.findByPk(newQuestionId)
      newQuestion.isAiGenerationError=false
      newQuestion.order=newQuestionId
      newQuestion.save()


      logger?.addCheckpoint(key,"ok")

      return {newQuestionId,error:false,errorMessage:""}
    }catch(e){
      const errorMessage=`dans ImportGptOneQuestionCreation erreur : ${e}`
      logger?.addCheckpoint(key,"ko")
      logger?.addError(key,{message:errorMessage,question:q,placeholder})
      console.error(errorMessage)

      if (forceQuestionCreationOnError){
        const newQuestionId=await QcmImportService.questionCreation(errorTemplate,null,ctx)
        return {newQuestionId,error:true,errorMessage:errorMessage}
      } else {
        console.error(e)
        throw new GraphQLError(errorMessage)
      }
    }
  },


  importGptFillInTheBlankQuestion: async ({
    q,
    logger,
    index,
    attributes:{
      coursIdArray, // Doit être du format [coursId1, coursId2]
      exerciseType, // doit être de format [id1,id2],
      scaleId, // le barem ID auquel lier la question
      mathpixieId=null,
      gptImportType=AI_CREATION_TYPE.GPT_IMPORTATION,
    },
    options:{
      acceptTypos,
      caseSensitive,
      createError=false, // Si true, créée, directement une erreure
      forceQuestionCreationOnError,
      ignoreSpecialChar,
    },
    ctx
  })=>{

    const key=`importGptFillInTheBlankQuestion for index : (${index})`
    logger.addCheckpoint(key,"unknown")

    // Fonction qui créé les exercices 'fillInTheBlank' créés par chatGPT
    const errorTemplate={
      question:"error",
      settings:{
        text:"error",
        acceptTypos:false,
        caseSensitive:false,
      },
      question_answers:[],
      cours:[],
      footerElements:[],
      headerElements:[],
      type_qcms:[], // doit être une array d'id : [id1,id2,id3]
      isAiGenerated:true,
      isAiGenerationError:true,
      aiGenerationHasBeenValidated:false,
      isCheckbox:false,
      mcqScaleId: null, // on test si on est un int, et si oui, alors on valide
      type:QuestionAnswerType?.FILL_IN_THE_BLANKS,
      creationType:gptImportType,
      mathpixieId:mathpixieId
    }

    // On veut avoir un copie de l'error template que l'on étoffe avec les champs
    const placeholder=cloneDeep(errorTemplate)

    try{
      if (createError){throw new Error("Error simulée pour créer une question vide")}

      //////////// VERIF
      // Cours
      const entriesCours = await models.Cours.count({where:{id:coursIdArray}})
      if (entriesCours !== coursIdArray.length) {throw new Error(`Il n'y a pas toutes les entries de cours en database (count demandé ${coursIdArray?.length})  trouvés : ${entriesCours}`)}

      /// Barems
      const result=await models.McqScale.findByPk(scaleId)
      if (result===undefined){throw new Error(`scale pour import gpt one question non trouvé`)}
      if (result?.questionType !== McqScaleQuestionType?.FillInTheBlanks){throw new Error ("Scale pas de type 'fill in the blanks' ")}

      // Formatage des cours
      const reformatedCoursIdListOfObject=coursIdArray?.map((value) =>{return {id:value}})

      // remplissage du placeholder avec les bonnes valeures => mise de valeur par default si non définies histoire de override le placeholder d'erreure
      placeholder["question"]=q?.["question"] || ""
      placeholder["settings"]["text"]= q?.["settings"]?.["text"] || ""
      placeholder["settings"]["acceptTypos"]= acceptTypos
      placeholder["settings"]["caseSensitive"]= caseSensitive
      placeholder["settings"]["ignoreAccentsAndSpecialChars"]=ignoreSpecialChar
      placeholder["cours"]=reformatedCoursIdListOfObject || []
      placeholder["type_qcms"]=exerciseType
      placeholder["mcqScaleId"]=scaleId

      if (Number.isInteger(mathpixieId)){
        placeholder["mathpixieId"]=mathpixieId
      }

      // Verif pour avoir la question dans l'interface de recherche
      placeholder["aiGenerationHasBeenValidated"]=true

      // creation de la question
      const newQuestionId = await QcmImportService.questionCreation(placeholder,null,ctx)

      // Retirage du bool 'isAiGenerationError'
      const newQuestion= await models.Question.findByPk(newQuestionId)
      newQuestion.isAiGenerationError=false
      newQuestion.order=newQuestionId
      newQuestion.save()

      logger.addCheckpoint(key,"ok")

      return {newQuestionId,error:false}
    }catch(e){

      logger.addCheckpoint(key,"ko")
      logger.addError(key,{message:e,placeholder})
      console.error(e)

      if (forceQuestionCreationOnError){
        // Maybe enforce la non modif ?
        const newQuestionId = await QcmImportService.questionCreation(errorTemplate,null,ctx)
        return {newQuestionId,error:true,errorMessage:e}
      } else {
        console.error(e)
        throw new GraphQLError(e)
      }
    }
  },

  createInBaseQcuAndQcmFromGptCreation:async({
    // Sous fonction qui créée les QCU/QCM créés par AI
    question,
    logger,
    index,
    attributes:{
      coursIdArray,
      exerciseType,
      scaleId,
      exerciseFormat, // permet de vérifier la cohérence du barem
      mathpixieId=null,
      gptImportType=AI_CREATION_TYPE.GPT_IMPORTATION,
    },
    options:{
      generateError,
      forceQuestionCreationOnError,
      randomizeQuestionsAnswers,
      throwErrorIfNotQcu,
    },
    ctx
  })=>{


    const key=`createInBaseQcuAndQcmFromGptCreation for index : (${index})`
    logger.addCheckpoint(key,"unknown")

    const errorTemplate={
      question:"Erreur lors de la génération",
      question_answers:[
        {
          text:"Erreur lors de la génération",
          isTrue:false,
          explanation:"erreur"
        }
      ],
      cours:[],
      footerElements:[],
      headerElements:[],
      type_qcms:[],
      isAiGenerated:true,
      isAiGenerationError:true,
      aiGenerationHasBeenValidated:false,
      mcqScaleId: null,
      isCheckbox: true,
      creationType:gptImportType,
      mathpixieId,
    }

    // On veut avoir un copie de l'error template que l'on étoffe avec les champs
    const placeholder=cloneDeep(errorTemplate)

    try{
      if (generateError){throw new Error("Lancement d'une erreur pour créer une question vide ")}

      //////////// VERIF
      // Cours
      const entriesCours = await models.Cours.count({where:{id:coursIdArray}})
      if (entriesCours !== coursIdArray.length) {throw new Error(`Il n'y a pas toutes les entries de cours en database (count demandé ${coursIdArray?.length})  trouvés : ${entriesCours}`)}

      /// Barems
      const result=await models.McqScale.findByPk(scaleId)
      if (result===undefined){throw new Error(`scale pour import gpt one question non trouvé`)}
      if (exerciseFormat!==McqScaleQuestionType?.UniqueChoice && exerciseFormat !== McqScaleQuestionType?.MultipleChoice){ throw new Error ("format non supporté dans l'import de question")}
      if (result?.questionType !==McqScaleQuestionType?.MultipleChoice && result?.questionType !== McqScaleQuestionType?.UniqueChoice){throw new Error("scale pas pour QCU ou QCM")}

      // Formatage des cours
      const reformatedCoursIdListOfObject=coursIdArray?.map((value) =>{return {id:value}})

      // Replissage du placeholder avec les données reçues
      placeholder["question"]=question?.["question"] || ""
      placeholder["cours"]=reformatedCoursIdListOfObject || []
      placeholder["type_qcms"]=exerciseType
      placeholder["mcqScaleId"]=scaleId
      placeholder["question_answers"]=[]

      /*
              } else if (question.isCheckbox){
          scaleQuestionType=McqScaleQuestionType.MultipleChoice
        } else if (!question.isCheckbox){
          scaleQuestionType=McqScaleQuestionType.UniqueChoice
        }else{
       */

      if (exerciseFormat===McqScaleQuestionType.UniqueChoice){
        placeholder["isCheckbox"]=false
      }

      if (Number.isInteger(mathpixieId)){
        placeholder["mathpixieId"]=mathpixieId
      }


      // Remplissage des questions answer
      for (const [aIndex,a] of question?.question_answers.entries()){
        const tempAnswer={}

        tempAnswer["text"]=a?.text || ""
        tempAnswer["isTrue"]=a?.isTrue ?? null
        tempAnswer["explanation"]=a?.explanation || ""

        placeholder.question_answers.push(tempAnswer)
      }

      // Shuffeling des question_answers
      if (randomizeQuestionsAnswers){
        function shuffleArrayInPlace(array) {
          // Shuffle in place une array
          for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]]; // Échange des éléments
          }
        }
        shuffleArrayInPlace(placeholder?.question_answers)
      }

      // Enforcement des QCU
      if (throwErrorIfNotQcu){
        const condition=placeholder?.question_answers?.filter(obj=>obj?.isTrue).length===1
        if (!condition){
          throw new Error(`dans QcmImportService.importGptOneQuestionCreation , l'option 'throwErrorIfNoQcu' a été activé, et la question passée n'est pas un QCU. Levée d'erreure. Question : ${JSON.stringify(placeholder)}`)
        }
      }


      // Creation de la question
      const newQuestionId = await QcmImportService.questionCreation(placeholder,null,ctx)

      // Retirage du bool 'isAiGenerationError'
      const newQuestion= await models.Question.findByPk(newQuestionId)
      newQuestion.isAiGenerationError=false
      newQuestion.order=newQuestionId
      newQuestion.save()

      logger.addCheckpoint(key,"ok")

      return {newQuestionId,error:false}
    }catch(e){

      logger.addCheckpoint(key,"ko")
      logger.addError(key,{message:e,placeholder})
      console.error(e)

      if (forceQuestionCreationOnError){
        const newQuestionId = await QcmImportService.questionCreation(errorTemplate,null,ctx)
        return {newQuestionId,error:true,errorMessage:e}
      } else {
        console.error(e)
        throw new GraphQLError(e)
      }
    }
  },


  createInBaseFlashcardFromGptCreation:async({
                                               // Sous fonction qui créée les FLASHCARD créés par AI
                                               question,
                                               logger,
                                               index=null,
                                               attributes:{
                                                 coursIdArray,
                                                 exerciseType,
                                                 scaleId,
                                                 exerciseFormat, // permet de vérifier la cohérence du barem
                                                 mathpixieId,
                                                 gptImportType
                                               },
                                               options:{
                                                 generateError,
                                                 forceQuestionCreationOnError,
                                                 flashcardResponseType
                                               },
                                               ctx
                                             })=>{

    const errorTemplate={
      question:"Erreur lors de la génération",
      question_answers:[],
      cours:[],
      footerElements:[],
      headerElements:[],
      type_qcms:[],
      isAiGenerated:true,
      isAiGenerationError:true,
      aiGenerationHasBeenValidated:false,
      mcqScaleId: null,
      isCheckbox: false,
      creationType: gptImportType ?? AI_CREATION_TYPE.GPT_IMPORTATION,
      mathpixieId:null,
      type:QuestionAnswerType.FLASHCARD,
      settings:{}
    }

    const key=`createInBaseFlashcardFromGptCreation for index : (${index})`
    logger.addCheckpoint(key,"unknown")

    // On veut avoir un copie de l'error template que l'on étoffe avec les champs
    const placeholder=cloneDeep(errorTemplate)

    try{
      if (generateError){throw new Error("Lancement d'une erreur pour créer une question vide ")}

      //////////// VERIF
      // Cours
      const entriesCours = await models.Cours.count({where:{id:coursIdArray}})
      if (entriesCours !== coursIdArray.length) {throw new Error(`Il n'y a pas toutes les entries de cours en database (count demandé ${coursIdArray?.length})  trouvés : ${entriesCours}`)}

      /// Barems
      const result=await models.McqScale.findByPk(scaleId)
      if (result===undefined){throw new Error(`scale pour import gpt one question non trouvé`)}
      if (exerciseFormat!==McqScaleQuestionType?.FLASHCARD){ throw new Error ("format non supporté dans l'import de question")}
      if (result?.questionType !== McqScaleQuestionType?.FLASHCARD){throw new Error("scale pas pour FLASHCARD")}

      // Formatage des cours
      const reformatedCoursIdListOfObject=coursIdArray?.map((value) =>{return {id:value}})

      // Replissage du placeholder avec les données reçues
      placeholder["question"]="letzgong"
      placeholder["cours"]=reformatedCoursIdListOfObject || []
      placeholder["type_qcms"]=exerciseType
      placeholder["mcqScaleId"]=scaleId
      placeholder["settings"]={flashcardResponseType}

      if (Number.isInteger(mathpixieId)){
        placeholder["mathpixieId"]=mathpixieId
      }

      // Creation de la question
      const newQuestionId = await QcmImportService.questionCreation(placeholder,null,ctx)

      const localCreateFe=async ({structure,newQuestionId,isCorrection} )=>{
        /* fonction locale de creation de FE */
        try {
          const {type,text,path,size}=structure

          if (type!=="image"&&type!=="text"){throw new Error("type not valid")}


          // Chaine de validation de l'objet FE Text
          const isText= (type==="text")
          const isImage=(type==="image")
          const isValidText= (type==="text") && (text && text !== "") && (["small","medium","big"].includes(size))
          const isValidImage=(type==="image") && (path && path!=="")


          // Temporel
          if (isImage){throw new Error("images pas supportées dans la création de flashcard pour le moment")}

          if (type!=="text" && type!=="image"){throw new Error(`FE type not valid : (${type})`)}
          if (isText && !isValidText){
            console.error(`Type text, mais echec des propriétées valides. On skip le FE => text : (${text}), size : (${size})`)
            return null
          }

          if (isImage && !isValidImage){
            console.error(`Type image, mais echec des propriétées valides.`)
            return null
          }

          const tempFE={
            type,
            ...(isCorrection
              ? { footerQuestionId: newQuestionId }
              : { questionId: newQuestionId })
          }


          const newElem=await FormationService.createFormationElement(tempFE,ctx?.me?.id,ctx)

          if (type==="text"){
            let fontSize

            switch (size){
              case "small":
                fontSize=16
                break

              case "medium":
                fontSize=32
                break

              case "big":
                fontSize=54
                break
            }

            newElem.type=ELEMENTS_TYPE.RICH_TEXT
            newElem.text=`<p style=\"text-align: center; font-size: ${fontSize}px;\"><strong>${text}</strong></p>`

          }

          if (type==="image"){
            newElem.image="pas d'image"
            newElem.type=ELEMENTS_TYPE.IMAGE
            /*

            // Move de l'image
            const newFormationElementFooterImagePath=await UploadService.duplicateMathpixPictureToAnotherFolder({
              mathpixPictureName:imageName,
              mathpixFolderPath:imageFolderPath,
              newFolder:UPLOAD_FOLDER_MAP.files
            })


            // ajout de l'image à l'élément nouvellement créé
            newElem.image=newFormationElementFooterImagePath

             */
          }


          // save
          await newElem.save()


        }catch(e){
          console.warn("error dans localCreateFe :",e)
          //throw new GraphQLError(e)
        }
      }

      // Creation des FE Enoncé
      for (const [aIndex,a] of question?.enonce.entries()){
        await localCreateFe({structure:a,newQuestionId,isCorrection:false})
      }

      for (const [aIndex,a] of question?.correction.entries()){
        await localCreateFe({structure:a,newQuestionId,isCorrection:true})
      }


      // Retirage du bool 'isAiGenerationError'
      const newQuestion= await models.Question.findByPk(newQuestionId)
      newQuestion.isAiGenerationError=false
      newQuestion.order=newQuestionId
      await newQuestion.save()

      // Update du title
      await QCMService.updateQuestionTitleForFlashcard({questionId:newQuestionId,isFlashcard:true})

      // Add checkpoint
      logger.addCheckpoint(key,"ok")

      return {newQuestionId,error:false}
    }catch(e){

      logger.addCheckpoint(key,"ko")
      logger.addError(key,{message:e,placeholder})
      console.error(e)

      if (forceQuestionCreationOnError){
        const newQuestionId = await QcmImportService.questionCreation(errorTemplate,null,ctx)
        return {newQuestionId,error:true,errorMessage:e}
      } else {
        console.error(e)
        throw new GraphQLError(e)
      }
    }
  },


  importMcqFromXLS: async ({ file, id_qcm = 2879 }, models) => {
    try {
      if (!file) {
        console.error('No file');
        return;
      }
      let fileName = await UploadService.uploadFile(file);

      const workSheetsFromFile = await xlsx.parse(`${process.cwd()}/${UPLOAD_FOLDER_MAP.files}/${fileName}`);
      const firstPage = workSheetsFromFile[0].data;
      const titres = firstPage[0];

      let i = 1;
      const firstLine = firstPage[i];
      if (firstLine[0] === 'Id du cours') {
        i++;
      }

      for (; i < firstPage.length; i++) {
        let currentQuestion = firstPage[i];

        let idDuCours = currentQuestion[0];
        let typeReponse = currentQuestion[1];

        let typeQuestion = currentQuestion[2];
        let difficulteSubjective = currentQuestion[3];
        let enonce = currentQuestion[4];
        if (enonce === undefined || enonce === null || enonce === '') {
          continue;
        }

        let answer1 = currentQuestion[5];
        let isAnswer1True = currentQuestion[6];
        let explicationAnswer1 = currentQuestion[7];

        let answer2 = currentQuestion[8];
        let isAnswer2True = currentQuestion[9];
        let explicationAnswer2 = currentQuestion[10];

        let answer3 = currentQuestion[11];
        let isAnswer3True = currentQuestion[12];
        let explicationAnswer3 = currentQuestion[13];

        let answer4 = currentQuestion[14];
        let isAnswer4True = currentQuestion[15];
        let explicationAnswer4 = currentQuestion[16];

        let answer5 = currentQuestion[17];
        let isAnswer5True = currentQuestion[18];
        let explicationAnswer5 = currentQuestion[19];

        let answer6 = currentQuestion[20];
        let isAnswer6True = currentQuestion[21];
        let explicationAnswer6 = currentQuestion[22];

        let answer7 = currentQuestion[23];
        let isAnswer7True = currentQuestion[24];
        let explicationAnswer7 = currentQuestion[25];

        let explications = currentQuestion[26];

        let questionType = () => {
          if (typeReponse === 'QRU') {
            return {
              isCheckbox: false,
            };
          }
          if (typeReponse === 'QRM') {
            return {
              isCheckbox: true,
            };
          }
          return {
            isCheckbox: false,
          };
        };

        const questionAnswers = () => {
          let answers = [];
          if (answer1) {
            answers.push({
              isTrue: Boolean(isAnswer1True === 1),
              text: answer1,
              explanation: explicationAnswer1,
              url_image_explanation: null,
            });
          }
          if (answer2) {
            answers.push({
              isTrue: Boolean(isAnswer2True === 1),
              text: answer2,
              explanation: explicationAnswer2,
              url_image_explanation: null,
            });
          }
          if (answer3) {
            answers.push({
              isTrue: Boolean(isAnswer3True === 1),
              text: answer3,
              explanation: explicationAnswer3,
              url_image_explanation: null,
            });
          }
          if (answer4) {
            answers.push({
              isTrue: Boolean(isAnswer4True === 1),
              text: answer4,
              explanation: explicationAnswer4,
              url_image_explanation: null,
            });
          }
          if (answer5) {
            answers.push({
              isTrue: Boolean(isAnswer5True === 1),
              text: answer5,
              explanation: explicationAnswer5,
              url_image_explanation: null,
            });
          }
          if (answer6) {
            answers.push({
              isTrue: Boolean(isAnswer6True === 1),
              text: answer6,
              explanation: explicationAnswer6,
              url_image_explanation: null,
            });
          }
          if (answer7) {
            answers.push({
              isTrue: Boolean(isAnswer7True === 1),
              text: answer7,
              explanation: explicationAnswer7,
              url_image_explanation: null,
            });
          }
          return answers;
        };

        let question_answers = await questionAnswers();

        const questionToInsert = {
          id_qcm: id_qcm,
          question: `${enonce}`,
          ...questionType(),
          definedDifficulty: difficulteSubjective,
          question_answers,
          explications: explications,
          linkCoursId: idDuCours, // useless, will be removed in the future (change treated)
          date_creation: new Date(),
          date_modif: new Date(),
          //autoAddNotions: true,
        };

        let questionObject = await models.Question.create(questionToInsert, { include: [models.QuestionAnswers] });

        if (explications) {
          let newElement = {
            footerQuestionId: questionObject.id_question,
            type: 'callout',
            text: 'info',
            name: explications,
          };
          const createdElement = await models.FormationElement.create(newElement);
          createdElement.order = createdElement.id;
          await createdElement.save();
        }

        await QuestionsService.addCoursToQuestion({ coursId: idDuCours, questionId: questionObject.id_question }, null);

        // Add Type
        let nameToSearch = typeQuestion;
        if (typeQuestion === 'R') {
          nameToSearch = 'Réglementaire';
        } else if (typeQuestion === 'T') {
          nameToSearch = 'Technique';
        }
        const typeQuestionM = await models.TypeQuestion.findOne({ where: { name: nameToSearch } });
        await questionObject.addType_question(typeQuestionM);
      }
      return true;
    } catch (e) {
      console.error(e);
      return false;
    }
  },

  importCSV: async ({ file, qcmId, type }, models) => {
    const qcm = await models.Qcm.findByPk(qcmId);
    if (!qcm) {
      throw new GraphQLError('QCM introuvable');
    }

    let result = [];
    let fileName = await UploadService.uploadFile(file);
    const path = `${process.cwd()}/${UPLOAD_FOLDER_MAP.files}/${fileName}`;

    if (type === 'changeQuestionOrder') {
      if (qcm.hasExternalQuestions) {
        await models.QuestionsQcm.findAll({
          where: {
            qcmId,
          },
        });
        fs.createReadStream(path)
          .pipe(csvParser())
          .on('data', (data) => {
            result.push(data);
          })
          .on('end', async () => {
            // Change imported question order
            let i = 1;
            for (const line of result) {
              const questionId = line.question_id;
              const questionToEdit = await models.QuestionsQcm.findOne({
                where: {
                  questionId,
                  qcmId,
                },
              });
              if (questionToEdit) {
                questionToEdit.order = i;
                await questionToEdit.save();
                i++;
                //console.log(`Question order ${questionId} order ${i} changed`);
              }
            }
          });

      } else {
        throw new GraphQLError('Ce QCM n\'a pas de questions externes');
        console.error('Ce QCM n\'a pas de questions externes');
      }
    }

    if (type === 'questionList') {
      fs.createReadStream(path)
        .pipe(csvParser())
        .on('data', (data) => {
          result.push(data);
        })
        .on('end', async () => {
          //console.log(result);
          for (const line of result) {
            const questionId = line.question_id;
            if (questionId) {
              await models.QuestionsQcm.create({
                qcmId: qcm?.id_qcm,
                questionId,
              });
            }
          }
        });
    }

    if (type === 'parentsChilds') {
      fs.createReadStream(path)
        .pipe(csvParser())
        .on('data', (data) => {
          result.push(data);
        })
        .on('end', async () => {
          for (const obj of result) {
            const parentQuestionId = obj.question_id;
            let childsQuestionIds = [];
            for (const [key, value] of Object.entries(obj)) {
              if (key !== 'question_id' && value) {
                childsQuestionIds.push(value);
              }
            }
            //console.log({ parentQuestionId, childsQuestionIds });
            // INSERT IN DB
            await QCMService.addMultipleQuestionChilds({ parentQuestionId, childsQuestionIds, isError: false });
          }
        });
    }
    if (type === 'doubleParentsChilds') {
      fs.createReadStream(path)
        .pipe(csvParser())
        .on('data', (data) => {
          result.push(data);
        })
        .on('end', async () => {
          for (const obj of result) {
            const parentQuestionIds = obj.question_id.split('x');
            const parentQuestionId = parentQuestionIds[0];
            const parentQuestionId2 = parentQuestionIds[1];
            if (parentQuestionId && parentQuestionId2) {
              let childsQuestionIds = [];
              for (const [key, value] of Object.entries(obj)) {
                if (key !== 'question_id' && value) {
                  childsQuestionIds.push(value);
                }
              }
              // insert stuff
              //console.log({ parentQuestionId, parentQuestionId2, childsQuestionIds });
              // INSERT IN DB

              await QCMService.addMultipleDoubleQuestionChilds({
                parentQuestionId,
                parentQuestionId2,
                childsQuestionIds,
                isError: false,
              });
            }
          }
        });
    }

    // For errors
    //console.log({ type });
    if (type === 'errorParentsChilds') {
      fs.createReadStream(path)
        .pipe(csvParser())
        .on('data', (data) => {
          result.push(data);
        })
        .on('end', async () => {
          for (const obj of result) {
            const parentQuestionId = obj.question_id;
            let childsQuestionIds = [];
            for (const [key, value] of Object.entries(obj)) {
              if (key !== 'question_id' && value) {
                childsQuestionIds.push(value);
              }
            }
            //console.log({ parentQuestionId, childsQuestionIds });
            // INSERT IN DB
            await QCMService.addMultipleQuestionChilds({ parentQuestionId, childsQuestionIds, isError: true });
          }
        });
    }
    if (type === 'errorDoubleParentsChilds') {
      fs.createReadStream(path)
        .pipe(csvParser())
        .on('data', (data) => {
          result.push(data);
        })
        .on('end', async () => {
          for (const obj of result) {
            const parentQuestionIds = obj.question_id.split('x');
            const parentQuestionId = parentQuestionIds[0];
            const parentQuestionId2 = parentQuestionIds[1];
            if (parentQuestionId && parentQuestionId2) {
              let childsQuestionIds = [];
              for (const [key, value] of Object.entries(obj)) {
                if (key !== 'question_id' && value) {
                  childsQuestionIds.push(value);
                }
              }
              // insert stuff
              // console.log({ parentQuestionId, parentQuestionId2, childsQuestionIds });
              // INSERT IN DB
              await QCMService.addMultipleDoubleQuestionChilds(
                {
                  parentQuestionId, parentQuestionId2, childsQuestionIds, isError: true,
                });
            }
          }
        });
    }

    return true;
  },

  /* Import user results from XLS */
  importMcqUserResultsFromXls: async ({ file, qcmId, examQuestionSerieId, selectedFileType }, models) => {
    try {
      if (!file) {
        console.error('No file');
        return;
      }

      //const importOnlyQuestionPoints = selectedFileType === 'freeText';

      // selectedFileType: qcm || freeText
      console.log('Uploading file...');
      let fileName = await UploadService.uploadFile(file);
      let userNames = [];
      let log = [];
      let errors = [];

      console.log('Getting questionForQcm...');
      const questions = await QCMService.getQuestionsForQcm(qcmId);
      let nbQuestions = questions?.length;
      if (!questions) {
        console.error('no questions found for this mcq');
        throw new GraphQLError('QCM introuvable');
      }
      console.log('parsing XLS file...');
      const workSheetsFromFile = await xlsx.parse(`${process.cwd()}/${UPLOAD_FOLDER_MAP.files}/${fileName}`);
      const firstPage = workSheetsFromFile[0].data;
      const titres = firstPage[0];
      let indexQuestion, foundFirstQ = false;
      /* Parse Titles to find the start index for user answers*/

      // 4ème colonne: 'Q1'
      indexQuestion = 3;

      /*
      for (let i = 0; i < titres.length; i++) {
        const actualTitle = titres[i];
        // Trouve le dernier 'Q1' parmi les titres
        if (actualTitle == 'Q1') {
          if (foundFirstQ) {
            indexQuestion = i;
          } else {
            foundFirstQ = true;
          }
        }
      }
      */

      // calculNoteQcm
      /*
      reponses: [
        { id_question: 16732, answers: [ '80913', '80914', '80915' ], certainty: null },
        { id_question: 16733, certainty: null }, // no answers
        { id_question: 16734, answers: [Array], certainty: null },
        { id_question: 16735, answers: [Array], certainty: null },
        { id_question: 16736, answers: [Array], certainty: null }
        ]
       */
      for (let i = 1; i < firstPage.length; i++) {
        // Array for grade and answer computation
        let reponses = [];
        let shouldImport = true;

        let currentQuestion = firstPage[i];
        let userName = currentQuestion[0]; // User Code Name
        let pseudo = currentQuestion[1]; // User Pseudo or email

        console.log({userName, pseudo})

        if (userName === undefined && pseudo === undefined) {
          // Skip si rien n'est trouvé
          continue;
        }
        let user = null;
        if(userName) {
          // Si user code name est défini
          user = await models.User.findOne({
            where: {
              userCodeName: userName,
            },
          });
        }
        if (!user) {
          // Cant find by userCodeName, try by pseudo
          user = await models.User.findOne({
            where: {
              username: pseudo,
            },
          });
          if(!user) {
            user = await models.User.findOne({
              where: {
                email: pseudo,
              },
            });
            if(!user) {
              // On trouve vraiment pas l'utilisateur
              errors.push(`${userName} est introuvable. Résultat non importé.`);
              continue;
            }
          }
        }
        // Present ou absent (oui / non)
        const statusU = currentQuestion[2];


        if (statusU.toLowerCase() !== 'OUI'.toLowerCase()) {
          errors.push(`${userName} n'est pas présent. Son résultat n'a pas été importé.`);
          continue;
        }
        userNames.push(userName);

        let questionIndex = 0;

        /* For each Question of user with userName */
        for (let j = indexQuestion; j < (indexQuestion + nbQuestions); j++) {
          // First Answers (Q1) (e.g : "ABD")
          const actualQuestion = questions[questionIndex];
          const questionAnswers = actualQuestion?.question_answers;
          /* For each answer */
          let answers = [];
          let pointsOnly = 0;
          let actualAnswersString = currentQuestion[j];
          // Obtenir la valeur de la cellule
          let answerIsPointsObtained = undefined;
          // Vérifier si la valeur est un nombre
          if (!isNaN(parseFloat(actualAnswersString)) && isFinite(actualAnswersString)) {
            answerIsPointsObtained = true;
          } else {
            answerIsPointsObtained = false;
          }
          // TODO determiner depuis actualQuestion si c'est freeText ou QCM
          //console.log({texteLibre: actualQuestion?.isAnswerFreeText});

          if (answerIsPointsObtained) {
            pointsOnly = parseFloat(actualAnswersString);
            //console.log(pointsOnly);
            if (actualAnswersString === '' || isNaN(pointsOnly)) {
              errors.push(`Points obtenus de ${userName} introuvables ou invalides, ne sera pas importé`);
            }
          } else {
            let arrayOfLetters = [...String(actualAnswersString)]; // Ensure actualAnswersString is a string before spreading
            //let arrayOfLetters = [...actualAnswersString]; // String to Array { arrayOfLetters: [ 'B', 'D', 'E' ] }
            for (const letter of arrayOfLetters) {
              if (!alphabetArray.includes(letter)) {
                continue; // Si c'est pas une lettre, on passe à la suivante
              }
              const answerIndex = alphabetArray.indexOf(letter); // Answer index
              // Answer model
              const currentAnswer = questionAnswers[answerIndex];
              if (!currentAnswer) {
                errors.push(`Réponse ${letter} de ${userName} n'existe pas pour la question id=${actualQuestion?.id_question}. L'import n'est pas interrompu. `);
              } else {
                answers.push(currentAnswer?.id);
              }
              // ici amélioration calculer note ? (ou construire résultat comme frontend avant insertion)
            }
          }

          if (answerIsPointsObtained) {
            if(isNaN(pointsOnly)) {
              console.log('isNaN(pointsOnly) ! => will not import this', pointsOnly);
              shouldImport = false;
            } else {
              reponses.push({
                id_question: actualQuestion?.id_question,
                pointsObtained: pointsOnly,
                id_utilisateur: user.id,
                certainty: null,
              });
            }

          } else {
            // Build frontend-like array
            reponses.push({ id_question: actualQuestion?.id_question, answers, certainty: null });
          }
          questionIndex++;
        }
        // End FOR LOOP question processing

        if(!shouldImport) {
          continue;
        }

        // Handle session creation for user results
        let mySession = null;
        if (examQuestionSerieId) {
          // Try to find unfinished session
          mySession = await models.QcmSession.findOne({
            where: {
              userId: user?.id,
              examQuestionSerieId,
            },
          });
          //const questionSerie = await models.ExamQuestionSeries.findByPk(examQuestionSerieId)
          //const id_qcm = questionSerie?.mcqId
          //const qcm = await models.Qcm.findByPk(id_qcm)
          // NEW SESSION
          if (!mySession) {
            mySession = await models.QcmSession.create({
              examQuestionSerieId,
              userId: user?.id,
              currentState: null,
              isFinished: true,
              questionsIdsDone: questions?.map(q => q?.id_question),
              qcmId
            });
            //await QcmSessionService.initCurrentCoursSession(mySession, qcm)
          } else {
            mySession.isFinished = true;
            mySession.questionsIdsDone = questions?.map(q => q?.id_question);
            mySession.userId = user?.id;
            await mySession.save();
          }
          await ExamService.updateExamUserSessionAfterQuizzSerieCompletion(mySession);
        }

        //console.log({reponses});

        //if (selectedFileType === 'qcm') {
          // INSERT grade and answers in DB
        /*
          const { note } = await QCMService.corrigerQcmUtilisateur(
            {
              qcmId,
              examSessionId: null,
              sessionId: (mySession ? mySession?.id : null),
              reponses,
            },
            user?.id,
          );
          log.push(`Résultats de ${userName} (${user?.username}) importés - Note obtenue: ${note.toFixed(2)}`);
         */
        //} else if (selectedFileType === 'freeText') {


          ////////////////////////////////////
          // Calcul total points
          let totalPoints = 0;
          for (const reponse of reponses) {
            const { id_question } = reponse;
            let pointsObtained = reponse?.pointsObtained;
            if(!reponse?.hasOwnProperty('answers')) {
              // QROC we dont need to correct the question (maybe later auto correct)
            } else {
              // Corriger la question avec ABCD pour obtenir les points
              const actualQuestion = await models.Question.findByPk(id_question, {
                // include answers
                include: [models.QuestionAnswers],
              });
              const {
                reponsesUtilisateur,
                note: noteQuestion,
                reponsesJustes: reponsesJustesQuestion,
                erreurs: erreursQuestion,
              } = await QCMService.calculNoteQuestion(actualQuestion, reponse);
              pointsObtained = noteQuestion;
            }
            totalPoints += pointsObtained;
          }
          // On a bien le total points obtenus, mais pas encore inséré dans la table QcmStatsQuestion

          const qcmSessionId = (mySession ? mySession?.id : null);

          // Désactive la suppression des résultats (décision alex 05/03/24)
        /*
          let existingQcmStats = await models.QcmStats.findAll({
            where: {
              id_utilisateur: user?.id,
              id_qcm: qcmId,
              qcmSessionId
            },
          });
          if (existingQcmStats?.length > 0) {
            for (const qStat of existingQcmStats) {
              await QCMService.deleteQcmResult({id: qStat.id});
            }
          }
         */

          ////////////////////////////////////
          // Create final grade:
          const newStat = await models.QcmStats.create({
            id_utilisateur: user?.id,
            id_qcm: qcmId,
            note: totalPoints,
            date: new Date(),
            qcmSessionId
          });
          // END Create final grade
          ////////////////////////////////////

          ////////////////////////////////////
          // insertion reponses ABCD si y'en a
          const reponsesWithAnswers = reponses.filter(r => r?.hasOwnProperty('answers')) || [];

          let statId = newStat.id;
          await QCMService.insertUserAnswers(reponsesWithAnswers, user?.id, qcmSessionId, statId);
          ////////////////////////////////////

          ////////////////////////////////////
          // insertion reponses QROC si y'en a
          const reponsesWithPoints = reponses.filter(r => !r?.hasOwnProperty('answers')) || [];

          for (const rep of reponsesWithPoints) {
            let jsonAnswers = null; // Schema answer data
            if(rep.jsonAnswers && Array.isArray(rep.jsonAnswers)) {
              jsonAnswers = rep.jsonAnswers;
            }
            await models.QcmStatsQuestion.create({
              id_question: rep.id_question,
              id_utilisateur: user?.id,
              qcmSessionId,
              pointsObtained: rep.pointsObtained,
              //ponderatedPointsObtained: rep.ponderatedPointsObtained,
              //qcmStatsQuestion: rep.qcmStatsQuestion, // new way to insert in statsQuestionsAnswers fast
              statId,
              jsonAnswers
            });
          }
          ////////////////////////////////////

          await UserStatsService.incrementStat(user?.id, UserStatsService.OPERATIONS.incrementMCQDone);

          log.push(`Résultats de ${userName || ''} (${user?.username}) importés - Note obtenue: ${totalPoints.toFixed(2)}`);
        //}
      }
      console.log({ errors, log });
      return { log, errors };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

};