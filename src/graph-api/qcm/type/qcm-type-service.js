import { GraphQLError } from 'graphql';
import models from '../../../models/index.js';
import { RedisService } from '../../../service/redis-service.js';
import { PermissionService } from '../../permission/permission-service.js';
import {ROLES} from "../../authorization";


const CRUDTypeQcm = {
  createTypeQcm: async ({ typeQcm }, userId) => {
    try {
      return await models.TypeQcm.create(typeQcm);
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },
  updateTypeQcm: async ({ id, typeQcm }, userId) => {
    try {
      let updated = await models.TypeQcm.update(typeQcm, { where: { id: id } });
      return updated[0]; // oui
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },
  deleteTypeQcm: async ({ id, replacementId }, userId) => {
    try {
      if (replacementId) {
        const replacementType = await models.TypeQcm.findByPk(replacementId);
        console.log(replacementType?.name);
        if (!replacementType) {
          throw new GraphQLError('Replacement type not found');
        }
      }
      if (replacementId) {
        // Replace the type in all the places
        await models.QuestionTypeQcm.update({
          typeQcmId: replacementId,
        }, {
          where: {
            typeQcmId: id,
          },
        });
        await models.QcmTypeQcm.update({
          typeQcmId: replacementId,
        }, {
          where: {
            typeQcmId: id,
          },
        });
        await models.EventTypeQcm.update({
          typeQcmId: replacementId,
        }, {
          where: {
            typeQcmId: id,
          },
        });
        await models.ExamTypeQcm.update({
          typeQcmId: replacementId,
        }, {
          where: {
            typeQcmId: id,
          },
        });

      } else {
        // No replacement type, we unlink all the types
        await models.QuestionTypeQcm.destroy({
          where: {
            typeQcmId: id,
          },
        });
        // QCM
        await models.QcmTypeQcm.destroy({
          where: {
            typeQcmId: id,
          },
        });
        // events
        await models.EventTypeQcm.destroy({
          where: {
            typeQcmId: id,
          },
        });
        //events
        await models.ExamTypeQcm.destroy({
          where: {
            typeQcmId: id,
          },
        });
        //schema
        await models.SchemaLibraryTypeQcm.destroy({
          where: {
            typeQcmId: id,
          }
        });
      }
      // Finally delete the type
      return await models.TypeQcm.destroy({ where: { id } });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  async addTypeQcmToQcm({ typeQcmId, qcmId }, id) {
    try {
      const qcm = await models.Qcm.findByPk(qcmId);
      const typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      await qcm.addType_qcm(typeQcm);
      return typeQcm;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
  async removeTypeQcmFromQcm({ typeQcmId, qcmId }, id) {
    try {
      const qcm = await models.Qcm.findByPk(qcmId);
      const typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      await qcm.removeType_qcm(typeQcm);
      return typeQcm;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },


  async addDefaultTypeQuestionQcmToQcm({ typeQcmId, qcmId }, id) {
    try {
      const qcm = await models.Qcm.findByPk(qcmId);
      const typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      if(!qcm || !typeQcm) {
        throw new GraphQLError('Qcm or TypeQcm not found');
      }
      await models.QcmDefaultTypeQcmForQuestions.create({
        qcmId,
        typeQcmId,
      });
      return typeQcm;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
  async removeDefaultTypeQuestionTypeQcmFromQcm({ typeQcmId, qcmId }, id) {
    try {
      const qcm = await models.Qcm.findByPk(qcmId);
      const typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      if(!qcm || !typeQcm) {
        throw new GraphQLError('Qcm or TypeQcm not found');
      }
      await models.QcmDefaultTypeQcmForQuestions.destroy({
        where: {
          qcmId,
          typeQcmId,
        }
      });
      await qcm.removeType_qcm(typeQcm);
      return typeQcm;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
};


export const QcmTypeService = {
  ...CRUDTypeQcm,

  async getGroupesInTypeQcm(typeQcm, args, { models, me }) {
    if (typeQcm) {
      const ids = await models.TypeQcmGroups.findAll({ where: { typeQcmId: typeQcm.id } });
      return models.Groupe.findAll({
        where: { id: ids?.map(i => i.groupeId) },
      });
    }
  },

  async getTypesQcmsForGroup(groupId) {
    const tqg = await models.TypeQcmGroups.findAll({ where: { groupeId: groupId } });
    const typeQcmIds = tqg?.map(t => t.typeQcmId);
    return models.TypeQcm.findAll({ where: { id: typeQcmIds } });
  },

  // Multiple types
  async getGroupesInTypesQcm(typeQcmsIds) {
    const ids = await models.TypeQcmGroups.findAll({ where: { typeQcmId: typeQcmsIds } });
    return models.Groupe.findAll({
      where: { id: ids?.map(i => i.groupeId) },
    });
  },
  // Tous les Type QCM
  async getAllTypeQcm({ models, me }, forUser) {
    let userId = me.id;
    if(forUser && [ROLES.ADMIN, ROLES.SUB_ADMIN, ROLES.TUTEUR, ROLES.PARENT].includes(me.role)) {
      userId = forUser;
    }
    const user = await models.User.findByPk(userId);
    return PermissionService.getAvailableTypeQcmsForUser(user);
  },
  // Les types du qcm
  async getTypesQcm(qcm, args, { models }) {
    if (qcm) {
      const ids = await models.QcmTypeQcm.findAll({ where: { qcmId: qcm.id_qcm } });
      return models.TypeQcm.findAll({
        where: { id: ids?.map(i => i.typeQcmId) },
      });
    }
  },
  // Types par défaut pour les questions du qcm
  async getDefaultQuestionsTypesQcm(qcm, args, { models }) {
    if (qcm) {
      const ids = await models.QcmDefaultTypeQcmForQuestions.findAll({ where: { qcmId: qcm.id_qcm } });
      return models.TypeQcm.findAll({
        where: { id: ids?.map(i => i.typeQcmId) },
      });
    }
  },
  async getQcmIdsInAnnale() {
    const tqcm = await models.TypeQcm.findOne({ where: { name: 'Annale' } });
    if (!tqcm) {
      return [];
    }
    const typeQcm = await models.QcmTypeQcm.findAll({ where: { typeQcmId: tqcm.id } });
    const mcq = await models.Qcm.findAll({
      where: {
        id_qcm: typeQcm.map(t => t.qcmId),
      },
      attributes: ['id_qcm'],
    });
    return mcq.map(q => q.id_qcm);
  },

  async getQcmIdsInType(typeQcmId) {
    const REDIS_KEY = `getQcmIdsInType-${typeQcmId}`;
    let cachedValue = await RedisService.get(REDIS_KEY);
    if (cachedValue) {
      return cachedValue;
    }
    const typeQcm = await models.QcmTypeQcm.findAll({
      where: { typeQcmId },
      raw: true,
    });
    const qcmIds = typeQcm?.map(q => q.qcmId);
    await RedisService.set(REDIS_KEY, qcmIds);
    return qcmIds
  },

  async getExamIdsInType(typeQcmId) {
    const REDIS_KEY = `getExamIdsInType-${typeQcmId}`;
    let cachedValue = await RedisService.get(REDIS_KEY);
    if (cachedValue) {
      return cachedValue;
    }
    const typeQcm = await models.ExamTypeQcm.findAll({
      where: { typeQcmId },
      raw: true,
    });
    const examIds = typeQcm?.map(q => q.examId);
    await RedisService.set(REDIS_KEY, examIds);
    return examIds
  },

  /* TODO CACHE */
  async getQuestionIdsInTypes(typeQcmIds) {
    if(!typeQcmIds || typeQcmIds?.length === 0) {
      return [];
    }
    const qtc = await models.QuestionTypeQcm.findAll({ where: { typeQcmId: typeQcmIds }, raw: true});
    return qtc?.map(q => q.questionId);
  }

};