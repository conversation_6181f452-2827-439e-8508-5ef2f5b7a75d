import gql from 'graphql-tag';

export default gql`
    scalar JSON
    extend type Query {
        "Get exercise serie by id"
        qcm(id: ID!): Qcm
        
        "LEGACY WILL BE REMOVE search exercise serie with search filter for user, used in MCQs tab and forum"
        searchQcms(filter: QcmSearchFilter): [Qcm]
        "Paginated Exercise series search with filter for user"
        searchQcmsV2(filter: QcmSearchFilter): PaginatedExerciseSeriesResult
        "Admin search exercise series with search filter"
        adminSearchQcms(filter:AdminQcmSearchFilter):[Qcm]
        
        "Admin search exercise series (paginated)"
        adminSearchExerciseSeries(filter:AdminQcmSearchFilterPaginated): PaginatedExerciseSeriesResult
        
        "Get my ranking on specific exercise serie, and for specific groups"
        monClassementQcm(id: ID!, groupIds: [ID], userId: ID): ClassementQCM
        monClassementPondereQcm(id: ID!, groupIds: [ID]): ClassementQCM

        "Get exercise series in subcategory id"
        qcmsInSousCategorie(id: ID!): [Qcm]

        allTypeQcm(forUser: ID): [TypeQcm]
        typeQcm(id: ID!): TypeQcm

        "Graph reponses correction"
        getGraphCorrection(id_qcm: ID!, sessionId: ID, userId: ID, groupIds: [ID]): JSON

        "Graph progression"
        getGraphProgression(userId: ID, ueId: ID!): JSON

        "Graph histogramme certitude"
        getPointsByCertainty(id_qcm: ID!, sessionId: ID, userId: ID): JSON

        "Revision module (legacy will be removed)"
        revisionTips(filter: RevisionTipsFilter): RevisionTipsResponse

        "Exercise module preview (course, notions). Returns done/undone exercises count."
        exerciseModulePreview(filter: ExerciseModulePreviewFilter): ExerciseModulePreviewResponse

        "User savedState to restore exercise serie if needed"
        qcmSavedState(qcmId: ID, sessionId: ID): QcmSavedState

        "For a qcmId, return qcm informations and each question informations + update History"
        getQcmDetailedInformation(id:ID!):QcmEachQuestionDescription

        "Generate exercise serie preview for generator"
        generateQcmPreview(params: GeneratorParams!): QcmGeneratorPreview
        
        "Get question serie stats for user or specific groups (for correction page)"
        getQcmCorrectionStats(id: ID!, sessionId: ID, userId: ID, groupIds: [ID], statId: ID): QcmResult

        "Get AI analysis (serie correction)"
        getAiAnalysis(qcmId: ID, userId: ID, sessionId: ID, statId: ID, force: Boolean): JSON
        
        "Check if current user can do this exercise serie"
        canIDoQcm(id: ID!): Boolean
    }

    extend type Mutation {
        # CRUD exercise serie
        createQcm(qcm: QcmInput!): Qcm!
        updateQcm(id: ID!, qcm: QcmInput!): Boolean
        deleteQcm(id: ID!): Boolean!

        saveQcmState(input: SaveQcmInput!): Boolean

        "Get exercise serie result"
        corrigerQcmUtilisateur(qcmId: ID!, sessionId: ID, examSessionId: ID, reponses: [QcmReponsesInput], seconds: Int): QcmResult
        "exercise serie generator: creates an MCQ from Generator parameters"
        generateQcm(params: GeneratorParams!): QcmGeneratorResponse

        "exercise serie generator advanced: creates an MCQ from Generator parameters with question packs"
        generateQcmFromPacks(params: [GeneratorParams]): QcmGeneratorResponse
        
        "Generate session from element single exercise"
        generateSessionFromExerciseElement(formationElementId: ID!): QcmGeneratorResponse

        "Generate session from element Diapo synthese"
        generateSessionFromDiapoSyntheseElement(formationElementId: ID!): QcmGeneratorResponse
        
        "Exercise serie generator: get MCQ result from generator answers"
        corrigerQcmGenerated(questions: JSON, reponses: [QcmReponsesInput]): QcmResult

        "Delete exercise serie result"
        deleteQcmResult(id: ID!): Boolean

        "Recalculate question serie stats"
        recalculateQcmStats(qcmId: ID!): Boolean

        # Special Ops exercise serie
        "Duplicate question serie, exercises and exercise items"
        duplicateQcm(id: ID!, duplicate: Boolean): Qcm
        "Create a new question serie from selected question serie"
        fusionQcm(ids: [ID]): Qcm
        "Update question serie grades"
        updateAllGradesForMcq(id: ID): Boolean
        "Import question serie from file"
        importFullMcqFromJson(file: Upload, ueId: ID, typesIdsForImport: [ID], annee: String): Qcm

        "Import question serie from XLS (beta)"
        importFullMcqFromXls(file: Upload, id_qcm: ID!): Boolean

        "Import question serie user results from XLS (for Antemed)"
        importMcqUserResultsFromXls(file: Upload, qcmId: ID!, examQuestionSerieId: ID, selectedFileType: String): JSON

        importCSV(file: Upload, qcmId: ID, type: String): Boolean

        exportJsonToXLSDownload(input: JSON, name: String, addUUID: Boolean = false): String

        "Update question serie good answers synthesis manually"
        updateMyGoodAnswersSynthesis: Boolean

        # CRUD type question serie
        createTypeQcm(typeQcm: TypeQcmInput!): TypeQcm!
        updateTypeQcm(id: ID!, typeQcm: TypeQcmInput!): Boolean
        deleteTypeQcm(id: ID!, replacementId: ID): Boolean!

        # Type question serie serie
        addTypeQcmToQcm(typeQcmId: ID!, qcmId: ID!): TypeQcm!
        removeTypeQcmFromQcm(typeQcmId: ID!, qcmId: ID!): TypeQcm!

        # Default Questions type for exercise serie 
        addDefaultTypeQuestionQcmToQcm(typeQcmId: ID!, qcmId: ID!): TypeQcm!
        removeDefaultTypeQuestionTypeQcmFromQcm(typeQcmId: ID!, qcmId: ID!): TypeQcm!
      
        # Try export / import des exercise serie 
        testImportMapping(file: Upload,resolvedMapping:ImportQcmResolvedMapping,doImport:Boolean):ImportMappingToResolve!

        # link questionId with QcmId 
        addLinkQuestionIdWithQcmId(questionId:ID!,qcmId:ID!):Boolean
      
        # remove QuestionId With QcmId
        removeLinkQuestionIdWithQcmId(questionId:ID!,qcmId:ID!):Boolean

        "mass delete exercise series by type (superadmin exostaff only)"
        massDeleteSeriesByType(typeQcmIds: [ID]): Int
    }
    type ImportTest{
      formationElement:FormationElement
      titleId:[ID]
    }
    
    type PaginatedExerciseSeriesResult {
        "Total"
        count: Int
        "Results"
        exerciseSeries: [Qcm]
    }
    
    "La structure de donnée que retournera la fonction d'import de QCM"
    type ImportMappingToResolve{
      qcmMappingToResolve:[QcmMappingToResolve!],
      questionMappingToResolve:[QuestionMappingToResolve!],
      formationElementMappingToResolve:[FormationElementMappingToResolve!]
      qcmTitre:String
      qcmDescription:String
      numberExercises:Int
      newQcmId:Int
    }
    
    type QuestionMappingToResolve{
      coursId:ID,
      description:String!
      mcqScaleId:ID
      exerciseTypeId:ID
    }
    
    type FormationElementMappingToResolve{
      coursId:ID,
      description:String!
      titleId:ID
    }
    
    
    "Les link à résolve pour l'importation de QCM"
    type QcmMappingToResolve{
      "Link entre QCM <-> UE"
      ueId:ID

      "Link entre QCM <-> Qcm Type"
      serieTypeId:ID
      defaultQuestionTypeId:ID
      
      "Description applicable pour chaque link à résolve"
      description:String!
    }
    
    
    input ImportQcmResolvedMapping{
      qcmMappingResolved:QcmMappingResolved
      questionMappingResolved:QuestionMappingResolved
      formationElementMappingResolved:FormationMappingResolved
    }
    
    input FormationMappingResolved{
      mappingTitle:[Mapping]
    }
    
    input QuestionMappingResolved{
      mappingCours:[Mapping]
      mappingScaleId:[Mapping]
      newExercisesTypes:[Mapping]!
    }
    
    
    "Le mapping résolu pour l'importation de QCM"
    input QcmMappingResolved{

      "La nouvelle Array de Serie Id"
      newSerieTypeId:[ID]!
      
      "la nouvelle array de question par default"
      newQuestionDefaultTypes:[ID]!
      
      "Le mapping des UE"
      mappingUe:[Mapping]
      
      "le nouveau Titre du qcm"
      newQcmTitre:String
      "La nouvelle description du qcm"
      newQcmDescription:String
    }
    
    input Mapping{
      "Le UE Id du QCM avant importation"
      oldUeId:ID
      "Le UE ID du qcm Pour l'importation"
      newUeId:ID
      
      oldCoursId:ID
      newCoursId:ID
      
      oldMcqScale:ID
      newMcqScale:ID
      
      oldTitleId:ID
      newTitleId:ID
      
      oldExerciseTypeId:ID
      newExerciseTypeIdArray:[ID]
    }
    
    
    type QcmGeneratorPreview {
        exercisesCount: Int
        questionIds: [ID]
        doneQuestionsIds: [ID]
    }

    input SaveQcmInput {
        qcmId: ID
        sessionId: ID
        formValues: JSON
        time: Int
    }
    type QcmSavedState {
        id: ID!
        qcmId: ID
        sessionId: ID
        formValues: JSON
        time: Int
        createdAt: Date
        updatedAt: Date
    }

    type QcmGeneratorResponse {
        isAnswerOrderRandom: Boolean
        questions: [Question]
        qcmSessionId: ID
        secondsPerExercise: Int
        hasTimer: Boolean
    }

    input QcmReponsesInput {
        "Question ID"
        id_question: Int
        "Answers checked as 'true'"
        answers: [ID] # Selected answers ID
        "Answers checked as 'false'"
        answers_false: [ID]
        "Niveau de confiance utilisateur"
        certainty: Int
        jsonAnswers: JSON
    }

    input GeneratorParams {
        ueIds: [ID]
        categoriesIds: [ID]
        coursIds: [ID]
        typeQcms: [String] # annale, medibox, les deux
        
        anneeDebut: String
        anneeFin: String
        
        annees: [ID]
        
        nombreQuestions: String
        infiniteQuestionByQuestion: Boolean
        isQuestionOrderRandom: Boolean
        isAnswerOrderRandom: Boolean

        secondsPerExercise: Int
        hasTimer: Boolean
        hasTimerPerQuestion: Boolean
        goToNextQuestionOnTimerEnd: Boolean
        
        createQcmSession: Boolean
    }


    input AdminQcmSearchFilter{ # rework de la query pour filtrer les qcm afin de pouvoir input plusieures années, et autre.
      annees:[ID]
      titre:String
      ueIds:[ID]
      userIds:[ID]
      typeQcms:[ID]
      coursId:[ID]
      dateCreationStart:Date
      dateCreationEnd:Date
      dateLastModifStart:Date
      dateLastModifEnd:Date
      isPublished:Boolean
      chronometre:Boolean
      deleted:Boolean
    }
    
    input AdminQcmSearchFilterPaginated { # rework de la query pour filtrer les qcm afin de pouvoir input plusieures années, et autre.
        annees:[ID]
        titre:String
        ueIds:[ID]
        userIds:[ID]
        typeQcms:[ID]
        coursId:[ID]
        dateCreationStart:Date
        dateCreationEnd:Date
        dateLastModifStart:Date
        dateLastModifEnd:Date
        isPublished:Boolean
        chronometre:Boolean
        deleted:Boolean
        offset:Int
        limit:Int
    }

    input QcmSearchFilter {
        offset: Int
        limit: Int
        
        annees: [ID]
        annee: String
        titre: String
        ue: Int #legacy
        ueId: ID # UE
        sousCategorieId: ID
        annale: Boolean #legacy to be removed do not use
        typeQcms: [ID]
        coursId: ID
        isPublished: Boolean
        chronometre: Boolean
        deleted: Boolean
        searchText: String
    }

    type ClassementQCM {
        notes: [QcmNote]
        monClassement: Int
        total: Int
        Q1: QcmNote
        Q2: QcmNote
        Q3: QcmNote
        notesParEffectif: [NoteParEffectif]
    }

    type NoteParEffectif {
        note: Float
        effectif: Int
    }

    "MCQ grade"
    type QcmNote {
        id: ID
        id_qcm: ID
        id_utilisateur: ID
        note: Float
        ponderatedGrade: Float
        #date: Date
    }

    "MCQ type"
    type Qcm {
        "MCQ ID"
        id_qcm: ID!
        "Legacy link ID"
        id_lien: ID!
        "MCQ Title"
        titre: String
        titre_en: String
        titre_it: String
        titre_de: String
        titre_es: String
        "MCQ Description"
        description: String
        description_en: String
        description_it: String
        description_de: String
        description_es: String
        "MCQ image URL"
        url_image: String
        "Creation date"
        date_creation: String
        id_createur: ID
        date_modif: String
        "Legacy UE (unused will be removed)"
        ue: Int # ue legacy
        "Is MCQ deleted or not"
        deleted: Boolean
        "If timer is enabled"
        chronometre: Boolean
        goToNextQuestionWhenTimesUp: Boolean
        shouldResumeTime: Boolean
        chronoByQuestionOrGlobal: String

        "Year"
        annee: Int
        "Creator username"
        pseudo_createur: String
        "If the author has been manualy changed"
        isAuthorChanged: Boolean
        "Whether it's public or not (legacy, could be used in future)"
        public: Boolean
        "Deprecated Is annale do not use"
        annale: Boolean
        "Is MCQ question by question fullscreen or not"
        isFullscreen: Boolean
        "Does this MCQ has checkboxes or radio buttons"
        hasCheckboxes: Boolean
        "Randomize questions order"
        randomizeQuestions: Boolean
        "Randomize answer items order"
        randomizeQuestionsAnswers: Boolean
        "If fullscreen, will show correction at each step"
        showCorrectionAtEachStep: Boolean
        "Legacy unique link ID"
        link: String
        external: ID
        "Timer delay in seconds"
        timer_delay: Int
        "Difficulty level"
        difficulty: Float
        "Number of views"
        views: Int
        "Number of questions"
        nombreQuestions: Int
        "Maximum points attainable"
        maximumPoints: Float

        "Questions that belong to the MCQ"
        questions: [Question]

        "Cours liés à cette entité QCM"
        cours: [Cours] # cours liés dans ce QCM
        "Cours impliqués dans les questions de ce QCM"
        coursImpliques: [Cours] # les cours impliqués dans les questions de ce QCM
        ueCategoriesImpliquees: [UECategory]
        "Parent category (UE)"
        UE: UE
        "Parent category ID"
        UEId: Int
        #ueCategory: UECategory
        "Result for a session and / or user, or for specific groups"
        resultat(sessionId: ID, userId: ID, groupIds: [ID], statId: ID): QcmResult
        
        "All user results for this MCQ"
        resultsForUser(userId: ID): [QcmResult]
        "Stats"
        statistiques: QcmResult
        "All user results"
        resultatsEleves(groupIds: [ID], firstTimeOnly: Boolean): [QcmResult] # tous resultats eleves de ce QCM (current year, avec groupes optionnels)
        countResultatsEleves: Int
        "Last forum post"
        lastPost: Post
        "Number of posts"
        postsNumber: Int
        "Is it published (visible) or not"
        isPublished: Boolean
        "Times MCQ can be done"
        timesItCanBeDone: Int
        mySession: QcmSession

        groupQuestionsByTheme: Boolean

        "Mcq notions"
        notions: [Notion]

        "Mcq types"
        type: [TypeQcm]
        defaultQuestionsType: [TypeQcm]

        hasExternalQuestions: Boolean
        questionPickingStrategy: String

        "Correction configuration (show/hide modules mostly)"
        correctionConfig: JSON

        "True if correction AI analysis is activated"
        enableAiAnalysis: Boolean
    }

    input QcmInput {
        titre: String
        "MCQ Title"
        titre_en: String
        titre_it: String
        titre_de: String
        titre_es: String
        "MCQ Description"
        description: String
        description_en: String
        description_it: String
        description_de: String
        description_es: String
        url_image: String
        date_creation: String
        id_createur: ID
        date_modif: String
        ue: Int # ue legacy
        UEId: Int
        timer_delay: Int
        isPublished: Boolean
        deleted: Boolean
        chronometre: Boolean
        goToNextQuestionWhenTimesUp: Boolean
        shouldResumeTime: Boolean
        chronoByQuestionOrGlobal: String
        annee: Int
        pseudo_createur: String
        public: Boolean
        annale: Boolean
        groupQuestionsByTheme: Boolean
        link: String
        external: ID
        difficulty: Float
        views: Int
        isFullscreen: Boolean
        hasCheckboxes: Boolean
        timesItCanBeDone: Int
        "Randomize questions order"
        randomizeQuestions: Boolean
        "Randomize questions order"
        randomizeQuestionsAnswers: Boolean
        "If fullscreen, will show correction at each step"
        showCorrectionAtEachStep: Boolean
        "if authors has been manually set"
        isAuthorChanged: Boolean
        hasExternalQuestions: Boolean
        questionPickingStrategy: String

        correctionConfig: JSON
        typeQcmIDS: [ID]
        defaultQuestionsTypeQcmIDS: [ID]

        "True if correction ai analysis is activated"
        enableAiAnalysis: Boolean
    }


    "Exercise serie result"
    type QcmResult {
        id: ID
        "Question serie ID"
        id_qcm: ID
        id_utilisateur: ID
        user: User
        "Final grade"
        note: Float
        "Ranking"
        classement: Int
        "Temps passé en secondes (série classique)"
        seconds: Int
        "Average grade for group"
        moyenne: Float
        "average time spent for group"
        averageSeconds: Float
        maxGrade: Float
        minGrade: Float
        date: Date
        reponses: [QcmItemResponse]
        qcmSessionId: ID
        session: QcmSession
        ponderatedGrade: Float
        maxPoints: Float
        count: Int
        isFirstTime: Boolean
        aiAnalysis: JSON
    }

    "Type QCM"
    type TypeQcm {
        id: ID
        name: String
        description: String
        contentType: String
        groupes: [Groupe]
        updatedAt: Date
        createdAt: Date
    }
    "Type QCM input"
    input TypeQcmInput {
        name: String
        description: String
        contentType: String
    }

    # choix utilisateur
    type QcmItemResponse {
        # TODO change
        id_question: ID
        A: Boolean
        B: Boolean
        C: Boolean
        D: Boolean
        E: Boolean
        F: Boolean
        G: Boolean
        erreurs: Int
    }


    input RevisionTipsFilter {
        startDate: Date
        endDate: Date
        coursId: ID
        notionId: ID
    }

    input ExerciseModulePreviewFilter {
        module: String
        coursId: ID
        notionId: ID
        moduleQuickAccessId: ID
        schemaAllOrCustom: String
        schemaVariants: [String]
    }
    
    "Legacy revision module response, will be removed soon. Use exerciseModulePreview instead."
    type RevisionTipsResponse {
        questions: [Question]
        allQuestions: [Question]
        
        allQuestionsCount: Int
        countUndoneExercises: Int
        
        questionsAnswered: Int
        schemaImagesPreview:[String]
    }

    type ExerciseModulePreviewResponse {
        allQuestionsCount: Int
        countUndoneExercises: Int
        questionsAnswered: Int
        schemaImagesPreview: [String]
    }

    "New type that compile Author and modifications for a question"
    type QuestionEditHistory{
        "the linked question"
        question:Question
        "the collection of modification log"
        questionEditHistory:[Log]

    }

    "New type that compile, each Questions and Update"
    type QcmEachQuestionDescription{
        "the linked Qcm"
        qcm:Qcm
        "the questions and updated linked to this Qcm"
        questions:[QuestionEditHistory]
        "qcm and question edit history : dans le getLog(filter) ça sélectionne aussi les "
        qcmEditHistory:[Log]
        "Dernier log en date (premier élément de qcmEditHistory)"
        lastestEditLog:Log
    }
`