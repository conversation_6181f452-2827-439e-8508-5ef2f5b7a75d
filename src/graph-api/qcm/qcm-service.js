import { GraphQLError } from 'graphql';
import _ from 'lodash';
import moment from 'moment';
import Sequelize from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import { isDev } from '../../index.js';
import models, { sequelize } from '../../models';
import { CoursTypesQcmSettings_MODULE_TYPES } from '../../models/cours_types_qcm_settings.js';
import { ELEMENTS_TYPE } from '../../models/formation/formation_element';
import { LOG_OPERATIONS } from '../../models/log/log.js';
import {
  McqScaleQuestionType,
  McqScaleRulesProperties,
  McqScaleRulesSettingsValues,
  McqScaleType,
} from '../../models/qcm/mcq_scale.js';
import { RedisService } from '../../service/redis-service.js';
import { ROLES } from '../authorization.js';
import { ExamService } from '../exam/exam-service.js';
import { UploadService } from '../file/upload-service.js';
import { FormationService } from '../formation/formation-service';
import { GroupeService } from '../groupe/groupe-service.js';
import { EXOQUALIZE } from '../helpers.js';
import LogService from '../log/log-service.js';
import { PermissionService } from '../permission/permission-service.js';
import { UserViewHistoryService } from '../user/user-view-history-service.js';
import { UserStatsService } from '../user/userStats-service.js';
import {
  FillInTheBlanksCorrection,
} from './exercise-correction/fill-in-the-blanks';
import { calculateReorderScoreIntegrated } from './exercise-correction/reorder-elements-integration.js';
import QcmGraphService from './qcm-graph-service.js';
import { nb_questions_asked } from './qcm-smart-positioning-service.js';
import { QCMStatsService } from './qcm-stats-service.js';
import { QuestionsService } from './questions/questions-service.js';
import { McqScaleService, SHOW_SCALE_LOG } from './scales/mcq-scale-service.js'
import { QcmTypeService } from './type/qcm-type-service.js';

const Op = Sequelize.Op;

const { QueryTypes } = require('sequelize');

export const CONTENT_TYPE_VALUES = {
  // COURSE: 'COURSE',
  EXERCISE: 'EXERCISE',
  EXERCISE_SERIES: 'EXERCISE_SERIES',
  EVENTS: 'EVENTS',
  EXAMS: 'EXAMS',
  CHALLENGES: 'CHALLENGES',
  // ELEMENTS: 'ELEMENTS',
};

export const CERTAINTY_KEYS = {
  AU_HASARD: 0,
  PEU_SUR: 1,
  MOYENNEMENT_SUR: 2,
  CERTAIN: 3,
};

export const alphabetArray = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

export const CHRONO_BY_QUESTION_OR_GLOBAL = {
  GLOBAL: 'globalTime',
  BY_QUESTION: 'timeByQuestion',
};

export const defaultSchemaText = 'Sur ce schéma, cliquez sur les légendes indiquées';
export const defaultSchemaFillInLegendsText = 'Sur ce schéma, complétez les légendes indiquées';
export const defaultFillInTheBlankText = 'Complétez les mots manquants';

export const AllSchemaQuestionTypesArray = [
  'SCHEMA_POINT_AND_CLICK',
  'SCHEMA_FILL_IN_LEGENDS',
];

export const QuestionAnswerType = {
  RADIO: 'RADIO',
  CHECKBOX: 'CHECKBOX',
  FREE_TEXT: 'FREE_TEXT',

  ALPHANUMERICAL: 'ALPHANUMERICAL',
  NUMERICAL: 'NUMERICAL',
  ALPHANUMERICAL_OR_NUMERICAL: 'ALPHANUMERICAL_OR_NUMERICAL',
  TRUE_OR_FALSE_OR_UNDEFINED: 'TRUE_OR_FALSE_OR_UNDEFINED',

  SCHEMA_POINT_AND_CLICK: 'SCHEMA_POINT_AND_CLICK',
  SCHEMA_FILL_IN_LEGENDS: 'SCHEMA_FILL_IN_LEGENDS',
  FILL_IN_THE_BLANKS: 'FILL_IN_THE_BLANKS',

  FLASHCARD: 'FLASHCARD',

  REORDER_ELEMENTS: 'REORDER_ELEMENTS',

  /* Not used yet */
  UNIQUE_CHOICE_IN_LIST: 'UNIQUE_CHOICE_IN_LIST',
  MULTIPLE_CHOICE_IN_LIST: 'MULTIPLE_CHOICE_IN_LIST',
};
/* LEGACY to be removed  ///// Update 26/01/24   valony => utilisé par qcmImport*/
const CRUDQcm = {
  createQcm: async ({ qcm }, userId, ip) => {
    try {
      let user = await models.User.findByPk(userId);
      const { typeQcmIDS, defaultQuestionsTypeQcmIDS, ...qcmInput } = qcm;
      let toCreate = {
        ...qcmInput,
        id_createur: userId,
        pseudo_createur: user.username,
        id_lien: uuidv4(),
        date_creation: moment().toDate(),
        date_modif: moment().toDate(),
        hasExternalQuestions: true,
      };
      const createdQcm = await models.Qcm.create(toCreate);
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Qcm.Create.action,
        foreignIds: { qcmId: createdQcm.id_qcm, userId },
        ip,
        models, userId,
      });
      // Add types to qcm
      for (const typeQcmId of typeQcmIDS) {
        await QcmTypeService.addTypeQcmToQcm({ typeQcmId, qcmId: createdQcm.id_qcm }, user);
      }
      if (defaultQuestionsTypeQcmIDS?.length > 0) {
        for (const typeQcmId of defaultQuestionsTypeQcmIDS) {
          await QcmTypeService.addDefaultTypeQuestionQcmToQcm({ typeQcmId, qcmId: createdQcm.id_qcm }, user);
        }
      }
      return createdQcm;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
  updateQcm: async ({ id, qcm }, userId, ip) => {
    try {
      qcm.date_modif = moment().toDate();
      const oldQcm = await models.Qcm.findByPk(id);

      // If MCQ was unpublished and is now published
      if (qcm?.isPublished && !oldQcm?.isPublished) {
        await LogService.logAction({
          logOperation: LOG_OPERATIONS.Qcm.PublishMcq.action,
          ip,
          foreignIds: { qcmId: id, userId },
          models, userId,
        });
        // Auto-publish all questions
        await QuestionsService.publishAllQuestionsForMcq(id);
      }
      // If MCQ was published and is now unpublished
      if (!qcm?.isPublished && oldQcm?.isPublished) {
        await LogService.logAction({
          logOperation: LOG_OPERATIONS.Qcm.UnPublishMcq.action,
          ip,
          foreignIds: { qcmId: id, userId },
          models, userId,
        });
        // Auto-unpublish all questions
        await QuestionsService.unPublishAllQuestionsForMcq(id);
      }

      if (qcm?.timer_delay && (parseInt(qcm?.timer_delay) !== parseInt(oldQcm?.timer_delay))) {
        // Supprime état enregistré si chrono change
        await QCMService.deleteSavedStateForQcm(id);
      }

      if (qcm?.id_createur != oldQcm?.id_createur) {
        await LogService.logAction({
          logOperation: LOG_OPERATIONS.Qcm.ModifyAuthor.action,
          ip,
          foreignIds: { qcmId: id, userId },
          logData: {
            'previousAuthorId': oldQcm?.id_createur,
            'newAuthorId': parseInt(qcm?.id_createur),
            'previousAuthorUsername': oldQcm?.pseudo_createur,
            'newAuthorUsername': qcm?.pseudo_createur,
          },
          models,
          userId,
        });

      }

      // Log update
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Qcm.Update.action,
        ip,
        foreignIds: { qcmId: id, userId },
        models, userId,
      });

      // Update qcm
      let updated = await models.Qcm.update(
        qcm, {
          where: { id_qcm: id },
        },
      );

      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
  deleteQcm: async ({ id }, userId, ip) => {
    try {
      let qcm = await models.Qcm.findByPk(id);
      const newState = !qcm.deleted;

      qcm.deleted = newState;

      // Si on delet un QCM (newState === true), alors on le dépublie. Si on le restaure, on conserve le même isPublished
      qcm.isPublished = newState === true ? false : qcm.isPublished;

      // Delete dates diffs
      if (qcm && qcm.deleted) {
        await models.DateDiffusion.destroy({ where: { qcmId: id } });
      }

      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Qcm.Restore.action,
        ip,
        foreignIds: { qcmId: id, userId },
        models, userId,
      });
      return !!qcm.save();
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
};

const CRUDTypeQuestion = {
  createTypeQuestion: async ({ typeQuestion }, userId) => {
    try {
      return await models.TypeQuestion.create(typeQuestion);
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },
  updateTypeQuestion: async ({ id, typeQuestion }, userId) => {
    try {
      let updated = await models.TypeQuestion.update(typeQuestion, { where: { id: id } });
      return updated[0]; // oui
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },
  deleteTypeQuestion: async ({ id }, userId) => {
    try {
      return await models.TypeQuestion.destroy({ where: { id: id } });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  async addTypeQuestionToQuestion({ typeQuestionId, questionId }, id) {
    try {
      const question = await models.Question.findByPk(questionId);
      const typeQuestion = await models.TypeQuestion.findByPk(typeQuestionId);
      await question.addType_question(typeQuestion);
      return typeQuestion;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
  async removeTypeQuestionFromQuestion({ typeQuestionId, questionId }, id) {
    try {
      const question = await models.Question.findByPk(questionId);
      const typeQuestion = await models.TypeQuestion.findByPk(typeQuestionId);
      await question.removeType_question(typeQuestion);
      return typeQuestion;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
};

/* TODO move to questionService */
const CRUDQuestion = {

  // TODO remove?? Legacy ? we should not get parent category from question, but from cours instead
  updateQuestionCategoryFromCours: async (question) => {
    // find cours category
    if (question.linkCoursId) {
      let cours = await models.Cours.findByPk(question.linkCoursId);
      if (cours) {
        let categoryId = cours.uecategoryId;
        question.id_sous_categorie = categoryId;
      }
    }
    return question;
  },


  createQuestion: async ({ question }, userId, ip) => {
    try {

      // Definition automatique de divers champs
      question.date_creation = moment().toDate();
      question.date_modif = question.date_creation;
      question.authorId = userId;

      // Initialize settings for REORDER_ELEMENTS questions
      if (question.type === 'REORDER_ELEMENTS') {
        question.settings = {
          ...question.settings,
          correctOrder: [
            { id: 'element-0', content: '', order: 0 },
            { id: 'element-1', content: '', order: 1 }
          ]
        };
      }

      // Import et uploads des fichiers images
      if (question.imageQuestion) {
        question.url_image_q = await UploadService.uploadQcmFile(question.imageQuestion);
      }
      if (question.imageExplication) {
        question.url_image_explication = await UploadService.uploadQcmFile(question.imageExplication);
      }

      // update du link question <-> Catégorie / cours => Obsolète
      question = await QCMService.updateQuestionCategoryFromCours(question);

      // Si un id_qcm renseigné =>  Obsolète => Maintenant on passe par la table de liaison questions_qcm
      // Alors on delet le link de question
      let qcm = null;
      if (question?.id_qcm) {
        qcm = await models.Qcm.findByPk(question?.id_qcm);
        delete question.id_qcm;
      }

      // Create question et key redis
      const q = await models.Question.create(question);

      const REDIS_KEY = `availableQuestionsIdsForUser-${userId}`;
      await RedisService.delete(REDIS_KEY);

      // Si il y avait un QCM relié à la question.
      if (qcm) {

        // Add default type qcm to question on creation
        await RedisService.delete(`mcqMaximumPoints-${qcm?.id_qcm}`);

        // On récupère les defaults typeQcmForQuestion depuis le QCM
        const ids = await models.QcmDefaultTypeQcmForQuestions.findAll({ where: { qcmId: qcm.id_qcm } });

        // On récupère les type qcm de la db
        const typeQcms = await models.TypeQcm.findAll({
          where: { id: ids?.map(i => i.typeQcmId) },
        });

        // Obsolète ??? => q.addType_qcm est obsolète ? => Oui => Maintenant y a une table 'questions_type_qcm'
        for (const typeQcm of typeQcms) {
          await q.addType_qcm(typeQcm);
        }

        // Update de la modif du qcm  et save
        qcm.date_modif = moment().toDate();
        await qcm.save();
      } else {
        // pas de série parente, on met la question dans les types autorisés pour l'utilisateur
        const user = await models.User.findByPk(userId, { raw: true });

        // On récupère les typeQcms autorisés pour l'user
        const typeQcms = await PermissionService.getAvailableTypeQcmsForUser(user);

        // On filtres le contenu de la table typeQcm pour des éléments, ici EXERCISE
        const exercisesSerieTypes = typeQcms?.filter(ty => ty?.contentType === CONTENT_TYPE_VALUES.EXERCISE);

        // On récupère les ID des types Exercises ID autorisés pour l'user
        const typeQcmIds = exercisesSerieTypes.map(t => t.id);

        // Pour chaque typeId des typeId autorisés pour l'user, on mets ces types là pour la question
        for (const typeId of typeQcmIds) {
          const typeQcm = await models.TypeQcm.findByPk(typeId);
          await q.addType_qcm(typeQcm); // Obsolète
        }
      }

      // Mise de q.isCheckbox à null
      q.isCheckbox = null;

      // Save en base de la questionn
      await q.save();

      // Update question order among other questions
      // Gestion de l'ordre interne des questions ?? Obsolète ?? Maintenant y a une table
      let updated = await models.Question.findByPk(q.id_question);
      updated.order = updated.id_question; // default order
      await updated.save();

      // add exercise in question serie
      await models.QuestionsQcm.create({
        qcmId: qcm?.id_qcm,
        questionId: q.id_question,
        order: updated.order,
      });

      /* Log question creation */
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Question.Create.action,
        foreignIds: {
          questionId: q.id_question,
          userId,
        },
        ip,
        models, userId,
      });

      return updated;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /* Update, delete images mcq only */
  async updateImageQcm({ type, file, action, id }, userId, ip) {
    try {
      let question, answer;
      switch (type) {
        case 'question':
          question = await models.Question.findByPk(id);
          if (question) {
            if (question.url_image_q) {
              // Delete old image
              await UploadService.deleteQcmFile(question.url_image_q);
              question.url_image_q = null;
            }
            if (action !== 'delete') {
              // If no delete
              question.url_image_q = await UploadService.uploadQcmFile(file);
            }
            await question.save();
            return question.url_image_q;
          }
        case 'reorder_element':
          // For reorder elements, just upload the file and return the filename
          // The frontend handles storing it in question.settings.correctOrder
          if (action === 'delete') {
            // Note: File deletion is handled by frontend when question is saved
            // This is because we don't have direct access to the filename here
            return null;
          } else {
            // Upload new file and return filename
            const filename = await UploadService.uploadQcmFile(file);
            return filename;
          }
          break;
        case 'question_explanation':
          question = await models.Question.findByPk(id);
          if (question) {
            if (question.url_image_explication) {
              // Delete old image
              await UploadService.deleteQcmFile(question.url_image_explication);
              question.url_image_explication = null;
            }
            if (action !== 'delete') {
              // If no delete
              question.url_image_explication = await UploadService.uploadQcmFile(file);
            }
            await question.save();
            return question.url_image_explication;
          }
          break;

        /* ANSWERS */
        case 'answer_explanation':
          answer = await models.QuestionAnswers.findByPk(id);
          if (answer) {
            if (answer.url_image_explanation) {
              // Delete old image
              await UploadService.deleteQcmFile(answer.url_image_explanation);
              answer.url_image_explanation = null;
            }
            if (action !== 'delete') {
              // If no delete
              answer.url_image_explanation = await UploadService.uploadQcmFile(file);
            }
            await answer.save();
            return answer.url_image_explanation;
          }
          break;
        case 'answer':
          answer = await models.QuestionAnswers.findByPk(id);
          if (answer) {
            if (answer.url_image) {
              // Delete old image
              await UploadService.deleteQcmFile(answer.url_image);
              answer.url_image = null;
            }
            if (action !== 'delete') {
              // If no delete
              answer.url_image = await UploadService.uploadQcmFile(file);
            }
            await answer.save();
            return answer.url_image;
          }
          break;
      }

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  async changeOrderImportedQcm({ qcmId, questionId, targetQuestionId }, userId) {
    if (qcmId) {
      const getExternalQuestions = async () => {
        return models.QuestionsQcm.findAll({
          where: {
            qcmId: qcmId,
          },
          order: [['order', 'ASC']],
        });
      };
      let externalQuestions = await getExternalQuestions();

      const targetQuestion = externalQuestions.find(q => q.questionId === parseInt(targetQuestionId));
      let questionToMove = externalQuestions.find(q => q.questionId === parseInt(questionId));
      if (!questionToMove) {
        throw new GraphQLError('Question to move not found');
      }
      // If a question has same order than another, increment that order in a way that questions have different order.
      // Use a for loop and keep same array order.
      let lastOrder = externalQuestions?.[0]?.order;
      let hasChanged = false;


      for (let i = 1; i < externalQuestions.length; i++) {
        const question = externalQuestions?.[i];
        if (question?.order === lastOrder) {
          console.log('question has same order than another : auto-fixing !');
          question.order++;
          await question.save();
          hasChanged = true;
        }
        lastOrder = question.order;
      }

      // Now questions are clean.
      if (hasChanged) {
        // Optimization: if we have changed order, we need to get questions again.
        externalQuestions = await getExternalQuestions();
        questionToMove = externalQuestions.find(q => q.questionId === parseInt(questionId));
      }
      const order = targetQuestion?.order;
      // If old position === new position, do nothing
      if (questionToMove.order === order) {
        console.log('old position === new position, do nothing');
        return true;
      } else {
        const questionsBefore = externalQuestions.filter(q => (q.order <= parseInt(order) && q.questionId !== parseInt(questionId)));
        const questionsAfter = externalQuestions.filter(q => q.order >= parseInt(order) && q.questionId !== parseInt(questionId));
        if (questionToMove.order < order) {
          // Drag records backwards
          for (const question of questionsBefore) {
            question.order--;
            await question.save();
          }
        } else if (questionToMove.order > order) {
          // Drag records forwards
          for (const question of questionsAfter) {
            question.order++;
            await question.save();
          }
        }
      }
      questionToMove.order = order; // ça semble poser problème
      await questionToMove.save();
      return true;
    }
  },

  changeQuestionOrder: async ({ id, order, qcmId }, userId, ip) => {
    try {
      let questions = await QCMService.getQuestionsForQcm(qcmId);
      const questionToMove = questions.find(q => q.id_question === parseInt(id));
      if (!questionToMove) {
        throw new GraphQLError('Question to move not found');
      }
      // If a question has same order than another, increment that order in a way that questions have different order.
      // Use a for loop and keep same array order.
      let lastOrder = questions?.[0]?.order;
      let hasChanged = false;
      for (let i = 1; i < questions.length; i++) {
        const question = questions[i];
        if (question.order === lastOrder) {
          question.order++;
          await question.save();
          hasChanged = true;
        }
        lastOrder = question.order;
      }
      // Now questions are clean.
      if (hasChanged) {
        // Optimization: if we have changed order, we need to get questions again.
        questions = await QCMService.getQuestionsForQcm(qcmId);
      }
      // If old position == new position, do nothing
      if (questionToMove.order === order) {
        return true;
      } else {
        const questionsBefore = questions.filter(q => (q.order <= parseInt(order) && q.id_question !== parseInt(id)));
        const questionsAfter = questions.filter(q => q.order >= parseInt(order) && q.id_question !== parseInt(id));
        if (questionToMove.order < order) {
          // Drag records backwards
          for (const question of questionsBefore) {
            question.order--;
            await question.save();
          }
        } else if (questionToMove.order > order) {
          // Drag records forwards
          for (const question of questionsAfter) {
            question.order++;
            await question.save();
          }
        }
      }
      questionToMove.order = order;
      await questionToMove.save();
      return true;
    } catch (e) {
      console.error(e);
    }
  },


  subProcessDetermineScaleQuestionType: async ({ question }) => {
    // Fonction qui mappe une question (champ : {Type, isAnswerFreeText, isCheckbox} ) et qui détermine son scaleQuestionType
    try {

      const scaleQuestionType = McqScaleService.determineScaleQuestionTypeFromQuestion({ question });
      return scaleQuestionType;

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  subProcessInitQuestionFromScaleQuestionType: async ({ id, question, scaleQuestionType }, ctx) => {
    // Parti pris de cette fonction d'utiliser les ScaleQuestionType qui sont généralement plus polyvalent que les questionType.
    // oldQuestion est la structure sequelize de la question. Contient notament l'id
    // question est la structure (question-like) qui contient les key/values de oldQuestion qui seront updated
    try {

      const models = ctx?.models;
      const userId = ctx?.me?.id;

      // Check de si le questionType est bon
      if (!Object.values(McqScaleQuestionType).includes(scaleQuestionType)) {
        throw new Error(`In subprocess init Q from ScaleQType -> questionType (${scaleQuestionType}) not in valid questionType`);
      }

      switch (scaleQuestionType) {
        case McqScaleQuestionType.Schema:
          await models.QuestionAnswers.destroy({ where: { questionId: id } });
          question.question = defaultSchemaText;
          break;

        case McqScaleQuestionType.SchemaFillInLegends:
          await models.QuestionAnswers.destroy({ where: { questionId: id } });
          question.question = defaultSchemaFillInLegendsText;
          break;

        case McqScaleQuestionType.FillInTheBlanks:
          await models.QuestionAnswers.destroy({ where: { questionId: id } });
          question.question = defaultFillInTheBlankText;
          break;

        case McqScaleQuestionType.MultipleChoice:
          // Ajouter une logique si nécessaire
          break;

        case McqScaleQuestionType.UniqueChoice:
          // Ajouter une logique si nécessaire
          break;

        case McqScaleQuestionType.FLASHCARD:

          // Suppression des question answers.
          await models.QuestionAnswers.destroy({ where: { questionId: id } });

          // Ajout des FE d'énoncé / correction
          const enonceElement = {
            questionId: id,
            type: ELEMENTS_TYPE.RICH_TEXT,
            text: '<p style=\"text-align: center; font-size: 54px;\"><strong>Recto de votre flashcard</strong></p>',
          };
          await FormationService.createFormationElement(enonceElement, userId, ctx);

          const correctionElement = {
            footerQuestionId: id,
            type: ELEMENTS_TYPE.RICH_TEXT,
            text: '<p style=\"text-align: center; font-size: 54px;\"><strong>Verso de votre flashcard</strong></p>',
          };
          await FormationService.createFormationElement(correctionElement, userId, ctx);
          break;

        case McqScaleQuestionType.ReorderElements:
          // Remove any existing answers for reorder elements
          await models.QuestionAnswers.destroy({ where: { questionId: id } });
          // Set default question text for reorder elements
          question.question = 'Remettez les éléments suivants dans le bon ordre :';
          break;

        case McqScaleQuestionType.Alphanumerical:
          await models.QuestionAnswers.destroy({ where: { questionId: id } });
          await models.QuestionAnswers.create({ questionId: id });
          break;

        case McqScaleQuestionType.FreeText:
          question.isCheckbox = false;
          question.isAnswerUniqueChoiceInList = false;
          question.isAnswerMultipleChoiceInList = false;

          await models.QuestionAnswers.destroy({ where: { questionId: id } });
          await models.QuestionAnswers.create({ questionId: id });
          break;

        default:
          console.warn(`subprocess InitQuestion from scaleQTypes : scaleQType non reconnu (${scaleQuestionType}), ignore`);
          break;
      }
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  processExerciseRightAfterCreation: async ({ oldQuestion, id, question }, ctx) => {
    // Fonction qui modifie l'exercice juste après sa création
    try {

      // Determination du question Type
      const scaleQuestionType = await QCMService.subProcessDetermineScaleQuestionType({ question });


      // Actions de prétraitement des questions
      await QCMService.subProcessInitQuestionFromScaleQuestionType({ id, question, scaleQuestionType }, ctx);


      ///// Set RightScale
      // Si question est dans un QCM, on va check les scales associées aux UE du QCM. Sinon on prend default de la questionType
      const qcm = await QCMService.getFirstQcmIdHavingQuestion(id);

      const mcqScale = await McqScaleService.getMcqScaleForQuestionTypeAndUe({
        questionType: scaleQuestionType,
        ueIds: qcm?.UEId ? [qcm.UEId] : [],
      });
      question.mcqScaleId = mcqScale?.id;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },


  updateQuestionTitleForFlashcard: async ({ questionId, isFlashcard }) => {
    // Fonction qui automatiquement set le title pour une flashcard depuis le premier text élément
    // Si isFlashcard === true, alors on bypass la vérification pour flashcard
    try {

      // Block de test
      if (!questionId) {
        throw new Error('In updateQuestionTitleForFlashcard, no questionId, exit');
      }
      const q = await models.Question.findByPk(questionId);
      if (!q) {
        throw new Error('In updateQuestionTitleForFlashcard, no question found from questionId, exit');
      }
      if (q?.type !== McqScaleQuestionType.FLASHCARD && !isFlashcard) {
        throw new Error('In updateQuestionTitleForFlashcard, questionType is not flashcard, exit');
      }

      // Récupération des éléments, puis du text du premier FE text
      const results = await models.FormationElement.findAll({
        where: {
          type: ELEMENTS_TYPE.RICH_TEXT,
          questionId: q.id_question,
        },
        order: [['order', 'ASC']],
        //limit: 1,
        attributes: ['text'],
      });
      const questionNewText = results?.[0]?.text || '';


      // Parse et cleanup du texte du FE
      let parsedString = questionNewText;

      function removeHtmlTags(htmlString, tagName) {
        // Fonction qui remove des balises html
        const regex = new RegExp(`<${tagName}[^>]*>(.*?)</${tagName}>`, 'gis');
        return htmlString.replace(regex, '$1');
      }

      // remove des balises de formatage par default
      parsedString = removeHtmlTags(parsedString, 'p');
      parsedString = removeHtmlTags(parsedString, 'strong');

      // set de la string
      q.question = parsedString;

      await q.save();


    } catch (e) {
      console.error(`dans updateQuestionTitleForFlashcar => e : ${e} `);
      throw new GraphQLError(e);
    }
  },


  /* Called when question is updated by user, AND after initial question type selection */
  updateQuestion: async ({ id, question }, ctx) => {
    try {

      const userId = ctx?.me?.id;
      const ip = ctx?.ip;

      // Récup de l'ancienne question
      const oldQuestion = await models.Question.findByPk(id);
      if (!oldQuestion) {
        throw new GraphQLError('Question not found');
      }

      // BOOKMARK DONE
      // BLOCK D'ACTION D'UPDATE DE LA QUESTION SI ELLE VIENT D'ETRE CRÉE
      if (oldQuestion.isCheckbox === null) {
        await QCMService.processExerciseRightAfterCreation({ oldQuestion, id: oldQuestion?.id_question, question }, ctx);//question:{...oldQuestion,type:question?.type}})
      }

      ////////////////////// BLOCK D'ACTION D'UPDATE DE LA QUESTION À CHAQUE MODIF


      //////// Block de vérification de sync de question et scale :
      // - Dans l'update, seuls les champs modifiés sont envoyés, donc on a de la rétro compatibilité avec les anciens exercices tant que la scale est pas modifiée
      // - On va faire un merge de l'ancienne question et de la nouvelle, afin de déterminer le nouveau 'scaleType' de l'exercice.
      // - Une fois qu'on a le scaleType du nouvel exercice, on va valider que le nouveau scale est compatible avec ce scaleType
      const tempFullQuestion = { ...oldQuestion.get(), ...question };
      const scaleQuestionType = await QCMService.subProcessDetermineScaleQuestionType({ question: tempFullQuestion });

      // Verifie que la nouvelle question a un bon type de qcm associé => seulement si on update
      if (scaleQuestionType && question?.mcqScaleId) {

        const validMcqScales = await McqScaleService.getQuestionTypeMcqScales({
          args: { questionType: scaleQuestionType },
          ctx,
        });
        const validMcqScalesIds = validMcqScales?.map((node) => node.id);

        if (!validMcqScalesIds.includes(parseInt(question?.mcqScaleId))) {
          throw new Error(`Error during questionUpdate. ScaleQuestionType (${scaleQuestionType}) does not match e`);
        }
      }

      // Si flashcard, modif du titre de la question au premier Formation Element de texte de la question
      if (scaleQuestionType === QuestionAnswerType.FLASHCARD) {
        await QCMService.updateQuestionTitleForFlashcard({ questionId: id || question.id, isFlashcard: true });
      }

      // upload des images de question
      if (question.imageQuestion) {
        question.url_image_q = await UploadService.uploadQcmFile(question.imageQuestion);
      }

      // Upload des images de correction
      if (question.imageExplication) {
        question.url_image_explication = await UploadService.uploadQcmFile(question.imageExplication);
      }
      question = await QCMService.updateQuestionCategoryFromCours(question);

      // Initialize settings for REORDER_ELEMENTS questions if not already present
      if (question.type === 'REORDER_ELEMENTS') {
        const existingQuestion = await models.Question.findByPk(id);
        const existingSettings = existingQuestion?.settings || {};

        // Only initialize correctOrder if it doesn't exist
        if (!existingSettings.correctOrder) {
          question.settings = {
            ...existingSettings,
            ...question.settings,
            correctOrder: []
          };
        }
      }

      // Update de la question
      let updated = await models.Question.update(question, { where: { id_question: id } });

      // Log des modif de questions
      const newQuestion = await models.Question.findByPk(id);

      if (!_.isEqual(oldQuestion?.mcqScaleId, newQuestion?.mcq)) {
        const oldScale = await models.McqScale.findByPk(oldQuestion?.mcqScaleId);
        const newScale = await models.McqScale.findByPk(newQuestion?.mcqScaleId);

        await LogService.logAction({
          logOperation: LOG_OPERATIONS.Question.ChangeScale.action,
          logData: {
            oldMcqScaleId: oldQuestion?.mcqScaleId,
            oldMcqScaleName: oldScale?.name,
            newMcqScaleId: newQuestion?.mcqScaleId,
            newMcqScaleName: newScale?.name,
          },
          ip,
          foreignIds: { questionId: id, userId },
          models,
          userId,
        });
      }

      if (!_.isEqual(oldQuestion, newQuestion)) {
        await LogService.logAction({
          logOperation: LOG_OPERATIONS.Question.Update.action,
          logData: { question },
          ip,
          foreignIds: { questionId: id, userId },
          models, userId,
        });
      }

      return updated[0];
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  deleteQuestion: async ({ id }, userId, ip, throwErrorOnNotDeletable=true) => {
    try {
      const question = await models.Question.findByPk(id);
      if (!question.deletable) {
        console.error('Question not deletable');
        if(throwErrorOnNotDeletable) {
          throw new GraphQLError('Question non-supprimable');
        } else {
          return false; // Si on ne veut pas throw, on retourne false
        }
      }

      // Delete image files associated ?
      // await UploadService.deleteQcmFile(question.imageQuestion)
      await models.QuestionsQcm.destroy({
        where: {
          questionId: id,
        },
      });
      const answers = await models.QuestionAnswers.findAll({
        where: {
          questionId: id,
        },
      });
      // Delete posts associated with answers
      for (const answer of answers) {
        await models.StatsQuestionAnswers.destroy({ where: { answerId: answer?.id } });
        const allPosts = await models.Post.findAll({
          where: {
            answerId: answer?.id,
          }, raw: true, attributes: ['id'],
        });
        const postsIds = allPosts.map(p => p.id);
        // Delete posts logs
        await models.Log.destroy({ where: { postId: postsIds } });
        // Delete Posts
        await models.Post.destroy({ where: { answerId: answer?.id } });
      }
      await models.QcmStatsQuestion.destroy({ where: { id_question: id } });
      // Delete answers
      await models.QuestionAnswers.destroy({ where: { questionId: id } });
      // Delete question's MCQ types
      await models.QuestionTypeQcm.destroy({ where: { questionId: id } });
      // Delete question

      // Delete question's header elements if any
      await models.FormationElement.destroy({ where: { questionId: id } });
      await models.FormationElement.destroy({ where: { footerQuestionId: id } });

      /*
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Question.Delete.action,
        //logData: { question },
        ip,
        foreignIds: { questionId: id, userId },
        models, userId,
      });
      */

      await models.Log.destroy({ where: { questionId: id } });

      return await models.Question.destroy({ where: { id_question: id } });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  updateQuestionSettings: async ({ id, settings }, userId, ip) => {
    try {
      const question = await models.Question.findByPk(id);
      if (!question) {
        throw new GraphQLError('Question not found');
      }

      await models.Question.update(
        { settings: settings },
        { where: { id_question: id } }
      );

      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Question.Update.action,
        logData: { questionId: id, settings },
        ip,
        foreignIds: { questionId: id, userId },
        models,
        userId,
      });

      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
};

const CRUDAnswer = {
  /* CRUD ANSWERS */
  createAnswer: async ({ answer }, { me, req, ip }) => {
    try {
      const userId = me?.id;

      if (answer.image_explanation) {
        answer.url_image_explanation = await UploadService.uploadQcmFile(answer.image_explanation);
      }


      const created = await models.QuestionAnswers.create(answer);

      await LogService.logAction({
        logOperation: LOG_OPERATIONS.QuestionAnswer.Create.action,
        logData: { answer },
        ip, req,
        foreignIds: {
          questionId: answer?.questionId,
          userId,
        },
        models, userId,
      });

      return created;
    } catch (e) {
      console.error({ e });
      throw new GraphQLError(e.message);
    }
  },
  createMultipleAnswer: async ({ answers }, ctx) => {
    try {
      for (const answer of answers) {
        if (answer.image_explanation) {
          answer.url_image_explanation = await UploadService.uploadQcmFile(answer.image_explanation);
        }
        await models.QuestionAnswers.create(answer, ctx);
      }
      return true;
    } catch (e) {
      console.error({ e });
      throw new GraphQLError(e.message);
    }
  },
  updateAnswer: async ({ id, answer }, ctx) => {
    try {
      const { me, ip, req } = ctx;
      const userId = me?.id;
      const previousAnswer = await models.QuestionAnswers.findByPk(id);
      if (answer.image_explanation) {
        answer.url_image_explanation = await UploadService.uploadQcmFile(answer.image_explanation);
      }
      let updated = await models.QuestionAnswers.update(
        answer, {
          where: { id: id },
        },
      );

      const newAnswer = await models.QuestionAnswers.findByPk(id);

      const paj = previousAnswer?.toJSON(), naj = newAnswer?.toJSON();

      const TO_IGNORE_IN_COMPARISON = ['createdAt', 'updatedAt'];
      if (!_.isEqual(_.omit(paj, TO_IGNORE_IN_COMPARISON), _.omit(naj, TO_IGNORE_IN_COMPARISON))) {
        await LogService.logAction({
          logOperation: LOG_OPERATIONS.QuestionAnswer.Update.action,
          logData: { previousAnswer, answer },
          ip, req,
          foreignIds: {
            questionId: previousAnswer?.questionId,
            userId,
          },
          models, userId,
        });
      }

      return updated[0]; // oui
    } catch (e) {
      console.error({ e });
      throw new GraphQLError(e.message);
    }
  },
  deleteAnswer: async ({ id }, ctx) => {
    try {
      const { me, ip, req } = ctx;
      const userId = me?.id;
      // Delete image files associated
      const previousAnswer = await models.QuestionAnswers.findByPk(id);
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.QuestionAnswer.Delete.action,
        logData: { previousAnswer },
        ip,
        foreignIds: {
          questionId: previousAnswer?.questionId,
          userId,
        },
        models, userId,
      });

      if (previousAnswer) {
        // Delete stats associated
        await models.StatsQuestionAnswers.destroy({ where: { answerId: id } });
        return await models.QuestionAnswers.destroy({ where: { id: id } });
      }

      return false;
    } catch (e) {
      console.error({ e });
      throw new GraphQLError(e.message);
    }
  },
  /* END CRUD ANSWERS */
};

const SearchMCQ = {


  searchQcmsV2: async (
    {
      filter: {
        offset,
        limit,

        annee,
        titre,
        ue,
        ueId,
        annale,
        //
        sousCategorieId = null,
        coursId = null,
        typeQcms = [],
        isPublished = undefined,
        searchText,
        annees = [], // annees ids
      },
    }, me) => {
    try {
      let where = {};

      if ([ROLES.ADMIN, ROLES.TUTEUR, ROLES.SUB_ADMIN].includes(me?.role) && isPublished === null) {
        // Publiés et non publiés pour les admins et profs
      } else {
        where = {
          isPublished: true,
        };
      }

      const anneesObjects = await models.Annee.findAll({
        where: {
          id: annees,
        }, raw: true,
      });
      const anneesArray = anneesObjects.map(a => a.annee);
      where.annee = anneesArray; // année IN annees[]

      const qcmIdsByType = await QcmTypeService.getQcmIdsInType(typeQcms);

      // Tout dans l'UE et année selectionnée
      where = {
        ...where,
        ue,
        ...(titre && { titre }),
      };
      let idQcms = [];
      idQcms = qcmIdsByType;

      where = { ...where, id_qcm: idQcms };
      if (searchText && searchText !== '') {
        where = {
          ...where,
          titre: sequelize.where(sequelize.fn('LOWER', sequelize.col('titre')), 'LIKE', `%${searchText?.toLowerCase()}%`),
        };
      }

      const count = await models.Qcm.count({
        where: where,
      });

      const exerciseSeries = await models.Qcm.findAll({
          where: where,
          order: [
            ['date_modif', 'DESC'],
          ],
          offset,
          limit,
        },
      );

      return {
        count,
        exerciseSeries,
      };
    } catch (e) {
      console.error(e);
    }
  },

  /**
   * Search MCQs with filters
   *
   * @param {Object} filter
   * @param {integer} annee - the year
   * @param {string} titre
   * @param {integer} ueId -  the ue ID
   * @param {array} typeQcms - Array of IDs of MCQ types (type-qcms)
   * @param me
   * @return {array} Array of models.Qcm
   */
  searchQcms: async (
    {
      filter: {
        annee,
        titre,
        ue,
        ueId,
        annale,
        //
        sousCategorieId = null,
        coursId = null,
        typeQcms = [],
        isPublished = undefined,
      },
    },
    me) => {
    try {
      // default where, champs minimaux (année, UE)
      // TYPE
      const qcmIdsByType = await QcmTypeService.getQcmIdsInType(typeQcms);

      let where = {};
      if ([ROLES.ADMIN, ROLES.TUTEUR, ROLES.SUB_ADMIN].includes(me?.role) && isPublished === null) {
        // Publiés et non publiés pour les admins et profs
      } else {
        where = {
          isPublished: true,
        };
      }

      where = {
        ...where,
        ...(annee && { annee }),
        deleted: 0, // QCMs non supprimés,
        // optionals
        ...(ue && { ue }), //legacy ue to remove
        ...(ueId && { ueId }), // new ue
      };

      if (coursId == '-1') {
        coursId = null;
      }
      if (sousCategorieId == '-1') {
        sousCategorieId = null;
      }
      let idQcms = [];
      const veutToutDansUE = (sousCategorieId === null && coursId === null);
      const veutDansCategorieEtToutLesCours = (sousCategorieId && !coursId);
      const veutDansCategorieEtCoursSpecifique = (sousCategorieId && coursId);
      if (veutToutDansUE) {
        // Tout dans l'UE et année selectionnée
        where = {
          ...where,
          ...(titre && { titre }),
        };
        idQcms = qcmIdsByType;
      } else if (veutDansCategorieEtToutLesCours) {
        // Tout dans l'UE et dans la catégorie
        let coursInCategory = await models.Cours.findAll({
          where: {
            uecategoryId: sousCategorieId,
            deleted: false,
          },
          attributes: ['id', 'name', 'text'],
        });
        const questionIds = await QuestionsService.getQuestionsIdsFromCoursIds(coursInCategory.map(cours => cours.id));
        // Recupérer les qcms liés aux questions
        const idsMcqLinkedToCourse = await QuestionsService.getQcmIdsForQuestionsIds(questionIds);
        idQcms = idsMcqLinkedToCourse?.filter(id => qcmIdsByType?.includes(id));
        // récup qcms liés aux cours dans la catégorie
      } else if (veutDansCategorieEtCoursSpecifique) {
        // Tout dans le cours sélectionné
        let coursInCategory = await models.Cours.findAll({
          where: {
            uecategoryId: sousCategorieId,
            id: coursId,
          },
          attributes: ['id', 'name', 'text'],
        });
        const questionIds = await QuestionsService.getQuestionsIdsFromCoursIds(coursInCategory.map(c => c.id));
        const idsMcqLinkedToCourse = await QuestionsService.getQcmIdsForQuestionsIds(questionIds);
        idQcms = idsMcqLinkedToCourse?.filter(id => qcmIdsByType?.includes(id));
      }

      where = { ...where, id_qcm: idQcms };

      let result = await models.Qcm.findAll({
          where: where,
          order: [
            ['date_modif', 'DESC'],
          ],
        },
      );

      return result;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },


  adminSearchQcms: async (
    {
      filter: {
        annees,
        titre,
        ueIds,
        userIds,
        typeQcms,
        dateCreationStart,
        dateCreationEnd,
        dateLastModifStart,
        dateLastModifEnd,
        deleted,
        isPublished,
      },
    }, userId) => {


    try {

      // Récupération des années depuis les annéesIds
      let anneesArray = [];
      if (annees && annees.length > 0) {
        anneesArray = await models.Annee.findAll({
          where: { id: annees },
          raw: true,
        }).then(result => result.map(node => node.annee));
      }

      let where = {
        // Mandatory
        ...({ ueId: ueIds }), // si null on reject all // si [] on reject all // Si [année] on recup années
        ...({ annee: anneesArray }), // si null on reject all // si [] on reject all // Si [année] on recup années
        ...({ deleted }), // soit true soit false


        // optionals  (note : le type et le nom sont traités après)
        ...(userIds && userIds.length > 0 && { id_createur: userIds }), // Si null, on ignore l'option // Si [] on reject all // Si [ID] on récupère uniquement IDs spécifiés

        // Si un est null on ignore, sinon on filtre
        ...(dateCreationStart && dateCreationEnd && { date_creation: { [Sequelize.Op.between]: [new Date(dateCreationStart), new Date(dateCreationEnd)] } }),

        // Si un est null on ignore, sinon on filtre
        ...(dateLastModifStart && dateLastModifEnd && { date_modif: { [Sequelize.Op.between]: [new Date(dateLastModifStart), new Date(dateLastModifEnd)] } }),
      };

      if (titre && titre !== '') { // Si titre === null et on ignore, sinon on fitre sur le titre
        where = {
          ...where,
          titre: sequelize.where(sequelize.fn('LOWER', sequelize.col('titre')), 'LIKE', `%${titre?.toLowerCase()}%`),
        };
      }


      // récupération des QCM autorisés pour l'user
      const user = await models.User.findByPk(userId);
      let authorizedIds = await PermissionService.getAvailableMcqIdsForUser(user);
      where = { ...where, id_qcm: authorizedIds };


      // Filtration sur les typeQcm => Si tableau est vide alors on retourne absolument rien.
      let include = [];

      // Si on a des types de QCM, alors on ajoute le include
      if (typeQcms && typeQcms.length > 0) {
        include.push({
          model: models.TypeQcm,
          where: { id: { [Op.or]: typeQcms } },
          through: { model: models.QcmTypeQcm, attributes: [] },
        });
      }

      return models.Qcm.findAll({
        where: where,
        order: [['date_creation', 'DESC']],
        include: include,
      });

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  async adminSearchQcmsV2(
    {
      filter: {
        annees,
        titre,
        ueIds,
        userIds,
        typeQcms,
        dateCreationStart,
        dateCreationEnd,
        dateLastModifStart,
        dateLastModifEnd,
        deleted,
        isPublished,
        offset, limit,
      },
    }, userId) {

    try {
      // Récupération des années depuis les annéesIds
      let anneesArray = [];
      if (annees && annees.length > 0) {
        anneesArray = await models.Annee.findAll({
          where: { id: annees },
          raw: true,
        }).then(result => result.map(node => node.annee));
      }
      let where = {
        // Mandatory
        ...(ueIds?.length > 0 ? { ueId: ueIds } : { ueId: null }), // Si tableau non vide alors on cherche sur ueIds, sinon sur null => rien de lié
        //...({ ueId: ueIds }), // si null on reject all // si [] on reject all // Si [année] on recup années
        ...({ annee: anneesArray }), // si null on reject all // si [] on reject all // Si [année] on recup années
        ...({ deleted }), // soit true soit false
        // optionals  (note : le type et le nom sont traités après)
        ...(userIds && userIds.length > 0 && { id_createur: userIds }), // Si null, on ignore l'option // Si [] on reject all // Si [ID] on récupère uniquement IDs spécifiés
        // Si un est null on ignore, sinon on filtre
        ...(dateCreationStart && dateCreationEnd && { date_creation: { [Sequelize.Op.between]: [new Date(dateCreationStart), new Date(dateCreationEnd)] } }),
        // Si un est null on ignore, sinon on filtre
        ...(dateLastModifStart && dateLastModifEnd && { date_modif: { [Sequelize.Op.between]: [new Date(dateLastModifStart), new Date(dateLastModifEnd)] } }),
      };
      if (titre && titre !== '') { // Si titre === null et on ignore, sinon on fitre sur le titre
        where = {
          ...where,
          titre: sequelize.where(sequelize.fn('LOWER', sequelize.col('titre')), 'LIKE', `%${titre?.toLowerCase()}%`),
        };
      }
      // récupération des QCM autorisés pour l'user
      const user = await models.User.findByPk(userId);
      let authorizedIds = await PermissionService.getAvailableMcqIdsForUser(user);
      where = { ...where, id_qcm: authorizedIds };
      // Filtration sur les typeQcm => Si tableau est vide alors on retourne absolument rien.
      let include = [];

      // Si on a des types de QCM, alors on ajoute le include
      if (typeQcms && typeQcms.length > 0) {
        include.push({
          model: models.TypeQcm,
          where: { id: { [Op.or]: typeQcms } },
          through: { model: models.QcmTypeQcm, attributes: [] },
        });
      }

      return {
        count: await models.Qcm.count({
          where: where,
          include: include,
        }),
        exerciseSeries: await models.Qcm.findAll({
          where: where,
          order: [['date_creation', 'DESC']],
          include: include,
          offset,
          limit,
        }),
      };

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
};

const QCMSpecialOperations = {

  // legacy, but can be usedful for imports
  massChangesQuestionsCategories: async ({ categoryId, coursId }) => {
    try {
      let questionsConcernees = await models.Question.count({ where: { id_sous_categorie: categoryId } });
      const questions = await models.Question.findAll({
        where: {
          id_sous_categorie: categoryId,
        },
      });
      for (const q of questions) {
        await models.QuestionCours.create({
          questionId: q.id_question,
          coursId,
        });
      }
      return questionsConcernees;
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  massChangesQuestionsCours: async ({ selectedCoursId, targetCoursId }) => {
    try {
      let questionsConcernees = await models.QuestionCours.count({ where: { coursId: selectedCoursId } });

      let result = await models.QuestionCours.update({
          coursId: targetCoursId,
        }, {
          where: {
            coursId: selectedCoursId,
          },
        },
      );
      return questionsConcernees;
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  changeCoursAllQuestion: async ({ id_qcm, coursId }, userId) => {
    try {
      const questionIds = await QuestionsService.getQuestionsIdsForMcq(id_qcm);
      let allQuestions = await models.Question.findAll({ where: { id_question: questionIds } });

      for (let q of allQuestions) {
        await QuestionsService.addCoursToQuestion({ questionId: q.id_question, coursId }, userId);
        await q.save();
      }

      return true;
    } catch (e) {
      console.error(e);
    }
  },

  updateAllGradesForMcq: async function({ id }, userId) {
    try {
      await new Promise(resolve => setTimeout(resolve, 2500));
      await QCMStatsService.updateAllScoreForMcq(id);
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  fusionQcm: async function({ ids, newMcqTitle = 'QCM fusionné' }, userId) {
    try {
      let allQuestions = [];
      // Créer qcm vide, associer toutes les questoins à ce qcm, en
      let UEId, annee;
      const typeQcmIDS = [];
      for (const id of ids) {
        let mcq = await models.Qcm.findByPk(id); // get mcq
        let questions = await QCMService.getQuestionsForQcm(id);
        allQuestions.push(...questions);
        UEId = mcq.UEId;
        annee = mcq.annee;
        const typesQcms = await QcmTypeService.getTypesQcm(mcq, null, { models });
        typeQcmIDS.push(...typesQcms.map(t => t.id));
      }

      const newQCM = await QCMService.createQcm({
        qcm: {
          titre: newMcqTitle,
          description: '',
          ue: UEId,
          UEId,
          annee,
          typeQcmIDS,
        },
      }, userId);
      for (const q of allQuestions) {
        let modifiedQuestion = JSON.parse(JSON.stringify(q));
        modifiedQuestion.id_qcm = newQCM.id_qcm;
        if (modifiedQuestion.url_image_q) {
          modifiedQuestion.url_image_q = await UploadService.duplicateQcmFile(modifiedQuestion.url_image_q);
        }
        if (modifiedQuestion.url_image_explication) {
          modifiedQuestion.url_image_explication = await UploadService.duplicateQcmFile(modifiedQuestion.url_image_explication);
        }
        // get answers
        const answers = await QCMService.getQuestionAnswers(modifiedQuestion.id_question);
        delete modifiedQuestion.id_question;
        // create new question
        const newQuest = await models.Question.create(modifiedQuestion);
        // duplique question answers
        await QCMService.duplicateQuestionAnswers(answers, newQuest.id_question);

        // link les cours
        const coursLinked = await QuestionsService.getCoursLinkedForQuestion(q);
        for (const c of coursLinked) {
          await QuestionsService.addCoursToQuestion({ questionId: newQuest.id_question, coursId: c.id }, userId);
        }

        // Link question types_QCM
        const types = await q.getType_qcms();
        for (const type of types) {
          await QCMService.addTypeQcmToQuestion({ typeQcmId: type?.id, questionId: newQuest.id_question }, null);
        }

        ////////////////////////////
        // Duplicate header elements
        const headerElements = await models.FormationElement.findAll({
          where: {
            questionId: q.id_question,
          },
        });
        for (const elem of headerElements) {
          let elemJson = elem.toJSON();
          delete elemJson.id;
          if (elemJson.image) {
            elemJson.image = await UploadService.duplicateFiles(elemJson.image);
          }
          const newElem = await models.FormationElement.create(elemJson);
          newElem.questionId = newQuest.id_question;
          await newElem.save();
        }

        ////////////////////////
        // Duplicate question types
        const questionsTypes = await models.QuestionTypeQuestion.findAll({
          where: {
            questionId: q.id_question,
          },
        });
        for (const qt of questionsTypes) {
          await models.QuestionTypeQuestion.create({
            typeQuestionId: qt.typeQuestionId,
            questionId: newQuest.id_question,
          });
        }

      }
      return newQCM;
    } catch (e) {
      console.error(e);
    }
  },

  /* Duplicate whole question serie */
  duplicateQcm: async ({ id, duplicate }, userId) => {
    try {
      let qcm = await models.Qcm.findByPk(id);
      if (!qcm) {
        throw new GraphQLError('Série d\'exercice introuvable');
      }

      const oldQuestions = await QCMService.getQuestionsForQcm(id);
      let modifiedQcm = qcm.toJSON();
      delete modifiedQcm.id_qcm;

      modifiedQcm.id_lien = await uuidv4();
      modifiedQcm.date_creation = moment().toDate();
      modifiedQcm.date_modif = moment().toDate();
      modifiedQcm.titre = `${modifiedQcm.titre} - duplicat ${duplicate ? '(variante)' : '(copie)'}`;

      const newQ = await models.Qcm.create(modifiedQcm);
      const newIdQcm = newQ.id_qcm;
      // linked classes mcq
      //const oldMcqLinkedClasses = await qcm.getCours();
      //await newQ.addCours(oldMcqLinkedClasses);

      // Ajout mêmes types QCMs
      const typesQcms = await QcmTypeService.getTypesQcm(qcm, null, { models });
      for (const typeQ of typesQcms) {
        await QcmTypeService.addTypeQcmToQcm({ qcmId: newQ.id_qcm, typeQcmId: typeQ.id }, userId);
      }

      if (duplicate) {
        if (oldQuestions) {
          for (const q of oldQuestions) {
            await QCMService.duplicateQuestion({ id: q?.id_question, parentQcmId: newIdQcm }, null, newIdQcm);
          }
        }
      } else {
        // Link only
        for (const q of oldQuestions) {
          await QCMService.addQuestionToQcm({ questionId: q?.id_question, qcmId: newIdQcm }, null);
        }
      }


      return newQ;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
};


const QuestionHierarchy = {

  // QUESTION HIERARCHY
  async addQuestionParent({ questionId, parentQuestionId }, id) {
    try {
      const question = await models.Question.findByPk(questionId);
      const questionParent = await models.Question.findByPk(parentQuestionId);
      if (!question || !questionParent) {
        throw new GraphQLError('La question ou la question parente ne sont pas valides');
      }
      const questionParentsChilds = await models.QuestionsParents.findOne({
        where: {
          questionId,
          parentQuestionId,
        },
      });
      if (questionParentsChilds) {
        //throw new GraphQLError('La question a déjà été ajoutée !');
        console.log('question déjà ajoutée');
        return;
      }
      const result = await models.QuestionsParents.create({
        questionId,
        parentQuestionId,
      });
      return !!result;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async addMultipleQuestionChilds({ parentQuestionId, childsQuestionIds, isError = false }) {
    try {
      for (const questionId of childsQuestionIds) {
        const result = await models.QuestionsParents.create({
          parentQuestionId: parentQuestionId,
          questionId,
          isError,
        });
      }
    } catch (e) {
      console.error(e);
    }
  },
  async addMultipleDoubleQuestionChilds({ parentQuestionId, parentQuestionId2, childsQuestionIds, isError = false }) {
    try {
      for (const questionId of childsQuestionIds) {
        const mod = await models.QuestionsDoubleParents.create({
          questionId,
          parentQuestionId,
          parentQuestionId2,
          isError,
        });
      }
    } catch (e) {
      console.error(e);
    }
  },

  async addQuestionChildren({ questionId, childrenQuestionId }, id) {
    try {
      const question = await models.Question.findByPk(questionId);
      const childrenQuestion = await models.Question.findByPk(childrenQuestionId);
      if (!question || !childrenQuestion) {
        throw new GraphQLError('La question ou la notion parente ne sont pas valides');
      }

      const notionParentsChilds = await models.QuestionsParents.findOne({
        where: {
          parentQuestionId: questionId,
          questionId: childrenQuestionId,
        },
      });
      if (notionParentsChilds) {
        //console.log('question déjà ajoutée');
        return;
      }
      const result = await models.QuestionsParents.create({
        parentQuestionId: questionId,
        questionId: childrenQuestionId,
      });
      return !!result;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async removeQuestionParent({ questionId, parentQuestionId }, id) {
    try {
      // suppression parent bug: childrenQuestionId undefined
      const question = await models.Question.findByPk(parentQuestionId);
      const questionChild = await models.Question.findByPk(questionId);
      if (!question || !questionChild) {
        throw new GraphQLError('La question ou la question parente ne sont pas valides');
      }
      return await models.QuestionsParents.destroy({
        where: {
          questionId,
          parentQuestionId,
        },
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async removeQuestionChildren({ questionId, childrenQuestionId }, id) {
    try {
      const question = await models.Question.findByPk(questionId);
      const questionChild = await models.Question.findByPk(childrenQuestionId);
      if (!question || !questionChild) {
        throw new GraphQLError('La question ou la question parente ne sont pas valides');
      }
      return await models.QuestionsParents.destroy({
        where: {
          parentQuestionId: questionId,
          questionId: childrenQuestionId,
        },
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  getQuestionIdsCommonElements(arrayIds1, arrayIds2) {
    return arrayIds1.filter(value => arrayIds2.includes(value));
  },

  async getParentQuestionIdsForQuestionId(questionId, isError = false) {
    const np = await models.QuestionsParents.findAll({
      where: { questionId: questionId, isError },
    });
    return np.map(q => q.parentQuestionId);
  },

  async getChildrensQuestionIdsForQuestion(parentQuestionId, isError = false) {
    const np = await models.QuestionsParents.findAll({
      where: { parentQuestionId, isError },
    });
    return np.map(q => q.questionId);
  },

  async getChildrensQuestionIdsForDoubleParentQuestion(parentQuestionId, parentQuestionId2, isError = false) {
    const np = await models.QuestionsDoubleParents.findAll({
      where: { parentQuestionId, parentQuestionId2, isError },
    });
    return np.map(q => q.questionId);
  },

  ///////////////
  async getParentsForQuestion(question) {
    try {
      const np = await models.QuestionsParents.findAll({
        where: { questionId: question.id_question },
      });
      return models.Question.findAll({
        where: {
          id_question: { [Op.in]: np.map(n => n.parentQuestionId) },
        },
      });
    } catch (e) {
      console.error(e);
    }
  },
  async getChildrensForQuestion(question) {
    try {
      const np = await models.QuestionsParents.findAll({
        where: { parentQuestionId: question.id_question },
      });
      return models.Question.findAll({
        where: {
          id_question: { [Op.in]: np.map(n => n.questionId) },
        },
      });
    } catch (e) {
      console.error(e);
    }
  },
  ////////////////////////

};

export const QCMService = {
  ...CRUDQcm,
  ...CRUDQuestion,
  ...CRUDAnswer,
  ...SearchMCQ,
  ...CRUDTypeQuestion,

  ...QCMSpecialOperations,

  ...QuestionHierarchy,

  findFirstQcmIdHavingQuestion: async (questionId) => {
    //TODO cache
    const link = await models.QuestionsQcm.findOne({
      where: {
        questionId,
      },
      attributes: ['qcmId'],
    });
    return link?.qcmId;
  },
  getFirstQcmIdHavingQuestion: async (questionId) => {
    //TODO cache
    const qcmId = await QCMService.findFirstQcmIdHavingQuestion(questionId);
    return models.Qcm.findByPk(qcmId);
  },
  getYearOfFirstQcmIdHavingQuestion: async (questionId) => {
    //TODO cache
    const qcmId = await QCMService.findFirstQcmIdHavingQuestion(questionId);
    const qcm = await models.Qcm.findByPk(qcmId, { attributes: ['annee'], raw: true });
    return qcm?.annee;
  },

  /* Duplicate questions answers with notions*/
  duplicateQuestionAnswers: async (answers, questionId) => {
    try {
      for (const an of answers) {
        let modifiedAnswer = an.toJSON();
        delete modifiedAnswer.id;
        modifiedAnswer.questionId = questionId;
        let newAnswer = await models.QuestionAnswers.create(modifiedAnswer);
        // duplicate notions
        let qNotions = await an.getNotions();
        await newAnswer.addNotions(qNotions);
        await newAnswer.save();
      }
      return true;
    } catch (e) {
      console.error('error duplicateQuestionAnswers : ',e);
    }
  },
  /* Duplicate questions, answers with notions*/
  duplicateQuestion: async ({ id, parentQcmId = null }, userId, newMcqId = undefined) => {
    try {
      const question = await models.Question.findByPk(id);
      if (!question) {
        throw new GraphQLError('Impossible de trouver la question à dupliquer');
      }
      const answers = await QCMService.getQuestionAnswers(question.id_question);
      let modifiedQuestion = question.toJSON();
      delete modifiedQuestion.id_question;
      if (modifiedQuestion.url_image_q) {
        modifiedQuestion.url_image_q = await UploadService.duplicateQcmFile(modifiedQuestion.url_image_q);
      }
      if (modifiedQuestion.url_image_explication) {
        modifiedQuestion.url_image_explication = await UploadService.duplicateQcmFile(modifiedQuestion.url_image_explication);
      }

      // Create duplicated question
      const newQuest = await models.Question.create(modifiedQuestion);

      if (parentQcmId) {
        await QuestionsService.linkQuestionToSerie({
          qcmId: parentQcmId,
          questionId: newQuest.id_question,
        });
      }

      // duplicate question notions
      let questionNotions = await question.getNotions();
      if (questionNotions) {
        await newQuest.addNotions(questionNotions);
      }

      ////////////////////////////
      // Duplicate header elements
      const headerElements = await models.FormationElement.findAll({
        where: {
          questionId: id,
        },
      });
      for (const elem of headerElements) {
        let elemJson = elem.toJSON();
        delete elemJson.id;
        if (elemJson.image) {
          elemJson.image = await UploadService.duplicateFiles(elemJson.image);
        }
        const newElem = await models.FormationElement.create(elemJson);
        newElem.questionId = newQuest.id_question;
        await newElem.save();
      }

      ////////////////////////////
      // Duplicate footer elements
      const footerElements = await models.FormationElement.findAll({
        where: {
          footerQuestionId: id,
        },
      });
      for (const elem of footerElements) {
        let elemJson = elem.toJSON();
        delete elemJson.id;
        if (elemJson.image) {
          elemJson.image = await UploadService.duplicateFiles(elemJson.image);
        }
        const newElem = await models.FormationElement.create(elemJson);
        newElem.footerQuestionId = newQuest.id_question;
        await newElem.save();
      }

      ////////////////////////
      // Duplicate cours
      const questionsCours = await models.QuestionCours.findAll({
        where: {
          questionId: id,
        },
      });
      for (const qc of questionsCours) {
        await models.QuestionCours.create({
          questionId: newQuest.id_question,
          coursId: qc?.coursId,
        });
      }
      ////////////////////////
      // Duplicate question types
      const questionsTypes = await models.QuestionTypeQuestion.findAll({
        where: {
          questionId: id,
        },
      });
      for (const qt of questionsTypes) {
        await models.QuestionTypeQuestion.create({
          typeQuestionId: qt.typeQuestionId,
          questionId: newQuest.id_question,
        });
      }
      ////////////////////////
      // Duplicate question types qcm
      const questionsTypesQcm = await models.QuestionTypeQcm.findAll({
        where: {
          questionId: id,
        },
      });
      for (const qtc of questionsTypesQcm) {
        await models.QuestionTypeQcm.create({
          typeQcmId: qtc.typeQcmId,
          questionId: newQuest.id_question,
        });
      }

      // duplique question answers
      await QCMService.duplicateQuestionAnswers(answers, newQuest.id_question);
      return newQuest;
    } catch (e) {
      console.error(`duplicateQuestion id=${id} error :${e}`);
      throw new GraphQLError(e.message);
    }
  },

  getQuestionAnswers: async (idQuestion) => {
    try {
      if (!idQuestion) {
        throw new Error('idQuestion should be defined');
      }
      return await models.QuestionAnswers.findAll({
        where: {
          questionId: idQuestion,
        },
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  getQcmIdsInCategoryId: async (ueCategoryId) => {
    // Tout dans l'UE et dans la catégorie
    let coursInCategory = await models.Cours.findAll({
      where: { uecategoryId: ueCategoryId, deleted: false },
      attributes: ['id'],
    });
    let coursIds = [...new Set(coursInCategory?.map(c => c.id))];
    const questionIds = await QuestionsService.getQuestionsIdsFromCoursIds(coursIds);

    let questions = await models.Question.findAll({
      where: {
        id_question: questionIds,
      },
      attributes: ['id_qcm'],
      include: [
        {
          model: models.Qcm,
          where: {
            deleted: false,
            isPublished: true,
          },
          required: true,
          //order: [['annee', 'DESC']],
        },
      ],
    });
    let questionsIds = questions?.map(q => q.id_question);
    const importedQuestionQcm = await models.QuestionsQcm.findAll({
      where: {
        questionId: questionsIds,
      },
      attributes: ['qcmId'],
    });
    const qcmIdsFromImportedQuestions = importedQuestionQcm.map(i => i.qcmId);
    const qcmIds = questions?.map(q => q.id_qcm);
    let finalQcmIds = [...qcmIds, ...qcmIdsFromImportedQuestions];
    return [...new Set(finalQcmIds)];
  },

  /**
   * Returns Grade for question serie
   *
   * @param qcmId
   * @param sessionId
   * @param examSessionId
   * @param reponses
   * @param seconds
   * @param userId
   * @returns {Promise<{note: number}>}
   */
  async corrigerQcmUtilisateur({ qcmId, sessionId = null, examSessionId = null, reponses, seconds = null }, userId) {
    // Récupérer QCM, calcul note, insérer stats questions et stats note, retourner resultat
    try {
      let qcm = await models.Qcm.findByPk(qcmId);
      let questions = await QCMService.getQuestionsForQcm(qcmId);
      let session;
      if (sessionId) {
        // Protection : ne pas autoriser à corriger un qcm déjà corrigé (sur tablette page précédente sans faire exprès est vite arrivé)
        const existingStat = await models.QcmStats.findOne({
          where: {
            id_utilisateur: userId,
            qcmSessionId: sessionId,
          },
        });
        if (existingStat) {
          return { note: existingStat.note };
        }
        // Sinon, on continue
        session = await models.QcmSession.findByPk(sessionId);
        if (session) {
          session.isFinished = true;
          session.questionsIdsDone = questions?.map(q => q?.id_question);
          await session.save();
          // Exam If it's the last from the session's exam question serie, save exam's user result.
          await ExamService.updateExamUserSessionAfterQuizzSerieCompletion(session);
        }
      }

      // Remove saved state for restauration if it exists
      await QCMService.deleteQcmState({ qcmId, userId, sessionId });

      if (!qcm || !questions) {
        let errorMsg = 'qcm ou questions introuvables';
        console.error(errorMsg);
        throw errorMsg;
      }
      if (reponses.length !== questions.length) {
        let errorMsg = 'nombre de questions correspond pas';
        console.error(errorMsg);
        throw errorMsg;
      }
      if (isDev) {
        console.log({ reponses });
      }
      const {
        reponsesUtilisateur,
        note,
        reponsesJustes,
        totalPonderatedGrade,
      } = await QCMService.calculNoteQcm(questions, reponses);

      // check si a déjà fait ou pas (note finale)
      let statExistante = await models.QcmStats.findOne({
        where: {
          id_qcm: qcmId,
          id_utilisateur: userId,
          isFirstTime: true,
        },
      });

      let isFirstTime = true;
      if (statExistante) {
        // Déjà fait au moins une fois
        isFirstTime = false;
      }

      // Register new mcq final grade result
      const newStat = await models.QcmStats.create({
        id_qcm: qcmId,
        id_utilisateur: userId,
        note: note, // note finale de la série
        ponderatedGrade: totalPonderatedGrade, // note pondérée, utilisée par Aptoria
        date: new Date(),
        qcmSessionId: sessionId, // if null, it's a "classic" serie
        isFirstTime,  // Premier essai (true) ou pas
        seconds, // seconds spent on this serie
      });

      let statId = newStat.id;

      await QCMService.insertUserAnswers(reponsesUtilisateur, userId, sessionId, statId);
      await UserStatsService.incrementStat(userId, UserStatsService.OPERATIONS.incrementMCQDone);

      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Qcm.Finish.action,
        logData: { seconds }, // Log secondes passées sur la série
        foreignIds: { qcmId, userId },
        models, userId,
      });

      return { note, id: statId };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /**
   *  Inserts user answers
   **/
  async insertUserAnswers(reponsesUtilisateur, userId, qcmSessionId = null, statId = null) {
    /* Create user answers and statsQuestionAnswers */
    for (const rep of reponsesUtilisateur) {
      let jsonAnswers = null; // Schema answer data
      //console.log({jsonanswers: rep.jsonAnswers});
      const jsonAnswerContainAutoEvaluation = !!rep?.jsonAnswers && 'autoEvaluation' in rep.jsonAnswers;
      const hasSchemaJsonAnswers = rep.jsonAnswers && Array.isArray(rep.jsonAnswers);
      const hasFillInTheBlanksJsonAnswers = rep.jsonAnswers && typeof rep.jsonAnswers === 'object' && !(rep?.json) && !jsonAnswerContainAutoEvaluation;

      if (hasSchemaJsonAnswers || hasFillInTheBlanksJsonAnswers || jsonAnswerContainAutoEvaluation) {
        jsonAnswers = rep.jsonAnswers;
      }
      // Insert points obtained and data for each question (statistiques_questions)
      await models.QcmStatsQuestion.create({
        id_question: rep.id_question,
        id_utilisateur: userId,
        qcmSessionId,
        pointsObtained: rep.pointsObtained,
        ponderatedPointsObtained: rep.ponderatedPointsObtained,
        qcmStatsQuestion: rep.qcmStatsQuestion, // new way to insert in statsQuestionsAnswers fast
        statId,
        jsonAnswers,
        certainty: rep?.certainty,
      }, {
        // Insert each answer result (stats_questions_answers)
        include: [{
          model: models.StatsQuestionAnswers,
          as: 'qcmStatsQuestion', // contains user choices, empty array for schema answer
        }],
      });
    }
    return true;
  },

  /*
   * selectedAnswersIds: array of user answers IDs
   * questionAnswer: actual question answer model instance (from correction)
   */
  isAnswerTrue: async (selectedAnswersIds = [], questionAnswer) => {
    if (questionAnswer?.isHorsConcours) {
      // Si c'est HorsConcours c'est good
      return true;
    }
    const selectedAnswersIdsCollection = selectedAnswersIds?.map(id => id?.toString());
    const answerIsTicked = !!(selectedAnswersIdsCollection?.includes(questionAnswer?.id?.toString()));
    const answerIsTrue = questionAnswer.isTrue;
    // Si c'est "juste et coché" ou "faux et pas coché", c'est good
    if ((answerIsTrue && answerIsTicked) || (!questionAnswer.isTrue && !answerIsTicked)) {
      return true;
    }
    return false;
  },

  /**
   * Calculate grade for a question, with user answers
   * TODO découper en plusieurs fonctions !
   * @param {*} question original question
   * @param {*} reponses user answers from frontend
   * @param {boolean} calculPondere
   */
  calculNoteQuestion: async (question, reponses, calculPondere = false) => {
    try {
      let reponsesJustes = 0, note = 0, erreurs = 0, totalPonderatedGrade = 0, pointsPerQuestion = 0;
      // Question
      let actualQuestion = question;
      const isSchemaExercise = question.type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK;
      const isSchemaFillInLegendExercise = question.type === QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS;
      const isFillInTheBlankExercise = question.type === QuestionAnswerType.FILL_IN_THE_BLANKS;
      const isFlashcard = question.type === QuestionAnswerType.FLASHCARD;
      const isReorderExercise = question.type === QuestionAnswerType.REORDER_ELEMENTS;
      const isTrueFalseUndefined = question.type === QuestionAnswerType.TRUE_OR_FALSE_OR_UNDEFINED; // Choix explicite entre rien, vrai, ou faux

      // Correction answers
      let questionAnswers = actualQuestion.question_answers || actualQuestion.answers;
      if (isDev) {
        console.log({ questionAnswers });
      }

      // User answers
      let actualAnswer = reponses;
      actualAnswer.qcmStatsQuestion = []; // Array to store stats for each answer

      // TRUE answers
      const selectedAnswersIds = actualAnswer.answers || actualAnswer.answersData || []; // answersData n'est plus utilisé
      const selectedFalseAnswersIds = actualAnswer.answers_false || [];

      if (String(actualQuestion.id_question) !== String(actualAnswer.id_question)) { // should never be false
        console.error('actualQuestion.id_question !== actualAnswer.id_question');
        throw new GraphQLError('actualQuestion.id_question !== actualAnswer.id_question');
      }

      // User has checked nothing
      let checkedNothing = selectedAnswersIds?.length === 0 && selectedFalseAnswersIds?.length === 0 && !isSchemaExercise && !isSchemaFillInLegendExercise;
      // cas spécial si ils ont changé de isTrueFalseUndefined vers normal qcm, on peut avoir enregistrement de explicitement faux?
      if (!isTrueFalseUndefined && selectedAnswersIds?.length === 0 && !isSchemaExercise && !isSchemaFillInLegendExercise) {
        // là on ignore les selectedFalseAnswersIds volontairement car pas possible de le faire sans isTrueFalseUndefined à true
        checkedNothing = true;
      }

      const isAlphanumericalQuestion = [QuestionAnswerType.ALPHANUMERICAL, QuestionAnswerType.NUMERICAL].includes(actualQuestion?.type);
      let hasInsertedAlphanumQuestion = false;

      let allPointsOrNothingTickedResult = 'none';

      let customErrors = [];
      let customNotationResultType = null;

      // Barême
      // Update de getMcqScale => check la question, sinon essaye d'automatiquement trouver une
      const mcqScale = await QCMService.getMcqScale({ question: actualQuestion });

      const notation = mcqScale.rules?.[McqScaleRulesProperties.notation] || 'default';

      let numberOfSchemaLegendsAsked = 0;
      let numberOfFillTheBlanks = 0;

      // Schema only/////////////////////////////
      const jsonAnswers = reponses?.jsonAnswers; // array
      if (isSchemaExercise) {
        const legendsIdsToCheck = actualQuestion?.settings?.checkedLegends;
        numberOfSchemaLegendsAsked = legendsIdsToCheck?.length || 0;
        const nbLegendsChecked = jsonAnswers?.length || 0; // ne devrait jamais être egal à 0 à part si admin n'a rien coché comme legende dans l'admin
        if (isDev) {
          console.log({ nbLegendsChecked, jsonAnswers });
        }
        for (const legendId of legendsIdsToCheck) {
          // find corresponding jsonAnswer
          const legendAnswer = jsonAnswers?.find(ja => ja?.id === legendId);
          if (Array.isArray(jsonAnswers) && legendAnswer) {
            // Utilisateur a répondu pour cette légende
            if (legendAnswer?.isInLegend) {
              reponsesJustes++;
            } else {
              erreurs++;
            }
          } else {
            // Utilisateur a pas répondu alors qu'il aurait du
            erreurs++;
          }
        }
        // pas de stats questions answers pour les schémas
      } else if (isSchemaFillInLegendExercise) {
        const correspondingSchema = await models.SchemaLibrary.findByPk(actualQuestion?.schemaLibraryId);
        const legends = correspondingSchema?.legends;
        const legendsIdsAsked = actualQuestion?.settings?.checkedLegends; // IDs de légendes demandées
        numberOfSchemaLegendsAsked = legendsIdsAsked?.length || 0;
        for (const legendId of legendsIdsAsked) {
          // find corresponding jsonAnswer
          const legendAnswer = jsonAnswers?.find(ja => ja?.id === legendId);
          if (Array.isArray(jsonAnswers) && legendAnswer) {
            // on a une réponse pour cette légende
            const correspondingLegendObj = legends?.find(l => l.id === legendId);
            const legendNameString = correspondingLegendObj?.name;
            const userAnswerString = legendAnswer?.text;
            const synonymsArray = correspondingLegendObj?.synonyms || [];
            // Vérifiez si la réponse est exactement le nom de la légende
            let isCorrect = FillInTheBlanksCorrection.isAcceptedAnswer(
              legendNameString,
              userAnswerString,
              false,
              true,
            );
            // Vérifiez les synonymes (alternatives) si la réponse principale n'est pas correcte
            //console.log({synonymsArray})
            if (!isCorrect && Array.isArray(synonymsArray) && synonymsArray?.length > 0) {
              for (const alternative of synonymsArray) {
                if (FillInTheBlanksCorrection.isAcceptedAnswer(alternative, userAnswerString, false, true)) {
                  isCorrect = true;
                  break;
                }
              }
            }
            if (isCorrect) {
              legendAnswer.isCorrect = true;
              reponsesJustes++;
            } else {
              legendAnswer.isCorrect = false;
              erreurs++;
            }
          } else {
            // Utilisateur a pas répondu alors qu'il aurait du
            erreurs++;
          }
        }
      } else if (isFillInTheBlankExercise) {
        // TEXTE A TROU
        const parsedQuestionText = FillInTheBlanksCorrection.parseQuestionText(actualQuestion?.settings?.text);

        for (const part of parsedQuestionText) {
          if (part.type === 'input') {
            numberOfFillTheBlanks++;
            // Try to find corresponding answer
            if (!Array.isArray(jsonAnswers)) {
              return {
                note: 0,
                reponsesUtilisateur: reponses, // Réponses enrichies
                reponsesJustes: 0,
                erreurs: 0,
                pointsPerQuestion: mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion],
                totalPonderatedGrade: 0,
              };
            }
            const correspondingUserAnswer = jsonAnswers?.find((a) => a.id === part.id) || { value: '' };

            isDev && console.log({ correspondingUserAnswer });

            // Vérifiez si la réponse est correcte
            let isCorrect = FillInTheBlanksCorrection.isAcceptedAnswer(
              part.correctAnswer, //ok
              correspondingUserAnswer.value,
              actualQuestion?.settings?.caseSensitive,
              actualQuestion?.settings?.acceptTypos,
              actualQuestion?.settings?.ignoreAccentsAndSpecialChars,
            );
            // Vérifiez les alternatives si la réponse principale n'est pas correcte
            if (!isCorrect && part.alternatives.length > 0) {
              for (const alternative of part.alternatives) {
                if (FillInTheBlanksCorrection.isAcceptedAnswer(alternative, correspondingUserAnswer.value, actualQuestion?.settings?.caseSensitive, actualQuestion?.settings?.acceptTypos)) {
                  isCorrect = true;
                  break;
                }
              }
            }
            if (isCorrect) {
              isDev && console.log('isCorrect', { part, correspondingUserAnswer });
              correspondingUserAnswer.isCorrect = true;
              reponsesJustes++;
            } else {
              correspondingUserAnswer.isCorrect = false;
              isDev && console.log('isInCorrect', { part, correspondingUserAnswer });
              erreurs++;
            }
          }
        }
      } else if (isFlashcard) {

        const flashcardRule = mcqScale?.rules?.flashcardBareme;

        // Récupération de l'autoévaluation qui normalement correspond à un key du flashcardRule
        const autoEvaluationKey = reponses?.jsonAnswers?.autoEvaluation ?? null;

        // Récupération de la valeur associé à la key , si la key n'est pas valide, alors on retourne une note de 0
        const autoEvaluationValue = autoEvaluationKey && flashcardRule.hasOwnProperty(autoEvaluationKey) ? flashcardRule?.[autoEvaluationKey] : { note: 0 };

        const note = autoEvaluationValue?.note || 0; // Soit la note remplie, soit 0 //factor=autoEvaluationValue?.note || 0
        const maxPoints = mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion];

        if (note === maxPoints) {
          reponsesJustes++;
        } else {
          erreurs++;
        }

        // Mettez à jour les points obtenus
        actualAnswer.pointsObtained = note;

        // Si nécessaire, mettez à jour la note pondérée
        totalPonderatedGrade = note;

        return {
          reponsesUtilisateur: reponses, // Réponses enrichies
          note, // Note de la question
          reponsesJustes,
          erreurs,
          pointsPerQuestion: maxPoints,
          totalPonderatedGrade,
        };

      } else if (isReorderExercise) {
        // REORDER ELEMENTS - Use integrated scoring system
        const scoringResult = calculateReorderScoreIntegrated(
          actualQuestion,
          actualAnswer,
          mcqScale,
          jsonAnswers
        );

        reponsesJustes = scoringResult.reponsesJustes;
        erreurs = scoringResult.erreurs;
        note = scoringResult.note;
        pointsPerQuestion = scoringResult.pointsPerQuestion;
        totalPonderatedGrade = scoringResult.totalPonderatedGrade;

        // Important: qcmStatsQuestion should be empty for REORDER_ELEMENTS
        // All detailed data is stored in jsonAnswers field
        actualAnswer.qcmStatsQuestion = scoringResult.qcmStatsQuestion; // Should be empty array

        // Return early - scoring is already complete, no need for additional scale processing
        return {
          reponsesUtilisateur: reponses, // Réponses enrichies
          note, // Note de la question
          reponsesJustes,
          erreurs,
          pointsPerQuestion,
          totalPonderatedGrade,
        };
      }
      //////////////////////////////////////////


      /*
       * Loop answers and get number of good and bad answers
       * --------------
      */
      if (!isSchemaExercise && !isSchemaFillInLegendExercise && !isFillInTheBlankExercise && !isReorderExercise) {
        for (const questionAnswer of questionAnswers) {
          let isGoodAnswer = false;

          // Current answer is selected as TRUE
          const answerIsTicked = !!(selectedAnswersIds?.map(id => id.toString())?.includes(questionAnswer?.id?.toString()));
          // Current answer is selected as FALSE
          const answerIsTickedAsFalse = !!(selectedFalseAnswersIds?.map(id => id.toString())?.includes(questionAnswer?.id?.toString()));
          // Current answer is not selected (undefined)
          const answerIsUnchecked = !answerIsTicked && !answerIsTickedAsFalse;

          /* Handle special alphanumerical */
          if (isAlphanumericalQuestion) {
            // Si tout coché, tous les points, sinon 0
            const correctAnswersArray = JSON?.parse(questionAnswer?.text);
            const selectedAnswersStrings = selectedAnswersIds?.map(s => String(s));
            const containsAll = correctAnswersArray?.every(arr2Item => selectedAnswersStrings?.includes(String(arr2Item)));

            if (isDev) {
              console.log('isAlphanumericalQuestion', {
                actualAnswer,
                correctAnswersArray,
                selectedAnswersStrings,
                containsAll,
              });
            }

            if (containsAll) {
              isGoodAnswer = true;
              reponsesJustes++;
              allPointsOrNothingTickedResult = 'allGood';
            }
          } else {

            // PROPOSITION VRAIE COCHEE VRAIE
            /* If answer is true or "hors concours" we add good answer */
            //if (await QCMService.isAnswerTrue(selectedAnswersIds, questionAnswer) || questionAnswer.isHorsConcours) {

            // --- Si utilisateur a bien répondu --
            let userAnswerIsGood;
            if (notation === 'custom') {
              // Bon si il a mis vrai et que c'est vrai, ou faux et que c'est faux, ou juste hors concours
              userAnswerIsGood = ((answerIsTicked && questionAnswer?.isTrue) || (answerIsTickedAsFalse && !questionAnswer?.isTrue)) || questionAnswer.isHorsConcours;
              isDev && console.log(`notation custom: userAnswerIsGood ${question?.id_question} =`, userAnswerIsGood);
            } else {
              userAnswerIsGood = await QCMService.isAnswerTrue(selectedAnswersIds, questionAnswer);
            }

            if (userAnswerIsGood) {
              isGoodAnswer = true;
              reponsesJustes++;

              /* Handles special answer all points or nothing */
              if (questionAnswer.isAllPointsOrNothing && answerIsTicked) {
                allPointsOrNothingTickedResult = 'allGood';
              }

            } else {
              // Autres cas

              /* Handles special answer all points or nothing */
              if (questionAnswer.isAllPointsOrNothing && answerIsTicked) {
                allPointsOrNothingTickedResult = 'allLost';
              }

              /* Cas barême custom: list erreurs possibles */
              // PROPOSITION VRAIE COCHEE FAUX
              if (questionAnswer.isTrue && answerIsTickedAsFalse) {
                // Ajouter le nombre d'erreur de ce type là
                customErrors.push({
                  questionAnswerId: questionAnswer?.id,
                  type: McqScaleRulesProperties.pointsLostPerErrorIfTrueAnswerIsCheckedFalse,
                  settings: McqScaleRulesProperties.pointsLostPerErrorIfTrueAnswerIsCheckedFalseSettings,
                });
              }
              // PROPOSITION FAUSSE COCHEE VRAIE
              else if (!questionAnswer.isTrue && answerIsTicked) {
                customErrors.push({
                  questionAnswerId: questionAnswer?.id,
                  type: McqScaleRulesProperties.pointsLostPerErrorIfFalseAnswerIsCheckedTrue,
                  settings: McqScaleRulesProperties.pointsLostPerErrorIfFalseAnswerIsCheckedTrueSettings,
                });
              }
              // PROPOSITION FAUSSE N'EST PAS COCHEE
              else if (!questionAnswer.isTrue && answerIsUnchecked) {
                customErrors.push({
                  questionAnswerId: questionAnswer?.id,
                  type: McqScaleRulesProperties.pointsLostPerErrorIfFalseAnswerIsUndefined,
                  settings: McqScaleRulesProperties.pointsLostPerErrorIfFalseAnswerIsUndefinedSettings,
                });
              }
              // PROPOSITION VRAIE N'EST PAS COCHEE
              else if (questionAnswer.isTrue && answerIsUnchecked) {
                customErrors.push({
                  questionAnswerId: questionAnswer?.id,
                  type: McqScaleRulesProperties.pointsLostPerErrorIfTrueAnswerIsUndefined,
                  settings: McqScaleRulesProperties.pointsLostPerErrorIfTrueAnswerIsUndefinedSettings,
                });
              } else {
                console.log('WARNING: cas non géré (proposition correction)');
              }

              erreurs++;
            }
          }

          ///////////////////////
          /* SAVE ANSWER */
          // Alphanum
          if (isAlphanumericalQuestion) {
            if (!hasInsertedAlphanumQuestion) {
              actualAnswer.qcmStatsQuestion.push({
                isGood: isGoodAnswer,
                answerId: questionAnswer?.id,
                value: JSON.stringify(selectedAnswersIds), // user text input
              });
              hasInsertedAlphanumQuestion = true;
            }
          }
          /* Freetext / texte libre */
          else if (actualQuestion.isAnswerFreeText) {
            actualAnswer.qcmStatsQuestion.push({
              isGood: null, // cannot be boolean
              answerId: questionAnswer?.id,
              value: selectedAnswersIds[0], // user text input
            });
          }
          // Most cases: QCM/QCU
          else {
            // Si coché vrai ou faux, on sauvegarde la valeur
            if (!answerIsUnchecked) {
              actualAnswer.qcmStatsQuestion.push({
                isGood: isGoodAnswer, // Has good answer on item
                value: answerIsTicked, // Si coché, true, si coché faux, false,
                answerId: questionAnswer?.id,
              });
            }
          }
          // Cas à ajouter: texte à trou, et autres

          ////////////////////////
        }
      }
      /* --------------------------
          END for questionAnswers
      *  -------------------------- */


      /*
       * SCALE (Barême)
       * --------------
      */
      // Tous les points au départ
      note += mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion];

      // Handle special alphanum question case when user hasn't chosen any good combination
      if (isAlphanumericalQuestion && allPointsOrNothingTickedResult !== 'allGood') {
        // Pas trouvé la bonne combinaison, c'est soit tout, soit rien
        allPointsOrNothingTickedResult = 'allLost';
        erreurs++;
      }

      let nbAnswers = questionAnswers?.length;

      // Schema nombre de reponses = nombre de légendes cochées par admin
      if (isSchemaExercise || isSchemaFillInLegendExercise) {
        nbAnswers = numberOfSchemaLegendsAsked;
      }
      if (isFillInTheBlankExercise) {
        nbAnswers = numberOfFillTheBlanks;
      }
      if (isReorderExercise) {
        nbAnswers = actualQuestion?.settings?.correctOrder?.length || 1;
      }

      // HANDLES ALL good / all lost
      if (['allGood', 'allLost'].includes(allPointsOrNothingTickedResult)) {
        if (allPointsOrNothingTickedResult === 'allLost') {
          note = mcqScale.rules[McqScaleRulesProperties.minimumGrade];
        } // if all good, already has all points
      } else {
        if (mcqScale.type === McqScaleType.dynamic) {
          // --------------- Dynamic scale ------------------
          isDev && console.log('notation dynamique');
          // QRM + Schemas + Texte à trous + Reorder
          if (actualQuestion.isCheckbox || actualQuestion.isAnswerMultipleChoiceInList || isSchemaExercise || isSchemaFillInLegendExercise || isFillInTheBlankExercise || isReorderExercise) {
            const pointsLostPerError = -(mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion] / nbAnswers);
            for (let e = 0; e < erreurs; e++) {
              note += pointsLostPerError;
            }
          } else if (actualQuestion.isAnswerFreeText) {
            // TODO
          }
          // QRU
          else if (!actualQuestion.isCheckbox) {
            if (erreurs > 0) {
              note -= mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion];
            }
          }
          // End dynamic scale

        } else {
          // QRM
          //  --------------- Normal and custom scale ------------------
          if (notation === 'custom') {
            // mettre le count ici
            let countCustomErrorsByType = {
              [McqScaleRulesProperties.pointsLostPerErrorIfTrueAnswerIsCheckedFalse]: 0,
              [McqScaleRulesProperties.pointsLostPerErrorIfFalseAnswerIsCheckedTrue]: 0,
              [McqScaleRulesProperties.pointsLostPerErrorIfTrueAnswerIsUndefined]: 0,
              [McqScaleRulesProperties.pointsLostPerErrorIfFalseAnswerIsUndefined]: 0,
            };

            // CUSTOM ERRORS Only - pas pour les schemas
            for (const customError of customErrors) {
              // TODO handle settings : fixed or variableByTotalAnswers or variableByTrueAnswers
              const settings = mcqScale.rules[customError.settings];

              countCustomErrorsByType[customError.type]++;
              const e = countCustomErrorsByType[customError.type] - 1;

              // Nombre d'erreur pour ce type d'erreur
              const nbErrorForThisType = countCustomErrorsByType[customError.type];

              if (settings === McqScaleRulesSettingsValues.fixed) {
                const pointsLostPerError = mcqScale.rules[customError.type]; // array
                // Si on a défini un nombre de points par erreur pour l'erreur numéro e
                if (pointsLostPerError.hasOwnProperty(e)) {
                  note += pointsLostPerError[e];
                  isDev && console.log(`[type: fixed] [Perdu : ${pointsLostPerError[e]} ] erreur n°${e + 1} (pointsLostPerError: ${pointsLostPerError})  reason: ${customError.type}`);
                } else {
                  isDev && console.log(`Attention, !pointsLostPerError.hasOwnProperty(e=${e}) - donc l'erreur sera pas prise en compte`);
                }
              } else if (settings === McqScaleRulesSettingsValues.variableByTotalAnswers) {
                // Points par question / nombre de réponses total
                const pointsLostPerError = -(mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion] / nbAnswers);
                note += pointsLostPerError;
                isDev && console.log(`[type: variableByTotalAnswers] [Perdu : ${pointsLostPerError} ] note=${note}`);
              } else if (settings === McqScaleRulesSettingsValues.variableByTrueAnswers) {
                // Points par question / nombre de réponses vraie
                const nbTrueAnswers = questionAnswers.filter(qa => qa.isTrue)?.length;
                const pointsLostPerError = -(mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion] / nbTrueAnswers);
                note += pointsLostPerError;
                isDev && console.log(`[type: variableByTrueAnswers] [Perdu : ${pointsLostPerError} ] note=${note}`);
              } else {
                console.log('WARNING: cas non géré (barême custom : McqScaleRulesSettingsValues)');
              }

            }

          } else {
            // CLASSIC MCQ +

            isDev && console.log({ erreurs });

            // Pour chaque erreur on perd des points
            const pointsLostPerError = mcqScale.rules[McqScaleRulesProperties.pointsLostPerError];
            for (let e = 0; e < erreurs; e++) {
              // Si on a défini un nombre de points par erreur pour l'erreur numéro e
              if (pointsLostPerError.hasOwnProperty(e)) {
                note += pointsLostPerError[e];
              }
            }
          }

          if (note < mcqScale.rules[McqScaleRulesProperties.minimumGrade]) {
            note = mcqScale.rules[McqScaleRulesProperties.minimumGrade];
          }
        }

        /* Special rule when nothing checked, set grade*/
        if (checkedNothing && mcqScale?.pointsObtainedWhenNothingChecked !== null && !isFillInTheBlankExercise && !isReorderExercise) {
          note = parseFloat(mcqScale?.pointsObtainedWhenNothingChecked);
          isDev && console.log(`note fixe car rien de coché : ${mcqScale?.pointsObtainedWhenNothingChecked}`);
        }
      }

      /*
       * End SCALE (Barême)
       * --------------
      */

      // Update grade actual question
      actualAnswer.pointsObtained = note;

      /* Ponderated grade */
      /* ---------------- */
      if (calculPondere || question.evaluateCertainty) {
        let typeQuestion, ponderatedGrade = 0;
        const numberOfAnswers = questionAnswers?.length;
        if (!actualQuestion.isAnswerFreeText && !actualQuestion.isCheckbox && !actualQuestion.isAnswerMultipleChoiceInList) {
          typeQuestion = 'singleChoice';
          const hasGotAllPoints = (note === mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion]);
          if (hasGotAllPoints) {
            switch (actualAnswer.certainty) {
              case CERTAINTY_KEYS.AU_HASARD:
                ponderatedGrade = 0;
                break;
              case CERTAINTY_KEYS.PEU_SUR:
                ponderatedGrade = 0.33;
                break;
              case CERTAINTY_KEYS.MOYENNEMENT_SUR:
                ponderatedGrade = 0.66;
                break;
              case CERTAINTY_KEYS.CERTAIN:
                ponderatedGrade = 1;
                break;
              default:
                break;
            }
          } else {
            switch (actualAnswer.certainty) {
              case CERTAINTY_KEYS.AU_HASARD:
                ponderatedGrade = 0;
                break;
              case CERTAINTY_KEYS.PEU_SUR:
                ponderatedGrade = -0.33;
                break;
              case CERTAINTY_KEYS.MOYENNEMENT_SUR:
                ponderatedGrade = -0.66;
                break;
              case CERTAINTY_KEYS.CERTAIN:
                ponderatedGrade = -1;
                break;
              default:
                break;
            }
          }
        }

        if (!actualQuestion.isAnswerFreeText && (actualQuestion.isCheckbox || actualQuestion.isAnswerMultipleChoiceInList)) {
          typeQuestion = 'multipleChoice';
          const pointsPerAnswer = mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion] / numberOfAnswers;
          // Propositions justes
          for (let i = 0, tmpPonderatedGrade = 0; i < reponsesJustes; i++) {
            switch (actualAnswer.certainty) {
              case CERTAINTY_KEYS.AU_HASARD:
                tmpPonderatedGrade = 0 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.PEU_SUR:
                tmpPonderatedGrade = 0.33 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.MOYENNEMENT_SUR:
                tmpPonderatedGrade = 0.66 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.CERTAIN:
                tmpPonderatedGrade = 1 * pointsPerAnswer;
                break;
              default:
                break;
            }
            ponderatedGrade += tmpPonderatedGrade;
          }
          // Propositions fausses
          for (let i = 0, tmpPonderatedGrade = 0; i < erreurs; i++) {
            switch (actualAnswer.certainty) {
              case CERTAINTY_KEYS.AU_HASARD:
                tmpPonderatedGrade = 0 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.PEU_SUR:
                tmpPonderatedGrade = -0.33 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.MOYENNEMENT_SUR:
                tmpPonderatedGrade = -0.66 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.CERTAIN:
                tmpPonderatedGrade = -1 * pointsPerAnswer;
                break;
              default:
                break;
            }
            ponderatedGrade += tmpPonderatedGrade;
          }
        }
        actualAnswer.ponderatedPointsObtained = ponderatedGrade;
        totalPonderatedGrade += ponderatedGrade;
      }

      if (isDev) {
        console.log({ note, reponses, reponsesJustes });
      }
      return {
        reponsesUtilisateur: reponses, // Réponses enrichies
        note, // Note de la question
        reponsesJustes,
        erreurs,
        pointsPerQuestion: mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion],
        totalPonderatedGrade,
      };
    } catch (e) {
      console.error('--calculNoteQuestion--');
      console.error(e);
    }
  },


  // Before new scale
  calculNoteQuestion_OLD: async (question, reponses, calculPondere = false) => {
    try {
      let reponsesJustes = 0, note = 0, erreurs = 0, totalPonderatedGrade = 0;
      // Question
      let actualQuestion = question;
      // Correction answers
      let questionAnswers = actualQuestion.question_answers || actualQuestion.answers;
      // User answers
      let actualAnswer = reponses;
      actualAnswer.qcmStatsQuestion = [];

      const selectedAnswersIds = actualAnswer.answers || actualAnswer.answersData || [];
      if (String(actualQuestion.id_question) !== String(actualAnswer.id_question)) { // should never be false
        throw new GraphQLError('actualQuestion.id_question !== actualAnswer.id_question');
      }

      // User has checked nothing
      const checkedNothing = selectedAnswersIds?.length === 0;
      const isAlphanumericalQuestion = [QuestionAnswerType.ALPHANUMERICAL, QuestionAnswerType.NUMERICAL].includes(actualQuestion?.type);
      let hasInsertedAlphanumQuestion = false;

      let allPointsOrNothingTickedResult = 'none';

      /*
       * Loop answers and get number of good and bad answers
       * --------------
      */
      for (const questionAnswer of questionAnswers) {
        let isGoodAnswer = false;
        const answerIsTicked = !!(selectedAnswersIds?.map(id => id.toString())?.includes(questionAnswer?.id?.toString()));

        /* Handle special alphanumerical */
        if (isAlphanumericalQuestion) {
          // Si tout coché, tous les points, sinon 0
          const correctAnswersArray = JSON?.parse(questionAnswer?.text);
          const containsAll = correctAnswersArray?.every(arr2Item => selectedAnswersIds?.includes(arr2Item));
          if (containsAll) {
            isGoodAnswer = true;
            reponsesJustes++;
            allPointsOrNothingTickedResult = 'allGood';
          }
        } else {
          /* If answer is true or "hors concours" we add good answer */
          if (await QCMService.isAnswerTrue(selectedAnswersIds, questionAnswer) || questionAnswer.isHorsConcours) {
            isGoodAnswer = true;
            reponsesJustes++;

            /* Handles special answer all points or nothing */
            if (questionAnswer.isAllPointsOrNothing && answerIsTicked) {
              allPointsOrNothingTickedResult = 'allGood';
            }

          } else {
            erreurs++;
            /* Handles special answer all points or nothing */
            if (questionAnswer.isAllPointsOrNothing && answerIsTicked) {
              allPointsOrNothingTickedResult = 'allLost';
            }
          }
        }

        // Save only ticked answers
        if (isAlphanumericalQuestion) {
          if (!hasInsertedAlphanumQuestion) {
            actualAnswer.qcmStatsQuestion.push({
              isGood: isGoodAnswer,
              answerId: questionAnswer?.id,
              value: JSON.stringify(selectedAnswersIds), // user text input
            });
            hasInsertedAlphanumQuestion = true;
          }
        }
        /* Freetext / texte libre */
        else if (actualQuestion.isAnswerFreeText) {
          actualAnswer.qcmStatsQuestion.push({
            isGood: null, // cannot be boolean
            answerId: questionAnswer?.id,
            value: selectedAnswersIds[0], // user text input
          });
        }
          ///////////////////////
        // Most cases: QCM/QCU
        else if (answerIsTicked) {
          actualAnswer.qcmStatsQuestion.push({
            isGood: isGoodAnswer, // for stats
            answerId: questionAnswer?.id,
          });
        }
        ////////////////////////
      }

      /*
       * SCALE (Barême) pour calculer la note en fonction des erreurs
       * --------------
      */
      const mcqScale = await QCMService.getMcqScale({ question: actualQuestion });
      // Tous les points au départ
      note += mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion];

      // Handle special alphanum question case when user hasn't chosen any good combination
      if (isAlphanumericalQuestion && allPointsOrNothingTickedResult !== 'allGood') {
        // Pas trouvé la bonne combinaison, c'est soit tout, soit rien
        allPointsOrNothingTickedResult = 'allLost';
        erreurs++;
      }

      // HANDLES ALL good / all lost
      if (['allGood', 'allLost'].includes(allPointsOrNothingTickedResult)) {
        if (allPointsOrNothingTickedResult === 'allLost') {
          note = mcqScale.rules[McqScaleRulesProperties.minimumGrade];
        } // if all good, already has all points
      } else {
        /* Normal question grade calculation */
        if (mcqScale.type === McqScaleType.dynamic) {
          // Dynamic scale
          const nbAnswers = questionAnswers?.length;
          // QRM
          if (actualQuestion.isCheckbox || actualQuestion.isAnswerMultipleChoiceInList) {
            const pointsLostPerError = -(mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion] / nbAnswers);
            for (let e = 0; e < erreurs; e++) {
              note += pointsLostPerError;
            }
          } else if (actualQuestion.isAnswerFreeText) {
            // TODO
          }
          // QRU
          else if (!actualQuestion.isCheckbox) {
            if (erreurs > 0) {
              note -= mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion];
            }
          }
        } else {
          // QRM
          // Pour chaque erreur on perd des points
          const pointsLostPerError = mcqScale.rules[McqScaleRulesProperties.pointsLostPerError];
          for (let e = 0; e < erreurs; e++) {
            if (pointsLostPerError.hasOwnProperty(e)) {
              note += pointsLostPerError[e];
            }
          }
          if (note < mcqScale.rules[McqScaleRulesProperties.minimumGrade]) {
            note = mcqScale.rules[McqScaleRulesProperties.minimumGrade];
          }
        }

        /* Special rule when nothing checked, set grade*/
        if (checkedNothing && mcqScale?.pointsObtainedWhenNothingChecked !== null) {
          note = parseFloat(mcqScale?.pointsObtainedWhenNothingChecked);
        }
      }

      /*
       * End SCALE (Barême)
       * --------------
      */

      // Update grade actual question
      actualAnswer.pointsObtained = note;

      /* ponderated grade */
      if (calculPondere || question.evaluateCertainty) {
        let typeQuestion, ponderatedGrade = 0;
        const numberOfAnswers = questionAnswers?.length;
        if (!actualQuestion.isAnswerFreeText && !actualQuestion.isCheckbox && !actualQuestion.isAnswerMultipleChoiceInList) {
          typeQuestion = 'singleChoice';
          const hasGotAllPoints = (note === mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion]);
          if (hasGotAllPoints) {
            switch (actualAnswer.certainty) {
              case CERTAINTY_KEYS.AU_HASARD:
                ponderatedGrade = 0;
                break;
              case CERTAINTY_KEYS.PEU_SUR:
                ponderatedGrade = 0.33;
                break;
              case CERTAINTY_KEYS.MOYENNEMENT_SUR:
                ponderatedGrade = 0.66;
                break;
              case CERTAINTY_KEYS.CERTAIN:
                ponderatedGrade = 1;
                break;
              default:
                break;
            }
          } else {
            switch (actualAnswer.certainty) {
              case CERTAINTY_KEYS.AU_HASARD:
                ponderatedGrade = 0;
                break;
              case CERTAINTY_KEYS.PEU_SUR:
                ponderatedGrade = -0.33;
                break;
              case CERTAINTY_KEYS.MOYENNEMENT_SUR:
                ponderatedGrade = -0.66;
                break;
              case CERTAINTY_KEYS.CERTAIN:
                ponderatedGrade = -1;
                break;
              default:
                break;
            }
          }
        }

        if (!actualQuestion.isAnswerFreeText && (actualQuestion.isCheckbox || actualQuestion.isAnswerMultipleChoiceInList)) {
          typeQuestion = 'multipleChoice';
          const pointsPerAnswer = mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion] / numberOfAnswers;
          // Propositions justes
          for (let i = 0, tmpPonderatedGrade = 0; i < reponsesJustes; i++) {
            switch (actualAnswer.certainty) {
              case CERTAINTY_KEYS.AU_HASARD:
                tmpPonderatedGrade = 0 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.PEU_SUR:
                tmpPonderatedGrade = 0.33 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.MOYENNEMENT_SUR:
                tmpPonderatedGrade = 0.66 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.CERTAIN:
                tmpPonderatedGrade = 1 * pointsPerAnswer;
                break;
              default:
                break;
            }
            ponderatedGrade += tmpPonderatedGrade;
          }
          // Propositions fausses
          for (let i = 0, tmpPonderatedGrade = 0; i < erreurs; i++) {
            switch (actualAnswer.certainty) {
              case CERTAINTY_KEYS.AU_HASARD:
                tmpPonderatedGrade = 0 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.PEU_SUR:
                tmpPonderatedGrade = -0.33 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.MOYENNEMENT_SUR:
                tmpPonderatedGrade = -0.66 * pointsPerAnswer;
                break;
              case CERTAINTY_KEYS.CERTAIN:
                tmpPonderatedGrade = -1 * pointsPerAnswer;
                break;
              default:
                break;
            }
            ponderatedGrade += tmpPonderatedGrade;
          }
        }
        actualAnswer.ponderatedPointsObtained = ponderatedGrade;
        totalPonderatedGrade += ponderatedGrade;
      }

      return {
        reponsesUtilisateur: reponses,
        note,
        reponsesJustes,
        erreurs,
        pointsPerQuestion: mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion],
        totalPonderatedGrade,
      };
    } catch (e) {
      console.error('--calculNoteQuestion--');
      console.error(e);
    }
  },


  /* Calcul note de toutes les questions */
  calculNoteQcm: async (questions, reponses, calculPondere = false) => {
    try {
      let reponsesJustes = 0, erreurs = 0, note = 0, totalPonderatedGrade = 0;
      let maxPoints = 0;// points max atteignables
      for (let i in questions) {
        const actualAnswer = reponses[i];
        if (!actualAnswer) {
          continue;
        }
        let actualQuestion = questions.find(q => q.id_question == actualAnswer.id_question); // RE ORDER trouve question correspondant à la réponse
        const {
          note: noteQuestion,
          reponsesJustes: reponsesJustesQuestion,
          erreurs: erreursQuestion,
          pointsPerQuestion,
          totalPonderatedGrade: ponderatedGrade,
        } = await QCMService.calculNoteQuestion(actualQuestion, actualAnswer, calculPondere);

        // Update note finale
        note += noteQuestion;

        maxPoints += pointsPerQuestion;

        // Update PER question grade
        actualAnswer.pointsObtained = noteQuestion;
        actualAnswer.ponderatedPointsObtained = ponderatedGrade;

        // Update PER answer
        reponsesJustes += reponsesJustesQuestion;
        erreurs += erreursQuestion;
        totalPonderatedGrade += ponderatedGrade;
      }

      return { reponsesUtilisateur: reponses, note, reponsesJustes, totalPonderatedGrade, maxPoints };
    } catch (e) {
      console.error('calculNoteQcm');
      console.error(e);
    }
  },

  /* Fetching the MCQ with the id passed in the function. Checks user's permission */
  getQcmById: async (id, userId) => {
    try {
      // const me = await models.User.findByPk(userId);
      let qcm = await models.Qcm.findOne({
        where: {
          id_qcm: id,
          deleted: 0,
        },
      });
      if (qcm) {
        // ICI modifs seulement par type

        const hasBeenSeen = await UserViewHistoryService.setQcmSeen(userId, id);
        if (!hasBeenSeen) {
          qcm.views += 1;
          await qcm.save();
        }
        return qcm;

        /*
          ICI IMPORTANT vérification par TYPE de QCM virée pour le moment, pour exams et autres.
          const authorized = await PermissionService.isUserAuthorizedForMcq(me, qcm);
          if (authorized) {
          } else {
            throw new GraphQLError('Vous n\'avez pas la permission d\'accéder à ce QCM, probablement car votre forfait actuel ne vous le permet pas.');
          }
        */
      } else {
        throw new GraphQLError('Série introuvable, probablement supprimée');
      }
    } catch (e) {
      //console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /* Get QCM in legacy QcmCategories */
  getQcmsInSousCategorie: async (id, userId) => {
    try {
      return await models.Qcm.findAll({
        where: {
          sousCategorieIdSousCategorie: id,
          deleted: 0,
        },
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur récupération QCM ' + id);
    }
  },

  getQcmsInSousCategorieAndAnnee: async (id, annee, userId) => {
    try {
      return await models.Qcm.findAll({
        where: {
          sousCategorieIdSousCategorie: id,
          deleted: 0,
        },
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur récupération QCM ' + id);
    }
  },

  /* TODO remove if Unused (legacy) */
  async getQCMByLink(id_lien) {
    try {
      return await models.Qcm.findAll({
        where: {
          id_lien,
        },
      });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  /* Fetching the mcqScale from database */
  async getMcqScale({ question }) {
    /* fonction qui d'abord essaye de trouver la scale adaptée avec la scale dans questionId, sinon, fais le wrapper pour trouver un fallback */
    try {
      let mcqScale = await models.McqScale.findByPk(question?.mcqScaleId);
      if (!mcqScale) {
        if (SHOW_SCALE_LOG){console.error(`pas de mcqScale associé à la question d'id (${question?.id_question}). Essay de fetch automatique`);}
        mcqScale = await McqScaleService.wrapperFindScaleFromQuestion({ question });
      }
      return mcqScale;
    } catch (e) {
      console.error(`Dans getMcqScale => error : ${e}`);
      throw new GraphQLError(e);
    }
  },

  /* Getting the points to make a certainty graph of a certain user for a certain qcm, by certainty and user's session */
  async getPointsByCertainty({ id_qcm, sessionId, userId: targetUserId }, userId) {
    try {
      const targetUser = targetUserId ? targetUserId : userId;
      let questions = await QCMService.getQuestionsForQcm(id_qcm); // questions with answers
      let graphData = [
        {
          certainty: 'Au hasard',
          points: 0,
        },
        {
          certainty: 'Peu sûr',
          points: 0,
        },
        {
          certainty: 'Moyennement sûr',
          points: 0,
        },
        {
          certainty: 'Certain',
          points: 0,
        },
      ];
      // Tous les points au départ
      for (const question of questions) {
        /* SCALE (Barême) */
        const mcqScale = await QCMService.getMcqScale({ question });
        const pointsMax = mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion];
        let pointsObtained = 0;
        let correctionAnswers = question.question_answers;
        let userAnswers = await QCMStatsService.getQuestionAnswerHistory(question, { sessionId }, targetUser);
        if (userAnswers) {
          pointsObtained = userAnswers.pointsObtained;
          switch (userAnswers.certainty) {
            case CERTAINTY_KEYS.AU_HASARD:
              graphData[0].points += pointsObtained;
              break;
            case CERTAINTY_KEYS.PEU_SUR:
              graphData[1].points += pointsObtained;
              break;
            case CERTAINTY_KEYS.MOYENNEMENT_SUR:
              graphData[2].points += pointsObtained;
              break;
            case CERTAINTY_KEYS.CERTAIN:
              graphData[3].points += pointsObtained;
              break;
            default:
              break;
          }
        }
      }
      return graphData;
    } catch (e) {
      console.error(e);
    }
  },


  //TODO idea try get all data before loops, with only whats needed
  // include with separate: true
  // O(n^2)
  // TODO use redis to cache this?? (not sure if it's worth it)
  /* Graph data for Questions. Displayed in mcq's correction */
  async getGraphCorrectionForQuestions(
    {
      questions,
      sessionId,
      isSmartMcq = false,
    },
    userId,
    shouldFetchResultsOfEveryone = false,
    groupIds = [],
  ) {
    let uniqueUes = [];
    let uniqueCategories = [];
    let uniqueCours = [];
    let uniqueNotions = [];

    let session;
    if (sessionId && isSmartMcq) {
      session = await models.QcmSession.findByPk(sessionId);
    }

    // Process each question
    for (const question of questions) {
      const mcqScale = await QCMService.getMcqScale({ question });

      const pointsMax = mcqScale.rules[McqScaleRulesProperties.pointsPerQuestion];
      let goodAnswers = 0, badAnswers = 0, pointsObtained = 0;
      let correctionAnswers = question.question_answers;

      /* Process each user answers, and update notions */
      const calculateGoodAndBadAnswers = async (currentUserAnswers) => {
        /* Smart mcq question point correction */
        if (isSmartMcq && session) {
          const qId = question?.id_question;
          if (session?.correctQuestionsIds?.includes(qId)) {
            goodAnswers += 2;
          }
          if (session?.failedQuestionsIds?.includes(qId)) {
            badAnswers += 2;
          }

          if (session?.assumedCorrectQuestionsIds?.includes(qId)) {
            goodAnswers += 1;
          }
          if (session?.assumedFailedQuestionsIds?.includes(qId)) {
            badAnswers += 1;
          }
        } else {
          // CALCULATE GOOD / BAD ANSWER PER QUESTION for regular MCQ
          for (const answer of correctionAnswers) {
            // TODO optimize now these values are stored in DB (are they updated after a question change?)
            // Check if answers good or bad
            let isValidAnswer = false;
            //TODO change for new exercise types
            if (await QCMService.isAnswerTrue(currentUserAnswers?.qcmStatsQuestion?.map(s => s?.answerId), answer)) {
              goodAnswers++;
              isValidAnswer = true;
            } else {
              badAnswers++;
            }
            // Build uniqueNotions array
            if (answer?.notions) {
              // If answer has notions
              for (const notion of answer.notions) {
                // Find existing previously detected notion
                let un = uniqueNotions.find(c => String(c.id) === String(notion.id));
                if (!un) {
                  // Notion not found in uniqueNotions array, add it to array with min values
                  if (notion.cours) {
                    const notionClean = notion.get({ plain: true });
                    uniqueNotions.push({
                      ...notionClean,
                      goodAnswers: 0,
                      badAnswers: 0,
                      pointsObtained: 0,
                      pointsMax: 0,
                      coursIds: String(notion?.cours?.map(c => c.id)),
                    });
                    un = uniqueNotions.find(n => String(n.id) === String(notion.id));
                  }
                }
                // update current notion if answer is valid
                if (isValidAnswer) {
                  un.goodAnswers++;
                  // Should update pointsObtained and pointsMax for each notion ?
                  //un.pointsObtained += pointsMax;
                } else {
                  un.badAnswers++;
                }
                // un.pointsObtained = 0;
                // un.pointsMax = 0;
                un.id = String(un.id);
              }
            }
          }
        }
      };


      if (shouldFetchResultsOfEveryone) {
        /* STEP 1 : get answer's history */
        let specificUserIdsToFetch = null;
        let allUsersAnswers;
        const hasGroupIds = (groupIds?.length > 0);
        if (hasGroupIds) {
          // Handle group results
          specificUserIdsToFetch = await GroupeService.getUserIdsInGroupIdsFast(groupIds);
          allUsersAnswers = await QCMStatsService.getEveryoneQuestionAnswerHistories(question, null, specificUserIdsToFetch);
        } else {
          // For all
          allUsersAnswers = await QCMStatsService.getEveryoneQuestionAnswerHistories(question);
        }

        /* STEP 2 : Calculate good and bad answers */
        for (const userAnswers of allUsersAnswers) {
          pointsObtained += userAnswers?.pointsObtained || 0;
          await calculateGoodAndBadAnswers(userAnswers);
        }
      } else {
        /* STEP 1 : get one user answer's history for one question */
        const userAnswers = await QCMStatsService.getQuestionAnswerHistory(question, { sessionId }, userId);
        pointsObtained = userAnswers?.pointsObtained || 0;
        /* STEP 2 : Calculate good and bad answers */
        await calculateGoodAndBadAnswers(userAnswers);
      }

      /* STEP 3 : build tree */
      /* Remonte la question pour mettre cours, catégories et faire la somme les bonnes / mauvaises réponses */
      // Cours => Category => UE
      //let cours = question?.cour;
      // get cours from question
      const courss = await QuestionsService.getCoursLinkedRawForQuestionFastAndMinimal(question);
      for (const cours of courss) {
        //TODO externalise  cours part?
        if (cours) {
          let uc = uniqueCours.find(c => String(c.id) === String(cours.id));
          if (!uc) {
            // Cours pas dans les cours, on ajoute
            uniqueCours.push({ ...cours, goodAnswers: 0, badAnswers: 0, pointsObtained: 0, pointsMax: 0 });
            uc = uniqueCours.find(c => String(c.id) === String(cours.id));
          }
          // update cours
          uc.goodAnswers += goodAnswers;
          uc.badAnswers += badAnswers;
          uc.pointsObtained += pointsObtained;
          uc.pointsMax += pointsMax;
          uc.id = String(uc.id);
          ///////
          // Handle course parents
          let returnedData = await QcmGraphService.handleCourseData({
            cours,
            uniqueCategories,
            uniqueUes,
            goodAnswers,
            badAnswers,
            pointsObtained,
            pointsMax,
          }, { models });
          uniqueCategories = returnedData.uniqueCategories;
          uniqueUes = returnedData.uniqueUes;
        }

      } // End for each course

    } // End for each question

    return {
      uniqueUes,
      uniqueCategories,
      uniqueCours,
      uniqueNotions,
    };
  },

  /**
   * Returns data for user synthesis for a given set of questions IDS, for a given session, for a given user or for a given group
   *
   * (utilisé pour correction exercice user, voir résultat de groupe, voir résultat de tous les étudiants, et résultats )
   *
   * @param whereQuestions
   * @param sessionId
   * @param groupIds
   * @param isSmartMcq
   * @param firstNodeName
   * @param targettedUserId
   * @param shouldFetchResultsOfEveryone
   * @returns {Promise<{children: *, collapsed: boolean, badAnswers: unknown, name: string, id: string, modelType: string, goodAnswers: unknown, pointsObtained: unknown, pointsMax: unknown}>}
   */
  async getGraphCorrectionDataForQuestions(
    {
      whereQuestions,
      sessionId,
      groupIds = null,
      isSmartMcq = false,
      firstNodeName = 'QCM',
      targettedUserId,
      shouldFetchResultsOfEveryone,
      saveToDB = false,
    }) {

    /* Find all questions to process */
    // TODO optimize takes 8sec (faster with raw !)
    let questions = await models.Question.findAll({
      where: whereQuestions,
      //order: [EXOQUALIZE.ORDER_BY_DEFINED_ORDER],
      //logging: true,
      separate: true,
      attributes: ['id_question', 'mcqScaleId'],
      include: [
        {
          model: models.QuestionAnswers,
          separate: true,
          include: [{
            model: models.Notion,
            include: [{
              model: models.Cours,
              attributes: ['id'],
            }],
          }],
        },
      ],
    });
    /*
    let start = new Date().getTime();
    let end = new Date().getTime();
    let time = end - start;
    start = new Date().getTime();
    */

    /* Calcul et Récup bonnes réponses / mauvaises réponses groupées et aggrégée pour tous les groupes */
    let {
      uniqueUes,
      uniqueCategories,
      uniqueCours,
      uniqueNotions,
    } = await QCMService.getGraphCorrectionForQuestions(
      {
        questions,
        sessionId,
        isSmartMcq,
      },
      targettedUserId,
      shouldFetchResultsOfEveryone,
      groupIds,
    );

    // Sorter function
    const sorterFn = (b, a) => ((a?.goodAnswers / (a?.goodAnswers + a?.badAnswers)) - (b?.goodAnswers / (b?.goodAnswers + b?.badAnswers)));

    let debug = false;
    debug && console.log({ uniqueUes });


    // ICI soit on reconstruct le tree comme avant soit on enregistre les data et le front query ce qu'il faut
    let oldWay = false;


    if (oldWay) {
      /* Build tree from arrays */
      for (const ue of uniqueUes) {
        // Mettre les catégories dans l'UE
        let ueCategories = uniqueCategories?.filter(c => String(c?.ueId) === String(ue?.id));
        ueCategories = ueCategories?.sort(sorterFn);
        for (const ueCategory of ueCategories) {
          // Mettre les cours dans la catégorie
          let cours = uniqueCours?.filter(c => String(c?.uecategoryId) === String(ueCategory?.id));
          cours = cours?.sort(sorterFn);
          for (const c of cours) {
            let notions = uniqueNotions?.filter(n => n?.coursIds?.includes(String(c?.id)));
            if (notions) {
              notions = notions?.sort(sorterFn);
            }
            c.children = notions;
          }
          ueCategory.children = cours;
        }
        ue.children = ueCategories;
      }
      uniqueUes = uniqueUes?.sort(sorterFn);

      // For frontend GRAPH config
      uniqueUes = uniqueUes?.map(ue => ({
        ...ue,
        id: `ue-${ue.id}`,
        dbId: `${ue.id}`,
        modelType: 'ue',
        // ICI children peuvent être des cours ou des UE (si l'UE a des enfants) ou des UECategories
        children: ue.children?.map(uec => ({
          ...uec,
          id: `ueCategory-${uec.id}`,
          dbId: `${uec.id}`,
          modelType: 'uec',
          collapsed: true,
          // ICI children peuvent être des cours ou des UECategories
          children: uec.children?.map(c => ({
            ...c,
            id: `cours-${c.id}`,
            dbId: `${c.id}`,
            modelType: 'cours',
            collapsed: true,
            children: c.children?.map(n => ({
              ...n,
              id: `notion-${n.id}-${c.id}`,
              dbId: `${n.id}`,
              modelType: 'notion',
              collapsed: true,
            })),
          })),
        })),
      }));
    } else {
      // NEW WAY BETA
      /* Build tree from arrays */

      // TO UPDATE DB USER STATS
      //TODO put in a service
      if (saveToDB) {
        for (const ue of uniqueUes) {
          // check si la ue existe (normalement oui
          const ueId = parseInt(ue?.id);
          const u = await models.UE.findByPk(ueId, { raw: true });
          if (!u) {
            continue;
          }
          let statUE = await QcmGraphService.getGoodAnswersStatsSynthesisFor({
            userId: targettedUserId,
            ueId: ueId,
          }, { models });
          if (!statUE) {
            // Create
            await models.GoodAnswersStatsUserSynthesis.create({
              ueId: ueId,
              userId: targettedUserId,
              goodAnswers: ue?.goodAnswers,
              badAnswers: ue?.badAnswers,
              pointsObtained: ue?.pointsObtained,
              pointsMax: ue?.pointsMax,
            });
          } else {
            // Update
            await models.GoodAnswersStatsUserSynthesis.update({
              goodAnswers: ue?.goodAnswers,
              badAnswers: ue?.badAnswers,
              pointsObtained: ue?.pointsObtained,
              pointsMax: ue?.pointsMax,
            }, {
              where: {
                userId: targettedUserId,
                ueId: ueId,
              },
            });
          }
        }

        for (const ueCategory of uniqueCategories) {
          const ueCategoryId = parseInt(ueCategory?.id);
          // check si la categ existe
          const categ = await models.UECategory.findByPk(ueCategoryId, { raw: true });
          if (!categ) {
            continue;
          }
          // Same as statUE but with ueCategoryId
          let statUECategory = await QcmGraphService.getGoodAnswersStatsSynthesisFor({
            userId: targettedUserId,
            ueCategoryId,
          }, { models });
          if (!statUECategory) {
            // Create
            await models.GoodAnswersStatsUserSynthesis.create({
              ueCategoryId: ueCategoryId,
              userId: targettedUserId,
              goodAnswers: ueCategory?.goodAnswers,
              badAnswers: ueCategory?.badAnswers,
              pointsObtained: ueCategory?.pointsObtained,
              pointsMax: ueCategory?.pointsMax,
            });
          } else {
            // Update
            await models.GoodAnswersStatsUserSynthesis.update({
              goodAnswers: ueCategory?.goodAnswers,
              badAnswers: ueCategory?.badAnswers,
              pointsObtained: ueCategory?.pointsObtained,
              pointsMax: ueCategory?.pointsMax,
            }, {
              where: {
                userId: targettedUserId,
                ueCategoryId: ueCategoryId,
              },
            });
          }
        }

        /////////////////////////////////////////////////////////////
        // Cours
        /////////////////////////////////////////////////////////////
        for (const cours of uniqueCours) {
          const coursId = parseInt(cours?.id);
          // check si cours existe pour éviter erreur de foreign key
          const c = await models.Cours.findByPk(coursId, { raw: true });
          if (!c) {
            continue;
          }
          // Same as statUE but with ueCategoryId
          let statUECategory = await QcmGraphService.getGoodAnswersStatsSynthesisFor({
            userId: targettedUserId,
            coursId,
          }, { models });
          if (!statUECategory) {
            // Create
            await models.GoodAnswersStatsUserSynthesis.create({
              coursId,
              userId: targettedUserId,
              goodAnswers: cours?.goodAnswers,
              badAnswers: cours?.badAnswers,
              pointsObtained: cours?.pointsObtained,
              pointsMax: cours?.pointsMax,
            });
          } else {
            // Update
            await models.GoodAnswersStatsUserSynthesis.update({
              goodAnswers: cours?.goodAnswers,
              badAnswers: cours?.badAnswers,
              pointsObtained: cours?.pointsObtained,
              pointsMax: cours?.pointsMax,
            }, {
              where: {
                userId: targettedUserId,
                coursId,
              },
            });
          }
          /////////////////////////////////////////////////////////////
          // Save notions associated with cours for graph data ////////
          // Notions du cours actuel, enfants de cours en quelque sorte
          /////////////////////////////////////////////////////////////
          let notions = uniqueNotions?.filter(n => n?.coursIds?.includes(String(coursId)));
          if (notions) {
            // Traitement par paire COURS + NOTION
            for (const notion of notions) {
              const notionId = notion?.id;
              let statNotionsCours = await QcmGraphService.getGoodAnswersStatsSynthesisFor({
                userId: targettedUserId,
                coursId,
                notionId,
              }, { models });
              if (!statNotionsCours) {
                // Create
                //console.log('creating notion');
                await models.GoodAnswersStatsUserSynthesis.create({
                  coursId,
                  notionId,
                  userId: targettedUserId,
                  goodAnswers: notion?.goodAnswers,
                  badAnswers: notion?.badAnswers,
                  pointsObtained: notion?.pointsObtained,
                  pointsMax: notion?.pointsMax,
                });
              } else {
                // Update
                await models.GoodAnswersStatsUserSynthesis.update({
                  goodAnswers: notion?.goodAnswers,
                  badAnswers: notion?.badAnswers,
                  pointsObtained: notion?.pointsObtained,
                  pointsMax: notion?.pointsMax,
                }, {
                  where: {
                    userId: targettedUserId,
                    coursId,
                    notionId,
                  },
                });
              }
            }
          }
        }
      }

      // For frontend GRAPH config
      uniqueUes = await QcmGraphService.buildUETree(
        {
          uniqueUes,
          uniqueCategories,
          uniqueCours,
          uniqueNotions,
        });
    }

    // For frontend GRAPH, return all nodes
    return {
      id: 'firstNode-QCM',
      name: firstNodeName,
      modelType: 'node',
      children: uniqueUes,
      collapsed: false,

      // TOTAL good answers
      goodAnswers: uniqueUes?.map(ue => ue?.goodAnswers)?.reduce((acc, ga) => acc + ga, 0),
      // TOTAL bad answers
      badAnswers: uniqueUes?.map(ue => ue?.badAnswers)?.reduce((acc, ga) => acc + ga, 0),
      // TOTAL points obtained
      pointsObtained: uniqueUes?.map(ue => ue?.pointsObtained)?.reduce((acc, ga) => acc + ga, 0),
      // TOTAL points max
      pointsMax: uniqueUes?.map(ue => ue?.pointsMax)?.reduce((acc, ga) => acc + ga, 0),
    };
  },

  /**
   * Data for graph good/bad answers, support unique and multiple exercises
   * Returns exercise(s) user's synthesis
   *
   * @param id_qcm
   * @param sessionId
   * @param id_qcms
   * @param firstNodeName
   * @param targetUserId
   * @param groupIds
   * @param userId
   * @returns {Promise<{children: [], collapsed: boolean, badAnswers: *, name: string, id: string, modelType: string, goodAnswers: *, pointsObtained: *, pointsMax: number|*}>}
   */
  async getGraphCorrection(
    {
      id_qcm = null,
      sessionId = null,
      id_qcms = [],
      firstNodeName = 'QCM',
      userId: targetUserId,
      groupIds = [],
    }, userId) {
    try {
      let targettedUserId = targetUserId ? targetUserId : userId;
      const userRequester = await models.User.findByPk(userId);

      let whereQuestions;
      let isSmartMcq = false;

      let shouldFetchResultsOfEveryone = false;

      // Dans le cas d'un qcm unique
      if (id_qcm) {
        /* If it's admin or professor, and has never done MCQ, and there is no targetUserId or target groups, fetch all results */
        if ([ROLES.ADMIN, ROLES.SUB_ADMIN, ROLES.TUTEUR].includes(userRequester.role) && !targetUserId) {
          // Si c'est admin et dans le cas d'un qcm unique
          // Should fetch all results for this mcq
          shouldFetchResultsOfEveryone = true;
        }
        // Find MCQ
        const qcm = await models.Qcm.findByPk(id_qcm, {
          attributes: ['hasExternalQuestions', 'questionPickingStrategy'],
          raw: true,
        });
        isSmartMcq = qcm?.questionPickingStrategy === 'smart';
        // Handle mcq with external question
        if (qcm?.hasExternalQuestions) {
          // If it's smart mcq
          if (isSmartMcq) {
            // Get questions done by user
            const sessionQcm = await models.QcmSession.findByPk(sessionId);
            // Add questions outside of current mcq
            let qIdsToLookFor = [
              ...sessionQcm.questionsIdsDone,
              ...sessionQcm.assumedCorrectQuestionsIds,
              ...sessionQcm.correctQuestionsIds,
              ...sessionQcm.failedQuestionsIds,
              ...sessionQcm.assumedFailedQuestionsIds,
            ];
            // Delete duplicated if any
            qIdsToLookFor = [...new Set(qIdsToLookFor)];
            whereQuestions = { id_question: qIdsToLookFor };
          } else {
            // get external questions
            const externalQuestions = await models.QuestionsQcm.findAll({
              where: {
                qcmId: id_qcm,
              },
            });
            whereQuestions = { id_question: externalQuestions.map(m => m.questionId) };
          }

        } else {
          // Classic QCM TODO remove now all series have external questions
          whereQuestions = { id_qcm };
        }
      }

      // Pour plusieurs QCMs (page progression)
      if (Array.isArray(id_qcms) && id_qcms.length > 0) {
        whereQuestions = { id_question: [] };
        const qcms = await models.Qcm.findAll({
          where: {
            id_qcm: id_qcms,
          },
          raw: true,
          attributes: ['hasExternalQuestions', 'questionPickingStrategy', 'id_qcm'],
        });

        for (let qcm of qcms) {
          isSmartMcq = qcm?.questionPickingStrategy === 'smart';
          // Handle mcq with external question
          if (qcm?.hasExternalQuestions) {
            // If it's smart mcq
            if (isSmartMcq) {
              // Get questions done by user
              const sessionQcm = await models.QcmSession.findOne({
                where: {
                  qcmId: qcm?.id_qcm,
                  userId: targettedUserId,
                },
                raw: true,
              });
              // Add questions outside of current mcq
              if (sessionQcm) {
                // Determine questions IDS to look for
                let qIdsToLookFor = [
                  ...(sessionQcm.questionsIdsDone ?? []),
                  ...(sessionQcm.assumedCorrectQuestionsIds ?? []),
                  ...(sessionQcm.correctQuestionsIds ?? []),
                  ...(sessionQcm.failedQuestionsIds ?? []),
                  ...(sessionQcm.assumedFailedQuestionsIds ?? []),
                ];
                // Delete duplicated if any
                qIdsToLookFor = [...new Set(qIdsToLookFor)];
                whereQuestions.id_question = [...whereQuestions.id_question, ...qIdsToLookFor];
              }

            } else {
              // get external questions
              const externalQuestions = await models.QuestionsQcm.findAll({
                where: {
                  qcmId: qcm?.id_qcm,
                },
                raw: true,
              });
              whereQuestions.id_question = [...whereQuestions.id_question, ...externalQuestions.map(m => m.questionId)];

            }
          } else {
            // Classic QCM, get classic questions and add to the list
            //TODO check & delete this case now all have external questions
            let classicMcqQuestionList = await models.Question.findAll({
              where: {
                id_qcm: qcm?.id_qcm,
              },
              raw: true,
              attributes: ['id_question'],
            });
            whereQuestions.id_question = [...whereQuestions.id_question, ...(classicMcqQuestionList.map(m => m?.id_question))];
          }
        }
      } // End fetching multiple question for several mcq

      // THE SLOWEST PART
      return QCMService.getGraphCorrectionDataForQuestions({
        whereQuestions,
        sessionId,
        groupIds,
        isSmartMcq,
        firstNodeName,
        targettedUserId,
        shouldFetchResultsOfEveryone,
      });

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },


  getMyQCMResults: async (id_qcm, userId, { userId: correctionUserId }) => {
    try {
      const targetUserId = correctionUserId ? correctionUserId : userId;
      // -- Multiple MCQ results for target user --
      return models.QcmStats.findAll({
        where: {
          id_utilisateur: targetUserId,
          id_qcm,
        },
        order: [['date', 'DESC']],
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /**
   *  Resultat QCM, note, moyenne (moyenne des premiers resultats seulement)
   *  lors de la correction (session ou non)
   */
  getMyQCMResult: async (id_qcm, userId, {
    sessionId = null,
    userId: correctionUserId,
    groupIds = [],
    statId = null,
  }, isFirstTime = true) => {
    try {
      const targetUserId = correctionUserId ? correctionUserId : userId;
      // -- Unique MCQ result for target user --
      let stats = await QCMStatsService.getMyStatResult(id_qcm, targetUserId, { sessionId, statId }, isFirstTime);
      if (!stats) {
        stats = {}; // stats est null si jamais fait le qcm
      }
      // --------------------------------------------------------------

      /* -- All other results -- */
      let statsResultQCM;
      // Si session, on prend tous les résultats
      statsResultQCM = await QCMStatsService.getAllUserResultsForQCM(id_qcm, false, groupIds);
      let moyenne = undefined;
      let averageSeconds = undefined;
      if (statsResultQCM.length > 0) { // Si fait au moins par une autre personne
        // TODO prendre en compte note smart mcq?
        const result = await QCMService.calculMoyenneQcmAndAverageTime(statsResultQCM, statsResultQCM.length);
        moyenne = result.moyenne;
        averageSeconds = result.averageSeconds;
        if (moyenne) {
          stats.moyenne = (moyenne * 1).toFixed(2);
        }
        if(averageSeconds) {
          stats.averageSeconds = averageSeconds;
        }
        stats.minGrade = statsResultQCM[statsResultQCM.length - 1]?.note;
        stats.maxGrade = statsResultQCM[0]?.note || undefined;
        stats.count = statsResultQCM.length;
      }
      // --------------------------------------------------------------
      return stats;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

countResultsForQcm: async (id_qcm) => {
    try {
      return models.QcmStats.count({ where: { id_qcm: id_qcm } });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  getAllResultsForQcm: async (id_qcm, { groupIds = [], firstTimeOnly = false }, me) => {
    try {
      /*
        if (!qcm.isFullscreen) {
          isFirstTime = true; // handles legacy mcq with no session
          // Question by question can be done multiple times
        }
      */
      return QCMStatsService.getAllUserResultsForQCM(id_qcm, firstTimeOnly, groupIds);
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /**
   * Returns linked course
   *
   * @param {models.Qcm} qcm
   */
  getCoursInQcm: async (qcm) => {
    try {
      return await qcm.getCours();
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  /**
   *  Returns implied courses in MCQ questions
   *  @param {models.Qcm} qcm
   *  @return {array} cours - array of models.Cours
   */
  getCoursImpliedInQcmQuestions: async (qcm) => {
    try {
      const questionsIds = await QuestionsService.getQuestionsIdsForMcq(qcm?.id_qcm);
      const coursIdsToRetrieve = await QuestionsService.getCoursIdsLinkedForQuestion(questionsIds);
      return models.Cours.findAll({
        where: {
          id: coursIdsToRetrieve,
          deleted: false,
        },
      });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  /**
   *  Returns Categories implied in mcq
   *  @param {models.Qcm} qcm
   *  @return {array} uecategories - array of models.UECategory
   */
  getUECategoriesImpliedInQcmQuestions: async (qcm) => {
    try {
      let coursIdsToRetrieve;
      if (qcm.hasExternalQuestions) {
        // get external questions
        const externalQuestions = await models.QuestionsQcm.findAll({
          where: {
            qcmId: qcm.id_qcm,
          },
        });
        const questions = await models.Question.findAll({
          where: {
            id_question: externalQuestions.map(m => m.questionId),
          },
          raw: true,
          attributes: ['id_question'],
        });
        const questionsIds = questions.map(q => q.id_question);
        coursIdsToRetrieve = await QuestionsService.getCoursIdsLinkedForQuestion(questionsIds);

      } else {
        //TODO test & delete this case
        const questions = await models.Question.findAll({
          where: {
            id_qcm: qcm.id_qcm,
          },
          raw: true,
          attributes: ['id_question'],
        });
        const questionsIds = questions.map(q => q.id_question);
        coursIdsToRetrieve = await QuestionsService.getCoursIdsLinkedForQuestion(questionsIds);
      }

      const cours = await models.Cours.findAll({
        attributes: ['id', 'uecategoryId'],
        where: {
          id: { [Op.in]: coursIdsToRetrieve },
          deleted: false,
        },
      });
      const ueCategoryIds = await cours.map(c => c.uecategoryId);
      return models.UECategory.findAll({
        where: {
          id: ueCategoryIds,
        },
      });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  /**
   * Get mcq questions with their items (questionAnswers)
   * @param {number} id_qcm
   * @return {array} models.Question with QuestionAnswers and McqScale
   */
  async getQuestionsForQcm(id_qcm) {
    try {
      const qcm = await models.Qcm.findByPk(id_qcm, { attributes: ['hasExternalQuestions'] });
      if (qcm?.hasExternalQuestions) {
        // get external questions
        const externalQuestions = await models.QuestionsQcm.findAll({
          where: {
            qcmId: id_qcm,
          },
          order: [['order', 'ASC']],
        });
        const qIds = externalQuestions?.map(q => q.questionId);
        if (!qIds?.length) {
          return [];
        }
        return models.Question.findAll({
          where: { id_question: qIds },
          order: sequelize.literal('FIELD(questions.id_question,' + qIds?.join(',') + ')'), // CAUSE PROBLEM APTORIA (dans migration à minima !!)
          include: [models.QuestionAnswers, { model: models.McqScale, as: 'mcqScale' }],
        });
      } else {
        //TODO check & delete now all have hasExternalQuestions true
        return models.Question.findAll({
          where: { id_qcm },
          order: [EXOQUALIZE.ORDER_BY_DEFINED_ORDER],
          include: [models.QuestionAnswers, { model: models.McqScale, as: 'mcqScale' }],
        });
      }
    } catch (e) {
      console.error(e);
      //throw new GraphQLError(e.message);
      return [];
    }
  },

  /**
   * Delete user result (grade)
   * @param {number} id
   */
  deleteQcmResult: async ({ id }) => {
    try {
      let qcmResult = await models.QcmStats.findByPk(id);
      if (!qcmResult) {
        return true;
      }
      /*
      * Cannot delete or update a parent row:
      * a foreign key constraint fails
      * (`antemed`.`statistiques`,
      * CONSTRAINT `statistiques_qcmSessionId_foreign_idx`
      * FOREIGN KEY (`qcmSessionId`) REFERENCES `qcm_sessions` (`id`))
      **/
      if (qcmResult.qcmSessionId) {
        // find session
        let sessionQcm = await models.QcmSession.findByPk(qcmResult.qcmSessionId);
        if (sessionQcm) {
          //console.log('deleting result with session found :');
          /*
          Error: Cannot delete or update a parent row:
          a foreign key constraint fails
          (`antemed`.`stats_questions_answers`, CONSTRAINT `stats_questions_answers_ibfk_1`
          FOREIGN KEY (`statsQuestionId`) REFERENCES `statistiques_questions` (`id_statistique_question`))
           */
          const statsQuestions = await models.QcmStatsQuestion.findAll({
            where: {
              [Op.or]: [{
                qcmSessionId: sessionQcm.id,
              }, {
                statId: id,
              }],
            },
          });
          for (const statQ of statsQuestions) {
            // Destroy user question items choices
            await models.StatsQuestionAnswers.destroy({
              where: {
                statsQuestionId: statQ.id_statistique_question,
              },
            });
            await statQ.destroy();
          }
          // stats question result destroy
          // await models.QcmStatsQuestion.destroy({ where: { qcmSessionId: qcmResult.qcmSessionId } });
          // Destroy final result grade
          await models.QcmStats.destroy({ where: { id } });
          // Finally destroy session, normally destroyed already with stats
          await models.QcmSession.destroy({ where: { id: qcmResult.qcmSessionId } });
        }
      } else {
        const statsQuestions = await models.QcmStatsQuestion.findAll({
          where: {
            statId: id,
          },
        });
        for (const statQ of statsQuestions) {
          // Destroy user question items choices
          await models.StatsQuestionAnswers.destroy({
            where: {
              statsQuestionId: statQ.id_statistique_question,
            },
          });
          // Destroy question result
          await statQ.destroy();
        }
        // Destroy final result grade
        await models.QcmStats.destroy({ where: { id } });
      }
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /**
   * Get number of questions in MCQ
   * @param {number} id_qcm
   * @return {number}
   */
  getNombreQuestionsQCM: async (id_qcm) => {
    try {
      //TODO cache
      const qcm = await models.Qcm.findByPk(id_qcm,
        {
          raw: true,
          attributes: ['hasExternalQuestions', 'questionPickingStrategy'],
        });
      if (qcm?.questionPickingStrategy === 'smart') {
        return nb_questions_asked;
      }
      if (qcm?.hasExternalQuestions) {
        return await models.QuestionsQcm.count({
          where: {
            qcmId: id_qcm,
          },
        });
      }
      return await models.Question.count({ where: { id_qcm } });
    } catch (e) {
      console.error('erreur recup nombre questions qcm: ' + e);
      throw new GraphQLError(e.message);
    }
  },

  getNombreDeNotePourQcm: async (id_qcm) => {
    try {
      const result = await sequelize.query('SELECT COUNT(*) AS nb FROM statistiques WHERE id_qcm = ? ORDER BY id DESC',
        {
          type: QueryTypes.SELECT,
          replacements: [id_qcm],
        });
      return result[0].nb;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  calculMoyenneQcm_temp: async (stats, qcmDoneTimes) => {
    try {
      let moyenne = 0.0;
      for (const stat of stats) {
        moyenne += parseFloat(stat.note);
      }
      return (moyenne /= qcmDoneTimes);
    } catch (e) {
      console.error(e);
      return null;
    }
  },

  calculMoyenneQcmAndAverageTime: async (stats, qcmDoneTimes) => {
    try {
      let moyenne = 0.0;
      let averageSeconds = 0.0;
      for (const stat of stats) {
        moyenne += parseFloat(stat.note);
        averageSeconds+=parseFloat(stat?.seconds);
      }
      return {
        moyenne: (moyenne /= qcmDoneTimes),
        averageSeconds: (averageSeconds /= qcmDoneTimes),
      };
    } catch (e) {
      console.error(e);
      return null;
    }
  },

  /**
   * Get number of questions for category
   * @param {number} id_sous_categorie
   * @return {number}
   */
  getNombreQuestionsForCategory: async (id_sous_categorie) => {
    try {
      return await models.Question.count({ where: { id_sous_categorie } });
    } catch (e) {
      console.error(e);
      return null;
    }
  },

  nombreQuestionsNonConnectees: async (id_sous_categorie) => {
    try {
      //TODO remove with "Attribuer cours aux questions"
      return await models.Question.count({ where: { id_sous_categorie: id_sous_categorie, linkCoursId: null } });
    } catch (e) {
      console.error(e);
      return null;
    }
  },

  /**
   * Change certainty evaluation
   * @param {number} id_qcm
   * @param {boolean} toggle
   * @param {number} userId
   */
  changeEvaluateCertaintyAllQuestion: async ({ id_qcm, toggle }, userId) => {
    try {
      const questionIds = await QuestionsService.getQuestionsIdsForMcq(id_qcm);
      const allQuestions = await models.Question.findAll({ where: { id_question: questionIds } });
      for (let q of allQuestions) {
        q.evaluateCertainty = toggle;
        await q.save();
      }
      return true;
    } catch (e) {
      console.error(e);
    }
  },

  /**
   * Change notions questions by keyword
   * @param {string} keyword
   * @param {array} notionsIds
   * @param {string} operation
   * @param {boolean} shouldAddToLinkedClass
   *
   * @return {integer}
   */
  changeNotionsQuestionByKeyword: async ({ keyword, notionsIds, operation, shouldAddToLinkedClass = true }) => {
    try {
      let questionAnswersWithKeyword = await models.QuestionAnswers.findAll({
        where: {
          text: sequelize.where(sequelize.fn('LOWER', sequelize.col('text')), 'LIKE', `%${keyword.toLowerCase()}%`),
        },
      });
      let questionsWithKeyword = await models.Question.findAll({
        where: {
          question: sequelize.where(sequelize.fn('LOWER', sequelize.col('question')), 'LIKE', `%${keyword.toLowerCase()}%`),
        },
      });
      const totalAnswersCount = questionsWithKeyword.length || 0;
      const totalQuestionsCount = questionAnswersWithKeyword.length || 0;
      if (operation === 'add') {
        questionAnswersWithKeyword.forEach(q => {
          q.addNotions(notionsIds);
        });
        questionsWithKeyword.forEach(q => {
          q.addNotions(notionsIds);
          if (shouldAddToLinkedClass) {
            QuestionsService.getCoursLinkedForQuestion(q).then(cours => {
              cours.forEach(c => {
                c.addNotions(notionsIds);
              });
            });
          }
        });

      } else if (operation === 'remove') {
        questionAnswersWithKeyword.forEach(q => {
          q.removeNotions(notionsIds);
        });
        questionsWithKeyword.forEach(q => {
          q.removeNotions(notionsIds);
          if (shouldAddToLinkedClass) {
            QuestionsService.getCoursLinkedForQuestion(q).then(cours => {
              cours.forEach(c => {
                c.removeNotions(notionsIds);
              });
            });
          }
        });
      } else {
        throw new GraphQLError('unknown operation');
      }
      return totalAnswersCount + totalQuestionsCount;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  getQcmIdsLinkedToCoursIds: async (coursIds) => {
    let qcmsTmps = await models.CoursQcm.findAll({
      attributes: ['qcmIdQcm'],
      where: {
        courId: { [Op.in]: coursIds },
      },
    });
    return qcmsTmps.map(q => q.qcmIdQcm);
  },

  //TODO annale change pass typeIds in parameter
  getQcmIdsInUeId: async (ueId, isAnnale = false) => {
    let qcms;
    if (isAnnale) {
      const qcmIdsAnnale = await QcmTypeService.getQcmIdsInAnnale();
      qcms = await models.Qcm.findAll({
        where: {
          id_qcm: qcmIdsAnnale,
          UEId: ueId,
          annale: isAnnale,
          deleted: 0,
        },
      });
    } else {
      qcms = await models.Qcm.findAll({
        where: {
          UEId: ueId,
          annale: isAnnale,
          deleted: 0,
        },
      });
    }

    return qcms.map(q => q.id_qcm);
  },
  getAllQcmIdsInUeId: async (ueId) => {
    let qcms = await models.Qcm.findAll({
      where: {
        UEId: ueId,
        deleted: 0,
      },
    });
    return qcms.map(q => q.id_qcm);
  },

  getMcqTitle: async (mcqId) => {
    let qcm = await models.Qcm.findByPk(mcqId, {
      attributes: ['titre'],
    });
    return qcm?.titre;
  },

  /**
   * Synthèse résultats profil user
   * Global user results synthesis for a given UE
   *
   * @param ueId
   * @param userId
   * @param me
   * @returns {Promise<{children: [], collapsed: boolean, badAnswers: *, name: string, id: string, modelType: string, goodAnswers: *, pointsObtained: *, pointsMax: number|*}>}
   */
  async getGraphProgression({ ueId, userId = null }, me) {
    try {
      let userIdToSearchFor = me.id;
      if ((me.role === 'ADMIN' || 'TUTEUR') && userId) {
        userIdToSearchFor = userId;
      }

      //TODO Possibilité de mettre cache par ue et user ici, et du coup pas besoin chercher question, à voir?
      const ueSynthesisStatsForUserInUE = await models.GoodAnswersStatsUserSynthesis.findOne({
        where: {
          userId: userIdToSearchFor,
          ueId,
          updatedAt: {
            // Mis à jour il y a moins de 24 h !
            [Op.gte]: moment().subtract(1, 'days').toDate(),
          },
        },
      });
      if (!ueSynthesisStatsForUserInUE) {
        // Recalcul tout
      } else {
        // Renvoi les stats? ou fait rien, si le calcul se fait en cron (
      }
      // Front: si pas de stats fraiches,

      const user = await models.User.findByPk(userIdToSearchFor);
      const coursIds = await PermissionService.getAvailableCoursIdsInUEForUser(ueId, user); // Cached
      const questionIdsFromCourses = await QuestionsService.getQuestionsIdsFromCoursIds(coursIds); // TODO cache disabled
      const questionsStats = await models.QcmStatsQuestion.findAll({
        where: {
          id_utilisateur: userIdToSearchFor,
        },
        raw: true,
      });
      const questionIdsToLookFor = questionsStats.map(s => s.id_question).filter(id => questionIdsFromCourses.includes(id));
      const whereQuestions = {
        id_question: questionIdsToLookFor,
      };

      // Get stats for questions for user
      const graphCorrectionDataForQuestionsInUE = await QCMService.getGraphCorrectionDataForQuestions(
        {
          //id_qcms: mcqDoneInUe?.map(q => q.id_qcm),
          whereQuestions,
          firstNodeName: 'Total',
          targettedUserId: userIdToSearchFor,
          saveToDB: true,
        });

      return graphCorrectionDataForQuestionsInUE;

    } catch (e) {
      console.error('error in getGraphProgression');
      console.error(e);
    }
  },

  // TODO move in some question repo
  async getQuestionNumberFromQuestion(question, models) {
    const qcmId = await QCMService.findFirstQcmIdHavingQuestion(question.id_question);
    const questions = await QCMService.getQuestionsForQcm(qcmId);
    if (questions) {
      const targetNumber = questions?.findIndex(quest => String(quest.id_question) === String(question.id_question));
      return targetNumber + 1;
    }
    return null;
  },

  // TODO move in some answer repo
  async getAnswersIdsFromQcms(qcmIds) {
    try {
      const questionIds = await QuestionsService.getQuestionsIdsForMcq(qcmIds);
      let answers = await models.QuestionAnswers.findAll({
        where: {
          questionId: questionIds,
        },
        attributes: ['id'],
      });
      return answers.map(a => a.id);
    } catch (e) {
      console.error(e);
    }
  },
  async getAnswersIdsFromQuestionIds(questionIds) {
    try {
      let answers = await models.QuestionAnswers.findAll({
        where: {
          questionId: questionIds,
        },
        attributes: ['id'],
        raw: true,
      });
      return answers.map(a => a.id);
    } catch (e) {
      console.error(e);
    }
  },

  /* all NOTIONS included in QCM */
  async getNotionsForQcm(qcm, args, ctx) {
    try {
      const questions = await QCMService.getQuestionsForQcm(qcm.id_qcm); // questions and answers
      let allNotions = [];
      for (const question of questions) {
        const notionQuestions = await question.getNotions() || [];
        const notionsQs = await models.NotionQuestionAnswers.findAll({
          where: { answerId: question.question_answers?.map(a => a.id) },
        });
        const notionsAnswers = await models.Notion.findAll({ where: { id: notionsQs?.map(n => n.notionId) } });
        for (const nq of notionQuestions) {
          if (!allNotions.find(n => n.id === nq.id)) {
            allNotions.push(nq);
          }
        }
        for (const na of notionsAnswers) {
          if (!allNotions.find(n => n.id === na.id)) {
            allNotions.push(na);
          }
        }
      }
      return allNotions;
    } catch (e) {
      console.error(e);
    }
  },


  // TODO LEGACY WILL BE REMOVED AFTER MOBILE APP UPDATE
  async revisionTips(filter, { me, models }) {
    try {
      /*
       filter:
        (supprimé) startDate: Date  - date de début
        (supprimé) endDate: Date   - date de fin
        coursId: ID     - id cours
        notionId: ID  -  TODO id notino
      */
      const coursId = filter?.coursId;
      const notionId = filter?.notionId;
      // get questions faites par user
      const statsq = await models.QcmStatsQuestion.findAll({
        where: {
          id_utilisateur: me.id,
        },
        attributes: ['id_question'],
        raw: true,
        nest: true,
      });

      // Questions ids done by user
      const qIdsDoneByUser = statsq?.map(s => s.id_question);

      let debug = false;

      if (filter?.coursId) {
        // Questions not done by user
        // récupération Cours-based type permission
        const typeQcmIds = await PermissionService.CoursTypeQcmSettings.getTypeqcmIdsForCoursModule(
          { coursId, coursModuleType: CoursTypesQcmSettings_MODULE_TYPES.revision },
        );
        debug && console.log({ typeQcmIdsallowed: typeQcmIds });
        // Voir c'est quoi les types que l'utilisateur a le droit de voir de base
        const userTypesAllowed = await PermissionService.getAvailableTypeQcmsForUser(me);
        debug && console.log({ userTypesAllowed: userTypesAllowed.map(t => t.name) });
        const typeQcmIdsAuthorized = userTypesAllowed.map(q => q.id);
        debug && console.log({ typeQcmIdsAuthorized });

        // Prend que les types qui sont dans les types autorisés parmi ceux proposé dans les settings du module du cours
        const typeQcmAllowed = typeQcmIds.filter(q => typeQcmIdsAuthorized.includes(q));
        debug && console.log({ typeQcmAllowed });
        const questionsWithTypeQcmId = await models.QuestionTypeQcm.findAll({
          where: {
            typeQcmId: typeQcmAllowed,
          },
          attributes: ['questionId'],
        });
        // Questions autorisées d'après les settings du module du cours ET les types qcm autorisés par l'utilisateur
        const questionIdsWithTypeQcmAllowed = questionsWithTypeQcmId.map(q => q.questionId);
        debug && console.log({ questionIdsWithTypeQcmAllowed });

        const questionIdsHavingCours = await QuestionsService.getQuestionsIdsFromCoursIds(coursId);
        debug && console.log({ questionIdsHavingCours });

        const questionIdsMatchingFilters = questionIdsHavingCours.filter(q => questionIdsWithTypeQcmAllowed?.includes(q));
        debug && console.log({ questionIdsMatchingFilters });

        const allQuestions = await models.Question.findAll({
          where: {
            id_question: questionIdsMatchingFilters,
            isPublished: true,
          },
          required: true,
          include: [
            {
              model: models.QuestionAnswers,
              separate: true,
              required: true,
            },
          ],
        });

        // Toutes les questions en enlevant celles faites par l'utilisateur
        const questionIdsUndone = questionIdsMatchingFilters.filter(q => !qIdsDoneByUser?.includes(q));
        // Questions pas encore faites
        const questionsNotDone = await models.Question.findAll({
          where: {
            id_question: questionIdsUndone,
            isPublished: true,
          },
          raw: 'true',
          attributes: ['id_question'],
        });
        const questionIdsNotDone = questionsNotDone.map(q => q.id_question);
        // prend seulemenet celles qui sont allowed
        const questionIdsToLookFor = questionIdsNotDone.filter(q => questionIdsWithTypeQcmAllowed.includes(q));

        // garder celles qui ne sont pas faites et qui ont le(s) cours autorisés
        const questionsIdsUndoneWithFilters = questionIdsToLookFor.filter(q => questionIdsHavingCours.includes(q));

        // Questions restantes de révision
        const questions = await models.Question.findAll({
          where: {
            id_question: questionsIdsUndoneWithFilters,
            isPublished: true,
          },
          required: true,
          include: [
            {
              model: models.QuestionAnswers,
              separate: true,
              required: true,
            },
          ],
        });

        await Promise.all(questions?.map(async question => {
          // for generator system, need to add some properties TODO refactor because it uses legacy generator system
          const qcm = await QCMService.getFirstQcmIdHavingQuestion(question.id_question);
          question.annee = qcm?.annee;
          question.annale = qcm?.annale;
          question.id_qcm = qcm?.id_qcm;
        }));

        await Promise.all(allQuestions?.map(async question => {
          // for generator system, need to add some properties
          const qcm = await QCMService.getFirstQcmIdHavingQuestion(question.id_question);
          question.annee = qcm?.annee;
          question.annale = qcm?.annale;
          question.id_qcm = qcm?.id_qcm;
        }));

        return {
          questionsAnswered: statsq.length,
          allQuestionsCount: allQuestions.length, // total questions
          questions: questions, // questions not done yet
          allQuestions: allQuestions, // all questions
        };
      }

      // FOR NOTION
      if (filter?.notionId) {
        // TODO voir si permission par type qcm est suffisante
        const availableQuestionsIds = await PermissionService.getAvailableQuestionsIdsByTypeQcmForUser({ id: me?.id });

        // Récupérer les answers ids
        const notionsQuestionAnswers = await models.NotionQuestionAnswers.findAll({
          where: {
            notionId: filter?.notionId,
          },
          attributes: ['answerId'],
        });
        const answersIdsOfThisNotion = notionsQuestionAnswers.map(nqa => nqa.answerId);

        // Récupérer les questions issus des answers
        const questionsFromAnswers = await models.QuestionAnswers.findAll({
          where: {
            id: answersIdsOfThisNotion,
          },
          attributes: ['questionId'],
        });
        const questionsIdsFromAnswers = questionsFromAnswers.map(q => q.questionId);

        // Récupérer les question ids
        const notionsQuestion = await models.NotionQuestion.findAll({
          where: {
            notionId: filter?.notionId,
          },
          attributes: ['questionId'],
        });
        const questionIdsOfThisNotion = notionsQuestion.map(nqa => nqa.questionId);

        const allQuestionsIds = [...questionsIdsFromAnswers, ...questionIdsOfThisNotion];
        // Remove allQuestionsIds duplicates
        const allQuestionsIdsUnique = [...new Set(allQuestionsIds)];

        // Récupérer les questions avec les bons droits
        const questionIdsWithNotion = allQuestionsIdsUnique.filter(q => availableQuestionsIds.includes(q));

        const allQuestions = await models.Question.findAll({
          where: {
            id_question: questionIdsWithNotion,
          },
          required: true,
          include: [
            {
              model: models.QuestionAnswers,
              separate: true,
              required: true,
            },
          ],
        });

        const questionsNotDone = await models.Question.findAll({
          where: {
            id_question: {
              [Op.notIn]: qIdsDoneByUser,
            },
          },
          raw: 'true',
          attributes: ['id_question'],
        });
        const questionIdsNotDone = questionsNotDone.map(q => q.id_question);
        // prend seulemenet celles qui sont allowed
        const questionIdsToLookFor = questionIdsNotDone.filter(q => availableQuestionsIds.includes(q));

        // Questions pas encore faites avec les bons droits ET qui ont la notion concernée
        const questionIdsWithNotionAndNotDone = questionIdsToLookFor.filter(q => questionIdsWithNotion.includes(q));

        // Questions restantes de révision
        const questions = await models.Question.findAll({
          where: {
            id_question: questionIdsWithNotionAndNotDone,
          },
          required: true,
          include: [
            {
              model: models.QuestionAnswers,
              separate: true,
              required: true,
            },
          ],
        });

        // for generator system
        await Promise.all(questions?.map(question => {
          question.annee = question.qcm.annee;
          question.annale = question.qcm.annale;
          question.id_qcm = question.qcm.id_qcm;
        }));

        await Promise.all(allQuestions?.map(question => {
          question.annee = question.qcm.annee;
          question.annale = question.qcm.annale;
          question.id_qcm = question.qcm.id_qcm;
        }));

        return {
          questionsAnswered: statsq?.length,
          allQuestionsCount: allQuestions?.length, // total questions
          questions: questions, // questions not done yet
          allQuestions,
        };

      }

    } catch (e) {
      console.error(e);
    }
  },


  // QUESTIONS DYNAMIC
  async addQuestionToQcm({ qcmId, questionId }, id) {
    try {
      const qcm = await models.Qcm.findByPk(qcmId);
      const question = await models.Question.findByPk(questionId);
      if (qcm && question) {
        // Check if already exists
        const existing = await models.QuestionsQcm.findOne({
          where: {
            qcmId,
            questionId,
          },
        });
        if (existing) {
          throw new GraphQLError('Exercice déjà présent dans la série');
        }
        await models.QuestionsQcm.create({
          qcmId: qcmId,
          questionId,
          order: question.order, // Garder même ordre ?
        });
      }
      //await qcm.addImportedQuestion(question);
      return !!qcm;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
  async removeQuestionFromQcm({ qcmId, questionId }, id) {
    try {
      const qcm = await models.Qcm.findByPk(qcmId);
      const question = await models.Question.findByPk(questionId);
      await qcm.removeImportedQuestion(question);
      return !!qcm;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /**
   * Search questions in DB
   * @param {object} filter
   * @param {number} userId
   * @return {Object} - count: number of questions. questions: array of models.Question
   */
  async searchQuestions({ filter }, userId) {
    try {
      const log = false;

      log && console.log({ filter });
      const offset = filter.offset;
      const limit = filter.limit;
      const name = filter.name || '';
      const id = filter.id || '';
      const isPublished = filter?.isPublished;
      const linkCoursIds = filter?.linkCoursIds;
      const typeIds = filter?.typeIds;

      const coursPointer = filter?.coursPointer;
      const formatExercices = filter?.formatExercices;
      const userIds = filter?.userIds;

      const dateCreationBegin = filter?.dateCreationBegin;
      const dateCreationEnd = filter?.dateCreationEnd;

      const dateModifBegin = filter?.dateModifBegin;
      const dateModifEnd = filter?.dateModifEnd;

      const seriesFiltration = filter?.seriesFiltration;
      const seriesComparisonSign = filter?.seriesComparisonSign;
      const seriesComparisonNumber = filter?.seriesComparisonNumber;

      let where = {};

      // Récupération des questionsIdsToGet
      // TODO change c'est temporaire pour terminale santé
      // let questionsIdsToGet = await PermissionService.getAvailableQuestionsIdsByTypeQcmForUser({ id: userId });
      const questionsAllTMP = await models.Question.findAll({
        attributes: ['id_question'],
        raw: true,
      });
      let questionsIdsToGet = questionsAllTMP?.map(q => q.id_question);

      if (seriesFiltration && (seriesComparisonNumber !== null && seriesComparisonNumber !== '') && (seriesComparisonSign !== null && seriesComparisonSign !== '')) {

        const operatorMap = {
          '>': Op.gt,
          '>=': Op.gte,
          '<': Op.lt,
          '<=': Op.lte,
          '==': Op.eq,
          '!=': Op.ne,
        };

        let operateurDeComparaison = operatorMap[seriesComparisonSign];


        // Filtration sur le nombre de series.
        const filtredByNumberLinkedQcmId = await models.QuestionsQcm.findAll({
          attributes: [
            'questionId',
            [sequelize.fn('COUNT', sequelize.col('qcmId')), 'nombreDeQcm'],
          ],
          group: ['questionId'],
          having: sequelize.where(sequelize.fn('COUNT', sequelize.col('qcmId')), {
            [operateurDeComparaison]: seriesComparisonNumber,
          }),
          raw: true,
        }).then(node => node.map(question => question.questionId));

        questionsIdsToGet = questionsIdsToGet.filter(q => filtredByNumberLinkedQcmId.includes(q));
      }

      // Filtration sur la date
      if (dateCreationBegin && dateCreationEnd) {
        if (isDev) {
          console.log('dateBegin and date end present');
        }
        where = {
          ...where,
          date_creation: {
            [Op.between]: [moment(dateCreationBegin).toDate(), moment(dateCreationEnd).toDate()],
          },
        };
      }

      if (dateModifBegin && dateModifEnd) {
        if (isDev) {
          console.log('dateModifBegin and date Modif end present');
        }
        where = {
          ...where,
          date_modif: {
            [Op.between]: [moment(dateModifBegin).toDate(), moment(dateModifEnd).toDate()],
          },
        };
      }


      // Filtration sur le nom
      if (name && name !== '') {
        let likeTosearch = `%${name.toLowerCase()}%`;
        where = {
          ...where,
          name: sequelize.where(sequelize.fn('LOWER', sequelize.col('questions.question')), 'LIKE', likeTosearch),
        };
      }

      // Filtration sur le créateur :
      if (userIds && userIds.length > 0) {
        where = { ...where, authorId: { [Op.in]: userIds } };
      }


      // Published null => tout, sinon, soit publié, soit dé-publié
      if (isPublished !== null) {
        where = { ...where, isPublished };
      }

      // By question type
      if (typeIds?.length > 0) {
        const questionIdsFromTypesWanted = await QcmTypeService.getQuestionIdsInTypes(typeIds);
        //console.log({questionsIdsToGet, questionIdsFromTypesWanted});
        questionsIdsToGet = questionsIdsToGet.filter(q => questionIdsFromTypesWanted.includes(q));
      }

      ///////////
      // Block de filtration sur les cours :
      ///////////

      // Si on récupère une liste de coursId
      if (coursPointer === 'cours') {
        const qIds = await QuestionsService.getQuestionsIdsFromCoursIds(linkCoursIds);
        questionsIdsToGet = questionsIdsToGet.filter(q => qIds.includes(q));


      } else if (coursPointer === 'coursless') {
        const qIdsLinked = await QuestionsService.getAllQuestionsIdsLinked();
        questionsIdsToGet = questionsIdsToGet.filter(qId => !qIdsLinked.includes(qId)); // On filtre questionsIdsToGet pour retirer les non linkées

      } else if (coursPointer === 'deletedcours') {
        // Tous les cours IDS supprimés
        const deletedCoursesIds = await models.Cours.findAll({
          where: { deleted: true },
          attributes: ['id'],
          raw: true,
        }).then(value => value.map(cours => cours.id));

        // Les questions des coursSupprimées
        const qIds = await QuestionsService.getQuestionsIdsFromCoursIds(deletedCoursesIds);
        questionsIdsToGet = questionsIdsToGet.filter(q => qIds.includes(q));
      } else {
        throw new Error('parameter \'coursPointer\' is invalid. Need to be : [\'cours\',\'coursless\',\'deletedcours\']');
      }

      // Filtration sur les types d'exercices
      let qcmIdArray = [];
      let qcuIdArray = [];
      let freeTextIdArray = [];
      let alphanumeriqueIdArray = [];
      let numeriqueIdArray = [];
      let texteATrouArray = [];
      let schemasPointAndClickArray = [];

      if (formatExercices.includes('Alphanumérique')) {
        alphanumeriqueIdArray = await models.Question.findAll({
          where: {
            id_question: questionsIdsToGet,
            type: { [Op.in]: ['ALPHANUMERICAL', 'ALPHANUMERICAL_OR_NUMERICAL'] },
          },
          raw: true,
        }).then(value => value.map(node => node.id_question));
      }

      if (formatExercices.includes('Texte à trous')) {
        texteATrouArray = await models.Question.findAll({
          where: {
            id_question: questionsIdsToGet,
            type: 'FILL_IN_THE_BLANKS',
          },
          raw: true,
        }).then(value => value.map(node => node.id_question));
      }

      if (formatExercices.includes('Schémas Point&Click')) {
        schemasPointAndClickArray = await models.Question.findAll({
          where: {
            id_question: questionsIdsToGet,
            type: 'SCHEMA_POINT_AND_CLICK',
          },
          raw: true,
        }).then(value => value.map(node => node.id_question));
      }
      if (formatExercices.includes('Schémas légendes à remplir')) {
        schemasPointAndClickArray = await models.Question.findAll({
          where: {
            id_question: questionsIdsToGet,
            type: 'SCHEMA_FILL_IN_LEGENDS',
          },
          raw: true,
        }).then(value => value.map(node => node.id_question));
      }

      if (formatExercices.includes('Numérique')) {
        numeriqueIdArray = await models.Question.findAll({
          where: {
            id_question: questionsIdsToGet,
            type: 'NUMERICAL',
          },
          raw: true,
        }).then(value => value.map(node => node.id_question));
      }

      if (formatExercices.includes('QCM')) {
        qcmIdArray = await models.Question.findAll({
          where: {
            id_question: questionsIdsToGet,
            isCheckbox: 1,
          },
          raw: true,
        }).then(value => value.map(node => node.id_question));
      }

      if (formatExercices.includes('QCU')) {
        qcuIdArray = await models.Question.findAll({
          where: {
            id_question: questionsIdsToGet,
            isCheckbox: 0,
            isAnswerFreeText: 0,
            type: {
              [Op.or]: {
                [Op.notIn]: ['NUMERICAL', 'ALPHANUMERICAL', 'ALPHANUMERICAL_OR_NUMERICAL'],
                [Op.is]: null,
              },
            },
          },
          raw: true,
        }).then(value => value.map(node => node.id_question));
      }

      if (formatExercices.includes('Texte libre')) {
        freeTextIdArray = await models.Question.findAll({
          where: {
            id_question: questionsIdsToGet,
            isAnswerFreeText: 1,
          },
          raw: true,
        }).then(value => value.map(node => node.id_question));
      }

      // merge des différents types
      const filterFormat = [...new Set([...qcmIdArray, ...qcuIdArray, ...freeTextIdArray, ...alphanumeriqueIdArray, ...numeriqueIdArray, ...texteATrouArray, ...schemasPointAndClickArray])];

      // Filter des éléments à chercher sur les différents formats
      questionsIdsToGet = questionsIdsToGet.filter(q => filterFormat.includes(q));

      where = { ...where, id_question: questionsIdsToGet };

      // Search by ID only
      if (id) {
        if (questionsIdsToGet?.includes(parseInt(id))) {
          where = {
            id_question: id,
          };
        } else {
          return {
            count: 0,
            questions: [],
          };
        }
      }

      // Ne pas afficher les questions générées par AI eronnées, mais afficher celles qui n'ont pas été validées à la main.
      where = {
        ...where,
        ...{
          isAiGenerationError: false,
          /*
          [Op.or]: [
            { aiGenerationHasBeenValidated: true },
            { aiGenerationHasBeenValidated: null },
          ],
          */
        },
      };

      const questions = await models.Question.findAll(
        {
          offset,
          limit,
          where: where,
          order: [['date_modif', 'DESC']],
        },
      );
      const count = await models.Question.count({
        where,
      });

      return {
        // total questions matching filter
        count,
        // Current page questions matching filter
        questions,
      };
    } catch (e) {
      console.error(e);
      console.error('Error while searching question: ' + e.message);
      throw new GraphQLError(e);
    }
  },

  /* Exercices comportant au moins une question de ce cours */
  async getMcqsWithCourseQuestion(coursId, me) {
    try {
      const DEBUG = false;
      const user = await models.User.findByPk(me.id);
      // Ids qcm autorisés de base
      const availableMcqIds = await PermissionService.getAvailableMcqIdsForUser(user);
      // récupération Cours-based type permission
      const typeQcmIds = await PermissionService.CoursTypeQcmSettings.getTypeqcmIdsForCoursModule(
        { coursId, coursModuleType: CoursTypesQcmSettings_MODULE_TYPES.qcmSeries },
      );
      DEBUG && console.log({ coursId, typeQcmIds }); // 0 pour user test
      // Voir c'est quoi les types que l'utilisateur a le droit de voir de base
      const userTypesAllowed = await PermissionService.getAvailableTypeQcmsForUser(user);
      const typeQcmIdsAuthorized = userTypesAllowed.map(q => q.id);
      // Prend que les types qui sont dans les types autorisés parmi ceux proposé dans les settings du module du cours
      const typeQcmAllowed = typeQcmIds.filter(q => typeQcmIdsAuthorized.includes(q));
      const qcmSeriesWithTypeAllowed = await models.QcmTypeQcm.findAll({
        where: {
          typeQcmId: typeQcmAllowed,
        },
        attributes: ['qcmId'],
        raw: true,
      });
      const qcmSeriesIdsWithTypeQcmAllowed = qcmSeriesWithTypeAllowed.map(q => q.qcmId);
      const questinoIdsLinked = await QuestionsService.getQuestionsIdsFromCoursIds(coursId);
      const questionsIdsInQcmHavingAllowedTypes = await QuestionsService.getQuestionsIdsForMcq(qcmSeriesIdsWithTypeQcmAllowed);
      const questionIdsToLookFor = questionsIdsInQcmHavingAllowedTypes.filter(id => questinoIdsLinked.includes(id));
      // ID QCM
      const idsMcqLinkedToCourse = await QuestionsService.getQcmIdsForQuestionsIds(questionIdsToLookFor);
      // Récupération des qcm liés au cours parmi ceux disponibles pour le user
      const idsToLookFor = idsMcqLinkedToCourse?.filter(id => availableMcqIds?.includes(id));
      return await models.Qcm.findAll({
        where: {
          isPublished: true,
          deleted: 0,
          id_qcm: idsToLookFor,
        },
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async addTypeQcmToQuestion({ typeQcmId, questionId }, id) {
    try {
      const question = await models.Question.findByPk(questionId);
      const typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      await question.addType_qcm(typeQcm);
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
  async removeTypeQcmFromQuestion({ typeQcmId, questionId }, id) {
    try {
      const question = await models.Question.findByPk(questionId);
      const typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      await question.removeType_qcm(typeQcm);
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  async getMcqMaximumPoints(qcm, ctx) {
    try {
      const REDIS_KEY = `mcqMaximumPoints-${qcm.id_qcm}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      let questions;

      // Now all qcm have external questions, so we can remove the else part in future
      if (qcm && qcm.hasExternalQuestions) {
        const questionsQcm = await models.QuestionsQcm.findAll({
          where: {
            qcmId: qcm.id_qcm,
          },
        });
        questions = await models.Question.findAll({
          where: {
            id_question: questionsQcm?.map(q => q.questionId),
          },
          attributes: ['mcqScaleId', 'isCheckbox', 'isAnswerFreeText', 'maxPoints', 'id_question', 'type'],
        });
      } else {
        questions = await models.Question.findAll({
          where: {
            id_qcm: qcm?.id_qcm,
          },
          raw: true,
          attributes: ['mcqScaleId', 'isCheckbox', 'isAnswerFreeText', 'maxPoints', 'id_question', 'type'],
        });
      }
      let total = 0;

      for (const q of questions) {
        const scale = await QCMService.getMcqScale({ question: q });

        if (q.isAnswerFreeText) {
          /* Handles free text questions */
          total += q.maxPoints;
        } else {
          /* Handles other types of questions */
          total += scale?.rules[McqScaleRulesProperties.pointsPerQuestion] || 0;
        }
      }
      await RedisService.set(REDIS_KEY, total);
      return total;
    } catch (e) {
      console.error(e);
    }
  },

  async getParentsQuestionsSeriesForQuestion(question) {
    try {
      const questionId = question.id_question;
      const link = await models.QuestionsQcm.findAll({
        where: {
          questionId,
        },
        attributes: ['qcmId'],
        group: ['qcmId'],
      });
      const qcmIds = link.map(l => l.qcmId);
      return models.Qcm.findAll({
        where: {
          id_qcm: qcmIds,
        },
      });
    } catch (e) {
      console.error(e);
    }
  },

  /* Save qcm state when user change form */
  async saveQcmState({ input }, { me }) {
    try {
      const { qcmId, sessionId, formValues, time } = input;
      const userId = me?.id;
      let where = {
        userId,
        ...(qcmId && { qcmId }),
        ...(sessionId && { sessionId }),
      };
      let qcmState = await models.QcmSavedState.findOne({
        where,
      });
      if (qcmState) {
        await qcmState.update({
          formValues,
          time,
        });
      } else {
        await models.QcmSavedState.create({
          userId,
          qcmId,
          sessionId,
          formValues,
          time,
        });
      }

      return true;
    } catch (e) {
      console.error(e);
    }
  },
  async getQcmState({ input }, { me }) {
    try {
      const { qcmId, sessionId } = input;
      const userId = me?.id;
      let where = {
        userId,
        ...(qcmId && { qcmId }),
        ...(sessionId && { sessionId }),
      };
      return models.QcmSavedState.findOne({ where, order: [['updatedAt', 'DESC']] });
    } catch (e) {
      console.error(e);
    }
  },
  async deleteSavedStateForQcm(qcmId) {
    try {
      await models.QcmSavedState.destroy({
        where: {
          qcmId,
        },
      });
    } catch (e) {
      console.error(e);
    }
  },
  async deleteQcmState({ qcmId, sessionId, userId }) {
    try {
      let where = {
        userId,
        ...(qcmId && { qcmId }),
        ...(sessionId && { sessionId }),
      };
      const state = await models.QcmSavedState.findOne({ where });
      if (state) {
        await state.destroy();
      }
      return true;
    } catch (e) {
      console.error(e);
    }
  },
  async getQcmDetailedInformations({ id }, { models, me }) {
    try {
      // Récupération des linked Questions :
      const listOfQuestions = await QCMService.getQuestionsForQcm(id);

      // Recupération des questionsId en Array
      const questionIdArray = listOfQuestions.map(obj => obj.id_question);

      // Creation de la structure de questionEditHistoryList
      const questionEditHistoryList = questionIdArray.map((id, index) => (
        {
          question: listOfQuestions[index],
          questionEditHistory: LogService.getQuestionsUserEditor({ id }, { me, models }),
        }
      ));

      // Creation de la structure de QcmEditHistory
      const qcmEditHistoryList = await LogService.getQcmEditLogs({ qcmId: id }, { models, me });

      // Récupération du Qcm en question // La fonction est outdated et ne fait rien de l'userId
      const qcm = await QCMService.getQcmById(id, null);

      let response = {
        qcm: qcm,
        questions: questionEditHistoryList,
        qcmEditHistory: qcmEditHistoryList,
        lastestEditLog: qcmEditHistoryList[qcmEditHistoryList.length - 1],
      };

      return response;
    } catch (e) {
      console.error(e);
    }
  },


  async recalculateQcmStats(qcmId, ctx) {
    try {
      // 2789
      await QCMStatsService.updateAllScoreForMcq(qcmId);
      return true;
    } catch (e) {

    }
  },

  async linkQuestionIdWithQcmId({
                                  args: {
                                    questionIds,
                                    qcmId,
                                  },
                                  ctx,
                                }) {
    try {
      // get du models
      const models = ctx?.models ?? models;

      // Verif de si il y a bien les différents en db
      const questionsArray = await models.Question.findAll({
        where: { id_question: questionIds },
      });

      // Mise en array des ids trouvés
      const ids = questionsArray.map(question => question?.id_question);

      if (ids?.length < questionIds.length) {
        throw new Error(`Ils y a des questions demandées qui ne sont pas dans la db. (asked : ${questionIds} // found : ${ids}`);
      }

      // Verif de qcmId
      const qcm = await models.Qcm.findByPk(qcmId);
      if (!qcm) {
        throw new Error(`le qcm d'id ${qcmId} n'a pas été trouvé`);
      }


      // Check des paires pré-existantes
      const existingPairs = await models.QuestionsQcm.findAll({
        where: {
          qcmId: qcmId,
          questionId: {
            [Op.in]: ids, // Utiliser Op.in pour spécifier une recherche dans un tableau
          },
        },
      });

      // Pour afficher les paires existantes
      const existingQuestionIds = new Set(existingPairs.map(pair => pair.questionId));

      // Filtrer les ids pour ne garder que les nouveaux
      const newEntries = ids
        .filter(id => !existingQuestionIds.has(id))
        .map(questionId => ({
          qcmId,
          questionId,
          order: questionId,
        }));

      try {
        if (newEntries.length > 0) {
          await models.QuestionsQcm.bulkCreate(newEntries);
        }
      } catch (e) {
        console.error('l\'association entre Questions et QCM n\'a pas marchée');
        throw new Error('l\'association entre Questions et QCM n\'a pas marchée');
      }

      return true;

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async addLinkQuestionIdWithQcmId({
                                     args: {
                                       questionId,
                                       qcmId,
                                     },
                                     ctx,
                                   }) {
    try {
      // get du models
      const models = ctx?.models ?? models;

      // Verif de si il y a bien les différents en db
      const q = await models.Question.findByPk(questionId);
      if (!q) {
        throw new Error(`La question d'id ${questionId} n'a pas été trouvée`);
      }

      if (q?.isAiGenerationError) {
        throw new Error(`la question d'id ${questionId} est une erreur de génération par IA et ne peut pas être associée à une Série`);
      }

      // Verif de qcmId
      const qcm = await models.Qcm.findByPk(qcmId);
      if (!qcm) {
        throw new Error(`Le qcm d'id ${qcmId} n'a pas été trouvé`);
      }

      // Check des paires pré-existantes
      const existingPairs = await models.QuestionsQcm.findOne({
        where: {
          qcmId,
          questionId,
        },
      });

      if (existingPairs) {
        console.error('L\'association existe déjà');
        return true;
      }

      // Création de la nouvelle association
      await models.QuestionsQcm.create({
        qcmId,
        questionId,
        order: questionId,
      });

      return true;

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },


  async removeLinkQuestionIdWithQcmId({
                                        args: {
                                          questionId,
                                          qcmId,
                                        },
                                        ctx,
                                      }) {
    try {
      // get du models
      const models = ctx?.models ?? models;

      // Verif de si il y a bien les différents en db
      const q = await models.Question.findByPk(questionId);
      if (!q) {
        throw new Error(`la question d'id ${questionId} n'a pas été trouvé`);
      }

      // Verif de qcmId
      const qcm = await models.Qcm.findByPk(qcmId);
      if (!qcm) {
        throw new Error(`le qcm d'id ${qcmId} n'a pas été trouvé`);
      }

      const result = await models.QuestionsQcm.destroy({
        where: {
          qcmId,
          questionId,
        },
      });

      return true;

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async getQcmCorrectionStats({ id, sessionId=null, userId, groupIds = [], statId, }, ctx) {
    try {
      const {me} = ctx;
      if (userId && me?.role === ROLES.USER) {
        throw new GraphQLError('Vous n\'avez pas l\'autorisation de faire cela.');
      }
      return QCMService.getMyQCMResult(id, me.id, { sessionId, groupIds, statId, userId });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async canIDoQcm(id, ctx) {
    try {
      const qcm = await models.Qcm.findByPk(id);
      return await PermissionService.isUserAuthorizedForMcq(ctx.me, qcm);
    } catch (e) {
      console.error(e);
    }
  },

  async massDeleteSeriesByType(typeQcmIds, userId) {
    try {
      const meUser = await models.User.findByPk(userId);
      if(meUser.exostaff === false) {
        throw new Error('Vous n\'êtes pas autorisé à supprimer des séries.');
      }
      const qcmTypesQcm = await models.QcmTypeQcm.findAll({
        where: {
          typeQcmId: typeQcmIds,
        },
        raw: true,
      });
      const qcmIds = qcmTypesQcm.map(q => q.qcmId);
      if (qcmIds.length === 0) {
        throw new Error('Aucune serie trouvée pour les types spécifiés.');
      }
      const uniqueQcmIds = [...new Set(qcmIds)];
      let count = 0;
      for(const qcmId of uniqueQcmIds) {
        const deleted = await models.Qcm.update(
          { deleted: true, isPublished: false },
          { where: { id_qcm: qcmId } }
        );
        await models.DateDiffusion.destroy({ where: { qcmId } });
        if(deleted) {
          count++;
        }
      }
      return count; // Retourne le nombre de questions supprimées
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
};