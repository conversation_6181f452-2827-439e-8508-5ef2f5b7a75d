import { GraphQLError } from 'graphql';
import { combineResolvers } from 'graphql-resolvers';
import sequelize from 'sequelize';
import models from '../../models/index.js';
import { isAdmin, isAuthenticated, isSuperAdmin, isTuteurOrAdmin, ROLES } from '../authorization';
import { PostService } from '../post/post-service.js';
import { AiAnalysisService } from './ai-analysis/ai-analysis-service';
import { QcmExportService } from './qcm-export-service.js';
import { QCMGeneratorService } from './qcm-generator-service.js';
import QcmGraphService from './qcm-graph-service.js';
import { QcmImportService } from './qcm-import-service.js';
import { batchTypesFromQcm } from './qcm-loaders';
import { QCMService } from './qcm-service';
import { QCMStatsService } from './qcm-stats-service.js';
import { QuestionsService } from './questions/questions-service';
import { QcmSessionService } from './sessions/qcm-session-service.js';
import { QcmTypeService } from './type/qcm-type-service.js';

export default {
  Query: {
    exerciseModulePreview: combineResolvers(
      isAuthenticated,
      async (parent, { filter }, ctx, info) => {
        return QCMGeneratorService.revisionTips(filter, ctx, 'count', filter.module, filter?.schemaAllOrCustom, filter?.schemaVariants);
      },
    ),

    revisionTips: combineResolvers(
      isAuthenticated,
      async (parent, { filter }, ctx, info) => {
        return QCMService.revisionTips(filter, ctx);
      },
    ),

    qcm: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me }, info) => {
        return await QCMService.getQcmById(id, me.id);
      },
    ),
    canIDoQcm: combineResolvers(
      isAuthenticated,
      async (parent, { id }, ctx, info) => {
        return await QCMService.canIDoQcm(id, ctx);
      },
    ),
    qcmsInSousCategorie: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me }, info) => {
        info.cacheControl.setCacheHint({ maxAge: 5, scope: 'PUBLIC' });
        return await QCMService.getQcmsInSousCategorie(id, me.id);
      },
    ),
    searchQcms: combineResolvers(
      isAuthenticated,
      async (parent, params, { models, me }, info) => QCMService.searchQcms(params, me),
    ),
    searchQcmsV2: combineResolvers(
      isAuthenticated,
      async (parent, params, { models, me }, info) => QCMService.searchQcmsV2(params, me),
    ),
    monClassementQcm: combineResolvers(
      isAuthenticated,
      async (parent, {
        id,
        groupIds,
        userId,
      }, { me }) => QCMStatsService.getMonClassementQcm(id, me.id, false, groupIds, userId),
    ),

    monClassementPondereQcm: combineResolvers(
      isAuthenticated,
      async (parent, { id, groupIds }, { me }) => QCMStatsService.getMonClassementQcm(id, me.id, true, groupIds),
    ),

    adminSearchQcms: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { models, me }, info) => {
        return QCMService.adminSearchQcms(params, me.id);
      }),

    adminSearchExerciseSeries: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { models, me }, info) => {
        return QCMService.adminSearchQcmsV2(params, me.id);
      }),

    getGraphCorrection: combineResolvers(
      isAuthenticated,
      async (parent, params, { models, me }, info) => {
        return QCMService.getGraphCorrection(params, me.id);
      }),
    getGraphProgression: combineResolvers(
      isAuthenticated,
      async (parent, params, { models, me }, info) => {
        //info.cacheControl.setCacheHint({ maxAge: 360, scope: 'PRIVATE' });
        return QCMService.getGraphProgression(params, me);
      }),

    getPointsByCertainty: combineResolvers(
      isAuthenticated,
      async (parent, params, { models, me }, info) => {
        return QCMService.getPointsByCertainty(params, me.id);
      }),

    allTypeQcm: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx, info) => {
        const forUser = params.forUser || false;
        return QcmTypeService.getAllTypeQcm(ctx, forUser);
      },
    ),
    typeQcm: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx, info) => {
        return models.TypeQcm.findByPk(params.id);
      },
    ),
    qcmSavedState: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx, info) => {
        return QCMService.getQcmState({ input: { qcmId: params.qcmId, sessionId: params.sessionId } }, ctx);
      },
    ),
    getQcmDetailedInformation:combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me}) => {
        return QCMService.getQcmDetailedInformations({id},{me,models})
      }
    ),

    generateQcmPreview: combineResolvers(
      isAuthenticated,
      async (parent, { params }, { models, me }) => await QCMGeneratorService.generateQcmPreview(params, me.id),
    ),

    getQcmCorrectionStats: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QCMService.getQcmCorrectionStats(params, ctx)
    ),
    getAiAnalysis: combineResolvers(
      isAuthenticated,
      async (parent, {qcmId, userId, statId, sessionId, force}, ctx) => AiAnalysisService.getAnalysis(qcmId, userId, sessionId, statId, force, ctx)
    ),

  },

  Mutation: {

    testImportMapping : combineResolvers(
      isTuteurOrAdmin,
      async (parent,params,ctx,info) => await QcmImportService.testImportMapping(parent,params,ctx,info)
    ),

    /* CRUD QCM */
    createQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { me, ip }, info) => QCMService.createQcm(params, me.id, ip),
    ),
    updateQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { me, ip }, info) => QCMService.updateQcm(params, me.id, ip),
    ),
    deleteQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { me, ip }, info) => QCMService.deleteQcm(params, me.id, ip),
    ),
    saveQcmState: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx, info) => QCMService.saveQcmState(params, ctx),
    ),

    /* IMPORTS */
    importFullMcqFromJson: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { models, me, ip }) => QcmImportService.importFullMcqFromJson(params, models),
    ),
    importFullMcqFromXls: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { models, me, ip }) => QcmImportService.importMcqFromXLS(params, models),
    ),
    importMcqUserResultsFromXls: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { models, me, ip }) => QcmImportService.importMcqUserResultsFromXls(params, models),
    ),

    exportJsonToXLSDownload: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { input, name ,addUUID}, ctx) => QcmExportService.exportTableJSONToXLSDownload(input, name, addUUID,ctx),
    ),

    recalculateQcmStats: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { qcmId }, ctx) => QCMService.recalculateQcmStats(qcmId, ctx),
    ),

    importCSV: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { models, me, ip }) => await QcmImportService.importCSV(params, models),
    ),

    /* QCM GENERATOR */
    generateQcm: combineResolvers(
      isAuthenticated,
      async (parent, { params }, { models, me }) => await QCMGeneratorService.generateQcmWithParams(params, me.id),
    ),
    /* QCM GENERATOR: Advanced */
    generateQcmFromPacks: combineResolvers(
      isAuthenticated,
      async (parent, { params }, { models, me }) => await QCMGeneratorService.generateQcmWithParamsPacksAdvanced(params, me.id),
    ),

    generateSessionFromExerciseElement: combineResolvers(
      isAuthenticated,
      async (parent, { formationElementId }, ctx) => await QCMGeneratorService.generateSessionFromExerciseElement(formationElementId, ctx),
    ),

    generateSessionFromDiapoSyntheseElement: combineResolvers(
      isAuthenticated,
      async (parent, { formationElementId }, ctx) => await QCMGeneratorService.generateSessionForDiapoSynthese(formationElementId, ctx),
    ),

    corrigerQcmGenerated: combineResolvers(
      isAuthenticated,
      async (parent, params, { me }, info) => await QCMGeneratorService.corrigerQcmGenerated(params, me.id),
    ),
    /* Correction série d'exercice en liste (tout d'un coup), pour série simple, ou série dans un exam */
    corrigerQcmUtilisateur: combineResolvers(
      isAuthenticated,
      async (parent, params, { me }, info) => await QCMService.corrigerQcmUtilisateur(params, me.id),
    ),

    deleteQcmResult: combineResolvers(
      isAdmin,
      async (parent, params, { me }, info) => await QCMService.deleteQcmResult(params),
    ),

    /* Special Ops on QCM */
    duplicateQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { me }, info) => await QCMService.duplicateQcm(params, me.id),
    ),
    fusionQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { me }, info) => await QCMService.fusionQcm(params, me.id),
    ),
    updateAllGradesForMcq: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, { me, models }, info) => await QCMService.updateAllGradesForMcq(params, me.id),
    ),

    updateMyGoodAnswersSynthesis: combineResolvers(
      isAuthenticated,
      async (parent, params, { me, models }, info) => await QcmGraphService.updateGoodAnswersStatsUserSynthesisForUser(
        me.id, false),
    ),

    /* TYPE QCM */
    createTypeQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, ctx, info) => QcmTypeService.createTypeQcm(params, ctx),
    ),
    updateTypeQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, ctx, info) => QcmTypeService.updateTypeQcm(params, ctx),
    ),
    deleteTypeQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, ctx, info) => QcmTypeService.deleteTypeQcm(params, ctx),
    ),
    addTypeQcmToQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, ctx, info) => QcmTypeService.addTypeQcmToQcm(params, ctx),
    ),
    removeTypeQcmFromQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, ctx, info) => QcmTypeService.removeTypeQcmFromQcm(params, ctx),
    ),

    /* Default type on series for question */
    addDefaultTypeQuestionQcmToQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, ctx, info) => QcmTypeService.addDefaultTypeQuestionQcmToQcm(params, ctx),
    ),
    removeDefaultTypeQuestionTypeQcmFromQcm: combineResolvers(
      isTuteurOrAdmin,
      async (parent, params, ctx, info) => QcmTypeService.removeDefaultTypeQuestionTypeQcmFromQcm(params, ctx),
    ),

    addLinkQuestionIdWithQcmId:combineResolvers(
      isTuteurOrAdmin,
      async (parent,args,ctx,info) => QCMService.addLinkQuestionIdWithQcmId({args,ctx})
    ),

    removeLinkQuestionIdWithQcmId:combineResolvers(
      isTuteurOrAdmin,
      async (parent,args,ctx,info) => QCMService.removeLinkQuestionIdWithQcmId({args,ctx})
    ),

    massDeleteSeriesByType: combineResolvers(
      isSuperAdmin,
      async (parent, { typeQcmIds }, { me }, info) => {
        if (!typeQcmIds || typeQcmIds.length === 0) {
          throw new GraphQLError('No typeQcmIds provided');
        }
        return QCMService.massDeleteSeriesByType(typeQcmIds, me.id);
      },
    ),
  },

  Qcm: {
    questions: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { loaders, models }) => {
        // Now all questions are external questions
        const questionIds = await models.QuestionsQcm.findAll({
          where: {
            qcmId: parent.id_qcm,
          },
          order: [['order', 'ASC']],
          attributes: ['questionId'],
          raw: 'true',
        });
        const qIds = questionIds?.map(q => q.questionId);
        const order = (qIds?.length > 0) ? { order: sequelize.literal('FIELD(questions.id_question,' + qIds.join(',') + ')') } : {};
        const questions = await models.Question.findAll({
          where: {
            id_question: qIds,
          },
          include: [models.QuestionAnswers, { model: models.McqScale, as: 'mcqScale' }],
          ...order,
        });
        return questions;
      },
    ),

    cours: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me }) => QCMService.getCoursInQcm(parent),
    ),
    coursImpliques: combineResolvers(
      isAuthenticated,
      async (qcm, { id }, { models, me }) => QCMService.getCoursImpliedInQcmQuestions(qcm),
    ),
    ueCategoriesImpliquees: combineResolvers(
      isAuthenticated,
      async (qcm, { id }, { models, me }) => QCMService.getUECategoriesImpliedInQcmQuestions(qcm),
    ),

    UE: combineResolvers(
      isAuthenticated,
      async (qcm, { id }, { models, me, loaders }) => loaders.ueFromQcm.load(qcm.id_qcm),
    ),

    /* MCQ MY RESULT (first time or last result by session) */
    resultat: combineResolvers(
      isAuthenticated,
      async (parent, args, { models, me }, info) => {
        //info.cacheControl.setCacheHint({ maxAge: 20, scope: 'PRIVATE' })
        if (args?.userId && me?.role === ROLES.USER) {
          throw new GraphQLError('Oh, oh! Vous n\'avez pas l\'autorisation de faire cela.');
        }
        return QCMService.getMyQCMResult(parent.id_qcm, me.id, args);
      },
    ),

    resultsForUser: combineResolvers(
      isAuthenticated,
      async (parent, args, { models, me }, info) => {
        //info.cacheControl.setCacheHint({ maxAge: 20, scope: 'PRIVATE' })
        if (args?.userId && me?.role === ROLES.USER) {
          throw new GraphQLError('Oh, oh! Vous n\'avez pas l\'autorisation de faire cela.');
        }
        return QCMService.getMyQCMResults(parent.id_qcm, me.id, args);
      },
    ),


    nombreQuestions: combineResolvers(
      isAuthenticated, // DATALODAER
      (qcm, args, { loaders, models, me }, info) => {
        if (qcm && qcm.hasExternalQuestions) {
          return models.QuestionsQcm.count({
            where: {
              qcmId: qcm.id_qcm,
            },
          });
        } else {
          return loaders.nombreQuestions.load(qcm.id_qcm);
        }
        //return await QCMService.getNombreQuestionsQCM(qcm.id_qcm)
      },
    ),

    maximumPoints: combineResolvers(isAuthenticated,
      async (qcm, args, ctx, info) => {
        return QCMService.getMcqMaximumPoints(qcm, ctx);
      },
    ),

    resultatsEleves: combineResolvers(
      isTuteurOrAdmin,
      async (qcm, args, { loaders, models, me }, info) => {
        return QCMService.getAllResultsForQcm(qcm.id_qcm, args, me);
      },
    ),

    countResultatsEleves: combineResolvers(
      isAuthenticated,
      async (qcm, args, { loaders, models, me }, info) => {
        return QCMService.countResultsForQcm(qcm.id_qcm);
      },
    ),

    lastPost: combineResolvers(
      isAuthenticated,
      async (qcm, args, { loaders, models, me }, info) => {
        info.cacheControl.setCacheHint({ maxAge: 5, scope: 'PUBLIC' });
        return PostService.getLastPostForQcm(qcm.id_qcm);
      },
    ),

    postsNumber: combineResolvers(
      isAuthenticated,
      async (qcm, _, { models, me }) => {
        return PostService.getPostCountQcmAmongIds([qcm.id_qcm]);
      },
    ),

    mySession: combineResolvers(
      isAuthenticated,
      async (qcm, args, ctx) => {
        return QcmSessionService.getSessionForMcq(qcm, args, ctx);
      },
    ),

    notions: combineResolvers(
      isAuthenticated,
      async (qcm, args, ctx) => {
        return QCMService.getNotionsForQcm(qcm, args, ctx);
      },
    ),

    type: combineResolvers(
      isAuthenticated,
      async (qcm, args, ctx) => {
        return ctx.loaders.batchTypesFromQcm.load(qcm.id_qcm);
      },
    ),
    defaultQuestionsType: combineResolvers(
      isAuthenticated,
      async (qcm, args, ctx) => {
        return QcmTypeService.getDefaultQuestionsTypesQcm(qcm, args, ctx);
      },
    ),

  },

  TypeQcm: {
    // TODO use loader (easy)
    groupes: combineResolvers(
      isAuthenticated,
      async (typeQcm, args, ctx) => {
        return QcmTypeService.getGroupesInTypeQcm(typeQcm, args, ctx);
      },
    ),
  },

  QcmResult: {
    // TODO use loader (easy)
    user: combineResolvers(
      isAuthenticated,
      async (qcm, args, ctx) => models.User.findByPk(qcm.id_utilisateur),
    ),
    // TODO use loader (easy)
    session: combineResolvers(
      isAuthenticated,
      async (result, args, ctx) => models.QcmSession.findByPk(result.qcmSessionId),
    ),
  },
};
