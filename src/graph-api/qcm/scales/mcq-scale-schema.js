import gql from 'graphql-tag'

export default gql`
    extend type Query {
        "All MCQ scales / Tous les barêmes"
        mcqScales: [McqScale]
        
        "Petite querie utilisée dans l'import de QCU/QCM par AI"
        mcqScalesQcmAndQcu:[McqScale]
        
        defaultUEsForMcqScale(id: ID!): [UE]
      
        "Retourne un descripteurs sommaire de toutes les scales : nom, type d'exercice ,leur nombre d'exercices liés  "
        mcqScalesDescription:[McqScaleDescription]
      
        "Pour un mcqScaleType, retourne les scales associées"
        getQuestionTypeMcqScales(questionType:String!):[McqScale]
        
        "From questionId, get all scale matching the questionType of the submited question"
        getAllScalesAvailableForQuestion(questionId:ID!):[McqScale]

        "Query massChangeScaleForQuestion => Permet d'avoir le nombre de questions qui seront modifées par la mutation de même nom"
        queryMassChangesScaleForQuestions(queryMassChangesScaleForQuestionsInput:MassChangesScaleForQuestionsInput!): Int
        
        getDefaultScaleForExerciseTypeAndCourseId(mcqScaleQuestionType:String!,coursIds:[ID]!):McqScale
    }
    
    extend type Mutation {
        # CRUD MCQ scale
        createMcqScale(mcqScale: McqScaleInput!): McqScale!
        updateMcqScale(id: ID!, mcqScale: McqScaleInput!): Boolean
        deleteMcqScale(id: ID!): Boolean!
      
        # Change default scale for a questionType
        modifyDefaultMcqScaleForQuestionType(mcqScaleId:ID!,questionType:String!):Boolean

        "Add default UE to mcq scale"
        addUeToMcqScale(mcqScaleId: ID!, ueId: ID!): Boolean
        "Remove default UE from mcq scale"
        removeUeFromMcqScale(mcqScaleId: ID!, ueId: ID!): Boolean

        "MassUpdate des questions (soit depuis les ID Directement, soit depuis les cours), selon un mappingScale qui associe un questionType à un nouveau scale à associer"
        mutationMassChangesScaleForQuestions(mutationMassChangesScaleForQuestionsInput:MassChangesScaleForQuestionsInput!): Int
    }
    
    input MassChangesScaleForQuestionsInput{
      selectedCoursIds:[ID]!
      mappingScale:[MappingScale!]!
      selectedQuestionIds:[ID]!
    }
    
    input MappingScale{
      questionType:String!
      scaleId:ID!
    }
    
    type McqScaleDescription{
      questionType:String
      defaultName:String
      count:Int
    }

    input McqScaleInput {
        "Name of the rule"
        name: String
        "The JSON rules"
        rules: McqScaleRulesInput
        "Is it default scale"
        isDefault: Boolean
        "Type dynamic or manual"
        type: String
        "MCQ or SCQ"
        questionType: String
        "Points obtained when nothing is checked, if null, ignored"
        pointsObtainedWhenNothingChecked: Float
        ueIds: [ID]
    }
    type McqScale {
        id: ID!
        "Name of the rule"
        name: String
        "The JSON rules"
        rules: JSON
        "Is it default scale"
        isDefault: Boolean
        "Type dynamic or manual"
        type: String
        "MCQ or SCQ"
        questionType: String
        "Points obtained when nothing is checked, if null, ignored"
        pointsObtainedWhenNothingChecked: Float
        "UEs associated to this scale"
        ues: [UE]
      
        author:User
      
        logs(logOperationFilter:[String],numberOfLastLogs:Int!=5):[Log]
      
        "Optional : number of linked exercises"
        numberOfLinkedExercises:Int
        createdAt: Date
        updatedAt: Date
    }
    input McqScaleRulesInput {
        minimumGrade: Float
        numberOfErrors: Int
        pointsPerQuestion: Float
        "Default or custom notation"
        notation: String
        "Points lost for default notation"
        pointsLostPerError: [Float]

        "Points lost for custom notation: true answer is checked (true)"
        pointsLostPerErrorIfTrueAnswerIsCheckedFalse: [Float]
        "'fixed' or 'variableByTotalAnswers' or 'variableByTrueAnswers'"
        pointsLostPerErrorIfTrueAnswerIsCheckedFalseSettings: String # "fixed" or "variableByTotalAnswers" or "variableByTrueAnswers"
        "Points lost for custom notation: false answer is checked (false)"
        pointsLostPerErrorIfFalseAnswerIsCheckedTrue: [Float]
        pointsLostPerErrorIfFalseAnswerIsCheckedTrueSettings: String
        "Points lost for custom notation: false answer is undefined (null or undefined)"
        pointsLostPerErrorIfFalseAnswerIsUndefined: [Float]
        pointsLostPerErrorIfFalseAnswerIsUndefinedSettings: String
        "Points lost for custom notation: true answer is undefined (null or undefined)"
        pointsLostPerErrorIfTrueAnswerIsUndefined: [Float]
        pointsLostPerErrorIfTrueAnswerIsUndefinedSettings: String
        flashcardBareme:JSON
    }

`
