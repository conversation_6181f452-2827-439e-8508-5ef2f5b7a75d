import models from '../../../models/index';
import { McqScaleQuestionType, McqScaleQuestionTypeForGPTMap } from '../../../models/qcm/mcq_scale';
import { isAdmin, ROLES } from '../../authorization';
import { PermissionService } from '../../permission/permission-service';
import { QuestionAnswerHistoryHelper } from '../answers/QuestionAnswerHistoryHelper';
import { QCMService, QuestionAnswerType } from '../qcm-service';
import { QCMStatsService } from '../qcm-stats-service';
import { QuestionsService } from '../questions/questions-service';
import { SYSTEM_PROMPT } from './prompts';
import OpenAI from 'openai';

// Set to true for production
const SEND_TO_OPEN_AI = true;
const DEBUG_LOG = true; // set to false production

export const AiAnalysisService = {

  async getOpenAiKeyFromConfig(config) {
    const value = JSON.parse(config.value);
    const integrationId = value?.integrationId;
    const integrationConfig = await models.Config.findByPk(integrationId);
    const parsedIntegrationConfig = JSON.parse(integrationConfig.value);
    return parsedIntegrationConfig?.accessToken;
  },

  async getOpenAiModelFromConfig(config) {
    const value = JSON.parse(config.value);
    return value?.models?.[0]?.model;
  },

  /**
   * Recupère la config dispo pour correction IA pour un userId
   * @param userId
   * @returns {Promise<any|null>}
   */
  async getCorrectionAiConfigForUser(userId) {
    const groupes = await PermissionService.Groupe.getUserGroups({ id: userId });
    const userGroupesIds = groupes?.map(g => g.id);

    const possiblesConfigsForAiCorrection = await models.Config.findAll({
      where: {
        key: 'CHAT_GPT_QCM_SETTINGS',
      },
    });
    const configsForAiCorrection = possiblesConfigsForAiCorrection?.filter(config => {
      const parsedValue = JSON.parse(config.value);
      return parsedValue?.type === 'correction';
    });

    if (configsForAiCorrection?.length === 0) {
      return null;
    }

    // pour chaque config trouver si on a un groupe
    for (const config of configsForAiCorrection) {
      const chatGptId = config.id;
      const userGroupesGpts = await models.UserGroupChatGpt.findAll({
        where: {
          chatGptId,
        }, attributes: ['groupeId'],
      });
      const allowedGroups = userGroupesGpts?.map(ug => ug.groupeId);
      // Si user dans groupe autorisé hop on retourne la config
      if (allowedGroups.some(g => userGroupesIds.includes(g))) {
        return config;
      }
    }
  },

  /**
   * Appelé par le front pour récupérer l'analyse d'une serie
   * @param qcmId
   * @param userId
   * @param sessionId
   * @param statId
   * @param force
   * @param ctx
   * @returns {Promise<any|null>}
   */
  async getAnalysis(qcmId, userId, sessionId, statId = null, force = false, ctx) {
    try {
      if(force && ![ROLES.ADMIN, ROLES.SUB_ADMIN].includes(ctx?.me?.role)) {
        throw new Error('Vous n\'avez pas les droits pour forcer l\'analyse');
      }

      const userIdToUse = userId || ctx?.me?.id;
      const qcm = await ctx.models.Qcm.findByPk(qcmId, {
        attributes: ['enableAiAnalysis'],
      });
      if (qcm?.enableAiAnalysis) {
        // check si analyse déjà existante
        let where = {
          id_qcm: qcmId,
          id_utilisateur: userIdToUse,
        };
        // It's either stat id or session id
        if (statId) {
          where = { ...where, id: statId };
        } else if (sessionId) {
          where = { ...where, qcmSessionId: sessionId };
        }
        const statResult = await models.QcmStats.findOne({
          where, attributes: ['id', 'aiAnalysis'],
        });

        // Si aps de résultat on retourne null
        if(!statResult) {
          return null;
        }

        // Si analyse existe déjà, et qu'on force pas, on la retourne
        if (statResult && statResult.aiAnalysis && !force) {
          return statResult.aiAnalysis;
        } else {
          // Get config IA associée à la correction
          const config = await this.getCorrectionAiConfigForUser(userIdToUse);

          if(!config) {
            return null;
          }

          const openAiKey = await this.getOpenAiKeyFromConfig(config);
          const openAiModel = await this.getOpenAiModelFromConfig(config); // Prend le premier model de la liste (normalemnt 4o)

          const jsonResult = await this.doAnalyseMcqWithUserAnswers(qcmId, statId, sessionId, userIdToUse, ctx, openAiKey, openAiModel);
          if (jsonResult) {
            statResult.aiAnalysis = jsonResult;
            await statResult.save();
            return jsonResult;
          }
        }
      }
      return null;

    } catch (e) {
      console.error(e);
    }
  },

  // appeler seulement si qcm.enableAnalysis = true
  async doAnalyseMcqWithUserAnswers(mcqId, statId = null, sessionId = null, userId, ctx, openAiKey, openAiModel = 'gpt-4o') {
    try {
      if (!openAiModel || !openAiKey) {
        console.error('OpenAI Key or Model not provided');
        return;
      }
      if (!mcqId) {
        console.error('mcqId not provided');
        return;
      }
      let questions = await QCMService.getQuestionsForQcm(mcqId); // questions with answers

      let promptExercicesAndUserChoices = '';
      // pour chaque question ?
      let questionNumber = 1;
      for (const question of questions) {
        let doProcessQuestion = true;
        let questionType, questionTypeString;
        // Determine question type
        DEBUG_LOG && console.log({ qType: question?.type });
        if (question.isCheckbox && !question.type) {
          // Try to find default MCQ scale
          questionType = McqScaleQuestionType.MultipleChoice;
        } else if (!question.isCheckbox && !question.type) { // Checkbox Question
          questionType = McqScaleQuestionType.UniqueChoice;
        } else if ([QuestionAnswerType.ALPHANUMERICAL, QuestionAnswerType.NUMERICAL].includes(question.type)) {
          questionType = McqScaleQuestionType.Alphanumerical;
        } else {
          questionType = McqScaleQuestionType.UniqueChoice;
        }
        if (question.isAnswerFreeText) {
          questionType = McqScaleQuestionType.FreeText;
          doProcessQuestion = false;
        }
        questionTypeString = McqScaleQuestionTypeForGPTMap[questionType];
        if (QuestionAnswerType.FILL_IN_THE_BLANKS === question.type) {
          questionTypeString = 'Texte à trous';
        }
        if (QuestionAnswerType.SCHEMA_POINT_AND_CLICK === question.type) {
          questionTypeString = 'Schéma(Point&Click)';
        }
        if (QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS === question.type) {
          questionTypeString = 'Schéma à compléter';
        }
        if (QuestionAnswerType.FLASHCARD === question.type) {
          questionTypeString = 'Flashcard';
        }
        if (QuestionAnswerType.REORDER_ELEMENTS === question.type) {
          questionTypeString = 'Remise en ordre';
        }

        if (doProcessQuestion) {
          const coursesLinked = await QuestionsService.getCoursLinkedForQuestion(question);
          const array_linkedCoursesString = Array.from(
            new Set(coursesLinked?.map(cours => `${cours.name || ''} ${cours?.text || ''}`)),
          ).join(', ') || '';

          const coursLinkedString = array_linkedCoursesString?.length > 0 ? '[Cours liés: ${array_linkedCoursesString}]' : '';
          ////////////////////////////////////////////////////////////////////////////////////////////////////////////
          promptExercicesAndUserChoices += `Exercice ${questionNumber}) [${questionTypeString}] ${coursLinkedString} ${question?.question || ''}\n`;

          let answerHistory = await QCMStatsService.getQuestionAnswerHistory(question, { statId, sessionId }, userId);
          const questionAnswers = question.question_answers;

          let index = 0;
          for (const answer of questionAnswers) {
            const letter = String.fromCharCode(65 + index);
            let correctionValueString = '';
            let explanation = '';
            if ([QuestionAnswerType.ALPHANUMERICAL, QuestionAnswerType.NUMERICAL].includes(question?.type)) {
              //value: '["A","B","C","D"]',
              if (answer?.text) {
                const array = JSON.parse(answer?.text);
                correctionValueString = array.join('');
              }
            } else {
              correctionValueString = answer.isTrue ? 'Réponse Vraie' : 'Réponse Fausse';
            }

            explanation = `\nCorrection du professeur : ${correctionValueString} : ${answer?.explanation || ''}\n`;

            const AnswerHistoryHelper = new QuestionAnswerHistoryHelper(answerHistory, question?.type, answer.id);
            let userAnswer = AnswerHistoryHelper.toString();
            const userAnswerString = `Réponse de l'élève pour ${letter}: ${userAnswer}`;

            promptExercicesAndUserChoices += `  ${letter}) ${answer.text} : ${explanation}\n${userAnswerString}\n`;
            index++;
          }

          // Exercice sans answer
          const isFillInTheBlankExercise = question.type === QuestionAnswerType.FILL_IN_THE_BLANKS;
          const isSchemaExercise = question.type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK;
          const isSchemaFillInLegendExercise = question.type === QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS;
          const isFlashcard = question.type === QuestionAnswerType.FLASHCARD;

          const jsonAnswers = answerHistory?.jsonAnswers;
          const ptsObtained = answerHistory?.pointsObtained;
          if (isFillInTheBlankExercise) {
            const jsonAnswersString = JSON.stringify(jsonAnswers);
            promptExercicesAndUserChoices += `${question?.settings?.text} \nRéponses de l'élève: ${jsonAnswersString} (${ptsObtained} points obtenus)\n`;
            /*
              jsonAnswers: [
                { id: 1, value: 'bleu', isCorrect: true },
                { id: 2, value: 'soyeux', isCorrect: true }
              ]
            */
          }

          // SCHEMAS
          if (isSchemaExercise || isSchemaFillInLegendExercise) {
            const legendsIdsToCheck = question?.settings?.checkedLegends;
            const correspondingSchema = await models.SchemaLibrary.findByPk(question?.schemaLibraryId);
            const legends = correspondingSchema?.legends;
            promptExercicesAndUserChoices += `(nom du schéma: ${correspondingSchema?.name || ''})\n`;
            let goodLegendsNames = [];
            let badLegendsNames = [];
            let unfilledLegendsNames = [];
            for (const legendId of legendsIdsToCheck) {
              const legendAnswer = jsonAnswers?.find(ja => ja?.id === legendId);
              const correspondingLegendObj = legends?.find(l => l.id === legendId);
              if (Array.isArray(jsonAnswers) && legendAnswer) {
                if (isSchemaExercise) {
                  // Utilisateur a répondu pour cette légende
                  if (legendAnswer?.isInLegend) {
                    goodLegendsNames.push(correspondingLegendObj?.name);
                  } else {
                    badLegendsNames.push(correspondingLegendObj?.name);
                  }
                } else if (isSchemaFillInLegendExercise) {
                  if (legendAnswer?.isCorrect) {
                    goodLegendsNames.push(correspondingLegendObj?.name);
                  } else {
                    badLegendsNames.push(correspondingLegendObj?.name);
                  }
                }
              } else {
                // Utilisateur a pas répondu alors qu'il aurait du
                if (isSchemaExercise) {
                  unfilledLegendsNames.push(correspondingLegendObj?.name);
                } else if (isSchemaFillInLegendExercise) {
                  unfilledLegendsNames.push(correspondingLegendObj?.name);
                }
              }
            }

            if (isSchemaExercise) {
              if (goodLegendsNames?.length > 0) {
                promptExercicesAndUserChoices += `L'élève a bien placé les légendes : ${goodLegendsNames.join(',')}\n`;
              }
              if (badLegendsNames?.length > 0) {
                promptExercicesAndUserChoices += `L'élève n'a pas bien placé les légendes : ${badLegendsNames.join(',')}\n`;
              }
              if (unfilledLegendsNames?.length > 0) {
                promptExercicesAndUserChoices += `L'élève n'a pas répondu pour les légendes : ${unfilledLegendsNames.join(',')}\n`;
              }
            } else if (isSchemaFillInLegendExercise) {
              if (goodLegendsNames?.length > 0) {
                promptExercicesAndUserChoices += `L'élève a bien reconnu les légendes : ${goodLegendsNames.join(',')}\n`;
              }
              if (badLegendsNames?.length > 0) {
                promptExercicesAndUserChoices += `L'élève n'a pas bien reconnu les légendes : ${badLegendsNames.join(',')}\n`;
              }
              if (unfilledLegendsNames?.length > 0) {
                promptExercicesAndUserChoices += `L'élève n'a pas répondu pour les légendes : ${unfilledLegendsNames.join(',')}\n`;
              }
            }
          }

          // FLASHCARDS
          if (isFlashcard) {
            console.log({ answerHistory });
            // Taux de certitude
            const certainty = answerHistory?.certainty;
            /*
            export const DICO_CERTAINTY_LABELS = {
              RANDOM: { value: '0', label: 'ICheckedRandom',labelFlashcard:"ICheckedRandomFlashcard" },
              NOT_SURE: { value: '1', label: 'NotSure',labelFlashcard:"NotSureFlashcard" },
              MODERATLY_SURE: { value: '2', label: 'ModeratlySure',labelFlashcard:"ModeratlySureFlashcard" },
              SURE: { value: '3', label: 'Sure',labelFlashcard:"SureFlashcard" }
            };
            */
            const flashCardCertaintyMap = {
              0: 'Au hasard',
              1: 'Pas sûr',
              2: 'Modérément sûr',
              3: 'Certain',
            };
            if (jsonAnswers?.flashcardResponseType === 'BINARY') {
              promptExercicesAndUserChoices += `Avec un niveau de certitude "${flashCardCertaintyMap[certainty]}" l'élève a répondu ${jsonAnswers?.autoEvaluation === 'TRUE' ? 'correctement' : 'incorrectement'} à la flashcard,  \n`;
            }
            if (jsonAnswers?.flashcardResponseType === 'CONTINUOUS') {
              promptExercicesAndUserChoices += `Avec un niveau de certitude "${flashCardCertaintyMap[certainty]}" l'élève a évalué sa réponse comme "${jsonAnswers?.autoEvaluation}" \n`;
            }
          }

          // REORDER ELEMENTS
          const isReorderElements = question.type === QuestionAnswerType.REORDER_ELEMENTS;
          if (isReorderElements) {
            const correctOrder = question?.settings?.correctOrder || [];
            const userOrder = jsonAnswers?.userOrder || [];
            const score = answerHistory?.pointsObtained || 0;

            // Add correct order information
            if (correctOrder.length > 0) {
              promptExercicesAndUserChoices += `Ordre correct attendu :\n`;
              correctOrder.forEach((element, index) => {
                // Extract content from element object (element.content contains the string)
                const elementText = typeof element === 'object' ? element.content : element;
                promptExercicesAndUserChoices += `  ${index + 1}. ${elementText}\n`;
              });
            }

            // Add user's order
            if (userOrder.length > 0) {
              promptExercicesAndUserChoices += `Ordre proposé par l'élève :\n`;
              userOrder.forEach((element, index) => {
                // Extract content from element object (element.content contains the string)
                const elementText = typeof element === 'object' ? element.content : element;
                promptExercicesAndUserChoices += `  ${index + 1}. ${elementText}\n`;
              });
            }

            // Add scoring information
            promptExercicesAndUserChoices += `Score obtenu : ${score} points\n`;

            // Add error analysis if available
            if (jsonAnswers?.errors && Array.isArray(jsonAnswers.errors)) {
              if (jsonAnswers.errors.length > 0) {
                promptExercicesAndUserChoices += `Erreurs de positionnement :\n`;
                jsonAnswers.errors.forEach((error, index) => {
                  promptExercicesAndUserChoices += `  - Élément "${error.userElement}" placé en position ${error.position + 1} au lieu de ${error.correctPosition !== null ? error.correctPosition + 1 : 'position inconnue'}\n`;
                });
              } else {
                promptExercicesAndUserChoices += `Aucune erreur de positionnement - ordre parfait !\n`;
              }
            }
          }

          questionNumber++;
        }
      }

      DEBUG_LOG && console.log({ promptExercicesAndUserChoices });

      let userPrompt = `
        ${promptExercicesAndUserChoices}
      `;
      let systemPrompt = SYSTEM_PROMPT;

      // Ask openai chat completion with json answer mode
      if (SEND_TO_OPEN_AI) {
        const openai = new OpenAI({ apiKey: openAiKey });
        const response = await openai.chat.completions.create({
          messages: [
            {
              role: 'system',
              content: systemPrompt,
            },
            {
              role: 'user',
              content: userPrompt,
            },
          ],
          model: openAiModel,
          response_format: { type: 'json_object' },
        });

        const gptAnswer = JSON.parse(response.choices[0].message.content || '{}');
        console.log({ gptAnswer });
        return gptAnswer;
      } else {

      }
    } catch (e) {
      console.error(e);
    }
  },


};