// TODO move questions related to qcm to qcm-service.js
import fs from 'fs'
import { GraphQLError } from 'graphql';
import Sequelize from 'sequelize'
import { isDev } from '../../../index.js';
import models from '../../../models/index.js';
import { LOG_OPERATIONS } from '../../../models/log/log.js'
import { McqScaleQuestionType, McqScaleRulesProperties } from '../../../models/qcm/mcq_scale.js';
import { RedisService } from '../../../service/redis-service.js';
import { UPLOAD_FOLDER_MAP } from '../../../util/utils.js'
import { ConfigUpgrades } from '../../config/config-upgrades';
import { CoursService } from '../../cours/cours-service';
import LogService from '../../log/log-service.js'
import { PermissionService } from '../../permission/permission-service.js';
import { AUTO_SCHEMA_CREATION_TYPE, SchemaLibraryService } from '../../schema-library/schema-library-service';
import { defaultSchemaText, QCMService } from '../qcm-service.js';
import { McqScaleService } from '../scales/mcq-scale-service.js';
const Op = Sequelize.Op;
const pdfParse = require('pdf-parse');
const {PDFDocument:pdfLib} = require('pdf-lib');
import { AI_CREATION_TYPE } from '../../chat-gpt/chat-gpt-service.js'

export const QuestionsService = {

  async isQuestionDoneByMe(question, me) {
    const questionStats = await models.QcmStatsQuestion.findOne({
      where: {
        id_question: question.id_question,
        id_utilisateur: me?.id,
      },
      raw: true,
    });
    return !!questionStats;
  },

  async addCoursToQuestion({ coursId, questionId }, userId) {
    try {
      let coursToAdd = coursId;
      const question = await models.Question.findByPk(questionId);
      const cours = await models.Cours.findByPk(coursToAdd);
      if (!question || !cours) {
        throw new GraphQLError('La question ou le cours ne sont pas valides');
      }
      const REDIS_KEY = `getCoursIdsLinkedForQuestionId-${questionId}`;
      await RedisService.delete(REDIS_KEY);

      // Cas spécial pour cours importé: on ajoute/enlève tous les cours importés
      const allCoursIdsToAdd = await CoursService.getAllImportedCoursIdsFromCours(cours);

      for(const coursIdToAdd of allCoursIdsToAdd) {
        const questionCoursMap = await models.QuestionCours.findOne({
          where: {
            questionId,
            coursId: coursIdToAdd,
          },
        });
        if (questionCoursMap) {
          console.log(`Cours déjà ajouté à question (addCours${coursIdToAdd}ToQuestion${questionId})`);
        } else {
          await models.QuestionCours.create({
            questionId,
            coursId: coursIdToAdd,
          });
        }
      }

      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async removeCoursFromQuestion({ coursId, questionId }, userId) {
    try {
      const question = await models.Question.findByPk(questionId);
      const cours = await models.Cours.findByPk(coursId);
      if (!question || !cours) {
        throw new GraphQLError('La question ou le cours ne sont pas valides');
      }
      const REDIS_KEY = `getCoursIdsLinkedForQuestionId-${questionId}`;
      await RedisService.delete(REDIS_KEY);

      const allCoursIdsRemove = await CoursService.getAllImportedCoursIdsFromCours(cours);
      for(const coursIdToRemove of allCoursIdsRemove) {
        await models.QuestionCours.destroy({
          where: {
            questionId,
            coursId: coursIdToRemove,
          },
        });
      }

      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async updateCoursFromQuestion({coursIdArray,questionId},userId){
    /* Fonction qui remplace les cours associés à une question */
    try{
      const question = await models.Question.findByPk(questionId);
      if (!question) {
        throw new GraphQLError(`La question d'id : ${questionId} n'est pas valide`);
      }
      const coursIds = coursIdArray.map(c => parseInt(c));
      questionId = parseInt(questionId);

      const linkedCoursIdArray = await models.QuestionCours.findAll({
        where: { questionId },
        raw: true,
      }).then(nodes => nodes.map(value => value.coursId));

      let coursLinkToAdd = [];
      let coursLinkToRemove = linkedCoursIdArray.filter(elem => !coursIds.includes(elem));

      // Vérification et ajout des cours valides
      for (const coursId of coursIds) {
        const cours = await models.Cours.findByPk(coursId, { raw: true });
        if (!cours) {
          throw new GraphQLError(`Le cours d'id : ${coursId} n'est pas valide`);
        }
        const coursIdsToAdd = await CoursService.getAllImportedCoursIdsFromCours(cours);
        coursLinkToAdd = [...new Set([...coursLinkToAdd, ...coursIdsToAdd])];
        // Si le cours original ou ses importés existent déjà, on les retire des cours à supprimer
        coursLinkToRemove = coursLinkToRemove.filter(id => !coursIdsToAdd.includes(id));
      }

      // Supprimer les cours à retirer
      await models.QuestionCours.destroy({
        where: {
          questionId: questionId,
          coursId: coursLinkToRemove,
        },
      });
      const REDIS_KEY = `getCoursIdsLinkedForQuestionId-${questionId}`;
      await RedisService.delete(REDIS_KEY);
      // Vérifier quels cours sont déjà liés et n'insérer que les nouveaux
      const alreadyLinkedCourses = await models.QuestionCours.findAll({
        where: {
          questionId: questionId,
          coursId: coursLinkToAdd,
        },
        raw: true,
      }).then(nodes => nodes.map(value => value.coursId));
      const newEntriesToAdd = coursLinkToAdd.filter(id => !alreadyLinkedCourses.includes(id));
      const entriesToCreate = newEntriesToAdd.map(coursId => ({ questionId, coursId }));
      if (entriesToCreate?.length > 0) {
        await models.QuestionCours.bulkCreate(entriesToCreate, { individualHooks: true });
      }
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Question.Update.action,
        foreignIds: {
          questionId,
        },
        logData: { coursIdArray: JSON.stringify(coursIds), text: "replace coursId" },
        models,
        userId,
      });

      return true;
    } catch(e){
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  /* Ajoute exercice dans une série */
  async linkQuestionToSerie({ questionId, qcmId, order }) {
    if (!order) {
      order = questionId;
    }
    if (!await models.QuestionsQcm.findOne({
      where: {
        qcmId,
        questionId,
      },
    })) {
      await models.QuestionsQcm.create(
        {
          qcmId,
          questionId,
          order,
        },
      );
    }
    return true;
  },

  /**
   * Get all questions linked to a cours
   * @param question
   * @returns {Promise<Model[]|any[]>}
   */
  async getCoursLinkedForQuestion(question) {
    try {
      const REDIS_KEY = `getCoursIdsLinkedForQuestionId-${question?.id_question}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      let coursIds
      if (cachedValue) {
        coursIds = cachedValue;
      } else {
        const qc = await models.QuestionCours.findAll({
          where: {
            questionId: question.id_question,
          },
          raw: true,
        });
        coursIds = qc.map(q => q.coursId);
      }
      await RedisService.set(REDIS_KEY, coursIds);

      return await models.Cours.findAll({
        where: {
          id: coursIds,
          deleted: false,
        },
      })
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  // Bulk multiple questions
  async getCoursLinkedForQuestionsMap(questions) {
    const questionIds = questions.map(q => q.id_question);
    const qcLinks = await models.QuestionCours.findAll({
      where: {
        questionId: questionIds,
      },
      raw: true,
    });
    const coursIds = qcLinks.map(q => q.coursId);
    const coursList = await models.Cours.findAll({
      where: {
        id: coursIds,
        deleted: false,
      },
      raw: true,
    });
    // mapping coursId → cours
    const coursMap = Object.fromEntries(coursList.map(c => [c.id, c]));
    // final result: Map<questionId, cours[]>
    const questionCoursMap = new Map();
    for (const link of qcLinks) {
      if (!questionCoursMap.has(link.questionId)) {
        questionCoursMap.set(link.questionId, []);
      }
      const cours = coursMap[link.coursId];
      if (cours) {
        questionCoursMap.get(link.questionId).push(cours);
      }
    }
    return questionCoursMap;
  },

  async getCoursLinkedRawForQuestionFastAndMinimal(question) {
    try {
      const qc = await models.QuestionCours.findAll({
        where: {
          questionId: question.id_question,
        },
        raw: true,
      });
      const coursIds = qc.map(q => q.coursId);
      return models.Cours.findAll({
        where: {
          id: coursIds,
          deleted: false,
        },
        attributes: ['id', 'uecategoryId', 'ueId', 'targetCoursId', 'formationId', 'name', 'name_en', 'name_es', 'name_it', 'name_de', 'text', 'text_en', 'text_es', 'text_it', 'text_de'],
        raw: true,
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async getQuestionsIdsForMcq(mcqId) {
    const externalQuestions = await models.QuestionsQcm.findAll({
      where: {
        qcmId: mcqId,
      },
      attributes: ['questionId'],
      raw: true
    });
    return [...new Set(await externalQuestions.map(q => q.questionId))];
  },

  async getQcmIdsForQuestionsIds(questionIds) {
    const externalQuestions = await models.QuestionsQcm.findAll({
      where: {
        questionId: questionIds,
      },
      attributes: ['qcmId'],
      raw: true
    });
    return [...new Set(await externalQuestions.map(q => q.qcmId))];
  },

  /* Single or multiple question */
  async getCoursIdsLinkedForQuestion(questionId) {
    try {
      const qc = await models.QuestionCours.findAll({
        where: {
          questionId: questionId,
        },
        raw: true,
        attributes: ['coursId'],
      });
      return qc.map(q => q.coursId);
    } catch (e) {
      console.error(e);
    }
  },

  // Exercices publiés et non-publiés
  async getQuestionsIdsFromCoursIds(coursIds) {
    let enableCache = true;
    const REDIS_KEY = `getQuestionsIdsFromCoursIds-${coursIds}`;
    if(enableCache) {
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
    }
    const qc = await models.QuestionCours.findAll({
      where: {
        coursId: coursIds,
      },
      raw: true,
      attributes: ['questionId'],
    });
    const questionIds = [...new Set(qc.map(q => q.questionId))];
    if(enableCache) {
      await RedisService.set(REDIS_KEY, questionIds);
    }
    return questionIds;
  },

  // Exercices publiés et sans erreur ia
  async countPublishedQuestionsIdsFromCoursIds(coursIds) {
    let enableCache = false;
    const REDIS_KEY = `getQuestionsIdsPublishedFromCoursIds-${coursIds}`;
    if(enableCache) {
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
    }
    const qc = await models.QuestionCours.findAll({
      where: {
        coursId: coursIds,
      },
      raw: true,
      attributes: ['questionId'],
    });
    const questionIds = [...new Set(qc.map(q => q.questionId))];
    const countPublishedQuestions = await models.Question.count({
      where: {
        id_question: questionIds,
        isPublished: true,
        isAiGenerationError: false,
      }
    });
    if(enableCache) {
      await RedisService.set(REDIS_KEY, countPublishedQuestions);
    }
    return countPublishedQuestions;
  },


  async publishAllQuestionsForMcq(mcqId) {
    const questions = await QCMService.getQuestionsForQcm(mcqId);
    for (const question of questions) {
      await question.update({ isPublished: true });
    }
    return true;
  },
  async unPublishAllQuestionsForMcq(mcqId) {
    const questions = await QCMService.getQuestionsForQcm(mcqId);
    for (const question of questions) {
      await question.update({ isPublished: false });
    }
    return true;
  },

  async getAllQuestionsIdsLinked() {
    const qc = await models.QuestionCours.findAll({
      raw: true,
      attributes: ['questionId'],
    });
    return [...new Set(await qc.map(q => q.questionId))];
  },

  async filterQuestionsAnswerIdGivenCoursIdAndQcmTypes(qcmTypeQcmIds,coursIds) {
    /* Fonction qui permet d'extraire les questionAnswersId pour ( coursId Union typeQcmId )  */
    try {
      let includeArray = []

      if (qcmTypeQcmIds && qcmTypeQcmIds.length >= 0) {
        includeArray.push(
          {
            model: models.TypeQcm,
            as: 'type_qcms',
            where: { id: qcmTypeQcmIds },
            attributes: [],
            required: true // On veut une inclusion
          }
        )
      }

      if (coursIds && coursIds.length >= 0) {
        includeArray.push(
          {
            model: models.Cours,
            as: 'cours',
            where: { id: coursIds },
            attributes: [],
            required: true // On veut une inclusion
          }
        )
      }

      const qaObj = await models.QuestionAnswers.findAll({
        include: [{
          model: models.Question,
          as: 'question',
          include: includeArray,
          required: true, // On veut une inclusion
          attributes: []
        }],
        attributes: ["id"],
        raw: true
      });
      const array = qaObj.map(qa => qa.id)
      //.then(value => value.map(qa => qa.id))

      return array
    } catch(e){
      console.error("error dans filterQuestionsAnswerIdGivenCoursIdAndQcmTypes :",e)
      return[]
    }

  },

  /*
  * selectedCoursIds: [ID], input: QuestionInput
  * */
  async massChangesQuestionsDisplaySettings({ selectedCoursIds = [], displaySetting, changeDisplay = false }, id) {
    try {
      const questionsIds = await this.getQuestionsIdsFromCoursIds(selectedCoursIds);

      const questions = await models.Question.findAll({
        where: {
          id_question: questionsIds,
        }
      });

      for (let question of questions) {
        if(changeDisplay) {
          // Change seulement certains types
          if(question.type === null || question.type === 'TRUE_OR_FALSE_OR_UNDEFINED') {
            question.type = displaySetting?.type;
          }
        }
        await question.save();
      }

      return questions.length;

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async isQuestionEditableByMe(id, ctx) {
    try {
      // Règle: si il a accès au type ou est super admin
      return PermissionService.canEditExerciseByType(id, ctx.me);
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async migrateIsQuestionLinkedToCourses(){
    try{
      const questions = await models.Question.findAll({
        include:[{
          model:models.Cours,
          attributes:["id"]
        }]
      });


      const qArray=questions


      for (const question of qArray){
        const temp = question.cours && question.cours.length > 0;
        question.isLinkedToCourses = temp;
        await question.save();
      }

    }catch(e){
      console.error(e)
      throw new Error(e)
    }
  },

  async validateAiGeneration({id,ctx}){
    try{
      const models=ctx?.models

      const q =await models.Question.findByPk(id)

      // Check si c'est modifié par AI
      const isAiGenerated=q?.isAiGenerated

      // Check si y a pas eu d'erreur
      if (q?.isAiGenerationError){throw new Error("Une erreur de génération par IA ne peut pas être validée")}

      // Check de si on a bien les conditions nécessaire : si la question a été générée par AI et si on a le bool à changer présent dans la question
      if (!isAiGenerated) {throw new Error(`la question d'id ${id} n'a pas été généré par AI. On ne change donc pas sa validation AI`)}
      if (q?.aiGenerationHasBeenValidated === undefined){throw new Error(`la question d'id ${id} n'a pas d'attribut 'aiGenerationHasBeenValidated'`)}

      q.aiGenerationHasBeenValidated=true

      await q.save()
      return true

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  async rejectAiGeneration({id,ctx}){
    try{
      const models=ctx?.models

      const q =await models.Question.findByPk(id)

      // Check si c'est modifié par AI
      const isAiGenerated=q?.isAiGenerated

      // Check de si on a bien les conditions nécessaire : si la question a été générée par AI et si on a le bool à changer présent dans la question
      if (!isAiGenerated) {throw new Error(`la question d'id ${id} n'a pas été généré par AI. On ne change donc pas sa validation AI`)}
      if (q?.aiGenerationHasBeenValidated === undefined){throw new Error(`la question d'id ${id} n'a pas d'attribut 'aiGenerationHasBeenValidated'`)}

      q.aiGenerationHasBeenValidated=false

      await q.save()
      return true

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  async cronJobDeleteRejectedAiGeneratedQuestions(){
    /* Cette fonction est censée supprimer les questions générées par AI qui ne sont pas validées.
    * Cependant, c'est à mon avis trop instable à mettre en place : Il faut supprimer en cascade tout ce qui est lié aux questions :
    *   -> logs
    *   -> QuestionsAnswers
    *     -> Les images des questions_Answers
    *   -> Question_type
    *
    * Le soucis, c'est qu'on a pas vraiment une table de jointure bidirectionnelle entre question_answers et images. Autrement dit, on est pas certain
    * qu'une image est UNIQUEMENT liée à une seule question answers. Et donc, si on fait pas attention, on peux supprimer des images utilisées par d'autre ressources.
    *
    * Également, comme il s'agit d'une environnement variable et en évolution, si il y a une création futur link avec les questions, il faudra updated ici et c'est pas ouf imo.
    *  */

    try {
      // Determination de la date Il y a X jours ago
      const daysLaps = 1
      const dateCutoff = new Date()
      dateCutoff.setUTCDate(dateCutoff.getUTCDate()-daysLaps)

      // Recuperation des records pour les générées sans erreures
      const records=await models.Question.findAll({
        where : {
          isAiGenerated:true,
          [Op.or]:[
            {aiGenerationHasBeenValidated:false},
            {isAiGenerationError:true}
          ],
          date_modif:{[Op.lte]:dateCutoff}
        }
      })

      const promessArray=[]
      for (const record of records) {

        const tempId=record?.id_question
        const promiseDeletion=QCMService.deleteQuestion({ id:tempId }, null, null)
        promessArray.push(promiseDeletion)
      }

      await Promise.all(promessArray)

      console.log(`Suppression de ${records.length} de questions générées par AI non validées ou en erreur`)
      return true
    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  async questionToExempleGPT({args,ctx}){
    try{
      const models=ctx?.models
      const idArray=args?.idArray
      const exerciseFormat=args?.exerciseFormat

      // Test de si le type d'exercice est supporté
      if (exerciseFormat===McqScaleQuestionType.FLASHCARD){
        console.warn("questionToExempleGPT, l'utilisation de flashcard comme exemple n'est pas implémenté : EXIT ")
        return null
      }
      const supportedExercisesTypeArray=[McqScaleQuestionType?.UniqueChoice,McqScaleQuestionType?.MultipleChoice,McqScaleQuestionType?.FillInTheBlanks]
      if (!supportedExercisesTypeArray.includes(exerciseFormat)){throw new Error(`Error, exerciseFormat not supported (${exerciseFormat}),    supported : ${supportedExercisesTypeArray}`)}

      let formatage
      let exercises

      if (exerciseFormat===McqScaleQuestionType?.FillInTheBlanks){
        exercises = await models?.Question.findAll({
          attributes:["question","settings"],
          where:{id_question:idArray}
        })

        formatage=(object)=>{return object}
        exercises?.forEach(node => {delete node?.settings?.caseSensitive ; delete node?.settings?.acceptTypos})
      }
      else if (exerciseFormat===McqScaleQuestionType?.UniqueChoice || exerciseFormat===McqScaleQuestionType?.MultipleChoice){
        // Recup des exercices
        exercises=await models.Question.findAll({
          attributes:["question"],
          where:{
            id_question:idArray
          },
          include: [
            {
              attributes: ["text","explanation","isTrue"],
              model: models.QuestionAnswers,
              required: false,
            }
          ],
        })
        formatage=(object)=>{
          // Fonction interne qui formate l'objet database 'Question' au format que l'on veut pour la stringification

          const qa = object?.question_answers?.map((qa, index) => {
            const result = {
              text: qa?.text || "",
              explanation: qa?.explanation || "",
              isTrue: qa?.isTrue || null,
            };
            return result;
          });

          return {
            question:object?.question || "",
            question_answers:qa,
          }
        }
      }

      const removeNewlines = (key, value) => {
        // Fonction de remplacement qui retourne les '//n' en ' '
        if (typeof value === 'string') {return value.replace(/\n/g, ' ');}
        return value;
      };

      const arrayOfString = JSON.parse(JSON.stringify(exercises.map(q => formatage(q)), removeNewlines));

      /*  Voici un exemple de comment utiliser la sortie (dans 'additionnalExempleStringArray')
      *   const specialCharMarker="°°°"
      *   const startString=`Voici d'autre exemples entourés des marqueurs ${specialCharMarker} pour te donner une idée : `
      *   const joinString=" et "
      *   additionnalExemple = startString + additionnalExempleStringArray.map(item => `${specialCharMarker}${JSON.stringify(item)}${specialCharMarker}`).join(joinString);
      * */


      return arrayOfString
    }catch(e){
      const newError=`dans questionToExempleGPT ${e}`
      console.error(newError)
      throw new GraphQLError(newError)
    }

  },

  async migrationForCreationTypeFallback(){
    /*
      Par default, la colonne 'creationType' est a human, et comme il y a déjà eu des questions crées/importées il faut update
      la valeur des colonnées de questions générées par AI de façon rétro-active.
      Cette migration permet de mettre à jour la colonne 'creationType' des questions générées par AI à "gptCreation'

     */

    try{
      await models.Question.update(
        { creationType: AI_CREATION_TYPE.GPT_CREATION }, // Valeurs à mettre à jour
        {
          where: { isAiGenerated: true }  // Condition pour sélectionner les enregistrements
        }
      );
    }catch(e){
      console.error(e)
    }
  },

  async getAuthorizedLinkedCoursForMe({question,me}){
    try {
      let coursLinkedForQuestion = await QuestionsService.getCoursLinkedForQuestion(question);
      return PermissionService.getLinkedCoursesToShowForUser(coursLinkedForQuestion, me);
    }catch(e){
      console.error(e)
    }
  },

  async getQuestionColor({args,ctx}){
    try {
      const questionId=args.id
      const models=ctx.models

      const questionInstance = await models.Question.findByPk(questionId);
      if (!questionInstance) {
        throw new GraphQLError(`Question avec l'ID ${questionId} introuvable.`);
      }

      // Récupération des cours associés à la question
      const coursArray = await questionInstance.getCours();

      // Récupération des UE parent pour chaque cours
      const parentUeArray = await Promise.all(
        coursArray.map(async (cours) => {
          return await CoursService.getParentUEForCours(cours);
        })
      );

      const colorArray=parentUeArray.map((node)=>node?.color)

      return colorArray; // si tu veux retourner les UE
    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  /**
   * Génère exercice schéma entrainement (quasi-automatique) à partir de schéma de librairie
   * @param schemaLibraryId
   * @param ctx
   * @returns {Promise<boolean>}
   */
  async generateAutoFillInLegendsExercise({ schemaLibraryId }, ctx) {
    try {
      // regarder si existe dejà
      const existingExercise = await models.Question.findOne({
        where: {
          schemaLibraryId: schemaLibraryId,
          creationType: AUTO_SCHEMA_CREATION_TYPE.SCHEMA,
          type: 'SCHEMA_FILL_IN_LEGENDS',
        },
      });
      if(existingExercise) {
        throw new Error('L\'exercice existe déjà');
      }
      const schema = await ctx.models.SchemaLibrary.findByPk(schemaLibraryId);
      if(!schema) {
        throw new Error('Le schéma n\'existe pas');
      }

      const allLegendsIds = schema?.legends?.map(l => l.id);
      let mcqScale = await models.McqScale.findOne({
        where: {
          questionType: 'schemaFillInLegends',
          type: 'dynamic', // Default schema scale
        }
      });
      if(!mcqScale) {
        throw new Error('Le barême pour ce type d\'exercice n\'existe pas');
      }

      let correspondingQuestionToCreate = {
        question: defaultSchemaText,
        type: 'SCHEMA_FILL_IN_LEGENDS',
        isPublished: true,
        isCheckbox: false,
        deletable: false, // non supprimable
        mcqScaleId: mcqScale.id,
        schemaLibraryId: schema.id,
        authorId: schema.authorId,
        date_creation: schema.createdAt,
        date_modif: schema.updatedAt,
        creationType: AUTO_SCHEMA_CREATION_TYPE.SCHEMA,
        settings: {
          checkedLegends: allLegendsIds, // à vérifier
        },
      };
      // Créer question item
      const newQuestion = await models.Question.create(correspondingQuestionToCreate);

      // lier les mêmes cours
      await SchemaLibraryService.autoAddCoursLinkedSchemaLibraryAndSchemaExercice(newQuestion, schema);
      // Lier les mêmes types
      const typesSchema = await models.SchemaLibraryTypeQcm.findAll({
        where: {
          schemaLibraryId: schema.id
        },
        raw: true,
        attributes: ['typeQcmId']
      });
      const allTypesIdsToAdd = typesSchema.map((t) => t.typeQcmId);
      for( const typeIdToAdd of allTypesIdsToAdd) {
        await models.QuestionTypeQcm.create({
          questionId: newQuestion.id_question,
          typeQcmId: typeIdToAdd,
        })
      }
      return newQuestion;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async massDeleteExercisesByType(typeQcmIds, userId) {
    try {
      const meUser = await models.User.findByPk(userId);
      if(meUser.exostaff === false) {
        throw new Error('Vous n\'êtes pas autorisé à supprimer des exercices.');
      }
      const questionsTypeQcm = await models.QuestionTypeQcm.findAll({
        where: {
          typeQcmId: typeQcmIds,
        },
        raw: true,
      });
      const questionIds = questionsTypeQcm.map(q => q.questionId);
      if (questionIds.length === 0) {
        throw new Error('Aucune question trouvée pour les types spécifiés.');
      }
      const uniqueQuestionIds = [...new Set(questionIds)];
      let count = 0;
      for(const questionId of uniqueQuestionIds) {
        // Supprimer la question sans throw d'error pour question non deletable
        const deleted = await QCMService.deleteQuestion({ id: questionId }, userId, null, false);
        if(deleted) {
          count++;
        }
      }
      return count; // Retourne le nombre de questions supprimées
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
};