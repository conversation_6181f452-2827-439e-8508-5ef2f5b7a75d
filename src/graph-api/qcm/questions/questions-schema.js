import gql from 'graphql-tag'

export default gql`
    extend type Query {
        "Get exercise by ID"
        question(id: ID!): Question
        
        "(Module accès rapide cours) Get exercises preview by predefined cours-type involving Course coursId (used for annales)"
        getQuestionsAnnaleForCours(coursId: ID!): CoursAnnalesResponse

        allTypeQuestion: [TypeQuestion]
        
        "Search exercises by filter"
        searchQuestions(filter: QuestionSearchFilter): SearchResponse
        
        "Return if exercise is editable by me or not"
        isQuestionEditableByMe(id: ID!): Boolean
      
        queryMultiplesQuestions(id:[ID]!):[Question]
      
        "Identifie les couleurs associées aux UE liées aux questions."
        getQuestionColor(id:ID!):[String]
    }
    extend type Mutation {
        # CRUD Questions
        createQuestion(question: QuestionInput!): Question!
        updateQuestion(id: ID!, question: QuestionInput!): Boolean
        changeQuestionOrder(id: ID!, qcmId: ID! order: Int!): Boolean
        deleteQuestion(id: ID!): Boolean!

        "Generate session questions annale for cours"
        generateExerciseSessionForCourseModule(coursId: ID, notionId: ID, includeAlreadyDone: Boolean, module: String, mode: String, numberOfExercises: Int, schemaAllOrCustom: String, schemaVariants: [String]): QcmGeneratorResponse

        "Update or delete image of a question"
        updateImageQcm(type: String, file: Upload, action: String, id: ID): String

        "Update question settings"
        updateQuestionSettings(id: ID!, settings: JSON!): Boolean

        "Change imported qcm question order"
        changeOrderImportedQcm(qcmId: ID, questionId: ID, targetQuestionId: ID): Boolean

        # CRUD type Questions
        createTypeQuestion(typeQuestion: TypeQuestionInput!): TypeQuestion!
        updateTypeQuestion(id: ID!, typeQuestion: TypeQuestionInput!): Boolean
        deleteTypeQuestion(id: ID!): Boolean!
        
        # Type questions
        addTypeQuestionToQuestion(typeQuestionId: ID!, questionId: ID!): TypeQuestion!
        removeTypeQuestionFromQuestion(typeQuestionId: ID!, questionId: ID!): TypeQuestion!

        # Type qcm (for permissions)
        addTypeQcmToQuestion(typeQcmId: ID!, questionId: ID!): Boolean
        removeTypeQcmFromQuestion(typeQcmId: ID!, questionId: ID!): Boolean
        
        # Special Ops Questions
        "Duplicate exercise and answers"
        duplicateQuestion(id: ID!, parentQcmId: ID): Question
        "Change exercise categories"
        massChangesQuestionsCategories(categoryId: ID!, coursId: ID!): String
        
        "Mass change exercise-course link"
        massChangesQuestionsCours(selectedCoursId: ID!, targetCoursId: ID!): String
      
        massChangesQuestionsDisplaySettings(selectedCoursIds: [ID], changeDisplay: Boolean,displaySetting: DisplaySetting): Int

        importQuestionWithAI(input: ImportQuestionWithAIInput): ImportQuestionWithAIResponse
        
        "Mass change classes for questions"
        changeCoursAllQuestion(id_qcm: ID!, coursId: ID!): Boolean
        "Change evaluate certainty all questions"
        changeEvaluateCertaintyAllQuestion(id_qcm: ID!, toggle: Boolean!): Boolean
        "Mass change notions for questions using keyword"
        changeNotionsQuestionByKeyword(notionsIds: [ID], keyword: String, operation: String, shouldAddToLinkedClass: Boolean): Int

        # Dynamic questions
        "Add exercise to exercise serie"
        addQuestionToQcm(qcmId: ID!, questionId: ID!): Boolean
        "Remove exercise from exercise serie"
        removeQuestionFromQcm(qcmId: ID!, questionId: ID!): Boolean

        # Cours questions
        "Add coursId to exercise"
        addCoursToQuestion(coursId: ID!, questionId: ID!): Boolean
        "Remove coursId from exercise"
        removeCoursFromQuestion(coursId: ID!, questionId: ID!): Boolean
        "Replace coursIdArray for exercise"
        updateCoursFromQuestion(coursIdArray:[ID]!,questionId:ID!):Boolean
        
        # QUESTION HIERARCHY
        "Add parent exercise"
        addQuestionParent(questionId: ID!, parentQuestionId: ID!): Boolean
        "Add child exercice"
        addQuestionChildren(questionId: ID!, childrenQuestionId: ID!): Boolean
        removeQuestionParent(questionId: ID!, parentQuestionId: ID!): Boolean
        removeQuestionChildren(questionId: ID!, childrenQuestionId: ID!): Boolean
      
        "Fonction qui pour une question générée par AI (isAiGenerated : true) switch le bool 'aiGenerationHasBeenValidated' à true"
        validateAiGeneration(id:ID!):Boolean
  
        "Fonction qui pour une question générée par AI (isAiGenerated : true) switch le bool 'aiGenerationHasBeenValidated' à false"
        rejectAiGeneration(id:ID!):Boolean
        
        "Génère exercice auto fill in legends pour entrainement"
        generateAutoFillInLegendsExercise(schemaLibraryId: ID): Question
        
        "Delete exercices by types (superadmin exostaff only)"
        massDeleteExercisesByType(typeQcmIds: [ID]): Int
    }

    input DisplaySetting{
      type:String
    }
    
    extend type Subscription {
      "La souscription qui retourne un index + une questionId , mais qui filtre sur le frontendToken défini"
      generateAiQuestions(frontendToken:String!):SubscriptionOutput
    }

    input ImportWithAiPdfData{
      dataType:String!
      file:Upload!
    }

    input ImportWithAiRawTextData{
      dataType:String!
      text:String!
    }

    input ImportWithAiPictureData{
      dataType:String!
      pictureFile:Upload!
    }
    
    
    "Type de retour de la subscription"
    type SubscriptionOutput{
      arrayIndex:Int # l'index (numéro de la question)
      questionId:Int # la questionId générée
      eventType:String! # le type d'event depuis le fichier 'question-subscription.js'
      errorMessage:String # un message d'erreur à afficher
    }
    
    input ImportQuestionWithAIInput {
        type: String
        prompt: String
        model: String

        chatGptIntegrationId: ID
        
        mode: String
        "For mode 1"
        subjectWithCorrection: String
        "For mode 2"
        subjectWithoutCorrection: String
        correction: String
        qcmId: ID
    }
    type ImportQuestionWithAIResponse {
        questions: [Question]
        
        inputPrompt: String
        tokenUsage: String
        aiAnswer: String
        
        error: String
    }

    input QuestionSearchFilter {
        offset: Int
        limit: Int
        name: String
        id: ID
        inputSetting: String
        isPublished: Boolean
        dateCreationBegin: Date
        dateCreationEnd: Date
      
        dateModifBegin:Date
        dateModifEnd:Date
        
        typeIds: [ID]
        linkCoursIds:[ID]!
        
        "Les types d'exercices que l'on veut avoir : ['Texte libre','QCU','QCM','Alphanumérique','Numérique', 'Texte à trous'] "
        formatExercices:[String]
      
        "Comme linkCoursIds / WithoutLInkedCours/ LinkedToDeletedCourses sont mutuellement exclusif, cette string permet de savoir quel filter choisir : cours, coursless, deletedcours"
        coursPointer:String
      
        "Les créateurs des exercices"
        userIds:[ID]
      
        "Indique si on effectue ou pas la filtration sur le nombre de séries linkées"
        seriesFiltration:Boolean
      
        "Le signe de comparaison à utiliser si seriesFiltration est True"
        seriesComparisonSign:String
      
        "Le chiffre pour la comparaison"
        seriesComparisonNumber:Int
    }
    
    type SearchResponse {
        count: Int
        questions: [Question]
    }
    
    "Question answers history"
    type QcmStatsQuestion {
        id_statistique_question: ID
        "MCQ question ID"
        id_question: ID
        "User ID"
        id_utilisateur: ID
        "MCQ session id"
        qcmSessionId: ID
        "User choices, will not be used in the future"
        answersData: [ID]
        "User choices when special exercise like free text or alphanumerical, usually an array"
        valueData: JSON
        "Score obtained for question"
        pointsObtained: Float
        "Weighed score for question"
        ponderatedPointsObtained: Float
        qcmStatsQuestion: [StatsQuestionAnswer]
        "Schema answer for question"
        jsonAnswers: JSON
      
        "certainty input"
        certainty:Int
    }
    
    type StatsQuestionAnswer {
        id: ID
        statsQuestionId: ID
        answerId: ID
        value: String
        isGood: Boolean
    }

    "MCQ Question"
    type Question {
        "Question ID"
        id_question: ID
        "MCQ ID"
        id_qcm: ID
        "Question order in current MCQ"
        order: Int
        "Question itself"
        question: String
        question_en: String
        question_it: String
        question_de: String
        question_es: String

        "If exercise is deletable or not"
        deletable: Boolean
        
        "Calculated difficulty of the question"
        calculatedDifficulty: Float
        "Admin defined difficulty of the question"
        definedDifficulty: Float
        
        "Unused old field"
        name: String
        
        "legacy TODO remove after mobile app upgrade (use linkedCours instead)"
        linkCoursId: ID
        
        authorId: ID
        annee: Int
        annale: Boolean
        "Checboxes if true, radio buttons if false"
        isCheckbox: Boolean
        "Free text"
        isAnswerFreeText: Boolean
        "Choice in long list (select)"
        isAnswerUniqueChoiceInList: Boolean
        "Multiple choices in long list (select)"
        isAnswerMultipleChoiceInList: Boolean
        # new way
        answers: [Answer]
        notions: [Notion]
        answerHistory(sessionId: ID, userId: ID, statId: ID): QcmStatsQuestion
        doneByMe: Boolean
        url_image_q: String
        date_creation: String
        date_modif: String
        explications: String
        id_sous_categorie: ID

        sousCategorie: UECategory

        url_image_explication: String
        "MCQ scale associated with the question (barême question)"
        mcqScale: McqScale
        successPercent: JSON
        linkedCours: [Cours]

        "Notion auto add"
        autoAddNotions: Boolean
        "Evaluate certainty or not"
        evaluateCertainty: Boolean
        "Type(s) de question"
        typeQuestion: [TypeQuestion]

        "Question Number In Mcq (needed only when querying question alone)"
        questionNumberInMcq: Int
        
        "First parent exercise serie found"
        parentQcm: Qcm
        
        "All parent exercise series using this exercise"
        parentQcms: [Qcm]

        "Alphanumerical type: can be alphanumerical or numerical or null"
        type: String
        
        parentsQuestions: [Question]
        childrensQuestions: [Question]

        "Mcq types"
        types: [TypeQcm]
        maxPoints: Float
        isPublished: Boolean
        allowComments: Boolean
        
        "IsLinkedToCourses"
        isLinkedToCourses:Boolean
      
        isAiGenerated:Boolean
        isAiGenerationError:Boolean
        aiGenerationHasBeenValidated:Boolean

        "Linked schema ID"
        schemaLibraryId: ID
        "Settings (optional, for schemas)"
        settings: JSON

        "Posts (comments, for exercises type Schema & fill the blanks)"
        posts: [Post]
        
        "Comment la question a - t - elle été créée (par AI ? Par humain, etc...) "
        creationType:String
        
        "Si généré par importation de QCM, le mathpixie ID qui a servi à la génération"
        mathpixieId:ID
    }
    
    type TypeQuestion {
        id: ID
        name: String
    }
    input TypeQuestionInput {
        name: String
    }

    "MCQ Question input"
    input QuestionInput {
        "MCQ ID"
        id_qcm: ID
        "Question text"
        question: String
        question_en: String
        question_it: String
        question_de: String
        question_es: String
        "Order number in MCQ"
        order: Int
        "Image url"
        url_image_q: String
        "Explanation image url"
        url_image_explication: String
        
        "If exercise is deletable or not"
        deletable: Boolean

        id_sous_categorie: ID
        
        "Linked course ID"
        linkCoursId: ID
        
        "user ID who created the question"
        authorId: ID

        date_creation: Date
        date_modif: Date

        "Explanation text - LEGACY unused field"
        explications: String
        
        "Explanation Image"
        imageExplication: Upload
        imageQuestion: Upload 

        "Scale ID for grade calculation"
        mcqScaleId: ID

        "Notion auto add"
        autoAddNotions: Boolean
        "Checboxes if true, radio buttons if false"
        isCheckbox: Boolean
        "Free text answer"
        isAnswerFreeText: Boolean
        "Choice in long list (select)"
        isAnswerUniqueChoiceInList: Boolean
        "Multiple choices in long list (select)"
        isAnswerMultipleChoiceInList: Boolean

        "Calculated difficulty of the question"
        calculatedDifficulty: Float
        "Admin defined difficulty of the question"
        definedDifficulty: Float
        "Alphanumerical type: can be alphanumerical or numerical or null"
        type: String

        "Evaluate certainty or not"
        evaluateCertainty: Boolean
        maxPoints: Float
        isPublished: Boolean
        allowComments: Boolean
        
        "Indicate if isLinkedToCourses"
        isLinkedToCourses:Boolean

        "Schema library ID"
        schemaLibraryId: ID
        "Settings (optional, for schemas)"
        settings: JSON
    }
    
    "Course annale training module response for preview"
    type CoursAnnalesResponse {
        "legacy to remove"
        questions: [Question]
        "Number of exercises found"
        questionsCount: Int
        countUndoneExercises: Int
        cours: Cours
        questionIds: [ID]
        
    }

`
