import { combineResolvers } from 'graphql-resolvers'
import { isAuthenticated } from '../../authorization'
import { ExamService } from '../../exam/exam-service.js'
import { QcmSessionService } from './qcm-session-service.js'

'use strict'

export default {
  Query: {
    sessionsForMcq: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.getSessionsForMcq(params, params, ctx),
    ),
    sessionForMcqExam: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.getSessionForMcqExam(params, params, ctx),
    ),
    sessionQcm: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.sessionQcm(params, ctx),
    ),
    getCurrentQuestionInSession: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.getCurrentQuestionInSession(params, ctx),
    ),

    sessionsHistory: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.getSessionsHistory(params, ctx),
    ),

    sessionsForElementId: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.getSessionsForElementId(params, ctx),
    ),
  },

  Mutation: {
    startOrResumeMcqSession: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.startOrResumeMcqSession(params, ctx),
    ),
    startOrResumeMcqSessionExam: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.startOrResumeMcqSessionExam(params, ctx),
    ),
    correctQuestionInSession: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.correctQuestionInSession(params, ctx),
    ),
    sessionSectionChange: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.sessionSectionChange(params, ctx),
    ),

    finishTrainingInSession: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.finishTrainingInSession(params, ctx),
    ),

    ignoreExerciseInSession: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.ignoreExerciseInSession(params, ctx),
    ),

    finishSession: combineResolvers(
      isAuthenticated,
      async (parent, params, ctx) => QcmSessionService.finishSession(params, ctx),
    ),
  },

  QcmSession: {
    exam: combineResolvers(
      isAuthenticated,
      async (session, params, ctx) => ExamService.getExamFromQcmSession(session, ctx),
    ),
    smartMcqScore: combineResolvers(
      isAuthenticated,
      async (session, params, ctx) => QcmSessionService.getSmartMcqScore(session, ctx),
    ),

    questionsDone: combineResolvers(
      isAuthenticated,
      async (session, params, ctx) => QcmSessionService.getQuestionsDone(session, ctx),
    ),

    questionsToDo: combineResolvers(
      isAuthenticated,
      async (session, params, ctx) => QcmSessionService.getQuestionsToDo(session, ctx),
    ),

    result: combineResolvers(
      isAuthenticated,
      async (session, params, ctx) => QcmSessionService.getQcmSessionResult(session, ctx),
    ),

    maxPoints: combineResolvers(
      isAuthenticated,
      async (session, params, ctx) => QcmSessionService.getMaxPointsForSession(session, ctx),
    ),
  }
}
