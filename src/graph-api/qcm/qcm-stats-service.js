import { GraphQLError } from 'graphql';
import moment from 'moment';
import Sequelize from 'sequelize';
import { isDev } from '../../index.js';
import models from '../../models';
import { consoleDebugObject } from '../../util/utils.js';
import { CoursService } from '../cours/cours-service.js';
import { GroupeService } from '../groupe/groupe-service.js';
import { EXOQUALIZE } from '../helpers.js';
import { PermissionService } from '../permission/permission-service.js';
import { UserViewHistoryService } from '../user/user-view-history-service.js';
import { QCMService } from './qcm-service.js';
import { QuestionsService } from './questions/questions-service.js';
import { QcmTypeService } from './type/qcm-type-service.js';

const { Op } = Sequelize;

'use strict';

// TODO be externalised in QCM GLOBAL CONFIG
const MOYENNE_GENERALE_SUR = 20;

function quantile(array, percentile) {
  array.sort((a, b) => a - b);
  let index = percentile / 100 * (array.length - 1);
  let result, fraction;
  if (Math.floor(index) === index) {
    result = array[index];
  } else {
    let i = Math.floor(index);
    fraction = index - i;
    result = array[i] + (array[i + 1] - array[i]) * fraction;
  }
  return result;
}

export const QCMStatsService = {

  /**
   * Returns object containing all other user grades and userId's ranking (Classement QCM correction)
   * @param {integer} id_qcm - mcq id
   * @param {integer} userId - user id
   * @param {boolean} ponderated - if using ponderated grade or not
   * @param groupIds - array of group ids (optional) - if empty will return all groups grades
   * @param targetUserId
   * @return {object}
   */
  async getMonClassementQcm(id_qcm, userId, ponderated = false, groupIds = [], targetUserId = null) {
    try {
      const userIdToTarget = targetUserId || userId;
      const gradeAttribute = ponderated ? 'ponderatedGrade' : 'note';
      const qcm = await models.Qcm.findByPk(id_qcm);

      // Construire la clause WHERE
      const where = {
        id_qcm,
      };

      //if (!qcm.isFullscreen) {
        where.isFirstTime = true;
      //}

      // Gestion des groupes
      if (Array.isArray(groupIds) && groupIds.length > 0) {
        const specificUserIdsToFetch = await GroupeService.getUserIdsInGroupIdsFast(groupIds);
        // Assurez-vous que specificUserIdsToFetch est non vide
        if (specificUserIdsToFetch.length > 0) {
          where.id_utilisateur = { [Op.in]: specificUserIdsToFetch };
        } else {
          // Si aucun utilisateur n'est trouvé dans les groupes, retourner des résultats vides
          return {
            notes: [],
            monClassement: null,
            total: 0,
            Q1: null,
            Q2: null,
            Q3: null,
            notesParEffectif: [],
          };
        }
      }

      /* QUERY */
      const toutesNotesQcm = await models.QcmStats.findAll({
        where,
        order: [['date', 'ASC']], // Trier par createdAt croissant pour les premières tentatives en premier
        attributes: [gradeAttribute, 'id_utilisateur', 'date'], // Sélectionner uniquement les attributs nécessaires
      });

      if (toutesNotesQcm.length === 0) {
        return {
          notes: [],
          monClassement: null,
          total: 0,
          Q1: null,
          Q2: null,
          Q3: null,
          notesParEffectif: [],
        };
      }

      // Étape 1 : Filtrer pour ne garder que la première tentative de chaque utilisateur
      const premierAttemptMap = new Map(); // Clé: id_utilisateur, Valeur: objet QcmStats

      toutesNotesQcm.forEach(entry => {
        if (!premierAttemptMap.has(entry.id_utilisateur)) {
          premierAttemptMap.set(entry.id_utilisateur, entry);
        }
      });

      const filteredNotesQcm = Array.from(premierAttemptMap.values());

      // Étape 2 : Trier les notes filtrées par grade décroissant
      filteredNotesQcm.sort((a, b) => b[gradeAttribute] - a[gradeAttribute]);

      // Étape 3 : Calcul des effectifs par note
      const counts = filteredNotesQcm.reduce((acc, entry) => {
        const note = parseFloat(entry[gradeAttribute]);
        acc[note] = (acc[note] || 0) + 1;
        return acc;
      }, {});

      const notesParEffectif = Object.keys(counts)
        .map(key => ({
          note: parseFloat(key),
          effectif: counts[key],
        }))
        .sort((a, b) => b.note - a.note); // Trier par note décroissante

      // Étape 4 : Calcul du classement en utilisant notesParEffectif
      let currentRank = 1;
      let previousNote = null;
      const noteToRankMap = {};

      for (const entry of notesParEffectif) {
        const note = entry.note;
        if (note !== previousNote) {
          noteToRankMap[note] = currentRank;
        }
        previousNote = note;
        currentRank += entry.effectif;
      }

      // Étape 5 : Trouver le classement de l'utilisateur
      const userEntry = filteredNotesQcm.find(entry => parseInt(entry.id_utilisateur) === parseInt(userIdToTarget));
      const maNote = userEntry ? parseFloat(userEntry[gradeAttribute]) : null;
      const monClassement = maNote !== null ? noteToRankMap[maNote] : null;

      // Étape 6 : Calculer Q1, Q2, Q3 (percentiles)
      const simpleNotesArray = filteredNotesQcm.map(n => parseFloat(n[gradeAttribute]));

      const getPercentile = (arr, percentile) => {
        if (arr.length === 0) return null;
        const sorted = [...arr].sort((a, b) => a - b); // Trier par ordre croissant pour les percentiles
        const index = Math.ceil((percentile / 100) * sorted.length) - 1;
        return sorted[index] !== undefined ? sorted[index] : null;
      };

      const Q1 = getPercentile(simpleNotesArray, 25);
      const Q2 = getPercentile(simpleNotesArray, 50);
      const Q3 = getPercentile(simpleNotesArray, 75);

      return {
        notes: filteredNotesQcm,
        monClassement,
        total: filteredNotesQcm.length,
        Q1,
        Q2,
        Q3,
        notesParEffectif,
      };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /**
   * Returns a user's answer history for one question
   * @param {models.Question} question
   * @param {object} args - sessionId, userId
   * @param {integer} userId - the requester
   * @param shouldFetchResultsOfEveryone
   * @return {Object} - models.QcmStatsQuestion
   */
  getQuestionAnswerHistory: async (question, args, userId) => {
    try {
      /* Determine request */
      let where = {
        id_question: question.id_question,
      };
      if (args?.sessionId) {
        // From session
        where = { ...where, qcmSessionId: args?.sessionId };
      } else {
        where = { ...where, qcmSessionId: null };
      }
      if (args?.userId) {
        // Target specific user id
        where = { ...where, id_utilisateur: args.userId };
      } else {
        // User who made the request
        where = { ...where, id_utilisateur: userId };
      }
      if (args?.statId) {
        where = { ...where, statId: args.statId };
      }
      /* Do request and treat data */
      let stats = await models.QcmStatsQuestion.findOne({
        where,
        order: [['createdAt', 'DESC']],
        include: [{
          model: models.StatsQuestionAnswers,
          as: 'qcmStatsQuestion', // answers data
        }],
      });
      const stats2 = JSON.parse(JSON.stringify(stats));
      if (stats2 && !stats2.answersData) {
        stats2.answersData = [];
      }
      if (stats2) {
        // Support frontend array of answer IDs in answersData (value non null ou false)
        // Pour ancien système garder seulement les valeurs différentes de false / 0 (différentes de répondues false)
        stats2.answersData = stats2?.qcmStatsQuestion?.filter(s => s?.value !== '0')?.map(s => (s?.answerId) || s) || [];
        stats2.valueData = JSON.stringify(stats2?.qcmStatsQuestion?.map(s => s?.value));
      }
      return stats2;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },


  /**
   * Returns all user answer history for one question
   * @param {models.Question} question
   * @param examSessionId
   * @param userIds
   * @return {Object} - models.QcmStatsQuestion
   */
  getEveryoneQuestionAnswerHistories: async (question, examSessionId = null, userIds = []) => {
    try {
      /* Determine request */
      let where = {
        id_question: question.id_question,
      };
      if (userIds.length > 0) {
        where = { ...where, id_utilisateur: userIds };
      }
      // TODO check for results in exam question serie only (parameter)
      /* Do request and treat data */
      const allStats = await models.QcmStatsQuestion.findAll({
        where,
        order: [['createdAt', 'DESC']],
        include: [{
          model: models.StatsQuestionAnswers, // User answers
          as: 'qcmStatsQuestion',
        }],
      });
      const stats2 = JSON.parse(JSON.stringify(allStats)); // Clone object, probably useless?
      for (const stat of stats2) {
        if (stat && !stat.answersData) {
          stat.answersData = [];
        }
        if (stat) {
          // Support frontend array of answer IDs in answersData
          stat.answersData = stat?.qcmStatsQuestion?.filter(s => s?.value !== '0')?.map(s => (s?.answerId) || s) || [];
        }
      }
      return stats2; // Array of stats
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },


  /**
   * Returns a user's answer history for several questions
   * @param {array} questionsIds - array of models.Question IDs
   * @param {integer} userId
   * @param {integer} qcmSessionId
   * @param statId
   */
  getQuestionsAnswerHistory: async (questionsIds = [], userId, qcmSessionId = null, statId = null) => {
    try {
      let stats = [];
      for (const qId of questionsIds) {
        stats.push(await QCMStatsService.getQuestionAnswerHistory(
          {
            id_question: qId,
          },
          { sessionId: qcmSessionId, userId, statId },
          userId,
        ));
      }
      return stats;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /**
   * Returns the success percent for a question
   * @param {models.Question} question - the question
   * @return {number} - the success percent
   */
  getSuccessPercentQuestion: async (question) => {
    try {
      // Tous les gens ayant répondus à cette question
      const stats = await models.QcmStatsQuestion.findAll({
        where: {
          id_question: question.id_question,
        },
        include: [{
          model: models.StatsQuestionAnswers,
          as: 'qcmStatsQuestion',
        }],
      });
      const countCorrect = {};
      const successPercent = {};

      let questionsAnswers = question?.question_answers; // Exercise items (answers) on questions
      if (!questionsAnswers) {
       // Pour module annales cours on a pas toujours de question_answers
        questionsAnswers = await models.QuestionAnswers.findAll({
          where: {
            questionId: question.id_question,
          }, raw: true,
        });
      }

      if (stats && Array.isArray(questionsAnswers)) {
        // initialisation
        for (const answer of questionsAnswers) {
          countCorrect[answer.id] = 0;
        }
        for (const answerFromStat of stats) {
          const userAnswerHistory = answerFromStat.qcmStatsQuestion.map(a => a.answerId);
          if (Array.isArray(userAnswerHistory)) {
            for (const questionAnswer of questionsAnswers) {
              if (await QCMService.isAnswerTrue(userAnswerHistory, questionAnswer)) {
                countCorrect[questionAnswer.id]++;
              }
            }
          }
        }
        const totalAnswers = stats.length;
        for (const answer of questionsAnswers) {
          if (answer && answer.id && countCorrect.hasOwnProperty(answer.id)) {
            const successPercentTmp = ((countCorrect[answer.id] / totalAnswers) * 100).toFixed(0);
            if (successPercentTmp == 0) {
              successPercent[answer.id] = undefined; // should never happen
            } else {
              successPercent[answer.id] = successPercentTmp;
            }
          } else {
            successPercent[answer.id] = undefined; // should never happen
          }
        }
      }
      return successPercent;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  // Mon résultat pour une série d'exercices (note)
  async getMyStatResult(id_qcm, userId, { sessionId, statId = null }, isFirstTime = true) {
    try {
      let where = {
        id_qcm,
        id_utilisateur: userId,
      };

      // It's either stat id or session id
      if (statId) {
        where = { ...where, id: statId };
      } else if (sessionId) {
        where = { ...where, qcmSessionId: sessionId };
      }

      return await models.QcmStats.findOne({
        where,
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /* User grade results only */
  getAllUserResultsForQCM: async (id_qcm, isFirstTime = undefined, groupIds = []) => {
    try {
      let results;
      let firstTimeOnly = false;
      if (isFirstTime) {
        firstTimeOnly = true;
      }
      // Handle group results
      let specificUserIdsToFetch = null;
      if (groupIds?.length > 0) {
        specificUserIdsToFetch = await GroupeService.getUserIdsInGroupIdsFast(groupIds);
      }

      ///////////////////////////////////
      // Exclude testers users from list
      //////////////////////////////////
      const testeurGroupe = await models.Groupe.findOne({ where: { name: 'Testeurs' } });
      let where = {};
      if (testeurGroupe) {
        const users = await testeurGroupe.getUsers();
        const testersUsersIds = users?.map(u => u.id);
        if (specificUserIdsToFetch) {
          // Remove testers users from the specific user ids
          const userIdsToFetch = specificUserIdsToFetch.filter(u => !testersUsersIds.includes(u));
          where = {
            id_qcm,
            id_utilisateur: userIdsToFetch,
          };
        } else {
          where = {
            id_qcm,
            id_utilisateur: {
              [Op.notIn]: testersUsersIds,
            },
          };
        }
        if (firstTimeOnly) {
          where = { ...where, isFirstTime: firstTimeOnly };
        }
        /////////////////////////////////////
      } else {
        // No testeur group, no need to exclude testers users
        where = {
          id_qcm,
        };
        if (firstTimeOnly) {
          where = { ...where, isFirstTime: firstTimeOnly };
        }
        if (specificUserIdsToFetch) {
          where = { ...where, id_utilisateur: specificUserIdsToFetch };
        }
      }

      results = await models.QcmStats.findAll({
        where,
        order: [['note', 'DESC']], // do NOT change order
      });
      return results;
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  //TODO remove (seems not used)
  async getUserResultsHistory(userId) {
    try {
      return await models.QcmStats.findOne({
        where: {
          id_utilisateur: userId,
        },
        order: [['createdAt', 'DESC']], // Résultats plus anciens à plus récents
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /**
   * QCMs fait par UE et user (relevé de note)
   * Utilisé pour récupérer son historique de notes dans Progression, ou l'historique d'un utilisateur
   * @param ue - the UE
   * @param {number} userId target user id
   * @param qcmSeriesTypes
   * @param dateBegin
   * @param dateEnd
   */
  getQcmFaitsPour: async (ue, userId, filter = {}) => {
    try {
      const { qcmSeriesTypes = [], dateBegin, dateEnd } = filter || {};

      let qcmIdsFilter = {};
      let dateFilter = {};

      /* Filter user results by type of question serie */
      if (qcmSeriesTypes?.length > 0) {
        const qcmIdsWithSelectedTypes = await QcmTypeService.getQcmIdsInType(qcmSeriesTypes);
        qcmIdsFilter = {
          id_qcm: qcmIdsWithSelectedTypes,
        };
      }

      /* Filter user results by date */
      if (dateBegin && dateEnd) {
        dateFilter = {
          date: {
            [Op.between]: [dateBegin, dateEnd],
          },
        };
      }

      const qcmFaits = await models.Qcm.findAll({
        where: {
          UEId: ue.id,
          ...qcmIdsFilter,
          deleted: 0,
          //isPublished: true,
        },
        include: [{
          model: models.QcmStats,
          where: {
            id_utilisateur: userId,
            ...dateFilter,
            //isFirstTime: true, // only first time
          },
          required: true,
        }],
      });


      for await (const qcm of qcmFaits) {
        if (qcm?.statistiques?.length > 0 && qcm.statistiques?.[0]) {
          qcm.statistiques = qcm.statistiques[0];
        }
        const statsResultQCM = await QCMStatsService.getAllUserResultsForQCM(qcm.id_qcm, true);
        let moyenne;
        if (statsResultQCM.length > 0) { // Si fait au moins par une autre personne
          moyenne = await QCMService.calculMoyenneQcm_temp(statsResultQCM, statsResultQCM.length); // todo cache or store
          if (moyenne) {
            qcm.statistiques.moyenne = (moyenne * 1).toFixed(2);
          }
        }
      }
      return await qcmFaits;
    } catch (e) {
      console.error(e);
    }
  },

  getProgressionByUEForUser: async (ue, userId) => {
    try {
      const user = await models.User.findByPk(userId);
      /*
      const countQcmFaits = await models.Qcm.count({
        where: {
          UEId: ue.id,
          deleted: 0,
          isPublished: true,
        },
        include: [{
          model: models.QcmStats,
          where: {
            id_utilisateur: userId,
          },
          required: true,
        }],
      });
      const totalQcms = await models.Qcm.count({
        where: {
          UEId: ue.id,
          deleted: 0,
          isPublished: true,
        },
      });
      */

      /* CHECK ça y'a moyen d'aller plus vite, récupérer les résultats utilisateurs puis chercher seulement ces qcms là */
      const qcmFaits = await models.Qcm.findAll({
        where: {
          UEId: ue.id,
          deleted: 0,
          isPublished: true,
        },
        include: [{
          model: models.QcmStats,
          where: {
            id_utilisateur: userId,
          },
          required: true,
        }],
      });
      const moyenneUser = await QCMStatsService.calculMoyenneSur20PourQcms(qcmFaits);


      const coursIdsToFetch = await PermissionService.getAvailableCoursIdsInUEForUser(ue.id, user);
      // Get questions linked to courses
      const questionIds = await QuestionsService.getQuestionsIdsFromCoursIds(coursIdsToFetch);

      /*
      const idQcms = qcmFaits.map(q => q.id_qcm);
      const qcms = await models.Qcm.findAll({ where: { id_qcm: idQcms } }, { attributes: ['hasExternalQuestions'] });
      // GET Questions IDS
      let questionIds = [];
      for (const qcm of qcms) {
        if (qcm.hasExternalQuestions) {
          // get external questions
          const externalQuestions = await models.QuestionsQcm.findAll({
            where: {
              qcmId: qcm.id_qcm,
            },
          });
          const quest = await models.Question.findAll({
            where: { id_question: externalQuestions.map(m => m.questionId) },
            order: [EXOQUALIZE.ORDER_BY_DEFINED_ORDER],
            attributes: ['id_question', 'id_qcm'],
          });
          const questIdsTmp = quest?.map(q => q.id_question);
          questionIds = [...questionIds, ...questIdsTmp];
        } else {
          const quest = await models.Question.findAll({
            where: { id_qcm: qcm.id_qcm },
            order: [EXOQUALIZE.ORDER_BY_DEFINED_ORDER],
            attributes: ['id_question', 'id_qcm'],
          });
          const questIdsTmp = quest?.map(q => q.id_question);
          questionIds = [...questionIds, ...questIdsTmp];
        }
      }
      */

      // FIND User stats for questions IDS
      const statsUser = await models.QcmStatsQuestion.findAll({
        where: {
          id_utilisateur: userId,
          id_question: questionIds,
        },
      });

      // count good and bad answers
      const statsQuestionAnswersUser = await models.StatsQuestionAnswers.findAll({
        where: {
          statsQuestionId: {
            [Op.in]: statsUser.map(s => s.id_statistique_question),
          },
        },
      });
      let goodAnswersTotal = 0, badAnswersTotal = 0;

      await Promise.all([
        statsQuestionAnswersUser.map(stat => {
          if (stat?.isGood) {
            goodAnswersTotal++;
          } else {
            if(stat?.isGood === false) {
              badAnswersTotal++;
            } else if(stat?.isGood === null) {
              // TODO traiter les cas texte libre et cas où pas encore corrigé
            }
          }
        }),
      ]);

      return {
        moyenneUser,
        moyenneGenerale: ue.moyenneGenerale,
        qcmFaits: statsUser.length,
        totalQcm: questionIds.length,
        goodAnswers: goodAnswersTotal,
        badAnswers: badAnswersTotal,
        coursVus: await UserViewHistoryService.countCoursVusAmongIds(await CoursService.getCoursIdsInUe(ue.id), userId),
        totalCours: await CoursService.countCoursInUe(ue.id),
      };
    } catch (e) {
      console.error(e);
    }
  },

  /* Progression par UE Catégorie */
  async getProgressionByCategoryForUser(category, userId) {
    const qcmIds = await QCMService.getQcmIdsInCategoryId(category.id);
    const qcmFaits = await models.Qcm.findAll({
      where: {
        id_qcm: qcmIds,
        deleted: 0,
        isPublished: true,
      },
      include: [{
        // Notes
        model: models.QcmStats,
        where: {
          id_utilisateur: userId,
        },
        required: true,
      }, {
        model: models.Question,
        attributes: ['id_question', 'id_qcm'],
        include: [{
          model: models.QcmStatsQuestion,
          where: {
            id_utilisateur: userId,
          },
          required: true,
          include: [{
            model: models.StatsQuestionAnswers,
            as: 'qcmStatsQuestion',
          }],
        }, {
          model: models.QuestionAnswers,
          required: true,
        }],
      },
      ],
    });

    let bonnesReponses = 0;
    let mauvaisesReponses = 0;
    for (const qcm of qcmFaits) {
      for (const question of qcm.questions) {
        const { statistiques_questions, question_answers } = question;
        if (statistiques_questions && statistiques_questions[0]) {
          const userResult = statistiques_questions[0];
          const userAnswerHistory = userResult.qcmStatsQuestion?.map(q => q.answerId);
          /* NEW WAY */
          for (const questionAnswer of question_answers) {
            if (await QCMService.isAnswerTrue(userAnswerHistory, questionAnswer)) {
              bonnesReponses++;
            } else {
              mauvaisesReponses++;
            }
          }
        }
      }
    }

    const moyenneUser = await QCMStatsService.calculMoyenneSur20PourQcms(qcmFaits);
    return {
      moyenneUser,
      moyenneGenerale: category.moyenneGenerale,
      bonnesReponses,
      mauvaisesReponses,
    };
  },

  /* Recalcule les notes de tous les users qui ont fait le qcm */
  async updateAllScoreForMcq(idQcm) {
    try {
      const shouldLog = isDev;
      // toutes les notes de ce QCM
      // résultat finaux QCM
      const statsMcq = await models.QcmStats.findAll({
        where: {
          id_qcm: idQcm,
        },
        order: [['date', 'DESC']],
      });
      let questions = await QCMService.getQuestionsForQcm(idQcm);
      // Ignorer les questions de type texte libre
      const hasFreeTextAnswers = questions.some(q => q.isAnswerFreeText === true);
      questions = questions.filter(q => q.isAnswerFreeText !== true);
      if (questions) {
        // Pour chaque résultat final
        for (const stat of statsMcq) {
          // recalcul note basé sur les résultats
          // Exams have session but not statId, in order to find correct answers we set statId to null.
          // Sur Medibox un exam avait statId mais pas sessionId !
          let sessionId = stat.qcmSessionId;
          shouldLog && console.log({ sessionId, statId: stat.id });
          let statId = sessionId ? null : stat.id; // Si on a une session, on ignore statId (statId null)
          const exercisesIds = questions.map(q => q.id_question);

          // inutile depuis le fix exam
          /*
          if(sessionId) {
            const qcmSession = await models.QcmSession.findByPk(sessionId, {
              raw: true,
              attributes: ['id', 'examQuestionSerieId'],
            });
            // Pour savoir si c'est dans un exam
            if(qcmSession?.examQuestionSerieId) {
              // Note issue d'exam, en cas de génération dynamique de résultat de même série entre les exams, la session n'est pas toujours bien reliée
              const originalStat = await models.QcmStats.findOne({
                where: {
                  qcmSessionId: qcmSession.id,
                },
                raw: true,
                attributes: ['id'],
              });
              if(originalStat) {
                shouldLog && console.log('Found original stat from exam session:' + originalStat.id);
                statId = originalStat.id;
                sessionId = qcmSession.id;
              }
            }
          }
          */

          let userChoices = await QCMStatsService.getQuestionsAnswerHistory(exercisesIds, stat.id_utilisateur, sessionId, statId);
          shouldLog && console.log({exercisesIds, userChoices});

          // Should not happen, but if no user choices found, try to get them from the old system
          if (userChoices?.filter(c => c !== null)?.length === 0) {
            shouldLog && console.log('WARNING No user choices found for this user userChoices?.filter(c => c !== null)?.length === 0 ');
            userChoices = await QCMStatsService.getQuestionsAnswerHistory(exercisesIds, stat.id_utilisateur, null, stat.id);
          }

          // Calcul de la note d'après les user choices et met à jour tout
          if (Array.isArray(userChoices)) {
            // On insère pas quand il répond pas, si répondu faux value = 0, si répondu juste value = 1, reconstruction de l'array pour calculNoteQcm
            userChoices = userChoices?.filter(u => u?.qcmStatsQuestion).map(u => {
              const statsQ = u?.qcmStatsQuestion;
              shouldLog && console.log({ statsQ });

              const answers = [];
              const answers_false = [];
              //TODO Uniquement si série dispose de affichage vrai/faux?
              for(const stat of statsQ) {
                if((stat?.value === '1') || stat?.value === null) { // Vrai  (null = ancien système)
                  answers.push(stat.answerId);
                } else if(stat?.value === '0') { // Faux
                  answers_false.push(stat.answerId);
                } else if(stat?.value !== null) { // Autre (non null)
                  // Alphanumérique ou autre
                  const arrayValue = JSON.parse(stat.value)
                  arrayValue.forEach(a => {
                    answers.push(a);
                  });
                }
              }
              return {
                answers,
                answers_false,
                ...u,
              }
            });

            const {
              reponsesUtilisateur,
              note,
              reponsesJustes,
              erreurs,
              pointsPerQuestion,
              totalPonderatedGrade,
            } = await QCMService.calculNoteQcm(questions, userChoices, true);

            shouldLog && console.log({ note });

            if (!hasFreeTextAnswers) {
              // Update final grade only if not free text answers
              stat.note = note;
              stat.ponderatedGrade = totalPonderatedGrade || null;
              await stat.save({ silent: true }); // Update final result
            }

            /* update user answers to add points obtained per question */
            let reponsesUser = await JSON.parse(JSON.stringify(reponsesUtilisateur))?.map(
              rep => ({
                pointsObtained: rep?.pointsObtained,
                ponderatedPointsObtained: rep?.ponderatedPointsObtained || null,
                ...rep,
              }),
            );
            // Prevent bug: keep only answers that have questions attached (99% of the time not null)
            reponsesUser = reponsesUser.filter(rep => rep !== null);
            try {
              await models.QcmStatsQuestion.bulkCreate(reponsesUser,
                { updateOnDuplicate: ['pointsObtained', 'ponderatedPointsObtained'] });
            } catch (e) {
              continue;
            }
          }
        }
      }
    } catch (e) {
      console.error('updateAllScoreForMcq');
      console.error(e);
    }
  },

  /* In Scheduler */
  async updateAllScores() {
    try {
      const qcmFaits = await models.Qcm.findAll({
        where: {
          deleted: 0,
        },
      });
      for (const qcm of qcmFaits) {
        await QCMStatsService.updateAllScoreForMcq(qcm.id_qcm);
      }
    } catch (e) {
      console.error(e);
    }
  },

  /* Update all categories moyenne */
  async updateMoyenneGeneraleCategories() {
    const ueCategories = await models.UECategory.findAll({ attributes: ['id'] });
    const ueCategoriesIds = await ueCategories.map(c => c.id);
    for (const categoryId of ueCategoriesIds) {
      const qcmIds = await QCMService.getQcmIdsInCategoryId(categoryId);
      const tousQcmDansUeCateg = await models.Qcm.findAll({
        where: {
          id_qcm: { [Op.in]: qcmIds },
          deleted: false,
        },
        include: [{
          model: models.QcmStats,
          where: {
            isFirstTime: true,
          },
        }],
      });
      if (tousQcmDansUeCateg) {
        const moyenneGenerale = await QCMStatsService.calculMoyenneSur20PourQcms(tousQcmDansUeCateg);
        if (moyenneGenerale) {
          await models.UECategory.update({ moyenneGenerale }, { where: { id: categoryId } });
        }
      }
    }
  },

  async updateMoyenneGeneraleUes() {
    const ues = await models.UE.findAll({ attributes: ['id'] });
    const ueIds = await ues.map(ue => ue.id);
    for (const ueId of ueIds) {
      const tousQcmDansUe = await models.Qcm.findAll({
        where: {
          UEId: ueId,
          deleted: false,
        },
        include: [{
          model: models.QcmStats, // la table 'statistique' // relie une note à un id_qcm de la table 'qcm'
          where: {
            isFirstTime: true,
          },
        }],
      });
      // consoleDebugObject(tousQcmDansUe);
      if (tousQcmDansUe) {
        const moyenneGenerale = await QCMStatsService.calculMoyenneSur20PourQcms(tousQcmDansUe);
        if (moyenneGenerale) {
          await models.UE.update({ moyenneGenerale }, { where: { id: ueId } });
        }
      }
    }
  },

  async calculMoyenneSur20PourQcms(qcmFaits, moyenneSur = MOYENNE_GENERALE_SUR) {
    let moyenne = 0;
    let total = 0;
    for (const qcm of qcmFaits) {
      const { statistiques, id_qcm } = qcm;

      const maxPoints = await QCMService.getMcqMaximumPoints(qcm, {});
      if (maxPoints===0){return null} // Parfois il arrive que aucun barem soient liés aux questions. On remove le qcm en question

      if (statistiques && statistiques[0]) {
        const { note } = statistiques[0];
        moyenne += ((note / maxPoints) * moyenneSur);
        total++;
      }
      // diviser notes par nombre questions par qcm, multiplier par 20, et faire la moyenne de toutes
    }

    // Permet d'éviter la division par 0 et de retourner un NaN qui fait crash
    if (total===0){return null}

    moyenne /= total;
    return moyenne;
  },

};
