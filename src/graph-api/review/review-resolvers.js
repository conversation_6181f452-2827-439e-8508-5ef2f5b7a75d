import { GraphQLError } from 'graphql';
import { combineResolvers } from 'graphql-resolvers';
import sequelize from 'sequelize';
import models from '../../models/index.js';
import {
  hasUserAlreadyCommantedCours,
  isAuthenticated, isReviewOwnerOrAdminId, isReviewOwnerOrAdminReview,
  reviewAssociatedIdNotEmpty,
  reviewNotEmpty,
} from '../authorization'
import {ReviewService} from './review-service.js'



export default {
  Query: {

    getCoursReviewStats:combineResolvers(
      isAuthenticated,
      async (parent,{coursId},ctx,info)=> {
        return await ReviewService.getCoursReviewStats(coursId, ctx);
      },
    ),

    getAllNotDeletedReviewForCourse:combineResolvers(
      isAuthenticated,
      async (parent,{coursId},ctx,info)=>{
        return await ReviewService.getAllNotDeletedReviewForCourse(coursId,ctx)
      }
    ),

    hasUserAlreadyReviewedCourse:combineResolvers(
      isAuthenticated,
      async(parent,{coursId}, { me },info)=>{return await ReviewService.hasUserAlreadyReviewedCourse(coursId,me.id)}
    ),

  },
  Mutation:{
    createReview : combineResolvers(
      isAuthenticated,
      reviewNotEmpty,
      reviewAssociatedIdNotEmpty,
      hasUserAlreadyCommantedCours,
      async (parent,arg,{me,ip})=>{
        return await ReviewService.createReview(arg,me.id,ip);
      }
    ),

    updateReview:combineResolvers(
      isAuthenticated,
      reviewNotEmpty,
      reviewAssociatedIdNotEmpty,
      isReviewOwnerOrAdminReview,
      async (parent,arg,{me,ip})=>{
        return await ReviewService.updateReview(arg,me.id,ip);
      }
    ),

    deleteReview : combineResolvers(
      isAuthenticated,
      isReviewOwnerOrAdminId,
      async (parent,arg,{me,ip}) =>{
        return await ReviewService.deleteReview(arg,me.id,ip);
      }
    ),

    likeReview: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { me }) => await ReviewService.likeReview(id, me.id),
    ),
    dislikeReview: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { me }) => await ReviewService.dislikeReview(id, me.id),
    ),

  }
}