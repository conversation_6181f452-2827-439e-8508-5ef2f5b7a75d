import { describe } from '@jest/globals'
import { PDFExtract } from 'pdf.js-extract'
import { NotionService } from '../notions/notion-service.js'
import { CoursService } from './cours-service'
import models from '../../models'

//const PDFExtract = require('pdf.js-extract').PDFExtract;

const extract = require('pdf-text-extract')

//  not working
async function getPdfText(data) {
  let doc = await PDFJS.getDocument({ data }).promise
  let pageTexts = Array.from({ length: doc.numPages }, async (v, i) => {
    return (await (await doc.getPage(i + 1))?.getTextContent()).items.map(token => token.str).join('')
  })
  return (await Promise.all(pageTexts)).join('')
}


describe('extract text from pdf', () => {
  test.only('it should get text', (done) => {
    //working, problem est que il lit de gauche à droite et comprend pas quand il y a deux blocs de texte par page
    /*
    extract('/Users/<USER>/Downloads/testpdfff.pdf', {
      //cwd: "./",
      splitPages: false,
    }, (err, text) => {
      if (err) {
        done.fail()
      }
      //console.dir('extracted pages', pages)
      const cleanedText = text.join('').replace(/(\r\n|\n|\r)/gm, " ");
      console.log({ cleanedText })
      done()
    })
     */
    const pdfExtract = new PDFExtract()
    const options = {}
    pdfExtract.extract('/Users/<USER>/Downloads/anat.pdf',
      options, (err, data) => {
        if (err) {
          console.log(err)
        }
        const cleanedUpText = data?.pages?.map(
          p => p?.content?.map(
            c => c.str
              .replace('\n', ' ')
              .replace('\t', ' '),
          )
            .join(''), // no space between pages content it breaks words
        )
          .join(' ') // space between pages
        NotionService.cleanTextForNotionSearch(cleanedUpText).then(t => {
          console.table(t)

        })

        // const lines = PDFExtract.utils.pageToLines(data.pages[0], data.pages.length)
        // const rows = PDFExtract.utils.extractTextRows(lines)
        // replace annoying caracters
        // '•' '-' ’ « ' " : ; . ,
        //console.log({ rows })
        //const text = rows.map((row) => row.join('')).join('').replace('\n', '')
        //console.log({ text })
        done()
      })
  })
})

describe('migrate updateInfos', () => {

  test('it migrate one class', async () => {
    let cours = await models.Cours.findByPk(9)
    await CoursService.createUpdateInfosForClass(cours)
  })
})

describe('getCoursById', () => {
  test('it should return cours by id', async () => {
    let { dataValues: coursResult } =
      await CoursService.getCoursById(models, 1, 1)
    delete coursResult.updatedAt
    delete coursResult.createdAt
    expect(coursResult).toEqual(coursId1)
  })
})
describe('addQcmToCours', () => {
  test('it should add qcm by cours id 1', async () => {
    let { dataValues: result } =
      await CoursService.addQcmToCours(1, 1)
    delete result.updatedAt
    delete result.createdAt
    expect(result).not.toBeNull()
    await CoursService.removeQcmFromCours(1, 1)
  })

})
describe('removeQcmFromCours', () => {
  test('it should remove qcm 1 from cours id 1', async () => {
    let { dataValues: result } = await CoursService.removeQcmFromCours(1, 1)
    expect(result).not.toBeNull()
  })
})


const coursId1 = {
  'id': 1,
  'name': '01: Chapitre 1',
  'text': 'Les lipides',
  'type': 'type',
  'pdf': null,
  'epub': null,
  'preferredDisplayType': 'pdf',
  'isAnnale': false,
  'difficulty': 4,
  'workTime': '7h',
  'updateInfos': 'Mis à jour à 12h23',
  'tips': 'Astuce: pour réussir, travaillez plus ',
  'usefulLinks': '',
  'views': 124,
  'version': '1',
  'isEnAvant': true,
  'date': '2020-05-12',
  'deleted': false,
  'uecategoryId': 1,
  'authorId': 1,
}