import { RedisPubSub } from 'graphql-redis-subscriptions';
import Redis from 'ioredis';

import * as MESSAGE_EVENTS from './message/message-subscription';
import * as NOTIFICATION_EVENTS from './notification/notification-subscription';
import * as QUESTION_EVENTS from './qcm/questions/questions-subscription'

export const EVENTS = {
  MESSAGE: MESSAGE_EVENTS,
  NOTIFICATION: NOTIFICATION_EVENTS,
  QUESTION_EVENTS: QUESTION_EVENTS,
};

const options = {
  host: 'localhost',
  port: '6379',
  retryStrategy: times => {
    // reconnect after
    return Math.min(times * 50, 2000);
  }
};

export default new RedisPubSub({
  publisher: new Redis(options),
  subscriber: new Redis(options),
  connectionListener: (err) => {
    console.log({err})
    if (err) {
      console.error(err);
    }
    console.info('Pubsub: Succefuly connected to redis');
  }
});
