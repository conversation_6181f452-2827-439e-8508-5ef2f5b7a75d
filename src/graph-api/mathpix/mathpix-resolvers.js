import { combineResolvers } from 'graphql-resolvers';
import { isAdmin, isAuthenticated, isTuteurOrAdmin } from '../authorization'
import mathpixSchema from './mathpix-schema'
import MathpixService from './mathpix-service'

export default {
  Query:{
    adminQueryMathpixies: combineResolvers(
      isAdmin,
      async (parents,args,ctx,info)=>MathpixService.adminQueryMathpixies({args,ctx}),
    ),

    processImage:combineResolvers(
      isTuteurOrAdmin,
      async (parents,args,ctx,info)=>await MathpixService.processImage({args,ctx})
    )
  },

  Mutation:{
    mathpixUploadPdf:combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx, info) => await MathpixService.mathpixUploadPdfV2({args,ctx })
    ),

    deleteMathpixie:combineResolvers(
      isAdmin,
      async (parents,args,ctx,info)=>MathpixService.deleteMathpixie({args,ctx})
    )
  }
}