 
import models from '../../models'

/*
type Statistique {
        connected: Int
        registered: Int
        messages: Int
        qcms: Int
        cours: Int
        fiches: Int
    }
 */

// GLOBAL STATS
export const StatsService = {
  getStats: async () => {
    try {
      //console.log(await StatsService.getTotalNumberOfConnectedUsers())
      return {
        connected: await StatsService.getTotalNumberOfConnectedUsers(),
        registered: await StatsService.getTotalNumberOfRegisteredUsers(),
        messages: await StatsService.getTotalNumberOfPosts(),
        qcms: await StatsService.getTotalNumberOfQcms(),
        cours: await StatsService.getTotalNumberOfCours(),
        fiches: await StatsService.getTotalNumberOfFiches(),
        notions: await StatsService.getTotalNumberOfNotions()
      }
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur récupération statistiques')
    }
  },
  getTotalNumberOfCours: async () => {
    return await models.Cours.count({ where: { deleted: false } })
  },
  getTotalNumberOfFiches: async () => {
    return await models.Fiche.count({ where: { deleted: false } })
  },
  getTotalNumberOfNotions: async () => {
    return await models.Notion.count()
  },
  getTotalNumberOfConnectedUsers: async () => {
    return (await models.User.count({ where: { isActive: "1" } }))
  },
  getTotalNumberOfRegisteredUsers: async () => {
    return await models.User.count({ where: { banned: "0" } })
  },
  getTotalNumberOfQcms: async () => {
    return await models.Question.count()
  },
  getTotalNumberOfPosts: async () => {
    return await models.Post.count()
  }
}
