import gql from 'graphql-tag'

export default gql`
    extend type Query {
        "Search notions using NotionSearchFilter"
        searchNotions(filter: NotionSearchFilter): NotionsResult
        "Get notion by id"
        notion(id: ID!): Notion
        "Search in NotionKeywords"
        searchNotionKeywords(filter: NotionKeywordSearchFilter): [NotionKeyword]
        "Extract notions from keywords in text"
        extractNotionsFromTextWithKeywords(input: String): [Notion]
        
        notionGraph(notionId: ID, filter: NotionGraphFilter): JSON
        
        getInitialNotionGraphForCours(coursId: ID, filter: NotionGraphFilter): J<PERSON><PERSON>
    }

    extend type Mutation {
        # CRUD NOTION
        createNotion(notion: NotionInput!): Notion!
        updateNotion(id: ID!, notion: NotionInput!): Boolean
        deleteNotion(id: ID!): Boolean!

        # CRUD Notions keywords
        createNotionKeyword(notionKeyword: NotionKeywordInput!): NotionKeyword!
        updateNotionKeyword(id: ID!, notionKeyword: NotionKeywordInput!): Boolean!
        removeNotionKeyword(notionKeywordId: ID!): Boolean!

        # Notions Cours
        addNotionToCours(notionId: ID!, coursId: ID!, autoAdded: Boolean!): Notion!
        removeNotionFromCours(notionId: ID!, coursId: ID!, autoAdded: Boolean!): Notion!

        # Notions questions
        addNotionToQuestion(notionId: ID!, questionId: ID!): Notion!
        removeNotionFromQuestion(notionId: ID!, questionId: ID!): Notion!

        # Notions QuestionAnswer
        addNotionToQuestionAnswer(notionId: ID!, answerId: ID!): Notion!
        removeNotionFromQuestionAnswer(notionId: ID!, answerId: ID!): Notion!
        setNotionsQuestionAnswer(notionsIds: [ID]!, answerId: ID!): Boolean

        "Add or remove notions to all Questions and question answers from an array of keywords"
        addRemoveNotionQuestionsFromKeyword(keywords: [String], notionId: ID, action: String): NotionAttributionResult
        
        "Set answer notions from keywords in text"
        setNotionsFromTextWithKeywords(input: String, answerId: ID!): Boolean
        "Set question notions from keywords in text"
        setNotionsQuestionFromTextWithKeywords(input: String, questionId: ID!): Boolean

        "Add parent notion to notionId"
        addNotionParent(notionId: ID!, parentNotionId: ID!): Boolean
        "Add child notion to notionId"
        addNotionChildren(notionId: ID!, childrenNotionId: ID!): Boolean

        removeNotionParent(notionId: ID!, parentNotionId: ID!): Boolean
        removeNotionChildren(notionId: ID!, childrenNotionId: ID!): Boolean
    }
    
    input NotionGraphFilter {
        showParentsNotion: Boolean
        showChildsNotion: Boolean
    }
    
    type NotionAttributionResult {
        totalQuestions: Int
        totalAnswers: Int
    }

    "Notion Keyword"
    type NotionKeyword {
        id: ID
        name: String
        notion: Notion
    }

    input NotionKeywordInput {
        name: String
        notionId: ID
    }
    
    type NotionsResult {
        count: Int
        notions: [Notion]
    }

    "Notion"
    type Notion {
        id: ID
        "The name"
        name: String
        name_en: String
        name_it: String
        name_de: String
        name_es: String
        "Description text"
        description: String
        description_en: String
        description_it: String
        description_de: String
        description_es: String
        image: String
        cours: [Cours]
        questions: [Question]
        qcms: [Qcm]
        keywords: [NotionKeyword]
        myNotionStats: StatsNotionUser
        formula: String
        
        files: [File]
        images: [File]
        usefulLinks: [File]
        
        parents: [Notion]
        childrens: [Notion]
        
        exercisesResultsSummary(userId: ID, coursId: ID): GoodAnswersStatsUserSynthesis
    }

    "Good and bad answers for given user and notion"
    type StatsNotionUser {
        id: ID
        goodAnswers: Int
        badAnswers: Int
        createdAt: Date
        updatedAt: Date
    }

    input NotionInput {
        "The name"
        name: String
        name_en: String
        name_it: String
        name_de: String
        name_es: String
        "Description text"
        description: String
        description_en: String
        description_it: String
        description_de: String
        description_es: String
        formula: String
        image: Upload
        keywords: [NotionKeywordInput]
    }

    "Used to search notions"
    input NotionSearchFilter {
        offset: Int
        limit: Int
        name: String
        keywords: String
        inputSetting: String
    }
    "Used to search notions keywords"
    input NotionKeywordSearchFilter {
        name: String
        keywords: String
    }

`
