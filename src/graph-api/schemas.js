import gql from 'graphql-tag';
import apiKeysSchema from './api-keys/api-keys-schema.js';
import averageHistorySchema from './average-history/average-history-schema.js';
import challengeSchema from './challenges/challenge-schema.js';
import chatGptSchema from './chat-gpt/chat-gpt-schema.js';
import configSchema from './config/config-schema';
import coursSchema from './cours/cours-schema';
import edtSchema from './edt/edt-schema.js';
import eventSchema from './event/event-schema.js';
import examSchema from './exam/exam-schema.js';
import fileSchema from './file/file-schema.js';
import folderSchema from './folder/folder-schema.js';
import forfaitSchema from './forfait/forfait-schema.js';
import formsSchema from './form/form-schema.js';
import formationSchema from './formation/formation-schema.js';
import forumSchema from './forum/forum-schema.js';
import groupeSchema from './groupe/groupe-schema';
import logSchema from './log/log-schema.js';
import messageSchema from './message/message-schema';
import modulesQuickAccessSchema from './modules-quick-access/modules-quick-access-schema.js';
import notificationSchema from './notification/notification-schema';
import notionSchema from './notions/notion-schema.js';
import postSchema from './post/post-schema';
import postLimitationRulesSchema from './post/post_limitation_rules/post-limitation-rule-schema.js';
import answersQcmSchema from './qcm/answers/answers-schema.js';
import qcmSchema from './qcm/qcm-schema';
import questionsQcmSchema from './qcm/questions/questions-schema.js';
import mcqScaleSchema from './qcm/scales/mcq-scale-schema.js';
import sessionsQcmSchema from './qcm/sessions/qcm-session-schema.js';
import reviewSchema from './review/review-schema.js';
import scheduledTasksSchema from './scheduled-tasks/scheduled-tasks-schema.js';
import schemaLibrarySchema from './schema-library/schema-library-schema.js';
import templateSchema from './template/template-schema.js';
import ueSchema from './ue/ue-schema';
import userCompanyInformationSchema from './user-company-information/user-company-information-schema.js';
import userSchema from './user/user-schema';
import mathpixSchema from './mathpix/mathpix-schema.js';
import webhooksSchema from './webhooks/webhooks-schema.js';
import calendarSchema from './edt/calendar/calendar-schema';
import vidstackTrackingSchema from './vidstackTracking/vidstackTracking-schema'
import s3Schema from './s3/s3-schema'
import ueModuleSchema from './ue/ue-module/ue-module-schema.js';
import scormSchema from './scorm/scorm-schema'

const linkSchema = gql`
    scalar Upload
    scalar Date

    type Query {
        _: Boolean
    }
    type Mutation {
        _: Boolean
    }
    type Subscription {
        _: Boolean
    }
`;

export default [
  linkSchema,
  userSchema,
  messageSchema,
  coursSchema,
  notificationSchema,
  configSchema,
  ueSchema,
  groupeSchema,
  postSchema,
  qcmSchema,
  forumSchema,
  edtSchema,
  forfaitSchema,
  notionSchema,
  mcqScaleSchema,
  questionsQcmSchema,
  answersQcmSchema,
  sessionsQcmSchema,
  formationSchema,
  examSchema,
  eventSchema,
  folderSchema,
  logSchema,
  challengeSchema,
  modulesQuickAccessSchema,
  formsSchema,
  reviewSchema,
  fileSchema,
  averageHistorySchema,
  userCompanyInformationSchema,
  templateSchema,
  scheduledTasksSchema,
  postLimitationRulesSchema,
  schemaLibrarySchema,
  chatGptSchema,
  apiKeysSchema,
  mathpixSchema,
  webhooksSchema,
  calendarSchema,
  vidstackTrackingSchema,
  s3Schema,
  ueModuleSchema,
  scormSchema,
];
