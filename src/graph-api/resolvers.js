import { GraphQLDateTime } from 'graphql-iso-date';
import apiKeysResolver from './api-keys/api-keys-resolvers.js';
import averageHistoryResolvers from './average-history/average-history-resolvers.js';
import challengeResolvers from './challenges/challenge-resolvers.js';
import chatGptResolver from './chat-gpt/chat-gpt-resolver.js';
import configResolver from './config/config-resolvers';
import coursResolvers from './cours/cours-resolvers';
import edtResolvers from './edt/edt-resolvers.js';
import eventResolvers from './event/event-resolvers.js';
import examsResolvers from './exam/exam-resolvers.js';
import fileResolvers from './file/file-resolvers.js';
import folderResolvers from './folder/folder-resolvers.js';
import forfaitsResolvers from './forfait/forfait-resolvers.js';
import formsResolvers from './form/form-resolvers.js';
import formationResolvers from './formation/formation-resolvers.js';
import forumResolvers from './forum/forum-resolvers.js';
import groupeResolvers from './groupe/groupe-resolvers';
import logResolvers from './log/log-resolvers.js';
import messageResolvers from './message/message-resolvers';
import modulesQuickAccessResolvers from './modules-quick-access/modules-quick-access-resolvers.js';
import notificationResolvers from './notification/notification-resolvers';
import notionResolvers from './notions/notion-resolvers.js';
import postResolvers from './post/post-resolvers';
import postLimitationRuleResolver from './post/post_limitation_rules/post-limitation-rule-resolver.js';
import answersQcmResolvers from './qcm/answers/answers-resolvers.js';
import qcmResolvers from './qcm/qcm-resolvers';
import questionsQcmResolvers from './qcm/questions/questions-resolvers.js';
import mcqScaleResolvers from './qcm/scales/mcq-scale-resolvers.js';
import qcmSessionResolvers from './qcm/sessions/qcm-session-resolvers.js';
import reviewResolvers from './review/review-resolvers.js';
import scheduledTasksResolvers from './scheduled-tasks/scheduled-tasks-resolvers.js';
import schemaLibraryResolvers from './schema-library/schema-library-resolvers.js';
import templateResolvers from './template/template-resolvers.js';
import ueResolvers from './ue/ue-resolvers';
import userCompanyInformationResolvers from './user-company-information/user-company-information-resolvers.js';
import mathpixResolvers from './mathpix/mathpix-resolvers'
import userResolvers from './user/user-resolvers';
import webhooksResolvers from './webhooks/webhooks-resolvers.js';
import calendarResolvers from './edt/calendar/calendar-resolvers.js';
import ueModulesResolvers from './ue/ue-module/ue-module-resolvers.js';
import vidstackTrackingResolver from './vidstackTracking/vidstackTracking-resolver'
import s3Resolver from './s3/s3-resolver'
import scormResolver from './scorm/scorm-resolver'

const customScalarResolver = {
  Date: GraphQLDateTime,
};

export default [
  //customScalarResolver,
  logResolvers,
  userResolvers,
  messageResolvers,
  coursResolvers,
  notificationResolvers,
  configResolver,
  ueResolvers,
  groupeResolvers,
  postResolvers,
  qcmResolvers,
  forumResolvers,
  edtResolvers,
  forfaitsResolvers,
  notionResolvers,
  mcqScaleResolvers,
  questionsQcmResolvers,
  answersQcmResolvers,
  qcmSessionResolvers,
  formationResolvers,
  examsResolvers,
  eventResolvers,
  folderResolvers,
  challengeResolvers,
  modulesQuickAccessResolvers,
  reviewResolvers,
  formsResolvers,
  fileResolvers,
  averageHistoryResolvers,
  userCompanyInformationResolvers,
  templateResolvers,
  scheduledTasksResolvers,
  postLimitationRuleResolver,
  schemaLibraryResolvers,
  chatGptResolver,
  apiKeysResolver,
  mathpixResolvers,
  webhooksResolvers,
  calendarResolvers,
  vidstackTrackingResolver,
  s3Resolver,
  ueModulesResolvers,
  scormResolver,
];

// resolver example: fieldName: (parent, args, context, info) => data;