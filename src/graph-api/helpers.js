import { Op } from 'sequelize'
import { sequelize } from '../models/index.js'

export const EXOQUALIZE = {
  ORDER_MOST_RECENT: ['createdAt', 'DESC'],
  ORDER_MOST_RECENTLY_UPDATED: ['updatedAt', 'DESC'],
  ORDER_BY_DEFINED_ORDER: ['order', 'ASC'],
}

export const likifyWhere = (item, property) => {
  if (item && item[property]) {
    item[property] = {
      [Op.like]: `%${item[property]}%`,
    }
  }
}

export const likyWhereCaseInsensitive = (item, property) => {
  if (item && item[property]) {
    item[property] = sequelize.where(sequelize.fn('LOWER', [property]), 'LIKE', `%${item[property]?.toLowerCase()}%`)
  }
}