import gql from 'graphql-tag'

/*
 * Formulaires
 *
 *
 */

export default gql`

    extend type Query {
        "Form by ID (int) or UUID (string)"
        form(id: ID, uuid: ID): Form
        "Returns all forms"
        allForms: [Form]
        "Returns all elements in a form"
        elementsInForm(formId: ID!): [FormationElement]
        "Returns all filtered form results"
        formResults(filter: FormResultFilter): [UserPropertyData]
        "Returns first undone mandatory form id to complete, for current user."
        undoneMandatoryFormIdToComplete: ID
    }

    extend type Mutation {
        #crud forms
        createForm(form: FormInput!): Boolean
        updateForm(id: ID!, form: FormInput!): Boolean
        deleteForm(id: ID!): Boolean
        
        duplicateForm(id: ID!): Boolean

        "Delete all form results for a form and a user"
        deleteFormUserResults(formId: ID!, userId: ID!): Boolean
        
        "Set form as fully completed or not, for a user. If no userId provided, use current user"
        setFormCompleted(finished: Boolean!, formId: ID!, userId: ID, parentElementId: ID): Boolean
        
        addElementToForm(formId: ID!, elementId: ID!): Boolean
        removeElementFromForm(formId: ID!, elementId: ID!): Boolean
        
        "Add a mandatory group to a form"
        addMandatoryGroupToForm(formId: ID!, groupId: ID!): Boolean
        removeMandatoryGroupFromForm(formId: ID!, groupId: ID!): Boolean
    }

    input FormResultFilter {
        formId: ID
        userId: ID
    }

    input FormInput {
        name: String
        name_en: String
        name_es: String
        name_de: String
        name_it: String
        color: String
        backgroundImage: String
        
        oneAnswerByUser: Boolean
        isMandatory: Boolean
    }
    type Form {
        id: ID!
        uuid: String
        name: String
        name_en: String
        name_es: String
        name_de: String
        name_it: String
        createdAt: Date
        updatedAt: Date
        elements: [FormationElement]
        completedBy: [FormUserResults]
        completedByMe: Boolean
        mandatoryGroups: [Groupe]
        color: String
        backgroundImage: String
        
        oneAnswerByUser: Boolean
        isMandatory: Boolean
    }
    type FormUserResults {
        state: String # 'finished' or 'inProgress'
        user: User
        createdAt: Date
    }

`;
