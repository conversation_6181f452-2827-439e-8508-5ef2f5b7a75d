import { combineResolvers } from 'graphql-resolvers';
import { isAdmin, isAuthenticated, isTuteurOrAdmin } from '../authorization';
import { ChallengeService } from './challenge-service.js';

export default {
  Query: {
    allChallenges: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ChallengeService.getAllChallenges(args, ctx),
    ),

    challenge: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => await ChallengeService.getChallengeById(args, ctx),
    ),

    completedChallenges: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => await ChallengeService.getCompletedChallenges(args, ctx),
    ),

    challengeUserProgress: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => await ChallengeService.getChallengeUserProgress(args, ctx),
    ),
  },

  Mutation: {
    /* CRUD Challenge */
    createChallenge: combineResolvers(
      isAdmin,
      async (parent, { input }, { models }) => await ChallengeService.createChallenge(input),
    ),
    updateChallenge: combineResolvers(
      isAdmin,
      async (parent, { id, input }, { models }) => await ChallengeService.updateChallenge(id, input),
    ),
    deleteChallenge: combineResolvers(
      isAdmin,
      async (parent, { id }, { models }) => await ChallengeService.deleteChallenge(id),
    ),
    /* CRUD ChallengeCondition */
    createChallengeCondition: combineResolvers(
      isAdmin,
      async (parent, { input }, { models }) => await ChallengeService.createChallengeCondition(input),
    ),
    updateChallengeCondition: combineResolvers(
      isAdmin,
      async (parent, { id, input }, { models }) => await ChallengeService.updateChallengeCondition(id, input),
    ),
    deleteChallengeCondition: combineResolvers(
      isAdmin,
      async (parent, { id }, { models }) => await ChallengeService.deleteChallengeCondition(id),
    ),

    /* TYPES */
    addTypeToChallenge: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await ChallengeService.addTypeToChallenge(args, ctx),
    ),
    removeTypeFromChallenge: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await ChallengeService.removeTypeFromChallenge(args, ctx),
    ),

    addGroupUnlockToChallenge: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await ChallengeService.addGroupUnlockToChallenge(args, ctx),
    ),
    removeGroupUnlockFromChallenge: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await ChallengeService.removeGroupUnlockFromChallenge(args, ctx),
    ),

  },

  Challenge: {
    challengeConditions: async (parent, args, ctx) => await ChallengeService.getChallengeConditions(parent.id, ctx),

    types: async (parent, args, ctx) => await ChallengeService.getTypes(parent, args, ctx),
    groupsObtained: async (parent, args, ctx) => await ChallengeService.getGroupsObtained(parent, args, ctx),
  },
};
