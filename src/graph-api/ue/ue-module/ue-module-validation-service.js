import { ELEMENTS_TYPE } from '../../../models/formation/formation_element';
import models from '../../../models/index.js';

const DEBUG_LOG = true;

/**
 * Toutes les méthodes de validation des modules
 *
 */
const ModulesElementsValidations = {

  async handleFinishFormFromModuleElement(formId, userId, parentElementId) {
    // Cas où formulaire terminé dans un module de formation
    const ueModule = await models.UEModules.findOne({
      where: {
        elementId: parentElementId,
      },
    });
    if(!ueModule) {
      // OK, probably in cours with steps, not in module
      return;
    }
    // on marque le module comme terminé
    const userModuleProgress = await models.UEModuleProgress.findOne({
      where: {
        userId,
        ueModuleId: ueModule.id,
      },
    });
    if (!userModuleProgress) {
      console.error(`Critical: ueModule exist but no progress found for user ${userId} on module ${ueModule.id}`);
      return;
    }
    userModuleProgress.completed = true;
    await userModuleProgress.save();
    return true; // module validé
  },

  async handleFinishFormFromCoursElement(formId, userId, parentElementId) {
    // Cas où formulaire dans étape de module de formation
    const element = await models.FormationElement.findByPk(parentElementId, {
      raw: true,
      attributes: ['id', 'blockId'],
    });
    if (!element?.blockId) {
      DEBUG_LOG && console.error(`handleFinishForm: element with id ${parentElementId} has no blockId, cannot validate module.`);
      return;
    }

    const block = await models.FormationBlock.findByPk(element.blockId, {
      raw: true,
      attributes: ['coursId'],
    });
    if (!block?.coursId) {
      console.error(`Critical: block with id ${element.blockId} has no coursId, cannot validate module.`);
      return;
    }
    const ueModule = await models.UEModules.findOne({
      where: {
        coursId: block.coursId,
      },
    });
    if (!ueModule) {
      console.error(`Critical: no UEModule found for coursId ${block.coursId} for user ${userId}`);
      return;
    }
    // on a tout ce qu'il faut pour valider l'étape du module
    // Prendre le dernier log de progression du module correspondant pour l'utilisateur et marquer comme terminé
    const userModuleProgressLog = await models.UEModuleProgressLogs.findOne({
      where: {
        userId,
        ueModuleId: ueModule.id,
        blockId: element.blockId,
      },
      order: [['createdAt', 'DESC']],
    });
    if (!userModuleProgressLog) {
      console.error(`Critical: no UEModuleProgressLog found for user ${userId} on module ${ueModule.id} and block ${element.blockId}`);
      return;
    }
    userModuleProgressLog.completed = true;
    await userModuleProgressLog.save();
    await UEModuleValidationService.afterModuleElementCompleted({
      coursId: block.coursId,
      userId,
      ueModuleId: ueModule.id,
    }, {
      models,
    });
    return true; // étape de module validé
  },

  async handleFinishForm(formId, userId, parentElementId) {
    try {
      DEBUG_LOG && console.log('handleFinishForm', formId, userId, parentElementId);

      await this.handleFinishFormFromModuleElement(formId, userId, parentElementId);
      await this.handleFinishFormFromCoursElement(formId, userId, parentElementId);

      return true; // Formulaire traité avec succès
    } catch (e) {
      console.error('handleFinishForm');
      console.error(e);
    }
  },

  async handleFinishedSessionFromModuleElement(mySession) {
    const userId = mySession?.userId;
    if (mySession?.formationElementId) {
      // etre sûr que cette session correspond à ce module
      const ueModule = await models.UEModules.findOne({
        where: {
          elementId: mySession.formationElementId,
        },
      });
      if (!ueModule) {
        return;
      }
      // on marque le module comme terminé
      const userModuleProgress = await models.UEModuleProgress.findOne({
        where: {
          userId,
          ueModuleId: ueModule.id,
        },
      });

      if (!userModuleProgress) {
        console.error(`Critical: ueModule exist but no progress found for user ${userId} on module ${ueModule.id}`);
        return;
      }
      // Si le critère de validation est atteint, on marque le module comme terminé

      if (ueModule?.validationSettings?.finishSerie === true) {
        // Critère terminer toute la série ou l'exercice
        userModuleProgress.completed = true;
        await userModuleProgress.save();
      } else if (ueModule?.validationSettings?.obtainMinimumGrade === true) {
        // Critère de note minimum, note sur 20 à atteindre
        const minimumGrade = ueModule.validationSettings.minimumGrade;
        const statQcm = await models.QcmStats.findOne({
          where: {
            qcmSessionId: mySession.id,
          },
          attributes: ['note'],
        });
        if (statQcm && statQcm.note >= minimumGrade) {
          userModuleProgress.completed = true;
          await userModuleProgress.save();
        } else {
          //console.warn(`User ${userId} did not achieve the minimum grade of ${minimumGrade} on module ${ueModule.id}`);
        }
      }
    }
  },

  async handleFinishedSessionFromCoursElement(mySession) {
    const userId = mySession?.userId;
    if (mySession?.formationElementId) {
      // Cas où session dans étape de module de formation
      const element = await models.FormationElement.findByPk(mySession.formationElementId, {
        raw: true,
        attributes: ['id', 'blockId'],
      });
      if (!element?.blockId) {
        DEBUG_LOG && console.error(`handleFinishedSession: element with id ${mySession.formationElementId} has no blockId, cannot validate module.`);
        return;
      }

      const block = await models.FormationBlock.findByPk(element.blockId, {
        raw: true,
        attributes: ['coursId'],
      });
      if (!block?.coursId) {
        console.error(`Critical: block with id ${element.blockId} has no coursId, cannot validate module.`);
        return;
      }
      const ueModule = await models.UEModules.findOne({
        where: {
          coursId: block.coursId,
        },
      });
      if (!ueModule) {
        console.error(`Critical: no UEModule found for coursId ${block.coursId} for user ${userId}`);
        return;
      }
      // on a tout ce qu'il faut pour valider l'étape du module
      // Prendre le dernier log de progression du module correspondant pour l'utilisateur et marquer comme terminé
      const userModuleProgressLog = await models.UEModuleProgressLogs.findOne({
        where: {
          userId,
          ueModuleId: ueModule.id,
          blockId: element.blockId,
        },
        order: [['createdAt', 'DESC']],
      });
      if (!userModuleProgressLog) {
        console.error(`Critical: no UEModuleProgressLog found for user ${userId} on module ${ueModule.id} and block ${element.blockId}`);
        return;
      }

      // Apply validation criteria before marking as completed
      if (ueModule?.validationSettings?.finishSerie === true) {
        // Critère terminer toute la série ou l'exercice
        userModuleProgressLog.completed = true;
        await userModuleProgressLog.save();
      } else if (ueModule?.validationSettings?.obtainMinimumGrade === true) {
        // Critère de note minimum, note sur 20 à atteindre
        const minimumGrade = ueModule.validationSettings.minimumGrade;
        const statQcm = await models.QcmStats.findOne({
          where: {
            qcmSessionId: mySession.id,
          },
          attributes: ['note'],
        });
        if (statQcm && statQcm.note >= minimumGrade) {
          userModuleProgressLog.completed = true;
          await userModuleProgressLog.save();
        } else {
          //console.warn(`User ${userId} did not achieve the minimum grade of ${minimumGrade} on module ${ueModule.id}`);
          return;
        }
      } else {
        // No specific validation criteria, mark as completed
        userModuleProgressLog.completed = true;
        await userModuleProgressLog.save();
      }

      await UEModuleValidationService.afterModuleElementCompleted({
        coursId: block.coursId,
        userId,
        ueModuleId: ueModule.id,
      }, {
        models,
      });
      return true; // étape de module validé
    }
  },

  // Finish MCQ session
  async handleFinishedSession(mySession) {
    try {
      DEBUG_LOG && console.log('handleFinishedSession', mySession);

      await this.handleFinishedSessionFromModuleElement(mySession);
      await this.handleFinishedSessionFromCoursElement(mySession);

      return true; // Session traitée avec succès
    } catch (e) {
      console.error('handleFinishedSession');
      console.error(e);
    }
  },
};


export const UEModuleValidationService = {

  ...ModulesElementsValidations,

  /**
   * (FOR FORMATION MODULE WITH STEPS) Returns true if the element is validated based on its validation settings
   *
   * On click on next module IN module cours, check if
   * @param element
   * @param ctx
   * @returns {Promise<boolean>}
   */
  async isElementValidatedBasedOnValidationSettings(element, ctx) {
    if (!element?.type) {
      console.error('isElementValidatedBasedOnValidationSettings: element type is not defined', element);
      return false; // Pas de type d'élément, on ne peut pas valider
    }
    const validationSettings = element?.validationSettings; // {"finishSerie": true, "obtainMinimumGrade": false}
    const elementsWithNoValidation = [
      ELEMENTS_TYPE.RICH_TEXT,
      ELEMENTS_TYPE.HTML,
      ELEMENTS_TYPE.CALLOUT,
      ELEMENTS_TYPE.DATE_AND_TIME_PICKER,
      ELEMENTS_TYPE.DATE_PICKER,
      ELEMENTS_TYPE.AVATAR_SELECT,
      ELEMENTS_TYPE.IMAGE,
      ELEMENTS_TYPE.LINK,
      ELEMENTS_TYPE.TITLE,
      ELEMENTS_TYPE.FILE,
      ELEMENTS_TYPE.DIAPO_SYNTHESE,
      ELEMENTS_TYPE.COURS,
      ELEMENTS_TYPE.COURSE_SHORTCUT, // Raccourci vers un cours, on peut pas savoir de validation
      //ELEMENTS_TYPE.VIDEO
    ];
    if (elementsWithNoValidation.includes(element.type)) {
      // Pas de validation custom pour ces éléments
      return true;
    }

    // Cas formulaire : validation ailleurs lorsque formulaire terminé
    if (ELEMENTS_TYPE.FORM === element.type) {
      return false; // On ne valide pas ici, on attend que le formulaire soit terminé
    }
    // Cas série / exercice
    else if ([ELEMENTS_TYPE.MCQ, ELEMENTS_TYPE.DO_EXERCISE].includes(element.type)) {
      // Si finishSerie est true, OU que obtainMinimumGrade est true on ne valide pas
      if (validationSettings?.finishSerie === true || validationSettings?.obtainMinimumGrade === true) {
        return false; // On ne valide pas ici, on attend que la série soit terminée ou la note atteinte
      }
    }

    /* Éléments qui ont une validation habituellement, mais qui est désactivée
    const shouldValidateModuleCours = (
      validationSettings?.minimumTime === false && validationSettings?.validatedSteps === false
    );

    if (shouldValidateModuleCours) {
      return true;
    }
    */

    return false; // par défaut on ne valide pas
  },

  /**
   * (FOR FORMATION MODULE WITHOUT STEPS) Check if the module is validated based on its validation settings.
   *
   * @param validationSettings
   * @param ctx
   * @returns {Promise<boolean>}
   */
  async isModuleValidatedBasedOnValidationSettings(validationSettings, ctx) {
    if (
      // Module avec minimum de temps => on ne fait rien, le module sera validé automatiquement
      validationSettings.minimumTime === true ||
      // Série / exercices: obtenir la note minimale validera
      validationSettings.obtainMinimumGrade === true ||
      // Série : terminer la série validera
      validationSettings.finishSerie === true
    ) {
      return true;
    }
    // Cas des formulaires: module validé quand complété

    // Cas évènement: TODO
    // Cas devoir: TODO

    // Si aucune condition de validation, on valide le module

    // CAS VALIDATION MODULE COURS SANS CONDITIONS
    const shouldValidateModuleCours = (
      validationSettings.minimumTime === false && validationSettings.validatedSteps === false
    );
    const shouldValidateModuleExerciseOrSerie = (
      validationSettings.finishSerie === false && validationSettings.obtainMinimumGrade === false
    );

    if (shouldValidateModuleCours || shouldValidateModuleExerciseOrSerie) {
      return true; // module validé sans conditions
    }

    return false; // par défaut on ne valide pas
  },

  /////////////////////////////////////////////////////////////////////////

  async setModuleProgressLogCompleted(ueModuleProgressLogId, ctx) {
    try {
      const ueModuleProgressLog = await ctx.models.UEModuleProgressLogs.findByPk(ueModuleProgressLogId);
      if (!ueModuleProgressLog) {
        console.error('shouldValidateModuleElement: UEModuleProgressLog not found for id:', ueModuleProgressLogId);
      } else {
        ueModuleProgressLog.completed = true;
        await ueModuleProgressLog.save();
        return true;
      }
      return false;
    } catch (e) {
      console.error('setModuleProgressLogCompleted error:', e);
    }
  },

  /**
   * After an element of a module is completed, check if all blocks of the module are completed.
   *
   * @param coursId
   * @param userId
   * @param ueModuleId
   * @param ctx
   * @returns {Promise<void>}
   */
  async afterModuleElementCompleted({ coursId, userId, ueModuleId }, ctx) {
    try {
      const allBlocks = await ctx.models.FormationBlock.findAll({
        where: {
          coursId,
        },
        attributes: ['id', 'coursId'],
        raw: true,
      });
      const allBlockIds = [...new Set(allBlocks.map(b => b.id))]; // on prend les ids des blocks
      const allProgressLogs = await ctx.models.UEModuleProgressLogs.findAll({
        where: {
          userId,
          ueModuleId,
          blockId: allBlockIds,
          completed: true,
        },
        attributes: ['blockId'], // inutile de tout charger
        raw: true,
      });
      // Un Set pour éviter les doublons de blockId
      const uniqueCompletedBlockIds = new Set(allProgressLogs.map(log => log.blockId));

      DEBUG_LOG && console.log('uniqueProgress.length', uniqueCompletedBlockIds.size, 'allBlockIds.length', allBlockIds.length);

      const allBlocksCompleted = uniqueCompletedBlockIds.size === allBlockIds.length;

      // Mettre à jour ueModuleProgress
      const ueModuleProgress = await ctx.models.UEModuleProgress.findOne({
        where: {
          userId,
          ueModuleId,
        },
      });
      ueModuleProgress.stepsCompleted = uniqueCompletedBlockIds.size; // on met à jour le nombre de steps complétés
      ueModuleProgress.totalSteps = allBlockIds.length; // on met à jour le nombre total de steps
      ueModuleProgress.completed = allBlocksCompleted; // on met à jour le statut de complétion du module
      await ueModuleProgress.save();

      // Si tous les blocks ont des logs complétés, on valide le module (il faut que 100% des blocks soient validés)
      if (allBlocksCompleted) {
        DEBUG_LOG && console.log(`All blocks are completed for module ${ueModuleId} for user ${userId}.`);
        // On valide le module
        const isModuleValidated = await this.setModuleProgressCompleted({ userId, ueModuleId }, ctx);
        if (isModuleValidated) {
          DEBUG_LOG && console.log(`Module ${ueModuleId} for user ${userId} has been validated after completing all elements.`);
        } else {
          console.error(`Failed to validate module ${ueModuleId} for user ${userId}.`);
        }
      } else {
        DEBUG_LOG && console.log(`Not all blocks are completed for module ${ueModuleId} for user ${userId}.`);
      }

    } catch (e) {
      console.error('afterModuleElementCompleted error:', e);
    }
  },

  ///////
  async setModuleProgressCompleted({ userId, ueModuleId }, ctx) {
    try {
      // marque le module comme complété
      const existingUEModuleProgress = await ctx.models.UEModuleProgress.findOne({
        where: {
          userId,
          ueModuleId,
        },
      });
      if (!existingUEModuleProgress) {
        console.error('ERROR No progress found for user and module');
        return false;
      } else {
        existingUEModuleProgress.completed = true;
        await existingUEModuleProgress.save();
        return true;
      }
    } catch (e) {
      console.error('setModuleProgressCompleted error:', e);
    }
  },

};