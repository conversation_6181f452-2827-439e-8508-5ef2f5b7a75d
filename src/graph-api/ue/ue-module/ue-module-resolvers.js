import { combineResolvers } from 'graphql-resolvers';
import { isAdmin, isAuthenticated } from '../../authorization';
import { UEModuleService } from './ue-module-service';

export default {
  Query: {

    countUEModules: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => UEModuleService.countUEModules(args, ctx)),

    ueModules: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx, info) => UEModuleService.getUEModules(args, ctx)),

    ueModuleProgressInUE: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => {
        return UEModuleService.getUEModuleProgressInUE(args, ctx);
      },
    ),

    ueModuleProgress: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => {
        return UEModuleService.getUEModuleProgress(args, ctx);
      },
    ),
  },
  Mutation: {
    createUEModule: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => UEModuleService.createUEModule(args, ctx),
    ),
    updateUEModule: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => UEModuleService.updateUEModule(args, ctx),
    ),
    deleteUEModule: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => UEModuleService.deleteUEModule(args, ctx),
    ),

    saveTimeSpentOnModule: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => UEModuleService.saveTimeSpentOnModule(args, ctx),
    ),
    handleUserDoUEModule: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => UEModuleService.handleUserDoUEModule(args, ctx),
    ),

    handleNextUEModule: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => UEModuleService.handleNextUEModule(args, ctx),
    ),
  },

  UEModule: {
    countSteps: (parent, args, ctx) => {
      return UEModuleService.countStepsInModule(parent, ctx);
    }
  }
};
