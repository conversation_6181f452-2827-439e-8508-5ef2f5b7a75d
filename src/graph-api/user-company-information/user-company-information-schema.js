import gql from 'graphql-tag'

export default gql`
  extend type Mutation{
    # CRUD Many to Many associations 
    "Multiples Associations creation or updates"
    createOrAddUsersToCompaniesAssociation(usersIds:[ID]=[],companiesIds:[ID]=[]): <PERSON><PERSON>an
    
    "Delete an association from associationId"
    deleteUsersToCompaniesAssociationFromAssociationIds(associationsIds:[ID]):Boolean
    
    "Delete an association from userIds and CompaniesIds depending of constraint ('both','company','user'). The constraint inform on how to uses Id to delete"
    deleteUsersToCompaniesAssociationFromUsersAndCompaniesId(usersIds:[ID]=[],companiesIds:[ID]=[],constraint:String="both"): Boolean
    
    "Replace the associations for given users (remove associations for the users, then relink them"
    replaceUsersToCompaniesAssociationFromUsersAndCompaniesId(usersIds:[ID]=[],companiesIds:[ID]=[]):Boolean

    "Replace the associations for given users / groupsIds "
    addOrDeleteOrReplaceUsersAndGroupsToCompaniesAssociations(type:String!,groupsIds:[ID]!,usersIds:[ID]!,targetCompaniesIds:[ID]!):Boolean
  }
  
  type CompanyDescription {
    companyConfigId:ID
    companyName:String
  }
`