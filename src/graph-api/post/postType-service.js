import models from '../../models/index.js'
import { UploadService } from '../file/upload-service.js'


export const PostTypeService = {
  async createPostType(postType, id) {
    try {
      if (postType.image) {
        postType.image = await UploadService.uploadFile(postType.image)
      }
      const obj = await models.PostType.create(postType)
      return obj
    } catch (e) {
      console.error(e)
    }
  },
  async updatePostType(id, postType, id2) {

    let oldPostType = await models.PostType.findByPk(id);
    if (postType?.image?.shouldDelete) {
      if (oldPostType) {
        await UploadService.deleteFile(oldPostType.image);
      }
      postType.image = null;
    } else {
      postType.image = await UploadService.uploadFile(postType.image)
    }
    const returnedValue = await models.PostType.update(postType, { where: { id } })
    return returnedValue[0]
  },
  async deletePostType(id, id2) {
    return models.PostType.destroy({ where: { id } })
  },

  getPostTypes(args, ctx) {
    return models.PostType.findAll()
  },

}