import { combineResolvers } from 'graphql-resolvers'
import {
  isAdmin,
  isAuthenticated,
  isPostOwnerOrAdminOrTutor,
  isTuteurOrAdmin,
  messageNotEmpty,
} from '../authorization';
import { QCMService } from '../qcm/qcm-service.js';
import { PostService } from './post-service';
import { PostTypeService } from './postType-service.js';

export default {
  Query: {
    posts: combineResolvers(
      isAdmin,
      async (parent, args, { models }) => await models.Post.findAll()),

    post: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id }, { models, me }) => models.Post.findByPk(id)),

    unresolvedForumPosts: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id }, { models, me }) => PostService.getUnresolvedPostsInForum(me?.id)),

    allUEsUnresolvedPosts: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id }, { models, me }) => PostService.getAllUnresolvedPostsUEs(me)),


    postTypes: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => PostTypeService.getPostTypes(args, ctx),
    ),

    userThreadPosts: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => PostService.getUserThreadPosts(args, ctx),
    ),
    userAnswerPosts: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => PostService.getUserAnswerPosts(args, ctx),
    ),

    getDefaultBotIdToAutoAnswerForPost: combineResolvers(
      isAuthenticated,
      async (parent, { postId }, ctx) => PostService.getDefaultBotIdToAutoAnswerForPost({postId}, ctx),
    ),

    postsForCours: combineResolvers(
      isAuthenticated,
      async (parent, { coursId, filter }, { me }, info) => PostService.getPostsFor(coursId, me.id, 'cours', filter)),
    postsForEvent: combineResolvers(
      isAuthenticated,
      async (parent, { eventId, filter }, { me }, info) => PostService.getPostsFor(eventId, me.id, 'event', filter)),
    postsForQcm: combineResolvers(
      isAuthenticated,
      async (parent, { qcmId, filter }, { me }) => await PostService.getPostsFor(qcmId, me.id, 'qcm', filter)),
    postsForForum: combineResolvers(
      isAuthenticated,
      async (parent, { forumId, filter }, { me }) => await PostService.getPostsFor(forumId, me.id, 'forum', filter)),
    postsForAnswer: combineResolvers(
      isAuthenticated,
      async (parent, { answerId, filter }, { me }) => await PostService.getPostsFor(answerId, me.id, 'answer', filter)),
    postsForExercise: combineResolvers(
      isAuthenticated,
      async (parent, { questionId, filter }, { me }) => await PostService.getPostsFor(questionId, me.id, 'question', filter)),
    postsForAnswersInQcm: combineResolvers(
      isAuthenticated,
      async (parent, { qcmId, filter }, { me }) => await PostService.getPostsFor(qcmId, me.id, 'answers', filter)),


    getYearsAvailableForPostsForCours: combineResolvers(
      isAuthenticated,
      async (parent, { coursId }, { me }) => await PostService.getYearsAvailableForPostsFor(coursId, me.id, 'cours')),
    getYearsAvailableForPostsForEvent: combineResolvers(
      isAuthenticated,
      async (parent, { eventId }, { me }) => await PostService.getYearsAvailableForPostsFor(eventId, me.id, 'event')),
    getYearsAvailableForPostsForQcm: combineResolvers(
      isAuthenticated,
      async (parent, { qcmId }, { me }) => await PostService.getYearsAvailableForPostsFor(qcmId, me.id, 'qcm')),
    getYearsAvailableForPostsForForum: combineResolvers(
      isAuthenticated,
      async (parent, { forumId }, { me }) => await PostService.getYearsAvailableForPostsFor(forumId, me.id, 'forum')),


    latestPosts: combineResolvers(
      isAuthenticated,
      async (parent, args, { me }) => PostService.getLatestsVisiblePosts(me.id)),

    adminSearchPosts : combineResolvers(
      isTuteurOrAdmin,
      async (parent,arg,ctx) => PostService.adminSearchPosts(parent,arg,ctx)
    ),

    getPostAdditionalInfos:combineResolvers(
      isAuthenticated,
      async (parent, arg)=>PostService.getPostAdditionalInfos(parent,arg)
    ),

    getPostReactions: combineResolvers(
      isAuthenticated,
      async (parent, { postId }, ctx) => PostService.getPostReactions({postId}, ctx),
    ),
  },

  Mutation: {
    /* CRUD POST */
    createPost: combineResolvers(
      isAuthenticated, messageNotEmpty,
      async (parent, { post }, { me, ip }) => await PostService.createPost(post, me.id, ip),
    ),
    updatePost: combineResolvers(
      isAuthenticated,
      isPostOwnerOrAdminOrTutor,
      messageNotEmpty,
      async (parent, { id, post }, { me }) => await PostService.updatePost(id, post, me.id),
    ),
    deletePost: combineResolvers(
      isAuthenticated,
      isPostOwnerOrAdminOrTutor,
      async (parent, { id }, { me }) => await PostService.deletePost(id, me.id),
    ),
    /* CRUD POST TYPE */
    createPostType: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { postType }, { me }) => await PostTypeService.createPostType(postType, me.id),
    ),
    updatePostType: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id, postType }, { me }) => await PostTypeService.updatePostType(id, postType, me.id),
    ),
    deletePostType: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id }, { me }) => await PostTypeService.deletePostType(id, me.id),
    ),
    generateAiAnswerForPost: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await PostService.generateAiAnswerForPost(args, ctx),
    ),

    postReaction: combineResolvers(
      isAuthenticated,
      async (parent, { postId, emoji }, ctx) => await PostService.postReaction({postId, emoji}, ctx),
    ),

    likePost: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { me }) => await PostService.likePost(id, me.id),
    ),
    dislikePost: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { me }) => await PostService.dislikePost(id, me.id),
    ),

    reportPost: combineResolvers(
      isAuthenticated,
      async (parent, { input }, { me }) => await PostService.reportPost(input, me.id),
    ),

    giveFeedbackToAiAnswer: combineResolvers(
      isAuthenticated,
      async (parent, { input }, { me }) => await PostService.giveFeedbackToAiAnswer(input, me.id),
    ),

    setAiAnswerVerified: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { postId }, { me }) => await PostService.setAiAnswerVerified(postId, me.id),
    ),


  },

  ///// CHILDS
  Post: {
    cours: combineResolvers(
      isAuthenticated,
      async (post, data, ctx) => {
        if(post?.cours?.id) {
          return post.cours;
        } else {
          if(post.courId === null) {
            return null;
          }
          return await ctx.models.Cours.findByPk(post.courId);
        }
      }
    ),
    forum: combineResolvers(
      isAuthenticated,
      async (post, data, ctx) => {
        if(post?.forum?.id) {
          return post.forum;
        } else {
          if(post.forumId === null) {
            return null;
          }
          return await ctx.models.Forum.findByPk(post.forumId);
        }
      }
    ),
    qcm: combineResolvers(
      isAuthenticated,
      async (post, data, ctx) => {
        if(post?.qcm) {
          return post.qcm;
        } else {
          if(post?.answerId) {
            const answer = await ctx.models.QuestionAnswers.findByPk(post.answerId);
            if(!answer) {
              return null;
            }
            const qcm = await QCMService.getFirstQcmIdHavingQuestion(answer.questionId);
            return qcm;
          }
          return null;
        }
      }
    ),

    user: combineResolvers(
      isAuthenticated,
      async (post, data, { loaders }) => await loaders.user.load(post.userId),
    ),
    file: combineResolvers(
      isAuthenticated,
      async (post, data, ctx) => await post.getFile(),
    ),
    fileImage: combineResolvers(
      isAuthenticated,
      async (post, data, ctx) => await post.getFileImage(),
    ),
    fileList: combineResolvers(
      isAuthenticated,
      async (post, data, ctx) => await PostService.getFiles(post, ctx),
    ),
    threadId: combineResolvers(
      isAuthenticated,
      async (post, data, ctx) => await PostService.getThreadId(post),
    ),
    threadPost: combineResolvers(
      isAuthenticated,
      async (post, data, ctx) => await PostService.getThreadPost(post),
    ),
    type: combineResolvers(
      isAuthenticated,
      async (post, data, { models }) => post && models.PostType.findByPk(post.postTypeId),
    ),

    answer: combineResolvers(
      isAuthenticated,
      async (post, data, { models }) => post && models.QuestionAnswers.findByPk(post?.answerId),
    ),

    approuvedResponse: combineResolvers(
      isAuthenticated,
      async (post,arg,ctx) => {
        const { post: lastResponseFromNonUserOrBot, role:lastResponseRole} = await PostService.lastReponseFromNonUserOrBot(null, { postId: post.id });
        return { lastResponseFromNonUserOrBot, lastResponseRole };
      }
    )
  },
};
