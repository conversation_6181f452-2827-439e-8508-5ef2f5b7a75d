
const INSTRUCTIONS_FOR_RENDERING = "" +
  "<Consignes>" +
  "IMPORTANT :\n" +
  "1. Réponds directement à la question. Ne commence jamais par '[ExoGPT - IA]' ou autre tag inutile.\n"+
  "2. Si tu dois mettre des équations (Latex, Katex), il faut absolument les mettre entre '$' pour que le rendu soit correct. " +
  "3. N'insères PAS d'image(s) dans ta réponse. " +
  "4. Réponds strictement en texte brut, par exemple: \"Réponse blabla\nVoici l'équation: $x^3+4=0$\" " +
  "5. S'il y a une ou des images analyse les avant de répondre." +
  "</Consignes>";

  export function getPromptTemplateForSettings(promptName, settings, DEBUG) {
    // Hardcoded templates
    if(promptName === 'exerciseQuestion') {
      return `
<Personnalité>
{{botPersonnalityPromptFragment}}
</Personnalité>
<Objectif>
Tu dois répondre à la question posée par {{pseudoUser}}.
Sa question porte sur un exercice dont le thème est {{array_linkedCoursesString}}. 
Analyse l’exercice avant de répondre à la question.
{{precisionPromptForChatGPT}}
Important: la question de {{pseudoUser}} porte en particulier sur : {{currentAnswerAndCorrectionString}}
</Objectif>

<Exercice>
L’exercice sur lequel porte la question est un {{questionTypeString}}

{{enonceQuestion}}
{{answersToString}}

{{footerElementsCorrectionString}}

</Exercice>

${INSTRUCTIONS_FOR_RENDERING}
            `;
    }

// SCHEMAS
    if(promptName === 'exerciseSchema') {
      return `
<Personnalité>
{{botPersonnalityPromptFragment}}
</Personnalité>
<Objectif>
Tu dois répondre à la question posée par {{pseudoUser}}.
Sa question porte sur un schéma du cours {{schemaLinkedCoursesString}}. 
Ce schéma nommé {{schemaName}} contient les légendes suivantes : {{legendsNamesString}}.
{{precisionPromptForChatGPT}}
</Objectif>
${INSTRUCTIONS_FOR_RENDERING}
`;
    }
    if(promptName === 'exerciseSchemaFillInLegends') {
      return `
<Personnalité>
{{botPersonnalityPromptFragment}}
</Personnalité>
<Objectif>
Tu dois répondre à la question posée par {{pseudoUser}}.
Sa question porte sur un schéma du cours {{schemaLinkedCoursesString}}. 
Ce schéma nommé {{schemaName}} contient les légendes suivantes : {{legendsNamesString}}.
Le but de l'exercice pour l'utilisateur est de nommer correctement les légendes du schéma.
{{precisionPromptForChatGPT}}
</Objectif>
${INSTRUCTIONS_FOR_RENDERING}
`;
    }


    // TEXTE A TROUS

    if(promptName === 'fillintheblanks') {
      return `
<Personnalité>
{{botPersonnalityPromptFragment}}
</Personnalité>
<Objectif>
Voici un exercice de type "Texte à trou" sur le thème de {{array_linkedCoursesString}}.
La correction des trous est écrit entre symbole étoiles (*). 
Par exemple "Le ciel est de couleur *bleu*" signifie que *bleu* était un trou à remplir avec "bleu" comme bonne réponse. 
Il peut y avoir plusieurs réponses justes possibles séparés par le caractère "/". 
Il peut également y avoir des indices après les deux points (:).
Texte à trou corrigé :
{{fillInTheBlanksText}}

Dans ta réponse, ne retourne pas les symboles étoiles (*) ni les indices après les deux points (:).
</Objectif>
${INSTRUCTIONS_FOR_RENDERING}
`;
    }

    // REORDER ELEMENTS
    if(promptName === 'exerciseReorderElements') {
      return `
<Personnalité>
{{botPersonnalityPromptFragment}}
</Personnalité>
<Objectif>
Tu dois répondre à la question posée par {{pseudoUser}}.
Sa question porte sur un exercice de remise en ordre sur le thème de {{array_linkedCoursesString}}.
L'exercice consiste à remettre des éléments dans le bon ordre.
{{precisionPromptForChatGPT}}
</Objectif>

<Exercice>
Énoncé de l'exercice : {{enonceQuestion}}

Éléments à remettre en ordre :
{{reorderElementsContent}}

Ordre correct attendu :
{{reorderElementsCorrectOrder}}
</Exercice>

${INSTRUCTIONS_FOR_RENDERING}
`;
    }

    DEBUG && console.log({ promptName });

    /* PROMPT QUESTION COURS */
    if(promptName === 'courseQuestion') {
      return `
<Personnalité>
{{botPersonnalityPromptFragment}}
</Personnalité>
<Objectif>
Tu dois répondre à la question posée par {{pseudoUser}}.
Sa question porte sur un cours dont le thème est {{coursName}} {{coursDescription}}. 
{{additionnalPrompt}}
{{precisionPromptForChatGPT}}
</Objectif>
${INSTRUCTIONS_FOR_RENDERING}
`;
    }

    if (settings && settings.templates && settings.templates[promptName]) {
      return settings.templates[promptName];
    }
  }

