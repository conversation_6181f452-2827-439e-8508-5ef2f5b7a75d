import { combineResolvers } from 'graphql-resolvers'
import { isAdmin, isAuthenticated } from '../authorization'
import { ForumService } from './forum-service.js'

'use strict'

export default {
  Query: {
    forumCategory: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { me }) => {
        return await ForumService.getForumCategory(id, me)
      },
    ),

    forumCategories: combineResolvers(
      isAuthenticated,
      async (parent, { parentId }, { me }) => {
        return await ForumService.getForumCategoriesIn(parentId, me)
      },
    ),
  },

  Mutation: {
    createForumCategory: combineResolvers(
      isAdmin,
      async (parent, { forumCategory }, { me }) => {
        return await ForumService.createForumCategory(forumCategory, me)
      },
    ),
    updateForumCategory: combineResolvers(
      isAdmin,
      async (parent, { id, forumCategory }, { me }) => {
        return await ForumService.updateForumCategory(id, forumCategory)
      },
    ),
    deleteForumCategory: combineResolvers(
      isAdmin,
      async (parent, { id }, { models, me }) => {
        return await ForumService.deleteForumCategory(id)
      },
    ),

    createForum: combineResolvers(
      isAdmin,
      async (parent, { forum }, { models, me }) => {
        return await ForumService.createForum(forum, me)
      },
    ),
    updateForum: combineResolvers(
      isAdmin,
      async (parent, { id, forum }, { models, me }) => {
        return await ForumService.updateForum(id, forum)
      },
    ),
    deleteForum: combineResolvers(
      isAdmin,
      async (parent, { id }, { models, me }) => {
        return await ForumService.deleteForum(id)
      },
    ),
  },

  Forum: {
    lastPost: combineResolvers(
      isAuthenticated,
      async (forum, _, { models, me }, info) => {
        info.cacheControl.setCacheHint({ maxAge: 5, scope: 'PRIVATE' })
        return models.Post.findOne({
          where: {
            forumId: forum.id
          },
          order: [['updatedAt', 'DESC']],
        })
      },
    ),
    postsNumber: combineResolvers(
      isAuthenticated,
      async (forum, _, { models, me }, info) => {
        info.cacheControl.setCacheHint({ maxAge: 10, scope: 'PUBLIC' })
        return forum.countPosts()
      },
    ),
  },

  ForumCategory: {
    parent: combineResolvers(
      isAuthenticated,
      async (forumCategory, _, { models, me }) => {
        return await forumCategory.getForum()
      },
    ),
    forums: combineResolvers(
      isAuthenticated,
      async (forumCategory, _, { models, me }) => {
        return await forumCategory.getForums()
      },
    ),
  },
}
