import gql from 'graphql-tag'

export default gql`

  extend type Query {
    isScormPackageCompletedForUser(formationElementId:ID!,userId:ID!):Boolean
  }


  extend type Mutation {
    uploadScormPackage:Boolean
    
    initScormRecord(initScormRecord:InitScormRecord):Boolean
  }
  
  input CommitInput{
    data:JSON!
  }
  
  input InitScormRecord{
    frontId:ID! 
    formationElementId:ID!
    userId:ID!
    scormFilePath:String! 
    scormEntryPoint:String!
  }
`