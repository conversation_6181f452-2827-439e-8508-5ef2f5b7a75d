import { GraphQLError } from 'graphql'
import { performance } from 'perf_hooks'
import { start } from 'repl'
import { col, fn, literal, Op, Sequelize } from 'sequelize'
import group from 'underscore/modules/_group.js'
import models from '../../models/index.js'
import { PermissionService } from '../permission/permission-service.js'
import { isDev } from '../../index.js';

export const AverageHistoryService = {

  decomposeDate:(dateInput)=>{
    /* Fonction qui prend une date et retourne l'année, le mois, la semaine et le jour */
    try {
      let date

      ////// Block de récupération de la dateInput en Date
      if (dateInput instanceof Date){ date = dateInput} // Si la date est du bon type
      else if (typeof dateInput === 'string'){ // Si on a une string en entrée, on essayes de la convertir
        const timeStamp=Date.parse(dateInput);
        if (isNaN(timeStamp)){return null}
        date=new Date(timeStamp)
      } else {
        throw new Error("the input is not a Date. Type : ",typeof(dateInput),"   Value :",JSON.stringify(dateInput))
        return null
      }


      ////// Split de la date en les différents champs
      const annee = date.getFullYear();
      const mois = date.getMonth() + 1; // Les mois sont indexés à partir de 0
      const jour = date.getDate();

      // Calcul de la semaine de l'année
      const jan1 = new Date(annee, 0, 1); // 1er janvier de l'année
      const millisecondesParJour = 86400000; // Nombre de millisecondes en un jour
      const semaine = Math.ceil((((date - jan1) / millisecondesParJour) + jan1.getDay() + 1) / 7);

      return { annee, mois, semaine, jour };
    }catch(e){
      console.error(e.message)
      throw new GraphQLError(e.message);
    }
  },

  recomposeDateFromWeek: (annee, semaine) =>{
    /* Fonction qui permet de retrouver la date à partir d'une anne (int) et d'un numéro de semaine (int) */
    try {

      const jan1 = new Date(annee, 0, 1); // 1er janvier de l'année
      const millisecondesParJour = 86400000; // Nombre de millisecondes en un jour
      const millisecondesParSemaine = 7 * millisecondesParJour;

      // Calculer le nombre total de millisecondes entre le 1er janvier et le premier jour de la semaine donnée
      const offset = (semaine - 1) * millisecondesParSemaine;

      // Ajuster à partir du premier jour de l'année
      const firstDayOfWeek = new Date(jan1.getTime() + offset);

      // Ajuster le jour de la semaine en fonction du jour du 1er janvier
      firstDayOfWeek.setDate(firstDayOfWeek.getDate() - firstDayOfWeek.getDay() + jan1.getDay());

      const year = firstDayOfWeek.getFullYear();
      const month = String(firstDayOfWeek.getMonth() + 1).padStart(2, '0');
      const day = String(firstDayOfWeek.getDate()).padStart(2, '0');

      return new Date(`${year}-${month}-${day}`);
    }catch(e){
      console.error(e.message)
      throw new GraphQLError(e.message)
    }
  },

  reconstructDataFromProgressNode: (progressNode)=>{
    try{
      if (progressNode?.jour){return new Date(`${progressNode.annee}-${String(progressNode.mois).padStart(2, '0')}-${String(progressNode.jour).padStart(2, '0')}`)}
      else if (progressNode?.semaine){return AverageHistoryService.recomposeDateFromWeek(progressNode?.annee,progressNode?.semaine)}
      else if (progressNode?.mois){return new Date(`${progressNode.annee}-${String(progressNode.mois).padStart(2, '0')}`)}
      else if (progressNode?.annee){return new Date(`${progressNode.annee}`)}
      else {return(new Date("2022-01-01"))} // On return une date par default
    }catch(e) {
      console.error(e)
    }
  },

  conditionalDateCondition:(dateDebut,dateFin)=>{
    /* Créée la condition sur le filter temporel */
    try{
      const isDateDebutValide= dateDebut instanceof Date && !isNaN(dateDebut) && dateDebut.getTime() !== 0; // !isNaN retourne true si la date est valide
      const isDateFinValide= dateFin instanceof Date && !isNaN(dateFin) && dateFin.getTime() !== 0; // !isNaN retourne true si la date est valide


      if (isDateFinValide && isDateFinValide){ return {[Op.between]:[dateDebut,dateFin]}}
      else if (isDateDebutValide) {return {[Op.gte]:dateDebut}}
      else if (isDateFinValide) {return {[Op.lte]:dateFin}}
      else {return ({ [Op.not]:null})} // Si on appelle la fonction, on veut forcément avoir des entrées qui ont des dates
    }catch(e) {
      console.error(e)
      return ({ [Op.not]:null})
    }
  },

  userProgress:async(coursIdArray,userId,bin,dateDebut,dateFin)=>{
    /* Fonction très importante qui récupère les résultats de l'utilisateur en fonction des bin et de la date de début / fin */
    try {
      const IMPORTED_TYPE_COURSE='imported'

      ////// Determination dynamique des colonnes en fonction de bin
      const mapping = {
        'annee': [{unit: 'YEAR', frenchName: 'annee'}],
        'mois': [{unit: 'YEAR', frenchName: 'annee'}, {unit: 'MONTH', frenchName: 'mois'}],
        'semaine': [{unit: 'YEAR', frenchName: 'annee'}, {unit: 'MONTH', frenchName: 'mois'}, {unit: 'WEEK', frenchName: 'semaine'}],
        'jour': [{unit: 'YEAR', frenchName: 'annee'}, {unit: 'MONTH', frenchName: 'mois'}, {unit: 'WEEK', frenchName: 'semaine'}, {unit: 'DAY', frenchName: 'jour'}],
        'average': []
      };
      const table = `${models.QcmStatsQuestion.tableName}.createdAt`;
      const groupByColumns = mapping[bin].map(item => fn(item.unit, col(table)));
      const attributesColumns = mapping[bin].map(item => [fn(item.unit, col(table)), item.frenchName.toLowerCase()]);

      ////// requête
      const filterDate=AverageHistoryService.conditionalDateCondition(new Date(dateDebut), new Date(dateFin))
      const result = await models.QcmStatsQuestion.findAll({
        where: {
          id_utilisateur: userId,
          ...(filterDate ? { createdAt: filterDate } : {})
        },
        include: [
          {
            model: models.Question,
            as: 'question',
            attributes: [],
            required: true,
            include: [
              {
                model: models.Cours,
                as: 'cours',
                attributes: [],
                required: true,
                where: {
                  id: coursIdArray,
                  [Op.or]:[
                    {type : IMPORTED_TYPE_COURSE},
                    {type:null}
                  ]
                },
                through: {
                  model: models.QuestionCours,
                  attributes:[]
                }
              }
            ]
          }
        ],
        attributes: [
          ...attributesColumns,
          [fn('AVG', col('pointsObtained')), 'moyenne'],
          [fn('COUNT', '*'), 'nbResults'],
        ],
        group: [...groupByColumns],
        order: groupByColumns.map(col => [col, 'ASC']),
        raw: true
      });
      return result
    }catch(e){
      console.error(e)
      return []
    }
  },

  structureProgress:async(coursIdArray,me,bin,dateDebut,dateFin)=>{
    /* Fonction très importante qui récupère les résultats d'une liste de cours en fonction de bin et date de début/fin */
    try{

      /* Transformer les dates de début et fin en année,mois,jours */
      const mapping = {
        'annee': ['annee'],
        'mois': ['annee', 'mois'],
        'semaine': ['annee', 'mois', 'semaine'],
        'jour': ['annee', 'mois', 'semaine', 'jour'],
        'average': []
      };

      const groupByColumns = mapping[bin];
      const filterDate=AverageHistoryService.conditionalDateCondition(new Date(dateDebut), new Date(dateFin))
      const temp = await models.AverageHistory.findAll({
        where:{
          coursId:coursIdArray,
          ...(filterDate ? { date: filterDate } : {})
        },
        group: groupByColumns,
        attributes: [
          [Sequelize.literal('SUM(`average` * `nbResults`) / SUM(`nbResults`)'), 'moyenne'],
          [Sequelize.literal('CAST(SUM(`nbResults`) AS UNSIGNED)'), 'nbResults'],
          ...groupByColumns
        ],
        order:groupByColumns.map(col=>[col,'ASC']),
        raw: true
      });
      return temp
    }catch(e){
      return []
    }
  },

  progressAnalyse:async({ ueId, categoryId, coursId, bin, dateDebut, dateFin ,userId: targetUserId},me)=>{
    try {
      const userId = targetUserId || me.id;
      const user = await models.User.findByPk(userId);

      ////// Récupération des progression dans les databases pour (UE, category, Cours)
      const uePromises=ueId ? ueId?.map(async id => {
        const ueCoursId = await PermissionService.getAvailableCoursIdsInUEForUser(id,user);
        const userProgress = await AverageHistoryService.userProgress(ueCoursId,userId,bin, dateDebut, dateFin);
        const structureProgress = await AverageHistoryService.structureProgress(ueCoursId,userId,bin,dateDebut,dateFin);
        const ue=await models.UE.findByPk(id)

        return { type: "ue", id: id, isFolder:ue?.isFolder, name:ue?.name,description:ue?.description,image:ue?.image,userProgress,structureProgress};
      }) : []

      const categoryPromises=categoryId ? categoryId?.map(async id=>{
        const categoryCoursId=await PermissionService.getAvailableCoursIdsInUECategoryForUserRecursive(id, user)
        const userProgress = await AverageHistoryService.userProgress(categoryCoursId,userId,bin,dateDebut,dateFin)
        const structureProgress = await AverageHistoryService.structureProgress(categoryCoursId,userId,bin,dateDebut,dateFin);
        const category = await models.UECategory.findByPk(id)
        const isFolder = await models.UECategory.findAll({where: { parentId: category?.id },order: [['order', 'ASC']] }).then(categories => categories.length > 0 ? true:false);

        return {type:"category",id:id,isFolder:isFolder,name:category?.name,description:category?.description,image:category?.image,userProgress,structureProgress}
      }) : []


      const coursPromises=coursId ? coursId?.map(async id =>{
        const userProgress = await AverageHistoryService.userProgress(id,userId,bin,dateDebut,dateFin)
        const structureProgress = await AverageHistoryService.structureProgress(id,userId,bin,dateDebut,dateFin);
        const cours = await models.Cours.findByPk(id)


        // Si on est un importedCourse (avec targetCoursId) alors on récup les données depuis le targetCoursId
        if (cours.targetCoursId){
          const tc=await models.Cours.findByPk(cours.targetCoursId)

          // Si le target Cours est bien présent, alors on retourne une mixture du cours importé et les propriétées du cours original (image, nom, description, etc...)
          if (tc){
            return {type:"cours",id:id,isFolder:false,name:tc?.name,description:tc?.text,userProgress,image:tc?.customImage,structureProgress}
          }
        }

        // TODO probable erreure ici : Mitigé par le HiérarchySelecteur qui empèche de sélectionner des cours. Si on a un step, alors on devra resolve avec des champs undefined
        if (cours.step){
          return Promise.resolve({})} // filtre les cours des pages
        return {type:"cours",id:id,isFolder:false,name:cours?.name,description:cours?.text,userProgress,image:cours?.customImage,structureProgress}
      }) : []


      const results = await Promise.all([...uePromises,...categoryPromises,...coursPromises]);


      ////// reconstruction de la date
      function reconstructDate(data){
        data.forEach(progressNode => {
          progressNode.modifiedDate=AverageHistoryService.reconstructDataFromProgressNode(progressNode)
        })
      }


      // Itération sur chaque strucutre (résultats d'une UE, d'une Catégorie, d'un cours)
      for (const structure of results){

        // On fait la moyenne de la structure
        if (structure?.structureProgress){
          reconstructDate(structure?.structureProgress)
        }

        if (structure?.userProgress){
          reconstructDate(structure?.userProgress)
        }
      }
      return results;
    }catch(e){
      console.log("error :",e)
      throw new GraphQLError("Erreur, veuillez réessayer plus tard. Error :",e);
    }
  },

  resumeAnalyse:async ({ ueId, categoryId, coursId, dateDebut, dateFin ,userId: targetUserId},me)=>{
    /* Fonction qui retourne les averages de structure et user pour la sélection choisie. Pas de notion de bin ducoup */
    try {
      const userId = targetUserId || me.id;
      const user = await models.User.findByPk(userId);

      ////// Récupération des progression dans les databases pour (UE, category, Cours)
      const uePromises=ueId ? ueId?.map(async id => {
        const ueCoursId = await PermissionService.getAvailableCoursIdsInUEForUser(id,user);
        const userResume = await AverageHistoryService.userProgress(ueCoursId,userId,"average", dateDebut, dateFin);
        const structureResume = await AverageHistoryService.structureProgress(ueCoursId,userId,"average",dateDebut,dateFin);
        const ue=await models.UE.findByPk(id)

        return { type: "ue", id: id, isFolder:ue?.isFolder, name:ue?.name,description:ue?.description,image:ue?.image,userResume,structureResume};
      }) : []

      const categoryPromises=categoryId ? categoryId?.map(async id=>{
        const categoryCoursId=await PermissionService.getAvailableCoursIdsInUECategoryForUserRecursive(id, user);
        const userResume = await AverageHistoryService.userProgress(categoryCoursId,userId,"average",dateDebut,dateFin)
        const structureResume = await AverageHistoryService.structureProgress(categoryCoursId,userId,"average",dateDebut,dateFin);
        const category = await models.UECategory.findByPk(id)
        const isFolder = await models.UECategory.findAll({where: { parentId: category?.id },order: [['order', 'ASC']] }).then(categories => categories.length > 0 ? true:false);

        return {type:"category",id:id,isFolder:isFolder,name:category?.name,description:category?.description,image:category?.image,userResume,structureResume}
      }) : []


      const coursPromises=coursId ? coursId?.map(async id =>{
        const userResume = await AverageHistoryService.userProgress(id,userId,"average",dateDebut,dateFin)
        const structureResume = await AverageHistoryService.structureProgress(id,userId,"average",dateDebut,dateFin);
        const cours = await models.Cours.findByPk(id)

        // Si on est un importedCourse (avec targetCoursId) alors on récup les données depuis le targetCoursId
        if (cours.targetCoursId){
          const tc=await models.Cours.findByPk(cours.targetCoursId)

          // Si le target Cours est bien présent, alors on retourne une mixture du cours importé et les propriétées du cours original (image, nom, description, etc...)
          if (tc){
            return {type:"cours",id:id,isFolder:false,name:tc?.name,description:tc?.text,userResume,image:tc?.customImage,structureResume}
          }
        }

        // TODO probable erreure ici : Mitigé par le HiérarchySelecteur qui empèche de sélectionner des cours. Si on a un step, alors on devra resolve avec des champs undefined
        if (cours.step){
          return Promise.resolve({})} // filtre les cours des pages
        return {type:"cours",id:id,isFolder:false,name:cours?.name,description:cours?.text,userResume,image:cours?.customImage,structureResume}
      }) : []


      const results = await Promise.all([...uePromises,...categoryPromises,...coursPromises]);

      /* Flatten des results, car on est pas en progress là */
      return results.map(value => ({...value, userResume: value.userResume[0], structureResume: value.structureResume[0]}));


    }catch(e){
      console.log("error")
      throw new GraphQLError("Erreur, veuillez réessayer plus tard");
    }
  },

  fullAnalyse:async ({ ueId, categoryId, coursId, bin, dateDebut, dateFin ,userId: targetUserId},me)=>{
    /* Fonction qui récupère toute l'analyse */
    // Y a -t-il besoin de faire une vérification sur le fait que l'user a accès à l'UE ou pas ?

    const userId = targetUserId || me.id;
    const user = await models.User.findByPk(userId);

    try {
      ////// Récupération des progression dans les databases pour (UE, category, Cours)
      const uePromises=ueId ? ueId?.map(async id => {
        const ueCoursId = await PermissionService.getAvailableCoursIdsInUEForUser(id,user);
        const userProgress = await AverageHistoryService.userProgress(ueCoursId,userId,bin, dateDebut, dateFin);
        const structureProgress = await AverageHistoryService.structureProgress(ueCoursId,userId,bin,dateDebut,dateFin);
        const ue=await models.UE.findByPk(id)

        return { type: "ue", id: id, isFolder:ue?.isFolder, name:ue?.name,description:ue?.description,image:ue?.image,userProgress,structureProgress};
      }) : []

      const categoryPromises=categoryId ? categoryId?.map(async id=>{
        const categoryCoursId=await PermissionService.getAvailableCoursIdsInUECategoryForUserRecursive(id, user)
        const userProgress = await AverageHistoryService.userProgress(categoryCoursId,userId,bin,dateDebut,dateFin)
        const structureProgress = await AverageHistoryService.structureProgress(categoryCoursId,userId,bin,dateDebut,dateFin);
        const category = await models.UECategory.findByPk(id)
        const isFolder = await models.UECategory.findAll({where: { parentId: category?.id },order: [['order', 'ASC']] }).then(categories => categories.length > 0 ? true:false);

        return {type:"category",id:id,isFolder:isFolder,name:category?.name,description:category?.description,image:category?.image,userProgress,structureProgress}
      }) : []


      const coursPromises=coursId ? coursId?.map(async id =>{
        const userProgress = await AverageHistoryService.userProgress(id,userId,bin,dateDebut,dateFin)
        const structureProgress = await AverageHistoryService.structureProgress(id,userId,bin,dateDebut,dateFin);
        const cours = await models.Cours.findByPk(id)

        // On check si le cours a un targetCoursId, autrement dit, si il est la cible d'une importation, et on récupère les éléments depuis le cours
        if (cours.targetCoursId){
          const tc=await models.Cours.findByPk(cours.targetCoursId)

          // Si le target Cours est bien présent, alors on retourne une mixture du cours importé et les propriétées du cours original (image, nom, description, etc...)
          if (tc){
            return {type:"cours",id:id,isFolder:false,name:tc?.name,description:tc?.description,userProgress,image:tc?.customImage,structureProgress}
          }
        }

        // TODO probable erreure ici : Mitigé par le HiérarchySelecteur qui empèche de sélectionner des cours. Si on a un step, alors on devra resolve avec des champs undefined
        if (cours.step){
          return Promise.resolve({})} // filtre les cours des pages
        return {type:"cours",id:id,isFolder:false,name:cours?.name,description:cours?.text,userProgress,image:cours?.customImage,structureProgress}
      }) : []


      const results = await Promise.all([...uePromises,...categoryPromises,...coursPromises]);

      ////// Computation des métriques . La date reconstruite des bins de progression + la moyenne average
      function iterateOverProgress(data){
        let note=0;
        let nbResults=0;
        data.forEach(progressNode => {
          note+=(progressNode.moyenne*progressNode.nbResults)
          nbResults+=progressNode.nbResults
          progressNode.modifiedDate=AverageHistoryService.reconstructDataFromProgressNode(progressNode)
        })
        data.moyenne= nbResults !== 0 ? note / nbResults : 0; // calcul de la moyenne pondérée et retourne -1 si pas de nb
        return {moyenne:data.moyenne,nbResults}
      }


      // Itération sur chaque strucutre (résultats d'une UE, d'une Catégorie, d'un cours)
      for (const structure of results){

        // On fait la moyenne de la structure
        if (structure?.structureProgress){
          const {moyenne:structureAverage,nbResults:structureNbResults}=iterateOverProgress(structure?.structureProgress)
          structure.structureResume={moyenne:structureAverage,nbResults:structureNbResults}
        }

        if (structure?.userProgress){
          const {moyenne:userAverage,nbResults:userNbResults}=iterateOverProgress(structure?.userProgress)
          structure.userResume={moyenne:userAverage,nbResults:userNbResults}
        }
      }
      return results;
    }catch(e){
      throw new GraphQLError("Erreur, veuillez réessayer plus tard");
    }
  },

  flushAverageHistory:async(passWord)=>{
    /* Fonction qui permet de flush la table AverageHistory. Demande un password codé en dur pour vérification et safitude du code*/

    if (isDev && passWord==="passWordResetAverageTable" ){
      await models.AverageHistory.count().then(value => console.log("il y a ",value," lignes"))
      await models.AverageHistory.destroy({where:{},truncate:true})
      await models.AverageHistory.count().then(value => console.log("il y a ",value," lignes"))
    } else (console.log("password flush average history is wrong or you are not a dev"))
  },

  createAndMaintainTableMutation:async()=>{
    /* Fonction CronJob qui créée et maintain la table AverageHistory.
    * Il faudrait idéalement update la table lorsque des modifications entre cours <-> Exercice sont fait, mais c'est trop compliqué à keep
    * Ce que l'on fait donc, est donc de delete et recréer la table régulièrement.
    * */
    try {

      /* Flush ou pas la db */
      await models.AverageHistory.count().then(value => console.log("il y a ",value," lignes"))

      ///////// Définition de la fenêtre temporelle  // Si pas de date dans averageHistory, alors on prend une très ancienne (permet de retirer les éléments sans date)
      // définition de la borne négative de la fenetre temporelle => Soit all, soit max-windowDaysOverlap
      const windowDaysOverlap=50000 // en jours, comme on va flush la table, on veut un max de jours
      const qcmStatStartDate=await models.AverageHistory.max('date') || new Date('2000-01-01')
      const startDate=new Date(qcmStatStartDate);
      startDate.setDate(startDate.getDate()-windowDaysOverlap)

      // Définition de la borne positive de la fenetre temporelle => Tjr le moment présent.
      const endDate=new Date()

      let filterDate=null
      if (startDate && endDate) {filterDate=AverageHistoryService.conditionalDateCondition(startDate, endDate)}

      //////// Requête SQL d'agréagation toussa toussa
      const result = await models.QcmStatsQuestion.findAll({
        where: {
          ...(filterDate ? { createdAt: filterDate } : {}),
          pointsObtained:{[Op.not]:null} // On rend plus strict, et on exclue les données où on a des points obtained à null
        },
        include: [
          {
            model: models.Question,
            as: 'question',
            attributes: [],
            required: true,
            include: [
              {
                model: models.Cours,
                as: 'cours',
                attributes: [],
                required: true,
                through: {
                  model: models.QuestionCours,
                  attributes:[]
                }
              }
            ]
          }
        ],
        attributes: [
          [fn('DATE', col(`${models.QcmStatsQuestion.tableName}.createdAt`)), 'date'],  // Tronque la date au jour
          [fn('AVG', col('pointsObtained')), 'average'],
          [fn('COUNT', '*'), 'nbResults'],
          [col('question.cours.id'), 'coursId']
        ],
        group: [literal(`DATE(${models.QcmStatsQuestion.tableName}.createdAt)`), 'question.cours.id'],
        raw: true
      });

      ///////////////// Delete de la table
      await models.AverageHistory.destroy({where:{},truncate:true})

      // Retro-compatibilité avec l'ancienne approche.
      const nbEntries =await models.AverageHistory.count()

      ///////////////// re création de la table
      const arrayPromises=[]

      /// Si pas d'entrées dans la table Average History => Alors on model.create
      if (nbEntries===0){

        // pré-formatage des dates => Normalement c'est en hook, mais comme on bulk, on le fait en JS
        const rows = result.map(v => {
          const { annee, mois, semaine, jour } =
            AverageHistoryService.decomposeDate(v.date);

          return {
            date: v.date,
            average: v.average,
            nbResults: v.nbResults,
            coursId: v.coursId,
            annee, mois, semaine, jour
          };
        });

        arrayPromises.push(models.AverageHistory.bulkCreate(rows, {
          validate: false,
          hooks: false,
        }));

      // Pas utilisé, car on flush à chaque fois // Obsolète et attention au multi upsert => faire du batch
      } else { // Sinon, on upsert
        result.map(value => {
          arrayPromises.push(models.AverageHistory.upsert({
            date: value.date,
            average: value.average,
            nbResults: value.nbResults,
            coursId: value.coursId
          }));
        });
      }

      /// Résolution
      await Promise.all(arrayPromises)

    }catch(e){
      console.error(e)
    }
  },

  getDetailledExerciceCountFromSelection:async({ ueId, categoryId, coursId, dateDebut, dateFin ,userId: targetUserId},me)=>{
    try{
      /* Fonction qui calcule pour l'user différents metriques */

      const userId = targetUserId || me.id;
      const user = await models.User.findByPk(userId);

      ////// Récupération de tous les coursID associés
      const uePromises=ueId ? ueId?.map(async id => {
        return await PermissionService.getAvailableCoursIdsInUEForUser(id, user);
      }):[]

      const categoryPromises=categoryId ? categoryId?.map(async id=> {
        return await PermissionService.getAvailableCoursIdsInUECategoryForUserRecursive(id, user)
      }):[]

      ////// Mise en commun des coursId
      const tempCoursIds = await Promise.all([...uePromises,...categoryPromises])

      const allCoursIds = [...new Set([...tempCoursIds.flat(), ...coursId])];

      ////// Query de la table questions

      ////// query de la db

      const filterDate=AverageHistoryService.conditionalDateCondition(new Date(dateDebut), new Date(dateFin))

      /*
      const result=await models.Question.findAll({

        where:{
          linkCoursId:allCoursIds,
        },
        include:[
          {
            model:models.QcmStatsQuestion,
            as : 'statistiques_questions',
            where : {
              id_utilisateur:userId,
              ...(filterDate ? { createdAt: filterDate } : {})
            },
            attributes:[],
            required:false
          },
          {
            model: models.Cours,
            as: 'cours',
            attributes: [],
            required: true
          }

        ],
        raw:true,
        attributes:[
          [Sequelize.literal('`statistiques_questions`.`pointsObtained`'),'pointsObtained'],
          'isCheckbox',
          'isAnswerFreeText',
          'type',
          'id_question'
        ]
      })
      */
      const result=await models.Question.findAll({
        include:[
          {
            model:models.QcmStatsQuestion,
            as : 'statistiques_questions',
            where : {
              id_utilisateur:userId,
              ...(filterDate ? { createdAt: filterDate } : {})
            },
            attributes:[],
            required:false
          },
          {
            model: models.Cours,
            as: 'cours',
            attributes: ['id'],
            where:{id:allCoursIds},
            required: true,
            through: {
              model: models.QuestionCours,
              attributes:[]
            }
          }

        ],
        raw:true,
        attributes:[
          [Sequelize.literal('`statistiques_questions`.`pointsObtained`'),'pointsObtained'],
          'isCheckbox',
          'isAnswerFreeText',
          'type',
          'id_question'
        ]
      })

      //// compute des statistiques
      const objAll={}
      const objDone={}

      const noteMax={}
      const countNote={}



      result.forEach((input) => {
        let type=null
        const isDone=!(input?.pointsObtained === null)
        const note=input?.pointsObtained

        if (input?.isCheckbox){
          type="QCM"
        }
        else if (input?.isAnswerFreeText){type="freeText"}
        else if (input?.type === 'ALPHANUMERICAL' || input?.type==='NUMERICAL'){type="alphaNumerique"}
        else {type="QCU"}

        if (objAll[type]) {objAll[type]+=1}else {objAll[type]=1}
        if (isDone){
          if (objDone[type]) {objDone[type]+=1}else {objDone[type]=1}
          if (noteMax[type]) {noteMax[type]+=1}else {noteMax[type]=1}
          if (countNote[type]) {countNote[type]+=note}else {countNote[type]=note}
        }
      })

      return ({
        uniqueQuestions:Object.values(objAll).reduce((acc, val) => acc + val, 0),
        uniqueQuestionsDone:Object.values(objDone).reduce((acc, val) => acc + val, 0),

        uniqueQcm:objAll["QCM"] || 0,
        uniqueQcmDone:objDone["QCM"] || 0,

        uniqueFreeText:objAll["freeText"] || 0,
        uniqueFreeTextDone:objDone["freeText"]||0,

        uniqueAlphaNumerique:objAll["alphaNumerique"]||0,
        uniqueAlphaNumeriqueDone:objDone["alphaNumerique"]||0,

        uniqueQcu:objAll["QCU"]||0,
        uniqueQcuDone:objDone["QCU"]||0,


        totalTheorique:Object.values(noteMax).reduce((acc, val) => acc + val, 0),
        totalNote:Object.values(countNote).reduce((acc, val) => acc + val, 0),

        noteQcmTheorique:noteMax["QCM"] || 0,
        noteQcmSum:countNote["QCM"] || 0,

        noteFreeTextTheorique:noteMax["freeText"] || 0,
        noteFreeTextSum:countNote["freeText"] || 0,

        noteAlphaNumeriqueTheorique:noteMax["alphaNumerique"] || 0,
        noteAlphaNumeriqueSum:countNote["alphaNumerique"] || 0,

        noteQcuTheorique:noteMax["QCU"] || 0,
        noteQcuSum:countNote["QCU"] || 0,
      })
    }catch(e){console.error(e.message)}
  },

}