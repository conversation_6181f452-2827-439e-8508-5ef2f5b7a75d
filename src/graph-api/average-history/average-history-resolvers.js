import { combineResolvers } from 'graphql-resolvers';

import {
  isAdmin,
  isAuthenticated,
} from '../authorization'
import { AverageHistoryService } from './average-history-service.js'

export default {
  Query: {
    fullAnalyse: combineResolvers(
      isAuthenticated,
      async (parent, arg, ctx, info) => {
        return await AverageHistoryService.fullAnalyse(arg, ctx.me)
      }
    ),
    resumeAnalyse:combineResolvers(
      isAuthenticated,
      async (parent,arg,ctx,info)=>{
        return await AverageHistoryService.resumeAnalyse(arg,ctx.me)
      }
    ),
    progressAnalyse:combineResolvers(
      isAuthenticated,
      async(parent,arg,ctx,info)=>{
        return await AverageHistoryService.progressAnalyse(arg,ctx.me)
      }
    ),

    getDetailledExerciceCountFromSelection:combineResolvers(
      isAuthenticated,
      async(parent,arg,ctx,info)=>{
        return await AverageHistoryService.getDetailledExerciceCountFromSelection(arg,ctx.me)
      }
    ),
    flushAverageHistory:combineResolvers(
      isAdmin,
      async(parent,{passWord},ctx,info)=>{
        return await AverageHistoryService.flushAverageHistory(passWord)
      }
    )
  }
}