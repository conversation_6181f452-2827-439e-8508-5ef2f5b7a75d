import { GraphQLError } from 'graphql';
import Sequelize from 'sequelize';
import models from '../../models/index.js';

const { Op } = Sequelize;

const CRUD_ScheduledTask = {
  async createScheduledTask(input, ctx) {
    try {
      return await ctx.models.ScheduledTask.create(input);
    } catch (e) {
      console.error(e);
    }
  },

  async updateScheduledTask(id, input, ctx) {
    try {
      await ctx.models.ScheduledTask.update(input, { where: { id } });
      return true;
    } catch (e) {
      console.error(e);
    }
  },

  async deleteScheduledTask(id, ctx) {
    try {
      await ctx.models.ScheduledTask.destroy({ where: { id } });
      return true;
    } catch (e) {
      console.error(e);
    }
  },

};

export const ScheduledTasksService = {
  ...CRUD_ScheduledTask,

  async executeScheduledTasks() {
    try {
      const scheduledTasks = await models.ScheduledTask.findAll({
        where: {
          executionDate: {
            [Op.lte]: new Date()
          },
          status: 'pending',
        }
      });

      for(const task of scheduledTasks) {
        // POUR LES GROUPES
        if(task.groupId) {
          // Apply state to group...
          const group = await models.Groupe.findByPk(task.groupId);
          if(!group) {
            console.error(`executeScheduledTasks: Groupe introuvable (taskId=${task.id}):`, task.groupId);
            continue;
          }
          const selection = task.state.selection;
          if(!selection) {
            //console.error(`executeScheduledTasks: selection introuvable (taskId=${task.id}):`, task.state);
            continue;
          }
          const coursIds = [...new Set([...selection.cours, ...selection.step])];
          const ueIds = [...new Set([...selection.ue, ...selection.folder])];
          const categorieIds = selection.category;

          // Supprime les anciennes permissions avant d'ajouter les nouvelles
          // ----------------------------------------------------------------
          // Cours: delete existing and add new
          await models.CoursGroups.destroy({ where: { groupeId: task.groupId } });
          await models.CoursGroups.bulkCreate(coursIds.map(coursId => ({ groupeId: task.groupId, coursId })));
          // UEs
          await models.UEGroups.destroy({ where: { groupeId: task.groupId} });
          await models.UEGroups.bulkCreate(ueIds.map(ueId => ({ groupeId: task.groupId, ueId })));
          // Categ
          await models.UECategoryGroups.destroy({ where: { groupeId: task.groupId} });
          await models.UECategoryGroups.bulkCreate(categorieIds.map(categoryId => ({ groupeId: task.groupId, uecategory_id: categoryId })));

          // Update task status to 'executed'
          console.log(`executeScheduledTasks: Groupe mis à jour (taskId=${task.id}, groupId=${task.groupId})`);

          task.status = 'executed';
          await task.save();
        }

        // POUR LES ELEMENTS
        if(task.elementId) {
          const formationElement = await models.FormationElement.findByPk(task.elementId);
          if(!formationElement) {
            console.error(`executeScheduledTasks: Element introuvable (taskId=${task.id}):`, task.elementId);
            continue;
          }
          // Apply state to element...
          const scheduledTasksGroups = await models.ScheduledTasksGroups.findAll({ where: { scheduledTaskId: task.id } });
          const groupIds = scheduledTasksGroups.map(link => link.groupId);
          // FormationElementGroups: delete existing and add new
          await models.FormationElementGroups.destroy({ where: { formationElementId: task.elementId } });
          await models.FormationElementGroups.bulkCreate(groupIds.map(gId => ({ formationElementId: task.elementId, groupeId: gId })));

          // Update task status to 'executed'
          console.log(`executeScheduledTasks: Élément mis à jour (taskId=${task.id}, elementId=${task.elementId})`);
          task.status = 'executed';
          await task.save();
        }
      }
      return true;
    } catch (e) {
      console.error(e);
    }
  },


  /**
   *
   * @param filter
   * @param ctx
   * @returns {Promise<awaited ElementNode[] | Promise<Model[]>>}
   */
  async getScheduledTasks(filter, ctx) {
    try {
      return await ctx.models.ScheduledTask.findAll({ where: filter, order: [['executionDate', 'ASC']]});
    } catch (e) {
      console.error(e);
    }
  },

  /**
   *
   * @param id
   * @param ctx
   * @returns {Promise<Model|null>}
   */
  async getScheduledTaskById(id, ctx) {
    try {
      return await ctx.models.ScheduledTask.findByPk(id);
    } catch (e) {
      console.error(e);
    }
  },

  /* Many to many */
  async getGroupsState(scheduledTask, ctx) {
    try {
      const groupLink = await ctx.models.ScheduledTasksGroups.findAll({ where: { scheduledTaskId: scheduledTask.id } });
      const groupIds = groupLink.map(link => link.groupId);
      if(groupIds) {
        return await ctx.models.Groupe.findAll({ where: { id: groupIds } });
      } else {
        return [];
      }
    } catch (e) {
      console.error(e);
    }
  },

  async addGroupToScheduledTask(groupId, scheduledTaskId, ctx) {
    try {
      const task = await ctx.models.ScheduledTask.findByPk(scheduledTaskId);
      if(!task) {
        console.error(`addGroupToScheduledTask: ScheduledTask introuvable (taskId=${scheduledTaskId}):`, scheduledTaskId);
        return false;
      }
      const group = await ctx.models.Groupe.findByPk(groupId);
      if(!group) {
        console.error(`addGroupToScheduledTask: Groupe introuvable (groupId=${groupId}):`, groupId);
        return false;
      }
      const existingLink = await ctx.models.ScheduledTasksGroups.findOne({ where: { groupId, scheduledTaskId } });
      if(existingLink) {
        return true;
      } else {
        await ctx.models.ScheduledTasksGroups.create({ groupId, scheduledTaskId });
        return true;
      }
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },


  async removeGroupFromScheduledTask(groupId, scheduledTaskId, ctx) {
    try {
      const task = await ctx.models.ScheduledTask.findByPk(scheduledTaskId);
      if(!task) {
        console.error(`addGroupToScheduledTask: ScheduledTask introuvable (scheduledTaskId=${scheduledTaskId}):`, scheduledTaskId);
        return false;
      }
      const group = await ctx.models.Groupe.findByPk(groupId);
      if(!group) {
        console.error(`addGroupToScheduledTask: Groupe introuvable (groupId=${groupId}):`, groupId);
        return false;
      }
      await ctx.models.ScheduledTasksGroups.destroy({ where: { groupId, scheduledTaskId } });
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
};
