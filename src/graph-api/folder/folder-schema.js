import gql from 'graphql-tag'

export default gql`
    extend type Query {
        "All folders (legacy, will be removed)"
        folders: [Folder]
        
        "Folder by id"
        folder(id: ID!): Folder
        
        "Find folders by filter"
        findFolders(filter: FolderFilter): [Folder]

        #Types/FolderType supportés: 'GROUP', 'FOLDER', 'EXAM', 'EVENT'
        "Tree data selection for folders and types (returns folders with/without associated entity types)"
        foldersTreeData(type: String, folderType: String, folderCheckable: Boolean, showNumberOfUsers: Boolean, separateForfaitsByType:Boolean): JSO<PERSON>
    }

    extend type Mutation {
        # Folder CRUD
        createFolder(folder: FolderInput!): Folder!
        updateFolder(id: ID!, folder: FolderInput!): Boolean
        deleteFolder(id: ID!): Boolean!
    }
    
    "Filter for folders"
    input FolderFilter {
        parentId: ID
        type: String
    }

    "Input for folder"
    input FolderInput {
        name: String
        type: String
        parentId: ID
    }
    
    "Folder (admin)"
    type Folder {
        id: ID
        name: String
        "Type of folder: group, exam, etc."
        type: String

        #Only for group type
        countGroups: Int
        
        countEvents: Int
        countExams: Int
        countForfaits: Int
        
        countUsers: Int
        countAccessibleCourses: Int
        
        "Parent folder"
        parentId: ID
        createdAt: Date
        updatedAt: Date
    }
`
