import { GraphQLError } from 'graphql';
import { Op } from 'sequelize';
import models from '../../models';
import { UPLOAD_FOLDER_MAP } from '../../util/utils';
import { CoursService } from '../cours/cours-service';
import { EdtService, orderPlusAncien } from '../edt/edt-service.js';
import { UploadService } from '../file/upload-service';
import { PermissionService } from '../permission/permission-service.js';
import {ROLES} from "../authorization";

'use strict';

const CRUDEvent = {
  //////// CRUD Event //////////
  createEvent: async (event, userId) => {
    try {
      if (event?.image) {
        event.image = await UploadService.uploadFileDataToFolder(event.image, UPLOAD_FOLDER_MAP.files);
      }
      const eventCreated = await models.Event.create(event);
      const { typeIDs = [] } = event;
      // Ajout des types à l'event
      for (const typeID of typeIDs) {
        await EventService.addTypeQcmToEvent({
          eventId: eventCreated.id,
          typeQcmId: typeID,
        });
      }
      return eventCreated;
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création de l\'évènement');
    }
  },
  updateEvent: async (id, input) => {
    try {
      if (input.image) {
        input.image = await UploadService.uploadFileDataToFolder(input.image, UPLOAD_FOLDER_MAP.files);
      }
      if (Array.isArray(input?.coursIds)) {
        const coursIds = input.coursIds.map(id => parseInt(id)); // Convertir tous les IDs en int
        id = parseInt(id);
        // Récupérer les cours déjà liés à cet événement
        const linkedCoursIdArray = await models.EventCours.findAll({
          where: { eventId: id },
          raw: true,
        }).then(nodes => nodes.map(value => value.coursId));
        let coursLinkToAdd = [];
        let coursLinkToRemove = linkedCoursIdArray.filter(elem => !coursIds.includes(elem));
        // Vérification et ajout des cours valides
        for (const coursId of coursIds) {
          const cours = await models.Cours.findByPk(coursId, { raw: true });
          if (!cours) {
            throw new GraphQLError(`Le cours d'id : ${coursId} n'est pas valide`);
          }
          const coursIdsToAdd = await CoursService.getAllImportedCoursIdsFromCours(cours);
          coursLinkToAdd = [...new Set([...coursLinkToAdd, ...coursIdsToAdd])];
          // Si le cours original ou ses importés existent déjà, on les retire des cours à supprimer
          coursLinkToRemove = coursLinkToRemove.filter(id => !coursIdsToAdd.includes(id));
        }
        // Supprimer les cours à retirer de l'événement
        await models.EventCours.destroy({
          where: {
            eventId: id,
            coursId: coursLinkToRemove,
          },
        });
        // Vérifier quels cours sont déjà liés et n'insérer que les nouveaux
        const alreadyLinkedCourses = await models.EventCours.findAll({
          where: {
            eventId: id,
            coursId: coursLinkToAdd,
          },
          raw: true,
        }).then(nodes => nodes.map(value => value.coursId)); // Assurer que tout est en int
        const newEntriesToAdd = coursLinkToAdd.filter(id => !alreadyLinkedCourses.includes(id));
        const entriesToCreate = newEntriesToAdd.map(coursId => ({ eventId: id, coursId }));
        // N'insérer que les nouvelles entrées qui ne sont pas déjà présentes
        if (entriesToCreate.length > 0) {
          await models.EventCours.bulkCreate(entriesToCreate);
        }
      }

      let updated = await models.Event.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // retourne le nombre d'éléments mis à jour
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour Event ' + id);
    }
  },
  deleteEvent: async (id, userId) => {
    try {
      await models.EventTypeQcm.destroy({
        where: {
          eventId: id,
        }
      });
      await models.EventCours.destroy({
        where: {
          eventId: id,
        }
      });
      return await models.Event.destroy({ where: { id } });
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error suppression Event ' + id);
    }
  },
};

export const EventService = {
  ...CRUDEvent,

  getAllEvents: async (models, me, { filter }) => {
    try {
      const folderId = filter?.folderId || null;
      let where = {
        folderId,
      }
      const events = await models.Event.findAll({ where });
      const myAllowedEventIds = await PermissionService.Events.getEventIdsAuthorizedForUser(me?.id)
      // return events, with attribute disabled:true if not in myAllowedEventIds
      return events.map(event => {
        return {
          ...event.get({ plain: true }), // Convertit l'instance Sequelize en objet simple
          disabled: !myAllowedEventIds.includes(event.id),
        }
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  /**
   * Search events paginated with folders
   * @param filter
   * @param ctx
   * @returns {Promise<{folders: awaited Node[] | Promise<Model[]>, count: *, events: (*&{disabled})[]}>}
   */
  searchEvents: async ({ filter }, ctx) => {
    try {
      const folderId = filter?.folderId || null;
      let where = {
        folderId,
      }
      const limit = filter?.limit;
      const offset = filter?.offset || 0;
      const myAllowedEventIds = await PermissionService.Events.getEventIdsAuthorizedForUser(ctx?.me?.id);

      const whereFolders = {
        parentId: folderId,
        type: 'EVENT',
      };
      const folders = await models.Folder.findAll({
        where: whereFolders,
        limit,
        offset,
      });
      const countFolders = await models.Folder.count({ where: whereFolders });

      // Calcul nombre éléments restants à paginer pour les événements
      const remainingLimit = limit - folders.length;
      const adjustedOffset = Math.max(offset - countFolders, 0);

      const events = await models.Event.findAll({
        where,
        limit: remainingLimit > 0 ? remainingLimit : 0,
        offset: adjustedOffset,
      });
      const countEvents = await models.Event.count({ where });

      return {
        count: countEvents + countFolders,
        countEvents,
        countFolders,
        // return events, with attribute disabled:true if not in myAllowedEventIds
        events: events.map(event => {
          return {
            ...event.get({ plain: true }), // Convertit l'instance Sequelize en objet simple
            disabled: !myAllowedEventIds.includes(event.id),
          }
        }),
        folders
      }
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async getEventById(id, me) {
    try {
      return await models.Event.findByPk(id);
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async getEventByIds(ids, me) {
    try {
      return await models.Event.findAll({
        where: { id: ids}
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async getCoursIdsLinkedToEventId(eventId) {
    const eventCours = await models.EventCours.findAll({
      where: { eventId },
      attributes: ['coursId'],
      raw: true
    });
    return eventCours.map(eventCours => eventCours.coursId);
  },

  async getDatesDiffusions(event, args, me) {
    try {
      const userId = me?.id;
      const user = await models.User.findByPk(userId);
      let where = {
        eventId: event?.id,
        customPlanningId: null, // Non générées
      };
      if ([ROLES.ADMIN, ROLES.SUB_ADMIN, ROLES.TUTEUR].includes(user?.role)) {

      } else {
        const dateVisibles = await EdtService.getDatesDiffusionVisiblePourUser(userId);
        where = {
          ...where,
          id: {
            [Op.in]: await dateVisibles.map(obj => obj.date_diffusion_id),
          },
        }
      }
      const dates = await models.DateDiffusion.findAll({
        where,
        include: [models.Groupe],
        order: orderPlusAncien,
      });
      return dates;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  // Admin
  async getGeneratedDatesDiffusions(event, args, me) {
    try {
      const userId = me?.id;
      const user = await models.User.findByPk(userId);
      let where = {
        eventId: event?.id,
        customPlanningId: {[Op.ne]: null}, // Générées
      };
      if ([ROLES.ADMIN, ROLES.SUB_ADMIN, ROLES.TUTEUR].includes(user?.role)) {

      } else {
        const dateVisibles = await EdtService.getDatesDiffusionVisiblePourUser(userId);
        where = {
          ...where,
          id: {
            [Op.in]: await dateVisibles.map(obj => obj.date_diffusion_id),
          },
        }
      }
      const dates = await models.DateDiffusion.findAll({
        where,
        include: [models.Groupe],
        order: orderPlusAncien,
      });
      return dates;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },


  async addTypeQcmToEvent({ typeQcmId, eventId }, ctx) {
    try {
      const event = await models.Event.findByPk(eventId);
      const typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      if (!event || !typeQcm) {
        throw new GraphQLError('Event or Type not found');
      }
      await models.EventTypeQcm.create({
        eventId,
        typeQcmId,
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async removeTypeQcmFromEvent({ typeQcmId, eventId }, ctx) {
    try {
      const event = await models.Event.findByPk(eventId);
      const typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      if (!event || !typeQcm) {
        throw new GraphQLError('Event or Type not found');
      }
      await models.EventTypeQcm.destroy({
        where: {
          eventId,
          typeQcmId,
        },
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async createBuilding(input) {
    try {
      if(input.image) {
        input.image = await UploadService.uploadFileDataToFolder(input.image, UPLOAD_FOLDER_MAP.files);
      }
      return await models.Building.create(input);
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur création Building');
    }
  },
  async updateBuilding(id, input) {
    try {
      if(input.image) {
        input.image = await UploadService.uploadFileDataToFolder(input.image, UPLOAD_FOLDER_MAP.files);
      }
      let updated = await models.Building.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour Building ' + id);
    }
  },
  async deleteBuilding(id) {
    try {
      return await models.Building.destroy({ where: { id } });
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error suppression Building ' + id);
    }
  },

  async createRoom(input) {
    try {
      if(input.image) {
        input.image = await UploadService.uploadFileDataToFolder(input.image, UPLOAD_FOLDER_MAP.files);
      }
      return await models.Room.create(input);
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur création Room');
    }
  },
  async updateRoom(id, input) {
    try {
      if(input.image) {
        input.image = await UploadService.uploadFileDataToFolder(input.image, UPLOAD_FOLDER_MAP.files);
      }
      let updated = await models.Room.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour Room ' + id);
    }
  },
  async deleteRoom(id) {
    try {
      return await models.Room.destroy({ where: { id } });
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error suppression Room ' + id);
    }
  },

  async getAllBuildings(models, me) {
    try {
      return await models.Building.findAll();
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async getAllRooms(models, me) {
    try {
      return await models.Room.findAll();
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async getBuildingById(id, me) {
    try {
      return await models.Building.findByPk(id);
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async getRoomById(id, me) {
    try {
      return await models.Room.findByPk(id);

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },


  async addCoursToEvent(args, ctx) {
    try {
      const event = await models.Event.findByPk(args.eventId);
        const cours = await models.Cours.findByPk(args.coursId);
        if (!event || !cours) {
            throw new GraphQLError('Event or Cours not found');
        }
        await models.EventCours.create({
            eventId: args.eventId,
            coursId: args.coursId,
        });
        return true;
    } catch (e) {
        console.error(e);
        throw new GraphQLError(e);
    }
  },
  async removeCoursFromEvent(args, ctx) {
    try {
      const event = await models.Event.findByPk(args.eventId);
        const cours = await models.Cours.findByPk(args.coursId);
        if (!event || !cours) {
            throw new GraphQLError('Event or Cours not found');
        }
        await models.EventCours.destroy({
            where: {
                eventId: args.eventId,
                coursId: args.coursId,
            },
        });
        return true;
    } catch (e) {
        console.error(e);
        throw new GraphQLError(e);
    }
  },

  async batchUpdateChampsCustomToDescription() {
    try {
      const allEvents = await models.Event.findAll();
      for(const event of allEvents) {
        const customFields = event.customFields;
        if(customFields) {
          for(const customField of customFields) {
            if(customField?.label && customField.value) {
              event.description = event.description + `<br />${customField.label} : ${customField.value}<br />`;
              await event.save();
            }
          }
        }

      }
    } catch (e) {

    }
  },

  async batchUpdateOrganizersForEvents() {
    try {
      const allEvents = await models.Event.findAll();
      for(const event of allEvents) {
        const eventOrganizersUserIds = event.organizers;
        // Get date diffusion for event
        if(eventOrganizersUserIds) {
          const dateDiffusions = await models.DateDiffusion.findAll({
            where: {
              eventId: event.id,
            },
          });
          for(const dateDiff of dateDiffusions) {
            for(const userId of eventOrganizersUserIds) {
              await EdtService.addOrganizerToDateDiffusion(
                  {dateDiffusionId: dateDiff.id, userId}, null);
            }
          }
        }
      }

    } catch (e) {
      console.error('error in batchUpdateOrganizersForEvents', e);;
    }
  },

  async createDefaultEventDiscussionType() {
    try {
      await models.PostType.create({
        name: 'Question sur l\'évènement',
        type: 'EVENT'
      })
    } catch (e) {
      console.error(e);
    }
  },

  async filterEventIdGivenCoursIdAndQcmTypes(coursIds,typeQcmIdFilter){
    /* Cette fonction prend un coursId, et un typeQcmId. Et filtre les typeId pour que :
    *    1) Le eventID soit lié à au moins 1 coursId de la liste
    *    2) Le eventId doit lié à au moins 1 typeQcmId de la liste
    *  */
    try {
      let includeArray = []

      if (coursIds && coursIds.length >= 0) {
        includeArray.push(
          {
            model: models.Cours,
            as: 'cours',
            where: { id: coursIds },
            required: true, // On veut une inclusion
            attributes: [],
          }
        )
      }

      if (typeQcmIdFilter && typeQcmIdFilter.length >= 0) {
        includeArray.push(
          {
            model: models.TypeQcm,
            as: 'type_qcms',
            where: { id: typeQcmIdFilter },
            required: true,
            attributes: [],
          }
        )
      }

      const eventIds = await models.Event.findAll({
        include: includeArray,
        attributes: ["id"],
        raw: true,
        required: true,
      }).then(result => result.map(value => value.id));

      return [...new Set(eventIds)]
    } catch(e){
      console.error("error dans filterEventIdGivenCoursIdAndQcmType :",e)
      return []
    }
  },

  async eventMassActions(args, ctx) {
    try {
      const {ids, input, action} = args;
      const events = await models.Event.findAll({
        where: {
          id: ids,
        },
      });
      if (action === 'move') {
        // Root folder ID is null
        if(input.folderId !== null) {
          const folder = await models.Folder.findByPk(input.folderId);
          if (!folder) {
            throw new GraphQLError('Dossier introuvable');
          }
        }
        for (let event of events) {
          await event.update({ folderId: input.folderId });
        }
        return true;
      }
      else {
        throw new GraphQLError('Action non reconnue');
      }
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
};
