import { combineResolvers } from 'graphql-resolvers';
import { consoleLogSequelizeMagicMethods } from '../../util/utils.js';
import { isAuthenticated, isTuteurOrAdmin } from '../authorization';
import { FormationService } from '../formation/formation-service.js';
import { PermissionService } from '../permission/permission-service';
import { EventService } from './event-service.js';

'use strict';

export default {
  Query: {

    event: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me }) => await EventService.getEventById(id, me),
    ),

    events: combineResolvers(
        isAuthenticated,
        async (parent, { ids }, { models, me }) => await EventService.getEventByIds(ids, me),
    ),

    allEvents: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, { models, me }) => await EventService.getAllEvents(models, me, args),
    ),

    searchEvents: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await EventService.searchEvents(args, ctx),
    ),

    allBuildings: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id }, { models, me }) => await EventService.getAllBuildings(models, me),
    ),

    allRooms: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id }, { models, me }) => await EventService.getAllRooms(models, me),
    ),

    building: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me }) => await EventService.getBuildingById(id, me),
    ),

    room: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me }) => await EventService.getRoomById(id, me),
    ),

  },

  Mutation: {
    /* CRUD EXAM */
    createEvent: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { input }, { models }) => await EventService.createEvent(input),
    ),
    updateEvent: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id, input }, { models }) => await EventService.updateEvent(id, input),
    ),
    deleteEvent: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id }, { models }) => await EventService.deleteEvent(id),
    ),

    eventMassActions: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await EventService.eventMassActions(args, ctx),
    ),

    /* Add / remove type for event */
    addTypeQcmToEvent: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await EventService.addTypeQcmToEvent(args, ctx),
    ),
    removeTypeQcmFromEvent: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await EventService.removeTypeQcmFromEvent(args, ctx),
    ),

    addCoursToEvent: combineResolvers(
        isTuteurOrAdmin,
        async (parent, args, ctx) => await EventService.addCoursToEvent(args, ctx),
    ),
    removeCoursFromEvent: combineResolvers(
        isTuteurOrAdmin,
        async (parent, args, ctx) => await EventService.removeCoursFromEvent(args, ctx),
    ),

    /* CRUD BUILDING */
    createBuilding: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { input }, { models }) => await EventService.createBuilding(input),
    ),
    updateBuilding: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id, input }, { models }) => await EventService.updateBuilding(id, input),
    ),
    deleteBuilding: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id }, { models }) => await EventService.deleteBuilding(id),
    ),

    /* CRUD ROOM */
    createRoom: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { input }, { models }) => await EventService.createRoom(input),
    ),
    updateRoom: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id, input }, { models }) => await EventService.updateRoom(id, input),
    ),
    deleteRoom: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id }, { models }) => await EventService.deleteRoom(id),
    ),

  },


  Event: {
    datesDiffusion: combineResolvers(
      isAuthenticated,
      async (event, args, { models, me }) => await EventService.getDatesDiffusions(event, args, me),
    ),
    elements: combineResolvers(
      isAuthenticated,
      async (event, args, { models, me }) => await FormationService.elementsInEvent(event?.id, me?.id),
    ),

    types: combineResolvers(
      isAuthenticated,
      async (event, args, { models, me }) => {
        const eventTypeQcm = await models.EventTypeQcm.findAll({
          where: {
            eventId: event?.id,
          },
          raw: true,
          attributes: ["typeQcmId"]
        });
        return models.TypeQcm.findAll({
          where: {
            id: eventTypeQcm?.map((type) => type.typeQcmId),
          },
        });
      },
    ),

    coursIds: combineResolvers(
      isAuthenticated,
      async (event, args, { models, me }) => {
        const coursIds = await EventService.getCoursIdsLinkedToEventId(event?.id);
        const coursLinked = await models.Cours.findAll({
          where: {
            id: coursIds,
            deleted:false,
          },attributes: ['id', 'targetCoursId'],raw:true
        });
        const linkedCourses = await PermissionService.getLinkedCoursesToShowForUser(coursLinked, me);
        return linkedCourses.map((c) => c.id);
      }
    ),
  },

  Building: {
    rooms: combineResolvers(
      isAuthenticated,
      async (building, args, { models, me }) => {
        return building?.getRooms();
      },
    ),
  },

  Room: {
    building: combineResolvers(
      isAuthenticated,
      async (room, args, { models, me }) => {
        return room?.getBuilding();
      }),
  }
};
