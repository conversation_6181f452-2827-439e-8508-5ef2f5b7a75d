import models from '../../models/index.js'
import message from '../../models/message.js'
import { consoleDebugObject } from '../../util/utils.js'

const Sequelize = require('sequelize')
const Op = Sequelize.Op

const TypeKeys = {
  COURS: 'courId',
  QCM: 'qcmIdQcm',
  POST: 'postId',
}

export const UserViewHistoryService = {
  /**
   * @param userId
   * @param typeId
   * @param {string} type among TypeKeys
   */
  async setSeen(userId, typeId, type) {
    let viewHist = await models.ViewHistory.findOne({
      where: {
        [type]: typeId,
        userId: userId,
      },
    })
    if (!viewHist) {
      await models.ViewHistory.create({
        [type]: typeId,
        userId: userId,
      })
      return false // has not been seen, first time
    }
    return true // has been seen before
  },

  async getAllSeenForUser(userId) {
    try {
      return await models.ViewHistory.findAll({
        where: { userId: userId },
      })
    } catch (e) {
      console.error(e.message)
    }
  },

  async countTotalCoursVus(userId) {
    const coursVus = await models.ViewHistory.count({
      where: {
        userId: userId,
        [TypeKeys.COURS]: {
          [Op.ne]: null,
        },
      },
    })
    return coursVus
  },

  async countCoursVusAmongIds(coursIds, userId) {
    const coursVus = await models.ViewHistory.count({
      where: {
        userId: userId,
        [TypeKeys.COURS]: {
          [Op.in]: coursIds,
        },
      },
    })
    return coursVus
  },

  async setCoursSeen(userId, coursId) {
    try {
      return await UserViewHistoryService.setSeen(userId, coursId, TypeKeys.COURS)
    } catch (e) {
      console.error('Impossible de mettre cours vu ')
      console.error({ userId, coursId })
    }
  },
  async setQcmSeen(userId, qcmId) {
    try {
      return await UserViewHistoryService.setSeen(userId, qcmId, TypeKeys.QCM)
    } catch (e) {
      console.error('Impossible de mettre qcm vu ')
      console.error({ userId, qcmId })
    }
  },
  async setPostSeen(userId, postId) {
    try {
      return await UserViewHistoryService.setSeen(userId, postId, TypeKeys.POST)
    } catch (e) {
      console.error('Impossible de mettre post vu ')
      console.error({ userId, postId })
    }
  },


}