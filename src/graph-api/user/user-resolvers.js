import { combineResolvers } from 'graphql-resolvers';
import moment from 'moment/moment.js';
import models from '../../models/index.js'
import User from '../../models/user.js'
import { isAdmin, isAuthenticated, isSuperAdmin, isTuteurOrAdmin, ROLES } from '../authorization';
import { GroupeService } from '../groupe/groupe-service.js';
import LogService from '../log/log-service.js';
import { PermissionService } from '../permission/permission-service';
import { UEService } from '../ue/ue-service.js';
import { UserImportService } from './user-import-service.js';
import { UserModificationSource, UserService } from './user-service';
import { UserSessionsSynthesisService } from './user-sessions-synthesis-service.js';
import { UserPropertyService } from './user_property_service.js';
import { UserStatsService } from './userStats-service.js';
import {UserViewHistoryService} from "./user-view-history-service";
import {LOG_OPERATIONS} from "../../models/log/log";

const Sequelize = require('sequelize');
const Op = Sequelize.Op;

export default {
  Query: {
    verifyRecaptcha: async (parent, {inputToken},_,__) => {
      return PermissionService.verifyRecaptcha(inputToken)
    },
    checkIfUserShouldSetupUsername: async (parent, {inputToken}, ctx) => {
      return UserService.checkIfUserShouldSetupUsername(inputToken,ctx)
    },
    checkUsernameAvailability: async (parent, {username}, ctx) => {
      return UserService.checkUsernameAvailability(username,ctx)
    },

    // AUTHENTICATED QUERIES
    me: async (parent, args, { models, me, req }) => {
      return !me ? null : await models.User.findByPk(me.id);
    },
    userProfile: combineResolvers(
      isAuthenticated,
      async (parent, { id, username }, { models }, info) => {
        info.cacheControl.setCacheHint({ maxAge: 25, scope: 'PUBLIC' });
        return UserService.getUserProfile(models, id, username);
      },
    ),
    searchUsersByUsername: combineResolvers(
      isAuthenticated,
      async (parent, { username, role = null, domain = null }, ctx, info) => {
        info.cacheControl.setCacheHint({ maxAge: 15, scope: 'PRIVATE' });
        return UserService.searchByUsernameAndNotBanned(username, role, domain, ctx);
      },
    ),

    myChilds: combineResolvers(
      isAuthenticated,
      async (parent, args, { models, me }) => {
        return await UserService.getMyChilds(me);
      }
    ),

    myResponsables: combineResolvers(
      isAuthenticated,
      async (parent, args, { models, me }) => {
        return await UserService.getMyResponsables(me);
      }
    ),

    userPropertiesFolders: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx, info) => {
        return UserPropertyService.getAllUserPropertiesFolders(args, ctx);
      },
    ),

    debugStatsForUserWithinDates: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { userId, startDate, endDate }, { models, me }) => {
        return UserSessionsSynthesisService.debugStatsForUserWithinDates(userId, startDate, endDate);
      }
    ),

    getTeamsMembersForMe: combineResolvers(
      isAuthenticated,
      async (parent, args, { me }, info) => {
        return UserService.getTeamsMembersForUser(me?.id);
      },
    ),
    userNotificationSettings: combineResolvers(
      isAuthenticated,
      async (parent, args, { models, me }, info) => {
        let user = await models.User.findByPk(me.id, {
          include: [{
            model: models.UserPreferences,
            as: 'userPreference',
          }],
        });
        return user.userPreference;
      },
    ),

    myBlockedUsers: combineResolvers(
      isAuthenticated,
      async (parent, args, { models, me }) => {
        return await UserService.getMyBlockedUsers(me);
      },
    ),
    // END AUTHENTICATED QUERIES

    // ADMIN QUERIES

    //TODO should not be used, remove if not used
    users: combineResolvers(
      isAdmin,
      async (parent, args, { models }) => await (models.User.findAll({
        include: [{
          model: models.UserDevice,
          as: 'user_devices',
        }],
      }))),

    user: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me }) => {
        // Si prof ou admin, a le droit de voir user. Sinon, il faut limiter les champs autorisés
        if([ROLES.ADMIN, ROLES.SUB_ADMIN, ROLES.TUTEUR].includes(me.role)) {
          return models.User.findByPk(id);
        } else {
          return models.User.findByPk(id, {
            attributes: ['id', 'username', 'firstName', 'name', 'role', 'avatar', 'isActive', 'createdAt', 'updatedAt'],
          });
        }
      }
    ),

    getUsersArray:combineResolvers(
      isAdmin,
      async (parent,{idArray},{models})=> await  models.User.findAll({where:{id:idArray}}),
    ),

    findUsersActiveForDates: combineResolvers(
      isAdmin,
      async (parent, {activeUsersFilter},ctx) => UserService.findUsersActiveForDates(activeUsersFilter,ctx),
    ),

    activeUsersGraph: combineResolvers(
        isAdmin,
        async (parent, args) => UserService.activeUsersGraph(args)
    ),

    usersAskingForDeletion: combineResolvers(
      isAdmin,
      async (parent, { id }, { models }) => UserService.findAllUsersAskingForDeletion()),

    tuteurs: combineResolvers(
      isAuthenticated, async (parent, args, { models, me }) => {
        return await models.User.findAll({ where: { role: 'TUTEUR' } });
      },
    ),
    searchUsers: combineResolvers(
      isTuteurOrAdmin, // Panneau tuteurs users + panneau admin users
      async (parent, { filter }, { models, me }) => {
        return UserService.searchUsers(filter, me);
      },
    ),

    reportedContent: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, { models, me }) => {
        return LogService.getReportedContent(me, models);
      },
    ),

    userIdsMatchingFilter: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { filter }, { models, me }) => {
        return UserService.userIdsMatchingFilter(filter, me);
      },
    ),

    exportUserIdsMatchingFilter: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { filter }, { models, me }) => {
        return UserService.exportUserIdsMatchingFilter(filter, me);
      },
    ),
    // END ADMIN QUERIES
  },

  Mutation: {
    // PUBLIC
    signIn: async (parent, { login, password }, {
      models,
      secret,
      ip,
      req,
    }) => UserService.signIn(models, login, password, secret, ip, req),
    signInWithUser: async (parent,{recaptchaToken,login,password},{
      models,
      secret,
      ip,
      req,
    }) => {
      return UserService.signInWithUser(models,recaptchaToken,login,password,secret,ip,req)
    },
    forgotPassword: async (parent, { email }, {
      models,
      secret,
      ip,
      req,
    }) => UserService.forgotPassword(models, email, secret, ip, req),
    resetPasswordWithToken: async (parent, { token, password }, {
      models,
      secret,
      ip,
      req,
    }) => UserService.resetTokenPassword(models, token, password, secret, ip, req),
    createFirstPasswordToken: async (parent, { token, password, username }, {
      models,
      secret,
      ip,
      req,
    }) => UserService.createFirstPasswordToken(models, token, password, username, secret, ip, req),
    // END PUBLIC

    disconnectUserFromAllDevices: combineResolvers(
      isSuperAdmin,
      async (parent, { userId }, { models, me }) => {
        return await UserService.disconnectUserFromAllDevices(userId, models, me);
      }
    ),

    restoreDeletedUser: combineResolvers(
      isSuperAdmin,
      async (parent, { id }, { models, me }) => {
        return UserService.restoreDeletedUser(id, me);
      }
    ),

    impersonateUser: combineResolvers(
      isAdmin,
      async (parent, { login }, {
        models, secret,
        me,
      }) => UserService.impersonateUser(login, me, secret),
    ),

    addParentChild: combineResolvers(
      isAdmin,
      async (parent, { childId, parentId }, ctx) => UserService.addParentChild(childId, parentId, ctx)
    ),
    removeParentChild: combineResolvers(
      isAdmin,
      async (parent, { childId, parentId }, ctx) => UserService.removeParentChild(childId, parentId, ctx)
    ),

    // AUTHENTICATED
    updatePassword: combineResolvers(
      isAuthenticated,
      async (parent, { oldPassword, newPassword }, {
        models,
        me,
      }) => UserService.updatePassword(oldPassword, newPassword, me.id),
    ),

    blockOrUnBlockUser: combineResolvers(
      isAuthenticated,
      async (parent, { userId, action }, {
        models,
        me,
      }) => UserService.blockOrUnblockUser(userId, action, me?.id, models),
    ),

    setUserBackground: combineResolvers(
      isAuthenticated,
      async (parent, { input }, { models, me }) => UserService.setUserBackground(input, me.id),
    ),
    updateMyProfile: combineResolvers(
      isAuthenticated,
      async (parent, data, { models, me }) => UserService.updateMyProfile(models, me.id, data),
    ),
    updateMyAvatar: combineResolvers(
      isAuthenticated,
      async (parent, data, { models, me }) => UserService.updateUserAvatar(models, me.id, data),
    ),
    updateMyAvatarFromList: combineResolvers(
      isAuthenticated,
      async (parent, data, { models, me }) => UserService.updateMyAvatarFromList(models, me.id, data),
    ),
    incrementUserStat: combineResolvers(
      isAuthenticated,
      async (parent, { operation, objectId }, { models, me }) => !!await UserStatsService.incrementStat(me.id, operation, objectId),
    ),
    updateUserNotification: combineResolvers(
      isAuthenticated,
      async (parent, data, { models, me }) => UserService.updateUserNotification(models, me.id, data),
    ),
    activateAssessmentWithBook: combineResolvers(
      isAuthenticated,
      async (parent, data, { models, me }) => UserService.activateAssessmentWithBook(data, me.id),
    ),
    addOneCreditIAP: combineResolvers(
      isAuthenticated,
      async (parent, { key }, { models, me }) => UserService.addOneCreditIAP(key, me.id),
    ),

    askForAccountDeletion: combineResolvers(
      isAuthenticated,
      async (parent, args, { models, me }) => UserService.askForAccountDeletion(me.id),
    ),
    // END AUTHENTICATED

    // ADMIN MUTATIONS ONLY
    createUser: combineResolvers(
      isAdmin,
      async (parent, { user }, ctx) => UserService.createUser(user, ctx, UserModificationSource.admin),
    ),
    updateUser: combineResolvers(
      isAdmin,
      async (parent, { id, user }, ctx) => {
        return UserService.updateUser(id, user,ctx);
      },
    ),
    updateUserAvatar: combineResolvers(
      isAdmin,
      async (parent, data, { models, me }) => UserService.updateUserAvatar(models, data.userId, data),
    ),
    deleteUser: combineResolvers(
      isSuperAdmin,
      async (parent, { id }, { models, me }) => {
        return UserService.softDeleteUser(id, me)
      },
    ),
    updateUserGroup: combineResolvers(
      isAdmin,
      async (parent, { userId, groupId }, { models, me }) => {
        return UserService.updateUserGroup(models, userId, groupId);
      },
    ),
    updateUserPassword: combineResolvers(
      isAdmin,
      async (parent, { userId, newPassword }, { models, me }) => {
        return UserService.adminUpdatePassword(newPassword, userId);
      },
    ),

    massUpdateUserGroups: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => {
        return UserService.massUpdateUserGroups(args, ctx);
      },
    ),
    massUpdateUserPreferences: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => {
        return UserService.massUpdateUserPreferences(args, ctx);
      }
    ),

    // Group UE autorisation
    addGroupeToUe: combineResolvers(
      isAdmin,
      async (parent, { ueId, groupId }, ctx) => PermissionService.addGroupeToUe(ueId, groupId, ctx)
    ),
    removeGroupeFromUe: combineResolvers(
      isAdmin,
      async (parent, { ueId, groupId }, ctx) => PermissionService.removeGroupeFromUe(ueId, groupId, ctx)
    ),
    /////////////////////////
    // Ajout tuteur à UE
    /*
     # Tuteurs in UE
        addTuteurToUe(ueId: ID!, userId: ID!): UE
        removeTuteurFromUe(ueId: ID!, userId: ID!): UE
     */
    addTuteurToUe: combineResolvers(
      isAdmin,
      async (parent, { ueId, userId }, { models, me }) => {
        return await PermissionService.addTuteurToUE(ueId, userId);
      },
    ),
    removeTuteurFromUe: combineResolvers(
      isAdmin,
      async (parent, { ueId, userId }, { models, me }) => {
        return await PermissionService.removeTuteurFromUE(ueId, userId);
      },
    ),
    updateTuteurFromUe:combineResolvers(
      isAdmin,
      async(parent,{ueIdArray,userId},{models,me})=>{
        return await PermissionService.updateTuteurFromUe(ueIdArray,userId)
      }
    ),

    importUsersFromXls: combineResolvers(
      isSuperAdmin,
      async (parent, args, ctx) => {
        return UserImportService.importUsersFromXls(args, ctx);
      },
    ),

    deleteReportedContent: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { id }, { models, me }) => {
        return LogService.deleteReportedContent(id, models, me);
      },
    ),

    // User properties
    createUserPropertyFolder: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => UserPropertyService.createUserPropertyFolder(args, ctx)
    ),
    updateUserPropertyFolder: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => UserPropertyService.updateUserPropertyFolder(args, ctx)
    ),
    deleteUserPropertyFolder: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => UserPropertyService.deleteUserPropertyFolder(args, ctx)
    ),
    createOrUpdateUserPropertyData: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => UserPropertyService.createOrUpdateUserPropertyData(args, ctx)
    ),

    updateUserPropertyDataById: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => UserPropertyService.updateUserPropertyDataById(args, ctx)
    ),

    // END ADMIN MUTATIONS ONLY
    acceptCgu: combineResolvers(
      isAuthenticated,
      async (parent,args, ctx) => {return await UserService.acceptCgu(ctx)}
    ),

    rejectCgu: combineResolvers(
      isAuthenticated,
      async (parent,args, ctx) => {return await UserService.rejectCgu(ctx)}
    ),

    /* Save elements input form */
    sendInputElementsForm: combineResolvers(
      isAuthenticated,
      async (parent, args, ctx) => UserPropertyService.sendInputElementsForm(args, ctx)
    ),

    deleteAllUsersInGroups: combineResolvers(
      isSuperAdmin,
      async (parent, { groupIds }, ctx) => {
        return UserService.deleteAllUsersInGroups(groupIds, ctx);
      }
    ),

  },

  User: {
    messages: async (user, args, { models }) => {
      // TODO see usages
      return await models.Message.findAll({
        where: {
          userId: user.id,
        },
      });
    },
    groups: async (user, args, { models }) => {
      return await user.getGroupes({ where: { isIndividual: false } }); // many to many get
    },
    notifications: async (user, args, { models }) => {
      return await user.getNotifications();
    },
    bills: async (user, args, { models }) => {
      return await user.getBills(); // many to many get
    },
    stats: async (user, args, ctx) => {
      return UserService.getUserStats(user, args, ctx)
    },

    childs: async (user, args, ctx) => {
      return UserService.getChildsForUser(user, args, ctx)
    },
    parents: async (user, args, ctx) => {
      return UserService.getParentsForUser(user, args, ctx)
    },

    responsibleForUeIds: async (user, args, { models }) => {
      const ueTuteurs = await models.UETuteurs.findAll({
        where: {
          userId: user.id
        }, raw: true
      })
      return ueTuteurs?.map(t => t.ueId);
    },

    statsByDay: async (user, args, { models, me }) => {
      return await UserStatsService.getUserDailyActivity({ userId: user.id, ...args }, me?.id);
    },
    totalTimeSpentExercising: async (user, { filter }, { models, me }) => {
      return await UserStatsService.getUserTotalTimeSpentBySubjects({userId: user?.id, ...filter}, me?.id);
    },

    fromUE: async (user, args, ctx) => UEService.getUEFromUser(user),

    individualGroup: async (user, args, ctx) => GroupeService.getIndividualGroupForUser(user?.id),

    myGroupsResponsibility: async (user, args, { models }) => {
      return models.Groupe.findAll({
        where: {
          id: await GroupeService.getAllowedGroupsIdsForUser(user)
        }
      });
    },

    isBlocked: async (user, args, { models, me }) => {
      const userB = await models.UserBlocked.findOne({
        where: {
          userId: me?.id,
          userBlockedId: user?.id,
        },
        attributes: ['userBlockedId'],
        raw: true,
      });
      return !!userB;
    },

    companiesDescriptions: async (user,arg,{models,me},info)=> {
      const clink = await models.UserCompanyInformations.findAll({
        where: {
          userId:user.id
        },
        attributes:[['companyInformationId','companyConfigId'],'companyName'],
        raw:true
      })
      return clink
    },
  },

  UserPropertyFolder : {
    elements: async (folder, args, ctx) => UserPropertyService.getElementsInFolder(folder, args, ctx)
  },
  UserPropertyData : {
    element: async (userPropertyData, args, ctx) => userPropertyData?.elementId && ctx.models.FormationElement.findByPk(userPropertyData?.elementId)
  },

};
