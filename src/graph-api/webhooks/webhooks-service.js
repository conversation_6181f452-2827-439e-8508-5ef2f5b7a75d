import axios from 'axios';
import crypto from 'crypto';
import models from '../../models/index';

function generateHmacSignature(secret, payload) {
  return crypto
    .createHmac('sha256', secret)
    .update(JSON.stringify(payload))
    .digest('hex');
};

function generateRandomSecret() {
  return crypto.randomBytes(32).toString('hex');
}

export const WebhooksService = {

  async getAllWebhooks(parent, args, { models }) {
    return await models.Webhook.findAll();
  },

  async getWebhookById(parent, { id }, { models }) {
    return await models.Webhook.findByPk(id);
  },


  createWebhook: async (parent, { input }, { models }) => {
    input.secret = generateRandomSecret();
    const webhook = await models.Webhook.create(input);
    return webhook;
  },
  updateWebhook: async (parent, { id, input }, { models }) => {
    const webhook = await models.Webhook.findByPk(id);
    if (!webhook) {
      throw new Error('Webhook not found');
    }
    await webhook.update(input);
    return webhook;
  },
  deleteWebhook: async (parent, { id }, { models }) => {
    const webhook = await models.Webhook.findByPk(id);
    if (!webhook) {
      throw new Error('Webhook not found');
    }
    await webhook.destroy();
    return true;
  },


  //TODO test
  sendEventToWebhook: async(event, webhook) => {
    try {
      const body = {
        event: event.event,   // ex: "user.created"
        payload: event.payload
      };

      // Création de la signature HMAC avec le secret stocké en base
      let headers = { 'Content-Type': 'application/json' };
      if (webhook.secret) {
        const signature = generateHmacSignature(webhook.secret, body);
        headers['x-webhook-signature'] = signature;
      }

      // Envoi au client
      const response = await axios.post(webhook.url, body, { headers });

      // 4. Créer un log avec 'success'
      await models.WebhookLogs.create({
        webhookId: webhook.id,
        status: 'success',
        response: {
          statusCode: response.status,
          data: response.data,
          // Voir si on stocke aussi headers, etc.
        }
      });

      return true;
    } catch (error) {
      // ... logs d'erreur, etc.
      console.error(error);
      await models.WebhookLogs.create({
        webhookId: webhook.id,
        status: 'failed',
        response: {
          message: error.message,
          stack: error.stack,
        }
      });
      return false;
      // throw error;
    }
  },





};

/*
Exemple côté client:
NODE

import crypto from 'crypto';

function verifyHmacSignature(secret, payload, signatureToCompare) {
  const hash = crypto
    .createHmac('sha256', secret)
    .update(JSON.stringify(payload))
    .digest('hex');
  return hash === signatureToCompare;
}

app.post('/my-webhook-endpoint', express.json(), (req, res) => {
  // Récupérer la signature dans les headers
  const signature = req.headers['X-Webhook-Signature'];
  const payload = req.body;

  // secret qu'on a configuré/manuellement enregistré
  // (le même que celui stocké par le serveur)
  const secret = process.env.WEBHOOK_SECRET || 'clé-fournie-par-le-serveur';

  // Vérifier
  if (!signature || !verifyHmacSignature(secret, payload, signature)) {
    return res.status(400).send('Signature invalide');
  }

  // Si la signature est valide, on traite l'événement
  console.log('Payload reçu:', payload);
  res.sendStatus(200);
});



RUBY
require 'sinatra'
require 'openssl'

# ton secret, par exemple récupéré d'une variable d'environnement
WEBHOOK_SECRET = ENV['WEBHOOK_SECRET'] || "secret-d-exemple"

def verify_signature(raw_body, signature)
  # Calcul de la signature HMAC SHA256
  digest = OpenSSL::Digest.new('sha256')
  expected = OpenSSL::HMAC.hexdigest(digest, WEBHOOK_SECRET, raw_body)

  # Comparaison en timing-safe (pour éviter les attaques type timing)
  Rack::Utils.secure_compare(expected, signature)
end

post '/my-webhook-endpoint' do
  # 1. Récupération du header
  header_signature = request.env['HTTP_X_WEBHOOK_SIGNATURE']

  # 2. Corps brut de la requête (sinon request.body.read)
  raw_body = request.body.read

  # 3. Vérification
  unless header_signature && verify_signature(raw_body, header_signature)
    halt 400, "Signature invalide"
  end

  # 4. Traiter le JSON (après la vérif)
  payload = JSON.parse(raw_body)
  # ... logique métier ...
  "OK"
end



PYTHON
from flask import Flask, request, abort
import hmac
import hashlib

app = Flask(__name__)

WEBHOOK_SECRET = "secret-d-exemple"

def verify_hmac_signature(secret, payload, signature):
    # Calcule la signature HMAC SHA-256
    computed_hash = hmac.new(
        secret.encode('utf-8'),
        payload,
        hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(computed_hash, signature)

@app.route('/my-webhook-endpoint', methods=['POST'])
def my_webhook():
    # 1. Récupérer la signature
    header_signature = request.headers.get('X-Webhook-Signature')

    # 2. Corps brut en bytes
    raw_body = request.get_data()  # renvoie un binaire

    # 3. Vérifier
    if not header_signature or not verify_hmac_signature(WEBHOOK_SECRET, raw_body, header_signature):
        abort(400, description="Signature invalide")

    # 4. Charger le JSON pour traitement
    payload = request.json  # ou json.loads(raw_body)
    # ... logique métier ...
    return "OK", 200

if __name__ == '__main__':
    app.run(debug=True, port=5000)




PHP (brut)
<?php
// ex: webhook.php
$secret = "secret-d-exemple"; // ou depuis un .env

// 1. Récupération de la signature envoyée (header)
$headerSignature = $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] ?? '';

// 2. Corps brut
$rawBody = file_get_contents('php://input');

// 3. Calcul HMAC
$computedHash = hash_hmac('sha256', $rawBody, $secret);

// 4. Comparaison (timing-safe)
if (!hash_equals($computedHash, $headerSignature)) {
    http_response_code(400);
    echo "Signature invalide";
    exit;
}

// 5. Décoder le JSON
$data = json_decode($rawBody, true);

// ... logique métier ...

// Réponse
http_response_code(200);
echo "OK";



PHP (Laravel)
public function handleWebhook(Request $request)
{
    $secret = env('WEBHOOK_SECRET', 'secret-d-exemple');
    $headerSignature = $request->header('X-Webhook-Signature');

    $rawBody = $request->getContent(); // corps brut
    $computedHash = hash_hmac('sha256', $rawBody, $secret);

    if (!hash_equals($computedHash, $headerSignature)) {
        return response('Signature invalide', 400);
    }

    // Traiter la charge utile
    $payload = json_decode($rawBody, true);
    // ... logique ...

    return response('OK', 200);
}




Java (Spring boot)
@RestController
public class WebhookController {

    // Dans un vrai projet, ce secret viendrait d'une config externe
    private static final String WEBHOOK_SECRET = "secret-d-exemple";

    @PostMapping("/my-webhook-endpoint")
    public ResponseEntity<String> handleWebhook(
        @RequestBody String rawBody,
        @RequestHeader(name = "X-Webhook-Signature", required = false) String signature
    ) {
        if (signature == null || !verifySignature(rawBody, signature)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Signature invalide");
        }

        // Ici, on parse le JSON si besoin
        // ObjectMapper mapper = new ObjectMapper();
        // Map<String, Object> payload = mapper.readValue(rawBody, Map.class);
        // ... logique ...

        return ResponseEntity.ok("OK");
    }

    private boolean verifySignature(String rawBody, String signature) {
        try {
            // Calcul de l'HMAC SHA256
            SecretKeySpec keySpec = new SecretKeySpec(WEBHOOK_SECRET.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(keySpec);
            byte[] rawHmac = mac.doFinal(rawBody.getBytes(StandardCharsets.UTF_8));
            String computedHash = bytesToHex(rawHmac);

            // Comparaison en timing-safe -> en Java, on peut utiliser MessageDigest.isEqual()
            // ou Apache Commons Codec
            return MessageDigest.isEqual(computedHash.getBytes(), signature.getBytes());
        } catch (Exception e) {
            return false;
        }
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}


*/

// TODO définir les payloads (user infos, payment infos)

