'use strict'

import { combineResolvers } from 'graphql-resolvers'
import { isAdmin, isAuthenticated } from '../authorization.js'
import { WebhookEventsService } from './webhook-events-service';
import { WebhooksLogsService } from './webhooks-logs-service';
import {WebhooksService} from './webhooks-service';

export default {
  Query : {
    // Query
    getAllWebhooks:combineResolvers(
      isAdmin,
      async(parent,arg,ctx)=>{
        return WebhooksService.getAllWebhooks(parent,arg,ctx)
      }
    ),

    //getWebhookById
    getWebhookById:combineResolvers(
      isAdmin,
      async(parent,arg,ctx)=>{
        return WebhooksService.getWebhookById(parent,arg,ctx)
      }
    ),

    getWebhookLogs:combineResolvers(
      isAdmin,
      async(parent,arg,ctx)=>{
        return WebhooksLogsService.getWebhookLogs(parent,arg,ctx)
      }
    ),

    getWebhookEvents:combineResolvers(
      isAdmin,
      async(parent,arg,ctx)=>{
        return WebhookEventsService.getWebhookEvents(parent,arg,ctx)
      }
    ),

    getWebhookEventsStats:combineResolvers(
      isAdmin,
      async(parent,arg,ctx)=>{
        return WebhookEventsService.getWebhookEventsStats(parent,arg,ctx)
      }
    )
  },
  Mutation : {
    /*
     # Mutation pour créer un nouveau webhook
        createWebhook(input: WebhookInput!): Webhook!

        # Mutation pour mettre à jour un webhook existant
        updateWebhook(id: ID!, input: WebhookInput!): Webhook!

        # Mutation pour supprimer un webhook
        deleteWebhook(id: ID!): Boolean!
     */
    createWebhook:combineResolvers(
      isAdmin,
      async(parent,arg,ctx)=>{
        return WebhooksService.createWebhook(parent,arg,ctx)
      }
    ),
    updateWebhook:combineResolvers(
      isAdmin,
      async(parent,arg,ctx)=>{
        return WebhooksService.updateWebhook(parent,arg,ctx)
      }
    ),
    deleteWebhook:combineResolvers(
      isAdmin,
      async(parent,arg,ctx)=>{
        return WebhooksService.deleteWebhook(parent,arg,ctx)
      }
    )
  }
}

