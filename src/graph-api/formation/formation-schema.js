import gql from 'graphql-tag'

/*
* LEGACY Formation -> Section(s) -> Step(s) -> Block(s) -> Element(s)
*
* NEW Formation : Formation = UE, contains Formation Modules
*/

export default gql`
    extend type Query {
        # FORMATIONS
        myFormations: [Formation]
        "Get Formation by id"
        formation(id: ID!): Formation

        sectionsInFormation(id: ID!): [FormationSection]
        stepsInSection(id: ID!): [FormationStep]

        "Query element by id"
        formationElement(id: ID!): FormationElement

        # Will be replaced by elementsInBlock
        elementsInStep(id: ID!): [FormationElement]

        blocksInStep(id: ID!): [FormationBlock]
        elementsInBlock(id: ID!): [FormationElement]

        elementsInCoursSupport(id: ID!): [FormationElement]
        elementsInEvent(id: ID!): [FormationElement]
        elementsInChallenge(id: ID!): [FormationElement]

        "Elements in header of the Exercise"
        elementsInQuestion(id: ID!): [FormationElement]
        "Elements in footer of the Exercise"
        elementsInQuestionFooter(id: ID!): [FormationElement]
        "Elements in header of the question serie"
        elementsInQcmHeader(id: ID!): [FormationElement]

        "Elements for schema correction"
        elementsInSchemaCorrection(schemaId: ID!): [FormationElement]

        "Elements for user properties (custom fields)"
        elementsForUserProperties: [FormationElement]

        "Elements for global announce"
        elementsForGlobalAnnounce(globalAnnounceType: String!): [FormationElement]

        formationProgress(formationId: ID!): [FormationProgress]

        groupsForFormationElementsUnlock(formationElementId: ID!): [Groupe]
        groupsForFormationElementsAccess(formationElementId: ID!): [Groupe]

        allTitles: [Title]

        "Elements before completion diapo synthese"
        elementsBeforeCompletionDiapoSynthese(id: ID!): [FormationElement]
        "Elements after completion diapo synthese"
        elementsAfterCompletionDiapoSynthese(id: ID!): [FormationElement]

    }

    extend type Mutation {
        #FORMATION CRUD
        createFormation(input: FormationInput!): Formation
        updateFormation(id: ID!, input: FormationInput!): Boolean
        deleteFormation(id: ID!): Boolean

        #FORMATION SECTION CRUD
        createFormationSection(input: FormationSectionInput!): Formation
        updateFormationSection(id: ID!, input: FormationSectionInput!): Boolean
        deleteFormationSection(id: ID!): Boolean

        #FORMATION STEP CRUD
        createFormationStep(input: FormationStepInput!): Formation
        updateFormationStep(id: ID!, input: FormationStepInput!): Boolean
        deleteFormationStep(id: ID!): Boolean

        #FormationElement CRUD
        createFormationElement(input: FormationElementInput!): Formation
        updateFormationElement(id: ID!, input: FormationElementInput!): Boolean
        deleteFormationElement(id: ID!): Boolean

        #FormationBLOCK CRUD
        createFormationBlock(input: FormationBlockInput!): FormationBlock
        updateFormationBlock(id: ID!, input: FormationBlockInput!): Boolean
        deleteFormationBlock(id: ID!): Boolean

        # Titles CRUD
        createTitle(input: TitleInput!): Title
        updateTitle(id: ID!, input: TitleInput!): Boolean
        deleteTitle(id: ID!): Boolean


        addGroupToFormation(groupId: ID!, formationId: ID!): Boolean
        removeGroupFromFormation(groupId: ID!, formationId: ID!): Boolean

        finishFormationStep(formationStepId: ID!, formationId: ID!): Boolean
        updateFormationProgression(formationId: ID!, formationStepId: ID!): Boolean

        startOrResumeFormationSession(formationId: ID!): FormationSession

        moveFormationElement(id: ID!, id2: ID, direction: String): Boolean

        "add group to unlock on a formation element"
        addGroupToFormationElement(groupId: ID!, formationElementId: ID!): Boolean
        "remove group to unlock on a formation element"
        removeGroupFromFormationElement(groupId: ID!, formationElementId: ID!): Boolean

        "add group access on a formation element"
        addGroupAccessToFormationElement(groupId: ID!, formationElementId: ID!): Boolean
        "remove group access on a formation element"
        removeGroupAccessFromFormationElement(groupId: ID!, formationElementId: ID!): Boolean

        "Watermark CRUD Files => Gère la création / deletion des watermarkImage dans la table file, supporte également l'opération en masse"
        createOrDeleteWatermarkPictureElementPdf(file:Upload,action:String!,idArray:[ID!]!):String

        # Gestion des images de chapitre. Normalement suffisament sécurisé pour gérer UNIQUEMENT les files créées par cet endpoint => Sinon security error.
        vidstackManageChapterImages(previousTrackImages:[VidstackTrackUploadImage],newTrackImages:[VidstackTrackUploadImage]):[VidstackTrackUploadImageOutput]

        "Endpoint to upload file to tempfolder and to get an URL => used on diapo formation element to create temp public URL of ppt, pptx and other proprietaries files"
        uploadTempFile(tempFile:Upload):String

        "gestion des fichier de diapo"
        diapoFormationElementManageTrackFile(previousTrackDiapo:[DiapoTrackUploadFileInput],newTrackDiapo:[DiapoTrackUploadFileInput]):[DiapoTrackUploadFileOutput]
    }


    input DiapoTrackUploadFileInput{
      id:ID!
      file:Upload
      src:String
    }

    type DiapoTrackUploadFileOutput{
      id:ID!
      src:String
    }

    input VidstackTrackUploadImage{
      id:ID!
      src:String
      file:Upload
    }

    type VidstackTrackUploadImageOutput{
      id:ID!
      src:String
    }

    "Formation type, not used anymore"
    type Formation {
        id: ID!
        name: String
        description: String
        image: String
        color1: String
        color2: String
        # Show order
        order: Int
        isPublished: Boolean
        authorId: ID
        #author: User
        createdAt: Date
        updatedAt: Date

        groupes: [Groupe]
        formationSections: [FormationSection]

        cours: Cours

        numberOfSections: Int
        numberOfStepsDone: Int
        numberOfSteps: Int

        session: FormationSession
        progress: FormationProgress
    }
    "Formation type input, not used anymore"
    input FormationInput {
        name: String
        description: String
        image: Upload
        color1: String
        color2: String
        # Show order
        order: Int
        isPublished: Boolean
        authorId: ID
        coursId: ID
    }

    type FormationSession {
        id: ID
        isActive: Boolean
        isFinished: Boolean
        currentStepId: ID
        formationId: ID
        #stepsDone: Int
        #elementsDone: Int
        createdAt: Date
        updatedAt: Date
    }

    type FormationProgress {
        # Stats
        id: ID
        isActive: Boolean
        isFinished: Boolean
        formationStepId: ID
        formationId: ID
        #stepsDone: Int
        #elementsDone: Int
        createdAt: Date
        updatedAt: Date
    }

    "Formation Section, not used anymore"
    type FormationSection {
        id: ID!
        name: String
        description: String
        image: String
        # Show order
        order: Int
        isPublished: Boolean
        authorId: ID
        #author: User

        formationId: ID
        parentSectionId: ID

        "Infinite sub section"
        parentSection: FormationSection

        "Parent formation data"
        parentFormation: Formation

        formationElements: [FormationElement]

        steps: [FormationStep]

        createdAt: Date
        updatedAt: Date
    }
    input FormationSectionInput {
        name: String
        description: String
        image: Upload
        order: Int
        isPublished: Boolean
        authorId: ID
        parentSectionId: ID

        formationId: ID
    }

    "Formation Step"
    type FormationStep {
        id: ID!
        name: String
        description: String
        image: String
        icon: String

        progress: FormationProgress

        # Show order
        order: Int
        isPublished: Boolean
        authorId: ID
        sectionId: ID
        #author: User
        createdAt: Date
        updatedAt: Date
    }
    input FormationStepInput {
        name: String
        description: String
        image: Upload
        icon: Upload
        order: Int
        isPublished: Boolean
        authorId: ID
        sectionId: ID
    }

    "Title type, with a name and differents settings"
    type Title {
        id: ID!
        name: String
        size: Int
        offset: Int
        level: Int
        fontWeight: Int

        color1: String
        color2: String
        backgroundColor: String

        createdAt: Date
        updatedAt: Date
    }
    input TitleInput {
        name: String
        size: Int
        offset: Int
        fontWeight: Int
        level: Int
        color1: String
        color2: String
        backgroundColor: String
    }

    # FORMATION ELEMENT
    type FormationElement {
        id: ID!
        "Element name"
        name: String
        name_en: String
        name_es: String
        name_it: String
        name_de: String
        "Element description"
        description: String
        description_en: String
        description_es: String
        description_it: String
        description_de: String
        "Element text content, or link"
        text: String
        text_en: String
        text_es: String
        text_it: String
        text_de: String
        "Element image"
        image: String
        "Element type"
        type: String
        # Show order
        "Order among other elements"
        order: Int
        "Element's creator id"
        authorId: ID
        #author: User
        objectId: ID
        "Target Course id in element"
        coursId: ID
        qcmId: ID
        "Target MCQ id in element"
        mcqId: ID
        "Element's settings"
        settings: JSON
        "Formation step parent"
        formationStepId: ID
        "Element's parent block ID"
        blockId: ID
        "Course support elements"
        coursSupportId: ID
        "Event elements"
        eventId: ID
        "Question with element header"
        questionId: ID
        "Question footer elements"
        footerQuestionId: ID
        "MCQ with element header"
        headerMcqId: ID
        "Correction schema library id elements link (if any)"
        correctionSchemaLibraryId: ID
        "Element's title ID (if any)"
        titleId: ID
        "Challenge id"
        challengeId: ID
        "Forfait ID"
        forfaitId: ID
        "Target form id (if type is form)"
        formId: ID
        "Element's title settings (if type is title)"
        title: Title
        "If element is accessible by all groups or not"
        isAccessible: Boolean

        "If element is an input for a user property, this is the parent folder user property id"
        userPropertyFolderId: ID
        userPropertyValue(userId: ID): UserPropertyData
        #userPropertyValues: [UserPropertyData]

        "If element is a global announce, this is the type of global announce"
        globalAnnounceType: String

        forfaitIds: [ID]

        "Config ID (for mobile app)"
        configId: ID

        "Do single exercise ID"
        doQuestionId: ID

        diapoSyntheseElementBeforeId: ID
        diapoSyntheseElementAfterId: ID

        "Target event id when element represent an event"
        targetEventId: ID
        "Target exam id when element represent an exam"
        targetExamId: ID
        validationSettings: JSON

        gptPrecisionPrompt: String
        createdAt: Date
        updatedAt: Date

        "FileName of a file in s3 bucket (path can be deducted)"
        s3FileName:String

        "Filename du package SCORM"
        scormFileName:String

        "Internal path du package scorm"
        scormFilePath:String

        "version du package scorm"
        scormVersion:String
        "entry point du package scorm"
        scormEntryPoint:String

        scormParsing:JSON
    }
    input FormationElementInput {
        "If element is a global announce, this is the type of global announce"
        globalAnnounceType: String

        "If true, the file will be uploaded in the public folder"
        uploadInPublicFolder: Boolean

        "Element name"
        name: String
        name_en: String
        name_es: String
        name_it: String
        name_de: String
        "Element description"
        description: String
        description_en: String
        description_es: String
        description_it: String
        description_de: String
        "Element text content, or link"
        text: String
        text_en: String
        text_es: String
        text_it: String
        text_de: String
        image: Upload
        file: Upload
        type: String
        formationStepId: ID
        settings: JSON
        # Show order
        order: Int
        authorId: ID
        objectId: ID
        "The target element course"
        coursId: ID
        "Parent Block in Formation"
        blockId: ID
        mcqId: ID
        "References associated course (supports secondaires)"
        coursSupportId: ID
        "Question with element header"
        questionId: ID
        "Question footer elements"
        footerQuestionId: ID
        "MCQ with element header"
        headerMcqId: ID
        "References associated event"
        eventId: ID
        "If this is a title element, references the title type"
        titleId: ID
        "Challenge id"
        challengeId: ID
        "If element is accessible by all groups or not"
        isAccessible: Boolean
        "Forfait ID"
        forfaitId: ID
        userPropertyFolderId: ID
        gptPrecisionPrompt: String
        "Config ID (for mobile app)"
        configId: ID
        "Target form id (if type is form)"
        formId: ID

        "Do single exercise ID"
        doQuestionId: ID

        "Correction schema library id elements link (if any)"
        correctionSchemaLibraryId: ID

        "Target event id when element represent an event"
        targetEventId: ID
        "Target exam id when element represent an exam"
        targetExamId: ID

        diapoSyntheseElementBeforeId: ID
        diapoSyntheseElementAfterId: ID

        validationSettings: JSON

        "s3FileName => Nom de la ressource dans le cloud s3"
        s3FileName:String

        "Package SCORM si besoin d'upload"
        scormFileToUpload:Upload
    }


    # FORMATION BLOCK Contains elements
    type FormationBlock {
        id: ID!
        name: String
        type: String
        # Show order
        order: Int
        authorId: ID
        coursId: ID
        titleId: ID
        "The block title type"
        title: Title

        "Optional, for summary only"
        titles: [FormationElement]

        elements: [FormationElement]

        #author: User
        formationStepId: ID
        settings: JSON
        createdAt: Date
        updatedAt: Date
    }
    input FormationBlockInput {
        name: String
        type: String
        # Show order
        order: Int
        authorId: ID
        titleId: ID
        coursId: ID
        #author: User
        formationStepId: ID
        settings: JSON
        createdAt: Date
        updatedAt: Date
    }

`
