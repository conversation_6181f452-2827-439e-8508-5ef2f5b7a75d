import fs from 'fs'
import { GraphQLError } from 'graphql';
import { Op } from 'sequelize';
import models from '../../models';
import { ELEMENTS_TYPE } from '../../models/formation/formation_element'
import { LOG_OPERATIONS } from '../../models/log/log.js';
import { WatermarkService } from '../../service/watermark-service.js'
import { UPLOAD_FOLDER_MAP } from '../../util/utils';
import { HubspotService } from '../config/hubspot/HubspotService.js';
import { UploadService } from '../file/upload-service';
import LogService from '../log/log-service.js';
import { PermissionService } from '../permission/permission-service.js';
import { S3Service } from '../s3/s3-service'
import { createHash } from 'crypto';
import {redisClient} from '../../models'
import {ScormService} from '../scorm/scorm-service'

'use strict';

// Copie des keys du front, nécessaire à la gestion des images de vidstackTracksObject
export const ONE_TRACK_KEY = {
  TITRE: 'TITRE',
  FIN: 'FIN',
  ID: 'ID',
  INDEX: 'INDEX',
  DAYJS: 'DAYJS',
  HOOVER: 'HOOVER',
  THUMBNAIL_STRUCTURE:'THUMBNAIL_STRUCTURE',
  THUMBNAIL_SRC:'THUMBNAIL_SRC'
};

// Cache manager
let isCacheLocked=false // lock
const IS_CACHE_READY_KEY="fe:ready"
const MAP_KEY = 'fe:map'


const CRUDFormation = {
  //////// CRUD Formation //////////
  createFormation: async (formation) => {
    try {
      let coursId = JSON.parse(JSON.stringify(formation?.coursId));
      delete formation?.coursId;

      if (formation.image) {
        formation.image = await UploadService.uploadFileDataToFolder(formation.image, UPLOAD_FOLDER_MAP.files);
      }
      const formationInstance = await models.Formation.create(formation);

      if (coursId) {
        const cours = await models.Cours.findByPk(coursId);
        cours.formationId = formationInstance?.id;
        await cours.save();
      }

      return formationInstance;
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création de la formation');
    }
  },
  updateFormation: async (id, input) => {
    try {
      if (input.image) {
        input.image = await UploadService.uploadFileDataToFolder(input.image, UPLOAD_FOLDER_MAP.files);
      }
      let updated = await models.Formation.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour formation ' + id);
    }
  },
  deleteFormation: async (id, userId) => {
    try {
      return await models.Formation.destroy({ where: { id } });
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error suppression formation ' + id);
    }
  },
};

const CRUDFormationSection = {
/////////////
  createFormationSection: async (formation, userId) => {
    try {
      if (formation.image) {
        formation.image = await UploadService.uploadFileDataToFolder(formation.image, UPLOAD_FOLDER_MAP.files);
      }
      return await models.FormationSection.create(formation);
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création de la section');
    }
  },
  updateFormationSection: async (id, input) => {
    try {
      if (input.image) {
        input.image = await UploadService.uploadFileDataToFolder(input.image, UPLOAD_FOLDER_MAP.files);
      }
      let updated = await models.FormationSection.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour section ' + id);
    }
  },
  deleteFormationSection: async (id, userId) => {
    try {
      return await models.FormationSection.destroy({ where: { id } });
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error suppression section ' + id);
    }
  },
};

const CRUDFormationElement = {

  handleUploadFiles: async (element,ctx={}) => {
    try {
      let hasImageSet = false;
      const folderToUpload = element?.uploadInPublicFolder ? UPLOAD_FOLDER_MAP.public : UPLOAD_FOLDER_MAP.files;
      if (element.file) {
        element.text = await UploadService.uploadFileDataToFolder(element.file, folderToUpload);
        // If it's PDF and no custom image set, create pdf preview
        if (element.text.split('.').pop().toLowerCase() === 'pdf' && !element.image) {
          // https://stackoverflow.com/questions/190852/how-can-i-get-file-extensions-with-javascript
          try {
            element.image = await UploadService.createOnePreviewPdf(element.text); // working
            hasImageSet = true;
          } catch (ex) {
            console.error(ex);
          }
        }
      }
      // If image is set
      if (!hasImageSet && element.image) {
        element.image = await UploadService.uploadFileDataToFolder(element.image, folderToUpload);
      }

      // Si package Scorm
      if (element?.scormFileToUpload){

        // Check de si y a un scorm précédent à delete
        const previousScorm = element?.scormFilePath && element.scormFilePath
        const previousScormFilePath= element?.scormFilePath
        const previousScormEntryPoint=element?.scormEntryPoint

        // Upload et traitement du package
        const {filename,sanitizedFilename,version,entryPoint,parsingObj} = await ScormService.uploadScormPackage({
          args:{
            upload:element.scormFileToUpload,
          },
          ctx
        })
        element.scormEntryPoint=entryPoint
        element.scormFilePath=sanitizedFilename
        element.scormVersion=version
        element.scormFileName=filename
        element.scormParsing=parsingObj

        // delete de l'ancien
        if (previousScorm){

          // Previous FE pour les logs
          const formationElementLinkedToScromToDelete=await models.FormationElement.findOne({where:{scormFilePath:previousScormFilePath,scormEntryPoint:previousScormEntryPoint}})

          const pathToDelete=`${process.cwd()}/${UPLOAD_FOLDER_MAP.scorm}/${previousScorm}`

          // Log de l'écriture du package SCORM
          await LogService.logAction({
            logOperation: LOG_OPERATIONS.Scorm.DeleteScormPackageFromFormationElement.action,
            logData:{
              previousScormFilePath:previousScormFilePath,
              previousScormEntryPoint:previousScormEntryPoint,
            },
            foreignIds:{
              userId:ctx?.me?.id,
              formationElementId:formationElementLinkedToScromToDelete.id
            },
            ip:ctx?.ip,
            req:ctx?.req,
            models:ctx?.models,
            userId: ctx?.me?.id,
          });



          fs.rm(pathToDelete, { recursive: true, force: true }, (err) => {
            if (err) {
              console.error(`Failed to delete folder: ${err.message}`);
            } else {
              console.log(`Folder deleted successfully: ${pathToDelete}`);
            }
          });

          //throw new Error("test")
        }
      }

      return element;
    }catch(e){
      console.error("Error handleUploadFile,  e :",e)
      throw new GraphQLError(e)
    }
  },

  createFormationElement: async (element, userId,ctx) => {
    try {
      const { me, req, ip }=ctx
      element = await FormationService.handleUploadFiles(element,ctx);

      delete element?.uploadInPublicFolder;
      element.settings = element.settings || {};

      let elem = await models.FormationElement.create(element);

      // Maintenant que l'on a créé le formationElement, alors on peut link le scorm au FE
      if (element.scormFileToUpload && elem){
        await LogService.logAction({
          logOperation: LOG_OPERATIONS.Scorm.LinkScormToFormationElement.action,
          logData:{
            scormFileName:elem?.scormFileName,
            scormFilePath:elem?.scormFilePath,
            scormEntryPoint:elem?.scormEntryPoint,
            scormVersion:elem?.scormVersion,
            scormParsing:elem?.scormParsing,
          },
          foreignIds:{
            userId:ctx?.me?.id,
            formationElementId:elem?.id
          },
          ip, req,
          models:ctx?.models,
          userId: me?.id,
        });
      }

      const hubspotProperty = HubspotService.createSafeHubspotPropertyName(elem);
      elem.settings = {...elem.settings, hubspotProperty}; // Ajout de la propriété hubspot
      elem.order = elem.id;
      await elem.save();

      // Update du cache pour le texte
      if (elem.text){FormationService.FormationElementCacheManager.updateCacheSingleFile(elem.id,elem.text)}

      // Si il y a un s3FileName, ça veut dire que le front l'a déjà upload au bucket. On le log ici
      if (elem?.s3FileName){
        await LogService.logAction({
          logOperation:LOG_OPERATIONS.S3.UploadFileConfirmation.action,
          logData:{fileName:element?.s3FileName},
          models:ctx?.models,
          userId:ctx?.me.id,
        })
      }

      if (element.coursSupportId) {
        let text = `Création d\'un support de cours : ${element?.name || ''} (${element?.type})`;
        const coursSupport = await models.CoursSupport.findByPk(element.coursSupportId);
        if (coursSupport) {
          const coursId = coursSupport.coursId;
          await LogService.logAction({
            logOperation: LOG_OPERATIONS.Cours.Update.action,
            logData: {
              text,
            },
            foreignIds: {
              coursId,
              userId: me.id,
            },
            ip,
            req,
            models,
            userId: me.id,
          });
        }
      }

      if (element.questionId) {
        let text = `Création élément au dessus de question : ${element?.name || ''} (${element?.type})`;
        const question = await models.Question.findByPk(element.questionId);
        if (question) {
          const questionId = question.id_question;
          await LogService.logAction({
            logOperation: LOG_OPERATIONS.Question.Update.action,
            logData: {
              text,
            },
            foreignIds: {
              questionId,
              userId: me.id,
            },
            ip,
            req,
            models,
            userId: me.id,
          });
        }
      }

      if (element.footerQuestionId) {
        let text = `Création élément de correction question : ${element?.name || ''} (${element?.type})`;
        const question = await models.Question.findByPk(element.footerQuestionId);
        if (question) {
          const questionId = question.id_question;
          await LogService.logAction({
            logOperation: LOG_OPERATIONS.Question.Update.action,
            logData: {
              text,
            },
            foreignIds: {
              questionId,
              userId: me.id,
            },
            ip,
            req,
            models,
            userId: me.id,
          });
        }
      }

      if (element.headerMcqId) {
        let text = `Création d'annexe sur une série : ${element?.name || ''} (${element?.type})`;
        const qcm = await models.Qcm.findByPk(element.headerMcqId);
        if (qcm) {
          const mcqId = qcm.id_qcm;
          await LogService.logAction({
            logOperation: LOG_OPERATIONS.Qcm.Update.action,
            logData: {
              text,
            },
            foreignIds: {
              mcqId,
              userId: me.id,
            },
            ip,
            req,
            models,
            userId: me.id,
          });
        }
      }

      return elem;
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création de la section');
    }
  },
  updateFormationElement: async (id, input, ctx) => {
    try {

      const { me, req, ip }=ctx

      // récupération de l'élément avant modif => Pour log watermark
      const previousElement = await models.FormationElement.findByPk(id)

      // Enrichissement de input avec scormFilePath afin de savoir dans handleUploadFile si il y a besoin de supprimer un package SCORM + avoir les données pour le log
      input.scormFilePath=previousElement.scormFilePath
      input.scormEntryPoint=previousElement.scormEntryPoint

      // Creation image, pdf, S3 file si besoin
      input = await FormationService.handleUploadFiles(input,ctx);
      delete input?.uploadInPublicFolder;

      // Delete de file S3 si besoin => Si previous.fileName !== null et input.fileName===null
      // C'est le frontEnd qui se charge de l'upload. Là on sync simplement. Mais ça peut créer des fichiers permanant si le frontend upload le fichier et s'intéromp avant que l'on envoie l'update au back => prob rare
      if (
        (previousElement?.s3FileName !== input?.s3FileName)
      ){
        // Si input existe et est un nom de fichier :
        if (typeof input?.s3FileName === "string" && input?.s3FileName){
          await LogService.logAction({
            logOperation:LOG_OPERATIONS.S3.UploadFileConfirmation.action,
            logData:{fileName:input?.s3FileName},
            models:ctx?.models,
            userId:ctx?.me.id,
          })
        }

        // si previous input existe et est un nom de fichier :
        if (typeof previousElement?.s3FileName  ==="string" && previousElement?.s3FileName){
          await S3Service.deleteFileFromBucket({args:{fileName:previousElement.s3FileName},ctx})
        }
      }

      // VERIF AVEC SYLVAIN QUE Y A PAS DE SOUCIS MAIS NORMALEMENT ISOKE
      // Delete de previous image si besoin (previous a image et pas actuel)

      // TODO => Vérifier validité de cette ligne
      //if ((previousElement?.image !==null && input?.image!==null) && (previousElement?.image!==input?.image) ){
      //  console.log("delete image ")
      //  await UploadService.deleteFile(previousElement.image)
      //}

      let updated = await models.FormationElement.update(
        input, {
          where: { id: id },
        },
      )

      if (input?.text){FormationService.FormationElementCacheManager.updateCacheSingleFile(id,input.text)}

      // Emission de log de watermark
      await WatermarkService.emitWatermarkLogIfNeeded(id, previousElement.settings, input.settings, {
        me,
        req,
        ip
      }, "elementPdf")

      // Handles logging
      if (input.coursSupportId) {
        // input.name (fiche, etc)
        // input.type (file, etc)
        let text = `Mise à jour d\'un support de cours : ${input?.name || ''} (${input?.type})`;
        const coursSupport = await models.CoursSupport.findByPk(input.coursSupportId);
        if (coursSupport) {
          const coursId = coursSupport.coursId;
          await LogService.logAction({
            logOperation: LOG_OPERATIONS.Cours.Update.action,
            logData: {
              text,
            },
            foreignIds: {
              coursId,
              userId: me.id,
            },
            ip,
            req,
            models,
            userId: me.id,
          });
        }
      }

      if (input.questionId) {
        let text = `Mise à jour élément au dessus de question : ${input?.name || ''} (${input?.type})`;
        const question = await models.Question.findByPk(input.questionId);
        if (question) {
          const questionId = question.id_question;
          await LogService.logAction({
            logOperation: LOG_OPERATIONS.Question.Update.action,
            logData: {
              text,
            },
            foreignIds: {
              questionId,
              userId: me.id,
            },
            ip,
            req,
            models,
            userId: me.id,
          });
        }
      }

      if (input.footerQuestionId) {
        let text = `Mise à jour élément de correction question : ${input?.name || ''} (${input?.type})`;
        const question = await models.Question.findByPk(input.footerQuestionId);
        if (question) {
          const questionId = question.id_question;
          await LogService.logAction({
            logOperation: LOG_OPERATIONS.Question.Update.action,
            logData: {
              text,
            },
            foreignIds: {
              questionId,
              userId: me.id,
            },
            ip,
            req,
            models,
            userId: me.id,
          });
        }
      }

      if (input.headerMcqId) {
        let text = `Mise à jour d'annexe sur une série : ${input?.name || ''} (${input?.type})`;
        const qcm = await models.Qcm.findByPk(input.headerMcqId);
        if (qcm) {
          const mcqId = qcm.id_qcm;
          await LogService.logAction({
            logOperation: LOG_OPERATIONS.Qcm.Update.action,
            logData: {
              text,
            },
            foreignIds: {
              mcqId,
              userId: me.id,
            },
            ip,
            req,
            models,
            userId: me.id,
          });
        }
      }

      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour section ' + id);
    }
  },
  deleteFormationElement: async (id, ctx) => {
    // TODO delete files as well
    const formationElement = await models.FormationElement.findByPk(id);

    if (formationElement?.s3FileName){
      S3Service.deleteFileFromBucket({args:{fileName:formationElement.s3FileName},ctx})
    }

    if (formationElement?.settings?.vidstackTrackObject){
      // Pour supprimer les images associées à un FE Video lorsque l'on delete, on a juste à dire dans cette fonction, que previous avait chaque image, et que new = rien

      const tracks=JSON.parse(formationElement?.settings?.vidstackTrackObject)

      const KEY_MAP = {
        [ONE_TRACK_KEY.ID]:               'id',
        [ONE_TRACK_KEY.THUMBNAIL_SRC]:    'src',
        [ONE_TRACK_KEY.THUMBNAIL_STRUCTURE]: 'file',
      };

      const arrayOfKeys = Object.keys(KEY_MAP);

      // Fonction helper du formatage de trackObject pour le management des images du chapitre vidstack
      const input = Object.values(tracks).map(track =>
        Object.entries(track)
          .filter(([key]) => arrayOfKeys.includes(key))
          .reduce((acc, [key, value]) => {
            const newKey = KEY_MAP[key] || key;
            acc[newKey] = value;
            return acc;
          }, {})
      );

      const deleteTrackImageArg={previousTrackImages:input,newTrackImages:[]}
      await FormationService.vidstackManageChapterImages({args:deleteTrackImageArg,ctx})
    }

    formationElement.mcqId = null;
    formationElement.formationStepId = null;
    formationElement.coursId = null;
    formationElement.authorId = null;
    formationElement.blockId = null;
    formationElement.coursSupportId = null;
    formationElement.eventId = null;
    formationElement.titleId = null;
    formationElement.eventId = null;
    formationElement.questionId = null;
    formationElement.headerMcqId = null;
    formationElement.footerQuestionId = null;
    formationElement.challengeId = null;
    await formationElement.save();
    return await models.FormationElement.destroy({ where: { id } });
  },
};

const CRUDFormationBlock = {
  createFormationBlock: async (formation, userId) => {
    try {
      return await models.FormationBlock.create(formation);
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création du block');
    }
  },
  updateFormationBlock: async (id, input) => {
    try {
      let updated = await models.FormationBlock.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour block ' + id);
    }
  },
  deleteFormationBlock: async (id, userId) => {
    try {
      return await models.FormationBlock.destroy({ where: { id } });
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error suppression block ' + id);
    }
  },
};


const CRUDTitle = {
  createTitle: async (title, userId) => {
    try {
      return await models.Title.create(title);
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création du titre');
    }
  },
  updateTitle: async (id, input) => {
    try {
      let updated = await models.Title.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour title ' + id);
    }
  },
  deleteTitle: async (id, userId) => {
    try {
      return await models.Title.destroy({ where: { id } });
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Erreur suppression title ' + id);
    }
  },
};


const CRUDFormationStep = {
  createFormationStep: async (formation, userId) => {
    try {
      if (formation.image) {
        formation.image = await UploadService.uploadFileDataToFolder(formation.image, UPLOAD_FOLDER_MAP.files);
      }
      return await models.FormationStep.create(formation);
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création de la section');
    }
  },
  updateFormationStep: async (id, input) => {
    try {
      if (input.image) {
        input.image = await UploadService.uploadFileDataToFolder(input.image, UPLOAD_FOLDER_MAP.files);
      }
      let updated = await models.FormationStep.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour section ' + id);
    }
  },
  deleteFormationStep: async (id, userId) => {
    try {
      return await models.FormationStep.destroy({ where: { id } });
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error suppression section ' + id);
    }
  },
};


export const FormationService = {
  ...CRUDFormation,
  ...CRUDFormationSection,
  ...CRUDFormationStep,
  ...CRUDFormationBlock,
  ...CRUDFormationElement,
  ...CRUDTitle,

  getMyFormations: async (models, me) => {
    try {
      if (me.role === 'ADMIN') {
        return await models.Formation.findAll({
          order: [['order', 'ASC']],
        });
      }
      const mesGroupes = await PermissionService.Groupe.getUserGroups(me);
      const formationGroups = await models.FormationGroups.findAll({
        where: {
          groupeId: mesGroupes?.map(g => g.id),
        },
      });
      return await models.Formation.findAll({
        where: {
          id: formationGroups?.map(g => g.formationId),
          isPublished: true,
        },
        order: [['order', 'ASC']],
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création du forum');
    }
  },

  async getFormationById(id, userId) {
    return models.Formation.findByPk(id);
  },

  /*  */
  getSectionsInFormation(formationId, userId) {
    return models.FormationSection.findAll({
      where: {
        formationId: formationId,
        //isPublished: true
      },
      order: [['order', 'ASC']],
    });
  },

  /* -------- */
  /* Elements */
  /* -------- */

  /* Elements in block */
  async elementsInBlock(blockId, userId) {
    const elements = await models.FormationElement.findAll({
      where: {
        blockId: blockId,
      },
      order: [['order', 'ASC']],
    });

    return await PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, userId);
  },
  /* Elements in support cours */
  async elementsInCoursSupport(coursSupportId, userId) {
    const elements = await models.FormationElement.findAll({
      where: {
        coursSupportId,
      },
      order: [['order', 'ASC']],
    });
    return await PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, userId);
  },
  /* Elements in event */
  async elementsInEvent(eventId, userId) {
    const elements = await models.FormationElement.findAll({
      where: {
        eventId,
      },
      order: [['order', 'ASC']],
    });

    return PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, userId);
  },
  /* Elements in challenge */
  async elementsInChallenge(challengeId, userId) {
    const elements = await models.FormationElement.findAll({
      where: {
        challengeId,
      },
      order: [['order', 'ASC']],
    });

    return PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, userId);
  },

  /* Elements in question */
  async elementsInQuestion(questionId, userId) {
    const elements = await models.FormationElement.findAll({
      where: {
        questionId,
      },
      order: [['order', 'ASC']],
    });
    return PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, userId);
  },

  /* Elements in question footer */
  async elementsInQuestionFooter(questionId, userId) {
    const elements = await models.FormationElement.findAll({
      where: {
        footerQuestionId: questionId,
      },
      order: [['order', 'ASC']],
    });
    return PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, userId);
  },

  /* Elements in question footer */
  async elementsInSchemaCorrection(schemaId, ctx) {
    const elements = await models.FormationElement.findAll({
      where: {
        correctionSchemaLibraryId: schemaId,
      },
      order: [['order', 'ASC']],
    });
    return PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, ctx.me.id);
  },
  /* Elements in qcm header */
  async elementsInQcmHeader(headerMcqId, userId) {
    const elements = await models.FormationElement.findAll({
      where: {
        headerMcqId,
      },
      order: [['order', 'ASC']],
    });
    return PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, userId);
  },

  /* Elements in steps */
  elementsInStep(formationStepId, userId) {
    //TODO
    return models.FormationElement.findAll({
      where: {
        formationStepId: formationStepId,
        //isPublished: true
      },
      order: [['order', 'ASC']],
    });
  },

  blocksInStep(id, userId) {
    return models.FormationBlock.findAll({
      where: {
        formationStepId: id,
      },
      order: [['order', 'ASC']],
    });
  },


  getStepsInSection(sectionId, userId) {
    return models.FormationStep.findAll({
      where: {
        formationStepId: sectionId,
        //isPublished: true
      },
      order: [['order', 'ASC']],
    });
  },


  async addGroupToFormation({ groupId, formationId }, me) {
    try {
      const groupe = await models.Groupe.findByPk(groupId);
      await groupe.addFormation(formationId);
      return !!(await groupe.save());
    } catch (e) {
      console.error(e);
    }
    return Promise.resolve(undefined);
  },
  async removeGroupFromFormation({ groupId, formationId }, me) {
    try {
      const groupe = await models.Groupe.findByPk(groupId);
      await groupe.removeFormation(formationId);
      return !!(await groupe.save());
    } catch (e) {
      console.error(e);
    }
  },


  /* Auto Unlock group after formationelement */
  async addGroupToFormationElement({ groupId, formationElementId }, me) {
    try {
      const groupe = await models.Groupe.findByPk(groupId);
      //consoleLogSequelizeMagicMethods(groupe);
      await groupe.addFormationElement(formationElementId);
      return !!(await groupe.save());
    } catch (e) {
      console.error(e);
    }
    return Promise.resolve(undefined);
  },
  async removeGroupFromFormationElement({ groupId, formationElementId }, me) {
    try {
      const groupe = await models.Groupe.findByPk(groupId);
      await groupe.removeFormationElement(formationElementId);
      return !!(await groupe.save());
    } catch (e) {
      console.error(e);
    }
  },


  async addGroupAccessToFormationElement({ groupId, formationElementId }, me) {
    try {
      const groupe = await models.Groupe.findByPk(groupId);
      //consoleLogSequelizeMagicMethods(groupe);
      await groupe.addFormationElementAccess(formationElementId);
      return !!(await groupe.save());
    } catch (e) {
      console.error(e);
    }
    return Promise.resolve(undefined);
  },
  async removeGroupAccessFromFormationElement({ groupId, formationElementId }, me) {
    try {
      const groupe = await models.Groupe.findByPk(groupId);
      await groupe.removeFormationElementAccess(formationElementId);
      return !!(await groupe.save());
    } catch (e) {
      console.error(e);
    }
  },


  async formationProgress(id, userId) {
    try {
      return await models.FormationProgress.findAll({
        where: {
          formationId: id,
          userId: userId,
        },
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  // Update current step
  async updateFormationProgression({ formationId, formationStepId }, me) {
    try {
      let formationProgress = await models.FormationProgress.findOne({
        where: {
          formationId: formationId,
          formationStepId,
          userId: me.id,
        },
      });
      if (formationProgress) {
        formationProgress.isActive = true;
        await formationProgress.save();
      } else {
        formationProgress = await models.FormationProgress.create({
          formationId,
          formationStepId,
          userId: me.id,
          isActive: true,
        });
      }
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async formationStepProgress(formationStepId, userId) {
    try {
      // get formation id
      const step = await models.FormationStep.findByPk(formationStepId);
      const section = await models.FormationSection.findByPk(step?.id);
      const formationId = section?.formationId;
      return await models.FormationProgress.findOne({
        where: {
          formationId,
          formationStepId,
          userId,
        },
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async finishFormationStep(formationStepId, formationId, me) {
    try {
      // Set current step to finished
      const formationStepProgress = await models.FormationProgress.create({
        formationStepId,
        userId: me?.id,
        formationId: formationId,
        isFinished: true,
      });

      // Update session to next step
      const isNotFinished = await FormationService.setSessionNextStep(
        {
          afterStepId: formationStepId,
          forUserId: me?.id,
          formationId: formationId,
        },
      );

      return isNotFinished;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async createSessionFor(formationId, userId) {
    return models.FormationSession.create({
      formationId,
      userId,
    });
  },
  async getSessionFor(formationId, userId) {
    return models.FormationSession.findOne({
      where: {
        formationId,
        userId,
      },
    });
  },


  /*  */
  async startOrResumeFormationSession({ formationId }, me) {
    try {
      let session = await FormationService.getSessionFor(formationId, me?.id);
      let stepId;
      // First STEP ID
      if (!session?.currentStepId) {
        // SET FIRST STEP
        const firstSection = await models.FormationSection.findOne({
          where: {
            formationId,
          },
          order: [['order', 'ASC']],
        });
        if (!firstSection) {
          throw new GraphQLError('Aucune section dans la formation');
        }
        let sectionId = firstSection?.id;

        const firstStepFormation = await models.FormationStep.findOne({
          where: {
            sectionId,
          },
          attributes: ['id'],
          order: [['order', 'ASC']],
        });
        if (!firstStepFormation) {
          throw new GraphQLError('Aucune étape dans la section');
        }
        stepId = firstStepFormation?.id;
      }
      // NEXT STEP ID
      if (session?.currentStepId !== null) {

      }
      if (session) {
        session.isActive = true;
        session.currentStepId = stepId;
        await session.save();
      } else {
        session = await models.FormationSession.create({
          currentStepId: stepId,
          userId: me?.id,
          formationId,
          isActive: true,
        });
      }
      return session;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },


  async setSessionNextStep({ afterStepId, forUserId, formationId }) {
    try {
      let session = await FormationService.getSessionFor(formationId, forUserId);
      let lastStep = await models.FormationStep.findByPk(afterStepId);
      let lastSectionId = lastStep?.sectionId;

      // Already done steps
      const stepsDone = await models.FormationProgress.findAll({
        where: {
          userId: forUserId,
          formationId: formationId,
        },
      });
      const alreadyDoneStepsIds = stepsDone?.map((step) => step.formationStepId);

      // Try to find in same section
      let nextStep = await models.FormationStep.findOne({
        where: {
          sectionId: lastSectionId,
          id: {
            [Op.notIn]: alreadyDoneStepsIds,
          },
        },
        order: [['order', 'ASC']],
      });
      if (!nextStep) {
        // Try in next section, if nothing left, formation finished !
        // Child section
        const formationStepsDone = await models.FormationStep.findAll({
          where: {
            id: alreadyDoneStepsIds,
          },
        });
        const sectionIds = formationStepsDone?.map(s => s.sectionId);
        const sectionsDone = await models.FormationSection.findAll({
          where: {
            id: sectionIds,
          },
        });
        const sectionIdsDone = sectionsDone.map(s => s.id);
        // TODO sections nested
        const nextSection = await models.FormationSection.findOne({
          where: {
            id: {
              [Op.notIn]: sectionIdsDone,
            },
            formationId,
          },
          order: [['order', 'ASC']],
        });

        if (nextSection) {
          nextStep = await models.FormationStep.findOne({
            where: {
              sectionId: nextSection?.id,
              id: {
                [Op.notIn]: alreadyDoneStepsIds,
              },
            },
            order: [['order', 'ASC']],
          });
        } else {
          // A terminé la formation
          session.isFinished = true;
          await session.save();
          return false;
          //throw new GraphQLError('Cant find nextSection')
        }
      }

      session.currentStepId = nextStep?.id;
      await session.save();
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },


  async moveFormationElement({ id, id2, direction }, me) {
    try {
      const formationElement = await models.FormationElement.findByPk(id);
      if (['up', 'down'].includes(direction) && !id2) {
        throw new GraphQLError('next or previous element not found for moving up or down');
      }
      if (!formationElement) {
        throw new GraphQLError('element not found');
      }
      if (direction === 'up') {
        let previousElement;
        previousElement = await models.FormationElement.findOne({
          where: {
            id: id2,
          },
        });
        if (previousElement) {
          // Change order
          const currentOrder = formationElement?.order;
          formationElement.order = previousElement.order;
          previousElement.order = currentOrder;

          // Cas (très spécial) où les deux ordres sont identiques, ça ne déplacera pas l'élément
          // On force donc le changement d'ordre
          if (formationElement.order === previousElement.order) {
            formationElement.order = formationElement.order - 1;
          }

          await formationElement?.save();
          await previousElement?.save();
        } else {
          console.error('previousElement not found');
        }

      } else if (direction === 'down') {
        const nextElement = await models.FormationElement.findOne({
          where: {
            id: id2,
          },
        });
        if (nextElement) {
          // Change order
          const currentOrder = formationElement?.order;
          formationElement.order = nextElement.order;
          nextElement.order = currentOrder;

          // Cas (très spécial) où les deux ordres sont identiques, ça ne déplacera pas l'élément
          // On force donc le changement d'ordre
          if (formationElement.order === nextElement.order) {
            formationElement.order = formationElement.order + 1;
          }

          await formationElement?.save();
          await nextElement?.save();
        } else {
          console.error('next element not found');
        }
      } else if (direction === 'left') {
        // Change position
        let settings = formationElement.settings;
        settings.position = 'left';
        formationElement.settings = settings;
        await formationElement?.save();
      } else if (direction === 'right') {
        // Change position
        let settings = formationElement.settings;
        settings.position = 'right';
        formationElement.settings = settings;
        await formationElement?.save();
      } else {
        throw new GraphQLError('Invalid direction');
      }
      return true;
    } catch (e) {
      console.error(e);
    }
  },

  /**
   * Returns all FormationElements having a parent global folder id
   *
   * @param data
   * @param ctx
   * @returns {Promise<awaited Node[] | Promise<Model[]>>}
   */
  async getElementsForUserProperties(data, ctx) {
    try {
      const elements = await models.FormationElement.findAll({
        where: {
          userPropertyFolderId: {
            [Op.not]: null
          },
        },
        order: [['order', 'ASC']],
      });
      return elements;
    } catch (e) {
      throw new GraphQLError('Erreur récupération éléments formulaire')
    }
  },

  async getForfaitIdsForElement(id) {
    try {
      const forfaitsElements = await models.FormationElementForfaits.findAll({
        where: { elementId: id },
        raw: true
      });
      return forfaitsElements.map(f => f.forfaitId);
    } catch (e) {
      console.error(e);
    }
  },

  createOrDeleteWatermarkPictureElementPdf:async({file, action, idArray  }, {me,ip,models,req})=> {
    // Mutation qui pour chaque id de idArray, créé / supprime une image dans le back puis renseigne son path dans formation_element.settings.picture_picture et formation_element.watermarkPicturePath pour la ou les ID de cours
    // Prend :
    //  -> file : (Upload) => le fichier de type Upload qu'envoie le front
    //  -> action : (string) => 'create' ou 'delete'.
    //  -> idArray : [idFormationElement, idFormationElement] => un tableau d'ID de formationElement. Va faire les opérations en parallèle

    try {
      if (idArray != null && idArray.length === 0) {throw new Error("Pour les opération en masse, il faut au moins un elementId")}
      if (!(action==="create" || action==="delete")){throw new Error("Argument needs to be 'create' or 'delete'")}


      // Détermination de la string image. Si create alors on upload l'image et récupère le nom unique associé. Si delete, alors on initialise à une empty string
      const fileString = action==="create" ? await UploadService.uploadFile(file) : ""

      // Récupération des éléments avec l'ID array
      const elementList = await models.FormationElement.findAll({ where: { id: { [Op.in]: idArray } } }) // Récupération des éléments à modifier => les ID présent dans array

      // Identification des pictures à delete : Si create alors on a une liste vide. Si delete, on récupère le set des images associés aux éléments input
      let allPicturesToDelet = action==="create" ? [] : [... new Set(elementList.map((element) => element.watermarkPicturePath))]

      let logArray = [] // placeholder qui accueuillera les promesses de log
      for (let element of elementList) {
        let logPicturePath = action==="create" ? fileString : element.settings.picture_picture // récupération de la path de picture en fonction de l'action
        logArray.push(LogService.logAction({// On push la promesse création de log dans l'array
          logOperation: LOG_OPERATIONS.Element.UpdateWatermarkPicture.action,
          logData: { "image": logPicturePath, "action": action },// ON renseigne si delete ou create
          foreignIds: { fileId: element.id, userId: me.id, },
          ip,
          req,
          models,
          userId: me.id,
        }))

        // Modif de l'élément
        const newSettings = { ...element.settings, picture_picture: fileString } // création d'un nouvel objet settings /!\ Important, l'assignation directe marche pas
        element.settings = newSettings
        element.watermarkPicturePath=fileString
      }

      await Promise.all(logArray) // On publie les logs
      await Promise.all(elementList.map(element => element.save())); // on save les changements sur l'élément'

      // Si delete, on doit supprimer les pictures du back. Rappel : si create on a allPicturesToDelet === [].
      // Pour chaque image à delete, on regarde si l'image est encore associé à un élément (en dehors de ceux que l'on doit delete).
      for (let deletedPicture of allPicturesToDelet){
        // Check si tjr associé à au moins 1 élément.
        const hit = await models.FormationElement.findOne({
          attributes:['id'],
          where:{watermarkPicturePath:deletedPicture}
        })
        if (hit===null){try { await UploadService.deleteFile(deletedPicture)}catch(e){console.error("picture non trouvée :",deletedPicture)}} // delet de la file au path
      }
      return fileString // retourne la string uploaded

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message)
    }
  },


  async elementsForGlobalAnnounce({globalAnnounceType}, ctx) {
    try {
      const userId = ctx?.me?.id;
      const elements = await models.FormationElement.findAll({
        where: {
          globalAnnounceType: globalAnnounceType,
        },
        order: [['order', 'ASC']],
      })

      return PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, userId);
    } catch (e) {

    }
  },

  async elementsBeforeCompletionDiapoSynthese(id, ctx) {
    try {
      const userId = ctx?.me?.id;
      const elements = await ctx.models.FormationElement.findAll({
        where: {
          diapoSyntheseElementBeforeId: id,
        },
        order: [['order', 'ASC']],
      });
      return PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, userId);
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message)
    }
  },
  async elementsAfterCompletionDiapoSynthese(id, ctx) {
    try {
      const userId = ctx?.me?.id;
      const elements = await ctx.models.FormationElement.findAll({
        where: {
          diapoSyntheseElementAfterId: id,
        },
        order: [['order', 'ASC']],
      });
      return PermissionService.FormationElement.getAllowedFormationElementsForUser(elements, userId);
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message)
    }
  },

  async vidstackManageChapterImages({args,ctx}){
    // Fonction qui manage les images de chapitre. Prend la track de l'élément avant update, et la track de l'élément durant l'update.
    // Regarde la différence, et créé/delete les images qui sont différentes
    // Sert pour la création, l'update et la deletion
    try {
      ///// Récupération des arguments
      const models=ctx?.models
      const previousTrack=args?.previousTrackImages
      const newTrack=args?.newTrackImages

      ////// on upload chaque images présentes dans file du nouveau track, et on met le résultat directement dans src
      const uploadPromises=newTrack
        .filter(node=>node.file) // Pour chaque élément de la track qui a un 'file' à upload
        .map(async node=>{

          const uploadWrapper = node.file.file;
          await uploadWrapper.promise;

          await UploadService.uploadFileDataToFolder(
            uploadWrapper,
            UPLOAD_FOLDER_MAP.files
          )
            .then(url=>{ // Lorsque l'upload est fini, alors on écrase l'argument src et file
              node.src=url,
              node.file=null
            })
            .catch(err=>{ // Si error, alors on le notifie
              console.error(`Échec upload pour ${node.id}`)
            })
        });

      await Promise.all(uploadPromises) // Await de tout ça

      /////// Gestion des files à delete et tout
      // Get des deux ensemble de src, après upload de la nouvelle track donc
      const previousSrc=new Set(previousTrack.map(o=>o?.src).filter(src => typeof src === 'string' && src.trim() !== ''))
      const newSrc=new Set(newTrack.map(o=>o?.src).filter(src => typeof src === 'string' && src.trim() !== ''))

      // Get des src présentes dans prev mais absente de new => ce sont les images à delete
      const toDeleteSrc=[...previousSrc].filter(x=> !newSrc.has(x))
      const deletePromises=toDeleteSrc.map(src=>UploadService.deleteFile(src))
      await Promise.all(deletePromises)

      /////// Return des données
      return newTrack


    }catch(e){
      console.error(`vidstackUploadChapterImages. e:${e}`)
      throw new GraphQLError(e)
    }
  },

  async DiapoFormationElementManageFile({args,ctx}){
    try {
      // Récup arguments
      const models=ctx?.models
      const previousTrack=args?.previousTrackDiapo
      const newTrack=args?.newTrackDiapo

      // On upload chaque image présent dans file du nouveau track, et on met le résultat directement dans src
      const uploadPromises=newTrack
        .filter(node=>node.file)
        .map( async node =>{
          const uploadWrapper=node.file
          await uploadWrapper.promise

          await UploadService.uploadFileDataToFolder(
            uploadWrapper,
            UPLOAD_FOLDER_MAP.files
          )
            .then(url=>{
              console.log(`upload réussi pour : ${node.file.name}`)
              node.src=url,
              node.file=null
            })
            .catch(err=>{
              console.error(`echec upload pour ${node.id}`)
            })
        })

      await Promise.all(uploadPromises)


      // Gestion des files à delete
      const previousSrc=new Set(previousTrack.map(o=>o?.src).filter(src => typeof src === 'string' && src.trim() !== ''))
      const newSrc=new Set(newTrack.map(o=>o?.src).filter(src => typeof src === 'string' && src.trim() !== ''))

      // Get des src présentes dans prev mais absente de new => ce sont les images à delete
      const toDeleteSrc=[...previousSrc].filter(x=> !newSrc.has(x))
      const deletePromises=toDeleteSrc.map(src=>UploadService.deleteFile(src))
      await Promise.all(deletePromises)

      // return des données
      return newTrack

    }catch(e){
      console.error(`DiapoFormationElementManageFile. e:${e}`)
      throw new GraphQLError(e)
    }
  },

  FormationElementCacheManager:{
    /*
    * L'objectif de cette section, est de créer un cache qui associe une string (il faut une condition d'unicité) à un ID de formation élément.
    * Ce cache a été créé afin de répondre au soucis de linkage file <-> formationÉlément.
    * La fonction findFormationElementId(filename) permet de retrouver LE formationElementId du filename.
    *
    *
    * Le cache est régulièrement recréé (opération peu couteuse) par cronJob, et est mis à jour momentanément à chaque update de formationElement.
    * Si il y a un miss, alors le cache est recréé
     */

    checkCacheLockBeforeTimeout:(maxAttempts=20,delayMs=500)=>{
      try {
        return new Promise((resolve,reject)=>{
          let attempts=0;

          const check=()=>{
            if (isCacheLocked === false ){resolve(true)}
            else {
              attempts++;
              if (attempts>=maxAttempts){reject(false)}
              else {setTimeout(check,delayMs)}
            }
          };
          check()
        })
      }catch(e){
        console.error(`error checkCacheLockBeforeTimeout FE cache : ${e}`)
      }
    },

    sha1:(str) => {
      try {
        return createHash('sha1').update(str).digest('hex');
      }catch(e){
        console.error(`error sha1 FE cache : ${e}`)
      }
    },

    createCache:async ()=> {
      const initCacheStatus=isCacheLocked
      try {
        // Check du lock, et wait si besoin
        if (isCacheLocked===false){
          isCacheLocked=true
        } else {
          // Si le lock est déjà use, c'est qu'il est en train d'être construit => On wait, et dès qu'on a true, on return OK
          await FormationService.FormationElementCacheManager.checkCacheLockBeforeTimeout()
          return true
        }

        ///// Ici, on est dans l'instance qui doit créer le cache
        // Creation des couple (key *text* , value *id*)
        const rows=await models.FormationElement.findAll({
          attributes:['id','text'],
          raw:true,
          where: {
            text: {
              [Op.ne]: null
            },
            type:ELEMENTS_TYPE.FILE
          },
        })

        const multi = redisClient.multi();          // ← remplace pipeline()

        for (const { id, text } of rows) {
          multi.hSet(MAP_KEY, FormationService.FormationElementCacheManager.sha1(text), id);      // hSet en camelCase
        }
        await multi.exec();

        // Release du lock => uniquement si tout s'est bien passé
        isCacheLocked=false

        return true
      } catch(e){
        const message=`dans formationElement Cache Manager => createCache, error : ${e}`
        console.error(message)

        // Si en entrant dans la fonction, on était en lock === false, c'est que c'est cette fonction qui l'a modifiée, et on remet à false ici
        if (initCacheStatus===false){isCacheLocked=false}
      }
    },

    findFormationElementId:async (text)=>{
      // à partir d'une string unique, retourne l'id du formation élément associé
      try {

        // Check de si le cache existe
        const cacheExists=await redisClient.exists(MAP_KEY)

        // Si non, alors on le créé (+ c'est géré dans createCache que l'on attends qu'il soit créé si plusieurs appels en simultané)
        if (!cacheExists){
          await FormationService.FormationElementCacheManager.createCache();
        }


        return redisClient.hGet(MAP_KEY,FormationService.FormationElementCacheManager.sha1(text))
      }catch(e){
        console.error(`error findFormationElementId : ${e}`)
      }
    },

    updateCacheSingleFile:async (id,text)=>{
      // Update le cache pour une unique value
      try {
        await redisClient.hSet(MAP_KEY,FormationService.FormationElementCacheManager.sha1(text),id)
      }catch(e){
        console.error(`error updateCacheSingleFile : ${e}`)
      }
    },

    deleteCache:async()=>{
      try {
        const multi = redisClient.multi();

        multi.del(MAP_KEY);

        await multi.exec();
      }catch(e){
        console.error(`error delete cache : ${e}`)
      }
    },

    rebuildCache:async ()=>{
      // Supprime puis reconstruit le cache
      try {
        // Dlete le cache
        await FormationService.FormationElementCacheManager.deleteCache();

        // Reconstruis ensuite le cache.
        await FormationService.FormationElementCacheManager.createCache();

        return true
      }catch(e){
        console.error(`error rebuild cache : ${e}`)
      }
    }
  }
};
