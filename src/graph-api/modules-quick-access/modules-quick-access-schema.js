import gql from 'graphql-tag'

export default gql`
    extend type Query {
        modulesQuickAccess(filter: ModuleQuickAccessFilter): [ModuleQuickAccess]
    }

    extend type Mutation {
        #Quick access
        createModuleQuickAccess(input: ModuleQuickAccessInput!): ModuleQuickAccess
        updateModuleQuickAccess(id: ID!, input: ModuleQuickAccessInput!): Boolean
        deleteModuleQuickAccess(id: ID!): Boolean

        # Types for quick access (can be challenge, training, etc)
        addTypeToModuleQuickAccess(typeQcmId: ID!, moduleQuickAccessId: ID!): Boolean
        removeTypeFromModuleQuickAccess(typeQcmId: ID!, moduleQuickAccessId: ID!): Boolean
    }
    
    input ModuleQuickAccessFilter {
        challengeId: ID
        ueId: ID
        ueCategoryId: ID
        coursId: ID
    }
    input ModuleQuickAccessInput {
        name: String
        description: String
        type: String
        challengeId: ID
        ueId: ID
        ueCategoryId: ID
        coursId: ID
        settings: JSON
    }
    type ModuleQuickAccess {
        id: ID!
        name: String
        description: String
        type: String
        settings: JSON
        challenges: [Challenge]
        challengeId: ID
        ueId: ID
        ueCategoryId: ID
        types: [TypeQcm]
        coursId: ID
        createdAt: Date
        updatedAt: Date
    }

`
