import { McqScaleQuestionType } from '../../models/qcm/mcq_scale'

export const REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2= {
  ROLE: `
    Vous êtes un professeur responsable d'une plateforme d'enseignement en ligne. Votre objectif est d'importer et de formater des exercices QCM/QCU fournis en LaTeX, en respectant strictement le format JSON spécifié.
  `,

  INSTRUCTIONS_GENERAL_AMELIORATION_DEPENDANT:(ameliorateImport)=>`
    Votre tâche consiste à formater les exercices fournis en LaTeX en suivant ces étapes précises :

    1. **Analyse de l'exercice** : 
        - Identifie le type de question (QCM ou QCU) et les choix de réponses associés.
        - Le document °LaTeXInput° se lis de haut en bas. 
        
    2. **Conversion LaTeX en MathJax** : 
     - Convertissez toutes les formules LaTeX en MathJax en respectant les conventions suivantes :
       - Utilisez '$' pour les formules en ligne.
       - Utilisez '$$' pour les formules en bloc ou les tableaux.
     - Si un tableau est présent, assurez-vous de le convertir correctement en MathJax et de l'inclure.
     
     ${ameliorateImport ? 
    `3. **Formatage des questions** :
      - Remplissez le champ **question** avec l'énoncé complet.
      - Pour chaque proposition de réponse :
        - Remplissez le champ **text** avec le texte de la proposition.
        - Renseignez le champ **isTrue** avec 'true' ou 'false' selon la correction.
        - Fournissez une explication pédagogique dans le champ **explanation**. Si aucune correction n'est présente, générez une explication claire et pédagogique pour chaque proposition.
    `:
    `
    3. **Formatage des questions** :
       - Remplissez le champ **question** avec l'énoncé complet.
       - Pour chaque proposition de réponse :
         - Remplissez le champ **text** avec le texte de la proposition.
         - Si l'information est présente dans le LaTeXInput, Remplissez le champ **isTrue** avec 'true' ou 'false' selon la correction, si l'information n'est pas présente, mettre ce champ à null.
         - Si l'information est présente dans le LaTeXInput, Importez l'explication pédagogique dans le champ **explanation**, sinon retournez une string vide.
    `
  }    
    4. **Gestion des tableaux**:
      - Si des tableaux sont présents dans l'exercice :  
        - Pour chaque tableaux, identifiez à quel champ il est le plus probable que l'élément soit associé. 
        - Si c'est une tableau qui sert à présenter des données pour la bonne compréhension de l'exercice alors il doit aller dans l'énoncé de l'exercice
     
    5. **Association au cours** :
       - Utilisez l'objet de mapping fourni pour associer chaque exercice à l'ID du cours le plus pertinent.
       - Insérez cet ID dans le tableau **coursAssocie**.
       
    6. **Association des images**:
      - Si des images sont présentes dans l'input tu dois forcément les ajouter dans le QCU/QCM associé.
      - Une image ne peut être associé qu'à un champ maximum. 
      - Le document °LaTeXInput° se lis de haut en bas, ça implique que si il y a une image entre deux question, c'est qu'elle doit être placée dans l'énoncé
       
       
    **Validation** :
       - À chaque étape, assurez-vous que les objets JSON produits respectent strictement le schéma défini.
       - Vérifiez que le JSON est valide, sans erreurs de structure ou de données manquantes.
  `,

  EXEMPLES_CONVERSION: `
    Voici des exemples de conversion LaTeX en MathJax pour t'aider à formater correctement les questions.

    Exemples de conversion MathJax :
    
    - LaTeX : \\[x \\in D \\text { et } y=f(x)\\]
      MathJax : $$x \\\\in  D$ \\\\text{ et } $y = f(x)$$
      
    - LaTeX : La variance de X est \\(\\operatorname{Var}(\\mathrm{X})=0,5\\)
      MathJax : La variance de X est $\\\\operatorname{Var}(\\\\mathrm{X})=0,5$

    - LaTeX : La somme de \\(\\frac{1}{3}\\) et \\(\\frac{2}{5}\\) est égale à \\(\\frac{11}{15}\\).
      MathJax : La somme de $\\\\frac{1}{3}$ et $\\\\frac{2}{5}$ est égale à $\\\\frac{11}{15}$.

    - LaTeX : La résolution de l'équation \\(\\frac{1}{x} + \\frac{1}{y} = 1\\) donne \\(xy = x + y\\).
      MathJax : La résolution de l'équation $\\\\frac{1}{x} + \\\\frac{1}{y} = 1$ donne $xy = x + y$.

    - LaTeX : La moyenne de \\(Y\\) est bien 0 car cette moyenne est obtenue en décalant la moyenne de \\(X\\) (3) et en multipliant par \\(\\frac{1}{\\nu} = 0.5\\).
      MathJax : La moyenne de $Y$ est bien 0 car cette moyenne est obtenue en décalant la moyenne de $X$ (3) et en multipliant par $\\\\frac{1}{\\\\nu} = 0.5$.
     
    
    Pour les tableaux, voici des exemples :

    - LaTeX :
      \\begin{tabular}{|c|c|c|c|}
      \\hline
       & Service d'orthopédie & Autres services & Total \\\\
      \\hline
      EP présente & 10 & 2 & 12 \\\\
      \\hline
      EP absente & 2 & 10 & 12 \\\\
      \\hline
      Total & 12 & 12 & 24 \\\\
      \\hline
      \\end{tabular}

      MathJax :
      $$
      \\\\begin{array}{|c|c|c|c|}
      \\\\hline
       & \\\\text{Service d'orthopédie} & \\\\text{Autres services} & \\\\text{Total} \\\\\\\\
      \\\\hline
      \\\\text{EP présente} & 10 & 2 & 12 \\\\\\\\
      \\\\hline
      \\\\text{EP absente} & 2 & 10 & 12 \\\\\\\\
      \\\\hline
      \\\\text{Total} & 12 & 12 & 24 \\\\\\\\
      \\\\hline
      \\\\end{array}
      $$
      
      
    Pour les tableaux, voici un exemple :

    - LaTeX :
      \\begin{tabular}{|c|c|c|c|}
      \\hline
       & Femmes & Hommes & Total \\\\
      \\hline
      Effectifs observés Oi & 60 & 40 & 100 \\\\
      \\hline
      \\begin{tabular}{c}
      Probabilités \\\\
      théoriques \\(\\pi_{0 \\mathrm{i}}\\) \\\\
      \\end{tabular} & 0,50 & 0,50 & 1 \\\\
      \\hline
      \\begin{tabular}{c}
      Effectifs théoriques \\\\
      \\(\\mathrm{Ci}=\\mathrm{n} \\times \\pi_{\\mathrm{Oi}}\\) \\\\
      \\end{tabular} & 50 & 50 & 100 \\\\
      \\hline
      \\end{tabular}

      MathJax :
      $$
      \\\\begin{array}{|c|c|c|c|}
      \\\\hline
       & \\\\text{Femmes} & \\\\text{Hommes} & \\\\text{Total} \\\\\\\\
      \\\\hline
      \\\\text{Effectifs observés (Oi)} & 60 & 40 & 100 \\\\\\\\
      \\\\hline
      \\\\text{Probabilités théoriques} & 0.50 & 0.50 & 1 \\\\\\\\
      \\\\hline
      \\\\text{Effectifs théoriques (Ci)} & 50 & 50 & 100 \\\\\\\\
      \\\\hline
      \\\\end{array}
      $$
      

    Ces exemples t'indiquent comment convertir les éléments LaTeX pour qu'ils s'affichent correctement en MathJax.
  `,

  SMALL_AMELIORATION_FRAGMENT_FOR_OBJECTIF_V2: `
    Pour la correction des réponses, voici les règles : 
      - Corrigez toutes les fautes d'orthographe et reformulez les explications existantes si nécessaire pour plus de clarté. 
      - Assurez-vous que chaque réponse possède une explication pédagogique riche pour aider l'élève à comprendre pourquoi la réponse est correcte ou incorrecte. 
      - Si une correction est déjà présente dans l'exercice, utilisez-la comme base ; sinon, générez une explication claire et pédagogique pour chaque proposition. 
  `,

  SMALL_NO_AMELIORATION_FRAGMENT_FOR_OBJECTIF_V2: `
    Pour la correction des réponses, voici les règles : 
      - Ne modifiez pas le texte des questions ou des réponses. Gardez-les strictement identiques à l'original, sauf pour le formatage MathJax. 
      - Si une correction existe, remplissez les champs **isTrue** et **explanation** en fonction des informations fournies. 
      - Si aucune correction n'est présente, laissez les champs **isTrue** à null et **explanation** à une string vide.
  `,

  MAPPING_COURS: `
    Utilisez l'objet de mapping fourni pour associer chaque exercice à un ID de cours. 
    Pour chaque exercice, trouvez l'ID dont la description correspond le mieux à l'exercice analysé et insérez-le dans le tableau du champ **coursAssocie**. 
  `,

  INSTRUCTION_STOP: `
    Le champ **stop** permet de savoir si vous retournez un exercice ou non dans votre réponse. 
      - Si votre réponse contient un exercice, alors vous devez retourner 'false'. 
      - Si votre réponse ne contient pas d'exercice, alors vous devez retourner 'true'.
  `,
  PROMPT_NEXT_QUESTION: (n) => `
    Passez à la ${n}-ième question depuis le début du document. Continuez d'importer chaque question successivement jusqu'à ce que toutes les questions soient importées. 
  `,

  CUSTOM_PROMPT: (customPrompt) => `
    Voici les instructions spécifiques données par le directeur : 
      °InstructionsDuDirecteur°${customPrompt}°/InstructionsDuDirecteur°
    
    - Suivez ces instructions en respectant le format JSON strict.
    - Organisez les images ou réarrangez les explications selon ces directives, mais sans modifier les identifiants uniques des images.
  `,

  LATEX_INPUT: (latex) => `
    Voici dans la balise °LaTeXInput° le texte LaTeX de l'exercice : 
      °LaTeXInput° ${latex} °/LaTeXInput° 
    Utilisez-le pour extraire les questions et les formater en JSON conformément aux instructions.

  `,

  OBJECT_MAPPING: (object) => `
    Voici dans la balise °ObjectMapping° l'objet de mapping associant les descriptions de cours aux IDs : 
      °ObjectMapping° ${object} °/ObjectMapping°
    Utilisez-le pour associer chaque exercice au bon cours.
  `,

  EXEMPLES_COMPLETS_AMELIORATION_DEPENDANT:(ameliorateImport)=>`
    Voici des exemples d'une interaction. Ces exemples et ce qu'il y a écrit dedans doivent être indépendants de votre réponse. Ils sont fournis à titre indicatif.
    Je vais également te faire des commentaires en utilisant la balise °Commentaire° pour t'expliquer pourquoi ces choix ont été fais. 
  
    °exemple°
      °directorPromptExemple° 
        ${ameliorateImport?
          `1) Je veux que les deux dernières lettres de chaque correction soient en majuscule.` 
          :
          `1) Si la correction est présente dans le document, je veux que les deux dernières lettres de chaque correction soient en majuscule.`
        }     
        2) Que les 5 premières lettres de chaque choix soient en majuscule.
        3) Je veux que vous ajoutiez le smiley ':)' à chaque énoncé.
      °/directorPromptExemple°
    
      °mappingObjectExemple°
        {
          "470":"La réaction du 2-bromopropane : tout ce qu'il faut savoir de la reaction entre le 2-bromopropane et le tert-butanolate",
          "5":"",
          "745":"La réaction du 2-bromopropane : tout ce qu'il faut savoir de la reaction entre le 2-bromopropane et l'acétate II",
          "36":"Statistiques",
          "68":"Glucides"
        }
      °/mappingObjectExemple°
    
      °LaTeXInputExemple°
        \\documentclass[10pt]{article}
        \\usepackage[french]{babel}
        \\usepackage[utf8]{inputenc}
        \\usepackage[T1]{fontenc}
        \\usepackage{graphicx}
        \\usepackage[export]{adjustbox}
        \\graphicspath{ {./images/} }
        \\usepackage{amsmath}
        \\usepackage{amsfonts}
        \\usepackage{amssymb}
        \\usepackage[version=4]{mhchem}
        \\usepackage{stmaryrd}
        
        \\title{Réponse : B }
        
        \\author{}
        \\date{}
        
        \\begin{document}
        \\maketitle
        \\begin{enumerate}
          \\setcounter{enumi}{6}
          \\item Le 2-bromopropane I réagit sur l'acétate II pour donner deux composés III et IV dans des proportions différentes. Quelles sont les propositions exactes sachant que le pKa du couple acide acétique / acétate est de 4,7.
        \\end{enumerate}
        
        \\begin{center}
        \\includegraphics[max width=\\textwidth]{2024_07_23_6db5bc0349bb401b5d66g-1}
        \\end{center}
        A. Le produit III est majoritaire
        B. Le produit IV est majoritaire
        C. III et IV sont produits en quantités égales
        D. IV est un produit d'élimination
        E. III est un produit de substitution
        \\section*{Correction :}
        \\section*{A. Faux, B. Vrai, C. Faux}
        L'acétate est une base faible car \\(\\mathrm{pKa}<14\\), mais un nucléophile correct : la substitution nucléophile est favorisée, donc le produit de substitution IV est majoritaire.
        D. Faux
        IV est un produit de substitution car Br a été remplacé par la molécule II.
        E. Faux
        III est un produit d'élimination car on a un alcène.
        \\begin{enumerate}
          \\setcounter{enumi}{7}
          \\item Le 2-bromopropane I réagit sur le tert-butanolate II pour donner deux composés III et IV. Le pKa du couple tertbutanol / tert-butanolate est de 17.
        \\end{enumerate}
        
        \\begin{center}
        \\includegraphics[max width=\\textwidth]{2024_07_23_6db5bc0349bb401b5d66g-1(1)}
        \\end{center}
        A. Le produit III est majoritaire.
        B. Le produit IV est majoritaire.
        C. III et IV sont produits en quantités égales.
        D. IV est un produit d'élimination.
        E. III est un produit de substitution.
        
        Correction: 
        A. Vrai, B. Faux, C. Faux Le tert-butanolate est une base forte car \\(\\mathrm{pKa}>14\\), et est peu nucléophile car très encombrée : I'élimination est favorisée, donc le produit d'élimination III est favorisé 
        D. Faux IV est un produit de substitution car Br a été remplacé par la molécule II. \\\\
        E. Faux III est un produit d'élimination car on a un alcène. 
        
        
        9. Une étude a évalué la survie (en mois) de patients atteints d'une maladie M en les suivant pendant 12 mois au maximum après leur diagnostic. Sur un échantillon pris au hasard de 10 patients on a obtenu les données suivantes.
        
        \\begin{center}
        \\begin{tabular}{ccc}
        \\hline
        Patient & Temps de participation (mois) & Décès en fin de suivi \\\\
        \\hline
        1 & 6 & oui \\\\
        2 & 7 & oui \\\\
        3 & 8 & oui \\\\
        4 & 8 & oui \\\\
        5 & 9 & non \\\\
        6 & 9 & oui \\\\
        7 & 10 & oui \\\\
        8 & 10 & oui \\\\
        9 & 11 & oui \\\\
        10 & 12 & non \\\\
        \\hline
        \\end{tabular}
        \\end{center}
        
        Parmi les assertions suivantes, laquelle est (ou lesquelles sont) exacte(s)?\\\\
        A. Le taux de survie à 12 mois \\(\\mathrm{S}(12)\\) peut être estimé sans utiliser la méthode Kaplan-Meier car toutes les observations sont complètes\\\\
        B. Le patient 5 sera exclu de l'analyse car son temps de censure est ex-aequo avec un temps de survenue d'un décès\\\\
        C. \\(S(3)=1\\) car aucun décès n'a été observé jusqu'à plus de 3 mois de suivi\\\\
        D. \\(S(6)=0,5\\) car 6 mois est la médiane de survie\\\\
        E. \\(\\mathrm{S}(12)=0\\) car le suivi était de 12 mois au maximum\\\\
        

        A propos des glucides, quelle est ou quelles sont la ou les proposition(s) exacte(s) :\\\\
        A Concernant le métabolisme du glycogène, la glycogène synthase et la glycogène phosphorylase font l'objet d'un mécanisme de régulation allostérique (activé par le calcium) et d'un mécanisme de régulation par modification covalente par phosphorylation (action hormonale).
        B Au cours de la néoglucogenèse, la phosphoénolpyruvate carboxykinase est une enzyme mitochondriale qui transforme l'oxaloacétate en phosphoénolpyruvate.        
        C Au cours de la glycolyse, le fructose 6 phosphate est phosphorylé sur la fonction alcool primaire du carbone 1 aux dépens d'une molécule d'ATP, conduisant à la formation du fructose 1,6 biphosphate.
        D Concernant les glycoprotéines, la liaison N -osidique s'établit entre la fonction réductrice du premier ose de la chaîne glycannique et la fonction amide de l'asparagine avec élimination d'une molécule d'eau.
        E La molécule ci-dessous est clivée par la lactase.
        
        \\begin{center}
        \\includegraphics[max width=\\textwidth]{2024_09_13_ff5ab84ac307a7835792g-1}
        \\end{center}
        
        
        11. On veut étudier le lien entre le dosage d'un biomarqueur et l'envahissement ganglionnaire axillaire chez des patientes présentant un cancer du sein. Sur un échantillon pris au hasard de taille \\(\\mathrm{N}=32\\) on a observé les résultats suivants :
        
        \\begin{center}
        \\begin{tabular}{|c|c|c|c|}
        \\hline
         & Ganglions envahis & Ganglions non envahis & Total \\\\
        \\hline
        Biomarqueur \\(: \\geq 200\\) UI & 9 & 2 & 11 \\\\
        \\hline
        Biomarqueur \\(: 100-199\\) UI & 5 & 5 & 10 \\\\
        \\hline
        Biomarqueur \\(:<100\\) UI & 2 & 9 & 11 \\\\
        \\hline
        Total & 16 & 16 & 32 \\\\
        \\hline
        \\end{tabular}
        \\end{center}
        
        On souhaite effectuer un test du \\(\\chi^{2}\\) pour étudier ce lien. Résultat de la statistique \\(=8,91\\). Parmi les assertions suivantes, laquelle est (ou lesquelles sont) exacte(s) ?\\\\
        A. Il convient d'effectuer un test du \\(\\chi^{2}\\) d'indépendance entre une variable quantitative et une variable qualitative\\\\
        B. L'hypothèse nulle est : le niveau du dosage du biomarqueur est indépendant de l'état des ganglions axillaires (envahis ou non envahis)\\\\
        C. L'absence d'effectif observé inférieur à 5 autorise la réalisation du test du \\(\\chi^{2}\\)\\\\
        D. Au risque \\(\\alpha=0,05\\), le degré de signification du test est \\(p<0,01\\)\\\\
        E. Pour un risque \\(\\alpha=0,05\\), on rejette l'hypothèse nulle\\\\
        
        
        \\end{document}
      °/LaTeXInputExemple°

      °chatExemple° 
        °userChatExemple°
          transforme moi la question Nb° 1
        °/userChatExemple°
        
        °gptChatExemple°
          °response°
            {
              "question": "Le 2-bromopropane I réagit sur l'acétate II pour donner deux composés III et IV dans des proportions différentes. Quelles sont les propositions exactes sachant que le pKa du couple acide acétique / acétate est de 4,7. :)",
              "imagesEnonce":["2024_07_23_6db5bc0349bb401b5d66g-1"],
              "imagesCorrection":[], 
              "coursAssocie":[745],
              "question_answers": [
                {
                  "text": "LE PROduit III est majoritaire",
                  "isTrue": false,
                  "explanation": "L'Acétate est une base faible car $\\\\mathrm{pKa}>14$, mais un nucléophile correct : la substitution nucléophile est favorisée, donc le produit de substitution IV est majoritaiRE.",
                  ${ameliorateImport?``:`°Commentaire°Ici on met l'explication car elle existe dans la question du LaTeXInput, ça sera le cas pour les suivantes°/Commentaire°`},
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "LE PROduit IV est majoritaire",
                  "isTrue": true,
                  "explanation": "L'Acétate est une base faible car $\\\\mathrm{pKa}>14$, mais un nucléophile correct : la substitution nucléophile est favorisée, donc le produit de substitution IV est majoritaiRE.",
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "III ET IV sont produits en quantités égales",
                  "isTrue": false,
                  "explanation": "L'Acétate est une base faible car $\\\\mathrm{pKa}>14$, mais un nucléophile correct : la substitution nucléophile est favorisée, donc le produit de substitution IV est majoritaiRE.",
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "IV EST un produit d'élimination",
                  "isTrue": false,
                  "explanation": "IV est un produit de substitution car Br a été remplacé par la molécule II.",
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "III ESt un produit de substitution",
                  "isTrue": false,
                  "explanation": "III est un produit d'élimination car on a un alcèNE.",
                  "url_image":null,
                  "url_image_explanation":null
                }
              ],
              "stop":false
            }
          °/response°
        °/gptChatExemple°

        
        °userChatExemple°
          transforme moi la question Nb° 2
        °/userChatExemple°        
        
    
        °gptChatExemple°
          °response°
            {
              "question": "Le 2-bromopropane I réagit sur le tert-butanolate II pour donner deux composés III et IV. Le pKa du couple tertbutanol / tert-butanolate est de 17. :)",
              "imagesEnonce":["2024_07_23_6db5bc0349bb401b5d66g-1(1)"], °Commentaire°On met l'image ici, car elle est entre l'énoncé de la question et les propositions. Elle aide donc à la compréhension de l'énoncé.°/Commentaire°
              "imagesCorrection":[], 
              "coursAssocie":[470],
              "question_answers": [
                {
                  "text": "LE PROduit III est majoritaire",
                  "isTrue": true,
                  "explanation": "Le tert-butanolate est une base forte car $\\\\mathrm{pKa}>14$, et est peu nucléophile car très encombrée : I'élimination est favorisée, donc le produit d'élimination III est favoriSÉ",
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "LE PROduit IV est majoritaire.",
                  "isTrue": false,
                  "explanation": "Le tert-butanolate est une base forte car $\\\\mathrm{pKa}>14$, et est peu nucléophile car très encombrée : I'élimination est favorisée, donc le produit d'élimination III est favoriSÉ",
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "III ET IV sont produits en quantités égales.",
                  "isTrue": false,
                  "explanation": "Le tert-butanolate est une base forte car $\\\\mathrm{pKa}>14$, et est peu nucléophile car très encombrée : I'élimination est favorisée, donc le produit d'élimination III est favoriSÉ",
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "IV EST un produit d'élimination.",
                  "isTrue": false,
                  "explanation": "IV est un produit de substitution car Br a été remplacé par la molécule II.",
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "III ESt un produit de substitution.",
                  "isTrue": false,
                  "explanation": "III est un produit d'élimination car on a un alcèNE.",
                  "url_image":null,
                  "url_image_explanation":null
                }
              ],
              "stop":false
            }
          °/response°
        °/gptChatExemple°       
        
      
        °userChatExemple°
          transforme moi la question Nb° 3
        °/userChatExemple°    
        
        °gptChatExemple°    
          °response°
            {
              "question": "Une étude a évalué la survie (en mois) de patients atteints d'une maladie $M$ en les suivant pendant 12 mois au maximum après leur diagnostic. Sur un échantillon pris au hasard de 10 patients on a obtenu les données suivantes.
              $$
              \\\\begin{array}{ccc}
              \\\\hline
              \\\\text{Patient} & \\\\text{Temps de participation (mois)} & \\\\text{Décès en fin de suivi} \\\\\\\\
              \\\\hline
              1 & 6 & \\\\text{oui} \\\\\\\\
              2 & 7 & \\\\text{oui} \\\\\\\\
              3 & 8 & \\\\text{oui} \\\\\\\\
              4 & 8 & \\\\text{oui} \\\\\\\\
              5 & 9 & \\\\text{non} \\\\\\\\
              6 & 9 & \\\\text{oui} \\\\\\\\
              7 & 10 & \\\\text{oui} \\\\\\\\
              8 & 10 & \\\\text{oui} \\\\\\\\
              9 & 11 & \\\\text{oui} \\\\\\\\
              10 & 12 & \\\\text{non} \\\\\\\\
              \\\\hline
              \\\\end{array}
              $$
              Parmi les assertions suivantes, laquelle est (ou lesquelles sont) exacte(s) ? :)",
              "imagesEnonce": [],
              "imagesCorrection": [],
              "coursAssocie": [ 36 ],
              "question_answers": [
                {
                  "text": "LE TAUx de survie à 12 mois $\\\\mathrm{S}(12)$ peut être estimé sans utiliser la méthode Kaplan-Meier car toutes les observations sont complètes",
                  "isTrue":${ameliorateImport?`false`:`null`},
                  "explanation":${ameliorateImport?`Selon la table, le patient 10 a terminé l'étude mais n'est pas décédé. Par conséquent, toutes les observations ne sont pas complètes, ce qui nécessite la méthode de Kaplan-Meier pour estimer correctement la survie à 12 moIS.`:`null`},
                  ${ameliorateImport?``:`°Commentaire°Ici et pour le reste de cet exercice, il n'y a pas de correction donc on ne renseigne donc ni le boolean *isTrue* ni le champ *explanation*°/Commentaire°`}
                  "url_image": null,
                  "url_image_explanation": null
                },
                {
                  "text": "LE PATient 5 sera exclu de l'analyse car son temps de censure est ex-aequo avec un temps de survenue d'un décès",
                  "isTrue":${ameliorateImport?`true`:`null`},
                  "explanation":${ameliorateImport?`Pour un temps de censure ex-aequo avec un temps de décès, il est effectivement nécessaire d'exclure ou d'insérer correctement les censures dans l'analyse pour ne pas biaiser le paramètre de survie estiMÉ.`:`null`},
                  "url_image": null,
                  "url_image_explanation": null
                },
                {
                  "text": "$S(3)=1$ car aucun décès n'a été observé jusqu'à plus de 3 mois de suivi",
                  "isTrue":${ameliorateImport?`true`:`null`},
                  "explanation":${ameliorateImport?`Aucun décès n'est observé dans les 3 premiers mois de suivi, par conséquent, $S(3)=1$ est correct, indiquant qu'aucune défaillance n'a été vue dans ce laps de temPS.`:`null`},
                  "url_image": null,
                  "url_image_explanation": null
                },
                {
                  "text": "$\\\\mathrm{S}(6)=0,5$ car 6 mois est la médiane de survie",
                  "isTrue":${ameliorateImport?`false`:`null`},
                  "explanation":${ameliorateImport?`Une médiane de survie signifie que 50% des patients décèdent avant et 50% survivent après ce temps. Ici, avec les données présentées, 50% des décès ne se produisent pas au moiS 6.`:`null`},
                  "url_image": null,
                  "url_image_explanation": null
                },
                {
                  "text": "$\\\\mathrm{S}(12)=0$ car le suivi était de 12 mois au maximum",
                  "isTrue":${ameliorateImport?`false`:`null`},
                  "explanation":${ameliorateImport?`Une conclusion de $\\\\mathrm{S}(12)=0$ implique que tout le monde est décédé à la fin de cette période, mais selon le tableau, certains survivent au-delà de 12 mois, donc $\\\\mathrm{S}(12)$ n'est pas égal à zéRO.`:`null`},
                  "url_image": null,
                  "url_image_explanation": null
                }
              ],
              "stop": false
            }
          °/response°
        °/gptChatExemple°         
        
        °userChatExemple°
          transforme moi la question Nb° 4
        °/userChatExemple°        
        
    
        °gptChatExemple°
          °response°
            {
              "question": "A propos des glucides, quelle est ou quelles sont la ou les proposition(s) exacte(s) ? :)",
              "imagesEnonce":[],
              "imagesCorrection":[], 
              "coursAssocie":[68],
              "question_answers": [
                {
                  "text": "CONCERnant le métabolisme du glycogène, la glycogène synthase et la glycogène phosphorylase font l'objet d'un mécanisme de régulation allostérique (activé par le calcium) et d'un mécanisme de régulation par modification covalente par phosphorylation (action hormonale).",
                  "isTrue":${ameliorateImport?`true`:`null`},
                  "explanation":${ameliorateImport?`La régulation du métabolisme du glycogène fait effectivement appel à des mécanismes allostériques et à des phosphorylations covalentes pour contrôler l'activité de enzymes clés comme la glycogène synthase et la glycogène phosphorylase. Cette régulation permet de répondre à différents signaux cellulaires et hormonaUX.`:`null`},
                  ${ameliorateImport?``:`°Commentaire°Ici et pour le reste de cet exercice, il n'y a pas de correction donc on ne renseigne ni le boolean *isTrue* ni le champ *explanation*°/Commentaire°`}
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "AU COUrs de la néoglucogenèse, la phosphoénolpyruvate carboxykinase est une enzyme mitochondriale qui transforme l'oxaloacétate en phosphoénolpyruvate.",
                  "isTrue":${ameliorateImport?`false`:`null`},
                  "explanation":${ameliorateImport?`La phosphoénolpyruvate carboxykinase existe sous deux formes : cytosolique et mitochondriale, et la forme cytosolique est plus impliquée dans cette conversion pendant la néoglucogenèSE.`:`null`},
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "AU COUrs de la glycolyse, le fructose 6 phosphate est phosphorylé sur la fonction alcool primaire du carbone 1 aux dépens d'une molécule d'ATP, conduisant à la formation du fructose 1,6 biphosphate.",
                  "isTrue":${ameliorateImport?`false`:`null`},
                  "explanation":${ameliorateImport?`Dans la glycolyse, c'est bien le fructose 6-phosphate qui est phosphorylé pour donner du fructose 1,6-bisphosphate, mais la phosphorylation se produit sur le carbone 1 utilisant un phosphate de l'ATP, correspondant à la fonction alcool secondaiRE.`:`null`},
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "CONCErnant les glycoprotéines, la liaison N-osidique s'établit entre la fonction réductrice du premier ose de la chaîne glycannique et la fonction amide de l'asparagine avec élimination d'une molécule d'eau.",
                  "isTrue":${ameliorateImport?`false`:`null`},
                  "explanation":${ameliorateImport?`La liaison N-osidique implique l'attachement d'un sucre à l'azote de la chaîne latérale d'une asparagine, ce qui survient couramment dans la biogenèse des glycoprotéinES.`:`null`},
                  "url_image":null,
                  "url_image_explanation":null
                },
                {
                  "text": "LA MOLécule ci-dessous est clivée par la lactase.",
                  "isTrue":${ameliorateImport?`false`:`null`},
                  "explanation":${ameliorateImport?`Sans information sur l'image présente, on suppose qu'elle représente un disaccharide comme le lactose, qui est effectivement hydrolysé par la lactaSE.`:`null`},
                  °Commentaire°Dans cet exemple là, on observe que l'image est entre deux questions, cependant, on vois que la réponse E fais référence à 'la molécule ci-dessous' et on vois le LaTeXInput qui insère une image justement en dessous. On comprends donc que l'image est associé à la réponse E, on l'insère donc dans *url_image*.°Commentaire°
                  "url_image":"2024_09_13_ff5ab84ac307a7835792g-1"
                  "url_image_explanation":null
                }
              ],
              "stop":false
            }
          °/response°
        °/gptChatExemple°    
        
        °userChatExemple°
          transforme moi la question Nb° 5
        °/userChatExemple°    
        
        °gptChatExemple°    
          °response°
            {
              "question": "On veut étudier le lien entre le dosage d'un biomarqueur et l'envahissement ganglionnaire axillaire chez des patientes présentant un cancer du sein. Sur un échantillon pris au hasard de taille $\\mathrm{N}=32$ on a observé les résultats suivants :
                $$
                \\\\begin{array}{|c|c|c|c|}
                \\\\hline
                 & \\\\text{Ganglions envahis} & \\\\text{Ganglions non envahis} & \\\\text{Total} \\\\\\\\
                \\\\hline
                \\\\text{Biomarqueur } (: \\\\geq 200 \\\\text{ UI}) & 9 & 2 & 11 \\\\\\\\
                \\\\hline
                \\\\text{Biomarqueur } (: 100-199 \\\\text{ UI}) & 5 & 5 & 10 \\\\\\\\
                \\\\hline
                \\\\text{Biomarqueur } (<100 \\\\text{ UI}) & 2 & 9 & 11 \\\\\\\\
                \\\\hline
                \\\\text{Total} & 16 & 16 & 32 \\\\\\\\
                \\\\hline
                \\\\end{array}
                $$
                On souhaite effectuer un test du $\\\\chi^{2}$ pour étudier ce lien. Résultat de la statistique $=8,91$. Parmi les assertions suivantes, laquelle est (ou lesquelles sont) exacte(s) ? :)",
              "imagesEnonce": [],
              "imagesCorrection": [],
              "coursAssocie": [ 36 ],
              "question_answers": [
                {
                  "text": "IL CONvient d'effectuer un test du $\\\\chi^{2}$ d'indépendance entre une variable quantitative et une variable qualitative",
                  "isTrue":${ameliorateImport?`false`:`null`},
                  "explanation":${ameliorateImport?`Le test du $\\\\chi^2$ est utilisé pour étudier l'association entre deux variables qualitatives. Ici, le dosage du biomarqueur est catégorisé donc les deux variables sont qualitativES.`:`null`},
                  ${ameliorateImport?``:`°Commentaire°Ici et pour le reste de cet exercice, il n'y a pas de correction donc on ne renseigne ni le boolean *isTrue* ni le champ *explanation*°/Commentaire°`}
                  "url_image": null,
                  "url_image_explanation": null
                },
                {
                  "text": "L'HYPOthèse nulle est : le niveau du dosage du biomarqueur est indépendant de l'état des ganglions axillaires (envahis ou non envahis)",
                  "isTrue":${ameliorateImport?`true`:`null`},
                  "explanation":${ameliorateImport?`L'hypothèse nulle dans un test du $\\\\chi^2$ d'indépendance suppose qu'il n'y a pas de relation entre les deux variables étudiées, ici le dosage du biomarqueur et l'état des ganglions axillairES.`:`null`},
                  "url_image": null,
                  "url_image_explanation": null
                },
                {
                  "text": "L'ABSEnce d'effectif observé inférieur à 5 autorise la réalisation du test du $\\\\chi^{2}$",
                  "isTrue":${ameliorateImport?`true`:`null`},
                  "explanation":${ameliorateImport?`Pour que le test du $\\\\chi^2$ soit valide, idéalement tous les effectifs attendus devraient être supérieurs à 5. Ici, les effectifs observés sont suffisants pour respecter cette conditiON.`:`null`},
                  "url_image": null,
                  "url_image_explanation": null
                },
                {
                  "text": "AU RISque $\\\\alpha=0,05$, le degré de signification du test est $\\\\mathrm{p}<0,01$",
                  "isTrue":${ameliorateImport?`true`:`null`},
                  "explanation":${ameliorateImport?`Puisque la valeur de la statistique $\\\\chi^2 = 8,91$, cela indique une probabilité p-value très faible, inférieure à 0,01 au risque $\\\\alpha=0,05$, supportant un rejet de l'hypothèse nulLE.`:`null`},
                  "url_image": null,
                  "url_image_explanation": null
                },
                {
                  "text": "POUR Un risque $\\\\alpha=0,05$, on rejette l'hypothèse nulle",
                  "isTrue":${ameliorateImport?`true`:`null`},
                  "explanation":${ameliorateImport?`Quand la p-value est moins de 0,05, l'hypothèse nulle est rejetée au seuil de signification de 5%, soutenant une relation significative entre les variablES.`:`null`},
                  "url_image": null,
                  "url_image_explanation": null
                }
              ],
              "stop": false
            }

          °/response°
        °/gptChatExemple°   
        
        
        °userChatExemple°
          transforme moi la question Nb° 6
        °/userChatExemple°    
        
        °gptChatExemple°    
          °response°
            {
              "question":"",
              "imagesEnonce":[],
              "imagesCorrection":[],
              "coursAssocie":[],
              "question_answers":[],
              "stop":true
            }
          °/response°
        °/gptChatExemple°         
      °/chatExemple°
    °/exemple°
  `,

  EXPECTED_QUESTION_NUMBER: (n) => `
    Le document contient ${n} questions. Assurez-vous que le nombre de questions importées correspond bien à ce total. Chaque question doit être clairement identifiée et formatée.
  `,


  GENERATE_SYSTEM_PROMPT: ({ latexText, customPrompt,ameliorateImport,coursMapping,expectedNumberOfQuestions}) => `
  ${REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.ROLE}
  
  ${REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.INSTRUCTIONS_GENERAL_AMELIORATION_DEPENDANT(ameliorateImport)}
  
  ${REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.EXEMPLES_CONVERSION}
  
  ${ameliorateImport === true ? REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.SMALL_AMELIORATION_FRAGMENT_FOR_OBJECTIF_V2 : ""}
  
  ${ameliorateImport === false ? REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.SMALL_NO_AMELIORATION_FRAGMENT_FOR_OBJECTIF_V2 : ""}
  
  ${REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.MAPPING_COURS}
  
  ${REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.INSTRUCTION_STOP}
  
  ${customPrompt==="" ? "" : REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.CUSTOM_PROMPT(customPrompt)}
  
  ${REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.OBJECT_MAPPING(coursMapping)}
  
  ${REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.EXEMPLES_COMPLETS_AMELIORATION_DEPENDANT(ameliorateImport)}
  
  ${expectedNumberOfQuestions !== "null" ? REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.EXPECTED_QUESTION_NUMBER(expectedNumberOfQuestions) : ""}
  
  ${REWORKED_IMPORT_QUESTION_WITH_AI_PROMPT_AND_JSON_SCHEMAV2.LATEX_INPUT(latexText)}
  `
}

export const JSON_SCHEMA_STRICT={
  QCM_AND_QCU_CREATION_FROM_PDF:{
    "type": "json_schema",
    "json_schema": {
      "name": "question_formatting",
      "strict": true,
      "schema": {
        "type": "object",
        "properties": {
          "question": {
            "type": "string",
            "description": "Énoncé de la question"
          },
          "question_answers": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "text": {
                  "type": "string",
                  "description": "Un des choix possibles pour la question"
                },
                "isTrue": {
                  "type": "boolean",
                  "description": "Indique si le choix est correct (true) ou incorrect (false)"
                },
                "explanation": {
                  "type": "string",
                  "description": "Explication de pourquoi le choix est correct ou incorrect"
                }
              },
              "required": ["text", "isTrue", "explanation"],
              "additionalProperties": false
            },
            "description": "Liste des choix possibles pour la question"
          }
        },
        "required": ["question", "question_answers"],
        "additionalProperties": false
      }
    }
  },
  FILL_IN_THE_BLANKS_FROM_PDF:{
    "type": "json_schema",
    "json_schema": {
      "name": "fill_in_the_blanks_formatting",
      "strict": true,
      "schema": {
        "type": "object",
        "properties": {
          "question": {
            "type": "string",
            "description": "Énoncé de la question"
          },
          "settings": {
            "type": "object",
            "properties": {
              "text": {
                "type": "string",
                "description": "Texte avec des trous à remplir, avec les mots manquants entourés d'astérisques (*) et séparés par un slash (/) si plusieurs réponses sont acceptées"
              }
            },
            "required": ["text"],
            "additionalProperties": false
          }
        },
        "required": ["question", "settings"],
        "additionalProperties": false
      }
    }
  },
  IMPORT_QCU_AND_QCM_AMELIORATION_DEPENDANT:(ameliorateImport)=> {
    const schema ={
      "type": "json_schema",
      "json_schema": {
        "name": "question_import_avec_amelioration_formatting",
        "strict": true,
        "schema": {
          "type": "object",
          "properties": {
            "question": {
              "type": "string",
              "description": "Énoncé de la question QCM/QCU à formater. Peut inclure des éléments MathJax."
            },
            "imagesEnonce": {
              "type": "array",
              "items": {
                "type": "string",
                "description": "Identifiant uniques d' image associées à la question."
              },
              "description": "Tableau contenant les identifiants uniques des images nécessaire à la bonne compréhension de la question."
            },
            "imagesCorrection": {
              "type": "array",
              "items": {
                "type": "string",
                "description": "Identifiants uniques des images présentes dans la correction."
              },
              "description": "Tableau contenant les identifiants uniques des images dans la correction."
            },
            "coursAssocie": {
              "type": "array",
              "items": {
                "type": "integer",
                "description": "IDs de mapping correspondant au cours associé à la question."
              },
              "description": "Liste des IDs de mapping associés au cours lié à la question."
            },
            "question_answers": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "text": {
                    "type": "string",
                    "description": "Proposition de réponse au QCM/QCU."
                  },
                  "isTrue": {
                    "type": "boolean",
                    "description":ameliorateImport ?
                      "Indique la valeur de vérité de la proposition. Si elle n'est pas indiqué dans le LaTeX fourni, tu dois l'inférer "
                      : 
                      "Si présent dans le LaTeXInput, récupérez la valeur de vérité de la proposition, sinon remplissez ce champ avec null."
                    ,
                  },
                  "explanation": {
                    "type": "string",
                    "description":ameliorateImport ?
                      "Explication de la valeur de vérité de la réponse pour aider l'élève à comprendre. Si l'explication n'est pas présente dans le LaTeX fourni, tu dois la créer."
                      :
                      "Si présent dans le LaTeXInput, récupère l'explication de la valeur de vérité du LaTeX sinon remplissez ce champ avec une string vide."
                    ,
                  },
                  "url_image": {
                    "type": ["string", "null"],
                    "description": "Identifiant unique de l'image liée à cette réponse, ou null s'il n'y a pas d'image."
                  },
                  "url_image_explanation": {
                    "type": ["string", "null"],
                    "description": "Identifiant unique de l'image liée à l'explication de cette réponse, ou null s'il n'y a pas d'image."
                  }
                },
                "required": ["text", "isTrue", "explanation", "url_image", "url_image_explanation"],
                "additionalProperties": false
              },
              "description": "Liste des propositions de réponse au QCM/QCU, avec leur explication."
            },
            "stop": {
              "type":"boolean",
              "description":"Indique si oui ou non tu as un exercice dans ta réponse. Si tu as un exercice tu dois retourner false, si tu n'as pas d'exercice, tu dois retourner true"
            }
          },
          "required": ["question", "question_answers", "imagesEnonce", "imagesCorrection", "coursAssocie","stop"],
          "additionalProperties": false
        }
      }
    }

    return schema
  },
  /*
  FLASHCARD_SCHEMA:{
    "type": "json_schema",
    "json_schema": {
      "name": "test",
      "strict": true,
      "schema": {
        "type": "object",
        "properties": {
          "enonce": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "type": {
                  "type": "string",
                  "enum": [
                    "image",
                    "text"
                  ],
                  "description": "Type de contenu, peut être \"image\" ou \"text\"."
                },
                "data": {
                  "type": "string",
                  "description": "Le texte ou le chemin de l'image, en fonction du type."
                }
              },
              "required": [
                "type",
                "data"
              ],
              "additionalProperties": false
            }
          },
          "correction": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "type": {
                  "type": "string",
                  "enum": [
                    "image",
                    "text"
                  ],
                  "description": "Type de contenu, peut être \"image\" ou \"text\"."
                },
                "data": {
                  "type": "string",
                  "description": "Le texte ou le chemin de l'image, en fonction du type."
                }
              },
              "required": [
                "type",
                "data"
              ],
              "additionalProperties": false
            }
          }
        },
        "required": [
          "enonce",
          "correction"
        ],
        "additionalProperties": false
      }
    }
  }*/
  "FLASHCARD_SCHEMA": {
    "type": "json_schema",
    "json_schema": {
      "name": "test",
      "strict": true,
      "schema": {
        "title": "Flashcard Schema",
        "description": "Schéma JSON pour la structure d'une flashcard",
        "type": "object",
        "properties": {
          "enonce": {
            "type": "array",
            "description": "Liste ordonée des éléments qui composent l'énoncé de la flashcard.",
            "items": {
              "type": "object",
              "properties": {
                "type": {
                  "type": "string",
                  "enum": ["text", "image"],
                  "description": "Type de contenu, peut être \"text\" ou \"image\" en fonction de ce que l'objet contient."
                },
                "text": {
                  "type": "string",
                  "description": "Le texte associé. Doit être null si le type est \"image\"."
                },
                "path": {
                  "type": "string",
                  "description": "Le chemin de l'image. Doit être null si le type est \"text\"."
                },
                "size": {
                  "type": ["string", "null"],
                  "enum": [null, "big", "medium", "small"],
                  "description": "Taille du texte : \"big\" (peu de mots), \"medium\" (phrases), \"small\" (paragraphes), ou null si c'est une image."
                }
              },
              "required": ["type", "text", "path", "size"],
              "additionalProperties": false
            }
          },
          "correction": {
            "type": "array",
            "description": "Liste ordonée des éléments qui composent la réponse de la flashcard.",
            "items": {
              "type": "object",
              "properties": {
                "type": {
                  "type": "string",
                  "enum": ["text", "image"],
                  "description": "Type de contenu, peut être \"text\" ou \"image\" en fonction de ce que l'objet contient."
                },
                "text": {
                  "type": "string",
                  "description": "Le texte associé. Doit être null si le type est \"image\"."
                },
                "path": {
                  "type": "string",
                  "description": "Le chemin de l'image. Doit être null si le type est \"text\"."
                },
                "size": {
                  "type": ["string", "null"],
                  "enum": [null, "big", "medium", "small"],
                  "description": "Renseigne sur la taille du texte : \"big\" pour les concepts importants et doit être limité à 4 mots max. \"medium\" pour les phrases. \"small\" pour les paragraphs et ou définitions. null si c'est une image."
                }
              },
              "required": ["type", "text", "path", "size"],
              "additionalProperties": false
            }
          }
        },
        "required": [
          "enonce",
          "correction"
        ],
        "additionalProperties": false
      }
    }
  }
}

export const IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT={
  ROLE: `
    Tu es un professeur expérimenté et attentif, responsable de l'évaluation de la compréhension des élèves. 
    Ta mission est de vérifier que tes élèves maîtrisent parfaitement le contenu du cours fourni dans la balise °coursInput°.
    Tu dois t'assurer que chaque question posée évalue efficacement la compréhension du cours par tes élèves.
  `,
  OBJECTIF: (questionNumber, questionFormat) => `
    Ton objectif est de générer ${questionNumber} ${questionFormat} distinctes pour évaluer l'étendue des connaissances de tes élèves sur le °coursInput°. 
    Chaque question doit couvrir des aspects différents du cours pour garantir une évaluation complète. 
    Les questions doivent être variées et pertinentes pour assurer une couverture maximale du contenu.
  `,
  FORMAT_ATTENDU: (questionFormat) => `
    Les questions doivent être formatées en JSON selon une structure précise que je vais te décomposer à l'aide des balises suivantes : 
      - La balise °key° indique le nom du champ à dans le format JSON
      - la balise °value° indique la valeur du champ dans le format JSON 
    
    Chaque question doit inclure :
      - °key°question°/key° : un énoncé clair sous forme de string, par exemple : °value°Combien font 2+5 ?°/value°
      - °key°question_answers°/key° : un tableau contenant des objets représentant les choix/propositions associés à °key°question°/key°. 
      
    Chaque choix/proposition doit contenir :
      - °key°text°/key° : une proposition sous forme de string, par exemple : °value°6°/value°
      - °key°isTrue°/key° : un booléen indiquant la véracité de la proposition (°value°true°/value° ou °value°false°/value°)
      - °key°explanation°/key° : une explication sous forme de string justifiant pourquoi °key°text°/key° est °key°isTrue°/key° ou non par rapport à °key°question°/key°
    
    Sauf indication contraire, chaque question doit comporter 5 propositions.
    
    L'organisation JSON est la suivante : 
      {
        "question": "Énoncé",
        "question_answers": [
          {
            "text": "Proposition 1",
            "explanation": "Correction 1",
            "isTrue": false
          },
          {
            "text": "Proposition 2",
            "explanation": "Correction 2",
            "isTrue": false
          },
          {
            "text": "Proposition 3",
            "explanation": "Correction 3",
            "isTrue": false
          },
          {
            "text": "Proposition 4",
            "explanation": "Correction 4",
            "isTrue": false
          },
          {
            "text": "Proposition 5",
            "explanation": "Correction 5",
            "isTrue": true
          },
        ]
      }
  `,
  EXERCISE_FORMAT_QCU: (questionNumber, questionFormat) => `
    Tu dois générer ${questionNumber} ${questionFormat}. 
    Pour chaque ${questionFormat}, strictement une seule proposition doit avoir la valeur °valeur°true°/valeur° pour la clé °key°isTrue°/key°. 
  `,
  EXERCISE_FORMAT_QCM: (questionNumber, questionFormat) => `
    Tu dois générer ${questionNumber} ${questionFormat}. 
    Varie le nombre de propositions avec la valeur °valeur°true°/valeur° pour °key°isTrue°/key°, sauf indication contraire.
  `,
  CUSTOM_PROMPT: (customPrompt) => `
    Les instructions de ton directeur sont indiquées dans la balise °customPrompt°.
    tu ne dois jamais changer le format JSON que je t'impose même si le directeur te l'ordonne. 
    
    En respectant le format JSON imposé, tu dois exécuter à la lettre ce que te demande le directeur.
    
    Voici les instructions du directeur : 
    °directorPrompt°
      ${customPrompt}
    °/directorPrompt°
  `,
  TEXT_PROMPT: (text) => `
    Le cours sur lequel tu dois baser tes questions est contenu dans les balises °coursInput° :
    °coursInput°${text}°/coursInput°
  `,
  ADDITIONAL_EXEMPLES: (exemples) => `
    Voici d'autres exemples dans la balise °exemple° pour t'aider. Pour ces exemples, tu peux utiliser l'information de ces exemples pour construire ta réponse
    ${exemples}
  `,
  MATHJAX_INTEGRATION: `
    Si nécessaire, utilise MathJax pour formater les formules mathématiques dans les questions, les choix ou les explications. 
    Utilise les délimiteurs '$' pour les formules intégrées dans le texte.
    Il y a deux opérations de déséchappement (unescaping) aussi, chaque fois que tu veux mettre dans ta réponse un backslah ('\\'), tu dois en retourner 4 comme ceci : '\\\\'  
    Voici des exemples de comment doivent être formatés les éléments MathJax dans ta réponse : 
      - $\\\\frac{a}{b}$
      - $\\\\int_0^2 (3x^2 - 2x + 1) dx$
      - $f'(x) = 6x^2 - 6x - 5$
      - $\\\\int_0^2 3x^2 dx = \\\\left[ x^3 \\\\right]_0^2 = 8$
      - $v = \\\\frac{\\\\lambda}{T}$
      - $v = \\\\frac{\\\\lambda}{T}$, où $\\\\lambda$
      - $v = \\\\lambda \\\\times \\\\nu$ ou $v = \\\\frac{\\\\lambda}{T}$
      - $v = \\\\operatorname{c}$
      - $\\\\mathrm{RCOOH} + \\\\mathrm{R'OH} \\\\rightarrow \\\\mathrm{RCOOR'} + \\\\mathrm{H_2O}$
  `,
  GENERATE_SYSTEM_PROMPT : ({ numberOfQuestions, exerciseFormat, customPrompt, context, additionnalExemple = "" }) => `
    ${IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.ROLE}  
    ${IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.OBJECTIF(numberOfQuestions, exerciseFormat)}
    ${exerciseFormat === McqScaleQuestionType?.MultipleChoice ? `${IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.EXERCISE_FORMAT_QCM(numberOfQuestions, exerciseFormat)}` : ""}
    ${exerciseFormat === McqScaleQuestionType?.UniqueChoice ? `${IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.EXERCISE_FORMAT_QCU(numberOfQuestions, exerciseFormat)}` : ""}
    ${IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.FORMAT_ATTENDU(exerciseFormat)}
    ${additionnalExemple === "" ? "" : IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.ADDITIONAL_EXEMPLES(additionnalExemple)}
    ${IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.CUSTOM_PROMPT(customPrompt)}
    ${IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.MATHJAX_INTEGRATION}
    ${IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.TEXT_PROMPT(context)}
    ${IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.CUSTOM_PROMPT(customPrompt)}
`
}

export const IMPROVED_FILL_IN_THE_BLANK_AI_CREATION_PROMPT={
  ROLE: `
    Tu es un professeur expérimenté spécialisé dans la création d'exercices pédagogiques pour évaluer la compréhension des élèves. 
    Ta mission est de créer des textes à trou basés sur le cours fourni dans la balise °coursInput°. Ces exercices doivent être conçus pour inciter les élèves à réfléchir et démontrer leur compréhension du contenu du cours.
  `,

  OBJECTIF: (textNumber) => `
    Tu dois générer ${textNumber} textes à trou en utilisant le contenu du °coursInput°. 
    Chaque texte doit comporter des trous pertinents pour évaluer la compréhension des concepts clés du cours. 
    Inclut des indices pour aider les élèves si nécessaire, et propose des alternatives valides lorsque cela est pertinent.
  `,

  FORMAT_ATTENDU: `
    Les textes à trou doivent être générés dans un format JSON précis. Voici la structure attendue :

    Chaque réponse doit contenir :
      - °key°question°/key° : Constante avec la valeur : "Complétez les mots manquants".
      - °key°settings.text°/key° : Le texte avec les trous. Les mots manquants ainsi que leurs indices doivent être entourés d'astérisques (une avant et une après). Plusieurs réponses valides peuvent être séparées par un slash (/). Un indice sous forme de texte peut être ajouté après les réponses et toujours dans les astérisques en plaçant un double-point (:) après les réponses suivi de l'indice. 
      - Chaque trou doit correspondre à un unique élément à trouver. Si plusieurs réponses sont proposées, elles doivent être des synonymes ou des variantes très proches pour garantir qu'une seule réponse plausible puisse être remplie dans le contexte du texte.
      
    Exemple : °value°Ce contenu peut être édité en utilisant un *navigateur/navigateur web:Quelque chose que vous êtes en train d'utiliser*°/value°.
    Dans cet exemple, les mots manquants sont 'navigateur' et 'navigateur web' et l'indice est 'Quelque chose que vous êtes en train d'utiliser'


    Assure-toi que le texte est clair et que les trous évaluent bien la compréhension du °coursInput°.
  `,

  EXERCISE_FORMAT_FILL_IN_THE_BLANKS: (textNumber) => `
    Génère ${textNumber} textes à trou. 
    Pour chaque texte, identifie les éléments essentiels du cours qui peuvent être transformés en trous. 
    Ajoute des indices si nécessaire, et propose des alternatives correctes là où c'est pertinent.
  `,

  CUSTOM_PROMPT: (customPrompt) => `
    Suis les instructions spécifiques du directeur, présentes dans la balise °customPrompt°. 
    N'oublie pas de maintenir le formatage imposé (JSON et imposé par le texte à trou) car sinon la question ne sera pas valide et ignorée. 
    Instructions du directeur : °directorPrompt°${customPrompt}°/directorPrompt°.
  `,

  TEXT_PROMPT: (text) => `
    Voici le texte de référence dans les balises °coursInput° pour créer les textes à trou :
    °coursInput°${text}°/coursInput°.
  `,


  EXEMPLES: `
    Pour t'aider, voici des exemples dans les balises °exemple° et °response° :
    °exemple°
      Exemple de texte à trou basé sur un °coursInput° d'histoire :
      °response°
        {
          "question": "Complétez les mots manquants concernant la Révolution française.",
          "settings": {
            "text": "La Révolution française, qui a commencé en *1789/an de 1789/année 1789:année de début*, a été marquée par des bouleversements *sociaux/sociétaux/populaires:types de bouleversements*. La *prise de la Bastille/capture de la Bastille/saisie de la Bastille:événement marquant* le 14 juillet 1789 symbolise le début de la Révolution. En 1793, le roi *Louis XVI/roi Louis XVI/monarque Louis XVI:nom du roi* fut exécuté. La période de la *Terreur/peur/règne de la Terreur:nom de la période* suivit, dirigée par *Robespierre/Maximilien Robespierre/R. Robespierre:nom du leader*. En 1799, *Napoléon Bonaparte/Napoléon I/Napoléon:nom du leader* prit le pouvoir, mettant fin à la *Révolution/rébellion/révolte:événement historique*. Cette période est souvent considérée comme le début de l'ère *moderne/actuelle/contemporaine:époque historique*. La Révolution a profondément influencé la structure *sociale/civique/communautaire:structure de la société* de la France."
          }
        }
      °/response°
    °/exemple°
    
    
    °exemple°
      Exemple de texte à trou basé sur un °coursInput° de biologie :
      °response°
        {
          "question": "Complétez les mots manquants concernant la Révolution française.",
          "settings": {
            "text": "La photosynthèse est un processus par lequel les plantes convertissent la *lumière solaire/énergie solaire/rayonnement solaire:source d'énergie* en énergie chimique. Ce processus se déroule dans les *chloroplastes/organites chloroplastiques/structures chloroplastiques:partie de la cellule* qui se trouvent principalement dans les *feuilles/folioles/feuillage:partie de la plante*. Le pigment *chlorophylle/vert chlorophylle/chlorophylle verte:pigment* y joue un rôle crucial. Le *dioxyde de carbone/CO2/gaz carbonique:réactif* et l'eau sont utilisés pour produire du *glucose/sucre/énergie chimique:produit final* et de l'*oxygène/O2/air respirable:autre produit*. Ce processus est essentiel pour la *survie/épanouissement/croissance:importance* des plantes et, par extension, pour la vie sur Terre. La photosynthèse contribue également à la *régénération/recyclage/production:effet* de l'oxygène dans l'atmosphère."          
          }
        }
      °/response°
    °/exemple°
    
    °exemple°
      Exemple de texte à trou basé sur un °coursInput° de biologie :
      °response°
        {
          "question": "Complétez les mots manquants concernant la Révolution française.",
          "settings": {
          "text": "Dans 'Les Misérables', Victor Hugo raconte l'histoire de *Jean Valjean/Valjean/Jean:nom du personnage principal*, un ancien *forçat/convict/bagnard:statut* qui cherche à se *racheter/se racheter/absolution:but du personnage*. Il est poursuivi par *Javert/inspecteur Javert/policier Javert:nom de l'antagoniste*, un inspecteur de police *inflexible/rigide/strict:caractéristique de l'antagoniste*. Le roman explore les thèmes de la *justice/équité/droit:thème principal*, la *rédemption/pardon/absolution:thème moral* et la lutte entre le *bien et le mal/vertu et vice/bien et mal:concepts opposés*. 'Les Misérables' est un roman *classique/iconique/majeur:importance dans la littérature* qui a profondément influencé la littérature *française/francophone/française et mondiale:champ d'influence*. L'œuvre de Victor Hugo est souvent vue comme une critique des *injustices sociales/inégalités sociales/injustices:thème critique* de l'époque."          }
        }
      °/response°
    °/exemple°
  `,

  ADDITIONAL_EXEMPLES: (exemples) => `
    D'autres exemples de réponses attendues :
    ${exemples}
  `,

  MATHJAX_NON_INTEGRATION: `
    Tu ne dois pas utiliser de langage de formatage dans les réponses.
  `,

  GENERATE_SYSTEM_PROMPT: ({ textNumber, customPrompt, context, additionalExample = "" }) => `
    ${IMPROVED_FILL_IN_THE_BLANK_AI_CREATION_PROMPT.ROLE}
    ${IMPROVED_FILL_IN_THE_BLANK_AI_CREATION_PROMPT.OBJECTIF(textNumber)}
    ${IMPROVED_FILL_IN_THE_BLANK_AI_CREATION_PROMPT.EXERCISE_FORMAT_FILL_IN_THE_BLANKS(textNumber)}
    ${IMPROVED_FILL_IN_THE_BLANK_AI_CREATION_PROMPT.FORMAT_ATTENDU}
    ${IMPROVED_FILL_IN_THE_BLANK_AI_CREATION_PROMPT.EXEMPLES}
    ${additionalExample === "" ? "" : IMPROVED_FILL_IN_THE_BLANK_AI_CREATION_PROMPT.ADDITIONAL_EXEMPLES(additionalExample)}
    ${IMPROVED_FILL_IN_THE_BLANK_AI_CREATION_PROMPT.CUSTOM_PROMPT(customPrompt)}
    ${IMPROVED_FILL_IN_THE_BLANK_AI_CREATION_PROMPT.MATHJAX_NON_INTEGRATION}
    ${IMPROVED_FILL_IN_THE_BLANK_AI_CREATION_PROMPT.TEXT_PROMPT(context)}
  `
}
export const FLASHCARD_CREATION_PROMPT = {
  ROLE: `
    Tu joues le rôle d'une intelligence artificielle avancée, spécialisée dans l'assistance à la création de contenus éducatifs. 
    Ton objectif est d'aider les professeurs à synthétiser un cours fourni sous forme de texte, extrait d'un fichier PDF, en un ensemble de flashcards structurées.
    Ces flashcards doivent être conçues pour évaluer la compréhension des points essentiels du cours par les élèves.
    Tu dois transformer les concepts clés du cours en questions et réponses claires, tout en respectant le format JSON imposé.
    Lorsque le contenu le permet, utilise des images pour illustrer des concepts complexes ou améliorer l'engagement des élèves. 
  `,

  //     Les questions doivent être consise afin de rentrer sur la flashcard.
  //     Les réponses doivent être petites et si possible en un mot.
/*
  OBJECTIF: (questionNumber) => `
    Ton objectif est de générer ${questionNumber} flashcards au total, chacune composée d’au moins un objet dans l'énoncé et un dans la réponse. 
    Même si ces flashcards seront demandées individuellement, tu dois préparer en amont un plan cohérent qui couvre l’ensemble du contenu fourni dans la balise °coursInput°.
  
    Qu’est-ce qu’une bonne flashcard ?
  
    1. **Elle couvre un seul concept clé** : l’idée ou la question doit être simple et ciblée, afin que la réponse (ou correction) soit claire et précise.
    2. **Elle est concise et rapide à appréhender** : 
       - Utilise la taille "big" pour mettre en valeur un titre percutant ou une réponse très courte (jusqu’à 4 mots).
       - La taille "medium" sert pour des phrases complètes ou des explications intermédiaires.
       - La taille "small" est idéale pour des paragraphes détaillés, des définitions ou des arguments plus développés.
    3. **Elle est structurée pour maximiser la mémorisation** : la flashcard doit être autonome, permettre une révision rapide et favoriser la rétention de l’information (par exemple en utilisant la répétition ou des mots-clés).
    4. **Elle est engageante et peut inclure des images** : alterne entre texte et image si cela renforce la compréhension ou capte davantage l’attention.
  
    Dans la pratique, tu transformeras le texte initial (°coursInput°) en une suite de flashcards conformes au schéma JSON spécifié. 
    - Chaque flashcard contient un tableau d’éléments pour l’énoncé (\`enonce\`) et un tableau d’éléments pour la réponse ou correction (\`correction\`). 
    - Chaque élément peut être de type "text" ou "image", avec la bonne taille ("big", "medium" ou "small") lorsque c’est du texte, ou \`null\` pour la taille si c’est une image.
  
    L’objectif est de proposer un contenu pédagogique, humain et agréable, qui rende l’apprentissage plus efficace grâce à un format simple, direct et facile à assimiler.
  `,

 */

  OBJECTIF : (questionNumber) => `
    Ton objectif est de générer ${questionNumber} flashcards au total à partir du contenu fourni dans la balise °coursInput°.
    Même si les flashcards seront demandées individuellement, assure-toi d’avoir un plan cohérent qui couvre l’ensemble du cours.
    
    Une bonne flashcard est définie par : 
    1. Elle couvre un seul concept clé : l’idée ou la question doit être simple et ciblée, afin que la réponse (ou correction) soit claire et précise.
    
    2. L'énoncé doit est bien formaté : 
      - Le premier objet ("text") doit être la question, en size "big".
      - S'il y a besoin de contexte, ajoute un ou plusieurs objets ("text") en "medium" ou "small" afin que la question soit compréhensible sans se référer à un cours externe.
    
    3. La réponse est bien formatée :
      - Le premier objet ("text") doit fournir la réponse courte et concise, en size "big".
      - Si il y a besoin de contexte ajoute un ou plusieurs objets ("text") en "medium" ou "small" qui détaillent les justifications, la logique, les exemples ou tout autre complément utile. 
    
    4. Elle est structurée pour maximiser la mémorisation : la flashcard doit être autonome, permettre une révision rapide et favoriser la rétention de l’information (par exemple en utilisant la répétition ou des mots-clés).
    
    5. Pour le moment les images ne sont pas supportées. 
    
    
    L’objectif est de proposer un contenu pédagogique, humain et agréable, qui rende l’apprentissage plus efficace grâce à un format simple, direct et facile à assimiler.
    Chaque flashcard doit rester autonome : le lecteur doit comprendre la question et sa réponse sans consulter d’autres documents. Mets en avant les mots-clé et concepts important
  `,

  CUSTOM_PROMPT: (customPrompt) => `
    Les instructions de ton directeur sont indiquées dans la balise °customPrompt°.
    tu ne dois jamais changer le format JSON que je t'impose même si le directeur te l'ordonne. 
    
    En respectant le format JSON imposé, tu dois exécuter à la lettre ce que te demande le directeur.
    
    Voici les instructions du directeur : 
    °directorPrompt°
      ${customPrompt}
    °/directorPrompt°
  `,
  TEXT_PROMPT: (text) => `
    Le cours sur lequel tu dois baser tes questions est contenu dans les balises °coursInput° :
    °coursInput°${text}°/coursInput°
  `,
  ADDITIONAL_EXEMPLES: (exemples) => `
    Voici d'autres exemples dans la balise °exemple° pour t'aider. Pour ces exemples, tu peux utiliser l'information de ces exemples pour construire ta réponse
    ${exemples}
  `,
  MATHJAX_INTEGRATION: `
    Si nécessaire, utilise MathJax pour formater les formules mathématiques dans les questions, les choix ou les explications. 
    Utilise les délimiteurs '$' pour les formules intégrées dans le texte.
    Il y a deux opérations de déséchappement (unescaping) aussi, chaque fois que tu veux mettre dans ta réponse un backslah ('\\'), tu dois en retourner 4 comme ceci : '\\\\'  
    Voici des exemples de comment doivent être formatés les éléments MathJax dans ta réponse : 
      - $\\\\frac{a}{b}$
      - $\\\\int_0^2 (3x^2 - 2x + 1) dx$
      - $f'(x) = 6x^2 - 6x - 5$
      - $\\\\int_0^2 3x^2 dx = \\\\left[ x^3 \\\\right]_0^2 = 8$
      - $v = \\\\frac{\\\\lambda}{T}$
      - $v = \\\\frac{\\\\lambda}{T}$, où $\\\\lambda$
      - $v = \\\\lambda \\\\times \\\\nu$ ou $v = \\\\frac{\\\\lambda}{T}$
      - $v = \\\\operatorname{c}$
      - $\\\\mathrm{RCOOH} + \\\\mathrm{R'OH} \\\\rightarrow \\\\mathrm{RCOOR'} + \\\\mathrm{H_2O}$
  `,

  GENERATE_SYSTEM_PROMPT: ({ numberOfQuestions, customPrompt, context, additionnalExemple = "" }) => `
    ${FLASHCARD_CREATION_PROMPT.ROLE}  
    ${FLASHCARD_CREATION_PROMPT.OBJECTIF(numberOfQuestions)}
    ${additionnalExemple === "" ? "" : FLASHCARD_CREATION_PROMPT.ADDITIONAL_EXEMPLES(additionnalExemple)}
    ${FLASHCARD_CREATION_PROMPT.CUSTOM_PROMPT(customPrompt)}
    ${IMPROVED_QCU_AND_QCM_AI_CREATION_PROMPT.MATHJAX_INTEGRATION}
    ${FLASHCARD_CREATION_PROMPT.TEXT_PROMPT(context)}
  `
}
