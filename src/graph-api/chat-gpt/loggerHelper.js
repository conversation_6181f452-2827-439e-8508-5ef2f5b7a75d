

export class LoggerHelper {

  constructor(){
    this.data={
      checkpoint:{},
      data:{},
      error:{}
    }


    this.checkpoints={}
    this.logs={}
  }


  //////
  addCheckpoint(key,data){
    this.data.checkpoint[key]=data
  }

  addData(key,data){
    this.data.data[key]=data
  }

  addError(key,data){
    this.data.error[key]=data
  }

  showError(){
    console.log(this.data.error)
  }
  showCheckpoint(){
    console.log(this.data.checkpoint)
  }

  showData(){
    console.log(JSON.stringify(this.data.data))
    console.log(this.data.data)
  }

  showAll(){
    this.showData()
    this.showCheckpoint()
    this.showError()
  }

  getData(){
    return this.data
  }
}