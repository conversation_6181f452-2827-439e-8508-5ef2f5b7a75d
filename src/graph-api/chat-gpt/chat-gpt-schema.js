import gql from 'graphql-tag'

export default gql`
    extend type Query {
      "Query qui demande à chatGPT de demander un input"
      enhanceQcmText(input:ChatGptQueryInput):[ChatGptQueryOutput]!
      
      "Query réservée aux dev local pour aider à l'analyse des logs"
      devGptLogAnalyser(id:ID!):JSON
    }
    
    extend type Mutation {
      "Permet de décommander un job de la génération par AI ( creation from pdf et importation from pdf)"
      removeGptJob(frontendToken:String!):Boolean

      ""
      createExerciseWithAiV2(frontendToken:String,input:CreateOrImport):Boolean

      importExerciseWithAiV2(frontendToken:String,input:CreateOrImport):Boolean
    }
    
    input CreateOrImport {
      coursIdArray:[ID] # Si Create => cours à lier   // Si Update => cours à inférer 
      exerciseType:[ID] # les type d'exercices à lier
      exerciceTag:[ID]

      # config model
      qcmConfigModelId:String # l'id du model de la config sélectionnée à utiliser => permet de récupérer la key de GPT
      qcmConfigConfigId:String # l'id de la configuration de gpt à utiliser,
      pixId:ID # l'id de la config mathpix

      
      # data
      createOrImportFromPdf:CreateOrImportFromPdf
      createOrImportFromRawTextData:CreateOrImportFromRawTextData
      createOrImportFromPictureData:[CreateOrImportFromPictureData!]
      createOrImportFromLocalRessourceData:CreateOrImportFromLocalRessourceData
      
      #
      numberOfQuestions:Int
      customPrompt:String
      exerciseFormat:String
      
      "Optional scaleId, if not renseigned, it will be infered"
      scaleId:ID

      exempleExerciseId:[ID]
      
      # options fill in the blank
      acceptSmallErrors:Boolean
      isCaseSensible:Boolean
      ignoreSpecialChar:Boolean
      
      # options flashcard 
      flashcardResponseType:String
      
      # Option importation
      correctionImprovement:Boolean
    }

    input CreateOrImportFromPdf{
      dataType:String!
      file:Upload!
    }

    input CreateOrImportFromRawTextData{
      dataType:String!
      text:String!
    }

    input CreateOrImportFromPictureData{
      dataType:String!
      pictureFile:Upload!
    }
    
    input CreateOrImportFromLocalRessourceData{
      dataType:String!
      localRessourceUrl:String!
    }

    

    "les paramètres nécessaire pour demander à chatGPT de transformer une query"
    input ChatGptQueryInput {
      "description du rôle de chatGPT"
      role:String!,
      
      "description de l'objectif de chatGPT"
      objectif:String!,
      
      "Set d'exemple que l'on fourni à chatGPT"
      exemple:String!,
      
      "format de réponse donné"
      formatInput:String!
      
      "format de réponse attendu"
      formatAttendu:String!,
      
      "L'objet complexe que l'on demande à chatGPT de changer"
      objectToModify:[ChatGptTextObjectInput!]!
      
      "JSON additionnel qui renseigne sur le context de la demande. Exemple : si on veut améliorer une justification, contiendra la question."
      context:JSON
      
      "Une string correspondant à des informations custom, exemple : la langue dans laquelle traduire un texte, un input customisé de l'user"
      customInstructions:String=""
      
      "La QcmConfigId associé à la demande => permet notament de récupérer l'API KEY"
      gptQcmConfigId:ID!
      
      "le modèle Id associé à QCMConfigId => permet de récupérer le modèle et ses spécifications depuis QcmConfigId"
      gptQcmModelId:ID!
    }
    
    "L'objet complexe de query à chatGPT"
    input ChatGptTextObjectInput {
      "Une string correspondante à un ID, dans ce cas, peut ne pas être unique"
      id:String!
      
      "Une string qui correspond à un ID Unique"
      uniqueId:String!
      
      "Le text à transformer "
      text:String!
      
      "Des informations complémentaire associé au text à transformer, notament si c'est un énoncé de QCM, si l'énoncé est vrai ou faux."
      additionalInputJson:AdditionalInputJson
    }
    
    "Informations supplémentaire associé au text à transformer, dans le cas d'un énoncé de QCM, si l'énoncé est vrai ou faux"
    input AdditionalInputJson {
      isTrue:Boolean=null
    }
    
    "L'objet de sortie de chatGPT"
    type ChatGptQueryOutput{
      "mirror de l'id depuis ChatGPTTextObjectInput"
      id:String!
      
      "mirror de l'uniqueId depuis ChatGPTTextObjectInput"
      uniqueId:String!
      
      "Si chatGPT considère qu'il a changé ou pas l'input (ça peut être le text ou le boolean de additionalJson)"
      isChanged:Boolean!
    
      "le text modifié ou pas de la réponse de chatGPT, peut être null"  
      newText:String
      
      "string qui informe sur ce que chatGPT a modifié si il a modifié qqc"
      explicationString:String
      
      "Retour d'information supplémentaire, à la demande de la query"
      additionalJson:JSON
    }
`