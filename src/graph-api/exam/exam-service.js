import { GraphQLError } from 'graphql';
import moment from 'moment';
import { Op } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import models from '../../models';
import { RedisService } from '../../service/redis-service.js';
import { UPLOAD_FOLDER_MAP } from '../../util/utils';
import { ROLES } from '../authorization.js';
import { AcademicDatesService } from '../config/config-service.js';
import { UploadService } from '../file/upload-service';
import { likifyWhere } from '../helpers.js';
import { PermissionService } from '../permission/permission-service.js';
import { QCMService } from '../qcm/qcm-service.js';
import { QuestionsService } from '../qcm/questions/questions-service.js';
import { QcmSessionService } from '../qcm/sessions/qcm-session-service.js';
import { QcmTypeService } from '../qcm/type/qcm-type-service.js';

export const getNormalizedRankingForResults = (_scores, gradeAttribute = 'note') => {
  const scores = _scores;
  if (scores && scores[0]) {
    scores[0].classement = 1;
  } else {
    return scores;
  }
  if (!scores?.[1]) {
    return scores;
  }
  for (let i = 1; i < scores?.length; i++) {
    if (scores[i]?.[gradeAttribute] === scores[i - 1]?.[gradeAttribute]) {
      scores[i].classement = scores[i - 1].classement;
    } else {
      scores[i].classement = i + 1;
    }
  }
  return scores;
};

const CRUDExam = {
  //////// CRUD Exam //////////
  createExam: async (exam, userId) => {
    try {
      if (exam.image) {
        exam.image = await UploadService.uploadFileDataToFolder(exam.image, UPLOAD_FOLDER_MAP.files);
      }

      let date = exam?.date;
      let dateEnd = exam?.dateEnd;
      let isOpen = exam?.isOpen;
      let duration = exam?.duration;

      let typeIds = exam?.typeIds;
      if (typeIds?.length === 0) {
        throw new GraphQLError('Aucun type d\'examen n\'a été sélectionné');
      }

      delete exam?.typeIds;
      delete exam.date;
      delete exam.dateEnd;
      delete exam.isOpen;
      delete exam.duration;

      // One session per exam
      const examCreated = await models.Exam.create(exam);

      // Ajouter Date, isOpen, durée totale

      await models.ExamSession.create({
        name: exam?.name,
        description: exam?.description,
        examId: examCreated?.id,
        order: exam?.order,
        date,
        dateEnd,
        isOpen,
        duration,
      });

      // Handles types
      for (const id of typeIds) {
        await ExamService.addTypeQcmToExam({
          typeQcmId: id,
          examId: examCreated?.id,
        }, { userId });
      }

      return examCreated;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  fixSynchroBetweenExamAndExamSession: async () => {
    const exams = await models.Exam.findAll();
    for (const exam of exams) {
      let session = await models.ExamSession.findOne({
        where: {
          examId: exam.id,
        },
      });
      if (session) {
        session.description = exam?.description;
        session.name = exam?.name;
        await session.save();
      }
    }
  },
  updateExam: async (id, input) => {
    try {
      let date = input?.date;
      let dateEnd = input?.dateEnd;
      let isOpen = input?.isOpen;
      let duration = input?.duration;

      let session = await models.ExamSession.findOne({
        where: {
          examId: id,
        },
      });
      //console.log({ date, dateEnd });
      if (session) {
        session.date = date;
        session.dateEnd = dateEnd;
        session.isOpen = isOpen;
        session.duration = duration;
        session.description = input?.description;
        session.name = input?.name;
        await session.save();
      }
      delete input.date;
      delete input.dateEnd;
      delete input.isOpen;
      delete input.duration;

      if (input.image) {
        input.image = await UploadService.uploadFileDataToFolder(input.image, UPLOAD_FOLDER_MAP.files);
      }
      let updated = await models.Exam.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0];
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour exam ' + id);
    }
  },
  deleteExam: async (id, userId) => {
    try {
      let debug = false;
      const examSessions = await models.ExamSession.findAll({
        where: {
          examId: id,
        },
      });
      debug && console.log({ examSessions });
      const qcmSessions = await models.QcmSession.findAll({
        where: {
          examSessionId: examSessions.map(s => s.id),
        },
      });
      debug && console.log({ qcmSessions });
      for (const sess of qcmSessions) {
        sess.examSessionId = null;
        sess.examQuestionSerieId = null;
        await sess.save();
      }
      const examResult = await models.ExamResult.findAll({
        where: {
          examSessionId: examSessions.map(s => s.id),
        },
      });
      for (const examRes of examResult) {
        examRes.examSessionId = null;
        examRes.userId = null;
        await examRes.save();
      }
      for (const sess of examSessions) {
        sess.examId = null;
        sess.isOpen = false;
        await sess.save();
        // Supprimer les date de diffusion liées, si elles existent
        if(sess?.id) {
          await models.DateDiffusion.destroy({
            where: {
              examSessionId: sess?.id,
            }
          });
        }
      }

      // Pas bon
      // const examQuestionSeries = await models.ExamQuestionSeries.destroy({ where: { examId: id } });

      const examQuestionSeries = await models.ExamQuestionSeries.findAll({ where: { examId: id } });
      for(const eqs of examQuestionSeries) {
        eqs.mcqId = null;
        eqs.examId = null;
        eqs.examSessionId = null;
        await eqs.save();
      }
      debug && console.log({ examQuestionSeries });
      await models.ExamScale.destroy({ where: { examId: id } }); //VIRER BAREMES
      debug && console.log('delete exam type qcm');
      await models.ExamTypeQcm.destroy({ where: { examId: id } }); // VIRER TYPES
      debug && console.log('delete exam');
      return await models.Exam.destroy({ where: { id } });
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error suppression exam ' + id);
    }
  },
};

const CRUDExamQuestionSeries = {
  //////// CRUD ExamQuestionSeries //////////
  createExamQuestionSeries: async (exam, userId) => {
    try {
      await ExamService.updateScaleQuestionSeriesLinkFromSerie(exam?.examId);
      if (exam.image) {
        exam.image = await UploadService.uploadFileDataToFolder(exam.image, UPLOAD_FOLDER_MAP.files);
      }
      const qs = await models.ExamQuestionSeries.create(exam);
      await ExamService.updateScaleQuestionSeriesLinkFromSerie(exam?.examId);
      return qs;
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création de la série');
    }
  },
  updateExamQuestionSeries: async (id, input) => {
    try {
      if (input.image) {
        input.image = await UploadService.uploadFileDataToFolder(input.image, UPLOAD_FOLDER_MAP.files);
      }
      let updated = await models.ExamQuestionSeries.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour série ' + id);
    }
  },
  deleteExamQuestionSeries: async (id, userId) => {
    try {
      const qcmSessions = await models.QcmSession.findAll({
        where: {
          examQuestionSerieId: id,
        },
      });
      for (const session of qcmSessions) {
        session.examQuestionSerieId = null;
        await session.save();
      }
      await models.ExamScaleQuestionSeries.destroy({
        where: {
          examQuestionSerieId: id,
        },
      });

      return await models.ExamQuestionSeries.destroy({ where: { id } });
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Error suppression série ' + id);
    }
  },
};

const CRUDExamScale = {
  //////// CRUD ExamScale //////////
  createExamScale: async (input, userId) => {
    try {
      // When scale is created, add it to all question series
      const examScale = await models.ExamScale.create(input);
      await ExamService.updateScaleQuestionSeriesLink(input?.examId, examScale?.id);

      return examScale;
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création de barême exam');
    }
  },
  updateExamScale: async (id, input) => {
    try {
      let updated = await models.ExamScale.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour barême exam ' + id);
    }
  },
  deleteExamScale: async (id, userId) => {
    try {
      return await models.ExamScale.destroy({ where: { id } });
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error suppression barême exam ' + id);
    }
  },
};

const CRUDExamSession = {
  //////// CRUD ExamSession //////////
  createExamSession: async (exam, userId) => {
    try {
      const examSession = await models.ExamSession.create(exam);
      // add date diffusion
      await models.DateDiffusion.create({
        examSessionId: examSession?.id,
        date: exam?.date,
      });

      return examSession;
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création de session exam');
    }
  },
  updateExamSession: async (id, input) => {
    try {
      // Update date diff
      const dateDiff = await models.DateDiffusion.findOne({
        where: {
          examSessionId: id,
        },
      });
      dateDiff.date = input?.date;
      await dateDiff.save();

      // Update ExamSession
      let updated = await models.ExamSession.update(
        input, {
          where: { id: id },
        },
      );
      return updated[0]; // oui
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur mise à jour session exam ' + id);
    }
  },
  deleteExamSession: async (id, userId) => {
    try {
      await models.DateDiffusion.destroy({ where: { examSessionId: id } });
      return await models.ExamSession.destroy({ where: { id } });
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Error suppression session exam ' + id);
    }
  },


  /* Groupes exam session */
  async addGroupToExamSession({ groupId, examSessionId }, me) {
    try {
      const groupe = await models.Groupe.findByPk(groupId);

      /* Update date diff groupes */
      const dateDiff = await models.DateDiffusion.findOne({
        where: {
          examSessionId: examSessionId,
        },
      });
      if (dateDiff) {
        await models.DateDiffusionGroups.create({
          groupId: groupId,
          date_diffusion_id: dateDiff?.id,
        });
      }

      await groupe.addExamSession(examSessionId);
      return !!(await groupe.save());
    } catch (e) {
      console.error(e);
    }
    return Promise.resolve(undefined);
  },
  async removeGroupFromExamSession({ groupId, examSessionId }, me) {
    try {
      const groupe = await models.Groupe.findByPk(groupId);

      /* Delete date diff group */
      const dateDiff = await models.DateDiffusion.findOne({
        where: {
          examSessionId: examSessionId,
        },
      });
      if (dateDiff) {
        await models.DateDiffusionGroups.destroy({
            where: {
              groupId: groupId,
              date_diffusion_id: dateDiff?.id,
            },
          },
        );
      }

      await groupe.removeExamSession(examSessionId);
      return !!(await groupe.save());
    } catch (e) {
      console.error(e);
    }
  },

};

export const ExamService = {
  ...CRUDExam,
  ...CRUDExamQuestionSeries,
  ...CRUDExamScale,
  ...CRUDExamSession,

  /* This is a function that gets all the exams from the database. */
  // For ADMINs, tuteurs
  getAllExams: async ({ filter }, models, me) => {
    try {
      const { name, folderId } = filter;
      let where = {
        ...(name && { name }),
        folderId,
      };
      likifyWhere(where, 'name');
      const myAllowedExamsIds = await PermissionService.Exams.getExamIdsAuthorizedForUser(me?.id);
      const exams = await models.Exam.findAll({
        where,
        order: [['createdAt', 'DESC']],
      });
      return exams.map(exam => {
        return {
          ...exam.get({ plain: true }), // Convertit l'instance Sequelize en objet simple
          disabled: !myAllowedExamsIds.includes(exam.id),
        }
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  /* Updating the scale question series link. */
  async updateScaleQuestionSeriesLink(examId, examScaleId) {
    const questionSeries = await models.ExamQuestionSeries.findAll({
      where: {
        examId,
      },
    });
    for (const questionSerie of questionSeries) {
      try {
        const obj = await models.ExamScaleQuestionSeries.findOne({
          where: {
            examScaleId: examScaleId,
            examQuestionSerieId: questionSerie?.id,
          },
        });
        if (!obj) {
          await models.ExamScaleQuestionSeries.create({
            examScaleId: examScaleId,
            examQuestionSerieId: questionSerie?.id,
          });
        }
      } catch (e) {
        console.error(e);
        continue;
      }
    }
  },

  /* Updating the scale question series link from the exam. */
  async updateScaleQuestionSeriesLinkFromSerie(examId, examQuestionSerieId) {
    const examScales = await models.ExamScale.findAll({
      where: {
        examId,
      },
    });
    // pour chaque scale, regarger si il en manque pas
    for (const examScale of examScales) {
      await ExamService.updateScaleQuestionSeriesLink(examId, examScale?.id);
    }
  },


  //
  async addExamScaleToExamQuestionSerie({ input }, me) {
    try {
      const { examScaleId, examQuestionSerieId, coefficient } = input;
      const scale = await models.ExamScale.findByPk(examScaleId);
      const questionSerie = await models.ExamQuestionSeries.findByPk(examQuestionSerieId);
      if (scale && questionSerie) {
        const examScaleQuestionSerie = await models.ExamScaleQuestionSeries.create({
          examScaleId,
          examQuestionSerieId,
          coefficient,
        });
      } else {
        throw new GraphQLError('Exam scale or question serie not found');
      }
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async removeExamScaleFromExamQuestionSerie({ input }, me) {
    try {
      const { examScaleId, examQuestionSerieId } = input;
      const examScaleQuestionSerie = await models.ExamScaleQuestionSeries.findOne({
        where: {
          examScaleId,
          examQuestionSerieId,
        },
      });
      return !!(await examScaleQuestionSerie?.destroy());
    } catch (e) {
      console.error(e);
    }
  },
  async updateExamScaleExamQuestionSerie({ input }, me) {
    try {
      const { examScaleId, examQuestionSerieId, coefficient } = input;
      const examScaleQuestionSerie = await models.ExamScaleQuestionSeries.findOne({
        where: {
          examScaleId,
          examQuestionSerieId,
        },
      });
      examScaleQuestionSerie.coefficient = coefficient;
      return !!(await examScaleQuestionSerie.save());
    } catch (e) {
      console.error(e);
    }
  },


  async getExamById(id, me) {
    try {
      //if (await PermissionService.Exams.isUserAuthorizedForExam(me?.id, id)) {
      // TODO voir pour check exam groupe(s) par date diffusion

      return await models.Exam.findByPk(id);
      //}
      throw new GraphQLError('Vous n\'avez pas l\'autorisation d\'accéder à cet examen');
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async getExamSessionById(id, me) {
    try {
      return await models.ExamSession.findByPk(id);
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async getExamQuestionSerieById(id, me) {
    try {
      return await models.ExamQuestionSeries.findByPk(id);
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  // on EXAM object
  async getExamSessions(exam, me) {
    // For admin gets all
    if ([ROLES.ADMIN, ROLES.SUB_ADMIN].includes(me?.role)) {
      return models.ExamSession.findAll({
        where: {
          examId: exam?.id,
        },
      });
    }

    const examSessionIds = await PermissionService.Exams.getExamSessionIdsAvailableForUser(exam, me?.id);

    return models.ExamSession.findAll({
      where: {
        id: examSessionIds,
        examId: exam?.id,
      },
    });
  },

  /* All scales for exam */
  async getExamScales(exam, me) {
    return models.ExamScale.findAll({
      where: {
        examId: exam?.id,
      },
    });
  },

  /* Default scale or first found for exam */
  async getDefaultExamScale(examId, me) {
    let scale = await models.ExamScale.findOne({
      where: {
        examId,
        isDefault: true,
      },
    });
    if (!scale) {
      scale = await models.ExamScale.findOne({
        where: {
          examId,
        },
      });
    }
    return scale;
  },

  /*
  * From mcq session, updates only if session has examQuestionSerieId
  * qcmSession : qcm_sessions db object
  */
  async updateExamUserSessionAfterQuizzSerieCompletion(qcmSession) {
    try {
      // If session has examQuestionSerie
      if (qcmSession?.examQuestionSerieId !== null) {
        // It's an exam mcq session type
        const examQuestionSerie = await models.ExamQuestionSeries.findByPk(qcmSession?.examQuestionSerieId);
        // Check if he has completed all question series
        const examSessionId = examQuestionSerie?.examSessionId;
        const allQuestionSeriesIn = await models.ExamQuestionSeries.findAll({
          where: {
            examSessionId,
          },
          attributes: ['id'],
        });
        const questionSeriesIds = allQuestionSeriesIn?.map(a => a.id);
        const mcqSessionsDoneInSerie = await models.QcmSession?.findAll({
          where: {
            examQuestionSerieId: questionSeriesIds,
            userId: qcmSession?.userId,
          },
        });

        // Check si resultat final existe
        const existingResult = await models.ExamResult.findOne({
          where: {
            examSessionId,
            userId: qcmSession?.userId,
          },
        });
        // Si fait toutes les questions de la session d'examen
        if (mcqSessionsDoneInSerie?.length === allQuestionSeriesIn?.length) {
          // Créer résultat examen
          if (!existingResult) {
            await models.ExamResult.create({
              examSessionId,
              userId: qcmSession?.userId,
            });
          }
        } else {
          //console.log(`Not all series done for user ${qcmSession?.userId} examSession ${examSessionId}`);
          // Si pas terminé après suppression par exemple, il faut virer le résultat final
          if (existingResult) {
            await models.ExamResult.destroy({
              where: {
                examSessionId,
                userId: qcmSession?.userId,
              },
            });
          }
        }
      } else {
        return true;
      }
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },


  /**
   * Auto-creates exam result (if not created) from session or legacy user's results
   * so we know user finished the exam
   *
   * @param examSessionId
   * @param {number} userId
   * @returns {Promise<boolean>}
   */
  async updateExamUserSessionAfterQuizzSerieCompletionFromSerieIdAndUser(examSessionId, userId) {
    try {
      if (examSessionId !== null) {
        // It's an exam mcq session type
        const allQuestionSeriesIn = await models.ExamQuestionSeries.findAll({
          where: {
            examSessionId,
          },
          attributes: ['id', 'mcqId'],
        });
        const mcqIds = allQuestionSeriesIn?.map(a => a.mcqId);
        /*
        const mcqSessionsDoneInSerie = await models.QcmSession?.findAll({
          where: {
            examQuestionSerieId: questionSeriesIds,
            userId: userId,
          },
        });
        */
        const userResultStat = await models.QcmStats.findAll({
          where: {
            id_qcm: mcqIds,
            id_utilisateur: userId,
          }, attributes: ['note'],
        });

        // Si fait toutes les questions de la session d'examen
        if (userResultStat?.length >= allQuestionSeriesIn?.length) {
          // Créer résultat examen
          const existingResult = await models.ExamResult.findOne({
            where: {
              examSessionId,
              userId: userId,
            },
          });
          if (!existingResult) {
            await models.ExamResult.create({
              examSessionId,
              userId: userId,
            });
          }
        }
      } else {
        return true;
      }
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  /**
   * All question series for an exam session
   * @param examSession
   * @param me
   * @returns {Promise<any[]|Model[ExamQuestionSeries]>}
   */
  async getExamQuestionSeries(examSession, me) {
    let enableCache = false;
    const REDIS_KEY_getExamQuestionSeries = `getExamQuestionSeries-${examSession?.id}-${me?.id}`;
    if (enableCache) {
      let cachedValue = await RedisService.get(REDIS_KEY_getExamQuestionSeries);
      if (cachedValue) {
        return cachedValue;
      }
    }
    const result = await models.ExamQuestionSeries.findAll({
      where: {
        examSessionId: examSession?.id,
      },
      order: [['order', 'ASC']],
    });
    if (enableCache && result) {
      await RedisService.set(REDIS_KEY_getExamQuestionSeries, result);
    }
    return result;
  },

  //TODO virer le cache quand ExamScaleQuestionSeries change ou examScaleId change
  async getExamScaleQuestionSerie(examScaleId, examQuestionSerieId, enableCache = true) {
    const REDIS_KEY_ExamScaleQuestionSeries = `scaleQuestionSerieForScale-${examScaleId}-examQuestionSerieId-${examQuestionSerieId}`;
    let scaleQuestionSerie;
    if (enableCache) {
      let cachedValue = await RedisService.get(REDIS_KEY_ExamScaleQuestionSeries);
      if (cachedValue) {
        return cachedValue;
      }
    }
    scaleQuestionSerie = await models.ExamScaleQuestionSeries?.findOne({
      where: {
        examScaleId,
        examQuestionSerieId,
      },
      raw: true,
    });
    if (enableCache && scaleQuestionSerie) {
      await RedisService.set(REDIS_KEY_ExamScaleQuestionSeries, scaleQuestionSerie);
    }
    return scaleQuestionSerie;
  },

  //todo virer cache quand la QcmSession du user est créée ou modifiée
  async getQcmSessionForExamQuestionSerieAndUser(userId, examQuestionSerieId, enableCache = true) {
    const REDIS_KEY = `getQcmSessionForExamQuestionSerieAndUser-${examQuestionSerieId}-${userId}`;
    let sessionQcm;
    if (enableCache) {
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
    }
    sessionQcm = await models.QcmSession.findOne({
      where: {
        userId,
        examQuestionSerieId,
      },
      attributes: ['id', 'questionsIdsDone'],
      order: [['createdAt', 'ASC']],
      raw: true,
    });
    if (enableCache && sessionQcm) {
      await RedisService.set(REDIS_KEY, sessionQcm);
    }
    return sessionQcm;
  },

  /**
   * (read only) Retourne la note finale SUR 20 obtenue pour des séries de questions examQuestionSeries, selon le barême examScale
   * @param examQuestionSeries
   * @param examScaleId
   * @param userId
   * @returns {Promise<number>}
   */
  async getUserGradeForQuestionSeries(examQuestionSeries, examScaleId, userId) {
    try {
      //TODO refactor & use cache
      const DEBUG = false;
      let enableCache = false;
      const REDIS_KEY = `getUserGradeForQuestionSeries-${examQuestionSeries?.map(eqs => ` ${eqs?.id}`)}-${examScaleId}-${userId}`;
      if (enableCache) {
        let cachedValue = await RedisService.get(REDIS_KEY);
        if (cachedValue) {
          return cachedValue;
        }
      }

      let coeffSum = 0.0;
      let gradeTmp = 0.0;
      let gradeAttributes = ['id', 'note', 'date', 'id_utilisateur'];
      DEBUG && console.log('---getUserGradeForQuestionSeries---');
      for (const eqs of examQuestionSeries) {
        const mcqId = eqs?.mcqId;
        // Barème de la question série
        const scaleQuestionSerie = await ExamService.getExamScaleQuestionSerie(examScaleId, eqs?.id, enableCache);
        if (!scaleQuestionSerie) {
          console.error('cannot find scaleQuestionSerie for examScaleId ' + examScaleId + ' and examQuestionSerieId ' + eqs?.id);
        }
        /* We need to find the userResultStat for this question serie */

        // Même requête de recup de la note que pour détail de notes exams (getStatsForQuestionSerie)
        let userResultPossibleStats = await models.QcmStats?.findAll({
          where: {
            id_qcm: mcqId,
            id_utilisateur: userId,
            isFirstTime: true,
          },
          include: [{
            model: models.QcmSession,
            attributes: ['id', 'isFinished'],
            required: false,
          }],
          attributes: gradeAttributes,
          order: [['date', 'ASC']],
          raw: true,
        });
        userResultPossibleStats = userResultPossibleStats.filter((result) => {
          if(!result?.['qcm_session.id']) {
            return true;
          }
          return result?.['qcm_session.isFinished'] === 1
        });

        let userResultStat = userResultPossibleStats?.[0];

        // Note brute obtenue
        const grade = parseFloat(userResultStat?.note);
        const coefficient = parseFloat(scaleQuestionSerie?.coefficient); // Coefficient du bareme, ex: 8
        // mettre la note sur 20
        const exercise = await models.Qcm.findByPk(mcqId, { raw: true });
        const maxPointsForExercise = await QCMService.getMcqMaximumPoints(exercise, {});
        const noteSur20 = (grade / parseFloat(maxPointsForExercise)) * 20.0;
        // Note pondérée
        gradeTmp += (noteSur20 * coefficient);
        // Total des coefficients
        coeffSum += coefficient;

        DEBUG && console.log({userId, grade, coefficient, noteSur20, maxPointsForExercise, coeffSum});
      }
      /* Note finale TODO la mettre en cache ? et supprimer si:
        examQuestionSeries changent
        le nombre max de points qcm change
        la note stat change
        barême change
      */
      const userGrade = (gradeTmp / coeffSum);
      DEBUG && console.log({userGrade});
      DEBUG && console.log('---/getUserGradeForQuestionSeries---');
      if (enableCache) {
        await RedisService.set(REDIS_KEY, userGrade);
      }

      return userGrade;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  /**
   * (read only) Returns the ExamResult for a given examSessionId and userId
   * @param examSessionId
   * @param userId
   * @returns {Promise<ExamResult>}
   */
  async getExamSessionResultForUserAndExamSession(examSessionId, userId) {
    // TODO maybe cache, not updated often
    return models.ExamResult.findOne({
      where: {
        examSessionId,
        userId,
      },
    });
  },

  /**
   * Session exam user final grades result for USER
   * @param id :examSessionId
   * @param userId
   * @param examScaleId
   * @param withAllGrades
   * @param me
   * @returns {Promise<{createdAt: *, total: number, notesParEffectif: *[], examSessionId: *, notes: *[], grade: string, id: *, monClassement: number, userId: *, moyenne: null, updatedAt: *}|null|ExamResult|{createdAt: *, total: (number|number), notesParEffectif: *[], examSessionId: (*|null), notes: (*|*[]), grade: string, id: *, monClassement: (number|*), userId: (*|null), moyenne: string, updatedAt: *}>}
   */
  async getExamSessionResult({ id, userId, examScaleId, withAllGrades = true }, me) {
    try {
      //TODO improve performances
      const shouldLogPerfs = false;
      const DEBUG = false;

      let start = new Date().getTime();
      const userIdToLookFor = parseInt(userId) || me?.id;
      // resultat de la table ExamResult
      let result = await ExamService.getExamSessionResultForUserAndExamSession(id, userIdToLookFor);
      // All series in exam
      const examQuestionSeries = await models.ExamQuestionSeries.findAll({
        where: {
          examSessionId: id,
        },
      });
      let end = new Date().getTime();
      let time = end - start;
      shouldLogPerfs && console.log('QUERY FOR getExamSessionResultForUserAndExamSession took ' + time);

      start = new Date().getTime();

      // Check if it can be autogenerated If no result registered yet
      if (!result && examQuestionSeries.length > 0) {
        /* Create final exam result if not already created */
        await ExamService.updateExamUserSessionAfterQuizzSerieCompletionFromSerieIdAndUser(id, userIdToLookFor);
        result = await ExamService.getExamSessionResultForUserAndExamSession(id, userIdToLookFor);
      }
      if (!examScaleId) {
        // Did not ask for specific scale, gets default result
        return result;
      }
      // My final grade
      DEBUG && console.log({examScaleId, userIdToLookFor});
      const finalGrade = await ExamService.getUserGradeForQuestionSeries(examQuestionSeries, examScaleId, userIdToLookFor);

      DEBUG && console.log({finalGrade});

      end = new Date().getTime();
      time = end - start;
      shouldLogPerfs && console.log('QUERY FOR getUserGradeForQuestionSeries 1 took ' + time);

      if (result && !finalGrade) {
        console.log('Exam finalGrade not found, but result registered, deleting result (condition: result && !finalGrade)');
        await result.destroy();
        return null;
      }

      //TODO fix classement ici si besoin
      if (!withAllGrades) {
        return {
          id: result?.id,
          notes: [],
          moyenne: null,
          monClassement: 1,
          grade: finalGrade.toFixed(2),
          total: 1,
          notesParEffectif: [],
          examSessionId: result?.examSessionId,
          createdAt: result?.createdAt,
          updatedAt: result?.updatedAt,
          userId: result?.userId,
        };
      }

      const allExamResults = await models.ExamResult.findAll({
        where: { examSessionId: id },
        attributes: ['userId'],
        raw: true,
      });
      const allUserIds = allExamResults?.map(a => a.userId);

      /* Build user grades */
      let allGrades = [];
      let m = 0;

      // Ça prend trop de temps
      start = new Date().getTime();
      for (const uId of allUserIds) {
        let userGrade = await ExamService.getUserGradeForQuestionSeries(examQuestionSeries, examScaleId, uId);
        if (!isNaN(userGrade)) {
          allGrades.push({ note: userGrade });
          m += userGrade;
        }
      }
      end = new Date().getTime();
      time = end - start;
      shouldLogPerfs && console.log('QUERY FOR getUserGradeForQuestionSeries IN LOOP took ' + time);

      // Sort
      allGrades = allGrades?.sort((a, b) => b?.note - a?.note);
      allGrades = allGrades?.map((r, i) => ({ classement: i + 1, ...r }));
      allGrades = getNormalizedRankingForResults(allGrades);
      let monClassement = allGrades.find(r => r.note === finalGrade)?.classement;
      // Mettre sur 20
      //const maPositionIndex = allGrades?.findIndex(g => g?.note === finalGrade);
      /*
      if (maPositionIndex !== null && maPositionIndex !== undefined) {
        monClassement = allGrades?.length - (maPositionIndex);
      }
      */
      const notesParEffectif = [];
      const simpleNotesArray = allGrades?.map(n => n.note);
      const counts = {};
      simpleNotesArray.forEach((note) => {
        counts[note] = (counts[note] || 0) + 1;
      });
      Object.keys(counts).forEach(key => {
        notesParEffectif.push({
          note: parseFloat(key),
          effectif: counts[key],
        });
      });

      /*
        if (!result) {
          return null;
        }
       */

      let moyenne = (m / allGrades?.length).toFixed(2);
      if (isNaN(moyenne)) {
        moyenne = null;
      }
      let grade = finalGrade.toFixed(2);
      if (isNaN(grade)) {
        grade = null;
      }
      return {
        id: result?.id,
        notes: allGrades || [],
        moyenne,
        monClassement: monClassement || 1,
        grade,
        total: allGrades?.length || 0,
        notesParEffectif: notesParEffectif || [],
        examSessionId: result?.examSessionId || null,
        createdAt: result?.createdAt,
        updatedAt: result?.updatedAt,
        userId: result?.userId || null,
        // Notes min et max
      };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },


  // Scale => ExamQuestionSerie
  async getExamQuestionSerieForExamScale(examScale, me) {
    try {
      const scaleSeries = await models.ExamScaleQuestionSeries.findAll({
        where: {
          examScaleId: examScale?.id,
        },
        attributes: ['coefficient', 'examQuestionSerieId'],
        raw: true,
      });
      let obj = [];
      for (const scaleSerie of scaleSeries) {
        const questionSerie = await models.ExamQuestionSeries.findByPk(scaleSerie?.examQuestionSerieId);
        obj.push({
          coefficient: scaleSerie?.coefficient,
          questionSerie,
        });
      }
      return obj;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async getExamFromQcmSession(session, ctx) {
    try {
      const examQuestionSerieId = session?.examQuestionSerieId;
      if (examQuestionSerieId) {
        const questionSerie = await models.ExamQuestionSeries.findByPk(examQuestionSerieId);
        return models.Exam.findByPk(questionSerie?.examId);
      }
      return null;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async createDuplicatedMcqFromGenerator(questionsIds, ueId, me) {
    try {
      const ue = await models.UE.findByPk(ueId);
      const user = await models.User.findByPk(me?.id);
      // Create qcm entity
      const qcm = await models.Qcm.create({
        titre: ue?.name + ' généré - examen',
        description: '',
        id_lien: uuidv4(),
        date_creation: moment().toDate(),
        date_modif: moment().toDate(),
        pseudo_createur: user?.username,
        id_createur: me?.id,
        UEId: ueId,
        ue: ueId,
        annee: await AcademicDatesService.getCurrentScholarYear(),
        hasExternalQuestions: false,
        //isPublished: false,
      });
      // Duplicate questions
      for (const qId of questionsIds) {
        await QCMService.duplicateQuestion({ id: qId }, me?.id, qcm?.id_qcm);
      }
      return qcm?.id_qcm;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  // With imported questions
  async createImportedMcqFromGenerator(questionsIds, ueId, me) {
    try {
      const ue = await models.UE.findByPk(ueId);
      const user = await models.User.findByPk(me?.id);
      const qcm = await models.Qcm.create({
        titre: ue?.name + ' généré - examen',
        description: '',
        id_lien: uuidv4(),
        date_creation: moment().toDate(),
        date_modif: moment().toDate(),
        pseudo_createur: user?.username,
        id_createur: me?.id,
        UEId: ueId,
        ue: ueId,
        annee: await AcademicDatesService.getCurrentScholarYear(),
        hasExternalQuestions: true,
        // isPublished: false,
      });

      for (const qId of questionsIds) {
        const question = await models.Question.findByPk(qId);
        await qcm.addImportedQuestion(question);
      }
      return qcm?.id_qcm;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  /**
   * Returns all examSession Results for a given examScale
   * @param examSession
   * @param examScaleId
   * @param me
   * @returns {Promise<*[]>}
   */
  async getUserResultsForSession(examSession, examScaleId, me) {
    try {
      const examSessionId = examSession?.id;
      // Question series in session
      const allQuestionSeriesIn = await models.ExamQuestionSeries.findAll({
        where: {
          examSessionId,
        },
        attributes: ['id', 'mcqId'],
        raw: true,
      });
      const mcqIds = allQuestionSeriesIn?.map(a => a.mcqId);
      // Find exercices grades for this exam
      const userResultStat = await models.QcmStats.findAll({
        where: {
          id_qcm: mcqIds,
          isFirstTime: true,
        },
        attributes: ['id_utilisateur'],
      });
      const userIds = [...new Set(await userResultStat.map(u => u.id_utilisateur))];
      for (const userId of userIds) {
        /* Create final exam result if not already created */
        await ExamService.updateExamUserSessionAfterQuizzSerieCompletionFromSerieIdAndUser(examSessionId, userId);
      }
      const examResults = await models.ExamResult.findAll({
        where: {
          examSessionId,
        },
      });
      let allResults = [];
      /* For each exam result, get final grade, rank, etc. */
      for (const examResult of examResults) {
        const userId = examResult?.userId;
        // Get final result
        const result = await ExamService.getExamSessionResult(
          {
            id: examSession?.id,
            userId,
            examScaleId,
            withAllGrades: false,
          },
          me,
        );
        if (result && result?.id) {
          allResults.push(result);
        }
      }
      return allResults;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async getSingleUserResultForSession(examSession, userId = null, me) {
    try {
      // Question series in session
      if (!examSession) {
        throw new GraphQLError('examSession should be specified');
      }
      if (!examSession?.examId) {
        console.error(`no exam link to session ${examSession?.id}`);
        return null;
      }
      const userIdToFetch = parseInt(userId) || me?.id;
      const examSessionId = examSession?.id;
      /*
      const allQuestionSeriesIn = await models.ExamQuestionSeries.findAll({
        where: {
          examSessionId,
        },
        attributes: ['id', 'mcqId'],
        raw: true,
      });
      */
      //const mcqIds = allQuestionSeriesIn?.map(a => a.mcqId);
      /* Create final exam result if not already created */
      await ExamService.updateExamUserSessionAfterQuizzSerieCompletionFromSerieIdAndUser(examSessionId, userIdToFetch);
      // find default exam scale for this session
      const defaultScale = await ExamService.getDefaultExamScale(examSession?.examId, me);
      if (!defaultScale) {
        //TODO fix this
        console.error(`Impossible de trouver barême pour exam ${examSession?.name} ${examSession?.examId}`);
        return null;
      }
      const examScaleId = defaultScale?.id;
      // Get final result
      const result = await ExamService.getExamSessionResult(
        {
          id: examSession?.id,
          userId: userIdToFetch,
          examScaleId,
          withAllGrades: true,
        }, me,
      );
      return result;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  // ALL USERS GRADES FOR AN EXAM QUESTION SERIE
  // TODO voir si Ok
  async getUserResultsForQuestionSerie(examQuestionSerie, me) {
    try {
      let enableCache = false;
      const qcmId = examQuestionSerie?.mcqId;
      // Session peut ne pas être créé dans le cas où serie faite ailleurs de l'exam et
      // un autre exam a été créé avec une nouvelle examQuestionSerie pointant vers la même série
      /*
        const mcqSessions = await models.QcmSession.findAll({
          where: {
            examQuestionSerieId: examQuestionSerie?.id,
          },
          raw: true,
        });
      */
      let mcqSessions = await models.QcmSession.findAll({
        where: {
          qcmId,
        },
      });
      const sessionIds = mcqSessions?.map(s => s?.id);

      /*
      //TODO check si y'a pas des doublons dans le cas où multiple imports
      let results = await models.QcmStats.findAll({
        where: {
          qcmSessionId: sessionIds,
        },
        order: [['note', 'DESC']],
      });
      */

      // All including previous outside exam
      const results = await models.QcmStats.findAll({
        where: {
          id_qcm: qcmId,
          isFirstTime: true,
        },
        order: [['note', 'DESC']],
      });

      return results;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  // Exams session started but not necessarily completed
  async getMyExamsDone({ userId, filter = {} }, me) {
    let debug = false;
    const { qcmSeriesTypes = [], dateBegin, dateEnd } = filter || {};
    let userIdToLookFor = userId;
    if (!userIdToLookFor) {
      userIdToLookFor = me?.id;
    }
    let dateFilter = {};
    const hasDateFilter = (dateBegin && dateEnd);
    /* Filter user results by date */
    if (hasDateFilter) {
      dateFilter = {
        createdAt: {
          [Op.between]: [dateBegin, dateEnd],
        },
      };
    }
    // Sessions de séries QCM commencées mais pas forcément terminées
    let sessionsWithExamQuestionSeriesResult = await models.QcmSession.findAll({
      where: {
        userId: userIdToLookFor,
        examQuestionSerieId: {
          [Op.ne]: null,
        },
      },
    });

    debug && console.log({ sessionsWithExamQuestionSeriesResultLength: sessionsWithExamQuestionSeriesResult.length });
    //Test with statsWithExamResults:

    let examSessionsIds = [];
    for (const session of sessionsWithExamQuestionSeriesResult) {
      // Update comme quoi il a fini si il a fini (ou pas)
      await ExamService.updateExamUserSessionAfterQuizzSerieCompletion(session);
      const examQuestionSerie = await models.ExamQuestionSeries.findByPk(session?.examQuestionSerieId);
      // FILTER BY DATE
      let shouldByIncluded = true;
      if (hasDateFilter) {
        const examResultWithDate = await models.ExamResult.findOne({
          where: {
            examSessionId: examQuestionSerie?.examSessionId,
            userId: userIdToLookFor,
            ...dateFilter,
          },
        });
        if (!examResultWithDate) {
          shouldByIncluded = false;
        }
      }
      debug && console.log({ shouldByIncluded });

      if (shouldByIncluded) {
        examSessionsIds.push(examQuestionSerie?.examSessionId);
      }
    }

    /* Filter user results by type of question serie */
    if (qcmSeriesTypes?.length > 0) {
      const examIdsWithTypesSelected = await QcmTypeService.getExamIdsInType(qcmSeriesTypes);
      // exam sessions having
      const examSessionsWithTypesSelected = await models.ExamSession.findAll({
        where: {
          examId: examIdsWithTypesSelected,
        },
        attributes: ['id'],
        raw: true,
      });
      const examSessionIdsWithTypesSelected = examSessionsWithTypesSelected?.map(e => e.id) || [];
      examSessionsIds = examSessionsIds.filter(id => examSessionIdsWithTypesSelected.includes(id));
    }

    return models.ExamSession.findAll({
      where: {
        id: examSessionsIds,
      },
    });
  },

  async isExamSessionFinishedByMe(examSession, { userId }, me) {
    let userIdToLookFor = userId;
    if (!userIdToLookFor) {
      userIdToLookFor = me?.id;
    }
    const examResult = await models.ExamResult.findOne({
      where: {
        userId: userIdToLookFor,
        examSessionId: examSession?.id,
      },
    });

    return !!examResult; // boolean
  },

  async addTypeQcmToExam({ typeQcmId, examId }, ctx) {
    try {
      const exam = await models.Exam.findByPk(examId);
      const typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      if (!exam || !typeQcm) {
        throw new GraphQLError('Exam or Type not found');
      }
      await models.ExamTypeQcm.create({
        examId,
        typeQcmId,
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async removeTypeQcmFromExam({ typeQcmId, examId }, ctx) {
    try {
      const exam = await models.Exam.findByPk(examId);
      const typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      if (!exam || !typeQcm) {
        throw new GraphQLError('Exam or Type not found');
      }
      await models.ExamTypeQcm.destroy({
        where: {
          examId,
          typeQcmId,
        },
      });
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async migrateExamSessionGroupsToDateDiff() {
    const examSessions = await models.ExamSession.findAll();

    for (const session of examSessions) {
      // date de début
      let dateDiff = {};
      dateDiff.examSessionId = session.id;
      if (session?.date) {
        dateDiff.date = session?.date;
        dateDiff.availability = 'date';
      } else {
        dateDiff.availability = 'allTheTime';
      }
      const newDateDiff = await models.DateDiffusion.create(dateDiff);

      // link exam session groups to dateDiff
      const examSessionGroups = await models.ExamSessionGroups.findAll({
        where: {
          examSessionId: session.id,
        },
      });
      for (const examSessionGroup of examSessionGroups) {
        await models.DateDiffusionGroups.create({
          date_diffusion_id: newDateDiff.id,
          groupId: examSessionGroup?.groupeId,
        });
      }

    }
  },

  /**
   * (read and write) Get QcmSession for an exam serie and user
   * @param examQuestionSerie
   * @param userId
   * @param me
   * @returns {Promise<*>}
   */
  async getExamQuestionSerieQcmSession(examQuestionSerie, userId, me) {
    try {
      const userIdToLookFor = userId || me?.id;
      let sessionQcm = {};
      // Cherche session correspondante à la série dans l'exam, et à l'user
      sessionQcm = await models.QcmSession.findOne({
        where: {
          userId: userIdToLookFor,
          examQuestionSerieId: examQuestionSerie?.id,
        },
        order: [['createdAt', 'ASC']],
      });
      const mcqId = examQuestionSerie?.mcqId;
      // Si on trouve pas la sessionQcm associée à l'user et à l'exam
      if (!sessionQcm) {
        // On cherche pour le userId alors qu'il a pas fini l'exam
        const oldUserResults = await models.QcmStats.findOne({
          where: {
            id_utilisateur: userIdToLookFor,
            isFirstTime: true, //TODO check si ré-importé, pas toujours first time !
            id_qcm: mcqId,
          },
        });
        // build dynamic
        if (oldUserResults) {
          console.log(`\n!! Session not existing for examQuestionSerieId=${examQuestionSerie?.id} userId=${userIdToLookFor}`);
          console.log(`!! Found oldUserResults(QcmStats), creating new session and updating old result to link session\n`);
          // Create it ?
          // Update old result to link session
          const questionIds = await QuestionsService.getQuestionsIdsForMcq(mcqId);
          // Create qcmSession dynamically
          const dynamicSession = await models.QcmSession.create({
            userId: userIdToLookFor,
            isFinished: true,
            questionsIdsDone: questionIds,
            currentCoursId: null,
            examQuestionSerieId: examQuestionSerie?.id,
            createdAt: oldUserResults.date,
            updatedAt: oldUserResults.date,
          });

          // Normalement, pas besoin de ça, c'est source de bugs. On devrait pas changer les anciens résultats.
          // oldUserResults.qcmSessionId = dynamicSession.id;
          // await oldUserResults.save();
          sessionQcm = dynamicSession;
        } else {
          // console.log(`did not find  oldUserResults(QcmStats) for eqs=${examQuestionSerie?.id} userId=${userId}` )
        }
      }
      return sessionQcm;
    } catch (e) {
      console.error(e);
    }
  },

  // Résultat utilisateur de série au sein de l'exam.
  /* Gets min grade, max grade and ranking for an examQuestionSerie */
  async getStatsForQuestionSerie(examQuestionSerie, me, sessionId, userId = null) {
    //TODO refactor this
    try {
      const debugDontUseFirstTimeOnly = false; // Should stay false for maximum exam compat
      const qcmId = examQuestionSerie?.mcqId;
      let userIdToLookFor = me?.id;
      if(userId) {
        userIdToLookFor = parseInt(userId);
      }
      if (sessionId) {
        const session = await models.QcmSession.findByPk(sessionId);
        userIdToLookFor = session?.userId;
      }

      let results;
      if (debugDontUseFirstTimeOnly) {
        // Toutes les sessions de la série
        let mcqSessions = await models.QcmSession.findAll({
          where: { examQuestionSerieId: examQuestionSerie?.id },
          attributes: ['id'],
          raw: true,
        });
        const sessionIds = mcqSessions?.map(s => s?.id);
        results = await models.QcmStats.findAll({
          where: {
            qcmSessionId: sessionIds,
            id_qcm: qcmId,
          },
          order: [['note', 'DESC']],
          raw: true,
        });
      } else {
        // Récup resultats finaux, si session il faut s'assurer qu'elle est terminée
        results = await models.QcmStats.findAll({
          where: {
            id_qcm: qcmId,
            isFirstTime: true,
          },
          include: [{
            model: models.QcmSession,
            attributes: ['id', 'isFinished'],
            required: false,
          }],
          order: [['note', 'DESC']],
          raw: true,
        });
        results = results.filter((result) => {
          if(!result?.['qcm_session.id']) {
            return true;
          }
          return result?.['qcm_session.isFinished'] === 1
        });
      }

      /* Classement */
      results = results?.map((r, i) => ({ classement: i + 1, ...r }));
      results = getNormalizedRankingForResults(results);

      //Premier resultat trouvé de moi
      const myResult = [...results].sort((a, b) => new Date(a.date) - new Date(b.date)).find(r => {
        return r.id_utilisateur === userIdToLookFor;
      });

      let classement = myResult?.classement;
      const myGrade = myResult?.note;

      let minGrade = results[results?.length - 1]?.note || undefined;
      let maxGrade = results[0]?.note || undefined;

      // Grades for ranking graph
      const notes = results?.map(r => ({ note: r?.note }));

      const notesParEffectif = [];
      let simpleNotesArray = results?.map(n => n.note);
      // Sort
      simpleNotesArray = simpleNotesArray.sort((a, b) => a?.note - b?.note);

      const counts = {};
      simpleNotesArray.forEach((note) => {
        counts[note] = (counts[note] || 0) + 1;
      });
      Object.keys(counts).forEach(key => {
        notesParEffectif.push({
          note: parseFloat(key), //?.toFixed(2),
          effectif: counts[key],
        });
      });

      return {
        minGrade,
        maxGrade,
        classement,
        myGrade,
        notes,
        notesParEffectif,
        total: results?.length || 0,
      };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async examsMassActions(args, ctx) {
    try {
      const {ids, input, action} = args;
      const exams = await models.Exam.findAll({
        where: {
          id: ids,
        },
      });
      if (action === 'move') {
        // Root folder ID is null
        if(input.folderId !== null) {
          const folder = await models.Folder.findByPk(input.folderId);
          if (!folder) {
            throw new GraphQLError('Dossier introuvable');
          }
        }
        for (let exam of exams) {
          await exam.update({ folderId: input.folderId });
        }
        return true;
      }
      else {
        throw new GraphQLError('Action non reconnue');
      }
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
};
