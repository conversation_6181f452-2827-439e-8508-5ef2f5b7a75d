import { combineResolvers } from 'graphql-resolvers';
import moment from 'moment';
import { isAdmin, isAuthenticated, isTuteurOrAdmin } from '../authorization';
import { EdtService } from '../edt/edt-service.js';
import { ExamService } from './exam-service.js';

export default {
  Query: {

    allExams: combineResolvers(
      isAdmin,
      async (parent, params, { models, me }) => ExamService.getAllExams(params, models, me),
    ),

    exam: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me }) => ExamService.getExamById(id, me),
    ),

    examSession: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me }) => ExamService.getExamSessionById(id, me),
    ),
    examQuestionSerie: combineResolvers(
      isAuthenticated,
      async (parent, { id }, { models, me }) => ExamService.getExamQuestionSerieById(id, me),
    ),

    examSessionResult: combineResolvers(
      isAuthenticated,
      async (parent, params, { models, me }) => ExamService.getExamSessionResult(params, me),
    ),

    myExamsSessionsDone: combineResolvers(
      isAuthenticated,
      async (parent, params, { models, me }) => ExamService.getMyExamsDone(params, me),
    ),
  },


  Mutation: {

    createImportedMcqFromGenerator: combineResolvers(
      isAdmin,
      async (parent, { questionsIds, ueId }, {
        models,
        me,
      }) => await ExamService.createImportedMcqFromGenerator(questionsIds, ueId, me),
    ),
    createDuplicatedMcqFromGenerator: combineResolvers(
      isAdmin,
      async (parent, { questionsIds, ueId }, {
        models,
        me,
      }) => await ExamService.createDuplicatedMcqFromGenerator(questionsIds, ueId, me),
    ),

    /* CRUD EXAM */
    createExam: combineResolvers(
      isAdmin,
      async (parent, { input }, { models }) => await ExamService.createExam(input),
    ),
    updateExam: combineResolvers(
      isAdmin,
      async (parent, { id, input }, { models }) => await ExamService.updateExam(id, input),
    ),
    deleteExam: combineResolvers(
      isAdmin,
      async (parent, { id }, { models }) => await ExamService.deleteExam(id),
    ),


    /* CRUD EXAM Question series */
    createExamQuestionSeries: combineResolvers(
      isAdmin,
      async (parent, { input }, { models }) => await ExamService.createExamQuestionSeries(input),
    ),
    updateExamQuestionSeries: combineResolvers(
      isAdmin,
      async (parent, { id, input }, { models }) => await ExamService.updateExamQuestionSeries(id, input),
    ),
    deleteExamQuestionSeries: combineResolvers(
      isAdmin,
      async (parent, { id }, { models }) => await ExamService.deleteExamQuestionSeries(id),
    ),

    /* CRUD EXAM SCALE */
    createExamScale: combineResolvers(
      isAdmin,
      async (parent, { input }, { models }) => await ExamService.createExamScale(input),
    ),
    updateExamScale: combineResolvers(
      isAdmin,
      async (parent, { id, input }, { models }) => await ExamService.updateExamScale(id, input),
    ),
    deleteExamScale: combineResolvers(
      isAdmin,
      async (parent, { id }, { models }) => await ExamService.deleteExamScale(id),
    ),

    /* CRUD EXAM SESSION */
    createExamSession: combineResolvers(
      isAdmin,
      async (parent, { input }, { models }) => await ExamService.createExamSession(input),
    ),
    updateExamSession: combineResolvers(
      isAdmin,
      async (parent, { id, input }, { models }) => await ExamService.updateExamSession(id, input),
    ),
    deleteExamSession: combineResolvers(
      isAdmin,
      async (parent, { id }, { models }) => await ExamService.deleteExamSession(id),
    ),

    // SESSION GROUPS
    addGroupToExamSession: combineResolvers(
      isAdmin,
      async (parent, args, { models, me }) => await ExamService.addGroupToExamSession(args, me),
    ),
    removeGroupFromExamSession: combineResolvers(
      isAdmin,
      async (parent, args, { models, me }) => await ExamService.removeGroupFromExamSession(args, me),
    ),

    // Questions series scale
    addExamScaleToExamQuestionSerie: combineResolvers(
      isAdmin,
      async (parent, args, { models, me }) => await ExamService.addExamScaleToExamQuestionSerie(args, me),
    ),
    removeExamScaleFromExamQuestionSerie: combineResolvers(
      isAdmin,
      async (parent, args, { models, me }) => await ExamService.removeExamScaleFromExamQuestionSerie(args, me),
    ),
    updateExamScaleExamQuestionSerie: combineResolvers(
      isAdmin,
      async (parent, args, { models, me }) => await ExamService.updateExamScaleExamQuestionSerie(args, me),
    ),


    addTypeQcmToExam: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await ExamService.addTypeQcmToExam(args, ctx),
    ),
    removeTypeQcmFromExam: combineResolvers(
      isTuteurOrAdmin,
      async (parent, args, ctx) => await ExamService.removeTypeQcmFromExam(args, ctx),
    ),

    examsMassActions: combineResolvers(
      isAdmin,
      async (parent, args, ctx) => await ExamService.examsMassActions(args, ctx),
    ),

  },

  Exam: {
    examSessions: combineResolvers(
      isAuthenticated,
      async (exam, a, { models, me }) => await ExamService.getExamSessions(exam, me),
    ),
    examScales: combineResolvers(
      isAuthenticated,
      async (exam, a, { models, me }) => await ExamService.getExamScales(exam, me),
    ),

    types: async (exam, a, ctx) => {
      const examtypeqcm = await ctx.models.ExamTypeQcm.findAll({
        where: { examId: exam?.id },
        raw: true,
        attributes: ["typeQcmId"]
      });
      return ctx.models.TypeQcm.findAll({
        where: {
          id: examtypeqcm?.map((type) => type.typeQcmId),
        },
      });
    },
  },

  ExamQuestionSeries: {
    isAvailable: combineResolvers(
      isAuthenticated,
      async (examQuestionSerie, { userId }, { models, me }) => {
        if (examQuestionSerie?.date_begins !== null && examQuestionSerie?.date_end !== null) {
          const now = moment();
          const momentDateBegins = moment(examQuestionSerie?.date_begins);
          const momentDateEnds = moment(examQuestionSerie?.date_end);
          return (now.isAfter(momentDateBegins) && now.isBefore(momentDateEnds));
        } else {
          return true;
        }
      },
    ),

    qcmSession: combineResolvers(
      isAuthenticated,
      async (examQuestionSerie, { userId }, {
        models,
        me,
      }) => ExamService.getExamQuestionSerieQcmSession(examQuestionSerie, userId, me),
    ),

    qcm: combineResolvers(
      isAuthenticated,
      async (exam, a, { models, me }) => {
        return models.Qcm.findByPk(exam?.mcqId) || null;
      },
    ),

    userResults: combineResolvers(
      isTuteurOrAdmin,
      async (examQuestionSerie, a, {
        models,
        me,
      }) => ExamService.getUserResultsForQuestionSerie(examQuestionSerie, me),
    ),

    myStats: combineResolvers(
      isAuthenticated,
      async (examQuestionSerie, { sessionId, userId }, {
        models,
        me,
      }) => ExamService.getStatsForQuestionSerie(examQuestionSerie, me, sessionId, userId),
    ),

  },

  ExamSessionResult: {
    user: combineResolvers(
      isAuthenticated,
      async (examSessionResult, a, { models, me }) => {
        return models.User.findByPk(examSessionResult?.userId);
      },
    ),
  },

  ExamSession: {
    exam: combineResolvers(
      isAuthenticated,
      async (examSession, a, { models, me }) => examSession?.examId && ExamService.getExamById(examSession?.examId, me),
    ),

    examQuestionSeries: combineResolvers(
      isAuthenticated,
      async (examSession, a, { models, me }) => ExamService.getExamQuestionSeries(examSession, me),
    ),

    /* Final exam all user results */
    userResults: combineResolvers(
      isAuthenticated,
      async (examSession, args, {
        models,
        me,
      }) => ExamService.getUserResultsForSession(examSession, args?.examScaleId, me),
    ),

    userResult: combineResolvers(
      isAuthenticated,
      async (examSession, args, {
        models,
        me,
      }) => ExamService.getSingleUserResultForSession(examSession, args?.userId, me),
    ),

    finishedByMe: combineResolvers(isAuthenticated,
      async (examSession, args, {
        models,
        me,
      }) => {
        return ExamService.isExamSessionFinishedByMe(examSession, args, me)
      },
    ),

    datesDiffusion: combineResolvers(
      isAuthenticated,
      async (examSession, args, {
        models,
        me,
      }) => EdtService.getDateDiffusionForExamSessionAndUser(examSession?.id, me?.id),
    ),

  },

  ExamScale: {
    examQuestionSeries: combineResolvers(
      isAuthenticated,
      async (examScale, a, { models, me }) => {
        return ExamService.getExamQuestionSerieForExamScale(examScale, me);
      },
    ),
  },

};
