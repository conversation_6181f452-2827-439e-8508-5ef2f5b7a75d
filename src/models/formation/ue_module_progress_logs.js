/**
 * Track detail progress of UE modules for users.
 * WHERE blockId IS NULL => logs du module
 * WHERE blockId IS NOT NULL => logs d'un bloc de module (si cours avec étapes)
 *
 * @param sequelize
 * @param DataTypes
 * @returns {*}
 */
const ueModuleProgressLogs = (sequelize, DataTypes) => {
  const UeModuleProgressLogs = sequelize.define('ue_module_progress_logs', {

    seconds: { type: DataTypes.INTEGER, allowNull: false, defaultValue: 0 },
    completed: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
    logOperation: { type: DataTypes.STRING, allowNull: false, defaultValue: 'unknown' },

  }, { tableName: 'ue_module_progress_logs' });

  UeModuleProgressLogs.associate = models => {
    UeModuleProgressLogs.belongsTo(models.UE, { foreignKey: 'ueId' });
    UeModuleProgressLogs.belongsTo(models.UEModules, { foreignKey: 'ueModuleId' });
    UeModuleProgressLogs.belongsTo(models.User, { foreignKey: 'userId' });
    UeModuleProgressLogs.belongsTo(models.FormationBlock, { foreignKey: 'blockId' });
  };
  return UeModuleProgressLogs;
};

export default ueModuleProgressLogs;
