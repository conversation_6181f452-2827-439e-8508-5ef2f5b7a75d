'use strict'

const formationModuleElement = (sequelize, DataTypes) => {
  const FormationModuleElement = sequelize.define('formation_step', {
    name: { type: DataTypes.TEXT('long') },
    description: { type: DataTypes.TEXT('long') },

    image: { type: DataTypes.STRING, defaultValue: null },
    icon: { type: DataTypes.STRING, defaultValue: null },
    order: { type: DataTypes.INTEGER, defaultValue: null },

    isPublished: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    
  }, { tableName: 'formation_step' })
  
  FormationModuleElement.associate = models => {
    FormationModuleElement.belongsTo(models.FormationSection, { foreignKey: 'sectionId' })
    FormationModuleElement.belongsTo(models.User, { foreignKey: 'authorId' })
  }
  return FormationModuleElement
}

export default formationModuleElement
