'use strict'
// Catégories de cours / SOUS-UE
const UECategory = (sequecategorylize, DataTypes) => {
  const UECategory = sequecategorylize.define('uecategory', {
    name: { type: DataTypes.STRING },
    image: { type: DataTypes.STRING },
    description: { type: DataTypes.STRING, defaultValue: null },

    name_en: { type: DataTypes.STRING, defaultValue: null },
    name_it: { type: DataTypes.STRING, defaultValue: null },
    name_de: { type: DataTypes.STRING, defaultValue: null },
    name_es: { type: DataTypes.STRING, defaultValue: null },
    description_en: { type: DataTypes.TEXT, defaultValue: null },
    description_it: { type: DataTypes.TEXT },
    description_de: { type: DataTypes.TEXT },
    description_es: { type: DataTypes.TEXT },

    color: { type: DataTypes.STRING },
    isVisible: { type: DataTypes.BOOLEAN, defaultValue: true },
    order: { type: DataTypes.INTEGER, defaultValue: null },
    moyenneGenerale: { type: DataTypes.FLOAT },
  })

  UECategory.associate = models => {
    UECategory.hasMany(models.Cours, { as: 'cours' })
    UECategory.belongsTo(models.UE) // ueId
    // Parent category
    UECategory.hasOne(models.UECategory, { as: 'parent' });
    UECategory.belongsToMany(models.Groupe, { through: models.UECategoryGroups, foreignKey: 'uecategory_id' }) // addForumcategorygroups

  }

  return UECategory
}

export default UECategory
