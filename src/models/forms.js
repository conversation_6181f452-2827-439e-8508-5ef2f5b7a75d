import Sequelize from 'sequelize';

const forms = (sequelize, DataTypes) => {
    const Forms = sequelize.define('forms', {
        name: {type: DataTypes.STRING, defaultValue: null},
        name_en: {type: DataTypes.TEXT, defaultValue: null},
        name_it: {type: DataTypes.TEXT, defaultValue: null},
        name_de: {type: DataTypes.TEXT, defaultValue: null},
        name_es: {type: DataTypes.TEXT, defaultValue: null},
        uuid: {type: DataTypes.UUID, defaultValue: Sequelize.UUIDV4},
        color: {type: DataTypes.STRING, defaultValue: null},
        backgroundImage: {type: DataTypes.STRING, defaultValue: null},
        oneAnswerByUser: {type: DataTypes.BOOLEAN, defaultValue: false},
        isMandatory: {type: DataTypes.BOOLEAN, defaultValue: false},
    });

    Forms.associate = models => {
        Forms.belongsToMany(models.FormationElement, {
            through: models.FormationElementsForms,
            foreignKey: 'formId',
        });
        Forms.belongsToMany(models.Groupe, {
            through: models.FormMandatoryGroups,
            foreignKey: 'formId',
        });


    };
    return Forms;
};

export default forms;
