'use strict'

// Used in Comment system and Forum
const post = (sequelize, DataTypes) => {
  const Post = sequelize.define('post', {
    title: { type: DataTypes.TEXT('long') },
    text: { type: DataTypes.TEXT('long') },
    tag: { type: DataTypes.STRING },
    views: { type: DataTypes.INTEGER, defaultValue: 0 },
    likes: { type: DataTypes.INTEGER, defaultValue: 0 },
    isResolved: { type: DataTypes.BOOLEAN, defaultValue: false },
    ip: { type: DataTypes.STRING, defaultValue: 'null' },
    state: { type: DataTypes.STRING, defaultValue: null },

    isAskingForHumanHelp: { type: DataTypes.BOOLEAN, defaultValue: false },
    answeredByAi: { type: DataTypes.BOOLEAN, defaultValue: false },
    isResolvedByAi: { type: DataTypes.BOOLEAN, defaultValue: false },
  })

  Post.associate = models => {
    Post.belongsTo(models.User, { as: 'userForAiFeedback', foreignKey: 'userIdForAiFeedback' }) // userIdForAiFeedback

    Post.belongsTo(models.User,{as:'user',foreignKey:'userId'}) // userId
    Post.belongsTo(models.User, { as: 'verifiedByUser',foreignKey: 'verifiedBy' }) // verifiedBy

    Post.belongsTo(models.Cours) // coursId?
    Post.belongsTo(models.Qcm) // qcmIdQcmDAadnozadnazd?
    Post.belongsTo(models.Forum) // forumId

    Post.belongsTo(models.QuestionAnswers, { foreignKey: 'answerId' }) // answerId
    Post.belongsTo(models.Question, { foreignKey: 'questionId' }) // questionId
    Post.belongsTo(models.PostType, { foreignKey: 'postTypeId' }) // postTypeId

    Post.belongsTo(models.Event, { foreignKey: 'eventId' })

    // Forum (parent)
    // Topic (in forum)
    Post.hasOne(models.File, { as: 'file' })
    Post.hasOne(models.File, { as: 'fileImage' })
    Post.hasOne(models.Post, { as: 'parent' }) // parentId

    Post.hasMany(models.LikeHistory, { foreignKey: 'postId' })
  }

  return Post
}

export default post
