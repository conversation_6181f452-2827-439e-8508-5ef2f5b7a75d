'use strict';

export const PaymentTypes = {
    once: 'once',
    installment: 'installment',
};

export const PaymentStates = {
    paid: 'paid',
    unpaid: 'unpaid', // Pending / Unpaid yet
    canceled: 'canceled',
};

const payments = (sequelize, DataTypes) => {
    const Payments = sequelize.define('payments', {
        state: { type: DataTypes.STRING },
        sum: { type: DataTypes.FLOAT },
        paymentType: { type: DataTypes.STRING },
        isNewUser: { type: DataTypes.BOOLEAN },
        stripeSubscriptionId: { type: DataTypes.STRING },
        stripePriceId: { type: DataTypes.STRING, defaultValue: null },

        numberOfMensuality: { type: DataTypes.INTEGER, defaultValue: 1 },
        totalMensuality: { type: DataTypes.INTEGER },


    }, { tableName: 'payments' });

    Payments.associate = models => {
        Payments.belongsToMany(models.Forfait, { through: models.PaymentsForfaits });
        Payments.belongsTo(models.User, { foreignKey: 'userId' });
        Payments.belongsTo(models.Log, { foreignKey: 'logId' });
        Payments.belongsTo(models.Config, { foreignKey: 'paymentMethodId' });
    };

    return Payments;
};

export default payments;
