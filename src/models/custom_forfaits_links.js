'use strict'

const customForfaitsLinks = (sequelize, DataTypes) => {
  const CustomForfaitsLinks = sequelize.define('custom_forfaits_links', {
    name: { type: DataTypes.TEXT, allowNull: null },
    link: { type: DataTypes.TEXT, validate: { notEmpty: true } },
    isPublished: { type: DataTypes.BOOLEAN, defaultValue: false },
  }, { tableName: 'custom_forfaits_links' })

  CustomForfaitsLinks.associate = models => {
    CustomForfaitsLinks.belongsToMany(models.Forfait, {
      through: models.CustomForfaitsLinksForfaits,
      foreignKey: 'customForfaitLinkId',
    })
  }

  return CustomForfaitsLinks
}

export default customForfaitsLinks
