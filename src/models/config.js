'use strict';

import LogService from '../graph-api/log/log-service.js'
import models from '../models';
import { LOG_OPERATIONS } from './log/log.js'

// TODO it's old, share config keys with frontend ?
export const CONFIG_KEYS = {
  WEBSITE_BASE_URL: 'WEBSITE_BASE_URL',
  WEBSITE_NAME: 'WEBSITE_NAME',
  WEBSITE_LOGO_FILE: 'logo2.png',
  ANNONCE_GENERALE: 'ANNONCE_GENERALE',
  MAINTENANCE_MODE: 'MAINTENANCE_MODE',
  TWITTER_ACCOUNT: 'TWITTER_ACCOUNT',
  FACEBOOK_ACCOUNT: 'FACEBOOK_ACCOUNT',
  USER_CONNECTED_TO_ADD: 'USER_CONNECTED_TO_ADD',
  USER_TOTAL_TO_ADD: 'USER_TOTAL_TO_ADD',
  USER_QCM_STATS_TO_ADD: 'USER_QCM_STATS_TO_ADD',
  PLANNING_FEATURE_ACTIVATED: 'PLANNING_FEATURE_ACTIVATED',
  MCQ_CONFIG: 'MCQ',
  DEFAULT_PAYMENT_SYSTEM: 'DEFAULT_PAYMENT_SYSTEM', // monetico or stripe
  CUSTOM_MOBILE_CONFIG: 'CUSTOM_MOBILE_CONFIG',

  PAYMENT_METHOD: 'PAYMENT_METHOD',

  DEFAULT_AVATARS: 'DEFAULT_AVATARS',

  HUBSPOT_INTEGRATION: 'HUBSPOT_INTEGRATION',
  CHAT_GPT_INTEGRATION: 'CHAT_GPT_INTEGRATION',

  LOGO_PRINT: 'LOGO_PRINT',
  PRIMARY_COLOR: 'PRIMARY_COLOR',
  SECONDARY_COLOR: 'SECONDARY_COLOR',

  /* STRIPE RELATED */
  STRIPE_PK: 'STRIPE_PK',
  STRIPE_SK: 'STRIPE_SK',
  STRIPE_HOOK_SECRET: 'STRIPE_HOOK_SECRET',

  CHAT_GPT_SETTINGS: 'CHAT_GPT_SETTINGS',
  CHAT_GPT_QCM_SETTINGS:'CHAT_GPT_QCM_SETTINGS',
  CHAT_GPT_API_KEY: 'CHAT_GPT_API_KEY',

  REGISTER_PAGE_SELECTED_FORFAITS: 'REGISTER_PAGE_SELECTED_FORFAITS',

  /* Notions synchronisation */
  NOTIONS_SYNCHRONISATION_ENDPOINT: 'NOTIONS_SYNCHRONISATION_ENDPOINT',

  LINK_TO_SALES_SITE: 'LINK_TO_SALES_SITE',
  REGISTERING_OPEN_ON_MOBILE_APP: 'REGISTERING_OPEN_ON_MOBILE_APP',
  COMPANY_INFORMATION: 'COMPANY_INFORMATION',
  ENABLED_LANGUAGES_FOR_CONTENT: 'ENABLED_LANGUAGES_FOR_CONTENT',
  CGV: 'CGV',
  APPEARANCE: 'APPEARANCE',

  MATHPIX_INTEGRATION:'MATHPIX_INTEGRATION'
};

/* Default MCQ_CONFIG */
export const MCQ_CONFIG_JSON = {
  defaultAnswersNumber: 5, // Nombre réponses par défaut
  defaultSecondsPerQuestion: 90, // sec par réponse par défaut
  minimumCharsPerAnswer: 0, // Nb min char réponse
  minimumCharPerAnswerExplanation: 0, // Nb min char explication
};

const config = (sequelize, DataTypes) => {
  const Config = sequelize.define('config', {
      key: { type: DataTypes.STRING },
      value: { type: DataTypes.TEXT('long') },
      domain: { type: DataTypes.STRING, defaultValue: null },
    },{
      hooks:{
       // Hook afterUpdate qui va mettre à jour la table 'user_company_information' pour modifier le 'companyName' lorsque cette table est update
        afterUpdate:async(configInstance,options)=>{
          try {

            // si la key que l'on change de la config est 'COMPANY_INFORMATION'
            if (configInstance?.get('key')===CONFIG_KEYS.COMPANY_INFORMATION){

              // Alors on parse les deux JSON d'information et on récupère le 'commercialName'
              const newName=JSON.parse(configInstance?.get('value'))?.["commercialName"] || null
              const previousName=JSON.parse(configInstance.previous('value'))?.["commercialName"] || null
              const currentId=configInstance?.get('id')

              // Si les noms sont différents, alors on update la table 'user_company_information' sur le nom de company, pour les éléments qui sont liés à cet id
              if (newName !== previousName){

                await LogService.logAction({
                  logOperation:LOG_OPERATIONS.User.CompanyNameChange.action,
                  models,
                  logData:{
                    companyId:currentId,
                    previousCompanyName:previousName,
                    newCompanyName:newName,
                  },
                })

                await models.UserCompanyInformations.update(
                  {'companyName':newName},
                  {where : {companyInformationId:currentId}}
                )
              }
            }

            if (configInstance?.get('key')===CONFIG_KEYS.CHAT_GPT_INTEGRATION){
              // Alors on parse les deux JSON d'information et on récupère le 'chatGPT Name' pour récupérer le nom précédent et le nom après
              // Alors on parse les deux JSON d'information et on récupère le 'commercialName'
              const newName=JSON.parse(configInstance?.get('value'))?.["name"] || null
              const previousName=JSON.parse(configInstance.previous('value'))?.["name"] || null
              const currentId=configInstance?.get('id')

              if (newName !== previousName) {
                await LogService.logAction({
                  logOperation: LOG_OPERATIONS.ChatGPT.updateChatGPTIntegration.action,
                  models,
                  logData: {
                    chatGptCOnfigId: currentId,
                    previousChatGptName: previousName,
                    newChatGptName: newName,
                  },
                })

                await models.UserGroupChatGpt.update(
                  { 'chatGptName': newName },
                  { where: { chatGptId: currentId } }
                )
              }

            }

         }catch(e){
            console.error(`WARNING : If this warning is triggered, it means that the 'afterUpdate Hook' in the config model definition is not working. 
            The batch update is not supported by the hook so try to add 'individualHooks: true' to the update function. 
            This hook is essential to the functioning  of the table 'user-company-information' and 'user_groupe_chatgpts' because it updates the name field in the table`)
            console.error("error :",e)
         }
       }
      }
    }
  );

  Config.associate = models => {
    Config.hasMany(models.Mathpix,{ // Une configMathpix a beaucoup de pdf
      foreignKey:'mathpixConfigId',
      onUpdate:'CASCADE',
      onDelete:'SET NULL' // Lorsque la config est supprimée, la mathpixConfigId Devient null dans la table mathpix
    })
  }

  return Config;
};

export default config;
