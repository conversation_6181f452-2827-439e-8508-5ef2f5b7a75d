// Properties folders for all users, can be created by admins

const userPropertiesFolders = (sequelize, DataTypes) => {
  const UserPropertiesFolders = sequelize.define('user_properties_folders', {

    name: { type: DataTypes.STRING, defaultValue: null },
    name_en: { type: DataTypes.STRING, defaultValue: null },
    name_es: { type: DataTypes.STRING, defaultValue: null },
    name_de: { type: DataTypes.STRING, defaultValue: null },
    name_it: { type: DataTypes.STRING, defaultValue: null },
      
  });

  /*
  UserPropertiesFolders.associate = models => {

  };
  */
  
  return UserPropertiesFolders;
};

export default userPropertiesFolders;
