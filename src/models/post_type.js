'use strict';

// Used in Comment system and Forum
const postType = (sequelize, DataTypes) => {
  const PostType = sequelize.define('post_type', {
    name: { type: DataTypes.STRING },
    image: { type: DataTypes.STRING },
    type: { type: DataTypes.STRING, defaultValue: null },

    // Chat GPT settings
    precisionPromptForChatGPT: { type: DataTypes.STRING },
    firstAnswerByChatGPT: { type: DataTypes.BOOLEAN, defaultValue: false },
    otherAnswersByChatGPT: { type: DataTypes.BOOLEAN, defaultValue: false },
  }, { tableName: 'post_type' });

  PostType.associate = models => {
    PostType.hasMany(models.Post, { foreignKey: 'postTypeId' }); // userId
  };

  return PostType;
};

export default postType;
