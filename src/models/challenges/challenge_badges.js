'use strict'

const challengeBadges = (sequelize, DataTypes) => {
  const ChallengeBadges = sequelize.define('challenge_badges', {
    name: { type: DataTypes.STRING },
    type: { type: DataTypes.STRING },
    image: { type: DataTypes.STRING },
    description: { type: DataTypes.TEXT },
    file: { type: DataTypes.TEXT },

  }, { tableName: 'challenge_badges' })


  ChallengeBadges.associate = models => {
    ChallengeBadges.belongsTo(models.Challenge, { foreignKey: 'challengeId' })
  }

  return ChallengeBadges
}

export default challengeBadges