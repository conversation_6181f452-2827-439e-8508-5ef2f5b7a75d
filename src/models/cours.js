'use strict'

export const CoursLayout = { pdf: 'pdf', video: 'video' }

const cours = (sequelize, DataTypes) => {
  const Cours = sequelize.define('cours', {
    name: { type: DataTypes.STRING },
    text: { type: DataTypes.TEXT },

    name_en: { type: DataTypes.STRING, defaultValue: null },
    name_it: { type: DataTypes.STRING, defaultValue: null },
    name_de: { type: DataTypes.STRING, defaultValue: null },
    name_es: { type: DataTypes.STRING, defaultValue: null },
    text_en: { type: DataTypes.STRING },
    text_it: { type: DataTypes.STRING },
    text_de: { type: DataTypes.STRING },
    text_es: { type: DataTypes.STRING },
    gptPrecisionPrompt: { type: DataTypes.TEXT, defaultValue: null },

    type: { type: DataTypes.STRING },
    pdf: { type: DataTypes.STRING },
    numberOfPagesPdf: { type: DataTypes.INTEGER },
    video: { type: DataTypes.TEXT, defaultValue: null },
    videoType: { type: DataTypes.STRING, defaultValue: null },
    videoPlayerSettings: {
      type: DataTypes.JSON, defaultValue: null,
      set(value) {
        this.setDataValue('videoPlayerSettings', JSON.stringify(value))
      },
      get() {
        try {
          const rawValue = this.getDataValue('videoPlayerSettings')
          return rawValue ? JSON.parse(rawValue) : {}
        } catch (e) {
          return {}
        }
      },
    },
    layout: { type: DataTypes.STRING, defaultValue: 'pdf' }, // "pdf" : support à la une PDF, "video": support à la une vidéo, "formation" : Cours enrichi (pas de support à la une), "steps" : Cours pas à pas
    epub: { type: DataTypes.STRING },
    preferredDisplayType: { type: DataTypes.STRING, defaultValue: 'pdf' }, // pdf ou epub
    isAnnale: { type: DataTypes.BOOLEAN, defaultValue: false },
    difficulty: { type: DataTypes.FLOAT },
    workTime: { type: DataTypes.STRING },
    updateInfos: { type: DataTypes.TEXT },
    tips: { type: DataTypes.TEXT('long') },
    usefulLinks: { type: DataTypes.TEXT },
    views: { type: DataTypes.INTEGER, defaultValue: 0 },
    version: { type: DataTypes.STRING, defaultValue: '1' },
    customImage: { type: DataTypes.TEXT, defaultValue: null },
    isEnAvant: { type: DataTypes.BOOLEAN, defaultValue: false },
    date: { type: DataTypes.DATE, defaultValue: null },
    deleted: { type: DataTypes.BOOLEAN, defaultValue: false },
    duration: { type: DataTypes.INTEGER, defaultValue: null },
    order: { type: DataTypes.INTEGER, defaultValue: null },
    isVisible: { type: DataTypes.BOOLEAN, defaultValue: true },
    fileUpdateDate: { type: DataTypes.DATE, defaultValue: null },
    watermarkPicturePath:{type:DataTypes.STRING,defaultValue:"",allowNull:false}, // la value est soit un path, soit une empty string //
    pdfPreviews: {
      type: DataTypes.JSON,
      defaultValue: null,
      set(value) {
        this.setDataValue('pdfPreviews', JSON.stringify(value))
      },
      get() {
        try {
          const rawValue = this.getDataValue('pdfPreviews')
          return rawValue ? JSON.parse(rawValue) : []
        } catch (e) {
          return []
        }
      },
    },
    settings: {
      type: DataTypes.JSON,
      defaultValue: {},
      set(value) {
        this.setDataValue('settings', JSON.stringify(value))
      },
      get() {
        try {
          const rawValue = this.getDataValue('settings')
          return rawValue ? JSON.parse(rawValue) : {}
        } catch (e) {
          return {}
        }
      },
    },
    isReviewEnabled:{type:DataTypes.BOOLEAN,defaultValue:false,allowNull:false},
    isFeedbackVisible:{type:DataTypes.BOOLEAN,defaultValue:false,allowNull:false},
  })

  Cours.associate = models => {
    Cours.hasOne(models.Edt)
    Cours.belongsTo(models.User, { as: 'author' })
    // Parent UECategory
    Cours.belongsTo(models.UECategory)
    // Parent UE
    Cours.belongsTo(models.UE, { foreignKey: 'ueId' })
    // Course dates
    Cours.hasMany(models.DateDiffusion)
    Cours.hasMany(models.File)

    Cours.hasMany(models.BaseRevision, {as: 'baseRevision', foreignKey: 'courId'})
    // TODO delete legacy
    Cours.hasMany(models.Fiche)

    Cours.hasMany(models.CoursSupport, { foreignKey: 'coursId' })
    Cours.hasMany(models.FormationBlock, { foreignKey: 'coursId' })

    // TODO delete legacy
    Cours.belongsToMany(models.Qcm, { through: models.CoursQcm }) // addCoursQcm
    // Course linked notions (auto-generated on PDF file upload)
    Cours.belongsToMany(models.Notion, { through: models.NotionCours, foreignKey: 'coursId' })
    // Permissions
    Cours.belongsToMany(models.Groupe, { through: models.CoursGroups, foreignKey: 'coursId' })
    // Linked exercises/questions
    Cours.belongsToMany(models.Question, { through: models.QuestionCours, foreignKey: 'coursId' }) // Question linked to cours

    // Linked schemas
    Cours.belongsToMany(models.SchemaLibrary, { through: models.SchemaLibraryCours, foreignKey: 'coursId' })

    Cours.belongsToMany(models.Event, { through: models.EventCours, foreignKey: 'coursId' });

    Cours.belongsTo(models.Formation, { foreignKey: 'formationId' }) // Formation
    Cours.belongsTo(models.Cours, { foreignKey: 'targetCoursId', as: 'targetCours' }) // Imported courses

    Cours.hasMany(models.Review,{foreignKey:'coursId'})
    Cours.hasMany(models.AverageHistory,{foreignKey:'coursId'})
  }

  return Cours
}

export default cours
