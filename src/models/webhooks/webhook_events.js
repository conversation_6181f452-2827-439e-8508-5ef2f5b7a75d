'use strict';

const WebhookEvents = (sequelize, DataTypes) => {
  const webhookevents = sequelize.define('webhook_events', {
    event: {
      type: DataTypes.STRING, // Nom de l'événement (ex. "user.created")
      allowNull: false,
    },
    payload: {
      type: DataTypes.JSON, // Données de l'événement
      allowNull: false,
    },
    status: {
      type: DataTypes.STRING, // Statut de l'événement (ex. "pending", "processed", "failed")
      allowNull: false,
      defaultValue: 'pending',
    },
    errors: {
      type: DataTypes.INTEGER, // Nombre d'erreurs lors de l'envoi de l'événement
      allowNull: false,
      defaultValue: 0,
    }

  }, {
    tableName: 'webhook_events',
  });

  webhookevents.associate = models => {
    //webhooks.belongsTo(models.User, { foreignKey: 'userId' });
  };

  return webhookevents;
};

export default WebhookEvents;
