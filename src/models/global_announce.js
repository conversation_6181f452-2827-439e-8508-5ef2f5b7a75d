'use strict'

const GlobalAnnounce = (sequelize, DataTypes) => {
  const GlobalAnnounce = sequelize.define('global_announce', {
    text: { type: DataTypes.TEXT('long') },

    text_en: { type: DataTypes.TEXT('long') },
    text_it: { type: DataTypes.TEXT('long') },
    text_de: { type: DataTypes.TEXT('long') },
    text_es: { type: DataTypes.TEXT('long') },

  }, {
    tableName: 'global_announce',
  })

  GlobalAnnounce.associate = models => {
    GlobalAnnounce.belongsToMany(models.Groupe, {
      through: models.GlobalAnnounceGroups,
      foreignKey: 'globalAnnounceId',
    })
  }

  return GlobalAnnounce
}

export default GlobalAnnounce
