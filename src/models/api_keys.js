'use strict';

const apiKeys = (sequelize, DataTypes) => {
  const ApiKeys = sequelize.define('api_keys', {
    key: { type: DataTypes.STRING, allowNull: false },
    name: { type: DataTypes.STRING, allowNull: false },
    description: { type: DataTypes.TEXT, allowNull: true },
    permissions: { type: DataTypes.JSON, allowNull: false },
    status: { type: DataTypes.STRING(20), allowNull: false, defaultValue: 'active' }

  }, {
    tableName: 'api_keys'
  });

  ApiKeys.associate = models => {
    ApiKeys.belongsTo(models.User, { foreignKey: 'userId' });
  };

  return ApiKeys;
};

export default apiKeys;
