/* TODO make associated migration, and move to shared package ? */

export const LOG_OPERATIONS = {

  Post: {
    Create: {
      action: 'post_create',
      text: '<PERSON>cris un post/commentaire',
    },
    Update: {
      action: 'post_update',
      text: 'Met à jour un post',
    },
    Delete: {
      action: 'post_delete',
      text: 'Supprime un Post',
    },
    CreateLimiteRule:{
      action:'post_limite_rule_create',
      text:'Créé une limitation de commentaire',
    },
    UpdateLimiteRule:{
      action:'post_limite_rule_update',
      text:"Mise à jour d'une limitation de commentaire",
    },
    DeleteLimiteRule:{
      action:'post_limite_rule_delete',
      text:"Supression d'une limitation de commentaire"
    }
  },

  Forfait: {
    BeforePayment: {
      action: 'BeforePayment',
      text: 'Choisi un forfait',
    },
  },

  /* ADMIN LOGS */
  Qcm: {
    Create: {
      action: 'CreateMcq',
      text: '<PERSON><PERSON><PERSON> QCM',
      object_id: 'id_qcm',
    },
    Update: {
      action: 'UpdateMcq',
      text: 'Met à jour un QCM',
      object_id: 'id_qcm',
    },
    Finish: {
      action: 'FinishMcq',
      text: 'Termine un QCM',
      object_id: 'id_qcm',
    },
    Delete: {
      action: 'DeleteMcq',
      text: 'Supprime un QCM',
      object_id: 'id_qcm',
    },
    Restore: {
      action: 'RestoreMcq',
      text: 'Restaure un QCM',
      object_id: 'id_qcm',
    },
    PublishMcq: {
      action: 'PublishMcq',
      text: 'Publie un QCM',
      object_id: 'id_qcm',
    },
    UnPublishMcq: {
      action: 'UnPublishMcq',
      text: 'Dépublie un QCM',
      object_id: 'id_qcm',
    },
    ModifyAuthor:{
      action:'ModifyAuthor',
      text:"Change l'auteur du qcm",
      object_id:'id_qcm',
    }
  },

  Question: {
    Create: {
      action: 'CreateQuestion',
      text: 'Créé une question de QCM',
      object_id: 'id_question',
    },
    Update: {
      action: 'UpdateQuestion',
      text: 'Met à jour une question de QCM',
      object_id: 'id_question',
    },
    Delete: {
      action: 'DeleteQuestion',
      text: 'Supprime une question de QCM',
      object_id: 'id_question',
    },
    ChangeScale:{
      action:"ChangeScale",
      text:"Modifie le barème",
      object_id:"id_question"
    }
  },

  QuestionAnswer: {
    Create: {
      action: 'CreateQuestionAnswer',
      text: 'Créé un item de QCM',
    },
    Update: {
      action: 'UpdateQuestionAnswer',
      text: 'Met à jour un item de QCM',
    },
    Delete: {
      action: 'DeleteQuestionAnswer',
      text: 'Supprime un item de QCM',
    },
  },

  Exam: {
    Create: {
      action: 'CreateExam',
    },
    Update: {
      action: 'UpdateExam',
    },
    Delete: {
      action: 'DeleteExam',
    },

    FinishQuestionSerie: {
      action: 'FinishQuestionSerie',
    },
    FinishExam: {
      action: 'FinishExam',
    },
  },

  Groupe: {
    Create: {
      action: 'CreateGroup',
      text: 'Créer un nouveau groupe',
      object_id: 'id',
    },
    Update: {
      action: 'UpdateGroup',
      text: 'Met un groupe à jour',
      object_id: 'id',
    },
    Delete: {
      action: 'DeleteGroup',
      text: 'Supprime un groupe',
      object_id: 'id',
    },
    AddGroupPermission: {
      action: 'AddGroupPermission',
      text: 'Ajoute un droit à un groupe',
      object_id: 'id',
    },
    RemoveGroupPermission: {
      action: 'RemoveGroupPermission',
      text: 'Enlève un droit à un groupe',
      object_id: 'id',
    },
  },

  User: {
    /* ADMIN RELATED */
    Create: {
      action: 'CreateUser',
      text: 'Créé un utilisateur',
      object_id: 'id',
    },
    Update: {
      action: 'UpdateUser',
      text: 'Modifie les infos d\'un utilisateur',
      object_id: 'id',
    },
    AddOneCreditIAP: {
      action: 'AddOneCreditIAP',
      text: 'Ajout un crédit IAP',
      object_id: 'id',
    },
    Delete: {
      action: 'DeleteUser',
      text: 'Supprime utilisateur',
      object_id: 'id',
    },

    ChangeRoles: {
      action: 'ChangeUserRoles',
      text: 'Change le rôle d\'un utilisateur',
      object_id: 'id',
    },
    ChangeGroups: {
      action: 'ChangeUserGroups',
      text: 'Change le(s) groupe d\'un utilisateur',
      object_id: 'id',
    },

    AddGroupToUser: {
      action: 'AddUserGroup',
      text: 'Ajoute un utilisateur au groupe',
      object_id: 'id',
    },
    RemoveGroupFromUser: {
      action: 'RemoveUserGroup',
      text: 'Supprime un utilisateur de groupe',
      object_id: 'id',
    },

    Login: {
      action: 'UserLogin',
      text: 'Utilisateur s\est connecté avec succès',
      object_id: 'id',
    },

    Logout: {
      action: 'UserLogout',
      text: 'Déconnexion',
      object_id: 'id',
    },

    LoginFail: {
      action: 'UserLoginFail',
      text: 'Utilisateur a entré un mauvais mot de passe',
      object_id: 'id',
    },

    AdminLoginFail: {
      action: 'AdminUserLoginFail',
      text: 'Administrateur a entré un mauvais mot de passe',
      object_id: 'id',
    },

    CreatedAfterPayment: {
      action: 'CreatedAfterPayment',
      text: 'Utilisateur créé après paiement',
      object_id: 'id',
    },
    UpdatedAfterPayment: {
      action: 'UpdatedAfterPayment',
      text: 'Utilisateur mis à jour après paiement',
      object_id: 'id',
    },
    AcceptCgu:{
      action:'HasAcceptedCgu',
      text:"Utilisateur a accepté les CGU",
      object_id:'id'
    },
    RejectCgu:{
      action:'HasRejectedCgu',
      text:"Utilisateur a refusé les CGU",
      object_id:'id'
    },
    AutomaticUncheckCguAfterVersionUpdate:{
      action:'AutomaticCguUncheckFromVersionUpdate',
      text:"Update de la version des CGU, réinitialisation du consentement ",
      object_id:'id'
    },
    AddedToCompany:{
      action:'HasBeenAddedToCompany',
      text:"Utilisateur a été lié à une entreprise",
      object_id:'id',
    },
    RemovedFromCompany:{
      action:'HasBeenRemovedFromCompany',
      text:"Utilisateur a été retiré d'une entreprise",
      object_id:'id'
    },
    CompanyNameChange:{
      action:'CompanyNameHasBeenChanged',
      text:"le nom de l'entreprise a été modifié",
      object_id:'id'
    }
  },

  Report: {
    Content: {
      action: 'ReportContent',
      text: 'Signale un contenu',
      object_id: 'id',
    },
    User: {
      action: 'ReportUser',
      text: 'Signale un utilisateur',
      object_id: 'id',
    },
  },

  DiffusionDate: {
    Create: {
      action: 'CreateDiffusionDate',
    },
    AddGroupToDiffusionDate: {
      action: 'AddGroupToDiffusionDate',
    },
    DeleteGroupFromDiffusionDate: {
      action: 'DeleteGroupFromDiffusionDate',
    },
    Update: {
      action: 'UpdateDiffusionDate',
    },
    Delete: {
      action: 'DeleteDiffusionDate',
    },
  },

  Scale:{
    Create:{
      action:'CreateScale',
      text:"Création d'un barème",
      object_id:"id"
    },
    Update:{
      action:'UpdateScale',
      text:"Update d'un barème",
      object_id:"id"
    },
    Delete:{
      action:"DeleteScale",
      text:"Suppression d'un barème",
      object_id:"id"
    },
    LinkUeToScale:{
      action:"LinkUeToScale",
      text:"Ajout d'une Ue au barème",
      object_id:"id"
    },
    RemoveUeFromScale:{
      action:"RemoveUeFromScale",
      text:"Retrait d'une Ue du barème",
      object_id:"id"
    },
    RemoveAllUeFromScale:{
      action:"RemoveAllUeFromScale",
      text:"Retrait de toutes les Ue du barème",
      object_id:"id"
    },
    LinkExerciseToScale:{
      action:"LinkExerciseToScale",
      text:"Ajout d'un exercice au barème",
      object_id:"id"
    },
    RemoveExerciseFromScale:{
      action:"RemoveExerciseFromScale",
      text:"Retrait d'un exercice du barème",
      object_id:"id"
    },
    RemoveIsDefaultFromScale:{
      action:"RemoveIsDefaultFromScale",
      text:"Retrait de cette scale comme 'par défaut'",
      object_id:"id"
    },
    AddIsDefaultToScale:{
      action:"AddIsDefaultToScale",
      text:"Définition de cette scale comme 'par défaut'",
      object_id:"id"
    }
  },

  Cours: {
    Create: {
      action: 'CreateClass',
      text: 'Créer un nouveau cours',
      object_id: 'id',
    },
    Update: {
      action: 'EditClass',
      text: 'Met un cours à jour',
      object_id: 'id',
    },
    Delete: {
      action: 'DeleteClass',
      text: 'Supprime un cours',
      object_id: 'id',
    },
    Seen: {
      action: 'SeenClass',
      text: 'Accède à un cours',
      object_id: 'id',
    },
    Download: {
      action: 'DownloadCours',
      text: 'Télécharge un cours',
      object_id: 'id',
    },
    UpdateWatermark: {
      action: 'EditClassWatermark',
      text: 'Modifie les options de filigrane',
      object_id: 'id',
    },
    UpdateWatermarkPicture: {
      action: 'EditClassWatermarkPicture',
      text: 'Modifie l\'image de watermark',
      object_id: 'id',
    },
  },
  Review:{
    Create:{
      action:'CreateReview',
      text:'Créé une review',
      object_id:'id'
    },
    Update:{
      action:'UpdateReview',
      text:'Update une review',
      object_id:'id'
    },
    Delete:{
      action:'DeleteReview',
      text:'Supprime une review',
      object_id:'id'
    }
  },
  Element:{
    UpdateWatermark:{
      action:'EditFileElementWatermark',
      text:'Modifie les options de filigrane pour un élément file (pdf)',
      object_id:'id'
    },

    UpdateWatermarkPicture:{
      action:'EditElementWatermarkPicture',
      text:'Modifie l\'image de template pour un élément file (pdf)'
    }
  },
  Watermark:{
    CreateWatermark:{
      action:'CreateWatermarkDatabase',
      text:"Création d'une template en database"
    },
    UpdateWatermark:{
      action:'UpdateWatermarkDatabase',
      text:"Update d'une template en database"
    },
    DeleteWatermark:{
      action:'DeleteWatermarkDatabase',
      text:"Supression d'une template en database"
    },
    UpdateWatermarkPicture:{
      action:"EditWatermarkTemplatePicture",
      text:"Modifie l'image de template pour un template de template"
    },
  },
  ChatGPT: {
    updateChatGPTIntegration: {
      action: "UpdateChatGPTIntegration",
      text: "Mise à jour de l'intégration des chatGPT"
    },
    QcmQuery:{
      action:"QueryChatGptForQcmEnhance",
      text:"Utiliseration de chatGPT pour amélioration de QCM"
    },
    BatchQuestionCreation:{
      action:"CreateBatchQuestionsWithAi",
      text:"Création de questions par AI"
    },
    BatchQuestionImportation:{
      action:"ImportBatchQuestionsWithAi",
      text:"Importation de questions par AI"
    }
  },
  Mathpix:{
    AddMathpixConfigPermissions:{
      action:"AddGroupToMathpixConfigPermissions",
      text:"Ajout d'un groupe aux permissions Mathpix"
    },
    RemoveMathpixConfigPermissions: {
      action:"RemoveGroupToMathpixConfigPermissions",
      text:"Retrait d'un groupe aux permissions Mathpix"
    },
    DeleteMathpixie:{
      action:"DeleteMathpixie",
      text:"Essai de suppression d'une Mathpixie"
    },
    SentFileToMathpix:{
      action:"SentFileToMathpix",
      text:"l'user a envoyé une file à Mathpix"
    }
  },
  S3:{
    CreateS3FileUploadLinkMultipart:{
      action:"createUploadLinkMultipart",
      text:"Création d'un lien présigné pour upload multipart"
    },
    ConfirmS3UploadMultipart:{
      action:"confirmS3UploadMultipart",
      text:"Confirmation du back et d'AWS de la création du fichier sur AWS",
    },
    CreateS3FileUploadLinkMonopart:{
      action:"createUploadLinkMonopart",
      text:"Création d'un lien présigné pour upload monopart"
    },
    UploadFileConfirmation:{
      action:"UploadConfirmation",
      text:"Confirmation de l'upload"
    },
    UploadFile:{
      action:"UploadS3File",
      text:"Upload d'un fichier au cloud S3"
    },
    DeleteFile:{
      action:"DeleteS3File",
      text:"Suppression d'un fichier du cloud S3"
    },
    CreateS3RessourceLink:{
      action:"CreateRessourceLink",
      text:"Creation d'une URL pour accéder à une ressource S3"
    },
    CreateS3DownloadLink:{
      action:"CreateS3DownloadLink",
      text:"Creation d'une URL pour télécharger une ressource S3"
    }
  },
  Scorm:{
    UploadPackage:{
      action:"UploadPackage",
      text:"l'user a upload un package"
    },
    LinkScormToFormationElement:{
      action:'LinkScormAndFormationElement',
      text :'Ajout du package SCORM à un formationElement'
    },
    DeleteScormPackageFromFormationElement:{
      action:'DeleteScormFromFormationElement',
      text:"Suppression d'un scorm package linké à un formation élement."
    },
    LogScormDataSubmit:{
      action :'LogScromData',
      text:"Save des données SCORM"
    }
  }
};

const Log = (sequelize, DataTypes) => {
  const log = sequelize.define('log', {
    //model: { type: DataTypes.TEXT }, // can be retrieved by model.constructor.getTableName()
    logOperation: { type: DataTypes.STRING }, // CRUD or custom
    ip: { type: DataTypes.STRING },
    userAgent: { type: DataTypes.STRING },
    logData: {  // Optional additionnal data
      type: DataTypes.JSON, defaultValue: null,
      get() {
        try {
          const rawValue = this.getDataValue('logData');
          return rawValue ? JSON.parse(rawValue) : {};
        } catch (e) {
          return {};
        }
      },
      set(value) {
        this.setDataValue('logData', JSON.stringify(value));
      },
    },
  });

  log.associate = models => {
    log.belongsTo(models.User, { as: 'user', foreignKey: 'userId' }); // userId // { foreignKey: 'userId' })
    log.belongsTo(models.User, { as: 'reportee', foreignKey: 'reporteeUserId' }); // userId // { foreignKey: 'userId' })

    /* MCQ RELATED */
    log.belongsTo(models.Qcm, { foreignKey: 'qcmId' }); // qcmId
    log.belongsTo(models.Question, { foreignKey: 'questionId' }); // questionId
    log.belongsTo(models.QuestionAnswers, { foreignKey: 'answerId' }); // answerId
    log.belongsTo(models.Cours, { foreignKey: 'coursId' }); // coursId

    /* PAYMENT RELATED */
    log.belongsTo(models.Forfait, { foreignKey: 'forfaitId' }); // forfaitId

    /* POSTS RELATED */
    log.belongsTo(models.Post, { foreignKey: 'postId' }); // postId

    /* REVIEWS RELATED */
    log.belongsTo(models.Review,{foreignKey:'reviewId'}); // reviewId

    /* API KEY RELATED */
    log.belongsTo(models.ApiKeys,{foreignKey:'apiKeyId'}); // apiKeyId

    /* SCALE RELATED */
    log.belongsTo(models.McqScale,{foreignKey:'scaleId'}); // scaleId

    /* Formation Element Id */
    log.belongsTo(models.FormationElement,{foreignKey:'formationElementId'}) // formationElementId
  };

  return log;
};

export default Log;
