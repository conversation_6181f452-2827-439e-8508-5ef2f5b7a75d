'use strict'

// Should be in frontend too
export const NotificationType = {
  NEW_COURS: 'NEW_COURS',
  NEW_QCM: 'NEW_QCM',

  NEW_EXAM: 'NEW_EXAM',
  NEW_EVENT: 'NEW_EVENT',

  NEW_FICHE: 'NEW_FICHE', // not implemented
  NEW_FILE: 'NEW_FILE', // // not implemented  Autre fichiers utiles

  NEW_MESSAGE: 'NEW_MESSAGE', // PM

  NEW_POST: 'NEW_POST',
  NEW_THREAD: 'NEW_THREAD',
  NEW_POST_ANSWER: 'NEW_POST_ANSWER',

  NEW_POST_LIKE: 'NEW_POST_LIKE', // not implemented

  NEW_GLOBAL_ANNOUNCE: 'NEW_GLOBAL_ANNOUNCE',
  UPDATED_COURS: 'UPDATED_COURS',

  USER_ASKS_FOR_DELETION: 'USER_ASKS_FOR_DELETION',
  NEW_GROUP_MESSAGE: 'NEW_GROUP_MESSAGE',

  USER_SUBSCRIBE_SUCCESS: 'USER_SUBSCRIBE_SUCCESS',
}

const notification = (sequelize, DataTypes) => {
  // cours qcm annales messages
  const Notification = sequelize.define('notification', {
    text: { type: DataTypes.STRING, validate: { notEmpty: true } },
    type: { type: DataTypes.STRING, validate: { notEmpty: true } },
    method: { type: DataTypes.STRING },

    objectId: { type: DataTypes.INTEGER },
    parentId: { type: DataTypes.INTEGER },

    count: { type: DataTypes.INTEGER, defaultValue: 0 },

    value: { type: DataTypes.STRING },
    seen: { type: DataTypes.BOOLEAN, defaultValue: false }, // Déjà vu ou pas

    pinned: { type: DataTypes.BOOLEAN, defaultValue: false },
  })

  Notification.associate = models => {
    Notification.belongsTo(models.User) // récepteur de la notif
    Notification.belongsTo(models.User, { as: 'fromUser' }) // créateur de la notif
  }

  return Notification
}

export default notification
