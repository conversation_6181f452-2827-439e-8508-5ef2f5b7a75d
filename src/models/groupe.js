'use strict';
const groupe = (sequelize, DataTypes) => {
  const Groupe = sequelize.define('groupe', {
    name: { type: DataTypes.STRING },
    name_en: { type: DataTypes.TEXT, defaultValue: null },
    name_it: { type: DataTypes.TEXT, defaultValue: null },
    name_de: { type: DataTypes.TEXT, defaultValue: null },
    name_es: { type: DataTypes.TEXT, defaultValue: null },

    role: { type: DataTypes.STRING, defaultValue: null },
    image: { type: DataTypes.TEXT, defaultValue: null },
    isIndividual: { type: DataTypes.BOOLEAN, defaultValue: false },
    // type : authenticated, public
  });

  Groupe.associate = models => {
    Groupe.belongsToMany(models.User, { through: models.UserGroups });
    Groupe.belongsToMany(models.UE, { through: models.UEGroups }, { as: 'UE' }); // addUegroups
    Groupe.belongsToMany(models.UECategory, { through: models.UECategoryGroups, foreignKey: 'groupeId' }); // addForumcategorygroups
    Groupe.belongsToMany(models.Cours, { through: models.CoursGroups, foreignKey: 'groupeId' }); // addForumcategorygroups

    Groupe.belongsToMany(models.ForumCategory, { through: models.ForumCategoryGroups, foreignKey: 'groupId' }); // addForumcategorygroups
    Groupe.belongsToMany(models.DateDiffusion, { through: models.DateDiffusionGroups, foreignKey: 'groupId' }); // addDateDiffusionGroups
    Groupe.belongsToMany(models.DateDiffusion, { through: models.DateDiffusionGroups, foreignKey: 'groupId' }); // addDateDiffusionGroups

    Groupe.belongsToMany(models.GlobalAnnounce, { through: models.GlobalAnnounceGroups, foreignKey: 'groupeId' });
    Groupe.belongsToMany(models.Fiche, { through: models.FichesGroups, foreignKey: 'groupeId' });
    Groupe.belongsToMany(models.Forfait, { through: models.ForfaitGroupsNotification, foreignKey: 'groupId' });

    Groupe.belongsToMany(models.ScheduledTask, { through: models.ScheduledTasksGroups, foreignKey: 'groupId' });

    Groupe.belongsToMany(models.Groupe, {
      through: models.GroupsResponsibility,
      foreignKey: 'groupeId',
      as: 'responsibleOfGroupe',
    });

    Groupe.belongsToMany(models.Config,{
      through:models.UserGroupChatGpt,
      foreignKey:'chatGptId',
      as:'availableChatGptConfig',
      otherKey:'id'
    })

    Groupe.belongsToMany(models.Formation, { through: models.FormationGroups, foreignKey: 'groupeId', as: 'formation' });


    // TODO will be deleted
    Groupe.belongsToMany(models.ExamSession, {
      through: models.ExamSessionGroups,
      foreignKey: 'groupeId',
      as: 'examSession',
    });

    Groupe.belongsToMany(models.FormationElement, {
      through: models.FormationElementGroupUnlock,
      foreignKey: 'groupeId',
      as: 'formationElement',
    });

    Groupe.belongsToMany(models.FormationElement, {
      through: models.FormationElementGroups,
      foreignKey: 'groupeId',
      as: 'formationElementAccess',
    });


    Groupe.belongsTo(models.Folder, { foreignKey: 'folderId'});

    Groupe.belongsToMany(models.Forms, { through: models.FormMandatoryGroups, foreignKey: 'groupId', as: 'mandatoryForm' });

  };

  return Groupe;
};

export default groupe;
