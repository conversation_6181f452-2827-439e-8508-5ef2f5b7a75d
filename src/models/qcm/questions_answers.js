/* jshint indent: 2 */

module.exports = function(sequelize, DataTypes) {
  const QuestionAnswers = sequelize.define('question_answers', {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
    },
    isTrue: {
      type: DataTypes.BOOLEAN,
      defaultValue: null,
    },
    isHorsConcours: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isAllPointsOrNothing: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    autoAddNotions: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    text: { type: DataTypes.TEXT },
    text_en: { type: DataTypes.TEXT, defaultValue: null },
    text_it: { type: DataTypes.TEXT, defaultValue: null },
    text_de: { type: DataTypes.TEXT, defaultValue: null },
    text_es: { type: DataTypes.TEXT, defaultValue: null },

    explanation: { type: DataTypes.TEXT },
    explanation_en: { type: DataTypes.TEXT, defaultValue: null },
    explanation_it: { type: DataTypes.TEXT, defaultValue: null },
    explanation_de: { type: DataTypes.TEXT, defaultValue: null },
    explanation_es: { type: DataTypes.TEXT, defaultValue: null },

    url_image_explanation: { type: DataTypes.TEXT, allowNull: true },
    url_image_explanation_en: { type: DataTypes.TEXT, defaultValue: null },
    url_image_explanation_it: { type: DataTypes.TEXT, defaultValue: null },
    url_image_explanation_de: { type: DataTypes.TEXT, defaultValue: null },
    url_image_explanation_es: { type: DataTypes.TEXT, defaultValue: null },

    url_image: { type: DataTypes.TEXT, defaultValue: null },
    url_image_en: { type: DataTypes.TEXT, defaultValue: null },
    url_image_it: { type: DataTypes.TEXT, defaultValue: null },
    url_image_de: { type: DataTypes.TEXT, defaultValue: null },
    url_image_es: { type: DataTypes.TEXT, defaultValue: null },


  }, {
    tableName: 'question_answers',
  })

  QuestionAnswers.associate = models => {
    QuestionAnswers.belongsTo(models.Question, { foreignKey: 'questionId' })
    QuestionAnswers.belongsToMany(models.Notion, { through: models.NotionQuestionAnswers, foreignKey: 'answerId' })
    QuestionAnswers.hasMany(models.Post, { foreignKey: 'answerId' })
  }

  return QuestionAnswers
}
