import models from '../index.js';

module.exports = (sequelize, DataTypes) => {
  const QuestionCours = sequelize.define('question_cours', {
    // Ici on peut mettre des champs, mais ça m'importe peu, je veux juste pouvoir définir un hook

  }, {
    tableName: 'question_cours',

    hooks: {
      /* Les hooks de la table. L'idée de ces hooks est de maintenir de façon implicite le boolean 'isLinkedToCourses' de la table question */


      afterCreate: async (instance, options) => {
        // Fonction qui après create, modifie la question pour ajouter isLinkedToCourses=True
        try{
          const questionId=instance.questionId
          const question = await models.Question.findByPk(questionId)

          question.isLinkedToCourses=true
          await question.save()
        }catch(e){
          console.error("error dans le hook de question_cours.js (afterCreate), l'update automatique de question.isLinkedToCourses a échoué.    e:",e)
        }
      },

      afterBulkCreate: async (instances, options) => {
        // Fonction qui après chaque création, update la question pour mettre 'isLinkedToCourses' à true

        try {
          // Mise dans une array de tous les questionsId ayant étés modifiées pour avoir un cour ou plus
          const bulkArray = []

          // lecture des instances et push dans bulkArray
          for (const i of instances) {
            bulkArray.push(i.questionId)
          }

          // unification de l'array
          const uniqueQuestionIdArray = [...new Set(bulkArray)]

          // modification des éléments de uniqueQuestionIdArray
          for (const qId of uniqueQuestionIdArray) {
            const question = await models.Question.findByPk(qId)
            question.isLinkedToCourses = true
            await question.save()
          }
        } catch (e) {
          console.error("error dans le hook de question_cours.js (afterBulkCreate), l'update automatique de question.isLinkedToCourses a échoué.    e:", e)
        }
      },


      afterDestroy:async (instance,options)=>{
        // Fonction qui après destroy, regarde si la question a tjr des coursId linkés. Et update le boolean IsLinkedToCourses en conséquence.

        try{
          const questionId=instance.questionId
          const question = await models.Question.findByPk(questionId,{
            include:[{
              model:models.Cours,
              attributes:["id"]
            }],
          });

          // Si on a plus de cours linké alors on update à false
          if (question?.cours === undefined || question?.cours?.length === 0){
            question.isLinkedToCourses=false
            await question.save()
          }
        }catch(e){
          console.error("error dans le hook de question_cours.js (afterDestroy), l'update automatique de question.isLinkedToCourses a échoué.    e:", e)
        }
      },

      beforeBulkDestroy : async (instances,options)=>{
        /* Cette fonction c'est vmt la galère : on va regarder les liens question <-> cours qui seront supprimés, puis regarder si
        après la suppression, pour chaque question, tous les liens vont être supprimés ou pas. */

        try{
          // recupère les éléments qui seront supprimés et les groupBy => dans instance, on a la query.
          // On rejoue la querie avec le groupBy (questionId) et concat des coursId, => pour chaque questionId on a une array [coursId]
          const elements = await models.QuestionCours.findAll({
            where: instances.where,
            attributes: ['questionId', [sequelize.fn('GROUP_CONCAT', sequelize.col('coursId')), 'coursId']],
            group: ['questionId']
          });

          // Pour chaque élément, et les arrays de coursId, on va regarder si les arrays à supprimer sont la totalité des arrays de la questionId
          for (const node of elements){
            const qId=node.questionId
            const coursIdArray=node.coursId.split(',')

            // Récupération des links pré-existants
            const questionCoursIdStringArray= await models.QuestionCours.findAll({
              where : {questionId:qId},
            }).then(node => node.map(value => value.coursId.toString()))

            // maintenant on compare les arrays des cours à retirer, et les array des cours existants

            const existant = [... new Set(questionCoursIdStringArray)]
            const toRemove = [... new Set(coursIdArray)]


            if (existant.length === toRemove.length){
              const full = existant.every(element => toRemove.includes(element));

              if (full){
                const question = await models.Question.findByPk(qId)
                question.isLinkedToCourses=false
                await question.save()
              }
            }
          }
        }catch(e){
          console.error("error dans le hook de question_cours.js (afterBulkDestroy), l'update automatique de question.isLinkedToCourses a échoué.    e:", e)
        }
      },
    }
  });

  QuestionCours.associate = models =>{
    QuestionCours.belongsTo(models.Cours, { foreignKey: 'coursId', as: 'cours' });
    QuestionCours.belongsTo(models.Question, { foreignKey: 'questionId', as: 'question' });
  }

  return QuestionCours;
};