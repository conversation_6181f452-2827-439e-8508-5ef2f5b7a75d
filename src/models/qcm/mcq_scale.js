'use strict'

export const McqScaleRulesProperties = {
  pointsPerQuestion: 'pointsPerQuestion',
  minimumGrade: 'minimumGrade',
  pointsLostPerError: 'pointsLostPerError', // ARRAY
  numberOfErrors: 'numberOfErrors',

  notation: 'notation', // 'default' or 'custom'

  // For custom notation, points lost per error (array)
  pointsLostPerErrorIfTrueAnswerIsCheckedFalse: 'pointsLostPerErrorIfTrueAnswerIsCheckedFalse',
  // "fixed" or "variableByTotalAnswers" or "variableByTrueAnswers"
  pointsLostPerErrorIfTrueAnswerIsCheckedFalseSettings: 'pointsLostPerErrorIfTrueAnswerIsCheckedFalseSettings',
  pointsLostPerErrorIfFalseAnswerIsCheckedTrue: 'pointsLostPerErrorIfFalseAnswerIsCheckedTrue',
  pointsLostPerErrorIfFalseAnswerIsCheckedTrueSettings: 'pointsLostPerErrorIfFalseAnswerIsCheckedTrueSettings',
  pointsLostPerErrorIfFalseAnswerIsUndefined: 'pointsLostPerErrorIfFalseAnswerIsUndefined',
  pointsLostPerErrorIfFalseAnswerIsUndefinedSettings: 'pointsLostPerErrorIfFalseAnswerIsUndefinedSettings',
  pointsLostPerErrorIfTrueAnswerIsUndefined: 'pointsLostPerErrorIfTrueAnswerIsUndefined',
  pointsLostPerErrorIfTrueAnswerIsUndefinedSettings: 'pointsLostPerErrorIfTrueAnswerIsUndefinedSettings',
}


export const McqScaleType = {
  manual: 'manual',
  dynamic: 'dynamic',
}

export const McqScaleQuestionType = {
  MultipleChoice: 'mcq',
  UniqueChoice: 'ucq',
  Alphanumerical: 'alphanumerical',
  FreeText: 'freetext',
  Schema: 'schema',
  SchemaFillInLegends: 'schemaFillInLegends',
  FillInTheBlanks: 'fillintheblanks',
  FLASHCARD:'FLASHCARD',
  ReorderElements: 'reorderelements'
}
export const McqScaleQuestionTypeToFrenchMap = {
  'mcq': 'QCM',
  'ucq': 'QCU',
  'alphanumerical': 'Réponse alphanumérique',
  'freetext': 'Texte libre',
  'schema': 'Schéma',
  'schemaFillInLegends': 'Schéma à compléter',
  'fillintheblanks': 'Texte à trous',
  'reorderelements': 'Remise en ordre',
}

export const McqScaleQuestionTypeForGPTMap = {
  'mcq': 'QCM',
  'ucq': 'QCM',
  'alphanumerical': 'Réponse alphanumérique',
  'freetext': 'Texte libre',
  'schema': 'Schéma',
  'schemaFillInLegends': 'Schéma: légendes à compléter',
  'fillintheblanks': 'Texte à trous',
  'reorderelements': 'Remise en ordre',
}

/* Default Rule (barême) */
export const McqScaleDefaultRules = {
  [McqScaleRulesProperties.pointsPerQuestion]: 1,
  [McqScaleRulesProperties.minimumGrade]: 0,
  [McqScaleRulesProperties.numberOfErrors]: 5,
  [McqScaleRulesProperties.pointsLostPerError]: [-0.5, -0.3, -0.2, 0, 0], // 5
}
export const McqScaleRulesSettingsValues = {
  fixed: 'fixed',
  variableByTotalAnswers: 'variableByTotalAnswers',
  variableByTrueAnswers: 'variableByTrueAnswers',
}

const McqScale = (sequelize, DataTypes) => {
  const McqScale = sequelize.define('mcq_scale', {
    // id
    name: { type: DataTypes.TEXT },
    isDefault: { type: DataTypes.BOOLEAN, defaultValue: false },
    type: {type: DataTypes.STRING, defaultValue: 'manual' },
    rules: {
      type: DataTypes.TEXT('long'),
      get() {
        const rawValue = this.getDataValue('rules')
        return rawValue ? JSON.parse(rawValue) : null
      },
    }, // JSON

    questionType: {type: DataTypes.STRING, defaultValue: null},
    pointsObtainedWhenNothingChecked: {type: DataTypes.FLOAT, defaultValue: null},

  }, {
    tableName: 'mcq_scale',
  })
  McqScale.associate = models => {
    McqScale.hasMany(models.Question, { foreignKey: 'mcqScaleId' })

    // Default mcq scales
    McqScale.belongsToMany(models.UE, {
      through: models.McqScaleUes,
      foreignKey: 'mcqScaleId',
    })

    //
    McqScale.belongsTo(models.User, { as: 'author',foreignKey:'authorId' })

    McqScale.hasMany(models.Log,{as:'logs',foreignKey:"scaleId"})
  }
  return McqScale
}

export default McqScale
