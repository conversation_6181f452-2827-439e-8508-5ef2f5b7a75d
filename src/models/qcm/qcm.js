import { QCMService } from '../../graph-api/qcm/qcm-service'

export const QuestionPickingStrategies = {
  NORMAL: 'normal',
  SMART: 'smart',
}

module.exports = function(sequelize, DataTypes) {
  const Qcm = sequelize.define('qcm', {
    id_qcm: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
    },
    id_lien: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
    },
    titre: {
      type: DataTypes.STRING(900),
      allowNull: false,
    },
    titre_en: { type: DataTypes.TEXT, defaultValue: null },
    titre_it: { type: DataTypes.TEXT, defaultValue: null },
    titre_de: { type: DataTypes.TEXT, defaultValue: null },
    titre_es: { type: DataTypes.TEXT, defaultValue: null },

    description: {
      type: DataTypes.STRING(900),
      allowNull: true,
    },

    description_en: { type: DataTypes.TEXT, defaultValue: null },
    description_it: { type: DataTypes.TEXT, defaultValue: null },
    description_de: { type: DataTypes.TEXT, defaultValue: null },
    description_es: { type: DataTypes.TEXT, defaultValue: null },

    enableAiAnalysis: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false},

    url_image: {
      type: DataTypes.STRING(200),
      allowNull: true,
    },
    date_creation: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    id_createur: {
      type: DataTypes.INTEGER(11),
      allowNull: true,
    },
    date_modif: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    // ue: legacy qcm system
    ue: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
    },
    deleted: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: '0',
    },
    chronometre: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: '0',
    },
    annee: {
      type: DataTypes.INTEGER(11),
      allowNull: true,
    },
    pseudo_createur: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    public: {
      type: DataTypes.INTEGER(1),
      allowNull: false,
      defaultValue: '0',
    },
    annale: {
      type: DataTypes.INTEGER(1),
      allowNull: true,
      defaultValue: '0',
    },
    linkCoursId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    isLinkedToCours: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    hasExternalQuestions: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    questionPickingStrategy: {
      type: DataTypes.STRING,
      defaultValue: 'normal',
    },
    difficulty: { type: DataTypes.FLOAT },
    views: { type: DataTypes.INTEGER(11) },
    timer_delay: {
      type: DataTypes.INTEGER(11),
      allowNull: true,
      defaultValue: '90',
    },
    goToNextQuestionWhenTimesUp: { type: DataTypes.BOOLEAN, defaultValue: false },
    shouldResumeTime: { type: DataTypes.BOOLEAN, defaultValue: false },
    chronoByQuestionOrGlobal: { type: DataTypes.STRING, defaultValue: 'timeByQuestion' },

    isPublished: { type: DataTypes.BOOLEAN, defaultValue: false },
    groupQuestionsByTheme: { type: DataTypes.BOOLEAN, defaultValue: false },
    isFullscreen: { type: DataTypes.BOOLEAN, defaultValue: false },
    hasCheckboxes: { type: DataTypes.BOOLEAN, defaultValue: true },
    timesItCanBeDone: { type: DataTypes.INTEGER, allowNull: true, defaultValue: null },

    randomizeQuestions: { type: DataTypes.BOOLEAN, defaultValue: false },
    randomizeQuestionsAnswers: { type: DataTypes.BOOLEAN, defaultValue: false },
    showCorrectionAtEachStep: { type: DataTypes.BOOLEAN, defaultValue: false },
    isAuthorChanged: { type:  DataTypes.BOOLEAN, defaultValue: false },
    correctionConfig: {
      type: DataTypes.JSON, defaultValue: null,
      set(value) {
        this.setDataValue('correctionConfig', JSON.stringify(value))
      },
      get() {
        try {
          const rawValue = this.getDataValue('correctionConfig')
          return rawValue ? JSON.parse(rawValue) : {}
        } catch (e) {
          return {}
        }
      },
    },

  }, {
    tableName: 'qcm',
    timestamps: false,
    /*
    hooks: {
      afterFind: async function (result, options) {
        const nbQ = await QCMService.getNombreQuestionsQCM(result.id_qcm)
        if (nbQ) {
          result.nombreQuestions = nbQ
        }
        return result
      },
    },
     */
  })
  Qcm.associate = models => {
    // MCQ Questions
    Qcm.hasMany(models.Question, { foreignKey: 'id_qcm' })
    Qcm.belongsTo(models.User, { as: 'author' })
    // Cours lié aux QCMs
    Qcm.belongsToMany(models.Cours, {
      through: models.CoursQcm,
    })
    // Parent category (less used)
    Qcm.belongsTo(models.UECategory, { as: 'sousCategorie', foreignKey: 'sousCategorieIdSousCategorie' }) // Souscatégorie QCM
    Qcm.belongsTo(models.UE, { as: 'UE' })
    Qcm.hasMany(models.QcmStats, { foreignKey: 'id_qcm' })
    Qcm.belongsToMany(models.TypeQcm, { through: models.QcmTypeQcm, foreignKey: 'qcmId' })
    Qcm.belongsToMany(models.TypeQcm, { through: models.QcmDefaultTypeQcmForQuestions, foreignKey: 'qcmId', as: 'defaultTypeQcmForQuestions' })

    Qcm.hasMany(models.FormationElement, { foreignKey: 'headerMcqId', as: 'headerElements'});

    // Question dynamically added
    Qcm.belongsToMany(models.Question, { through: models.QuestionsQcm, foreignKey: 'qcmId', as: 'importedQuestion' })
  }

  return Qcm
}
