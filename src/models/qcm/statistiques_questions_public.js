/* jshint indent: 2 */

module.exports = function(sequelize, DataTypes) {
  return sequelize.define('statistiques_questions_public', {
    id_statistique_question: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    id_question: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    id_utilisateur: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    reponse: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    A_eleve: {
      type: DataTypes.INTEGER(4),
      allowNull: true
    },
    B_eleve: {
      type: DataTypes.INTEGER(4),
      allowNull: true
    },
    C_eleve: {
      type: DataTypes.INTEGER(4),
      allowNull: true
    },
    D_eleve: {
      type: DataTypes.INTEGER(4),
      allowNull: true
    },
    E_eleve: {
      type: DataTypes.INTEGER(4),
      allowNull: true
    }
  }, {
    tableName: 'statistiques_questions_public',
    timestamps: false,
  });
};
