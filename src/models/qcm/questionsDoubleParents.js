
module.exports = function(sequelize, DataTypes) {
  const QuestionsDoubleParents = sequelize.define('questions_double_parents', {
    isError: {
      type: DataTypes.BOOLEAN, defaultValue: false,
    },
  }, {
    tableName: 'questions_double_parents',
  });

  QuestionsDoubleParents.associate = models => {
    QuestionsDoubleParents.belongsTo(models.Question, { foreignKey: 'questionId' })
    QuestionsDoubleParents.belongsTo(models.Question, { foreignKey: 'parentQuestionId' })
    QuestionsDoubleParents.belongsTo(models.Question, { foreignKey: 'parentQuestionId2' })
  };

  return QuestionsDoubleParents;
};
