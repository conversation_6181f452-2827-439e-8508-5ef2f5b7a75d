'use strict'


const postReaction = (sequelize, DataTypes) => {
  const PostReaction = sequelize.define('post_reactions', {
    emoji: { type: DataTypes.STRING },
  })

  PostReaction.associate = models => {
    PostReaction.belongsTo(models.User, { as: 'user', foreignKey: 'userId' })
    PostReaction.belongsTo(models.Post, { as: 'post', foreignKey: 'postId' })
  }

  return PostReaction
}

export default postReaction
