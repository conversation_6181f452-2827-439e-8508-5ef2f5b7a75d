'use strict';
const fiche = (sequelize, DataTypes) => {
  const Fiche = sequelize.define('fiche', {
    name: { type: DataTypes.STRING, validate: { notEmpty: true }, },
    file: { type: DataTypes.STRING, validate: { notEmpty: true }, },
    image: { type: DataTypes.STRING },
    deleted: { type: DataTypes.BOOLEAN, defaultValue: false },
    isAccessible: { type: DataTypes.BOOLEAN, defaultValue: true },
  });

  Fiche.associate = models => {
    Fiche.belongsTo(models.Cours);

    Fiche.belongsToMany(models.Groupe, { through: models.FichesGroups, foreignKey: 'ficheId' })
  };

  return Fiche;
};

export default fiche;
