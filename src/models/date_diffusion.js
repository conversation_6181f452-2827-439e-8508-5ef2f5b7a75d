'use strict';

const dateDiffusion = (sequelize, DataTypes) => {
  const DateDiffusion = sequelize.define('date_diffusion', {
    date: { type: DataTypes.DATE, defaultValue: null },
    dateEnd: { type: DataTypes.DATE, defaultValue: null },
    show: { type: DataTypes.BOOLEAN, defaultValue: true },

    updateInfos: { type: DataTypes.TEXT, defaultValue: null },
    updateInfos_en: { type: DataTypes.TEXT, defaultValue: null },
    updateInfos_it: { type: DataTypes.TEXT, defaultValue: null },
    updateInfos_es: { type: DataTypes.TEXT, defaultValue: null },
    updateInfos_de: { type: DataTypes.TEXT, defaultValue: null },

    availability: { type: DataTypes.STRING, defaultValue: null },
    recurringPeriod: { type: DataTypes.STRING, defaultValue: null },
    link: { type: DataTypes.STRING, defaultValue: null },
    recurringEndDate: { type: DataTypes.DATE, defaultValue: null },
    timezone: { type: DataTypes.STRING, defaultValue: null },
    allDay: { type: DataTypes.BOOLEAN, defaultValue: false },

    recurrenceRule: { type: DataTypes.JSON, defaultValue: null }, // champ pour les règles de récurrence
    hasRecurrence:{ type: DataTypes.BOOLEAN, defaultValue: false }, // champ pour savoir si l'événement est récurrent

    ical_summary:{ type: DataTypes.STRING, allowNull: true }, // champ pour le résumé de l'événement ical
    ical_uid: { type: DataTypes.STRING, defaultValue: null },
    locationString: { type: DataTypes.STRING, defaultValue: null },
    ical_data: { type: DataTypes.JSON, defaultValue: null },

    customTitle: { type: DataTypes.STRING, allowNull: true},
  }, {
    tableName: 'date_diffusion',
  });
  DateDiffusion.associate = models => {
    DateDiffusion.belongsTo(models.Cours, { foreignKey: 'courId' }); // courId
    DateDiffusion.belongsTo(models.Event, { foreignKey: 'eventId' }); // Events
    DateDiffusion.belongsTo(models.Qcm, { foreignKey: 'qcmId' }); // qcm
    DateDiffusion.belongsTo(models.ExamSession, { foreignKey: 'examSessionId' }); // examSession
    DateDiffusion.belongsTo(models.CustomPlanning, { foreignKey: 'customPlanningId' }); // customPlanningId

    DateDiffusion.belongsTo(models.Building, { foreignKey: 'buildingId' }); // Building
    DateDiffusion.belongsTo(models.Room, { foreignKey: 'roomId' }); // Room

    DateDiffusion.belongsTo(models.Calendar, { foreignKey: 'calendarId' }); // Calendar (personal calendar or ical importation)

    DateDiffusion.belongsToMany(models.Groupe, {
      through: models.DateDiffusionGroups,
      foreignKey: 'date_diffusion_id',
    });
    DateDiffusion.belongsToMany(models.User, {
      through: models.DateDiffusionParticipants,
      foreignKey: 'date_diffusion_id',
      as: 'participants'
    });
    DateDiffusion.belongsToMany(models.User, {
      through: models.DateDiffusionOrganizers,
      foreignKey: 'date_diffusion_id',
      as: 'organizers'
    });

  };
  return DateDiffusion;
};

export default dateDiffusion;
