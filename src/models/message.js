'use strict'
import Sequelize from 'sequelize'

// Private messages and real time chat message
const message = (sequelize, DataTypes) => {
  const Message = sequelize.define('message', {
    text: { type: DataTypes.TEXT('long') },
    tag: { type: DataTypes.STRING },
    read: DataTypes.BOOLEAN, // message a été vu
    likes: { type: DataTypes.INTEGER },
    ip: { type: DataTypes.STRING, defaultValue: 'null' },
    isDeleted: { type: DataTypes.BOOLEAN, defaultValue: false },
    createdAt: { allowNull: false, type: DataTypes.DATE(6), defaultValue: Sequelize.NOW },
    updatedAt: { allowNull: false, type: DataTypes.DATE(6), defaultValue: Sequelize.NOW }
  })

  Message.associate = models => {
    Message.belongsTo(models.User)
    Message.belongsTo(models.Discussion)
    Message.hasOne(models.File, { as: 'file', foreignKey: 'messageId' }) // Peut avoir un fichier
    Message.hasOne(models.File, { as: 'fileImage', foreignKey: 'messageFileImageId' }) // Peut avoir un fichier image
  }

  return Message
}

export default message
