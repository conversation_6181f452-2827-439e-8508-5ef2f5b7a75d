'use strict';

export const FOLDER_TYPES = {
  GROUP: 'GROUP', // Groupe
  EXAM: 'EXAM', // Examen
  EVENT: 'EVENT', // Événement
  FORFAIT: 'FORFAIT', // Forfait / Offre
  CONTENT_TYPE: 'CONTENT_TYPE', // Type de contenu
};
const folder = (sequelize, DataTypes) => {
  const Folder = sequelize.define('folders', {
    name: { type: DataTypes.STRING, validate: { notEmpty: true }, defaultValue: null, },
    type: { type: DataTypes.STRING, defaultValue: null, },
  });

  Folder.associate = models => {
    // Parent folder
    Folder.hasOne(models.Folder, { as: 'parent', foreignKey: 'parentId' });
  };

  return Folder;
};

export default folder;
