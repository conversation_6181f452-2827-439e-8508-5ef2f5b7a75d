'use strict'

const schemaLibrary = (sequelize, DataTypes) => {
  const SchemaLibrary = sequelize.define('schema_library', {
    name: { type: DataTypes.STRING },
    name_en: { type: DataTypes.STRING, defaultValue: null },
    name_it: { type: DataTypes.STRING, defaultValue: null },
    name_de: { type: DataTypes.STRING, defaultValue: null },
    name_es: { type: DataTypes.STRING, defaultValue: null },

    description: { type: DataTypes.STRING },
    description_en: { type: DataTypes.STRING },
    description_it: { type: DataTypes.STRING },
    description_de: { type: DataTypes.STRING },
    description_es: { type: DataTypes.STRING },

    image: { type: DataTypes.STRING },
    imageCorrection: { type: DataTypes.STRING },
    isPublished: { type: DataTypes.BOOLEAN, defaultValue: true },

    legends: { type: DataTypes.JSON, defaultValue: [] },
    settings: { type: DataTypes.JSON, defaultValue: {} },
    lines: { type: DataTypes.JSON, defaultValue: [] },
    text: { type: DataTypes.JSON, defaultValue: [] },
    deletedAt: { type: DataTypes.DATE, defaultValue: null, allowNull: true },
  }, {
    tableName: 'schema_library',
  });

  SchemaLibrary.associate = models => {
    SchemaLibrary.belongsTo(models.User, { as: 'author', foreignKey: 'authorId' });
    SchemaLibrary.belongsToMany(models.Cours, { through: models.SchemaLibraryCours,
      foreignKey: 'schemaLibraryId'
    });

    SchemaLibrary.belongsToMany(models.TypeQcm, { through: models.SchemaLibraryTypeQcm, foreignKey: 'schemaLibraryId' });
  };
  return SchemaLibrary;
}

export default schemaLibrary;
