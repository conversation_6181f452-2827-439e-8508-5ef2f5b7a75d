import jwt from 'jsonwebtoken';
import { generate } from 'shortid';
import config from '../config/config.js';
import { createToken, UserModificationSource, UserService } from '../graph-api/user/user-service.js';

const express = require('express');
const router = express.Router();

router.get('/', function(req, res, next) {
  res.status(404).end();
});

// SSO entrypoint from barchen
router.post('/barchen/:token', async function(req, res, next) {
  try {
    // Check token validity
    const me = await jwt.verify(req.params.token, process.env.BARCHEN_SECRET);
    //console.log(me)
    // Check if user already logged in before
    if (!me) {
      throw new Error('Invalid token');
    }
    let externalId = me.username;
    if (!externalId) {
      externalId = me.sub;
    }
    if (!externalId) {
      throw new Error('Cant find subject identifier');
    }

    let user = await UserService.findByExternalId(externalId);
    if (!user) {
      // Create user
      user = await UserService.createUser({
        username: externalId + '_b',
        email: `u${externalId}@aptoria.com`,
        externalId: externalId,
        password: generate(),
      }, { me:null, req, ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress }, UserModificationSource.auto);
    }

    // Create session JWT for this user
    const sessionToken = await createToken(user, process.env.SECRET, config.TOKEN_EXPIRATION);
    res.json({
      token: sessionToken,
      me: user,
    });

  } catch (e) {
    console.error(e);
    res.json({ error: e.message });
  }
});

export default router;