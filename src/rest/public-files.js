import models from "../models";
import {UPLOAD_FOLDER_MAP} from "../util/utils";
const express = require('express');
const router = express.Router();

router.get('/avatar/:fileId', async function(req, res, next) {
  try {
    await res.sendFile(`${process.cwd()}/${UPLOAD_FOLDER_MAP.avatars}/${req.params.fileId}`, (err => console.error(err)));
  } catch (e) {
    console.error(e);
  }
});

module.exports = router;
