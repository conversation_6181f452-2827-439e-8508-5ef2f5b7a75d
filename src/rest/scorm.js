import contentDisposition from 'content-disposition'
import jwt from 'jsonwebtoken'
import { ROLES } from '../graph-api/authorization'
import { TOKEN_HEADER_NAME, UPLOAD_FOLDER_MAP } from '../util/utils'
const express = require('express');
const router = express.Router();
import path from 'path';
import { promises as fs, constants } from 'fs';
import {validateUserRestRessource} from '../util/utils'
import models from '../models/index'
import {ScormService} from '../graph-api/scorm/scorm-service'
import {PermissionService} from '../graph-api/permission/permission-service'

router.post('/commit',async (req,res)=>{
  const { learnerId, scoId } = req.query;          // ou token JWT
  const commitObject = req.body;                   // CMI complet ou delta


  // Recup des infos du header
  const token = req?.headers[TOKEN_HEADER_NAME]
  const userId = req.headers['userid'];
  const frontId = req.headers['frontid'];
  const formationElementId=req.headers['formationelementid']
  const scormFilePath=req.headers['scormfilepath']
  const scormEntryPoint=req.headers['scormentrypoint']

  // Verif du x-token
  let me
  try {
    me = await jwt.verify(token, process.env.SECRET);
    if (parseInt(userId,10) !== me?.id) {
      throw new Error("")
    }
  } catch (e) {
    console.error("Error token !!! e :",e)
    res.status(401).json({ message: 'jwt-error' });
    return;
  }

  // Extraction des variables du commitObject
  const totalTime=commitObject?.totalTimeSeconds
  const completionStatus=commitObject?.completionStatus
  const successStatus=commitObject?.successStatus
  const cmi=commitObject?.cmi

  // Get contexte simplifié
  const simplifiedCtx={
    models,
    me,
    ip: req.ip,
    req,
  }

  // Submit à la BDD
  ScormService.updateScormRecord({
    args:{
      frontId,
      formationElementId,
      totalTime,
      completionStatus,
      successStatus,
      cmi,
      scormFilePath,
      scormEntryPoint,
    },
    ctx:simplifiedCtx
  })


  res.json({ result: true, errorCode: 0 });
  return ;
});

router.get('/:file(CFA\\.js)',async(req,res)=>{
    try {
      const { file } = req.params;

      const baseDir=path.resolve(process.cwd(),UPLOAD_FOLDER_MAP.scorm) // le path vers le dossier de fichier autorisé

      const requestedPath = path.resolve(baseDir, file);

      // anti-path-traversal :
      if (!requestedPath.startsWith(baseDir + path.sep)) {
        console.warn("rest protected file : accès non autorisé")
        res.status(403).json({ error: 'Accès non autorisé.' });
        return;
      }

      const disposition = contentDisposition(requestedPath, {
        type: 'inline',
      });

      res.sendFile(
        requestedPath,
        {
          headers: {
            'Content-Disposition': disposition,
            'Content-Security-Policy': "frame-ancestors *",
          },
        },
        (err) => {
          if (err) {
            console.error("api rest : 404 error :",err)
            return res.status((err && err.status) || 404).end()
          }
        }
      );
      return;
    } catch (e) {
      console.error(`[REST SCORM PROTECTED FILE] e : ${e}`);
      return res.status(500).send('Internal Server Error');
    }
  }
);

router.get('/:scormPackage/:scormEntryPoint(*)',
  //validateUserRestRessource,
  async(req,res,next)=>{
  try {
    /////// Permission :
    const token=req.query.tk
    let me
    let isEntryPointOk=false

    // Si token, on a le droit d'accéder à l'entrypoint
    if (token){
      try {
        me = await jwt.verify(token, process.env.SECRET);
        isEntryPointOk=true
      } catch (e) {}
    }

    // get des arguments
    const { scormPackage, scormEntryPoint } = req.params;

    // Recup du FE correspondant au path et scormEntryPoint
    const formationElement=await models.FormationElement.findOne({where:{scormFilePath:scormPackage,scormEntryPoint}})


    // Si on cherche un entryPoint, mais que on a pas de formation Element
    if (isEntryPointOk && !formationElement){
      console.warn("entry point qui existe mais FE non existant (Probablement delete du FE non propagé en front qui query cet endpoint)")
      // Je retire le retourne car j'ai un cas où la navigation est OK pour l'entryPoint, mais pas bon pour le formationElement => same racine, mais pas même entry point quoi
      //res.status(404).json({message:'FE linked to SCORM not found'})
      // return;

    // Si on cherche un entryPoint et que l'on a l'élément
    } else if (isEntryPointOk && formationElement){
      // Verif de l'authorisation
      const authorizedList = PermissionService.FormationElement.getAllowedFormationElementsForUser([formationElement],me.id)
      if (authorizedList.length===0){
        console.warn("Pas l'autorisation d'accéder à l'entry point de l'élément")
        res.status(403).json({message:'forbidden'})
        return;
      }
    }

    // Si on est là, c'est qu'on est pas en entry Point, ou alors on est en entry Point + vérifié => on sert le fichier
    const baseDir=path.resolve(process.cwd(),UPLOAD_FOLDER_MAP.scorm) // le path vers le dossier de fichier autorisé

    // Construction du path complet de la file dans le système de fichier
    const requestedPath = path.resolve(baseDir, scormPackage, scormEntryPoint);

    // identification de si on essaye de sortir du path système autorisé
    if (!requestedPath.startsWith(baseDir + path.sep)) {
      console.warn("[SCORM PACK] : accès non autorisé")
      res.status(403).json({ error: "Accès non autorisé." });
      return;
    }

    // Envoie de la ressource => Soit on l'a, et on la retourne, soit error, et 404 => comportement souhaité
    const disposition = contentDisposition(requestedPath, {type: 'inline'})

    res.sendFile(requestedPath,
      {
        headers: {
          'Content-Disposition': disposition ,
          'Content-Security-Policy': "frame-ancestors *"
        } },
      (err) => {
        if (err) {
          console.error("sendFile error :", err)
          // 404 si fichier manquant, sinon code envoyé par express (ex : 403)
          res.status(err.status || 404).end()
          return;
        }
      }
    )
    return;
  }

  catch (e){
    console.error(`[ROUTE scorm] global error - fileId : ${req.params.scormPackage}  ,\n  error : ${e} `)
    return res.status(500).send('Internal Server Error');
  }
})

module.exports = router;


