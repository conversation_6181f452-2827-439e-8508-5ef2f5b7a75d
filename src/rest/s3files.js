import LogService from '../graph-api/log/log-service'
import { S3Service } from '../graph-api/s3/s3-service'
import models from '../models/index'
import { UserStatsService } from '../graph-api/user/userStats-service.js'
import { LOG_OPERATIONS } from '../models/log/log'

const express = require('express')
const router = express.Router()

router.get('/', function(req, res, next) {
  res.status(404).end()
})

router.get('/getUrl/:filename', async function(req, res, next) {
  try {
    /////// Note : les vérifications sont probablement un peu superficielle vu que le middleware validate REST ressource est déjà validée sur cette route avec le x-token
    // Verification
    const userId = req.body.userId;
    if (!userId) {return res.status(401).json({ error: "Accès interdit : userId manquant" });}

    // Verif que user existe
    const me = await models.User.findByPk(userId);
    if (!me) {return res.status(403).json({ error: "Accès interdit : utilisateur invalide" });}

    // Get contexte simplifié
    const simplifiedCtx={
      models,
      me,
      ip: req.ip,
      req,
    }

    // Get argument
    const filename=req.params.filename

    // On log la création d'une URL de download. On fait exprêt de pas faire de await, car on a pas besoin de wait
    LogService.logAction({
      logOperation:LOG_OPERATIONS.S3.CreateS3RessourceLink.action,
      logData:{
        fileName:filename,
      },
      models:simplifiedCtx?.models,
      userId:simplifiedCtx?.me.id,
    })

    // Valider l'URL
    const signedUrl=await S3Service.getSignedUrlForFile({args:{filename,expirationTime:300},simplifiedCtx})

    res.json({ signedUrl })
  } catch (e) {
    res.json({ error: e.message })
  }
})

router.get('/download/:filename', async function(req, res, next) {
  try {

    /////// Note : les vérifications sont probablement un peu superficielle vu que le middleware validate REST ressource est déjà validée sur cette route avec le x-token
    // Verification
    const userId = req.body.userId;
    if (!userId) {return res.status(401).json({ error: "Accès interdit : userId manquant" });}

    // Verif que user existe
    const me = await models.User.findByPk(userId);
    if (!me) {return res.status(403).json({ error: "Accès interdit : utilisateur invalide" });}

    // Get contexte simplifié
    const simplifiedCtx={
      models,
      me,
      ip: req.ip,
      req,
    }

    // Get argument
    const filename=req.params.filename

    // On log la création d'une URL de download. On fait exprêt de pas faire de await, car on a pas besoin de wait
    LogService.logAction({
      logOperation:LOG_OPERATIONS.S3.CreateS3DownloadLink.action,
      logData:{
        fileName:filename,
      },
      models:simplifiedCtx?.models,
      userId:simplifiedCtx?.me.id,
    })

    // Valider l'URL
    const signedUrl=await S3Service.getSignedUrlForFile({args:{filename,expirationTime:300},simplifiedCtx})
    await UserStatsService.incrementStat(req.body.userId, UserStatsService.OPERATIONS.incrementDownloadedFiles)
    res.json({ signedUrl })
  } catch (e) {
    res.json({ error: e.message })
  }
})

export default router