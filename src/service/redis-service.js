import { redisClient } from '../models/index.js';

export const RedisService = {
  /**
   * set redis data
   * @param {string} key key
   * @param {any} val val
   * @param {number} timeout en secondes (default 60)
   */
  async set(key, val, timeout = 60) {
    val = JSON.stringify(val);
    await redisClient.set(key, val, {
      EX: timeout,
    });
    await redisClient.expire(key, timeout);
  },

  async delete(key) {
    await redisClient.sendCommand(['DEL', key]);
  },

  async get(key) {
    const val = await redisClient.get(key);
    return JSON.parse(val);
  },

  /* Warning, flush all Redis DB */
  async flushAll() {
    return redisClient.sendCommand(['FLUSHALL', 'ASYNC']);
  },

  /**
   * Supprime toutes les clés qui commencent par un préfixe
   * @param {string} prefix Préfixe des clés à supprimer
   */
  async deleteByPrefix(prefix) {
    const keys = await redisClient.keys(prefix + '*');
    if (Array.isArray(keys) && keys.length > 0) {
      await redisClient.del(...keys);
    }
  },
};