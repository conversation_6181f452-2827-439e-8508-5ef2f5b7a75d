import models from '../models/index.js'

const addMdbxTypeToQcmInUE = async (fromUEId) => {
  const typeMdbx = await models.TypeQcm.findOne({
    where: { name: 'Médi<PERSON>' },
  })
  const qcmsWithQuestions = await models.Qcm.findAll({
    where: {
      UEId: fromUEId,
      deleted: 0, // non supprimées
    },
  })
  console.log(`${qcmsWithQuestions.length} QCMs trouvés, début ajout de type`)
  for (const qcm of qcmsWithQuestions) {
    try {
      await qcm.addType_qcm(typeMdbx)
    } catch (e) {
      // Continue on errors
      continue
    }
  }

}

addMdbxTypeToQcmInUE(38).then(() => {
  console.log('done.')
})