import models from '../models/index.js'

const lineReader = require('line-reader')

lineReader.eachLine('./tmpcr.txt', async function(line) {
  let u = await models.User.findOne({ where: { username: line } })
  if (u) {
    u.credits++
    console.log(u.username + ' now has ' + u.credits + ' credits')
    await u.save()
  } else {
    console.log('peut pas recup user ' + line + ' (existe pas)')
  }
})
