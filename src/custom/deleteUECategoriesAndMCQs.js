import { Op } from 'sequelize'
import models from '../models/index.js'


export const deleteUECategoriesAndMCQs = async (ueId) => {
  try {
    await models.Qcm.destroy({ where: { UEId: ueId } })
    let ueCategs = await models.UECategory.findAll({ where: { ueId: ueId } })
    for (const ueCateg of ueCategs) {
      let coursInCateg = await models.Cours.findAll({
        where: { uecategoryId: ueCateg.id },
      })
      await models.Cours.destroy({ where: { id: { [Op.in]: coursInCateg.map(c => c.id) } } })
      await models.UECategory.destroy({ where: { id: ueCateg.id } })

    }
  } catch (e) {
    console.error(e)
  }
}