// run:
// npx babel-node src/custom/updateQuestionsCours.js

/* Migrate all legacy questions to QuestionAnswers + UserStats */
const Sequelize = require('sequelize')
import models from '../models/index.js'

const letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G']

const migrateStatsQuestionsUser = async (q, answerId, letter) => {
  // Migrate all stats for this answer
  // Toutes les stats questions concernant cette question q
  let stats = await models.QcmStatsQuestion.findAll({ where: { id_question: q.id_question } })
  for (const stat of stats) {
    let answersData = Array.isArray(stat.answersData) ? stat.answersData : [];
    if (stat[`${letter}_eleve`]) { // si coché
      answersData.push(answerId)
    }
    stat.answersData = JSON.stringify(answersData)
    await stat.save()
  }
}

const migrateStatsQuestionsUser2 = async (q, lettersAnswerMap) => {
  // Migrate all stats for this answer
  // Toutes les stats questions concernant cette question q
  let stats = await models.QcmStatsQuestion.findAll({ where: { id_question: q.id_question } })
  for (const stat of stats) {
    for(let letter of lettersAnswerMap) {
      let answersData = []
      if (stat[`${letter.letter}_eleve`] == 1) { // si coché
        answersData.push(letter.answerId)
      }
      stat.answersData = JSON.stringify(answersData)
      await stat.save()
    }
  }
}

const migrateQuestionAnswers = async (q) => {
  try {
    let lettersAnswerMap = []
    for (const letter of letters) {
      let answerText = q[`reponse_${letter}`]
      let answerValue = q[letter]
      let answerExplanation = q[`explication_${letter}`]
      if (answerText) {
        let newAnswer = {
          questionId: q.id_question,
          text: answerText,
          explanation: answerExplanation,
          isTrue: answerValue,
        }
        const answerCreated = await models.QuestionAnswers.create(newAnswer)
        lettersAnswerMap.push({letter: letter, answerId: answerCreated.id})
        await migrateStatsQuestionsUser(q, answerCreated.id, letter) // les answers IDs de la question
      }
    }
    //await migrateStatsQuestionsUser2(q, lettersAnswerMap)
    // autre solution tout migrer les stats ici, avec map lettre => id answer
    // et migrer
    return true
  } catch (e) {
    console.error(e)
  }
}

export const migrateAllQcm = async () => {
  try {
    let countQuestions = 0
    let countQcm = 0
    const qcmsWithQuestions = await models.Qcm.findAll()
    console.log(`${qcmsWithQuestions.length} QCMs trouvés`)
    for (const qcm of qcmsWithQuestions) {
      const questions = await qcm.getQuestions() // todo add raw true?
      // Créer les answers
      for (const q of questions) {
        await migrateQuestionAnswers(q)
        countQuestions++
      }
      countQcm++
      console.log(`QCM ${qcm.id_qcm} migrated (${countQcm} / ${qcmsWithQuestions.length})`)
    }
    console.log(countQuestions + 'questions migrées')
  } catch (e) {
    console.error(e)
  }
}

migrateAllQcm().then(() => {
  console.log('done.')
})