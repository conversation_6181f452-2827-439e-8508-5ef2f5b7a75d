import { Graph<PERSON>Error } from 'graphql';
import jwt from 'jsonwebtoken';
import { QuestionsService } from '../graph-api/qcm/questions/questions-service';
import models from '../models/index';
import { RedisService } from '../service/redis-service';
const { Readable } = require('stream');

export const FILES_ROUTER_URL = '/files';
export const UPLOAD_IMAGES_FOLDER = '/images';

export const ENABLE_RECAPTCHA_BACK = false;

// UPLOAD SETTINGS
export const PUBLIC_DIR = './public'; // public
const UPLOAD_DIR = './uploads'; // private
export const UPLOAD_FOLDER_MAP = {
  public: PUBLIC_DIR,
  avatars: PUBLIC_DIR + '/avatars',
  files: UPLOAD_DIR + '/files',
  fiches: UPLOAD_DIR + '/fiches',
  cours: UPLOAD_DIR + '/cours',
  images: UPLOAD_DIR + UPLOAD_IMAGES_FOLDER,
  bills: UPLOAD_DIR + '/bills',
  qcm: process.env.UPLOAD_PATH_QCM || '/var/www/html/qcm/uploads', // Valeur par défaut pour éviter undefined?
  mathpix:UPLOAD_DIR+'/mathpix', // folder pour mathpix
  scorm:UPLOAD_DIR+'/scorm', // folder pour scorm
  temp:UPLOAD_DIR+'/temp', // cron supprime tous les fichier de plus de X temps
};
// GRAPHQL JWT
export const TOKEN_HEADER_NAME = 'x-token';

// API KEY REST ROBOT TOKEN
export const API_KEY_HEADER_NAME = 'x-api-key';
export const API_KEY_SECRET = 's47a0c67k-ea7da-4fa8-a326-ec20680ae11d';

// API KEY reCAPTCHA INDENTIFICATION
export const recaptchaPrivateKey="6LencBkmAAAAAK7o540kL6EeVk64AcC7O0gV2q4P"
export const recaptchaValidationUrl="https://www.google.com/recaptcha/api/siteverify"

// URL CGU -> To get CGU Version from scrapping
export const CGU_URL='https://www.exoteach.com/cgu/'
export const CGU_SCRAPPING_TARGETDIV="cguversion"

export function consoleLogSequelizeMagicMethods(modelInstance) {
  console.log(Object.keys(modelInstance.__proto__));
}


const getUserTokenVersion = async (userId) => {
  const REDIS_KEY = `userTokenVersion-${userId}`;
  let cachedValue = await RedisService.get(REDIS_KEY);
  if (cachedValue) {
    return cachedValue;
  }
  // sinon
  const user = await models.User.findByPk(userId, {
    attributes: ['tokenVersion'],
    raw: true,
  });
  const tokenVersion = user?.tokenVersion || 1;
  await RedisService.set(REDIS_KEY, tokenVersion, 28800);
  return tokenVersion;
}

/**
 * Récupère infos basiques de l'utilisateur connecté
 * @param req
 * @returns {Promise<JwtPayload|string>}
 */
export const getMe = async (req) => {
  let token = req?.headers?.[TOKEN_HEADER_NAME];
  if (!token)
    token = req.query.token;
  if (token) {
    try {
      // Vérifie token et sa signature
      const decoded = await jwt.verify(token, process.env.SECRET);
      const userId = decoded?.id;
      const tokenVersion = decoded?.tokenVersion || 1; // TokenVersion from token
      const userTokenVersion = await getUserTokenVersion(userId);
      // Compare le tokenVersion avec celui en base de données
      if(userTokenVersion !== tokenVersion) {
        throw new GraphQLError('Token invalide', {
          extensions: {
            code: 'UNAUTHENTICATED',
          },
        });
      }
      return decoded;
    } catch (e) {
      throw new GraphQLError('Non authentifié', {
        extensions: {
          code: 'UNAUTHENTICATED',
        },
      });
    }
  }
};

export const getUserAgent = (req) => (req?.headers && req?.headers['user-agent']) || null;

export function isEmpty(obj) {
  return Object.keys(obj).length === 0;
}

export const validateEmail = (email) => {
  return String(email)
    .toLowerCase()
    .match(
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    );
};

export const consoleDebugObject = (obj) => {
  console.log(JSON.stringify(obj, null, 2));
};


export const validateExternalApiKey = async (req, res, next) => {
  let apiKey = req.headers[API_KEY_HEADER_NAME];
  if (!apiKey) {
    apiKey = req.query.apiKey;
  }
  if (apiKey) {
    const correspondingApiKey = await models.ApiKeys.findOne({ where: { key: apiKey } });
    try {
      if (correspondingApiKey) {
        // Ajouter l'objet correspondant à l'API Key à l'objet req pour utiliser plus tard
        req.correspondingApiKey = correspondingApiKey; // Ajouter correspondingApiKey à l'objet req
        next();
      } else {
        res.json({ message: 'invalid-api-key' });
      }
    } catch (e) {
      res.json({ message: 'error-in-api-key-validation' });
    }
  } else {
    res.json({ message: 'api-key-not-present' });
  }
};


export const validateApiKey = async (req, res, next) => {
  let apiKey = req.headers[API_KEY_HEADER_NAME];
  if (!apiKey) {
    apiKey = req.query.apiKey;
  }
  if (apiKey) {
    try {
      if (apiKey === API_KEY_SECRET) {
        next();
      } else {
        res.json({ message: 'invalid-apik' });
      }
    } catch (e) {
      res.json({ message: 'error-in-apik-validation' });
    }
  } else {
    res.json({ message: 'apik-not-present' });
  }
};

// Validate protected ressources token in query
export const validateUserRestRessource = async (req, res, next) => {
  let token = req.headers[TOKEN_HEADER_NAME]; // TOKEN DANS HEADERS
  let isFromQuery = false;
  if (!token) {
    token = req.query.tk; // TOKEN DANS QUERY POUR DOWNLOAD DIRECT
    isFromQuery = true;
  }
  if (token) {
    try {
      const me = await jwt.verify(token, process.env.SECRET);
      req.body.userId = me.id;
      next();
    } catch (e) {
      if (isFromQuery) {
        res.send('<p>Le lien a expiré. Veuillez retourner sur la page de téléchargement et réessayer</p>');
      } else {
        res.json({ message: 'jwt-error' });
      }
    }
  } else {
    res.json({ message: 'no-jwt' });
  }
};

// Get object methods
export const getMethods = (obj) => {
  let properties = new Set();
  let currentObj = obj;
  do {
    Object.getOwnPropertyNames(currentObj).map(item => properties.add(item));
  } while ((currentObj = Object.getPrototypeOf(currentObj)));
  return [...properties.keys()].filter(item => typeof obj[item] === 'function');
};

export const fontsPaths = {
  Ubuntu: {
    normal: `${UPLOAD_FOLDER_MAP.public}/Ubuntu-Regular.ttf`,
    bold: `${UPLOAD_FOLDER_MAP.public}/Ubuntu-Bold.ttf`,
  }
};

function bufferToStream(buffer) {
  const stream = new Readable();
  stream.push(buffer);
  stream.push(null); // No more data
  return stream;
}

// Petite classe qui permet de simuler une structure file à partir d'un buffer
export class SimulateFileFromBuffer{

  constructor({buffer,filename,mimetype,encoding}) {
    this.buffer = buffer;
    this.mimetype = mimetype;
    this.encoding = encoding;
    this.filename=filename;

    // mettre la destructuration
    this.createReadStream = this.createReadStream.bind(this)
  }

  // methode pour create un readstream à partir du buffer
  createReadStream(){
    return bufferToStream(this.buffer)
  }

}