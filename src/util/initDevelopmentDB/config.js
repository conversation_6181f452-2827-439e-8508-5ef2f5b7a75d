import models from '../../models'
import { CONFIG_KEYS } from '../../models/config'

export const initConfig = async () => {
  const websiteName = await models.Config.create({
    key: CONFIG_KEYS.WEBSITE_NAME, value: 'Médibox Marseille',
  })
  const a = await models.Config.create({
    key: CONFIG_KEYS.ANNONCE_GENERALE, value: 'Bienvenue à tous, n\'hésitez pas à démarrer vos révisions grâce à notre planning de révision de l\'été !',
  })
  const b = await models.Config.create({
    key: CONFIG_KEYS.MAINTENANCE_MODE, value: '0',
  })
  const c = await models.Config.create({
    key: CONFIG_KEYS.WEBSITE_BASE_URL, value: 'http://localhost:8000',
  })
  const twitter = await models.Config.create({
    key: CONFIG_KEYS.TWITTER_ACCOUNT, value: 'https://twitter.com/MediboxClermont',
  })
  const facebook = await models.Config.create({
    key: CONFIG_KEYS.FACEBOOK_ACCOUNT, value: 'https://facebook.com/MediboxClermont',
  })

}
