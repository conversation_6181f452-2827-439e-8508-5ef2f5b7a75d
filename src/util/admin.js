// ADMIN
import AdminBro from 'admin-bro';
import AdminBroExpress from 'admin-bro-expressjs';
import AdminBroSequelize from 'admin-bro-sequelizejs';
import models, { sequelize } from '../models';
import bcrypt from 'bcrypt';

AdminBro.registerAdapter(AdminBroSequelize);

const contentParent = {
    name: 'Utilisateurs',
    icon: 'fa fa-file-user',
};

const menu = {
    users: { name: 'Utilisateurs', icon: 'icon-users' },
};

export const adminBro = new AdminBro({
    databases: [sequelize],
    rootPath: '/admin',
    branding: {
        companyName: 'Mouidoubox',
    },

    resources: [
        // { resource: City, options: { listProperties: ['name', 'population', 'polution'] } },
        /*
        { resource: City, options: { properties: {
              name: {
                isVisible: { list: false, filter: false, show: true, edit: false },
              }
            }},
         */

        {resource: models.Groupe, options: { parent: contentParent }},
        {resource: models.UserGroups, options: { parent: contentParent }},
        { resource: models.Cours, options: { properties: { text: { type: 'richtext' },}}},

        {
            resource: models.User,
            options: {
                parent: contentParent,
                properties: {
                    encryptedPassword: {
                        isVisible: false,
                    },
                    password: {
                        type: 'string',
                        isVisible: {
                            list: false, edit: true, filter: false, show: false,
                        },
                    },
                },
                actions: {
                    new: {
                        before: async (request) => {
                            if(request.payload.record.password) {
                                request.payload.record = {
                                    ...request.payload.record,
                                    encryptedPassword: await bcrypt.hash(request.payload.record.password, 10),
                                    password: undefined,
                                }
                            }
                            return request
                        },
                    }
                }
            }
        }],

    /*
    dashboard: {
    handler: async (request, response, data) => {
      const categories = await CategoryModel.find({}).limit(5)
      return {
        usersCount: await UserModel.countDocuments(),
        pagesCount: await PageModel.countDocuments(),
        categories: await Promise.all(categories.map(async c => {
          const comments = await CommentModel.countDocuments({ category: c._id })
          return {
            title: c.title,
            comments,
            _id: c._id,
          }
        }))
      }
    },
    component: AdminBro.bundle('./components/dashboard'),
  },
     */
});


/*
const adminBroOptions = {
    rootPath: '/xyz-admin',
      logoutPath: '/xyz-admin/exit',
      loginPath: '/xyz-admin/sign-in',
      databases: [mongooseConnection],
      resources: [{ resource: ArticleModel, options: {...}}],
      branding: {
        companyName: 'XYZ c.o.',
      },

  resources: [
    { resource: Article, options: { parent: contentParent } },
    { resource: BlogPost, options: { parent: contentParent } },
    { resource: Comment, options: { parent: contentParent } },
  ],
}

 */
//export const router = AdminBroExpress.buildRouter(adminBro);
export const router = AdminBroExpress.buildAuthenticatedRouter(adminBro, {
    authenticate: async (email, password) => {
        const user = await models.User.findOne({ email })
        if (user && user.role === 'ADMIN') {
            if(await user.validatePassword(password)) {
                return user;
            }
        }
        return false
    },
    cookiePassword: 'o-i-love-cookies-from-alexandre-mmhhh',
});
