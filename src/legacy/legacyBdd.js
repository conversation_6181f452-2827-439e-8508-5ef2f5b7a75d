'use strict'
import Sequelize from 'sequelize'
import config from '../config/config.js'

const { SEQUELIZE_LOGGING, SEQUELIZE_CHARSET } = config

let legacySequelize;

legacySequelize = new Sequelize(
  process.env.DATABASE_LEGACY || 'medibox_marseille',
  process.env.DATABASE_USER || 'root',
  process.env.DATABASE_PASSWORD || 'root',
  {
    host: process.env.DATABASE_HOST || 'localhost',
    dialect: process.env.SEQUELIZE_DIALECT || 'mysql',
    logging: SEQUELIZE_LOGGING || false,
    reconnect: true,
    retry: {},
    define: {
      charset: SEQUELIZE_CHARSET || 'utf8mb4',
    },
  },
)

export { legacySequelize }